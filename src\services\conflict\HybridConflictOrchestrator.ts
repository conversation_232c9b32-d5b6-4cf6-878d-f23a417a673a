import { ConflictInfo, ConflictSeverity } from '../../types/conflict/index';
import { ResourceInfo } from '../../types/resource/interfaces';
import { Logger } from '../../utils/logging/logger';
import { RuleBasedConflictDetector, DependencyMap } from './RuleBasedConflictDetector';
import { LlmConflictDetector } from './LlmConflictDetector';

const logger = new Logger('HybridConflictOrchestrator');

/**
 * Configuration options for the hybrid conflict orchestrator
 */
export interface HybridConflictOrchestratorConfig {
    /**
     * Whether to use LLM-based conflict detection
     */
    useLlm: boolean;
    
    /**
     * Whether to enhance rule-based conflicts with LLM analysis
     */
    enhanceWithLlm: boolean;
    
    /**
     * Minimum severity level for LLM enhancement
     */
    llmEnhancementMinSeverity: ConflictSeverity;
    
    /**
     * Whether to use rule-based conflict detection
     */
    useRuleBased: boolean;
}

/**
 * Default configuration for the hybrid conflict orchestrator
 */
const DEFAULT_CONFIG: HybridConflictOrchestratorConfig = {
    useLlm: true,
    enhanceWithLlm: true,
    llmEnhancementMinSeverity: ConflictSeverity.MEDIUM,
    useRuleBased: true
};

/**
 * Hybrid conflict orchestrator that combines rule-based and LLM-based approaches
 * to detect conflicts between resources.
 */
export class HybridConflictOrchestrator {
    private ruleBasedDetector: RuleBasedConflictDetector;
    private llmDetector: LlmConflictDetector;
    private config: HybridConflictOrchestratorConfig;
    
    /**
     * Create a new hybrid conflict orchestrator
     * @param config Configuration options
     */
    constructor(config: Partial<HybridConflictOrchestratorConfig> = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.ruleBasedDetector = new RuleBasedConflictDetector();
        this.llmDetector = new LlmConflictDetector();
        
        logger.info(`HybridConflictOrchestrator initialized with config: ${JSON.stringify(this.config)}`);
    }
    
    /**
     * Detect conflicts between resources using both rule-based and LLM-based approaches
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @param dependencies Optional map of resource dependencies
     * @returns Array of detected conflicts
     */
    async detectConflicts(
        resource1: ResourceInfo, 
        resource2: ResourceInfo,
        dependencies?: DependencyMap
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];
        
        // Step 1: Apply rule-based detection (fast)
        if (this.config.useRuleBased) {
            await this.applyRuleBasedDetection(resource1, resource2, dependencies, conflicts);
        }
        
        // Step 2: Apply LLM-based detection if no conflicts found or if configured to always use LLM
        if (this.config.useLlm && (conflicts.length === 0 || this.config.enhanceWithLlm)) {
            await this.applyLlmBasedDetection(resource1, resource2, conflicts);
        }
        
        return conflicts;
    }
    
    /**
     * Apply rule-based conflict detection
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @param dependencies Optional map of resource dependencies
     * @param conflicts Array to add detected conflicts to
     */
    private async applyRuleBasedDetection(
        resource1: ResourceInfo, 
        resource2: ResourceInfo,
        dependencies: DependencyMap | undefined,
        conflicts: ConflictInfo[]
    ): Promise<void> {
        try {
            // Check for TGI conflicts
            const tgiConflict = this.ruleBasedDetector.detectTgiConflicts(resource1, resource2);
            if (tgiConflict) {
                conflicts.push(tgiConflict);
                
                // For high-severity conflicts, enhance with LLM analysis if configured
                if (this.config.enhanceWithLlm && 
                    tgiConflict.severity >= this.config.llmEnhancementMinSeverity) {
                    await this.enhanceConflictWithLlm(tgiConflict, resource1, resource2);
                }
            }
            
            // Check for dependency conflicts if dependencies are provided
            if (dependencies) {
                const depConflict = this.ruleBasedDetector.detectDependencyConflicts(resource1, resource2, dependencies);
                if (depConflict) {
                    conflicts.push(depConflict);
                    
                    // For high-severity conflicts, enhance with LLM analysis if configured
                    if (this.config.enhanceWithLlm && 
                        depConflict.severity >= this.config.llmEnhancementMinSeverity) {
                        await this.enhanceConflictWithLlm(depConflict, resource1, resource2);
                    }
                }
            }
            
            // Check for content conflicts
            const contentConflict = this.ruleBasedDetector.detectContentConflicts(resource1, resource2);
            if (contentConflict) {
                conflicts.push(contentConflict);
                
                // For high-severity conflicts, enhance with LLM analysis if configured
                if (this.config.enhanceWithLlm && 
                    contentConflict.severity >= this.config.llmEnhancementMinSeverity) {
                    await this.enhanceConflictWithLlm(contentConflict, resource1, resource2);
                }
            }
        } catch (error: any) {
            logger.error(`Error in rule-based conflict detection: ${error.message || error}`);
        }
    }
    
    /**
     * Apply LLM-based conflict detection
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @param conflicts Array to add detected conflicts to
     */
    private async applyLlmBasedDetection(
        resource1: ResourceInfo, 
        resource2: ResourceInfo,
        conflicts: ConflictInfo[]
    ): Promise<void> {
        try {
            // Skip LLM detection if we already found a TGI conflict (exact match)
            const hasTgiConflict = conflicts.some(c => c.id.startsWith('tgi-'));
            if (hasTgiConflict) {
                return;
            }
            
            // Use LLM to detect conflicts
            const llmConflict = await this.llmDetector.detectConflicts(resource1, resource2);
            if (llmConflict) {
                conflicts.push(llmConflict);
            }
        } catch (error: any) {
            logger.error(`Error in LLM-based conflict detection: ${error.message || error}`);
        }
    }
    
    /**
     * Enhance a conflict with LLM analysis
     * @param conflict Conflict to enhance
     * @param resource1 First resource involved in the conflict
     * @param resource2 Second resource involved in the conflict
     */
    private async enhanceConflictWithLlm(
        conflict: ConflictInfo,
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<void> {
        try {
            const enhancement = await this.llmDetector.enhanceConflictInfo(conflict, resource1, resource2);
            if (enhancement) {
                // Update conflict with enhanced information
                Object.assign(conflict, enhancement);
            }
        } catch (error: any) {
            logger.error(`Error enhancing conflict with LLM: ${error.message || error}`);
        }
    }
}
