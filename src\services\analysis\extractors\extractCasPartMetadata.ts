import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../types/resource/interfaces.js'; // Corrected path
import { Logger } from '../../../utils/logging/logger.js'; // Corrected path
import { DatabaseService, DependencyInfo } from '../../databaseService.js'; // Corrected path
import { parseCasp, ParsedCaspData, interpretAgeGenderFlags, CaspFlag, CaspTgiRef } from '../../../utils/parsing/caspParser.js'; // Corrected path & added types
import { BinaryResourceTypeValue } from '../../../types/resource/core.js'; // Corrected path

const logger = new Logger('ExtractCasPartMetadata');

// Enum for CASP Outfit Type Flags (Values based on community findings)
// These are the *bits* within the value associated with an Outfit Type category ID
enum CasOutfitTypeFlag {
    EVERYDAY = 0x0001,
    FORMAL = 0x0002,
    ATHLETIC = 0x0004,
    SLEEP = 0x0008,
    PARTY = 0x0010,
    SWIMWEAR = 0x0020,
    HOT_WEATHER = 0x0040,
    COLD_WEATHER = 0x0080,
    // Add others if discovered (Batuu, Situation, Career, etc.)
}

// Known Category IDs for Outfit Types within the CASP FlagList
// (Based on research - needs verification for Swim/Weather)
const OUTFIT_CATEGORY_IDS: { [key: number]: string } = {
    0x0C1C0FAB: 'Everyday',
    0x0C1C0FAC: 'Formal',
    0x0C1C0FAD: 'Athletic',
    0x0C1C0FAE: 'Sleep',
    0x0C1C0FAF: 'Party',
    // TODO: Verify these IDs from reliable sources (S4S, etc.)
    // 0x0C1C0FB0: 'Swimwear',
    // 0x0C1C0FB1: 'HotWeather',
    // 0x0C1C0FB2: 'ColdWeather',
};

// Function to interpret Outfit Type flag *values*
function interpretOutfitTypeFlags(value: number): string[] {
    const interpreted: string[] = [];
    if (value & CasOutfitTypeFlag.EVERYDAY) interpreted.push('Everyday');
    if (value & CasOutfitTypeFlag.FORMAL) interpreted.push('Formal');
    if (value & CasOutfitTypeFlag.ATHLETIC) interpreted.push('Athletic');
    if (value & CasOutfitTypeFlag.SLEEP) interpreted.push('Sleep');
    if (value & CasOutfitTypeFlag.PARTY) interpreted.push('Party');
    if (value & CasOutfitTypeFlag.SWIMWEAR) interpreted.push('Swimwear');
    if (value & CasOutfitTypeFlag.HOT_WEATHER) interpreted.push('HotWeather');
    if (value & CasOutfitTypeFlag.COLD_WEATHER) interpreted.push('ColdWeather');
    return interpreted;
}


/**
 * Extracts metadata specifically from CAS Part resources.
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object for CAS Part resources.
 */
export async function extractCasPartMetadata(
    key: AppResourceKey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};
    let contentSnippet: string | undefined = undefined;

    // --- START NEW LOGGING & CHECKS ---
    if (!buffer) {
        logger.error(`[ExtractCasPartMetadata] CRITICAL: Buffer is null/undefined before calling parseCasp for ${key.instance}. Skipping.`);
        contentSnippet = '[CAS Part Parse Skipped - Null Buffer]';
    } else if (buffer.length < 20) { // Check minimum size again here
        logger.error(`[ExtractCasPartMetadata] CRITICAL: Buffer is too small (${buffer.length} bytes) before calling parseCasp for ${key.instance}. Skipping.`);
        contentSnippet = `[CAS Part Parse Skipped - Buffer Too Small (${buffer.length})]`;
    } else {
        // --- Re-enable parseCasp CALL ---
        logger.info(`[ExtractCasPartMetadata] Buffer for ${key.instance} seems valid (size: ${buffer.length}). Proceeding to call parseCasp.`); // Re-enable this info log
        // --- END NEW LOGGING & CHCKS ---
        let parsedCaspData: ParsedCaspData | null = null; // Declare outside try
        try { // Start try block HERE, after checks
            parsedCaspData = parseCasp(buffer); // Assign to outer variable
            logger.debug(`Parsed CASP data for ${key.instance}:`, parsedCaspData); // Log parsed CASP data

            if (parsedCaspData !== null) { // Explicitly check for non-null
                logger.info(`Successfully parsed CASP data for ${key.instance}`); // Re-enable this info log
                extractedMetadata.casBodyTypeRaw = parsedCaspData.bodyType; // Add to extracted metadata
                extractedMetadata.casAgeGenderFlagsRaw = parsedCaspData.ageGender; // Add to extracted metadata
                // Interpret flags only if ageGender is defined
                if (extractedMetadata.casAgeGenderFlagsRaw !== undefined) {
                    extractedMetadata.casAgeGenderInterpreted = interpretAgeGenderFlags(extractedMetadata.casAgeGenderFlagsRaw); // Add to extracted metadata
                }

                // --- Interpret Outfit Type Flags ---
                let interpretedOutfitTypesSet = new Set<string>();
                if (parsedCaspData.flags) {
                    for (const flag of parsedCaspData.flags) {
                        // Check if the category is a known outfit type category
                        if (OUTFIT_CATEGORY_IDS[flag.category]) {
                            const typesForCategory = interpretOutfitTypeFlags(flag.value);
                            typesForCategory.forEach(type => interpretedOutfitTypesSet.add(type));
                        }
                        // TODO: Potentially check other category IDs if research reveals more relevant flags
                    }
                }

                // Save the interpreted outfit types if any were found
                if (interpretedOutfitTypesSet.size > 0) {
                    const interpretedOutfitTypes = Array.from(interpretedOutfitTypesSet);
                    extractedMetadata.casOutfitTypesInterpreted = JSON.stringify(interpretedOutfitTypes); // Save as JSON string array, add to extracted metadata
                    logger.debug(`Saved interpreted OutfitTypes for resource ${resourceId}: ${JSON.stringify(interpretedOutfitTypes)}`);
                }
                // Map flags from { category, value } to { a, b } for ResourceMetadata
                // Add explicit type for 'f' parameter and store as JSON string
                extractedMetadata.casFlagsRawJson = JSON.stringify(parsedCaspData.flags?.map((f: CaspFlag) => ({ a: f.category, b: f.value }))); // Serialize to JSON string
                // Swatch colors are already strings in parseCasp result (if implemented)
                // extractedMetadata.casSwatchColors = parsedCaspData.swatchColors; // Uncomment when swatches are parsed, add to extracted metadata

                // Update snippet based on successfully parsed data
                contentSnippet = `[CAS Part: Ver=${parsedCaspData.version?.toString(16)}, BodyType=0x${parsedCaspData.bodyType?.toString(16)}, AgeGender=0x${parsedCaspData.ageGender?.toString(16)}, Flags=${parsedCaspData.flags?.length ?? 0}]`; // Use parsedCaspData.flags length

                // --- Save Dependencies (TGI List) ---
                if (parsedCaspData.tgiList && parsedCaspData.tgiList.length > 0) {
                    try {
                        // Map to DependencyInfo structure
                        // Add explicit type for 'tgi' parameter
                        const dependenciesToSave: DependencyInfo[] = parsedCaspData.tgiList.map((tgi: CaspTgiRef) => ({
                            sourceResourceId: resourceId, // Add sourceResourceId
                            targetType: tgi.type,
                            targetGroup: BigInt(tgi.group), // Convert group to bigint
                            targetInstance: tgi.instance
                        }));

                        if (databaseService.dependencies && typeof databaseService.dependencies.saveDependencies === 'function') {
                            await databaseService.dependencies.saveDependencies(dependenciesToSave);
                            logger.debug(`Saved ${dependenciesToSave.length} dependencies for CASP resource ${resourceId}`);
                        } else {
                            logger.warn(`Cannot save dependencies for CASP resource ${resourceId}: dependencies repository not available`);
                        }
                    } catch (dbError: any) {
                        logger.error(`Failed to save dependencies for CASP resource ${resourceId} to DB: ${dbError.message || dbError}`);
                    }
                }
                // --- End Save Dependencies ---

            } else {
                logger.warn(`parseCasp returned null for ${key.instance}. Buffer might be invalid or too small.`); // Keep this warn
                contentSnippet = '[CAS Part Parse Failed]';
            }
        } catch (caspError: any) { // Catch block for parseCasp call
            // Log the specific error from parseCasp
            logger.warn(`Error calling parseCasp for ${key.instance}: ${caspError.message || caspError}`, caspError); // Keep this warn
            contentSnippet = '[CAS Part Parse Error]';
        } // End of try block for parseCasp
    } // Closing brace for the else block (line 477) for valid buffer check

    // Return extracted metadata and dependencies (dependencies are saved directly)
    return {
        contentSnippet: contentSnippet,
        ...extractedMetadata,
    };
}
