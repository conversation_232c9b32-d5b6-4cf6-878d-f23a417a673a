/**
 * Error handling utilities for SimData version handlers
 */

import { Logger } from '../../../../../../utils/logging/logger.js';

const logger = new Logger('VersionHandlerError');

/**
 * Context information for version handler errors
 */
export interface VersionErrorContext {
    version?: number;
    operation?: string;
    bufferLength?: number;
    additionalInfo?: Record<string, any>;
}

/**
 * Creates an error context object for version handler errors
 * @param version SimData version
 * @param operation Operation being performed when the error occurred
 * @param additionalInfo Additional context information
 * @returns Error context object
 */
export function createVersionErrorContext(
    version?: number,
    operation?: string,
    additionalInfo?: Record<string, any>
): VersionErrorContext {
    return {
        version,
        operation,
        additionalInfo
    };
}

/**
 * Handles a version handler error
 * @param error Error object
 * @param context Error context
 * @param defaultValue Default value to return
 * @returns Default value
 */
export function handleVersionError<T>(
    error: any,
    context: VersionErrorContext,
    defaultValue: T
): T {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Build detailed error message
    let detailedMessage = `SimData version handler error: ${errorMessage}`;
    
    if (context.version !== undefined) {
        detailedMessage += `, version: ${context.version}`;
    }
    
    if (context.operation) {
        detailedMessage += `, operation: ${context.operation}`;
    }
    
    if (context.bufferLength !== undefined) {
        detailedMessage += `, buffer length: ${context.bufferLength}`;
    }
    
    if (context.additionalInfo) {
        for (const [key, value] of Object.entries(context.additionalInfo)) {
            detailedMessage += `, ${key}: ${value}`;
        }
    }
    
    // Log the error
    logger.error(detailedMessage);
    
    // Return the default value
    return defaultValue;
}

/**
 * Higher-order function that wraps a function with error handling
 * @param fn Function to wrap
 * @param getContext Function to get error context
 * @param defaultValue Default value to return on error
 * @returns Wrapped function
 */
export function withVersionErrorHandling<T, Args extends any[]>(
    fn: (...args: Args) => T,
    getContext: (...args: Args) => VersionErrorContext,
    defaultValue: T
): (...args: Args) => T {
    return (...args: Args): T => {
        try {
            return fn(...args);
        } catch (error) {
            return handleVersionError(error, getContext(...args), defaultValue);
        }
    };
}
