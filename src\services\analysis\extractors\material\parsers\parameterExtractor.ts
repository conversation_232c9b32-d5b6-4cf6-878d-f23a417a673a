/**
 * Utility for extracting material parameters
 */

import { MaterialParameterType, MaterialParameterValue } from '../materialTypes.js';
import { handleMaterialExtractionError } from '../error/index.js';

/**
 * Extracts a parameter value from a buffer based on its type
 * @param buffer The buffer to extract from
 * @param offset The current offset in the buffer
 * @param paramType The parameter type
 * @returns The extracted value and the new offset
 */
export function extractParameterValue(
    buffer: Buffer, 
    offset: number, 
    paramType: number
): { value: MaterialParameterValue, newOffset: number } {
    try {
        let paramValue: MaterialParameterValue = null;
        let newOffset = offset;

        switch (paramType) {
            case MaterialParameterType.FLOAT:
                // Float value
                if (offset + 4 <= buffer.length) {
                    paramValue = buffer.readFloatLE(offset);
                    newOffset += 4;
                }
                break;
            
            case MaterialParameterType.INTEGER:
                // Integer value
                if (offset + 4 <= buffer.length) {
                    paramValue = buffer.readInt32LE(offset);
                    newOffset += 4;
                }
                break;
            
            case MaterialParameterType.VECTOR4:
                // Vector4 value
                if (offset + 16 <= buffer.length) {
                    paramValue = {
                        x: buffer.readFloatLE(offset),
                        y: buffer.readFloatLE(offset + 4),
                        z: buffer.readFloatLE(offset + 8),
                        w: buffer.readFloatLE(offset + 12)
                    };
                    newOffset += 16;
                }
                break;
            
            case MaterialParameterType.STRING:
                // String value
                if (offset + 4 <= buffer.length) {
                    const strLength = buffer.readUInt32LE(offset);
                    newOffset += 4;

                    if (newOffset + strLength <= buffer.length) {
                        paramValue = buffer.slice(newOffset, newOffset + strLength).toString('utf8');
                        newOffset += strLength;
                    }
                }
                break;
            
            case MaterialParameterType.BOOLEAN:
                // Boolean value
                if (offset + 1 <= buffer.length) {
                    paramValue = buffer.readUInt8(offset) !== 0;
                    newOffset += 1;
                }
                break;
            
            default:
                // Skip unknown parameter type
                newOffset += 4; // Assume 4 bytes for unknown types
                break;
        }

        return { value: paramValue, newOffset };
    } catch (error) {
        return handleMaterialExtractionError(
            error,
            {
                operation: 'extractParameterValue',
                bufferLength: buffer?.length,
                additionalInfo: { 
                    offset,
                    paramType
                }
            },
            { value: null, newOffset: offset + 4 }
        );
    }
}
