/**
 * Unit tests for DependencyGraphVisualizer
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DependencyGraphVisualizer } from '../../../../services/analysis/semantic/dependencyGraph/dependencyGraphVisualizer.js';
import { DatabaseService } from '../../../../services/databaseService.js';

// Mock the database service
const mockDatabaseService = {
  resources: {
    getAllResources: vi.fn(),
    getResourcesByPackageId: vi.fn(),
    getResourceById: vi.fn(),
    getResourcesByTGI: vi.fn()
  },
  dependencies: {
    getAllDependencies: vi.fn(),
    getDependenciesByPackageId: vi.fn(),
    getDependencies: vi.fn()
  }
} as unknown as DatabaseService;

describe('DependencyGraphVisualizer', () => {
  let dependencyGraphVisualizer: DependencyGraphVisualizer;
  
  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();
    
    // Create a new instance of DependencyGraphVisualizer
    dependencyGraphVisualizer = new DependencyGraphVisualizer(mockDatabaseService);
  });
  
  afterEach(() => {
    // Clear the dependency graph
    dependencyGraphVisualizer.clear();
  });
  
  it('should initialize correctly', () => {
    expect(dependencyGraphVisualizer).toBeDefined();
  });
  
  it('should generate a DOT graph', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphVisualizer.buildGraph({ includeResourceMetadata: true });
    
    // Generate DOT graph
    const dotGraph = dependencyGraphVisualizer.generateDotGraph();
    
    // Check DOT graph
    expect(dotGraph).toContain('digraph G {');
    expect(dotGraph).toContain('"1"');
    expect(dotGraph).toContain('"2"');
    expect(dotGraph).toContain('"1" -> "2"');
    expect(dotGraph).toContain('}');
  });
  
  it('should generate a JSON graph', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphVisualizer.buildGraph({ includeResourceMetadata: true });
    
    // Generate JSON graph
    const jsonGraph = dependencyGraphVisualizer.generateJsonGraph();
    
    // Check JSON graph
    expect(jsonGraph.nodes.length).toBe(2);
    expect(jsonGraph.links.length).toBe(1);
    expect(jsonGraph.nodes[0].id).toBe('1');
    expect(jsonGraph.nodes[1].id).toBe('2');
    expect(jsonGraph.links[0].source).toBe('1');
    expect(jsonGraph.links[0].target).toBe('2');
  });
  
  it('should serialize and deserialize the graph', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphVisualizer.buildGraph({ includeResourceMetadata: true });
    
    // Serialize the graph
    const serializedGraph = dependencyGraphVisualizer.serializeGraph();
    
    // Clear the graph
    dependencyGraphVisualizer.clear();
    
    // Check that the graph is cleared
    expect(dependencyGraphVisualizer.isGraphBuilt()).toBe(false);
    
    // Deserialize the graph
    dependencyGraphVisualizer.deserializeGraph(serializedGraph);
    
    // Check that the graph is restored
    expect(dependencyGraphVisualizer.isGraphBuilt()).toBe(true);
    expect(dependencyGraphVisualizer.getDependencies('1').has('2')).toBe(true);
    expect(dependencyGraphVisualizer.getDependents('2').has('1')).toBe(true);
  });
  
  it('should export the graph in different formats', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphVisualizer.buildGraph({ includeResourceMetadata: true });
    
    // Export as DOT
    const dotExport = dependencyGraphVisualizer.exportGraph('dot');
    expect(dotExport).toContain('digraph G {');
    
    // Export as JSON
    const jsonExport = dependencyGraphVisualizer.exportGraph('json');
    expect(jsonExport).toContain('"nodes":');
    
    // Export as serialized
    const serializedExport = dependencyGraphVisualizer.exportGraph('serialized');
    expect(serializedExport).toContain('dependencyGraph');
  });
  
  it('should handle visualization options', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphVisualizer.buildGraph({ includeResourceMetadata: true });
    
    // Generate DOT graph with options
    const dotGraph = dependencyGraphVisualizer.generateDotGraph({
      maxNodes: 1,
      includeMetadata: false,
      includeDependencyMetadata: false
    });
    
    // Check DOT graph
    expect(dotGraph).toContain('digraph G {');
    expect(dotGraph).toContain('"note"');
    expect(dotGraph).toContain('Graph limited to 1 nodes');
    
    // Generate JSON graph with options
    const jsonGraph = dependencyGraphVisualizer.generateJsonGraph({
      maxNodes: 1,
      includeMetadata: false,
      includeDependencyMetadata: false
    });
    
    // Check JSON graph
    expect(jsonGraph.nodes.length).toBe(1);
  });
});
