/**
 * SimData format validation utilities
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { VersionCategory, getVersionCategory } from '../types.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('FormatValidator');

/**
 * Validates that a buffer appears to be a valid SimData buffer for the given version
 * @param buffer SimData buffer
 * @param version SimData version
 * @returns True if the buffer appears to be a valid SimData buffer
 */
export function validateSimDataFormat(buffer: Buffer, version: number): boolean {
    try {
        if (buffer.length < 8) {
            return false;
        }

        // Check that the version in the buffer matches the expected version
        const bufferVersion = buffer.readUInt16LE(0);
        if (bufferVersion !== version) {
            return false;
        }

        // Check that the flags field (bytes 2-3) is reasonable
        const flags = buffer.readUInt16LE(2);
        if (flags > 0xFFFF) {
            return false;
        }

        // Perform version-specific validation
        const category = getVersionCategory(version);
        
        switch (category) {
            case VersionCategory.STANDARD:
                return validateStandardFormat(buffer, version);
            case VersionCategory.SPECIAL:
                return validateSpecialFormat(buffer, version);
            case VersionCategory.MOD:
                return validateModFormat(buffer, version);
            case VersionCategory.EXPERIMENTAL:
                return validateExperimentalFormat(buffer, version);
            default:
                return validateGenericFormat(buffer, version);
        }
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(version, 'validateSimDataFormat', { bufferLength: buffer.length }),
            false
        );
    }
}

/**
 * Validates that a buffer appears to be a valid standard SimData buffer
 * @param buffer SimData buffer
 * @param version SimData version
 * @returns True if the buffer appears to be a valid standard SimData buffer
 */
function validateStandardFormat(buffer: Buffer, version: number): boolean {
    try {
        // For standard versions (1-20), check if there's a valid schema structure
        if (buffer.length < 6) {
            return false;
        }

        // Try to read schema name length at offset 4
        const schemaNameLength = buffer.readUInt16LE(4);

        // Check if schema name length is reasonable
        if (schemaNameLength === 0 || schemaNameLength > 100) {
            return false;
        }

        // Check if buffer is large enough to contain the schema name
        if (buffer.length < 6 + schemaNameLength) {
            return false;
        }

        // Try to read the schema name
        const schemaName = buffer.toString('utf8', 6, 6 + schemaNameLength);

        // Check if schema name contains only valid characters
        if (!/^[a-zA-Z0-9_]+$/.test(schemaName)) {
            return false;
        }

        return true;
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(version, 'validateStandardFormat', { bufferLength: buffer.length }),
            false
        );
    }
}

/**
 * Validates that a buffer appears to be a valid special SimData buffer
 * @param buffer SimData buffer
 * @param version SimData version
 * @returns True if the buffer appears to be a valid special SimData buffer
 */
function validateSpecialFormat(buffer: Buffer, version: number): boolean {
    // Special versions (16708, 48111) have been validated in real mods
    // We'll just do basic validation here
    return buffer.length >= 16;
}

/**
 * Validates that a buffer appears to be a valid mod SimData buffer
 * @param buffer SimData buffer
 * @param version SimData version
 * @returns True if the buffer appears to be a valid mod SimData buffer
 */
function validateModFormat(buffer: Buffer, version: number): boolean {
    // For mod versions, we'll do basic validation
    return buffer.length >= 16;
}

/**
 * Validates that a buffer appears to be a valid experimental SimData buffer
 * @param buffer SimData buffer
 * @param version SimData version
 * @returns True if the buffer appears to be a valid experimental SimData buffer
 */
function validateExperimentalFormat(buffer: Buffer, version: number): boolean {
    // For experimental versions, we'll do basic validation
    return buffer.length >= 16;
}

/**
 * Validates that a buffer appears to be a valid generic SimData buffer
 * @param buffer SimData buffer
 * @param version SimData version
 * @returns True if the buffer appears to be a valid generic SimData buffer
 */
function validateGenericFormat(buffer: Buffer, version: number): boolean {
    // For unknown versions, we'll do basic validation
    return buffer.length >= 16;
}
