import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'; // Use ESM import

console.log('[Preload] Script starting (TS)...');

// Simple validation that our preload script is running
try {
    if (!contextBridge || !ipc<PERSON>enderer) {
        console.error('[Preload] contextBridge or ipc<PERSON><PERSON><PERSON> is undefined!');
    } else {
        console.log('[Preload] Electron modules loaded successfully');
    }
} catch (error) {
    console.error('[Preload] Error checking Electron modules:', error);
}

// Expose only the necessary function(s)
try {
    contextBridge.exposeInMainWorld('electronAPI', {
        openFileDialog: async () => { // Make async
            console.log('[Preload] Invoking open-file-dialog handler');
            // Use invoke instead of send
            return await ipc<PERSON>enderer.invoke('open-file-dialog');
        },
        // Add a simple invoke function for testing
        testInvoke: async (message: string) => {
            console.log(`[Preload] Invoking test-invoke-channel with: ${message}`);
            return await ipc<PERSON><PERSON><PERSON>.invoke('test-invoke-channel', message);
        },
        // Add a simple ping function for testing
        sendPing: (message: string) => { // Add type annotation
            console.log(`[Preload] Sending test-ping: ${message}`);
            ipcRenderer.send('test-ping', message);
        },
        // Expose a safe 'on' method for specific channels
        on: (channel: string, callback: (...args: any[]) => void) => {
            const validChannels = ['analysis-progress', 'analysis-complete', 'analysis-error'];
            if (validChannels.includes(channel)) {
                // Deliberately strip event as it includes `sender`
                ipcRenderer.on(channel, (event, ...args) => callback(...args));
                console.log(`[Preload] Registered listener for channel: ${channel}`);
            } else {
                console.warn(`[Preload] Attempted to register listener for invalid channel: ${channel}`);
            }
        }
    });
    console.log('[Preload] electronAPI exposed successfully');
    // Removed preload-ready event dispatch
} catch (error) {
    console.error('[Preload] Error exposing electronAPI:', error);
}

// --- Old event forwarding listeners removed ---
