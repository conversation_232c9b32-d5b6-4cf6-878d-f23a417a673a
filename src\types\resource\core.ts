/**
 * Core resource type definitions.
 * Re-exports the official enum from the installed S4TK package.
 */

// Import the enum directly from the installed S4TK package
// Import from the main module and access BinaryResourceType
// Import all from the main module
// Import the enum directly from the installed S4TK package, with .js extension
// Import the enum directly from the installed S4TK package, with .js extension, as default export
import BinaryResource from "@s4tk/models/lib/enums/binary-resources.js";

// Re-export the enum values under the desired name
export const BinaryResourceTypeValue = BinaryResource;

// Define the application's ResourceType to support both S4TK enum values and numeric values
// This allows us to handle resource types that don't exist in the S4TK enum
export type BinaryResourceType = number;

// Optional: Log to confirm successful import during startup/testing
// console.log('Successfully imported BinaryResourceType:', BinaryResourceTypeValue);
