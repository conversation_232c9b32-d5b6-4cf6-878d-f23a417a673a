export interface AnalysisStats {
  totalResources: number;
  totalConflicts: number;
  conflictsByType: Record<string, number>;
  conflictsBySeverity: Record<string, number>;
  conflictsByResourceType: Record<string, number>;
  resourcesByType: Record<string, number>;
  resourcesByCategory: Record<string, number>;
  timestamp: number;
}

export interface ResourceStats {
  type: string;
  count: number;
  size: number;
  conflicts: number;
}

export interface ConflictStats {
  type: string;
  count: number;
  severity: string;
  resources: number;
}

export interface PackageStats {
  resources: ResourceStats[];
  conflicts: ConflictStats[];
  totalSize: number;
  totalResources: number;
  totalConflicts: number;
  timestamp: number;
} 