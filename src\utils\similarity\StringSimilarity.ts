/**
 * Utility class for string similarity comparison
 */
export class StringSimilarity {
  /**
   * Compares two strings and returns a similarity score between 0 and 1
   * Uses Levenshtein distance for comparison
   */
  public compare(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;
    if (str1 === str2) return 1;

    const matrix: number[][] = [];

    // Initialize matrix
    for (let i = 0; i <= str1.length; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= str2.length; j++) {
      matrix[0][j] = j;
    }

    // Fill matrix
    for (let i = 1; i <= str1.length; i++) {
      for (let j = 1; j <= str2.length; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // deletion
          matrix[i][j - 1] + 1, // insertion
          matrix[i - 1][j - 1] + cost // substitution
        );
      }
    }

    // Calculate similarity score
    const maxLength = Math.max(str1.length, str2.length);
    const distance = matrix[str1.length][str2.length];
    return 1 - distance / maxLength;
  }

  /**
   * Checks if two strings are similar enough to be considered a match
   */
  public isSimilar(str1: string, str2: string, threshold: number = 0.8): boolean {
    return this.compare(str1, str2) >= threshold;
  }
} 
