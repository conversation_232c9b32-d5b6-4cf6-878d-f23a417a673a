/**
 * Database service for semantic understanding components
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { GameplaySystem, GameplaySystemCategorization } from '../interfaces/gameplaySystem.js';
import { ResourcePurposeAnalysis } from '../interfaces/resourcePurpose.js';
import { injectable, singleton } from '../../../di/decorators.js';

/**
 * Database service for semantic understanding
 */
@singleton()
export class SemanticDatabase {
    private logger: Logger;
    private db: any; // SQLite database instance
    private initialized: boolean = false;

    /**
     * Constructor
     * @param databaseService The database service
     * @param logger The logger instance
     */
    constructor(private databaseService: DatabaseService, logger?: Logger) {
        this.logger = logger || new Logger('SemanticDatabase');
        this.db = databaseService['db']; // Access the db property directly
    }

    /**
     * Initialize the database tables
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Create GameplaySystems table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS GameplaySystems (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    relatedResourceTypes TEXT, -- JSON array of resource type IDs
                    keywords TEXT, -- JSON array of keywords
                    isCore INTEGER NOT NULL DEFAULT 0,
                    packName TEXT,
                    metadata TEXT -- JSON object with additional metadata
                );
            `);

            // Create ResourcePurposes table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS ResourcePurposes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    resourceId INTEGER NOT NULL,
                    primaryPurpose TEXT NOT NULL,
                    purposeType TEXT NOT NULL,
                    confidence REAL DEFAULT 0,
                    gameplaySystem TEXT,
                    description TEXT,
                    details TEXT, -- JSON object with additional details
                    timestamp INTEGER NOT NULL,
                    FOREIGN KEY (resourceId) REFERENCES Resources(id)
                );
            `);

            // Create GameplaySystemCategorizations table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS GameplaySystemCategorizations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    resourceId INTEGER NOT NULL,
                    primarySystem TEXT NOT NULL,
                    primaryConfidence REAL DEFAULT 0,
                    secondarySystems TEXT, -- JSON array of {system, confidence} objects
                    explanation TEXT,
                    timestamp INTEGER NOT NULL,
                    FOREIGN KEY (resourceId) REFERENCES Resources(id)
                );
            `);

            // Create indices for performance
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourcepurposes_resourceid ON ResourcePurposes(resourceId);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourcepurposes_purpose ON ResourcePurposes(primaryPurpose);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourcepurposes_gameplaySystem ON ResourcePurposes(gameplaySystem);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_gameplaycategorizations_resourceid ON GameplaySystemCategorizations(resourceId);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_gameplaycategorizations_system ON GameplaySystemCategorizations(primarySystem);');

            this.initialized = true;
            this.logger.info('Semantic database tables initialized');
        } catch (error) {
            this.logger.error('Error initializing semantic database tables:', error);
            throw error;
        }
    }

    /**
     * Save a gameplay system to the database
     * @param system The gameplay system to save
     */
    public async saveGameplaySystem(system: GameplaySystem): Promise<void> {
        await this.initialize();

        try {
            await this.databaseService.executeQuery(`
                INSERT OR REPLACE INTO GameplaySystems (
                    id, name, description, relatedResourceTypes, keywords, isCore, packName, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                system.id,
                system.name,
                system.description,
                JSON.stringify(system.relatedResourceTypes),
                JSON.stringify(system.keywords),
                system.isCore ? 1 : 0,
                system.packName || null,
                system.metadata ? JSON.stringify(system.metadata) : null
            ]);

            this.logger.debug(`Saved gameplay system: ${system.id}`);
        } catch (error) {
            this.logger.error(`Error saving gameplay system ${system.id}:`, error);
            throw error;
        }
    }

    /**
     * Get a gameplay system by ID
     * @param id The gameplay system ID
     * @returns The gameplay system or undefined if not found
     */
    public async getGameplaySystem(id: string): Promise<GameplaySystem | undefined> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM GameplaySystems WHERE id = ?
            `, [id]);

            if (!result || result.length === 0) {
                return undefined;
            }

            const system = result[0];
            return {
                id: system.id,
                name: system.name,
                description: system.description,
                relatedResourceTypes: JSON.parse(system.relatedResourceTypes),
                keywords: JSON.parse(system.keywords),
                isCore: system.isCore === 1,
                packName: system.packName,
                metadata: system.metadata ? JSON.parse(system.metadata) : undefined
            };
        } catch (error) {
            this.logger.error(`Error getting gameplay system ${id}:`, error);
            throw error;
        }
    }

    /**
     * Save a resource purpose analysis to the database
     * @param analysis The resource purpose analysis to save
     * @returns The ID of the saved analysis or -1 if save failed
     */
    public async saveResourcePurposeAnalysis(analysis: ResourcePurposeAnalysis): Promise<number> {
        await this.initialize();

        try {
            // Validate resourceId
            if (!analysis || !analysis.resourceId) {
                this.logger.warn(`Invalid resourceId (${analysis?.resourceId}) for saveResourcePurposeAnalysis, skipping save`);
                return -1;
            }

            // Validate required fields
            if (!analysis.primaryPurpose || !analysis.purposeType) {
                this.logger.warn(`Missing required fields for resource ${analysis.resourceId}, skipping save`);
                return -1;
            }

            const result = await this.databaseService.executeQuery(`
                INSERT INTO ResourcePurposes (
                    resourceId, primaryPurpose, purposeType, confidence, gameplaySystem,
                    description, details, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                RETURNING id
            `, [
                analysis.resourceId,
                analysis.primaryPurpose,
                analysis.purposeType,
                analysis.confidence || 0,
                analysis.gameplaySystem || null,
                analysis.description || '',
                analysis.details ? JSON.stringify(analysis.details, (key, value) =>
                    typeof value === 'bigint' ? value.toString() : value
                ) : null,
                analysis.timestamp || Date.now()
            ]);

            const id = result[0]?.id;
            this.logger.debug(`Saved resource purpose analysis for resource ${analysis.resourceId} with ID ${id}`);
            return id;
        } catch (error) {
            this.logger.error(`Error saving resource purpose analysis for resource ${analysis.resourceId}:`, error);
            // Return -1 instead of throwing to prevent cascading failures
            return -1;
        }
    }

    /**
     * Save a gameplay system categorization to the database
     * @param categorization The gameplay system categorization to save
     * @param resourceId The resource ID
     * @returns The ID of the saved categorization or -1 if save failed
     */
    public async saveGameplaySystemCategorization(
        categorization: GameplaySystemCategorization,
        resourceId: number
    ): Promise<number> {
        await this.initialize();

        try {
            // Validate resourceId
            if (!resourceId) {
                this.logger.warn(`Invalid resourceId (${resourceId}) for saveGameplaySystemCategorization, skipping save`);
                return -1;
            }

            // Validate categorization
            if (!categorization || !categorization.primarySystem) {
                this.logger.warn(`Invalid categorization for resource ${resourceId}, skipping save`);
                return -1;
            }

            const result = await this.databaseService.executeQuery(`
                INSERT INTO GameplaySystemCategorizations (
                    resourceId, primarySystem, primaryConfidence, secondarySystems,
                    explanation, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?)
                RETURNING id
            `, [
                resourceId,
                categorization.primarySystem,
                categorization.primaryConfidence || 0,
                JSON.stringify(categorization.secondarySystems || []),
                categorization.explanation || '',
                categorization.timestamp || Date.now()
            ]);

            const id = result[0]?.id;
            this.logger.debug(`Saved gameplay system categorization for resource ${resourceId} with ID ${id}`);
            return id;
        } catch (error) {
            this.logger.error(`Error saving gameplay system categorization for resource ${resourceId}:`, error);
            // Return -1 instead of throwing to prevent cascading failures
            return -1;
        }
    }

    /**
     * Get resource purpose analysis for a resource
     * @param resourceId The resource ID
     * @returns The resource purpose analysis or undefined if not found
     */
    public async getResourcePurposeAnalysis(resourceId: number): Promise<ResourcePurposeAnalysis | undefined> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM ResourcePurposes WHERE resourceId = ?
                ORDER BY timestamp DESC LIMIT 1
            `, [resourceId]);

            if (!result || result.length === 0) {
                return undefined;
            }

            const analysis = result[0];
            return {
                resourceId: analysis.resourceId,
                primaryPurpose: analysis.primaryPurpose,
                purposeType: analysis.purposeType,
                confidence: analysis.confidence,
                gameplaySystem: analysis.gameplaySystem,
                description: analysis.description,
                details: analysis.details ? JSON.parse(analysis.details) : undefined,
                timestamp: analysis.timestamp
            };
        } catch (error) {
            this.logger.error(`Error getting resource purpose analysis for resource ${resourceId}:`, error);
            throw error;
        }
    }

    /**
     * Get gameplay system categorization for a resource
     * @param resourceId The resource ID
     * @returns The gameplay system categorization or undefined if not found
     */
    public async getGameplaySystemCategorization(resourceId: number): Promise<GameplaySystemCategorization | undefined> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM GameplaySystemCategorizations WHERE resourceId = ?
                ORDER BY timestamp DESC LIMIT 1
            `, [resourceId]);

            if (!result || result.length === 0) {
                return undefined;
            }

            const categorization = result[0];
            return {
                primarySystem: categorization.primarySystem,
                primaryConfidence: categorization.primaryConfidence,
                secondarySystems: JSON.parse(categorization.secondarySystems),
                explanation: categorization.explanation,
                timestamp: categorization.timestamp
            };
        } catch (error) {
            this.logger.error(`Error getting gameplay system categorization for resource ${resourceId}:`, error);
            throw error;
        }
    }
}
