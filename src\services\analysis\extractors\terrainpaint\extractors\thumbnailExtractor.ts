/**
 * Thumbnail Extractor for Terrain Paint Analysis
 * 
 * Extracts metadata from thumbnail resources in terrain paint mods.
 * Thumbnails are used for displaying terrain paint swatches in the game UI.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey } from '../../../../../types/resource/interfaces.js';

const log = new Logger('ThumbnailExtractor');

/**
 * Extracts metadata from thumbnail resources
 */
export class ThumbnailExtractor {
    /**
     * Extracts metadata from thumbnail resources
     * @param resources Array of thumbnail resources
     * @returns Metadata extracted from thumbnails
     */
    public async extract(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): Promise<{
        count?: number;
        dimensions?: string;
        format?: string;
    }> {
        if (resources.length === 0) {
            return {};
        }
        
        log.info(`Extracting metadata from ${resources.length} thumbnail resources`);
        
        // Parse each thumbnail resource
        const thumbnails = resources.map(resource => this.parseThumbnail(resource.buffer));
        
        // Extract metadata from thumbnails
        const count = thumbnails.length;
        const dimensions = this.extractDimensions(thumbnails);
        const format = this.extractFormat(thumbnails);
        
        return {
            count,
            dimensions,
            format
        };
    }
    
    /**
     * Parses a thumbnail buffer
     * @param buffer Thumbnail buffer
     * @returns Parsed thumbnail
     */
    private parseThumbnail(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
    } {
        try {
            // Check for DDS magic number (0x20534444 or "DDS ")
            if (buffer.length >= 4 && buffer.toString('ascii', 0, 4) === 'DDS ') {
                return this.parseDDSThumbnail(buffer);
            }
            
            // Check for PNG magic number (0x89504E47)
            if (buffer.length >= 8 && 
                buffer[0] === 0x89 && 
                buffer[1] === 0x50 && 
                buffer[2] === 0x4E && 
                buffer[3] === 0x47 && 
                buffer[4] === 0x0D && 
                buffer[5] === 0x0A && 
                buffer[6] === 0x1A && 
                buffer[7] === 0x0A) {
                return this.parsePNGThumbnail(buffer);
            }
            
            // Check for JPEG magic number (0xFFD8FF)
            if (buffer.length >= 3 && 
                buffer[0] === 0xFF && 
                buffer[1] === 0xD8 && 
                buffer[2] === 0xFF) {
                return this.parseJPEGThumbnail(buffer);
            }
            
            // Unknown format
            return {};
        } catch (error) {
            log.error(`Error parsing thumbnail: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a DDS thumbnail buffer
     * @param buffer DDS thumbnail buffer
     * @returns Parsed DDS thumbnail
     */
    private parseDDSThumbnail(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
    } {
        try {
            // DDS header is 128 bytes
            if (buffer.length < 128) {
                return {};
            }
            
            // Extract width and height from DDS header
            const width = buffer.readUInt32LE(12);
            const height = buffer.readUInt32LE(16);
            
            // Extract format from DDS header
            const fourCC = buffer.toString('ascii', 84, 88);
            
            return {
                width,
                height,
                format: `DDS (${fourCC})`
            };
        } catch (error) {
            log.error(`Error parsing DDS thumbnail: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a PNG thumbnail buffer
     * @param buffer PNG thumbnail buffer
     * @returns Parsed PNG thumbnail
     */
    private parsePNGThumbnail(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
    } {
        try {
            // PNG header is 8 bytes, followed by IHDR chunk
            if (buffer.length < 24) {
                return {};
            }
            
            // Extract width and height from IHDR chunk
            const width = buffer.readUInt32BE(16);
            const height = buffer.readUInt32BE(20);
            
            return {
                width,
                height,
                format: 'PNG'
            };
        } catch (error) {
            log.error(`Error parsing PNG thumbnail: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a JPEG thumbnail buffer
     * @param buffer JPEG thumbnail buffer
     * @returns Parsed JPEG thumbnail
     */
    private parseJPEGThumbnail(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
    } {
        try {
            // JPEG format is complex, so we'll just return the format
            return {
                format: 'JPEG'
            };
        } catch (error) {
            log.error(`Error parsing JPEG thumbnail: ${error}`);
            return {};
        }
    }
    
    /**
     * Extracts dimensions from thumbnails
     * @param thumbnails Array of parsed thumbnails
     * @returns Extracted dimensions
     */
    private extractDimensions(thumbnails: { width?: number; height?: number; format?: string; }[]): string | undefined {
        // Find the first thumbnail with width and height
        const thumbnail = thumbnails.find(t => t.width && t.height);
        
        if (thumbnail && thumbnail.width && thumbnail.height) {
            return `${thumbnail.width}x${thumbnail.height}`;
        }
        
        return undefined;
    }
    
    /**
     * Extracts format from thumbnails
     * @param thumbnails Array of parsed thumbnails
     * @returns Extracted format
     */
    private extractFormat(thumbnails: { width?: number; height?: number; format?: string; }[]): string | undefined {
        // Find the first thumbnail with a format
        const thumbnail = thumbnails.find(t => t.format);
        
        if (thumbnail && thumbnail.format) {
            return thumbnail.format;
        }
        
        return undefined;
    }
}
