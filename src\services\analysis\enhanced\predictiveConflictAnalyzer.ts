/**
 * Predictive Conflict Analyzer - Phase 1 Implementation
 * 
 * This module provides basic predictive conflict analysis to help users
 * avoid conflicts before they occur by analyzing mod compatibility patterns.
 * 
 * Key features:
 * - Pre-installation conflict prediction
 * - Compatibility scoring between mods
 * - Risk assessment for new mod installations
 * - Pattern-based conflict prevention
 * - Load order optimization suggestions
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceInfo } from '../../../types/database.js';
import { EnhancedResourceMetadata } from './enhancedMetadataExtractor.js';
import { IntelligentConflict, ConflictType, ConflictSeverity } from './intelligentConflictDetector.js';

const logger = new Logger('PredictiveConflictAnalyzer');

/**
 * Compatibility prediction result
 */
export interface CompatibilityPrediction {
    modA: string;
    modB: string;
    compatibilityScore: number; // 0-100 (100 = fully compatible)
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    predictedConflicts: PredictedConflict[];
    recommendations: string[];
    confidence: number; // 0-100
}

/**
 * Predicted conflict interface
 */
export interface PredictedConflict {
    type: ConflictType;
    severity: ConflictSeverity;
    probability: number; // 0-100
    description: string;
    affectedSystems: string[];
    preventionSuggestions: string[];
}

/**
 * Risk assessment for mod installation
 */
export interface InstallationRiskAssessment {
    modName: string;
    overallRisk: 'low' | 'medium' | 'high' | 'critical';
    riskFactors: RiskFactor[];
    compatibilityIssues: CompatibilityIssue[];
    recommendations: InstallationRecommendation[];
    safetyScore: number; // 0-100 (100 = completely safe)
}

/**
 * Risk factor interface
 */
export interface RiskFactor {
    type: 'performance' | 'stability' | 'compatibility' | 'dependency';
    severity: 'low' | 'medium' | 'high';
    description: string;
    impact: string;
    mitigation: string;
}

/**
 * Compatibility issue interface
 */
export interface CompatibilityIssue {
    conflictingMod: string;
    issueType: string;
    severity: ConflictSeverity;
    description: string;
    resolution: string;
}

/**
 * Installation recommendation interface
 */
export interface InstallationRecommendation {
    type: 'load_order' | 'configuration' | 'alternative' | 'prerequisite' | 'warning';
    priority: 'low' | 'medium' | 'high';
    description: string;
    action: string;
}

/**
 * Mod compatibility pattern
 */
export interface CompatibilityPattern {
    pattern: string;
    conflictType: ConflictType;
    severity: ConflictSeverity;
    frequency: number;
    description: string;
    examples: string[];
}

/**
 * Predictive conflict analyzer class
 */
export class PredictiveConflictAnalyzer {
    private databaseService: DatabaseService;
    private compatibilityPatterns: Map<string, CompatibilityPattern>;
    private knownCompatibilities: Map<string, Map<string, number>>;

    constructor(databaseService: DatabaseService) {
        this.databaseService = databaseService;
        this.compatibilityPatterns = new Map();
        this.knownCompatibilities = new Map();
        
        this.initializeCompatibilityPatterns();
    }

    /**
     * Initialize known compatibility patterns based on Sims 4 modding knowledge
     */
    private initializeCompatibilityPatterns(): void {
        const patterns: CompatibilityPattern[] = [
            {
                pattern: 'trait_conflicting_traits',
                conflictType: ConflictType.TRAIT_CONFLICT,
                severity: ConflictSeverity.MAJOR,
                frequency: 85,
                description: 'Traits with conflicting_traits field will conflict',
                examples: ['Mean trait vs Nice trait', 'Lazy trait vs Active trait']
            },
            {
                pattern: 'buff_replacement_same_type',
                conflictType: ConflictType.BUFF_REPLACEMENT,
                severity: ConflictSeverity.MODERATE,
                frequency: 70,
                description: 'Buffs that replace the same buff type will conflict',
                examples: ['Multiple mood buffs', 'Skill gain modifiers']
            },
            {
                pattern: 'script_same_injection_point',
                conflictType: ConflictType.SCRIPT_INJECTION,
                severity: ConflictSeverity.MAJOR,
                frequency: 90,
                description: 'Scripts injecting at the same point will conflict',
                examples: ['Multiple CAS modifications', 'Interaction overrides']
            },
            {
                pattern: 'tuning_same_resource_id',
                conflictType: ConflictType.TUNING_OVERRIDE,
                severity: ConflictSeverity.CRITICAL,
                frequency: 95,
                description: 'Tuning files with identical resource IDs will conflict',
                examples: ['Object tuning overrides', 'Skill modifications']
            },
            {
                pattern: 'object_same_catalog_id',
                conflictType: ConflictType.OBJECT_DEFINITION,
                severity: ConflictSeverity.MODERATE,
                frequency: 60,
                description: 'Objects with same catalog ID may conflict',
                examples: ['Furniture replacements', 'Decoration overrides']
            }
        ];

        for (const pattern of patterns) {
            this.compatibilityPatterns.set(pattern.pattern, pattern);
        }

        logger.info(`Initialized ${patterns.length} compatibility patterns`);
    }

    /**
     * Predict compatibility between two mods before installation
     */
    public async predictCompatibility(
        modAResources: ResourceInfo[],
        modBResources: ResourceInfo[],
        modAMetadata: Map<number, EnhancedResourceMetadata>,
        modBMetadata: Map<number, EnhancedResourceMetadata>
    ): Promise<CompatibilityPrediction> {
        try {
            const modAName = this.extractModName(modAResources);
            const modBName = this.extractModName(modBResources);

            logger.info(`Predicting compatibility between ${modAName} and ${modBName}`);

            // Analyze resource overlaps
            const resourceOverlaps = this.analyzeResourceOverlaps(modAResources, modBResources);
            
            // Analyze gameplay system conflicts
            const systemConflicts = this.analyzeGameplaySystemConflicts(
                modAMetadata,
                modBMetadata
            );

            // Check known compatibility patterns
            const patternConflicts = this.checkCompatibilityPatterns(
                modAResources,
                modBResources,
                modAMetadata,
                modBMetadata
            );

            // Calculate compatibility score
            const compatibilityScore = this.calculateCompatibilityScore(
                resourceOverlaps,
                systemConflicts,
                patternConflicts
            );

            // Determine risk level
            const riskLevel = this.determineRiskLevel(compatibilityScore);

            // Generate predicted conflicts
            const predictedConflicts = this.generatePredictedConflicts(
                resourceOverlaps,
                systemConflicts,
                patternConflicts
            );

            // Generate recommendations
            const recommendations = this.generateCompatibilityRecommendations(
                predictedConflicts,
                compatibilityScore
            );

            // Calculate confidence based on available data
            const confidence = this.calculatePredictionConfidence(
                modAMetadata,
                modBMetadata,
                resourceOverlaps.length
            );

            return {
                modA: modAName,
                modB: modBName,
                compatibilityScore,
                riskLevel,
                predictedConflicts,
                recommendations,
                confidence
            };

        } catch (error) {
            logger.error('Error predicting compatibility:', error);
            throw error;
        }
    }

    /**
     * Assess risk of installing a new mod with existing collection
     */
    public async assessInstallationRisk(
        newModResources: ResourceInfo[],
        newModMetadata: Map<number, EnhancedResourceMetadata>,
        existingMods: Array<{
            resources: ResourceInfo[];
            metadata: Map<number, EnhancedResourceMetadata>;
        }>
    ): Promise<InstallationRiskAssessment> {
        try {
            const modName = this.extractModName(newModResources);
            logger.info(`Assessing installation risk for ${modName}`);

            const riskFactors: RiskFactor[] = [];
            const compatibilityIssues: CompatibilityIssue[] = [];

            // Analyze against each existing mod
            for (const existingMod of existingMods) {
                const compatibility = await this.predictCompatibility(
                    newModResources,
                    existingMod.resources,
                    newModMetadata,
                    existingMod.metadata
                );

                if (compatibility.compatibilityScore < 70) {
                    const existingModName = this.extractModName(existingMod.resources);
                    compatibilityIssues.push({
                        conflictingMod: existingModName,
                        issueType: 'Compatibility Conflict',
                        severity: this.mapRiskToSeverity(compatibility.riskLevel),
                        description: `Potential conflicts with ${existingModName}`,
                        resolution: compatibility.recommendations.join('; ')
                    });
                }
            }

            // Analyze mod-specific risk factors
            riskFactors.push(...this.analyzeModRiskFactors(newModResources, newModMetadata));

            // Calculate overall risk
            const overallRisk = this.calculateOverallRisk(riskFactors, compatibilityIssues);

            // Calculate safety score
            const safetyScore = this.calculateSafetyScore(riskFactors, compatibilityIssues);

            // Generate installation recommendations
            const recommendations = this.generateInstallationRecommendations(
                riskFactors,
                compatibilityIssues,
                overallRisk
            );

            return {
                modName,
                overallRisk,
                riskFactors,
                compatibilityIssues,
                recommendations,
                safetyScore
            };

        } catch (error) {
            logger.error('Error assessing installation risk:', error);
            throw error;
        }
    }

    /**
     * Extract mod name from resources
     */
    private extractModName(resources: ResourceInfo[]): string {
        if (resources.length === 0) return 'Unknown Mod';
        
        const packageName = resources[0].packageName;
        if (packageName) {
            // Extract filename without extension
            return packageName.replace(/\.[^/.]+$/, '');
        }
        
        return 'Unknown Mod';
    }

    /**
     * Analyze resource overlaps between two mods
     */
    private analyzeResourceOverlaps(
        modAResources: ResourceInfo[],
        modBResources: ResourceInfo[]
    ): Array<{ resourceA: ResourceInfo; resourceB: ResourceInfo; overlapType: string }> {
        const overlaps: Array<{ resourceA: ResourceInfo; resourceB: ResourceInfo; overlapType: string }> = [];

        for (const resourceA of modAResources) {
            for (const resourceB of modBResources) {
                // Check for exact TGI match
                if (resourceA.type === resourceB.type &&
                    resourceA.group === resourceB.group &&
                    resourceA.instance === resourceB.instance) {
                    overlaps.push({
                        resourceA,
                        resourceB,
                        overlapType: 'exact_tgi_match'
                    });
                }
                
                // Check for same resource type and similar instance
                else if (resourceA.type === resourceB.type &&
                         resourceA.resourceType === resourceB.resourceType) {
                    overlaps.push({
                        resourceA,
                        resourceB,
                        overlapType: 'same_type_similar'
                    });
                }
            }
        }

        return overlaps;
    }

    /**
     * Analyze gameplay system conflicts
     */
    private analyzeGameplaySystemConflicts(
        modAMetadata: Map<number, EnhancedResourceMetadata>,
        modBMetadata: Map<number, EnhancedResourceMetadata>
    ): string[] {
        const modASystems = new Set<string>();
        const modBSystems = new Set<string>();

        // Collect all affected systems
        for (const metadata of modAMetadata.values()) {
            metadata.gameplaySystemsAffected.forEach(system => modASystems.add(system));
        }

        for (const metadata of modBMetadata.values()) {
            metadata.gameplaySystemsAffected.forEach(system => modBSystems.add(system));
        }

        // Find overlapping systems
        const conflicts: string[] = [];
        for (const system of modASystems) {
            if (modBSystems.has(system)) {
                conflicts.push(system);
            }
        }

        return conflicts;
    }

    /**
     * Check compatibility patterns
     */
    private checkCompatibilityPatterns(
        modAResources: ResourceInfo[],
        modBResources: ResourceInfo[],
        modAMetadata: Map<number, EnhancedResourceMetadata>,
        modBMetadata: Map<number, EnhancedResourceMetadata>
    ): CompatibilityPattern[] {
        const matchedPatterns: CompatibilityPattern[] = [];

        // Check for trait conflicts
        const traitPattern = this.compatibilityPatterns.get('trait_conflicting_traits');
        if (traitPattern && this.hasTraitConflictPattern(modAResources, modBResources)) {
            matchedPatterns.push(traitPattern);
        }

        // Check for script injection conflicts
        const scriptPattern = this.compatibilityPatterns.get('script_same_injection_point');
        if (scriptPattern && this.hasScriptConflictPattern(modAResources, modBResources)) {
            matchedPatterns.push(scriptPattern);
        }

        // Check for tuning conflicts
        const tuningPattern = this.compatibilityPatterns.get('tuning_same_resource_id');
        if (tuningPattern && this.hasTuningConflictPattern(modAResources, modBResources)) {
            matchedPatterns.push(tuningPattern);
        }

        return matchedPatterns;
    }

    /**
     * Calculate compatibility score
     */
    private calculateCompatibilityScore(
        resourceOverlaps: any[],
        systemConflicts: string[],
        patternConflicts: CompatibilityPattern[]
    ): number {
        let score = 100; // Start with perfect compatibility

        // Deduct for resource overlaps
        score -= resourceOverlaps.length * 10;

        // Deduct for system conflicts
        score -= systemConflicts.length * 15;

        // Deduct for pattern conflicts
        for (const pattern of patternConflicts) {
            score -= (pattern.frequency / 100) * 30;
        }

        return Math.max(0, Math.min(100, score));
    }

    /**
     * Determine risk level from compatibility score
     */
    private determineRiskLevel(compatibilityScore: number): 'low' | 'medium' | 'high' | 'critical' {
        if (compatibilityScore >= 80) return 'low';
        if (compatibilityScore >= 60) return 'medium';
        if (compatibilityScore >= 40) return 'high';
        return 'critical';
    }

    /**
     * Generate predicted conflicts
     */
    private generatePredictedConflicts(
        resourceOverlaps: any[],
        systemConflicts: string[],
        patternConflicts: CompatibilityPattern[]
    ): PredictedConflict[] {
        const conflicts: PredictedConflict[] = [];

        // Add conflicts from patterns
        for (const pattern of patternConflicts) {
            conflicts.push({
                type: pattern.conflictType,
                severity: pattern.severity,
                probability: pattern.frequency,
                description: pattern.description,
                affectedSystems: systemConflicts,
                preventionSuggestions: [
                    'Check mod load order',
                    'Look for compatibility patches',
                    'Consider alternative mods'
                ]
            });
        }

        return conflicts;
    }

    /**
     * Generate compatibility recommendations
     */
    private generateCompatibilityRecommendations(
        predictedConflicts: PredictedConflict[],
        compatibilityScore: number
    ): string[] {
        const recommendations: string[] = [];

        if (compatibilityScore < 50) {
            recommendations.push('High risk of conflicts - consider alternatives');
        } else if (compatibilityScore < 70) {
            recommendations.push('Moderate risk - monitor for issues');
        }

        if (predictedConflicts.length > 0) {
            recommendations.push('Check load order carefully');
            recommendations.push('Look for compatibility patches');
        }

        return recommendations;
    }

    /**
     * Calculate prediction confidence
     */
    private calculatePredictionConfidence(
        modAMetadata: Map<number, EnhancedResourceMetadata>,
        modBMetadata: Map<number, EnhancedResourceMetadata>,
        overlapCount: number
    ): number {
        let confidence = 50; // Base confidence

        // Increase confidence with more metadata
        confidence += Math.min(modAMetadata.size + modBMetadata.size, 30);

        // Increase confidence with overlaps (more data to analyze)
        confidence += Math.min(overlapCount * 5, 20);

        return Math.min(100, confidence);
    }

    // Helper methods for pattern detection
    private hasTraitConflictPattern(modAResources: ResourceInfo[], modBResources: ResourceInfo[]): boolean {
        const hasTraitA = modAResources.some(r => r.resourceType === 'TRAIT');
        const hasTraitB = modBResources.some(r => r.resourceType === 'TRAIT');
        return hasTraitA && hasTraitB;
    }

    private hasScriptConflictPattern(modAResources: ResourceInfo[], modBResources: ResourceInfo[]): boolean {
        const hasScriptA = modAResources.some(r => r.resourceType?.includes('SCRIPT'));
        const hasScriptB = modBResources.some(r => r.resourceType?.includes('SCRIPT'));
        return hasScriptA && hasScriptB;
    }

    private hasTuningConflictPattern(modAResources: ResourceInfo[], modBResources: ResourceInfo[]): boolean {
        const hasTuningA = modAResources.some(r => r.resourceType === 'TUNING_XML');
        const hasTuningB = modBResources.some(r => r.resourceType === 'TUNING_XML');
        return hasTuningA && hasTuningB;
    }

    // Additional helper methods would be implemented here...
    private analyzeModRiskFactors(resources: ResourceInfo[], metadata: Map<number, EnhancedResourceMetadata>): RiskFactor[] {
        // Implementation would analyze mod-specific risk factors
        return [];
    }

    private calculateOverallRisk(riskFactors: RiskFactor[], compatibilityIssues: CompatibilityIssue[]): 'low' | 'medium' | 'high' | 'critical' {
        // Implementation would calculate overall risk
        return 'low';
    }

    private calculateSafetyScore(riskFactors: RiskFactor[], compatibilityIssues: CompatibilityIssue[]): number {
        // Implementation would calculate safety score
        return 85;
    }

    private generateInstallationRecommendations(
        riskFactors: RiskFactor[],
        compatibilityIssues: CompatibilityIssue[],
        overallRisk: string
    ): InstallationRecommendation[] {
        // Implementation would generate installation recommendations
        return [];
    }

    private mapRiskToSeverity(riskLevel: string): ConflictSeverity {
        switch (riskLevel) {
            case 'critical': return ConflictSeverity.CRITICAL;
            case 'high': return ConflictSeverity.MAJOR;
            case 'medium': return ConflictSeverity.MODERATE;
            case 'low': return ConflictSeverity.MINOR;
            default: return ConflictSeverity.INFO;
        }
    }
}
