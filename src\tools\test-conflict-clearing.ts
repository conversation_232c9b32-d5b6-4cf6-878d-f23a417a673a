#!/usr/bin/env node

/**
 * Simple test to verify conflict clearing functionality
 */

import { DatabaseService } from '../services/databaseService.js';
import { Logger } from '../utils/logging/logger.js';

async function testConflictClearing() {
    const logger = new Logger('ConflictClearingTest');
    
    console.log('🧪 Testing conflict clearing functionality...\n');
    
    try {
        // Initialize database
        const databaseService = new DatabaseService(':memory:');
        await databaseService.initialize();
        
        // Check initial conflict count
        const initialConflicts = databaseService.getConflicts(1000);
        console.log(`📊 Initial conflicts in database: ${initialConflicts.length}`);
        
        // Add some test conflicts
        const testConflicts = [
            {
                id: 'test-conflict-1',
                type: 'RESOURCE' as any,
                severity: 'HIGH' as any,
                description: 'Test conflict 1',
                timestamp: Date.now(),
                recommendations: ['Test recommendation 1'],
                affectedResources: []
            },
            {
                id: 'test-conflict-2',
                type: 'CONTENT' as any,
                severity: 'MEDIUM' as any,
                description: 'Test conflict 2',
                timestamp: Date.now(),
                recommendations: ['Test recommendation 2'],
                affectedResources: []
            }
        ];
        
        console.log(`➕ Adding ${testConflicts.length} test conflicts...`);
        const savedCount = databaseService.saveConflicts(testConflicts);
        console.log(`✅ Saved ${savedCount} conflicts`);
        
        // Check conflict count after adding
        const conflictsAfterAdd = databaseService.getConflicts(1000);
        console.log(`📊 Conflicts after adding: ${conflictsAfterAdd.length}`);
        
        // Clear conflicts
        console.log(`🧹 Clearing all conflicts...`);
        const clearedCount = databaseService.clearConflicts();
        console.log(`✅ Cleared ${clearedCount} conflicts`);
        
        // Check final conflict count
        const finalConflicts = databaseService.getConflicts(1000);
        console.log(`📊 Final conflicts in database: ${finalConflicts.length}`);
        
        // Verify clearing worked
        if (finalConflicts.length === 0) {
            console.log(`\n🎉 SUCCESS: Conflict clearing is working correctly!`);
        } else {
            console.log(`\n❌ FAILURE: ${finalConflicts.length} conflicts remain after clearing`);
        }
        
        // Close database
        await databaseService.close();
        
    } catch (error: any) {
        console.error(`❌ Error during test: ${error.message || error}`);
    }
}

// Run the test
testConflictClearing().catch(console.error);
