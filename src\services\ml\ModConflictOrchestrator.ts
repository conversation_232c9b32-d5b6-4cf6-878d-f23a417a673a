﻿﻿import { ResourcePackageInfo } from '../../types/resource/conflicts.js';
import { ResourceInfo } from '../../types/database.js';
import { ConflictSeverity, ConflictType } from '../../types/conflict/ConflictTypes.js';
import { ConflictInfo } from '../../types/conflict/index.js';
import { PydanticAIResult } from './types.js'; // Import PydanticAIResult from consolidated types
// LLMConflictDetectionService is no longer needed directly
import { BinaryResourceType } from '../../types/resource/core.js';
import { ResourceCategory } from '../../types/resource/enums.js';
import { ResourceKey, ResourceMetadata, ResourceKeyMetadataPair } from '../../types/resource/interfaces.js';
import {
    createResourceKeyFromS4TKWithName,
    compareResourceKeys,
    getResourceTypeCategory,
    isBinaryResourceType,
    getResourceTypeName
} from '../../utils/resource/helpers.js';
import { Logger } from '../../utils/logging/logger.js';
import { EventEmitter } from 'events';
import { Package } from '@s4tk/models'; // Fixed import path
import { promises as fs } from 'fs';
import { createHash } from 'crypto';
import { PydanticAIService } from './pydanticAIService.js'; // Import the new service


export class ModConflictOrchestrator extends EventEmitter {
    private static instance: ModConflictOrchestrator;
    private logger: Logger;
    private pydanticAIService: PydanticAIService; // Add instance of the new service
    private readonly MAX_CONCURRENT_ANALYSES = 3; // Concurrency limit for MCP calls
    private analysisQueue: Promise<any>[] = [];

    private constructor(logger: Logger) {
        super();
        this.logger = logger || new Logger('ModConflictOrchestrator');
        this.pydanticAIService = new PydanticAIService(this.logger); // Initialize the new service
    }

    public static getInstance(logger: Logger): ModConflictOrchestrator {
        if (!ModConflictOrchestrator.instance) {
            ModConflictOrchestrator.instance = new ModConflictOrchestrator(logger);
        }
        return ModConflictOrchestrator.instance;
    }

    public async initialize(): Promise<void> {
        this.logger.info('Initializing ModConflictOrchestrator');
        // No service initialization needed here anymore
    }

    public async analyzeConflictsBetweenPackages(
        pkg1Info: ResourcePackageInfo,
        pkg2Info: ResourcePackageInfo
    ): Promise<ConflictInfo[]> {
        const startTime = Date.now();
        this.logger.info(`Analyzing conflicts between ${pkg1Info.name} and ${pkg2Info.name}`);

        const pkg1ResourceInfos = pkg1Info.resources;
        const pkg2ResourceInfos = pkg2Info.resources;

        const allFoundConflicts: ConflictInfo[] = [];

        const typeConflicts = this.analyzeResourceTypeConflicts(pkg1ResourceInfos, pkg2ResourceInfos);
        allFoundConflicts.push(...typeConflicts);

        const contentConflicts = await this.analyzeResourceContentConflicts(pkg1ResourceInfos, pkg2ResourceInfos);
        allFoundConflicts.push(...contentConflicts);

        // TODO: Add overall recommendations based on allFoundConflicts
        // const recommendations = this.generateOverallRecommendations(allFoundConflicts);

        this.logger.info(`Conflict analysis completed in ${Date.now() - startTime}ms. Found ${allFoundConflicts.length} conflicts.`);
        return allFoundConflicts;
    }

    private analyzeResourceTypeConflicts(pkg1Resources: ResourceInfo[], pkg2Resources: ResourceInfo[]): ConflictInfo[] {
        const conflicts: ConflictInfo[] = [];
        const comparedPairs = new Set<string>();
        for (const r1Info of pkg1Resources) {
            for (const r2Info of pkg2Resources) {
                const r1Key = r1Info.key;
                const r2Key = r2Info.key;
                if (compareResourceKeys(r1Key, r2Key)) {
                    const pairKey = `${r1Key.type}-${r1Key.group}-${r1Key.instance}`;
                    if (comparedPairs.has(pairKey)) continue;
                    const cat1 = getResourceTypeCategory(r1Key.type);
                    let cType: ConflictType | null = null;
                    let sev: ConflictSeverity | null = null;
                    let desc = '';
                    let recs: string[] = [];

                    if (cat1 === ResourceCategory.SCRIPT) {
                        cType = ConflictType.RESOURCE; sev = ConflictSeverity.HIGH;
                        desc = `Potential SCRIPT conflict for resource ${r1Key.name || pairKey}. Both packages modify the same script.`;
                        recs = ['Use Sims 4 Studio or other tools to compare script contents.', 'Check mod documentation for compatibility notes.', 'Ensure compatibility or choose one version.'];
                    } else if (cat1 === ResourceCategory.TUNING) {
                        cType = ConflictType.RESOURCE; sev = ConflictSeverity.MEDIUM;
                        desc = `Potential TUNING conflict for resource ${r1Key.name || pairKey}. Both packages modify the same tuning.`;
                        recs = ['Use Sims 4 Studio or XML Compare to view differences.', 'Load conflicting mods last if one should override the other.', 'Merge changes or choose one version.'];
                    }
                    if (cType && sev) {
                        conflicts.push({ id: pairKey, type: cType, severity: sev, description: desc, affectedResources: [r1Key, r2Key], timestamp: Date.now(), recommendations: recs });
                    }
                    comparedPairs.add(pairKey);
                }
            }
        }
        return conflicts;
    }

    private async analyzeResourceContentConflicts(pkg1Resources: ResourceInfo[], pkg2Resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        const conflictPromises: Promise<ConflictInfo | null>[] = [];
        const comparedPairs = new Set<string>();

        for (const r1Info of pkg1Resources) {
            for (const r2Info of pkg2Resources) {
                const r1Key = r1Info.key;
                const r2Key = r2Info.key;

                if (compareResourceKeys(r1Key, r2Key)) {
                    const pairKey = `${r1Key.type}-${r1Key.group}-${r1Key.instance}`;
                    if (comparedPairs.has(pairKey)) continue;

                    const r1Metadata = r1Info.metadata;
                    const r2Metadata = r2Info.metadata;

                    if (!r1Metadata || !r2Metadata) {
                        this.logger.warn(`Missing metadata for conflict pair ${pairKey}. Skipping content analysis.`);
                        continue;
                    }

                    // Limit concurrency
                    while (this.analysisQueue.length >= this.MAX_CONCURRENT_ANALYSES) {
                        await Promise.race(this.analysisQueue);
                    }

                    const mcpArguments = {
                        metadata1: r1Metadata,
                        metadata2: r2Metadata
                    };

                    // Call the new service method
                    const mcpPromise = this.pydanticAIService.analyzeConflict(pairKey, mcpArguments)
                        .then((mcpResult: PydanticAIResult | null) => {
                            if (mcpResult && mcpResult.hasConflict) {
                                // Map string severity from Python to TS enum
                                let severityEnum: ConflictSeverity;
                                switch (mcpResult.severity.toUpperCase()) {
                                    case "LOW": severityEnum = ConflictSeverity.LOW; break;
                                    case "MEDIUM": severityEnum = ConflictSeverity.MEDIUM; break;
                                    case "HIGH": severityEnum = ConflictSeverity.HIGH; break;
                                    case "CRITICAL": severityEnum = ConflictSeverity.CRITICAL; break;
                                    default: severityEnum = ConflictSeverity.UNKNOWN;
                                }
                                return {
                                    id: `mcp-${pairKey}`,
                                    type: ConflictType.RESOURCE, // Content conflicts are resource conflicts
                                    severity: severityEnum,
                                    description: mcpResult.details,
                                    affectedResources: [r1Key, r2Key],
                                    timestamp: Date.now(),
                                    recommendations: mcpResult.recommendations
                                } as ConflictInfo;
                            }
                            return null;
                        })
                        .catch((err: Error) => {
                            this.logger.error(`PydanticAIService call failed for ${pairKey}: ${err.message || err}`);
                            return null;
                        })
                        .finally(() => {
                            const i = this.analysisQueue.indexOf(mcpPromise);
                            if (i > -1) this.analysisQueue.splice(i, 1);
                        });

                    this.analysisQueue.push(mcpPromise);
                    conflictPromises.push(mcpPromise);
                    comparedPairs.add(pairKey);
                }
            }
        }
        // Await all promises and filter out nulls
        const results = await Promise.all(conflictPromises);
        return results.filter((c): c is ConflictInfo => c !== null);
    }

    // TODO: Re-implement or integrate generateOverallRecommendations if needed
    // private generateOverallRecommendations(conflicts: ConflictInfo[]): Set<string> { ... }

    // TODO: Re-implement or integrate calculateConflictMetrics if needed
    // private calculateConflictMetrics(conflicts: ConflictInfo[]) { ... }
}
