# S4TK Package: @s4tk/compression

Based on documentation found in `docs/technical/compression/`.

## Overview

This package provides utilities for compressing and decompressing Buffer objects using algorithms relevant to Sims 4 package file resources.

## Installation

```sh
npm i @s4tk/compression
```

## API

### Enums

#### `CompressionType`

Defines the types of compression used within Sims 4 package files.

*   `Uncompressed = 0`
*   `StreamableCompresssion = 65534` (Likely RefPack variant)
*   `InternalCompression = 65535` (Likely RefPack variant)
*   `DeletedRecord = 65504`
*   `ZLIB = 23106` (Standard ZLIB)

### Interfaces

#### `CompressedBuffer`

Represents a buffer known to be compressed.

*   `buffer: Buffer` (readonly): The compressed buffer data.
*   `compressionType: CompressionType` (readonly): The type of compression used.
*   `sizeDecompressed: number` (readonly): The expected size after decompression.

### Functions

#### `compressBuffer(buffer: Buffer, compression: CompressionType): Buffer`

Compresses the input `buffer` using the specified `compression` algorithm (likely only supports `ZLIB`). Returns a new `Buffer` with the compressed data.

#### `decompressBuffer(buffer: Buffer, compression: CompressionType): Buffer`

Decompresses the input `buffer` according to the specified `compression` type. Returns a new `Buffer` with the decompressed data. Supports `ZLIB` and potentially the older RefPack variants (`StreamableCompresssion`, `InternalCompression`).