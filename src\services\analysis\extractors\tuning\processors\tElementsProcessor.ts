import { DependencyInfo } from '../../../../../types/database.js';
import { createDependency } from '../helpers/dependencyCreator.js';

/**
 * Processes T elements from XML tuning
 * @param tElements Array of T elements
 * @param sourceResourceId Source resource ID
 * @returns Array of dependencies found
 */
export function processTElements(tElements: any[], sourceResourceId: number): DependencyInfo[] {
    const dependencies: DependencyInfo[] = [];

    for (const element of tElements) {
        if (!element) continue;

        // Handle string value
        if (typeof element === 'string') {
            try {
                // Check if it's a numeric instance ID
                if (/^\d+$/.test(element)) {
                    const instanceId = BigInt(element);
                    const dependency = createDependency(
                        0, // Unknown type
                        0, // Unknown group
                        instanceId,
                        'TElementReference'
                    );
                    dependency.resourceId = sourceResourceId;
                    dependencies.push(dependency);
                }
                // Check if it's a hex instance ID
                else if (/^0x[0-9a-fA-F]+$/.test(element)) {
                    const instanceId = BigInt(element);
                    const dependency = createDependency(
                        0, // Unknown type
                        0, // Unknown group
                        instanceId,
                        'TElementReference'
                    );
                    dependency.resourceId = sourceResourceId;
                    dependencies.push(dependency);
                }
            } catch (e) {
                // Ignore parsing errors
            }
        }
        // Handle object with _text property
        else if (element._text) {
            try {
                const text = element._text.toString().trim();
                // Check if it's a numeric instance ID
                if (/^\d+$/.test(text)) {
                    const instanceId = BigInt(text);
                    const dependency = createDependency(
                        0, // Unknown type
                        0, // Unknown group
                        instanceId,
                        'TElementReference'
                    );
                    dependency.resourceId = sourceResourceId;
                    dependencies.push(dependency);
                }
                // Check if it's a hex instance ID
                else if (/^0x[0-9a-fA-F]+$/.test(text)) {
                    const instanceId = BigInt(text);
                    const dependency = createDependency(
                        0, // Unknown type
                        0, // Unknown group
                        instanceId,
                        'TElementReference'
                    );
                    dependency.resourceId = sourceResourceId;
                    dependencies.push(dependency);
                }
            } catch (e) {
                // Ignore parsing errors
            }
        }
    }

    return dependencies;
} 