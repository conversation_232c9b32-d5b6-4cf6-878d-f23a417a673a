#!/bin/bash

# Start TypeScript server
echo "Starting TypeScript server..."
npm run dev &

# Wait for TypeScript server to start
sleep 2

# Start Streamlit server
echo "Starting Streamlit server..."
streamlit run src/streamlit/server.py &

echo "Both servers are starting..."
echo "TypeScript server will be available at http://localhost:3000"
echo "Streamlit interface will be available at http://localhost:8501"

# Wait for both servers
wait 