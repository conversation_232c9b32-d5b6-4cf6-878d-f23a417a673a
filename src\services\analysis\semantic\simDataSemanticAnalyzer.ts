/**
 * SimData Semantic Analyzer
 * 
 * This module provides specialized semantic analysis for SimData resources.
 * It works with the SimDataVersionHandler to extract semantic meaning from SimData resources.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { injectable, singleton } from '../../di/decorators.js';
import { SimDataVersionHandler } from '../simdata/simDataVersionHandler.js';
import { SimDataSchemaCatalog } from '../simdata/simDataSchemaCatalog.js';
import { ContentSemanticAnalysisResult, SemanticEntity, SemanticEntityType } from './contentSemanticAnalyzer.js';

/**
 * SimData semantic analyzer
 */
@singleton()
export class SimDataSemanticAnalyzer {
    private logger: Logger;
    private simDataVersionHandler: SimDataVersionHandler;
    private simDataSchemaCatalog: SimDataSchemaCatalog;
    
    /**
     * Constructor
     * @param simDataVersionHandler SimData version handler
     * @param simDataSchemaCatalog SimData schema catalog
     * @param logger Logger instance
     */
    constructor(
        simDataVersionHandler?: SimDataVersionHandler,
        simDataSchemaCatalog?: SimDataSchemaCatalog,
        logger?: Logger
    ) {
        this.logger = logger || new Logger('SimDataSemanticAnalyzer');
        this.simDataVersionHandler = simDataVersionHandler || new SimDataVersionHandler();
        this.simDataSchemaCatalog = simDataSchemaCatalog || new SimDataSchemaCatalog();
    }
    
    /**
     * Analyze SimData content semantically
     * @param resourceKey Resource key
     * @param buffer SimData buffer
     * @param metadata Resource metadata
     * @returns Content semantic analysis result
     */
    public analyzeSimData(
        resourceKey: ResourceKey,
        buffer: Buffer,
        metadata: Record<string, any>
    ): ContentSemanticAnalysisResult {
        try {
            // Initialize result
            const result: ContentSemanticAnalysisResult = {
                entities: [],
                keyPhrases: [],
                mainTopics: [],
                summary: '',
                complexityScore: 0,
                contentType: 'simdata',
                timestamp: Date.now()
            };
            
            // Skip analysis for empty buffer
            if (!buffer || buffer.length === 0) {
                return result;
            }
            
            // Try to parse SimData
            const simDataVersion = this.simDataVersionHandler.detectVersion(buffer);
            if (!simDataVersion) {
                this.logger.warn(`Could not detect SimData version for resource ${resourceKey.type}:${resourceKey.instance}`);
                result.summary = 'Unknown SimData format';
                return result;
            }
            
            // Parse SimData
            const simData = this.simDataVersionHandler.parseSimData(buffer, simDataVersion);
            if (!simData) {
                this.logger.warn(`Could not parse SimData for resource ${resourceKey.type}:${resourceKey.instance}`);
                result.summary = 'Failed to parse SimData';
                return result;
            }
            
            // Extract schema name
            const schemaName = simData.schemaName || '';
            if (schemaName) {
                result.mainTopics.push(schemaName);
                
                // Map schema name to entity type
                let entityType = SemanticEntityType.UNKNOWN;
                if (schemaName.includes('Trait')) entityType = SemanticEntityType.TRAIT;
                else if (schemaName.includes('Buff')) entityType = SemanticEntityType.BUFF;
                else if (schemaName.includes('Career')) entityType = SemanticEntityType.CAREER;
                else if (schemaName.includes('Skill')) entityType = SemanticEntityType.SKILL;
                else if (schemaName.includes('Aspiration')) entityType = SemanticEntityType.ASPIRATION;
                else if (schemaName.includes('Interaction')) entityType = SemanticEntityType.INTERACTION;
                else if (schemaName.includes('Object')) entityType = SemanticEntityType.OBJECT;
                
                // Add entity
                result.entities.push({
                    type: entityType,
                    name: schemaName,
                    attributes: { version: simDataVersion },
                    confidence: 90
                });
            }
            
            // Extract instance name
            const instanceName = simData.instanceName || '';
            if (instanceName) {
                result.keyPhrases.push(instanceName);
            }
            
            // Extract fields
            if (simData.fields) {
                // Get schema from catalog
                const schema = this.simDataSchemaCatalog.getSchema(schemaName);
                
                // Process fields
                for (const [fieldName, fieldValue] of Object.entries(simData.fields)) {
                    // Add field name to key phrases
                    result.keyPhrases.push(fieldName);
                    
                    // Check if field is critical
                    const isCritical = schema?.criticalFields?.includes(fieldName) || false;
                    if (isCritical) {
                        // Add critical field to key phrases with higher priority
                        result.keyPhrases.unshift(`${fieldName}: ${fieldValue}`);
                    }
                    
                    // Check for specific fields
                    if (fieldName === 'display_name' || fieldName === 'name') {
                        result.keyPhrases.unshift(String(fieldValue));
                    } else if (fieldName === 'description' || fieldName === 'display_description') {
                        result.keyPhrases.push(String(fieldValue));
                    }
                }
            }
            
            // Calculate complexity score
            result.complexityScore = this.calculateComplexityScore(simData);
            
            // Generate summary
            result.summary = this.generateSummary(simData, result);
            
            return result;
        } catch (error) {
            this.logger.error(`Error analyzing SimData for resource ${resourceKey.type}:${resourceKey.instance}:`, error);
            
            // Return empty result on error
            return {
                entities: [],
                keyPhrases: [],
                mainTopics: [],
                summary: 'Error analyzing SimData',
                complexityScore: 0,
                contentType: 'simdata',
                timestamp: Date.now()
            };
        }
    }
    
    /**
     * Calculate complexity score
     * @param simData SimData object
     * @returns Complexity score (0-100)
     */
    private calculateComplexityScore(simData: any): number {
        // Base score
        let score = 0;
        
        // Add points for fields
        const fieldCount = Object.keys(simData.fields || {}).length;
        score += Math.min(50, fieldCount * 5);
        
        // Add points for nested structures
        let nestedCount = 0;
        for (const fieldValue of Object.values(simData.fields || {})) {
            if (typeof fieldValue === 'object' && fieldValue !== null) {
                nestedCount++;
            }
        }
        score += Math.min(30, nestedCount * 10);
        
        // Add points for schema complexity
        const schema = this.simDataSchemaCatalog.getSchema(simData.schemaName || '');
        if (schema) {
            score += Math.min(20, (schema.criticalFields?.length || 0) * 5);
        }
        
        // Cap at 100
        return Math.min(100, score);
    }
    
    /**
     * Generate summary from SimData
     * @param simData SimData object
     * @param result Analysis result
     * @returns Summary string
     */
    private generateSummary(simData: any, result: ContentSemanticAnalysisResult): string {
        const schemaName = simData.schemaName || 'Unknown';
        const instanceName = simData.instanceName || '';
        const entityType = result.entities[0]?.type || SemanticEntityType.UNKNOWN;
        
        let summary = `SimData for ${schemaName}`;
        
        if (instanceName) {
            summary += ` (${instanceName})`;
        }
        
        if (entityType !== SemanticEntityType.UNKNOWN) {
            summary += `, contains ${entityType}`;
        }
        
        const fieldCount = Object.keys(simData.fields || {}).length;
        if (fieldCount > 0) {
            summary += ` with ${fieldCount} fields`;
        }
        
        return summary;
    }
}
