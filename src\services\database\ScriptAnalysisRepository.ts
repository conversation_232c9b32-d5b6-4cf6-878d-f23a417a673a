/**
 * Repository for script analysis data
 */

import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';

/**
 * Repository for script analysis data
 */
export class ScriptAnalysisRepository {
    private db: Database.Database;
    private logger: Logger;

    constructor(db: Database.Database, logger: Logger) {
        this.db = db;
        this.logger = logger;
        this.createTable();
    }

    /**
     * Create the ScriptAnalysis table if it doesn't exist
     */
    private createTable(): void {
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS ScriptAnalysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                resourceId INTEGER NOT NULL UNIQUE,
                analysisData TEXT NOT NULL,
                timestamp INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (resourceId) REFERENCES Resources(id)
            );
        `);
        this.db.exec(`CREATE INDEX IF NOT EXISTS idx_scriptanalysis_resourceId ON ScriptAnalysis(resourceId);`);
        this.logger.debug('ScriptAnalysis table created or already exists');
    }

    /**
     * Save script analysis data to the database
     * @param resourceId Resource ID
     * @param analysisData Script analysis data (will be JSON stringified)
     * @returns ID of the saved script analysis data
     */
    public saveScriptAnalysisData(resourceId: number, analysisData: any): number {
        try {
            // First check if the resource exists in the Resources table
            const resourceExists = this.checkResourceExists(resourceId);
            if (!resourceExists) {
                this.logger.warn(`Cannot save script analysis data: Resource ${resourceId} does not exist in the Resources table`);
                return -1; // Return -1 to indicate failure
            }

            // Convert analysis data to JSON string
            // Handle BigInt serialization
            const analysisDataJson = JSON.stringify(analysisData, (key, value) =>
                typeof value === 'bigint' ? value.toString() : value
            );

            // Check if script analysis data already exists for this resource
            const existingData = this.getScriptAnalysisData(resourceId);

            if (existingData) {
                // Update existing data
                const stmt = this.db.prepare(`
                    UPDATE ScriptAnalysis
                    SET analysisData = ?, timestamp = strftime('%s', 'now')
                    WHERE resourceId = ?
                `);

                const result = stmt.run(analysisDataJson, resourceId);
                this.logger.debug(`Updated script analysis data for resource ${resourceId}`);
                return existingData.id;
            } else {
                // Insert new data
                const stmt = this.db.prepare(`
                    INSERT INTO ScriptAnalysis (resourceId, analysisData)
                    VALUES (?, ?)
                `);

                const result = stmt.run(resourceId, analysisDataJson);
                this.logger.debug(`Saved script analysis data for resource ${resourceId}`);
                return result.lastInsertRowid as number;
            }
        } catch (error: any) {
            this.logger.error(`Error saving script analysis data for resource ${resourceId}:`, error);
            return -1; // Return -1 to indicate failure instead of throwing
        }
    }

    /**
     * Check if a resource exists in the Resources table
     * @param resourceId Resource ID to check
     * @returns True if the resource exists, false otherwise
     */
    private checkResourceExists(resourceId: number): boolean {
        try {
            const stmt = this.db.prepare(`
                SELECT COUNT(*) as count FROM Resources WHERE id = ?
            `);

            const result = stmt.get(resourceId) as { count: number };
            return result.count > 0;
        } catch (error: any) {
            this.logger.error(`Error checking if resource ${resourceId} exists:`, error);
            return false;
        }
    }

    /**
     * Get script analysis data from the database
     * @param resourceId Resource ID
     * @returns Script analysis data or null if not found
     */
    public getScriptAnalysisData(resourceId: number): { id: number, resourceId: number, analysisData: any, timestamp: number } | null {
        try {
            const stmt = this.db.prepare(`
                SELECT id, resourceId, analysisData, timestamp
                FROM ScriptAnalysis
                WHERE resourceId = ?
            `);

            const result = stmt.get(resourceId);

            if (result) {
                // Parse the JSON string back to an object
                return {
                    id: result.id,
                    resourceId: result.resourceId,
                    analysisData: JSON.parse(result.analysisData),
                    timestamp: result.timestamp
                };
            }

            return null;
        } catch (error: any) {
            this.logger.error(`Error getting script analysis data for resource ${resourceId}:`, error);
            return null;
        }
    }

    /**
     * Delete script analysis data from the database
     * @param resourceId Resource ID
     * @returns True if deleted, false otherwise
     */
    public deleteScriptAnalysisData(resourceId: number): boolean {
        try {
            const stmt = this.db.prepare(`
                DELETE FROM ScriptAnalysis
                WHERE resourceId = ?
            `);

            const result = stmt.run(resourceId);

            if (result.changes > 0) {
                this.logger.debug(`Deleted script analysis data for resource ${resourceId}`);
                return true;
            }

            return false;
        } catch (error: any) {
            this.logger.error(`Error deleting script analysis data for resource ${resourceId}:`, error);
            return false;
        }
    }
}
