/**
 * SharedColorUtils - Core color utility functions shared across the application
 *
 * This class provides essential color conversion, analysis, and processing functions
 * that are used by both the core application and demo components. It serves as the
 * single source of truth for color operations to reduce redundancy and ensure consistency.
 */
export class SharedColorUtils {
  /**
   * Converts a hex color string to RGB values
   * @param hex Hex color string (e.g., "#FF0000" or "#F00")
   * @returns RGB object or null if invalid
   */
  public static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    // Remove # if present
    hex = hex.replace(/^#/, '');

    // Handle shorthand hex (e.g., #F00 -> #FF0000)
    if (hex.length === 3) {
      hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }

    // Validate hex format
    if (!/^[0-9A-Fa-f]{6}$/.test(hex)) {
      return null;
    }

    // Parse hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return { r, g, b };
  }

  /**
   * Converts RGB values to a hex color string
   * @param r Red component (0-255)
   * @param g Green component (0-255)
   * @param b Blue component (0-255)
   * @returns Hex color string (e.g., "#FF0000")
   */
  public static rgbToHex(r: number, g: number, b: number): string {
    // Ensure values are in valid range
    r = Math.max(0, Math.min(255, Math.round(r)));
    g = Math.max(0, Math.min(255, Math.round(g)));
    b = Math.max(0, Math.min(255, Math.round(b)));

    // Convert to hex
    return (
      '#' +
      r.toString(16).padStart(2, '0') +
      g.toString(16).padStart(2, '0') +
      b.toString(16).padStart(2, '0')
    );
  }

  /**
   * Converts RGB values to HSL (Hue, Saturation, Lightness)
   * @param r Red component (0-255)
   * @param g Green component (0-255)
   * @param b Blue component (0-255)
   * @returns HSL object or null if invalid
   */
  public static rgbToHsl(
    r: number,
    g: number,
    b: number
  ): { h: number; s: number; l: number } | null {
    // Validate inputs
    if (r < 0 || r > 255 || g < 0 || g > 255 || b < 0 || b > 255) {
      return null;
    }

    // Convert RGB to [0, 1] range
    r /= 255;
    g /= 255;
    b /= 255;

    // Find min and max values
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    // Calculate lightness
    const l = (max + min) / 2;

    // If min and max are the same, it's a shade of gray (no hue)
    if (max === min) {
      return { h: 0, s: 0, l };
    }

    // Calculate saturation
    const d = max - min;
    const s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    // Calculate hue
    let h;
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
      default:
        h = 0;
    }

    h /= 6;

    // Return HSL values
    return { h, s, l };
  }

  /**
   * Converts HSL values to RGB
   * @param h Hue (0-1)
   * @param s Saturation (0-1)
   * @param l Lightness (0-1)
   * @returns RGB object
   */
  public static hslToRgb(h: number, s: number, l: number): { r: number; g: number; b: number } {
    // Ensure values are in valid range
    h = Math.max(0, Math.min(1, h));
    s = Math.max(0, Math.min(1, s));
    l = Math.max(0, Math.min(1, l));

    // If saturation is 0, it's a shade of gray
    if (s === 0) {
      const value = Math.round(l * 255);
      return { r: value, g: value, b: value };
    }

    // Helper function for conversion
    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    const r = hue2rgb(p, q, h + 1 / 3);
    const g = hue2rgb(p, q, h);
    const b = hue2rgb(p, q, h - 1 / 3);

    // Convert to 0-255 range
    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255),
    };
  }

  /**
   * Calculates the perceived luminance of a color
   * Uses the formula from WCAG 2.0 for relative luminance
   * @param rgb RGB color object
   * @returns Luminance value (0-1)
   */
  public static calculateLuminance(rgb: { r: number; g: number; b: number }): number {
    // Convert RGB to sRGB
    const sRGB = {
      r: rgb.r / 255,
      g: rgb.g / 255,
      b: rgb.b / 255,
    };

    // Apply gamma correction
    const gammaCorrect = (value: number): number => {
      return value <= 0.03928 ? value / 12.92 : Math.pow((value + 0.055) / 1.055, 2.4);
    };

    // Calculate relative luminance
    return (
      0.2126 * gammaCorrect(sRGB.r) + 0.7152 * gammaCorrect(sRGB.g) + 0.0722 * gammaCorrect(sRGB.b)
    );
  }

  /**
   * Calculates the contrast ratio between two colors
   * @param color1 First color in RGB format
   * @param color2 Second color in RGB format
   * @returns Contrast ratio (1-21)
   */
  public static calculateContrastRatio(
    color1: { r: number; g: number; b: number },
    color2: { r: number; g: number; b: number }
  ): number {
    const luminance1 = this.calculateLuminance(color1);
    const luminance2 = this.calculateLuminance(color2);

    // Ensure the lighter color is first
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);

    // Calculate contrast ratio
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Determines if a color is light or dark
   * @param rgb RGB color object
   * @returns True if the color is light, false if dark
   */
  public static isLightColor(rgb: { r: number; g: number; b: number }): boolean {
    const luminance = this.calculateLuminance(rgb);
    return luminance > 0.5;
  }

  /**
   * Formats a color object to a hex string
   * Handles various color object formats
   * @param color The color object to format
   * @returns The formatted hex string
   */
  public static formatToHex(color: unknown): string {
    try {
      // Initialize normalized color values
      const normalized = {
        r: 0,
        g: 0,
        b: 0,
        a: undefined as number | undefined,
      };

      // Handle different color formats
      if (color && typeof color === 'object') {
        // Format 1: {r, g, b, a} with values from 0-255
        if ('r' in color && 'g' in color && 'b' in color) {
          normalized.r = Math.round(Number((color as Record<string, unknown>).r));
          normalized.g = Math.round(Number((color as Record<string, unknown>).g));
          normalized.b = Math.round(Number((color as Record<string, unknown>).b));
          if ('a' in color) normalized.a = Math.round(Number((color as Record<string, unknown>).a));
        }
        // Format 2: {red, green, blue, alpha} with values from 0-1
        else if ('red' in color && 'green' in color && 'blue' in color) {
          // Convert from 0-1 range to 0-255 range
          normalized.r = Math.round(Number((color as Record<string, unknown>).red) * 255);
          normalized.g = Math.round(Number((color as Record<string, unknown>).green) * 255);
          normalized.b = Math.round(Number((color as Record<string, unknown>).blue) * 255);
          if ('alpha' in color)
            normalized.a = Math.round(Number((color as Record<string, unknown>).alpha) * 255);
        }
        // Format 3: {R, G, B, A} with values from 0-255
        else if ('R' in color && 'G' in color && 'B' in color) {
          normalized.r = Math.round(Number((color as Record<string, unknown>).R));
          normalized.g = Math.round(Number((color as Record<string, unknown>).G));
          normalized.b = Math.round(Number((color as Record<string, unknown>).B));
          if ('A' in color) normalized.a = Math.round(Number((color as Record<string, unknown>).A));
        }
      }

      // Ensure values are in valid range (0-255)
      normalized.r = Math.max(0, Math.min(255, normalized.r));
      normalized.g = Math.max(0, Math.min(255, normalized.g));
      normalized.b = Math.max(0, Math.min(255, normalized.b));

      // Format as hex string
      let hexString = '#';
      hexString += normalized.r.toString(16).padStart(2, '0');
      hexString += normalized.g.toString(16).padStart(2, '0');
      hexString += normalized.b.toString(16).padStart(2, '0');

      // Add alpha if available
      if (normalized.a !== undefined) {
        normalized.a = Math.max(0, Math.min(255, normalized.a));
        hexString += normalized.a.toString(16).padStart(2, '0');
      }

      return hexString.toUpperCase();
    } catch (error) {
      console.error('Error formatting color to hex:', error);
      return '#000000'; // Default to black in case of error
    }
  }
}
