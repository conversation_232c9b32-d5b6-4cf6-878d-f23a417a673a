const PI_NODE_TAG = "__PI_NODE";

export function replaceProcessingInstructions(xml: string | Buffer): string {
    const piSpanRegex = /<\?\s*[^(xml)](?:(?!\?>).)*\?>/gis;
    const piOpenTagRegex = /^<\?\s*\S*/;
    const piCloseTagRegex = /\?>$/;
    const xmlString = (typeof xml === "string" ? xml : xml.toString());
    return xmlString.replace(piSpanRegex, (prev) => {
        const openTagResult = piOpenTagRegex.exec(prev);
        if (!openTagResult) {
            return prev;
        }
        const tag = openTagResult[0].replace("<?", "").trim();
        const innerContent = prev
            .replace(piOpenTagRegex, "")
            .replace(piCloseTagRegex, "");
        return `<${PI_NODE_TAG} tag="${tag}">${innerContent}</${PI_NODE_TAG}>`;
    });
}
