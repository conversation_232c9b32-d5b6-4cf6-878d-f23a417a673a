﻿﻿import { RESOURCE_ANALYSIS_CONSTANTS } from '../../constants/analysis.js'; // Added .js
// Corrected imports
import { ResourceMetadata } from '../../types/resource/interfaces.js';
import { BinaryResourceType } from '../../types/resource/core.js';
import { ValidationResult } from '../../types/validation.js'; // Added .js

const {
  MAX_RESOURCE_SIZE,
  MIN_RESOURCE_SIZE,
  VALID_RESOURCE_TYPES,
  CAS_PART_FLAGS
} = RESOURCE_ANALYSIS_CONSTANTS;

export class ResourceValidation {
  private static instance: ResourceValidation;

  private constructor() {}

  public static getInstance(): ResourceValidation {
    if (!ResourceValidation.instance) {
      ResourceValidation.instance = new ResourceValidation();
    }
    return ResourceValidation.instance;
  }

  private validateResourceSize(size: number): ValidationResult {
    const isValid = size > 0 && size <= MAX_RESOURCE_SIZE;
    const message = isValid
      ? 'Resource size is valid'
      : `Resource size must be between ${MIN_RESOURCE_SIZE} and ${MAX_RESOURCE_SIZE} bytes`;

    return {
      valid: isValid,
      message,
      errors: isValid ? [] : ['Invalid resource size'],
      warnings: [],
      timestamp: Date.now()
    };
  }

  private validateResourceType(type: BinaryResourceType): ValidationResult {
    const isValid = VALID_RESOURCE_TYPES.includes(type);
    const message = isValid
      ? 'Resource type is valid'
      : `Invalid resource type: ${type}`;

    return {
      valid: isValid,
      message,
      errors: isValid ? [] : ['Invalid resource type'],
      warnings: [],
      timestamp: Date.now()
    };
  }

  private validateCasPartFlags(flags: number): ValidationResult {
    const isValid = CAS_PART_FLAGS.some(flag => (flags & flag) === flag);
    const message = isValid
      ? 'CAS part flags are valid'
      : 'Invalid CAS part flags';

    return {
      valid: isValid,
      message,
      errors: isValid ? [] : ['Invalid CAS part flags'],
      warnings: [],
      timestamp: Date.now()
    };
  }

  public validateResource(resource: ResourceMetadata): ValidationResult {
    const sizeValidation = this.validateResourceSize(resource.size);
    // TODO: Resource type validation requires ResourceKey, cannot be done with ResourceMetadata alone.
    // The 'type' property does not exist on the ResourceMetadata interface.
    // const typeValidation = this.validateResourceType(resource.type as BinaryResourceType);

    // const isValid = sizeValidation.valid && typeValidation.valid;
    // const errors = [...(sizeValidation.errors || []), ...(typeValidation.errors || [])];
    // const warnings = [...(sizeValidation.warnings || []), ...(typeValidation.warnings || [])];
    const isValid = sizeValidation.valid; // Only check size for now
    const errors = [...(sizeValidation.errors || [])];
    const warnings = [...(sizeValidation.warnings || [])];


    return {
      valid: isValid,
      message: isValid ? 'Resource metadata validation passed (size)' : 'Resource metadata validation failed (size)',
      errors,
      warnings,
      timestamp: Date.now()
    };
  }
}
