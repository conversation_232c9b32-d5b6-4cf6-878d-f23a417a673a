﻿import { Logger } from 'winston';
// Corrected imports
import { BinaryResourceType, BinaryResourceTypeValue } from '../../types/resource/core.js'; // Import both type and value
import { ResourceKey, ResourceMetadata } from '../../types/resource/interfaces.js';
import { Package } from '@s4tk/models/packages';
import type { ValidationResult, ValidationRule } from '../../types/validation.js';
import {
  metadataCache,
  MetadataCache,
  MetadataCacheEvent,
  CacheErrorInfo,
} from '../../lib/package/metadataCache.js';
import { TypeValidation } from './typeValidation.js';
import { ResourceValidation } from './resourceValidation.js';

// Script resource type (not included in BinaryResourceType enum)
const SCRIPT_TYPE = 0x0c772e27;

// Minimum sizes for different resource types (in bytes)
const MIN_SIZES = {
  OBJECT_DEFINITION: 50,
  SIMDATA: 20,
  MODEL: 100,
  TEXTURE: 100,
  STRING_TABLE: 10,
  RIG: 50,
  SCRIPT: 20,
};

export interface PackageValidationOptions {
  validateResources?: boolean;
  validateDependencies?: boolean;
  validateScripts?: boolean;
  validateTuning?: boolean;
  maxResourceSize?: number;
}

export class PackageValidator {
  private logger: Logger;
  private rules: ValidationRule[];

  constructor(logger: Logger) {
    this.logger = logger;
    this.rules = this.initializeRules();
  }

  private initializeRules(): ValidationRule[] {
    return [
      {
        name: 'Resource Type Validation',
        description: 'Validates that the resource type is valid',
        validate: (resource: ResourceMetadata): ValidationResult => {
          // Use type assertion (as any) for properties not defined on ResourceMetadata
          const valid = this.isValidResourceType((resource as any).type);
          return {
            valid,
            message: valid ? 'Resource type is valid' : 'Invalid resource type',
            errors: [],
            warnings: [],
            timestamp: Date.now()
          };
        }
      },
      {
        name: 'Resource Size Validation',
        description: 'Validates that the resource size is within acceptable limits',
        validate: (resource: ResourceMetadata): ValidationResult => {
          const valid = resource.size > 0 && resource.size < Number.MAX_SAFE_INTEGER;
          return {
            valid,
            message: valid ? 'Resource size is valid' : 'Invalid resource size',
            errors: [],
            warnings: [],
            timestamp: Date.now()
          };
        }
      }
    ];
  }

  async validatePackage(
    packagePath: string,
    options: PackageValidationOptions = {}
  ): Promise<ValidationResult[]> {
    try {
      this.logger.info(`Validating package: ${packagePath}`);

      const {
        validateResources = true,
        validateDependencies = true,
        validateScripts = true,
        validateTuning = true,
        maxResourceSize = 100 * 1024 * 1024 // 100MB
      } = options;

      // TODO: Implement actual package validation
      const results: ValidationResult[] = [];

      this.logger.info('Package validation complete');
      return results;
    } catch (error) {
      this.logger.error('Error validating package:', error);
      throw error;
    }
  }

  private isValidResourceType(type: BinaryResourceType): boolean { // Removed TuningResourceType
    return Object.values(BinaryResourceTypeValue).includes(type); // Use value for Object.values
  }

  async cleanup(): Promise<void> {
    // Cleanup any resources if needed
  }
}

export class PackageValidationRules {
  private static instance: PackageValidationRules;

  private constructor() {}

  public static getInstance(): PackageValidationRules {
    if (!PackageValidationRules.instance) {
      PackageValidationRules.instance = new PackageValidationRules();
    }
    return PackageValidationRules.instance;
  }

  private isValidResourceType(type: BinaryResourceType): boolean { // Removed TuningResourceType
    return Object.values(BinaryResourceTypeValue).includes(type); // Use value for Object.values
  }

  public getValidationRules(): ValidationRule[] {
    return [
      {
        name: 'Resource Type Validation',
        description: 'Validates that the resource type is valid',
        validate: (resource: ResourceMetadata): ValidationResult => {
          // Use type assertion (as any) for properties not defined on ResourceMetadata
          const resourceType = (resource as any).type;
          const valid = this.isValidResourceType(resourceType);
          return {
            valid,
            message: valid ? 'Resource type is valid' : 'Invalid resource type',
            details: {
              type: resourceType,
              isValidType: valid
            },
            timestamp: Date.now()
          };
        }
      },
      {
        name: 'Resource Size Validation',
        description: 'Validates that the resource size is within acceptable limits',
        validate: (resource: ResourceMetadata): ValidationResult => {
          const valid = resource.size > 0 && resource.size < Number.MAX_SAFE_INTEGER;
          return {
            valid,
            message: valid ? 'Resource size is valid' : 'Invalid resource size',
            details: {
              size: resource.size,
              isValidSize: valid
            },
            timestamp: Date.now()
          };
        }
      },
      {
        name: 'Resource Dependencies Validation',
        description: 'Validates that resource dependencies are properly specified',
        validate: (resource: ResourceMetadata): ValidationResult => {
          const valid = !resource.dependencies || 
            (Array.isArray(resource.dependencies) && 
             resource.dependencies.every((dep: ResourceKey) => 
               typeof dep === 'object' && 
               'type' in dep && 
               'name' in dep && 
               'id' in dep && 
               'path' in dep
             ));
          return {
            valid,
            message: valid ? 'Dependencies are valid' : 'Invalid dependencies format',
            details: {
              hasDependencies: !!resource.dependencies,
              dependencyCount: resource.dependencies?.length || 0
            },
            timestamp: Date.now()
          };
        }
      }
    ];
  }
}
