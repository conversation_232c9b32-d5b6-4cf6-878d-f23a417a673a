/**
 * Enhanced Buffer Pool
 *
 * This module provides an enhanced buffer pool implementation with advanced features
 * for memory management, buffer categorization, and adaptive sizing.
 *
 * Features:
 * - Buffer categorization by size
 * - Adaptive buffer sizing
 * - Buffer expiration
 * - Memory pressure awareness
 * - Performance metrics
 * - Resource tracking integration
 */

import { Logger } from '../logging/logger.js';
import EnhancedMemoryManager from './enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState, CleanupPriority } from './resourceTracker.js';
import { AppError, ErrorCategory, ErrorCode, ErrorSeverity } from '../error/errorTypes.js';
import { EnhancedErrorHandler } from '../error/enhancedErrorHandler.js';

// Create a logger for this module
const logger = new Logger('EnhancedBufferPool');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get resource tracker instance
const resourceTracker = ResourceTracker.getInstance();

// Get error handler instance
const errorHandler = EnhancedErrorHandler.getInstance();

/**
 * Buffer size categories
 */
export enum BufferSizeCategory {
    TINY = 'tiny',      // < 1KB
    SMALL = 'small',    // 1KB - 16KB
    MEDIUM = 'medium',  // 16KB - 256KB
    LARGE = 'large',    // 256KB - 1MB
    XLARGE = 'xlarge',  // 1MB - 16MB
    XXLARGE = 'xxlarge' // > 16MB
}

/**
 * Buffer with tracking information
 */
interface TrackedBuffer {
    buffer: Buffer;
    id: string;
    resourceId: string;
    category: BufferSizeCategory;
    size: number;
    creationTime: number;
    lastUsedTime: number;
    useCount: number;
    refCount: number;
}

/**
 * Buffer pool statistics
 */
export interface BufferPoolStats {
    totalBuffers: number;
    activeBuffers: number;
    idleBuffers: number;
    buffersByCategory: Record<BufferSizeCategory, number>;
    totalMemory: number;
    activeMemory: number;
    idleMemory: number;
    peakMemory: number;
    totalAllocated: number;
    totalReused: number;
    totalReleased: number;
    averageBufferAge: number;
    averageBufferUseCount: number;
}

/**
 * Buffer pool options
 */
export interface BufferPoolOptions {
    maxBuffers?: number;
    maxMemory?: number;
    maxBuffersPerCategory?: Record<BufferSizeCategory, number>;
    maxMemoryPerCategory?: Record<BufferSizeCategory, number>;
    bufferExpirationTime?: number;
    enableAdaptiveSizing?: boolean;
    trackResources?: boolean;
}

/**
 * Enhanced buffer pool class
 */
export class EnhancedBufferPool {
    private static instance: EnhancedBufferPool;
    private buffers: Map<string, TrackedBuffer> = new Map();
    private availableBuffers: Map<BufferSizeCategory, TrackedBuffer[]> = new Map();
    private options: BufferPoolOptions;
    private stats: BufferPoolStats;

    /**
     * Create a new enhanced buffer pool
     * @param options Buffer pool options
     */
    private constructor(options: BufferPoolOptions = {}) {
        this.options = {
            maxBuffers: 100,
            maxMemory: 100 * 1024 * 1024, // 100MB
            maxBuffersPerCategory: {
                [BufferSizeCategory.TINY]: 50,
                [BufferSizeCategory.SMALL]: 30,
                [BufferSizeCategory.MEDIUM]: 20,
                [BufferSizeCategory.LARGE]: 10,
                [BufferSizeCategory.XLARGE]: 5,
                [BufferSizeCategory.XXLARGE]: 2
            },
            maxMemoryPerCategory: {
                [BufferSizeCategory.TINY]: 1 * 1024 * 1024, // 1MB
                [BufferSizeCategory.SMALL]: 10 * 1024 * 1024, // 10MB
                [BufferSizeCategory.MEDIUM]: 20 * 1024 * 1024, // 20MB
                [BufferSizeCategory.LARGE]: 30 * 1024 * 1024, // 30MB
                [BufferSizeCategory.XLARGE]: 30 * 1024 * 1024, // 30MB
                [BufferSizeCategory.XXLARGE]: 50 * 1024 * 1024 // 50MB
            },
            bufferExpirationTime: 60000, // 1 minute
            enableAdaptiveSizing: true,
            trackResources: true,
            ...options
        };

        // Initialize available buffers for each category
        for (const category of Object.values(BufferSizeCategory)) {
            this.availableBuffers.set(category, []);
        }

        // Initialize stats
        this.stats = {
            totalBuffers: 0,
            activeBuffers: 0,
            idleBuffers: 0,
            buffersByCategory: {
                [BufferSizeCategory.TINY]: 0,
                [BufferSizeCategory.SMALL]: 0,
                [BufferSizeCategory.MEDIUM]: 0,
                [BufferSizeCategory.LARGE]: 0,
                [BufferSizeCategory.XLARGE]: 0,
                [BufferSizeCategory.XXLARGE]: 0
            },
            totalMemory: 0,
            activeMemory: 0,
            idleMemory: 0,
            peakMemory: 0,
            totalAllocated: 0,
            totalReused: 0,
            totalReleased: 0,
            averageBufferAge: 0,
            averageBufferUseCount: 0
        };

        logger.info('Enhanced buffer pool initialized');
    }

    /**
     * Get the buffer pool instance
     * @param options Buffer pool options
     * @returns Buffer pool instance
     */
    public static getInstance(options?: BufferPoolOptions): EnhancedBufferPool {
        if (!EnhancedBufferPool.instance) {
            EnhancedBufferPool.instance = new EnhancedBufferPool(options);
        } else if (options) {
            // Update options if provided
            EnhancedBufferPool.instance.options = {
                ...EnhancedBufferPool.instance.options,
                ...options
            };
        }

        return EnhancedBufferPool.instance;
    }

    /**
     * Get a buffer from the pool
     * @param size Required buffer size
     * @returns Buffer of at least the requested size
     */
    public getBuffer(size: number): Buffer {
        if (size <= 0) {
            throw errorHandler.createError(
                ErrorCode.INVALID_INPUT,
                'Buffer size must be greater than 0',
                {
                    category: ErrorCategory.VALIDATION,
                    severity: ErrorSeverity.ERROR,
                    context: {
                        component: 'EnhancedBufferPool',
                        operation: 'getBuffer',
                        parameters: { size }
                    }
                }
            );
        }

        // Get buffer category
        const category = this.getSizeCategory(size);

        // Try to find a suitable buffer in the available buffers
        const availableBuffersForCategory = this.availableBuffers.get(category) || [];

        // Find the smallest buffer that is large enough
        let bestFitIndex = -1;
        let bestFitSize = Number.MAX_SAFE_INTEGER;

        for (let i = 0; i < availableBuffersForCategory.length; i++) {
            const bufferSize = availableBuffersForCategory[i].size;

            if (bufferSize >= size && bufferSize < bestFitSize) {
                bestFitIndex = i;
                bestFitSize = bufferSize;

                // If we find an exact match, use it immediately
                if (bufferSize === size) {
                    break;
                }
            }
        }

        if (bestFitIndex !== -1) {
            // Reuse existing buffer
            const trackedBuffer = availableBuffersForCategory.splice(bestFitIndex, 1)[0];

            // Update buffer state
            trackedBuffer.lastUsedTime = Date.now();
            trackedBuffer.useCount++;
            trackedBuffer.refCount = 1;

            // Update stats
            this.stats.totalReused++;
            this.stats.idleBuffers--;
            this.stats.activeBuffers++;
            this.stats.idleMemory -= trackedBuffer.size;
            this.stats.activeMemory += trackedBuffer.size;

            // Update resource state if tracking is enabled
            if (this.options.trackResources) {
                resourceTracker.updateResourceState(trackedBuffer.resourceId, ResourceState.ACTIVE);
            }

            logger.debug(`Reused buffer #${trackedBuffer.id} of size ${this.formatBytes(trackedBuffer.size)} for request of ${this.formatBytes(size)}`);

            return trackedBuffer.buffer;
        }

        // No suitable buffer found, allocate a new one
        return this.allocateNewBuffer(size, category);
    }

    /**
     * Return a buffer to the pool
     * @param buffer Buffer to return
     */
    public returnBuffer(buffer: Buffer): void {
        // Find the tracked buffer
        let trackedBuffer: TrackedBuffer | undefined;
        let bufferId: string | undefined;

        for (const [id, tracked] of this.buffers.entries()) {
            if (tracked.buffer === buffer) {
                trackedBuffer = tracked;
                bufferId = id;
                break;
            }
        }

        if (!trackedBuffer || !bufferId) {
            throw errorHandler.createError(
                ErrorCode.INVALID_INPUT,
                'Attempted to return a buffer that was not obtained from this pool',
                {
                    category: ErrorCategory.VALIDATION,
                    severity: ErrorSeverity.ERROR,
                    context: {
                        component: 'EnhancedBufferPool',
                        operation: 'returnBuffer'
                    }
                }
            );
        }

        // Decrement reference count
        trackedBuffer.refCount--;

        if (trackedBuffer.refCount < 0) {
            logger.warn(`Buffer #${bufferId} has negative reference count: ${trackedBuffer.refCount}`);
            trackedBuffer.refCount = 0;
        }

        if (trackedBuffer.refCount === 0) {
            // Buffer is no longer in use
            trackedBuffer.lastUsedTime = Date.now();

            // Update stats
            this.stats.totalReleased++;
            this.stats.activeBuffers--;
            this.stats.idleBuffers++;
            this.stats.activeMemory -= trackedBuffer.size;
            this.stats.idleMemory += trackedBuffer.size;

            // Update resource state if tracking is enabled
            if (this.options.trackResources) {
                resourceTracker.updateResourceState(trackedBuffer.resourceId, ResourceState.IDLE);
            }

            // Check if we should keep this buffer in the pool
            const memoryPressure = memoryManager.getMemoryPressure();
            const categoryBuffers = this.availableBuffers.get(trackedBuffer.category) || [];
            const maxBuffersForCategory = this.options.maxBuffersPerCategory![trackedBuffer.category];
            const maxMemoryForCategory = this.options.maxMemoryPerCategory![trackedBuffer.category];
            const currentMemoryForCategory = categoryBuffers.reduce((sum, buf) => sum + buf.size, 0);

            if (
                categoryBuffers.length >= maxBuffersForCategory ||
                currentMemoryForCategory + trackedBuffer.size > maxMemoryForCategory ||
                this.stats.totalMemory > this.options.maxMemory! ||
                memoryPressure > 0.8
            ) {
                // Remove from buffers map
                this.buffers.delete(bufferId);

                // Update stats
                this.stats.totalBuffers--;
                this.stats.idleBuffers--;
                this.stats.totalMemory -= trackedBuffer.size;
                this.stats.idleMemory -= trackedBuffer.size;
                this.stats.buffersByCategory[trackedBuffer.category]--;

                // Release resource if tracking is enabled
                if (this.options.trackResources) {
                    resourceTracker.releaseResource(trackedBuffer.resourceId);
                }

                logger.debug(`Discarded buffer #${bufferId} of size ${this.formatBytes(trackedBuffer.size)} (pool full or memory pressure)`);
            } else {
                // Add to available buffers
                categoryBuffers.push(trackedBuffer);
                this.availableBuffers.set(trackedBuffer.category, categoryBuffers);

                logger.debug(`Returned buffer #${bufferId} of size ${this.formatBytes(trackedBuffer.size)} to pool`);
            }
        } else {
            logger.debug(`Decreased reference count for buffer #${bufferId} to ${trackedBuffer.refCount}`);
        }
    }

    /**
     * Track an external buffer
     * @param buffer Buffer to track
     * @returns The same buffer for chaining
     */
    public trackBuffer(buffer: Buffer): Buffer {
        // Find if this buffer is already tracked
        for (const tracked of this.buffers.values()) {
            if (tracked.buffer === buffer) {
                // Increment reference count
                tracked.refCount++;
                tracked.lastUsedTime = Date.now();

                logger.debug(`Increased reference count for buffer #${tracked.id} to ${tracked.refCount}`);

                return buffer;
            }
        }

        // Not tracked, create a new tracked buffer
        const category = this.getSizeCategory(buffer.length);
        const id = `${this.stats.totalAllocated + 1}`;

        // Create tracked buffer
        const trackedBuffer: TrackedBuffer = {
            buffer,
            id,
            resourceId: '',
            category,
            size: buffer.length,
            creationTime: Date.now(),
            lastUsedTime: Date.now(),
            useCount: 1,
            refCount: 1
        };

        // Track resource if enabled
        if (this.options.trackResources) {
            trackedBuffer.resourceId = resourceTracker.trackResource(
                ResourceType.BUFFER,
                'EnhancedBufferPool',
                () => {
                    // No cleanup needed for external buffers
                },
                {
                    id: `buffer-${id}`,
                    size: buffer.length,
                    state: ResourceState.ACTIVE,
                    priority: CleanupPriority.LOW,
                    metadata: {
                        category,
                        external: true
                    }
                }
            );
        }

        // Add to buffers map
        this.buffers.set(id, trackedBuffer);

        // Update stats
        this.stats.totalAllocated++;
        this.stats.totalBuffers++;
        this.stats.activeBuffers++;
        this.stats.totalMemory += buffer.length;
        this.stats.activeMemory += buffer.length;
        this.stats.peakMemory = Math.max(this.stats.peakMemory, this.stats.totalMemory);
        this.stats.buffersByCategory[category]++;

        logger.debug(`Started tracking external buffer #${id} of size ${this.formatBytes(buffer.length)}`);

        return buffer;
    }

    /**
     * Get buffer pool statistics
     * @returns Current statistics
     */
    public getStats(): BufferPoolStats {
        // Calculate average buffer age and use count
        let totalAge = 0;
        let totalUseCount = 0;

        for (const buffer of this.buffers.values()) {
            totalAge += Date.now() - buffer.creationTime;
            totalUseCount += buffer.useCount;
        }

        this.stats.averageBufferAge = this.stats.totalBuffers > 0 ? totalAge / this.stats.totalBuffers : 0;
        this.stats.averageBufferUseCount = this.stats.totalBuffers > 0 ? totalUseCount / this.stats.totalBuffers : 0;

        return { ...this.stats };
    }

    /**
     * Allocate a new buffer
     * @param size Required buffer size
     * @param category Buffer size category
     * @returns Newly allocated buffer
     * @private
     */
    private allocateNewBuffer(size: number, category: BufferSizeCategory): Buffer {
        // Round up to the nearest power of 2 to reduce fragmentation
        const allocSize = Math.max(size, this.nextPowerOf2(size));

        // Create buffer
        const buffer = Buffer.allocUnsafe(allocSize);
        const id = `${this.stats.totalAllocated + 1}`;

        // Create tracked buffer
        const trackedBuffer: TrackedBuffer = {
            buffer,
            id,
            resourceId: '',
            category,
            size: allocSize,
            creationTime: Date.now(),
            lastUsedTime: Date.now(),
            useCount: 1,
            refCount: 1
        };

        // Track resource if enabled
        if (this.options.trackResources) {
            trackedBuffer.resourceId = resourceTracker.trackResource(
                ResourceType.BUFFER,
                'EnhancedBufferPool',
                () => {
                    // Clear buffer data to help garbage collection
                    if (trackedBuffer.buffer) {
                        for (let i = 0; i < Math.min(trackedBuffer.buffer.length, 1024); i++) {
                            trackedBuffer.buffer[i] = 0;
                        }
                    }
                },
                {
                    id: `buffer-${id}`,
                    size: allocSize,
                    state: ResourceState.ACTIVE,
                    priority: CleanupPriority.MEDIUM,
                    metadata: {
                        category,
                        external: false
                    }
                }
            );
        }

        // Add to buffers map
        this.buffers.set(id, trackedBuffer);

        // Update stats
        this.stats.totalAllocated++;
        this.stats.totalBuffers++;
        this.stats.activeBuffers++;
        this.stats.totalMemory += allocSize;
        this.stats.activeMemory += allocSize;
        this.stats.peakMemory = Math.max(this.stats.peakMemory, this.stats.totalMemory);
        this.stats.buffersByCategory[category]++;

        logger.debug(`Allocated new buffer #${id} of size ${this.formatBytes(allocSize)} for request of ${this.formatBytes(size)}`);

        return buffer;
    }

    /**
     * Get the next power of 2 greater than or equal to the given number
     * @param n Number to round up
     * @returns Next power of 2
     * @private
     */
    private nextPowerOf2(n: number): number {
        if (n <= 0) return 1;

        // If n is already a power of 2, return it
        if ((n & (n - 1)) === 0) return n;

        // Find the next power of 2
        let power = 1;
        while (power < n) {
            power *= 2;
        }

        return power;
    }

    /**
     * Get the size category for a buffer size
     * @param size Buffer size
     * @returns Size category
     * @private
     */
    private getSizeCategory(size: number): BufferSizeCategory {
        if (size < 1024) {
            return BufferSizeCategory.TINY;
        } else if (size < 16 * 1024) {
            return BufferSizeCategory.SMALL;
        } else if (size < 256 * 1024) {
            return BufferSizeCategory.MEDIUM;
        } else if (size < 1024 * 1024) {
            return BufferSizeCategory.LARGE;
        } else if (size < 16 * 1024 * 1024) {
            return BufferSizeCategory.XLARGE;
        } else {
            return BufferSizeCategory.XXLARGE;
        }
    }

    /**
     * Format bytes to human-readable string
     * @param bytes Number of bytes
     * @returns Formatted string
     * @private
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Dispose of the buffer pool
     */
    public dispose(): void {
        // Clean up all buffers
        for (const [id, buffer] of this.buffers.entries()) {
            // Release resource if tracking is enabled
            if (this.options.trackResources && buffer.resourceId) {
                resourceTracker.releaseResource(buffer.resourceId);
            }

            // Clear buffer data to help garbage collection
            if (buffer.buffer) {
                for (let i = 0; i < Math.min(buffer.buffer.length, 1024); i++) {
                    buffer.buffer[i] = 0;
                }
            }
        }

        // Clear all collections
        this.buffers.clear();

        for (const category of Object.values(BufferSizeCategory)) {
            this.availableBuffers.set(category, []);
        }

        // Reset stats
        this.stats = {
            totalBuffers: 0,
            activeBuffers: 0,
            idleBuffers: 0,
            buffersByCategory: {
                [BufferSizeCategory.TINY]: 0,
                [BufferSizeCategory.SMALL]: 0,
                [BufferSizeCategory.MEDIUM]: 0,
                [BufferSizeCategory.LARGE]: 0,
                [BufferSizeCategory.XLARGE]: 0,
                [BufferSizeCategory.XXLARGE]: 0
            },
            totalMemory: 0,
            activeMemory: 0,
            idleMemory: 0,
            peakMemory: this.stats.peakMemory,
            totalAllocated: this.stats.totalAllocated,
            totalReused: this.stats.totalReused,
            totalReleased: this.stats.totalReleased,
            averageBufferAge: 0,
            averageBufferUseCount: 0
        };

        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }

        logger.info('Buffer pool disposed');
    }

    /**
     * Clean up the buffer pool
     */
    public cleanup(): void {
        const now = Date.now();
        const memoryPressure = memoryManager.getMemoryPressure();

        // Determine how aggressive to be with cleanup based on memory pressure
        const maxIdleTime = memoryPressure > 0.7 ?
            this.options.bufferExpirationTime! / 2 : // More aggressive when memory pressure is high
            this.options.bufferExpirationTime!;

        // Clean up each category
        for (const category of Object.values(BufferSizeCategory)) {
            const categoryBuffers = this.availableBuffers.get(category) || [];
            let i = 0;

            while (i < categoryBuffers.length) {
                const trackedBuffer = categoryBuffers[i];
                const age = now - trackedBuffer.lastUsedTime;

                if (age > maxIdleTime || trackedBuffer.refCount > 0) {
                    // Remove from available buffers
                    categoryBuffers.splice(i, 1);

                    if (trackedBuffer.refCount === 0) {
                        // Remove from buffers map
                        this.buffers.delete(trackedBuffer.id);

                        // Update stats
                        this.stats.totalBuffers--;
                        this.stats.idleBuffers--;
                        this.stats.totalMemory -= trackedBuffer.size;
                        this.stats.idleMemory -= trackedBuffer.size;
                        this.stats.buffersByCategory[category]--;

                        // Release resource if tracking is enabled
                        if (this.options.trackResources) {
                            resourceTracker.releaseResource(trackedBuffer.resourceId);
                        }

                        logger.debug(`Cleaned up idle buffer #${trackedBuffer.id} of size ${this.formatBytes(trackedBuffer.size)} (age: ${age}ms)`);
                    } else {
                        logger.warn(`Found buffer #${trackedBuffer.id} in available buffers with refCount ${trackedBuffer.refCount}`);
                    }
                } else {
                    i++;
                }
            }

            // Update available buffers
            this.availableBuffers.set(category, categoryBuffers);
        }

        // Log cleanup results
        logger.debug(`Buffer pool cleanup complete. Active: ${this.stats.activeBuffers}, Idle: ${this.stats.idleBuffers}, Total memory: ${this.formatBytes(this.stats.totalMemory)}`);
    }
}
