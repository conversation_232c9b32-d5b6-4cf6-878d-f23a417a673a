/**
 * Tests for the ConsolidatedStreamPipeline
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Readable, Writable } from 'stream';
import { ConsolidatedStreamPipeline } from '../services/analysis/stream/consolidatedStreamPipeline.js';
import { IStreamTransformer } from '../services/analysis/stream/baseStreamTransformer.js';
import EnhancedMemoryManager from '../utils/memory/enhancedMemoryManager.js';
import { EnhancedBufferPool } from '../utils/memory/enhancedBufferPool.js';

// Mock dependencies
vi.mock('../utils/logging/logger.js');
vi.mock('../utils/memory/enhancedMemoryManager.js');
vi.mock('../utils/memory/enhancedBufferPool.js');
vi.mock('../services/analysis/adaptive/AdaptiveProcessingManager.js');

// Mock stream transformer
class MockTransformer implements IStreamTransformer {
    private name: string;
    private bytesProcessed: number = 0;
    private chunkCount: number = 0;
    private errorCount: number = 0;
    private retryCount: number = 0;
    private startTime: number = Date.now();
    private endTime: number = 0;
    private progress: number = 0;
    private options: any = {};
    
    constructor(name: string) {
        this.name = name;
    }
    
    // Transform implementation
    _transform(chunk: any, encoding: string, callback: Function): void {
        this.bytesProcessed += chunk.length;
        this.chunkCount++;
        this.progress = Math.min(1, this.progress + 0.1);
        this.emit('progress', this.progress);
        callback(null, chunk);
    }
    
    _flush(callback: Function): void {
        this.endTime = Date.now();
        callback();
    }
    
    // IStreamTransformer methods
    getName(): string {
        return this.name;
    }
    
    getStats(): any {
        return {
            transformer: this.name,
            bytesProcessed: this.bytesProcessed,
            chunkCount: this.chunkCount,
            errorCount: this.errorCount,
            retryCount: this.retryCount,
            startTime: this.startTime,
            endTime: this.endTime || Date.now(),
            duration: (this.endTime || Date.now()) - this.startTime,
            memoryUsage: 0
        };
    }
    
    getProgress(): number {
        return this.progress;
    }
    
    async initialize(): Promise<void> {
        // No-op
    }
    
    async cleanup(): Promise<void> {
        // No-op
    }
    
    canRecover(): boolean {
        return false;
    }
    
    recoverFromError(): void {
        // No-op
    }
    
    reset(): void {
        this.bytesProcessed = 0;
        this.chunkCount = 0;
        this.errorCount = 0;
        this.retryCount = 0;
        this.startTime = Date.now();
        this.endTime = 0;
        this.progress = 0;
    }
    
    setOptions(options: any): void {
        this.options = options;
    }
    
    // EventEmitter methods
    on(event: string, listener: (...args: any[]) => void): this {
        return this;
    }
    
    once(event: string, listener: (...args: any[]) => void): this {
        return this;
    }
    
    emit(event: string, ...args: any[]): boolean {
        return true;
    }
    
    removeListener(event: string, listener: (...args: any[]) => void): this {
        return this;
    }
    
    removeAllListeners(event?: string): this {
        return this;
    }
    
    // Transform methods
    pipe<T extends NodeJS.WritableStream>(destination: T, options?: { end?: boolean }): T {
        return destination;
    }
    
    destroy(error?: Error): void {
        // No-op
    }
}

describe('ConsolidatedStreamPipeline', () => {
    let pipeline: ConsolidatedStreamPipeline;
    let mockSource: Readable;
    let mockTransformer: MockTransformer;
    
    beforeEach(() => {
        // Create a new pipeline for each test
        pipeline = new ConsolidatedStreamPipeline();
        
        // Create a mock source stream
        mockSource = new Readable({
            read() {
                this.push(Buffer.from('Test data'));
                this.push(null); // End of stream
            }
        });
        
        // Create a mock transformer
        mockTransformer = new MockTransformer('MockTransformer');
        
        // Reset mocks
        vi.clearAllMocks();
    });
    
    afterEach(async () => {
        // Clean up
        await pipeline.destroy();
    });
    
    it('should create a pipeline successfully', () => {
        expect(pipeline).toBeDefined();
    });
    
    it('should add a transformer to the pipeline', () => {
        pipeline.addTransformer(mockTransformer);
        expect(pipeline.getStats().transformerStats).toHaveProperty('MockTransformer');
    });
    
    it('should process data through the pipeline', async () => {
        // Add the transformer
        pipeline.addTransformer(mockTransformer);
        
        // Create the pipeline
        const result = await pipeline.createPipeline(0x12345678, mockSource);
        
        // Collect data from the pipeline
        const chunks: Buffer[] = [];
        for await (const chunk of result) {
            chunks.push(chunk);
        }
        
        // Check that data was processed
        expect(chunks.length).toBeGreaterThan(0);
        expect(Buffer.concat(chunks).toString()).toBe('Test data');
        
        // Check that transformer processed the data
        expect(mockTransformer.getStats().bytesProcessed).toBe('Test data'.length);
        expect(mockTransformer.getStats().chunkCount).toBe(1);
    });
    
    it('should register and use a transformer for a specific resource type', async () => {
        // Register a transformer for a specific resource type
        const resourceType = 0x12345678;
        pipeline.registerTransformer(resourceType, () => mockTransformer);
        
        // Create the pipeline for that resource type
        const result = await pipeline.createPipeline(resourceType, mockSource);
        
        // Collect data from the pipeline
        const chunks: Buffer[] = [];
        for await (const chunk of result) {
            chunks.push(chunk);
        }
        
        // Check that data was processed
        expect(chunks.length).toBeGreaterThan(0);
        expect(Buffer.concat(chunks).toString()).toBe('Test data');
        
        // Check that transformer processed the data
        expect(mockTransformer.getStats().bytesProcessed).toBe('Test data'.length);
    });
    
    it('should handle errors in the pipeline', async () => {
        // Create an error-throwing transformer
        const errorTransformer = new MockTransformer('ErrorTransformer');
        errorTransformer._transform = (chunk, encoding, callback) => {
            callback(new Error('Test error'));
        };
        
        // Add the transformer
        pipeline.addTransformer(errorTransformer);
        
        // Create the pipeline
        const resultPromise = pipeline.createPipeline(0x12345678, mockSource);
        
        // Expect the pipeline to throw an error
        await expect(resultPromise).rejects.toThrow('Test error');
    });
    
    it('should clean up resources when destroyed', async () => {
        // Add the transformer
        pipeline.addTransformer(mockTransformer);
        
        // Create the pipeline
        await pipeline.createPipeline(0x12345678, mockSource);
        
        // Spy on transformer cleanup
        const cleanupSpy = vi.spyOn(mockTransformer, 'cleanup');
        
        // Destroy the pipeline
        await pipeline.destroy();
        
        // Check that transformer was cleaned up
        expect(cleanupSpy).toHaveBeenCalled();
    });
});
