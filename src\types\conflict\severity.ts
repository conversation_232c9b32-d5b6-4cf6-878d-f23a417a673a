/**
 * Resource conflict severity levels
 */
export enum ResourceConflictSeverity {
  NONE = 'NONE',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * Maps conflict types to their default severity levels
 */
export const DefaultConflictSeverity: Record<string, ResourceConflictSeverity> = {
  RESOURCE: ResourceConflictSeverity.HIGH,
  METADATA: ResourceConflictSeverity.MEDIUM,
  VERSION: ResourceConflictSeverity.HIGH,
  DEPENDENCY: ResourceConflictSeverity.CRITICAL,
  CUSTOM: ResourceConflictSeverity.MEDIUM
}; 