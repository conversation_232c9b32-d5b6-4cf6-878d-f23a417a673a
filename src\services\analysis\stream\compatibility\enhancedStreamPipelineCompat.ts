/**
 * EnhancedStreamPipeline Compatibility Layer
 * 
 * This module provides a compatibility layer for the EnhancedStreamPipeline class.
 * It delegates to the ConsolidatedStreamPipeline while maintaining the same API.
 */

import { Readable } from 'stream';
import { EventEmitter } from 'events';
import { Logger } from '../../../../utils/logging/logger.js';
import { IStreamTransformer } from '../baseStreamTransformer.js';
import { ConsolidatedStreamPipeline, ConsolidatedStreamPipelineOptions } from '../consolidatedStreamPipeline.js';
import { IStreamPipeline, PipelineStats, StreamPipelineOptions } from '../streamPipelineBase.js';
import { WorkloadType } from '../../adaptive/AdaptiveProcessingManager.js';

// Create a logger for this module
const logger = new Logger('EnhancedStreamPipelineCompat');

/**
 * Enhanced stream pipeline options
 */
export interface EnhancedStreamPipelineOptions extends StreamPipelineOptions {
    // Hardware-aware options
    enableHardwareAwareness?: boolean;
    workloadType?: WorkloadType;
    resourceSize?: number;
    
    // Additional transformer options
    modelTransformerOptions?: any;
    animationTransformerOptions?: any;
    simDataTransformerOptions?: any;
    
    // Performance options
    enablePerformanceLogging?: boolean;
    performanceSamplingRate?: number;
}

/**
 * EnhancedStreamPipeline compatibility implementation
 */
export class EnhancedStreamPipeline extends EventEmitter implements IStreamPipeline {
    private consolidatedPipeline: ConsolidatedStreamPipeline;
    private options: EnhancedStreamPipelineOptions;
    
    /**
     * Create a new enhanced stream pipeline
     * @param options Pipeline options
     */
    constructor(options: EnhancedStreamPipelineOptions = {}) {
        super();
        
        this.options = options;
        
        // Create consolidated pipeline with the same options
        this.consolidatedPipeline = new ConsolidatedStreamPipeline(options);
        
        // Forward events from the consolidated pipeline
        this.consolidatedPipeline.on('progress', (progress) => this.emit('progress', progress));
        this.consolidatedPipeline.on('stats', (stats) => this.emit('stats', stats));
        this.consolidatedPipeline.on('error', (error) => this.emit('error', error));
        
        logger.debug('Created EnhancedStreamPipeline compatibility layer');
    }
    
    /**
     * Create a pipeline for a specific resource type
     * @param resourceType Resource type
     * @param source Source stream
     * @param options Pipeline options
     */
    public async createPipeline(
        resourceType: number, 
        source: Readable, 
        options: EnhancedStreamPipelineOptions = {}
    ): Promise<Readable> {
        // Merge options
        const mergedOptions: EnhancedStreamPipelineOptions = { ...this.options, ...options };
        
        // Pass directly to consolidated pipeline
        return this.consolidatedPipeline.createPipeline(resourceType, source, mergedOptions);
    }
    
    /**
     * Add a custom transformer to the pipeline
     * @param transformer Transformer to add
     * @param position Position to add the transformer at
     */
    public addTransformer(
        transformer: IStreamTransformer, 
        position?: number
    ): void {
        this.consolidatedPipeline.addTransformer(transformer, position);
    }
    
    /**
     * Register a transformer factory for a resource type
     * @param resourceType Resource type
     * @param factory Factory function to create a transformer
     */
    public registerTransformer(
        resourceType: number,
        factory: () => IStreamTransformer
    ): void {
        this.consolidatedPipeline.registerTransformer(resourceType, factory);
    }
    
    /**
     * Get pipeline statistics
     */
    public getStats(): PipelineStats {
        return this.consolidatedPipeline.getStats();
    }
    
    /**
     * Destroy the pipeline and clean up resources
     */
    public async destroy(): Promise<void> {
        return this.consolidatedPipeline.destroy();
    }
}
