/**
 * Error handling utilities for SimData parsing
 */

import { Logger } from '../../../../../utils/logging/logger.js';

const logger = new Logger('SimDataParserError');

/**
 * Context information for SimData parsing errors
 */
export interface SimDataErrorContext {
    version?: number;
    offset?: number;
    operation?: string;
    schemaName?: string;
    instanceName?: string;
    columnName?: string;
    bufferLength?: number;
    additionalInfo?: Record<string, any>;
}

/**
 * Creates an error context object for SimData parsing errors
 * @param version SimData version
 * @param offset Buffer offset where the error occurred
 * @param operation Operation being performed when the error occurred
 * @param additionalInfo Additional context information
 * @returns Error context object
 */
export function createSimDataErrorContext(
    version?: number,
    offset?: number,
    operation?: string,
    additionalInfo?: Record<string, any>
): SimDataErrorContext {
    return {
        version,
        offset,
        operation,
        additionalInfo
    };
}

/**
 * Creates a buffer error context object
 * @param buffer Buffer being read
 * @param offset Offset where the error occurred
 * @param operation Operation being performed
 * @returns Error context object
 */
export function createBufferErrorContext(
    buffer: Buffer,
    offset: number,
    operation: string
): SimDataErrorContext {
    return {
        offset,
        operation,
        bufferLength: buffer.length,
        additionalInfo: {
            bufferTooSmall: offset >= buffer.length,
            remainingBytes: Math.max(0, buffer.length - offset)
        }
    };
}

/**
 * Handles a SimData parsing error
 * @param error Error object
 * @param context Error context
 * @param defaultValue Default value to return
 * @returns Default value
 */
export function handleSimDataError<T>(
    error: any,
    context: SimDataErrorContext,
    defaultValue: T
): T {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Build detailed error message
    let detailedMessage = `SimData parsing error: ${errorMessage}`;
    
    if (context.version !== undefined) {
        detailedMessage += `, version: ${context.version}`;
    }
    
    if (context.operation) {
        detailedMessage += `, operation: ${context.operation}`;
    }
    
    if (context.offset !== undefined) {
        detailedMessage += `, offset: ${context.offset}`;
    }
    
    if (context.bufferLength !== undefined) {
        detailedMessage += `, buffer length: ${context.bufferLength}`;
    }
    
    if (context.schemaName) {
        detailedMessage += `, schema: ${context.schemaName}`;
    }
    
    if (context.instanceName) {
        detailedMessage += `, instance: ${context.instanceName}`;
    }
    
    if (context.columnName) {
        detailedMessage += `, column: ${context.columnName}`;
    }
    
    // Log the error
    logger.error(detailedMessage);
    
    // Return the default value
    return defaultValue;
}

/**
 * Higher-order function that wraps a function with error handling
 * @param fn Function to wrap
 * @param getContext Function to get error context
 * @param defaultValue Default value to return on error
 * @returns Wrapped function
 */
export function withSimDataErrorHandling<T, Args extends any[]>(
    fn: (...args: Args) => T,
    getContext: (...args: Args) => SimDataErrorContext,
    defaultValue: T
): (...args: Args) => T {
    return (...args: Args): T => {
        try {
            return fn(...args);
        } catch (error) {
            return handleSimDataError(error, getContext(...args), defaultValue);
        }
    };
}

/**
 * Safely reads a string from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to start reading from
 * @param length Length of the string to read
 * @param operation Operation name for error context
 * @returns The string or undefined if reading fails
 */
export function safeReadString(
    buffer: Buffer,
    offset: number,
    length: number,
    operation: string = 'readString'
): string | undefined {
    try {
        // Validate buffer bounds
        if (offset < 0 || offset + length > buffer.length) {
            return undefined;
        }
        
        return buffer.toString('utf8', offset, offset + length);
    } catch (error) {
        handleSimDataError(
            error,
            createBufferErrorContext(buffer, offset, operation),
            undefined
        );
        return undefined;
    }
}

/**
 * Safely reads a number from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to start reading from
 * @param size Size of the number (1, 2, 4, or 8 bytes)
 * @param signed Whether the number is signed
 * @param operation Operation name for error context
 * @returns The number or undefined if reading fails
 */
export function safeReadNumber(
    buffer: Buffer,
    offset: number,
    size: 1 | 2 | 4 | 8,
    signed: boolean = false,
    operation: string = 'readNumber'
): number | undefined {
    try {
        // Validate buffer bounds
        if (offset < 0 || offset + size > buffer.length) {
            return undefined;
        }
        
        switch (size) {
            case 1:
                return signed ? buffer.readInt8(offset) : buffer.readUInt8(offset);
            case 2:
                return signed ? buffer.readInt16LE(offset) : buffer.readUInt16LE(offset);
            case 4:
                return signed ? buffer.readInt32LE(offset) : buffer.readUInt32LE(offset);
            case 8:
                // For 8-byte numbers, we'll return the number as a string to avoid precision issues
                return Number(signed ? buffer.readBigInt64LE(offset) : buffer.readBigUInt64LE(offset));
            default:
                return undefined;
        }
    } catch (error) {
        handleSimDataError(
            error,
            createBufferErrorContext(buffer, offset, operation),
            undefined
        );
        return undefined;
    }
}

/**
 * Safely stringifies a value to JSON, handling BigInt values
 * @param value Value to stringify
 * @returns JSON string or undefined if stringification fails
 */
export function safeJsonStringify(value: any): string | undefined {
    try {
        return JSON.stringify(value, (_, v) => 
            typeof v === 'bigint' ? v.toString() : v
        );
    } catch (error) {
        handleSimDataError(
            error,
            { operation: 'jsonStringify' },
            undefined
        );
        return undefined;
    }
}
