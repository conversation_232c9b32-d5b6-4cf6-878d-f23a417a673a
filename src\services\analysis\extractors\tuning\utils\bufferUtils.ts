import { Logger } from '../../../../../utils/logging/logger.js';
import { TuningExtractionContext } from '../types.js';

/**
 * Helper function to inspect buffer content and log detailed information
 * @param buffer The buffer to inspect
 * @param label A descriptive label for the log
 * @param instance The instance ID of the resource
 * @param type The type ID of the resource
 * @param log The logger instance
 */
export function inspectBuffer(
    buffer: Buffer, 
    label: string, 
    instance: bigint, 
    type: number, 
    log: Logger
): void {
    log.debug(`${label} for ${instance.toString(16)} (Type: 0x${type.toString(16)})`);
    log.debug(`  Length: ${buffer.length} bytes`);
    log.debug(`  Hex (first 32 bytes): ${buffer.toString('hex', 0, Math.min(buffer.length, 32))}`);
    log.debug(`  UTF-8 (first 50 chars): "${buffer.toString('utf-8', 0, Math.min(buffer.length, 50))}"`);

    // Check for common XML issues
    const firstChar = buffer.toString('utf-8', 0, 1);
    if (firstChar !== '<') {
        log.debug(`  WARNING: <PERSON><PERSON><PERSON> does not start with '<' character, starts with '${firstChar}' (${buffer[0].toString(16)})`);
    }

    // Check for BOM or other markers
    if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        log.debug(`  WARNING: Buffer starts with UTF-8 BOM (EF BB BF)`);
    }
}

/**
 * Preprocesses a buffer to handle common issues with Sims 4 XML resources
 * @param buffer The original buffer
 * @param instance The instance ID of the resource
 * @param type The type ID of the resource
 * @param log The logger instance
 * @returns The processed buffer
 */
export function preprocessBuffer(
    buffer: Buffer,
    instance: bigint,
    type: number,
    log: Logger
): Buffer {
    let processedBuffer = buffer;

    // 1. Check for and remove the known problematic 4-byte header (03 00 00 00)
    if (buffer.length >= 4 && buffer.readUInt32LE(0) === 0x00000003) {
        log.info(`Detected 4-byte header (03 00 00 00) for resource ${instance.toString(16)}. Slicing 4 bytes.`);
        processedBuffer = buffer.subarray(4);
        inspectBuffer(processedBuffer, "After removing 03 00 00 00 header", instance, type, log);
    }

    // 2. Check for and remove a leading 'D' (0x44) if present
    if (processedBuffer.length >= 1 && processedBuffer[0] === 0x44) {
        log.info(`Detected leading 'D' (0x44) for resource ${instance.toString(16)}. Slicing 1 byte.`);
        processedBuffer = processedBuffer.subarray(1);
        inspectBuffer(processedBuffer, "After removing leading 'D'", instance, type, log);
    }

    // 3. Check for any non-XML leading bytes and find the first '<' character
    if (processedBuffer.length > 0 && processedBuffer[0] !== 0x3C) { // 0x3C is '<'
        const lessThanIndex = processedBuffer.indexOf(0x3C);
        if (lessThanIndex > 0) {
            log.info(`Found non-XML leading bytes. First '<' at position ${lessThanIndex}. Slicing ${lessThanIndex} bytes.`);
            processedBuffer = processedBuffer.subarray(lessThanIndex);
            inspectBuffer(processedBuffer, "After finding first '<'", instance, type, log);
        }
    }

    // 4. Check for UTF-8 BOM (EF BB BF) and remove if present
    if (processedBuffer.length >= 3 &&
        processedBuffer[0] === 0xEF &&
        processedBuffer[1] === 0xBB &&
        processedBuffer[2] === 0xBF) {
        log.info(`Detected UTF-8 BOM for resource ${instance.toString(16)}. Removing BOM.`);
        processedBuffer = processedBuffer.subarray(3);
        inspectBuffer(processedBuffer, "After removing UTF-8 BOM", instance, type, log);
    }

    // Final check of processed buffer before parsing
    log.debug(`Final processed buffer before parsing for ${instance.toString(16)} (Type: 0x${type.toString(16)})`);
    log.debug(`  Length: ${processedBuffer.length} bytes`);
    log.debug(`  Starts with: ${processedBuffer.toString('utf-8', 0, Math.min(processedBuffer.length, 100))}`);

    return processedBuffer;
}

/**
 * Checks if a buffer is a valid XML buffer
 * @param buffer The buffer to check
 * @param log The logger instance
 * @returns Whether the buffer is a valid XML buffer
 */
export function isValidXmlBuffer(buffer: Buffer, log: Logger): boolean {
    if (buffer.length < 5) {
        log.warn(`Buffer too small to be valid XML: ${buffer.length} bytes`);
        return false;
    }

    // Check if the buffer starts with '<' (possibly after BOM)
    const startIndex = buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF ? 3 : 0;
    
    // Check for XML declaration or root element
    if (buffer[startIndex] === 0x3C) { // '<'
        // Check for XML declaration
        if (buffer.length >= startIndex + 5 && 
            buffer[startIndex + 1] === 0x3F && // '?'
            buffer[startIndex + 2] === 0x78 && // 'x'
            buffer[startIndex + 3] === 0x6D && // 'm'
            buffer[startIndex + 4] === 0x6C) { // 'l'
            return true;
        }
        
        // Check for root element
        return true; // If it starts with '<', assume it's XML
    }
    
    return false;
}

/**
 * Creates a buffer inspection context for error handling
 * @param buffer The buffer to inspect
 * @param context The extraction context
 * @returns The buffer inspection context
 */
export function createBufferInspectionContext(buffer: Buffer, context: TuningExtractionContext): Record<string, any> {
    return {
        bufferLength: buffer.length,
        bufferHex: buffer.toString('hex', 0, Math.min(buffer.length, 32)),
        bufferUtf8: buffer.toString('utf-8', 0, Math.min(buffer.length, 50)),
        hasBom: buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF,
        startsWithXml: buffer.length > 0 && buffer[0] === 0x3C, // '<'
        resourceId: context.resourceId,
        resourceType: context.key.type.toString(16),
        resourceInstance: context.key.instance.toString(16)
    };
}
