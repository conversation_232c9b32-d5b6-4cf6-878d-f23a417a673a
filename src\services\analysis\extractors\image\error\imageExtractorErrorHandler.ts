/**
 * Error handling utilities for image extraction
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ImageErrorContext, ImageHeaderInfo, ImageParseResult } from '../types.js';

// Create a logger instance
const log = new Logger('ImageExtractorErrorHandler');

/**
 * Creates an error context for image extraction
 * @param resourceId Resource ID
 * @param instanceId Instance ID (as hex string)
 * @param operation Operation being performed
 * @param additionalInfo Additional information about the error
 * @returns Error context
 */
export function createImageErrorContext(
    resourceId: number,
    instanceId: string,
    operation: string,
    additionalInfo?: Record<string, any>
): ImageErrorContext {
    return {
        resourceId,
        instanceId,
        operation,
        additionalInfo
    };
}

/**
 * Handles an error during image extraction
 * @param error Error object
 * @param context Error context
 * @param defaultResult Default result to return if error handling fails
 * @returns Image parse result with error information
 */
export function handleImageError(
    error: any,
    context: ImageErrorContext,
    defaultResult?: ImageParseResult
): ImageParseResult {
    try {
        // Extract error message
        const errorMessage = error?.message || String(error);
        
        // Log the error with context
        log.error(`Error in ${context.operation} for resource ${context.resourceId} (instance: ${context.instanceId}): ${errorMessage}`, {
            resourceId: context.resourceId,
            instanceId: context.instanceId,
            operation: context.operation,
            error: errorMessage,
            ...context.additionalInfo
        });
        
        // Create a minimal header with format information
        const header: ImageHeaderInfo = {
            format: context.format || 'Unknown'
        };
        
        // Create a content snippet with error information
        const contentSnippet = `[Image Parse Error: ${context.operation}]`;
        
        // Return a result with error information
        return {
            header,
            contentSnippet,
            dependencies: [],
            error: errorMessage
        };
    } catch (handlerError: any) {
        // If error handling itself fails, log and return default result
        log.error(`Error handling failed: ${handlerError?.message || handlerError}`);
        
        if (defaultResult) {
            return defaultResult;
        }
        
        // Return a minimal result
        return {
            header: { format: 'Unknown' },
            contentSnippet: '[Image Parse Error]',
            dependencies: [],
            error: 'Error handling failed'
        };
    }
}

/**
 * Higher-order function that wraps an image extraction function with error handling
 * @param fn Function to wrap
 * @returns Wrapped function with error handling
 */
export function withImageExtractionErrorHandling<T extends any[], R>(
    fn: (...args: T) => Promise<R> | R
): (...args: T) => Promise<R> {
    return async function(...args: T): Promise<R> {
        try {
            return await fn(...args);
        } catch (error: any) {
            // Extract context information from arguments if possible
            const resourceId = typeof args[2] === 'number' ? args[2] : 0;
            const instanceId = args[0]?.instance?.toString(16) || 'unknown';
            
            // Create error context
            const context = createImageErrorContext(
                resourceId,
                instanceId,
                fn.name || 'imageExtraction'
            );
            
            // Handle the error
            const result = handleImageError(error, context);
            
            // Return the result as R (this is a type assertion)
            return result as unknown as R;
        }
    };
}
