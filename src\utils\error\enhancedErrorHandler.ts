/**
 * Enhanced Error Handler
 * 
 * This module provides a centralized error handling system for the application.
 * It handles error logging, reporting, and recovery.
 */

import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import { 
    AppError, 
    ErrorCategory, 
    ErrorCode, 
    ErrorContext, 
    ErrorMessage, 
    ErrorReport, 
    ErrorSeverity, 
    RecoveryOptions 
} from './errorTypes.js';

// Create a logger for this module
const logger = new Logger('EnhancedErrorHandler');

/**
 * Error handler options
 */
export interface ErrorHandlerOptions {
    enableTelemetry?: boolean;
    telemetryEndpoint?: string;
    telemetryBatchSize?: number;
    telemetryBatchInterval?: number;
    enableErrorDialog?: boolean;
    maxErrorsPerSession?: number;
    maxErrorsPerMinute?: number;
    defaultRecoveryOptions?: Partial<RecoveryOptions>;
}

/**
 * Enhanced error handler class
 */
export class EnhancedErrorHandler extends EventEmitter {
    private static instance: EnhancedErrorHandler;
    private options: ErrorHandlerOptions;
    private errorCount: number = 0;
    private errorCountByMinute: Map<number, number> = new Map();
    private pendingReports: ErrorReport[] = [];
    private telemetryTimer?: NodeJS.Timeout;
    private userConsent: boolean = false;
    
    /**
     * Create a new error handler
     * @param options Error handler options
     */
    private constructor(options: ErrorHandlerOptions = {}) {
        super();
        
        this.options = {
            enableTelemetry: false,
            telemetryBatchSize: 10,
            telemetryBatchInterval: 60000, // 1 minute
            enableErrorDialog: true,
            maxErrorsPerSession: 100,
            maxErrorsPerMinute: 10,
            defaultRecoveryOptions: {
                canRetry: false,
                canFallback: false,
                canSkip: false,
                canAbort: true,
                canRestart: false
            },
            ...options
        };
        
        // Set up telemetry timer if enabled
        if (this.options.enableTelemetry && this.options.telemetryEndpoint) {
            this.startTelemetryTimer();
        }
        
        logger.info('Enhanced error handler initialized');
    }
    
    /**
     * Get the error handler instance
     * @param options Error handler options
     * @returns Error handler instance
     */
    public static getInstance(options?: ErrorHandlerOptions): EnhancedErrorHandler {
        if (!EnhancedErrorHandler.instance) {
            EnhancedErrorHandler.instance = new EnhancedErrorHandler(options);
        } else if (options) {
            // Update options if provided
            EnhancedErrorHandler.instance.options = {
                ...EnhancedErrorHandler.instance.options,
                ...options
            };
        }
        
        return EnhancedErrorHandler.instance;
    }
    
    /**
     * Handle an error
     * @param error Error to handle
     * @param context Additional error context
     * @returns True if the error was handled, false otherwise
     */
    public handleError(error: Error | AppError, context: ErrorContext = {}): boolean {
        try {
            // Check if we've exceeded the maximum errors per session
            if (this.errorCount >= this.options.maxErrorsPerSession!) {
                logger.error(`Maximum errors per session exceeded (${this.errorCount}), suppressing error`);
                return false;
            }
            
            // Check if we've exceeded the maximum errors per minute
            const currentMinute = Math.floor(Date.now() / 60000);
            const errorsThisMinute = this.errorCountByMinute.get(currentMinute) || 0;
            
            if (errorsThisMinute >= this.options.maxErrorsPerMinute!) {
                logger.error(`Maximum errors per minute exceeded (${errorsThisMinute}), suppressing error`);
                return false;
            }
            
            // Update error counts
            this.errorCount++;
            this.errorCountByMinute.set(currentMinute, errorsThisMinute + 1);
            
            // Clean up old minute counts
            this.cleanupErrorCounts();
            
            // Convert to AppError if needed
            const appError = this.ensureAppError(error, context);
            
            // Log the error
            this.logError(appError);
            
            // Report the error
            this.reportError(appError);
            
            // Emit error event
            this.emit('error', appError);
            
            // Show error dialog if enabled
            if (this.options.enableErrorDialog) {
                this.showErrorDialog(appError);
            }
            
            return true;
        } catch (handlerError) {
            // If error handling fails, log the original error and the handler error
            logger.error('Error handler failed:', handlerError);
            logger.error('Original error:', error);
            return false;
        }
    }
    
    /**
     * Set user consent for telemetry
     * @param consent User consent
     */
    public setUserConsent(consent: boolean): void {
        this.userConsent = consent;
        
        if (consent && this.options.enableTelemetry && !this.telemetryTimer) {
            this.startTelemetryTimer();
        } else if (!consent && this.telemetryTimer) {
            this.stopTelemetryTimer();
            this.pendingReports = [];
        }
        
        logger.info(`User telemetry consent set to ${consent}`);
    }
    
    /**
     * Create an application error
     * @param errorCode Error code
     * @param message Error message
     * @param options Additional error options
     * @returns Application error
     */
    public createError(
        errorCode: ErrorCode,
        message: string,
        options: {
            category?: ErrorCategory;
            severity?: ErrorSeverity;
            context?: ErrorContext;
            userMessage?: Partial<ErrorMessage>;
            recovery?: Partial<RecoveryOptions>;
            cause?: Error;
        } = {}
    ): AppError {
        return new AppError(errorCode, message, options);
    }
    
    /**
     * Ensure an error is an AppError
     * @param error Error to convert
     * @param context Additional error context
     * @returns Application error
     * @private
     */
    private ensureAppError(error: Error | AppError, context: ErrorContext = {}): AppError {
        if (error instanceof AppError) {
            // Merge context if provided
            if (Object.keys(context).length > 0) {
                error.context = {
                    ...error.context,
                    ...context
                };
            }
            
            return error;
        }
        
        // Convert to AppError
        return new AppError(
            ErrorCode.UNKNOWN_ERROR,
            error.message,
            {
                category: ErrorCategory.UNKNOWN,
                severity: ErrorSeverity.ERROR,
                context,
                cause: error
            }
        );
    }
    
    /**
     * Log an error
     * @param error Error to log
     * @private
     */
    private logError(error: AppError): void {
        const logMessage = `[${error.category}:${error.errorCode}] ${error.message}`;
        
        switch (error.severity) {
            case ErrorSeverity.FATAL:
                logger.error(`FATAL: ${logMessage}`, error);
                break;
                
            case ErrorSeverity.ERROR:
                logger.error(logMessage, error);
                break;
                
            case ErrorSeverity.WARNING:
                logger.warn(logMessage, error);
                break;
                
            case ErrorSeverity.INFO:
                logger.info(logMessage, error);
                break;
        }
    }
    
    /**
     * Report an error for telemetry
     * @param error Error to report
     * @private
     */
    private reportError(error: AppError): void {
        if (!this.options.enableTelemetry || !this.userConsent) {
            return;
        }
        
        // Create error report
        const report = error.createReport({
            // Add session and app information
            sessionId: this.getSessionId(),
            appVersion: this.getAppVersion(),
            osInfo: this.getOsInfo()
        });
        
        // Add to pending reports
        this.pendingReports.push(report);
        
        // Send reports if batch size reached
        if (this.pendingReports.length >= this.options.telemetryBatchSize!) {
            this.sendTelemetryReports();
        }
    }
    
    /**
     * Show an error dialog
     * @param error Error to show
     * @private
     */
    private showErrorDialog(error: AppError): void {
        // In a real implementation, this would show a dialog
        // For now, just emit an event
        this.emit('errorDialog', error);
    }
    
    /**
     * Start the telemetry timer
     * @private
     */
    private startTelemetryTimer(): void {
        if (this.telemetryTimer) {
            clearInterval(this.telemetryTimer);
        }
        
        this.telemetryTimer = setInterval(() => {
            this.sendTelemetryReports();
        }, this.options.telemetryBatchInterval);
    }
    
    /**
     * Stop the telemetry timer
     * @private
     */
    private stopTelemetryTimer(): void {
        if (this.telemetryTimer) {
            clearInterval(this.telemetryTimer);
            this.telemetryTimer = undefined;
        }
    }
    
    /**
     * Send telemetry reports
     * @private
     */
    private sendTelemetryReports(): void {
        if (!this.options.enableTelemetry || !this.userConsent || !this.options.telemetryEndpoint) {
            return;
        }
        
        if (this.pendingReports.length === 0) {
            return;
        }
        
        // In a real implementation, this would send reports to the telemetry endpoint
        // For now, just log the reports
        logger.debug(`Sending ${this.pendingReports.length} telemetry reports`);
        
        // Clear pending reports
        this.pendingReports = [];
    }
    
    /**
     * Clean up old error counts
     * @private
     */
    private cleanupErrorCounts(): void {
        const currentMinute = Math.floor(Date.now() / 60000);
        
        // Remove counts older than 5 minutes
        for (const minute of this.errorCountByMinute.keys()) {
            if (minute < currentMinute - 5) {
                this.errorCountByMinute.delete(minute);
            }
        }
    }
    
    /**
     * Get the session ID
     * @returns Session ID
     * @private
     */
    private getSessionId(): string {
        // In a real implementation, this would get the session ID
        return 'session-' + Date.now();
    }
    
    /**
     * Get the application version
     * @returns Application version
     * @private
     */
    private getAppVersion(): string {
        // In a real implementation, this would get the app version
        return '1.0.0';
    }
    
    /**
     * Get the OS information
     * @returns OS information
     * @private
     */
    private getOsInfo(): { platform?: string; version?: string; arch?: string } {
        // In a real implementation, this would get the OS info
        return {
            platform: process.platform,
            arch: process.arch
        };
    }
}
