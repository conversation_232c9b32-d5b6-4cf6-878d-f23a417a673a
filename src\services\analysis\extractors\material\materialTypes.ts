/**
 * Types for Material extraction
 */

/**
 * Material resource structure information based on Sims 4 file format
 * This is a simplified representation for metadata extraction
 */
export interface MaterialHeaderInfo {
    format: string;         // Format identifier (e.g., "MTST", "MTRL", "MTDT", "TXMT")
    version: number;        // Format version
    materialCount: number;  // Number of materials in the set (for MTST)
    flags: number;          // Format-specific flags
    shaderType?: string;    // Type of shader used
    textureCount?: number;  // Number of textures referenced
    parameterCount?: number; // Number of material parameters
    materialNames?: string[]; // Names of materials
    textureReferences?: { type: string, path: string }[]; // Texture references
    parameters?: { name: string, value: any }[]; // Material parameters
}

/**
 * Material parameter types
 */
export enum MaterialParameterType {
    FLOAT = 1,
    INTEGER = 2,
    VECTOR4 = 3,
    STRING = 4,
    BOOLEAN = 5
}

/**
 * Material parameter value
 */
export type MaterialParameterValue = 
    | number 
    | string 
    | boolean 
    | { x: number, y: number, z: number, w: number } 
    | null;

/**
 * Material parameter
 */
export interface MaterialParameter {
    name: string;
    value: MaterialParameterValue;
}

/**
 * Texture reference
 */
export interface TextureReference {
    type: string;
    path: string;
}
