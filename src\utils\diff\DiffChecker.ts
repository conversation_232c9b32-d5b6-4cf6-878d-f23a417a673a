/**
 * DiffChecker utility for comparing XML and other structured data
 */

/**
 * Compares two objects and returns the differences
 * @param obj1 First object to compare
 * @param obj2 Second object to compare
 * @returns Object containing the differences
 */
export function compareObjects(
  obj1: any,
  obj2: any
): { added: string[]; removed: string[]; changed: string[] } {
  const result = {
    added: [] as string[],
    removed: [] as string[],
    changed: [] as string[],
  };

  // Check for properties in obj2 that are not in obj1 (added)
  for (const key in obj2) {
    if (!(key in obj1)) {
      result.added.push(key);
    } else if (
      typeof obj2[key] === 'object' &&
      obj2[key] !== null &&
      typeof obj1[key] === 'object' &&
      obj1[key] !== null
    ) {
      // Recursively compare nested objects
      const nestedDiff = compareObjects(obj1[key], obj2[key]);

      // Add nested differences with path prefix
      nestedDiff.added.forEach(prop => result.added.push(`${key}.${prop}`));
      nestedDiff.removed.forEach(prop => result.removed.push(`${key}.${prop}`));
      nestedDiff.changed.forEach(prop => result.changed.push(`${key}.${prop}`));
    } else if (obj1[key] !== obj2[key]) {
      // Values are different
      result.changed.push(key);
    }
  }

  // Check for properties in obj1 that are not in obj2 (removed)
  for (const key in obj1) {
    if (!(key in obj2)) {
      result.removed.push(key);
    }
  }

  return result;
}

/**
 * Compares two XML strings and returns the differences
 * @param xml1 First XML string to compare
 * @param xml2 Second XML string to compare
 * @returns Object containing the differences
 */
export function compareXml(
  xml1: string,
  xml2: string
): { added: string[]; removed: string[]; changed: string[] } {
  try {
    // Simple XML comparison by converting to string and comparing
    if (xml1 === xml2) {
      return { added: [], removed: [], changed: [] };
    }

    // For a more detailed comparison, we would need to parse the XML
    // and compare the DOM trees, but for now we'll just return a simple diff
    return {
      added: [],
      removed: [],
      changed: ['xml_content'],
    };
  } catch (error) {
    console.error('Error comparing XML:', error);
    return {
      added: [],
      removed: [],
      changed: ['error_comparing'],
    };
  }
}

/**
 * Calculates the difference percentage between two objects
 * @param obj1 First object to compare
 * @param obj2 Second object to compare
 * @returns Percentage of difference (0-100)
 */
export function calculateDiffPercentage(obj1: any, obj2: any): number {
  const diff = compareObjects(obj1, obj2);
  const totalDiffs = diff.added.length + diff.removed.length + diff.changed.length;

  // Count total properties in both objects
  const totalProps = new Set([...Object.keys(obj1), ...Object.keys(obj2)]).size;

  if (totalProps === 0) return 0;

  return Math.min(100, (totalDiffs / totalProps) * 100);
}

export default {
  compareObjects,
  compareXml,
  calculateDiffPercentage,
};
