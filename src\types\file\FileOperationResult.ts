/**
 * Interface for file operation results
 */
export interface FileOperationResult {
  /**
   * Whether the operation was successful
   */
  success: boolean;

  /**
   * The error message if the operation failed
   */
  error?: string;

  /**
   * The path of the file that was operated on
   */
  path: string;

  /**
   * The type of operation that was performed
   */
  operation: 'read' | 'write' | 'delete' | 'move' | 'copy' | 'create';

  /**
   * The result of the operation
   */
  result?: any;

  /**
   * Additional metadata about the operation
   */
  metadata?: Record<string, unknown>;
} 
