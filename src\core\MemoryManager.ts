/**
 * Enhanced Memory Manager for Batch Processing
 * 
 * Implements the superior pattern from the comprehensive implementation plan:
 * - 500-item batch processing with automatic cleanup
 * - Memory pressure monitoring and adaptive processing
 * - Force garbage collection during high memory pressure
 * 
 * Target Performance Improvements:
 * - Memory usage: 90% → <50% on 8GB systems (45% reduction)
 * - Batch processing: 500 items per batch
 * - Memory leaks: Zero detected memory leaks
 */

import { Logger } from '../utils/logging/logger.js';
import { DatabaseService } from '../services/databaseService.js';
import EnhancedMemoryManager from '../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker } from '../utils/memory/resourceTracker.js';
import { formatBytes } from '../utils/formatting/formatUtils.js';
import { EventEmitter } from 'events';

// Create a logger for this module
const logger = new Logger('BatchMemoryManager');

/**
 * Batch processing item interface
 */
export interface BatchItem {
    id: string;
    data: any;
    size?: number;
    priority?: number;
}

/**
 * Batch processing result
 */
export interface BatchResult {
    batchId: string;
    processedItems: number;
    failedItems: number;
    totalSize: number;
    processingTime: number;
    memoryUsed: number;
    errors: string[];
}

/**
 * Memory pressure levels
 */
export enum MemoryPressureLevel {
    LOW = 'LOW',           // < 50% memory usage
    MODERATE = 'MODERATE', // 50-70% memory usage
    HIGH = 'HIGH',         // 70-85% memory usage
    CRITICAL = 'CRITICAL', // 85-95% memory usage
    EMERGENCY = 'EMERGENCY' // > 95% memory usage
}

/**
 * Batch processing options
 */
export interface BatchProcessingOptions {
    batchSize?: number;
    maxMemoryUsage?: number; // Maximum memory usage percentage (0-1)
    adaptiveBatching?: boolean;
    forceGcThreshold?: number; // Memory pressure threshold for forced GC
    maxRetries?: number;
    retryDelay?: number;
    enableProgressReporting?: boolean;
    progressCallback?: (progress: BatchProgress) => void;
}

/**
 * Batch progress information
 */
export interface BatchProgress {
    batchId: string;
    totalBatches: number;
    completedBatches: number;
    currentBatchSize: number;
    totalItems: number;
    processedItems: number;
    memoryPressure: MemoryPressureLevel;
    memoryUsage: {
        heapUsed: number;
        heapTotal: number;
        percentage: number;
    };
    estimatedTimeRemaining: number;
}

/**
 * Enhanced Memory Manager for batch processing operations
 */
export class MemoryManager extends EventEmitter {
    private static instance: MemoryManager;
    private enhancedMemoryManager!: EnhancedMemoryManager;
    private resourceTracker!: ResourceTracker;
    private databaseService!: DatabaseService;
    private isInitialized = false;
    
    // Batch processing state
    private activeBatches = new Map<string, BatchProgress>();
    private pendingResources: any[] = [];
    private pendingConflicts: any[] = [];
    private pendingMetadata: any[] = [];
    
    // Configuration
    private readonly DEFAULT_BATCH_SIZE = 500;
    private readonly MIN_BATCH_SIZE = 50;
    private readonly MAX_BATCH_SIZE = 1000;
    private readonly MEMORY_PRESSURE_THRESHOLD = 0.8; // 80%
    private readonly CRITICAL_MEMORY_THRESHOLD = 0.9; // 90%
    private readonly EMERGENCY_MEMORY_THRESHOLD = 0.95; // 95%
    
    // Performance tracking
    private stats = {
        totalBatches: 0,
        totalItemsProcessed: 0,
        totalMemoryReclaimed: 0,
        forcedGcCount: 0,
        adaptiveBatchAdjustments: 0,
        averageBatchSize: 0,
        averageProcessingTime: 0,
        memoryEfficiencyScore: 0
    };

    /**
     * Private constructor to enforce Singleton pattern
     */
    private constructor() {
        super();
        this.setMaxListeners(100);
    }

    /**
     * Get the MemoryManager instance (Singleton pattern)
     */
    public static getInstance(): MemoryManager {
        if (!MemoryManager.instance) {
            MemoryManager.instance = new MemoryManager();
        }
        return MemoryManager.instance;
    }

    /**
     * Initialize the memory manager
     */
    public async initialize(databaseService?: DatabaseService): Promise<void> {
        if (this.isInitialized) {
            logger.warn('MemoryManager is already initialized');
            return;
        }

        try {
            // Initialize enhanced memory manager
            this.enhancedMemoryManager = EnhancedMemoryManager.getInstance({
                thresholds: {
                    warning: 4 * 1024 * 1024 * 1024,  // 4GB warning
                    critical: 6 * 1024 * 1024 * 1024, // 6GB critical
                    emergency: 7 * 1024 * 1024 * 1024 // 7GB emergency
                },
                autoGcEnabled: true,
                trackingEnabled: true,
                trackingIntervalMs: 5000,
                detailedTracking: true,
                logLevel: 'info'
            });

            this.resourceTracker = ResourceTracker.getInstance();
            
            // Initialize memory manager
            this.enhancedMemoryManager.initialize();

            // Set up database service
            if (databaseService) {
                this.databaseService = databaseService;
            } else {
                this.databaseService = DatabaseService.getInstance();
            }

            this.isInitialized = true;
            logger.info('MemoryManager initialized successfully');

        } catch (error) {
            logger.error('Failed to initialize MemoryManager:', error);
            throw error;
        }
    }

    /**
     * Process items in batches with memory management
     */
    public async processBatch<T, R>(
        items: T[],
        processor: (batch: T[]) => Promise<R[]>,
        options: BatchProcessingOptions = {}
    ): Promise<R[]> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        const batchId = `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const startTime = Date.now();
        
        // Set default options
        const opts: Required<BatchProcessingOptions> = {
            batchSize: options.batchSize || this.DEFAULT_BATCH_SIZE,
            maxMemoryUsage: options.maxMemoryUsage || this.MEMORY_PRESSURE_THRESHOLD,
            adaptiveBatching: options.adaptiveBatching !== false,
            forceGcThreshold: options.forceGcThreshold || this.MEMORY_PRESSURE_THRESHOLD,
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000,
            enableProgressReporting: options.enableProgressReporting !== false,
            progressCallback: options.progressCallback || (() => {})
        };

        logger.info(`Starting batch processing ${batchId} for ${items.length} items`);

        const results: R[] = [];
        const errors: string[] = [];
        let currentBatchSize = opts.batchSize;
        
        // Calculate batches
        const totalBatches = Math.ceil(items.length / currentBatchSize);
        let completedBatches = 0;
        let processedItems = 0;

        // Initialize progress tracking
        const progress: BatchProgress = {
            batchId,
            totalBatches,
            completedBatches: 0,
            currentBatchSize,
            totalItems: items.length,
            processedItems: 0,
            memoryPressure: this.getMemoryPressureLevel(),
            memoryUsage: this.getMemoryUsage(),
            estimatedTimeRemaining: 0
        };

        this.activeBatches.set(batchId, progress);

        try {
            for (let i = 0; i < items.length; i += currentBatchSize) {
                // Check memory pressure before processing batch
                const memoryPressure = this.enhancedMemoryManager.getMemoryPressure();
                
                if (memoryPressure > opts.forceGcThreshold) {
                    logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%), forcing cleanup`);
                    await this.forceGarbageCollection();
                    
                    // Adjust batch size if adaptive batching is enabled
                    if (opts.adaptiveBatching) {
                        currentBatchSize = this.calculateAdaptiveBatchSize(memoryPressure, currentBatchSize);
                        this.stats.adaptiveBatchAdjustments++;
                    }
                }

                // Create batch
                const batch = items.slice(i, i + currentBatchSize);
                const batchStartTime = Date.now();
                const batchStartMemory = process.memoryUsage().heapUsed;

                try {
                    // Process batch with retry logic
                    const batchResults = await this.processBatchWithRetry(
                        batch,
                        processor,
                        opts.maxRetries,
                        opts.retryDelay
                    );

                    results.push(...batchResults);
                    processedItems += batch.length;
                    completedBatches++;

                    // Update statistics
                    const batchEndTime = Date.now();
                    const batchEndMemory = process.memoryUsage().heapUsed;
                    const batchProcessingTime = batchEndTime - batchStartTime;
                    const memoryUsed = batchEndMemory - batchStartMemory;

                    this.stats.totalBatches++;
                    this.stats.totalItemsProcessed += batch.length;
                    this.stats.averageBatchSize = this.stats.totalItemsProcessed / this.stats.totalBatches;
                    this.stats.averageProcessingTime = (this.stats.averageProcessingTime * (this.stats.totalBatches - 1) + batchProcessingTime) / this.stats.totalBatches;

                    // Update progress
                    progress.completedBatches = completedBatches;
                    progress.processedItems = processedItems;
                    progress.currentBatchSize = currentBatchSize;
                    progress.memoryPressure = this.getMemoryPressureLevel();
                    progress.memoryUsage = this.getMemoryUsage();
                    progress.estimatedTimeRemaining = this.calculateEstimatedTimeRemaining(
                        completedBatches,
                        totalBatches,
                        Date.now() - startTime
                    );

                    // Report progress
                    if (opts.enableProgressReporting) {
                        opts.progressCallback(progress);
                        this.emit('batchProgress', progress);
                    }

                    logger.debug(`Batch ${completedBatches}/${totalBatches} completed: ${batch.length} items in ${batchProcessingTime}ms, memory: ${formatBytes(memoryUsed)}`);

                } catch (error: any) {
                    logger.error(`Batch ${completedBatches + 1} failed:`, error);
                    errors.push(`Batch ${completedBatches + 1}: ${error.message}`);
                    
                    // Continue with next batch instead of failing entire operation
                    processedItems += batch.length; // Count as processed even if failed
                    completedBatches++;
                }

                // Force cleanup after each batch to prevent memory accumulation
                await this.cleanupAfterBatch();
            }

            // Final cleanup
            await this.forceGarbageCollection();

            const totalTime = Date.now() - startTime;
            const finalMemoryUsage = this.getMemoryUsage();

            logger.info(`Batch processing ${batchId} completed: ${processedItems}/${items.length} items in ${totalTime}ms`);
            logger.info(`Final memory usage: ${formatBytes(finalMemoryUsage.heapUsed)} (${finalMemoryUsage.percentage.toFixed(1)}%)`);

            // Update memory efficiency score
            this.updateMemoryEfficiencyScore(finalMemoryUsage.percentage);

            return results;

        } catch (error: any) {
            logger.error(`Batch processing ${batchId} failed:`, error);
            throw error;
        } finally {
            // Clean up progress tracking
            this.activeBatches.delete(batchId);
        }
    }

    /**
     * Process batch with retry logic
     */
    private async processBatchWithRetry<T, R>(
        batch: T[],
        processor: (batch: T[]) => Promise<R[]>,
        maxRetries: number,
        retryDelay: number
    ): Promise<R[]> {
        let lastError: Error | null = null;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await processor(batch);
            } catch (error: any) {
                lastError = error;
                
                if (attempt < maxRetries) {
                    logger.warn(`Batch processing attempt ${attempt + 1} failed, retrying in ${retryDelay}ms:`, error.message);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    
                    // Force cleanup before retry
                    await this.forceGarbageCollection();
                } else {
                    logger.error(`Batch processing failed after ${maxRetries + 1} attempts:`, error);
                }
            }
        }

        throw lastError;
    }

    /**
     * Calculate adaptive batch size based on memory pressure
     */
    private calculateAdaptiveBatchSize(memoryPressure: number, currentBatchSize: number): number {
        if (memoryPressure > this.EMERGENCY_MEMORY_THRESHOLD) {
            // Emergency: Use minimum batch size
            return Math.max(this.MIN_BATCH_SIZE, Math.floor(currentBatchSize * 0.25));
        } else if (memoryPressure > this.CRITICAL_MEMORY_THRESHOLD) {
            // Critical: Reduce batch size significantly
            return Math.max(this.MIN_BATCH_SIZE, Math.floor(currentBatchSize * 0.5));
        } else if (memoryPressure > this.MEMORY_PRESSURE_THRESHOLD) {
            // High pressure: Reduce batch size moderately
            return Math.max(this.MIN_BATCH_SIZE, Math.floor(currentBatchSize * 0.75));
        } else if (memoryPressure < 0.5) {
            // Low pressure: Can increase batch size
            return Math.min(this.MAX_BATCH_SIZE, Math.floor(currentBatchSize * 1.25));
        }

        // Normal pressure: Keep current size
        return currentBatchSize;
    }

    /**
     * Force garbage collection and cleanup
     */
    public async forceGarbageCollection(): Promise<void> {
        const beforeMemory = process.memoryUsage().heapUsed;

        try {
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
                this.stats.forcedGcCount++;
            }

            // Additional cleanup
            await this.cleanupPendingItems();

            // Wait a bit for GC to complete
            await new Promise(resolve => setTimeout(resolve, 100));

            const afterMemory = process.memoryUsage().heapUsed;
            const memoryReclaimed = beforeMemory - afterMemory;

            if (memoryReclaimed > 0) {
                this.stats.totalMemoryReclaimed += memoryReclaimed;
                logger.debug(`Garbage collection reclaimed ${formatBytes(memoryReclaimed)}`);
            }

        } catch (error) {
            logger.error('Error during garbage collection:', error);
        }
    }

    /**
     * Cleanup after batch processing
     */
    private async cleanupAfterBatch(): Promise<void> {
        // Process pending items if they've reached batch size
        await this.processPendingItems();

        // Force minor cleanup
        if (global.gc) {
            global.gc();
        }
    }

    /**
     * Add resource to pending batch
     */
    public addPendingResource(resource: any): void {
        this.pendingResources.push(resource);
        
        if (this.pendingResources.length >= this.DEFAULT_BATCH_SIZE) {
            // Process asynchronously to avoid blocking
            setImmediate(() => this.processPendingItems());
        }
    }

    /**
     * Add conflict to pending batch
     */
    public addPendingConflict(conflict: any): void {
        this.pendingConflicts.push(conflict);
        
        if (this.pendingConflicts.length >= this.DEFAULT_BATCH_SIZE) {
            // Process asynchronously to avoid blocking
            setImmediate(() => this.processPendingItems());
        }
    }

    /**
     * Add metadata to pending batch
     */
    public addPendingMetadata(metadata: any): void {
        this.pendingMetadata.push(metadata);
        
        if (this.pendingMetadata.length >= this.DEFAULT_BATCH_SIZE) {
            // Process asynchronously to avoid blocking
            setImmediate(() => this.processPendingItems());
        }
    }

    /**
     * Process all pending items
     */
    private async processPendingItems(): Promise<void> {
        try {
            // Process pending resources
            if (this.pendingResources.length > 0) {
                // Use existing database methods for batch processing
                await this.databaseService.saveResources(this.pendingResources);
                this.pendingResources = [];
            }

            // Process pending conflicts
            if (this.pendingConflicts.length > 0) {
                await this.databaseService.saveConflicts(this.pendingConflicts);
                this.pendingConflicts = [];
            }

            // Process pending metadata
            if (this.pendingMetadata.length > 0) {
                // Process metadata items individually for now
                for (const metadata of this.pendingMetadata) {
                    // Store metadata using executeQuery for flexibility
                    await this.databaseService.executeQuery(
                        'INSERT OR REPLACE INTO metadata (resourceId, key, value) VALUES (?, ?, ?)',
                        [metadata.resourceId, metadata.key, JSON.stringify(metadata.value)]
                    );
                }
                this.pendingMetadata = [];
            }

        } catch (error) {
            logger.error('Error processing pending items:', error);
        }
    }

    /**
     * Cleanup all pending items
     */
    private async cleanupPendingItems(): Promise<void> {
        this.pendingResources = [];
        this.pendingConflicts = [];
        this.pendingMetadata = [];
    }

    /**
     * Get current memory pressure level
     */
    private getMemoryPressureLevel(): MemoryPressureLevel {
        const pressure = this.enhancedMemoryManager.getMemoryPressure();
        
        if (pressure > this.EMERGENCY_MEMORY_THRESHOLD) {
            return MemoryPressureLevel.EMERGENCY;
        } else if (pressure > this.CRITICAL_MEMORY_THRESHOLD) {
            return MemoryPressureLevel.CRITICAL;
        } else if (pressure > this.MEMORY_PRESSURE_THRESHOLD) {
            return MemoryPressureLevel.HIGH;
        } else if (pressure > 0.5) {
            return MemoryPressureLevel.MODERATE;
        } else {
            return MemoryPressureLevel.LOW;
        }
    }

    /**
     * Get current memory usage information
     */
    private getMemoryUsage() {
        const memUsage = process.memoryUsage();
        return {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100
        };
    }

    /**
     * Calculate estimated time remaining
     */
    private calculateEstimatedTimeRemaining(
        completedBatches: number,
        totalBatches: number,
        elapsedTime: number
    ): number {
        if (completedBatches === 0) {
            return 0;
        }

        const averageTimePerBatch = elapsedTime / completedBatches;
        const remainingBatches = totalBatches - completedBatches;
        return remainingBatches * averageTimePerBatch;
    }

    /**
     * Update memory efficiency score
     */
    private updateMemoryEfficiencyScore(memoryPercentage: number): void {
        // Calculate efficiency score (lower memory usage = higher score)
        const efficiency = Math.max(0, 100 - memoryPercentage);
        this.stats.memoryEfficiencyScore = (this.stats.memoryEfficiencyScore + efficiency) / 2;
    }

    /**
     * Get performance statistics
     */
    public getStats() {
        return {
            ...this.stats,
            activeBatches: this.activeBatches.size,
            pendingItems: {
                resources: this.pendingResources.length,
                conflicts: this.pendingConflicts.length,
                metadata: this.pendingMetadata.length
            },
            memoryUsage: this.getMemoryUsage(),
            memoryPressure: this.getMemoryPressureLevel()
        };
    }

    /**
     * Get active batch progress
     */
    public getActiveBatches(): BatchProgress[] {
        return Array.from(this.activeBatches.values());
    }

    /**
     * Reset statistics
     */
    public resetStats(): void {
        this.stats = {
            totalBatches: 0,
            totalItemsProcessed: 0,
            totalMemoryReclaimed: 0,
            forcedGcCount: 0,
            adaptiveBatchAdjustments: 0,
            averageBatchSize: 0,
            averageProcessingTime: 0,
            memoryEfficiencyScore: 0
        };
    }
}

export default MemoryManager;