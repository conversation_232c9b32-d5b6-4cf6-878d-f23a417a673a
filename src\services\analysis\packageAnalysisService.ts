﻿import { Logger } from '../../utils/logging/logger.js';
import { PackageAnalysisResult } from '../../types/analysis/PackageAnalysisResult.js';
import { ResourceKey as AppResourceKey, ResourceMetadata, ResourceInfo } from '../../types/resource/interfaces.js';
import { PackageMetadata } from '../../types/resource/Package.js';
import { ResourcePackageInfo } from '../../types/resource/conflicts.js';
import { ModConflictOrchestrator } from '../ml/ModConflictOrchestrator.js';
import { ConflictInfo } from '../../types/conflict/index.js';
// Import the default export and access Package and Resource
import S4TK from '@s4tk/models';
const { Package, Resource } = S4TK;
console.log('Debug S4TK import:', S4TK); // Add debug log
import { Resource as ResourceType } from '@s4tk/models'; // Fixed Resource type import (removed type keyword)
import { promises as fs } from 'fs'; // Revert node: prefix
import path from 'path'; // Revert node: prefix
import { EventEmitter } from 'events'; // Revert node: prefix
import { createHash } from 'crypto'; // <-- Add crypto import

// Import refactored functions
import { readPackageFileKeys, validatePackageFile, streamResourcePairs } from './s4tkReader.js';
// import { extractMetadataFromResource } from './metadataExtractor.js'; // Removed duplicate extractor
import { ResourceMetadataExtractor } from './package/resourceMetadataExtractor.js'; // Import the correct extractor class
import { calculateFileHash, validateFileMetadata } from '../../utils/file/fileUtils.js';
import { generateResourceId, generateResourceIdFromMeta, getResourceTypeName, createResourceKeyFromS4TKWithName } from '../../utils/resource/helpers.js'; // Added createResourceKeyFromS4TKWithName
import { DatabaseService } from '../databaseService.js'; // Import DatabaseService class
import { OverrideInfo } from '../../types/database.js'; // Import OverrideInfo from types
import { detectAndSaveOverrides } from './overrideDetector.js'; // Import the new override detector
// Breakdown utils are not used internally by the service anymore, removed imports

export class PackageAnalysisService extends EventEmitter {
    private static instance: PackageAnalysisService;
    private logger: Logger;
    private conflictOrchestrator: ModConflictOrchestrator;
    private databaseService: DatabaseService; // Add private property for DatabaseService
    // Removed xmlParser as it's now in metadataExtractor

    // State Management - Resources and Metadata now managed by DatabaseService
    // private resources: Map<string, AppResourceKey> = new Map(); // Removed
    // private metadata: Map<string, ResourceMetadata> = new Map(); // Removed
    private constructor(logger: Logger, databaseService: DatabaseService) { // Accept DatabaseService in constructor
        super();
        this.logger = logger;
        this.databaseService = databaseService; // Assign DatabaseService to private property
        // Initialize orchestrator here
        this.conflictOrchestrator = ModConflictOrchestrator.getInstance(logger);
    }

    public static getInstance(logger: Logger, databaseService: DatabaseService): PackageAnalysisService { // Accept DatabaseService in getInstance
        if (!PackageAnalysisService.instance) {
            PackageAnalysisService.instance = new PackageAnalysisService(logger, databaseService); // Pass DatabaseService to constructor
        }
        return PackageAnalysisService.instance;
    }

    public async initialize(): Promise<void> {
        // Initialize dependencies if needed (e.g., ML orchestrator)
        await this.conflictOrchestrator.initialize();
        this.logger.info("PackageAnalysisService initialized.");
    }

    // Builds metadata by streaming resources and calling the extractor
    // Removed resourceKeys parameter as it's unreliable due to S4TK issue
    // Modified return type to include ResourceInfo[]
    private async buildPackageMetadata(filePath: string): Promise<{ packageInfo: PackageMetadata; resourceInfos: ResourceInfo[] }> {
        // Instantiate ResourceMetadataExtractor here
        const metadataExtractor = new ResourceMetadataExtractor(this.databaseService, this.logger);

        try {
            const stats = await fs.stat(filePath);
            const hash = await calculateFileHash(filePath); // Use imported util

            // Save/Update Package info in DB
            const packageId = this.databaseService.packages.savePackage({
                name: path.basename(filePath),
                path: filePath,
                hash: hash,
                size: stats.size,
                lastModified: stats.mtimeMs
            });
            this.logger.info(`Saved/Updated package ${filePath} with ID: ${packageId}`);

            const resourceMetadataList: ResourceMetadata[] = []; // Still needed for PackageMetadata structure
            const resourceInfos: ResourceInfo[] = []; // Array to hold { key, metadata } pairs
            const processedResources: { resourceId: number; tgiString: string }[] = []; // Initialize array

            // Extract pairs (key + resource model) using the updated reader
            // No need to pass loadResources: true as extractResources does this by default
            const stream = await streamResourcePairs(filePath);

            // Define the target TGI for logging
            const targetTuningType = 0x7F4AD89D; // ZoneModifierTuning
            const targetTuningGroup = 0x80000000;
            const targetTuningInstance = 87952794550814124n; // BigInt

            // Array to hold data needed for async metadata extraction after the transaction
            const resourceDataForMetadata: { appKey: AppResourceKey, pairValue: ResourceType | null, resourceId: number, keyId: string, isTargetResource: boolean }[] = [];

            // Use the public transaction method to save initial resource info
            this.databaseService.executeTransaction(() => {
                for (const pair of stream) {
                    const keyId = `${pair.key.type}_${BigInt(pair.key.group).toString()}_${pair.key.instance.toString()}`;
                    this.logger.debug(`[PackageAnalysisService] Processing resource (in transaction): ${keyId}`);
                    const appKey = createResourceKeyFromS4TKWithName(pair.key, `Resource_${pair.key.instance}`, filePath);

                    const isTargetResource = appKey.type === targetTuningType &&
                                             appKey.group === BigInt(targetTuningGroup) &&
                                             appKey.instance === targetTuningInstance;

                    if (isTargetResource) {
                        this.logger.debug(`[PackageAnalysisService] Found target resource for verification (in transaction): ${keyId}.`);
                    }

                    let resourceId: number | null = null;
                    try {
                        // Determine hash/size synchronously if possible (getBuffer might be sync)
                        // Note: getBuffer() might still be async depending on S4TK implementation details.
                        // If it IS async, this transaction approach won't work as intended.
                        // Assuming for now it can be treated as sync within the transaction context.
                        const initialBuffer = pair.value?.getBuffer ? pair.value.getBuffer() : null;
                        const initialSize = initialBuffer?.length || 0;
                        const initialHash = initialBuffer ? createHash('sha1').update(initialBuffer).digest('hex') : 'N/A';

                        // Save basic resource info synchronously within the transaction
                        resourceId = this.databaseService.resources.saveResource({
                            packageId: packageId,
                            type: appKey.type,
                            group: appKey.group, // Pass bigint directly
                            instance: appKey.instance,
                            hash: initialHash,
                            size: initialSize,
                            offset: 0, // Placeholder
                            contentSnippet: undefined,
                            resourceType: getResourceTypeName(appKey.type) || 'Unknown'
                        });
                        this.logger.debug(`Saved/Updated initial resource ${keyId} with ID: ${resourceId} (in transaction)`);

                        if (isTargetResource) {
                            this.logger.debug(`[PackageAnalysisService] Target resource ${keyId} saved with ID: ${resourceId} (in transaction)`);
                        }

                        if (resourceId !== null) {
                            processedResources.push({ resourceId: resourceId, tgiString: `${appKey.type}:${Number(appKey.group)}:${appKey.instance}` });
                            // Store data needed for async metadata extraction later
                            resourceDataForMetadata.push({ appKey, pairValue: pair.value as ResourceType | null, resourceId, keyId, isTargetResource });
                        } else {
                            this.logger.error(`Failed to get resourceId for ${keyId} after saveResource call (in transaction).`);
                        }

                    } catch (dbError: any) {
                        this.logger.error(`Failed to save initial resource ${keyId} to DB (in transaction): ${dbError.message || dbError}`);
                        if (isTargetResource) {
                            this.logger.error(`[PackageAnalysisService] Failed to save target resource ${keyId} to DB (in transaction).`);
                        }
                        // Continue to next resource within the transaction on error
                    }
                } // End of for loop inside transaction
            }); // End of executeTransaction call

            // --- Step 2: Extract detailed metadata (Asynchronously, AFTER transaction) ---
            this.logger.info(`Starting asynchronous metadata extraction for ${resourceDataForMetadata.length} resources...`);
            // Use Promise.all to run metadata extraction concurrently for better performance
            const metadataExtractionResults = await Promise.all(resourceDataForMetadata.map(async ({ appKey, pairValue, resourceId, keyId, isTargetResource }) => {
                 let metadata: ResourceMetadata | null = null;
                 try {
                     // Pass the already obtained resourceId
                     if (pairValue && typeof pairValue === 'object') {
                         // This part remains async as extractMetadataFromResource involves potential async operations
                         metadata = await metadataExtractor.extractMetadata(appKey, pairValue, resourceId); // Use the instantiated class
                     } else {
                         this.logger.warn(`Resource value missing or invalid for ${keyId} during async metadata. Creating minimal metadata.`);
                         metadata = { name: appKey.name || keyId, path: filePath, size: 0, hash: '', timestamp: stats.mtimeMs, source: path.basename(filePath), contentSnippet: '[Resource Content Not Loaded]', instance: appKey.instance };
                     }
                 } catch (extractError: any) {
                     this.logger.warn(`Error during async metadata extraction for resource ${keyId}: ${extractError.message || extractError}. Creating minimal metadata.`);
                     metadata = { name: appKey.name || keyId, path: filePath, size: 0, hash: '', timestamp: stats.mtimeMs, source: path.basename(filePath), instance: appKey.instance };
                 }
                 // Return the result for collection
                 return { appKey, metadata };
            }));

            // Populate lists after all promises resolve
            metadataExtractionResults.forEach(result => {
                if (result && result.metadata) {
                     resourceMetadataList.push(result.metadata);
                     resourceInfos.push({ key: result.appKey, metadata: result.metadata });
                }
            });
            this.logger.info(`Finished asynchronous metadata extraction.`);


            // Construct PackageMetadata part of the result
            const packageInfo: PackageMetadata = {
                name: path.basename(filePath), path: filePath, size: stats.size,
                timestamp: stats.mtimeMs, hash: hash, version: 'N/A', author: 'N/A',
                description: 'N/A', resources: resourceMetadataList, conflicts: [], // Use metadata list here
                processedResources: processedResources
            };
            // Return both package info and the detailed resource info list
            return { packageInfo, resourceInfos };
        } catch (error: any) {
            this.logger.error(`Error building package metadata for ${filePath}: ${error.message || error}`);
            throw new Error(`Failed to build metadata for ${filePath}`);
        }
    }

    // Public method to analyze a single package
    public async analyzePackage(filePath: string): Promise<PackageAnalysisResult> {
        // No need to clear global state if conflicts are not stored globally
        const startTime = Date.now();
        let packageMetadata: PackageMetadata | null = null;
        try {
            await validateFileMetadata(filePath);
            if (path.extname(filePath).toLowerCase() === '.package') {
                 const isValid = await validatePackageFile(filePath);
                 if (!isValid) throw new Error('Invalid package file format according to S4TK validation.');
            }

            const { packageInfo, resourceInfos } = await this.buildPackageMetadata(filePath);
            packageMetadata = packageInfo;

            // REMOVED: Global maps population removed earlier
            // REMOVED: Data is now saved directly to DB in buildPackageMetadata
            // resourceKeys.forEach(key => this.resources.set(generateResourceId(key), key));
            // packageMetadata.resources.forEach(meta => {
            //      // Convert rk.group (bigint) to number for generateResourceIdFromMeta
            //      const key = resourceKeys.find(rk => generateResourceId(rk) === generateResourceIdFromMeta(meta, rk.type, Number(rk.group)));
            //      if (key) {
            //          this.metadata.set(generateResourceId(key), meta);
            //      }
            // });

            // Conflicts for a single package analysis are typically internal resource conflicts,
            // which would be detected during metadata extraction or semantic analysis.
            // The ModConflictOrchestrator is primarily for cross-package conflicts.
            // For now, we'll leave the conflicts array empty for single package analysis,
            // assuming internal conflicts are handled elsewhere or not the focus here.
            const conflicts: ConflictInfo[] = []; // Conflicts for single package analysis

            const analysisTime = Date.now() - startTime;
            const result = this.constructAnalysisResult(filePath, packageMetadata, resourceInfos, conflicts); // Pass conflicts
            result.analysisTime = analysisTime;
            this.logger.info(`Analyzed ${filePath} in ${analysisTime}ms. Found ${result.resourceCount} resources.`);
            return result;
        } catch (error: any) {
            this.logger.error(`Error analyzing package ${filePath}: ${error.message || error}`);
            // No need to clear global state
            return {
                 metadata: this.getPackageMetadata(filePath),
                 resources: [], conflicts: [], timestamp: Date.now(),
                 isValid: false, errors: [String(error.message || error)], warnings: [],
                 analysisTime: Date.now() - startTime, resourceCount: 0, totalSize: 0
            };
        }
    }

     // Public method to analyze multiple packages
     public async analyzeMultiplePackages(packagePaths: string[]): Promise<PackageAnalysisResult[]> {
         // No need to clear global state
         this.logger.info(`Analyzing ${packagePaths.length} packages for conflicts...`);
         const results: PackageAnalysisResult[] = [];
         const allPackageInfos: ResourcePackageInfo[] = [];
         const analysisStartTime = Date.now();

         for (const filePath of packagePaths) {
             const perPackageStartTime = Date.now();
             let currentPackageMetadata: PackageMetadata | null = null;
             // Removed resourceKeys pre-fetching
             let resourceInfos: ResourceInfo[] = [];
             let errors: string[] = [];

             try {
                 await validateFileMetadata(filePath); // Use imported util
                 // Removed explicit package file validation call

                 // Build metadata for this package - no longer passes resourceKeys
                 // Destructure result from buildPackageMetadata
                 const { packageInfo, resourceInfos: builtResourceInfos } = await this.buildPackageMetadata(filePath);
                 currentPackageMetadata = packageInfo; // Assign packageInfo
                 resourceInfos = builtResourceInfos; // Assign the directly returned resourceInfos

                 // Removed the flawed reconstruction logic

                 if (currentPackageMetadata) {
                     // Use the resourceInfos directly as it contains { key, metadata }
                     allPackageInfos.push({
                          name: currentPackageMetadata.name, filePath: currentPackageMetadata.path,
                          resources: resourceInfos, // Use the resourceInfos obtained from buildPackageMetadata
                          size: currentPackageMetadata.size,
                          hash: currentPackageMetadata.hash, timestamp: currentPackageMetadata.timestamp
                     });
                 }

             } catch (error: any) {
                 // Log the raw error object for detailed inspection
                 console.error(`[Service CATCH RAW ERROR] File: ${filePath}`, error);
                 this.logger.error(`Skipping package ${filePath} due to analysis error: ${error.message || error}`);
                 // isValidPackage = false; // Removed validation flag
                 errors.push(String(error.message || error));
                 currentPackageMetadata = this.getPackageMetadata(filePath); // Basic metadata on error
             }

             // Store result for this package (conflicts added later)
             results.push({
                 metadata: currentPackageMetadata || this.getPackageMetadata(filePath),
                 resources: resourceInfos,
                 conflicts: [], // Conflicts will be populated after orchestrator runs
                 timestamp: Date.now(), isValid: errors.length === 0,
                 errors: errors, warnings: [],
                 analysisTime: Date.now() - perPackageStartTime,
                 resourceCount: resourceInfos.length,
                 totalSize: currentPackageMetadata?.size || 0
             });
         }

          // Detect conflicts using the orchestrator across ALL packages
          let crossPackageConflicts: ConflictInfo[] = [];
          if (allPackageInfos.length > 0) { // Check if there are packages to analyze
              this.logger.info(`Detecting conflicts between ${allPackageInfos.length} packages...`);
              try {
                   // Reverting to passing the first two packages based on the orchestrator's expected signature
                   crossPackageConflicts = await this.conflictOrchestrator.analyzeConflictsBetweenPackages(allPackageInfos[0], allPackageInfos[1]);
                   // Conflicts are now stored in the database by the orchestrator or will be retrieved from DB
                   // We don't need to store them in a class property anymore.
                   this.logger.info(`Found ${crossPackageConflicts.length} potential conflicts between the first two packages.`);

                   // Conflicts should ideally be retrieved from the DB per package result
                   // For now, we'll distribute the conflicts returned by the orchestrator
                   // This might need adjustment if the orchestrator saves conflicts directly to DB
                   results.forEach(result => {
                       if (result.isValid) {
                           // Filter conflicts relevant to this package from the combined list
                           result.conflicts = crossPackageConflicts.filter(c =>
                               c.affectedResources.some((resKey: AppResourceKey) => resKey.path === result.metadata.path)
                           );
                       }
                   });

              } catch (conflictError: any) {
                   this.logger.error(`Error during cross-package conflict detection: ${conflictError.message || conflictError}`);
              }
         }

         // --- Deferred Override Detection ---
         // Moved to a separate function for better organization
         await detectAndSaveOverrides(this.databaseService, results);
         // --- End Deferred Override Detection ---


          const totalAnalysisTime = Date.now() - analysisStartTime;
          this.logger.info(`Multi-package analysis complete in ${totalAnalysisTime}ms.`);
          return results;
      }


    // --- Other Public Methods ---

    public async getModsDirectory(): Promise<string> {
        // Placeholder - Implement robust detection later
        const placeholderPath = path.join(process.env.USERPROFILE || process.env.HOME || '.', 'Documents', 'Electronic Arts', 'The Sims 4', 'Mods');
        this.logger.warn(`getModsDirectory returning placeholder path: ${placeholderPath}. This may be incorrect.`);
        try {
            await fs.access(placeholderPath);
            return placeholderPath;
        } catch {
            this.logger.warn(`Placeholder Mods directory not found. Returning default relative path './Mods'.`);
            return './Mods';
        }
    }

    // Removed deprecated detectConflicts method

    // --- State Management ---

    // REMOVED: addResource - Data managed by DatabaseService
    // public addResource(key: AppResourceKey, metadata: ResourceMetadata): void {
    //     const id = generateResourceId(key); // Use imported util
    //     if (!this.resources.has(id)) {
    //         this.resources.set(id, key);
    //     }
    //     this.metadata.set(id, metadata);
    // }

    // Removed addConflict - Conflicts are handled by the orchestrator and/or database

    // REMOVED: getResourceMetadata - Data should be fetched from DatabaseService when needed
    // public getResourceMetadata(key: AppResourceKey): ResourceMetadata | undefined {
    //     const id = generateResourceId(key); // Use imported util
    //     return this.metadata.get(id);
    // }

    private getPackageMetadata(filePath?: string): PackageMetadata {
        const name = filePath ? path.basename(filePath) : 'aggregate';
        return {
            name: name, path: filePath || '', size: 0, timestamp: 0, hash: '',
            version: 'N/A', author: 'N/A', description: 'N/A',
            resources: [], conflicts: [],
        };
    }

    // Constructs the final result object using internal state
    // Accept conflicts as a parameter
    private constructAnalysisResult(filePath: string, packageMetadata: PackageMetadata, resourceInfos: ResourceInfo[], conflicts: ConflictInfo[]): PackageAnalysisResult {
        let calculatedSize = 0;
        resourceInfos.forEach(info => {
            calculatedSize += info.metadata.size;
        });

        packageMetadata.size = calculatedSize;
        // Assign the provided conflicts
        packageMetadata.conflicts = conflicts;

        return {
            metadata: packageMetadata,
            conflicts: packageMetadata.conflicts,
            resources: resourceInfos,
            timestamp: Date.now(),
            isValid: true,
            errors: [], warnings: [],
            analysisTime: 0,
            resourceCount: resourceInfos.length,
            totalSize: packageMetadata.size
        };
    }


    public clear(): void {
        // Only clear conflicts if they were stored globally (which they are not anymore)
        // this.conflicts = []; // Removed
        this.logger.info('PackageAnalysisService state cleared (no global state to clear).');
    }

    // Removed generateResourceId and generateResourceIdFromMeta (moved to helpers)
    // Removed breakdown methods (moved to analysisUtils)
}
