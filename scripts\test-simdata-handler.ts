import { Logger } from '../src/utils/logging/logger.js';
import { DatabaseService } from '../src/services/databaseService.js';
import { PackageReader } from '../src/services/packageReader.js';
import { SimDataParser } from '../src/services/analysis/extractors/simdata/simDataParser.js';
import { SimDataVersionHandler } from '../src/services/analysis/extractors/simdata/simDataVersionHandler.js';
import path from 'path';
import fs from 'fs';

const log = new Logger('TestSimDataHandler');

/**
 * Test script for SimData version handler
 * This script tests the enhanced SimData version handler with a specific file
 */
async function main() {
    try {
        // Get package file path from command line arguments or use default
        const packagePath = process.argv[2] || "C:\\Users\\<USER>\\OneDrive\\Documents\\Electronic Arts\\The Sims 4\\Mods\\mc_cmd_center.package";

        log.info(`Testing SimData version handler with: ${packagePath}`);

        // Initialize services
        const dbLogger = new Logger('DatabaseService');
        const db = new DatabaseService(dbLogger);
        await db.initialize();
        const packageReader = new PackageReader();
        const simDataParser = new SimDataParser();

        // First get the package ID
        let packageId = -1;
        try {
            packageId = db.packages.getPackageIdByPath(packagePath);
            if (packageId === -1) {
                log.info('Package not found in database. Adding it...');

                // Save package info to database
                packageId = db.packages.savePackage({
                    name: path.basename(packagePath),
                    path: packagePath,
                    hash: 'test-hash',
                    size: fs.statSync(packagePath).size,
                    lastModified: Date.now()
                });

                log.info(`Added package to database with ID: ${packageId}`);
            }
        } catch (dbError) {
            log.error(`Database error: ${dbError}`);
            // Continue with packageId = -1
        }

        // Get all SimData resources for this package (type 0x545AC67A)
        const simDataType = 0x545AC67A;

        // First, scan the package to find SimData resources
        log.info('Scanning package for SimData resources...');

        let buffer: Buffer | undefined;

        try {
            // Use the packageReader to get a list of resources in the package
            const resources = await packageReader.getResourcesInPackage(packagePath);

            // Filter for SimData resources
            const simDataResources = resources.filter(res => res.type === simDataType);

            log.info(`Found ${simDataResources.length} SimData resources in package`);

            if (simDataResources.length === 0) {
                log.warn('No SimData resources found in this package. Using default test resource.');

                // Use a default test resource from MC Command Center
                const testResourceKey = {
                    type: simDataType,
                    group: 0x9bc58e,
                    instance: BigInt('0xceb1d104b317ebdf')
                };

                log.info(`Extracting default test resource: ${testResourceKey.type.toString(16)}:${testResourceKey.group.toString(16)}:${testResourceKey.instance.toString(16)}`);

                buffer = await packageReader.extractResource("C:\\Users\\<USER>\\OneDrive\\Documents\\Electronic Arts\\The Sims 4\\Mods\\mc_cmd_center.package", testResourceKey);
            } else {
                // Extract the first SimData resource
                const firstResource = simDataResources[0];
                const testResourceKey = {
                    type: firstResource.key.type,
                    group: firstResource.key.group,
                    instance: firstResource.key.instance
                };

                log.info(`Extracting found resource: ${testResourceKey.type.toString(16)}:${testResourceKey.group.toString(16)}:${testResourceKey.instance.toString(16)}`);

                buffer = await packageReader.extractResource(packagePath, testResourceKey);
            }
        } catch (scanError) {
            log.error(`Error scanning package: ${scanError}`);

            // Fallback to a default test resource from MC Command Center
            log.info('Falling back to default test resource from MC Command Center');

            const testResourceKey = {
                type: simDataType,
                group: 0x9bc58e,
                instance: BigInt('0xceb1d104b317ebdf')
            };

            log.info(`Extracting default test resource: ${testResourceKey.type.toString(16)}:${testResourceKey.group.toString(16)}:${testResourceKey.instance.toString(16)}`);

            buffer = await packageReader.extractResource("C:\\Users\\<USER>\\OneDrive\\Documents\\Electronic Arts\\The Sims 4\\Mods\\mc_cmd_center.package", testResourceKey);
        }

        if (!buffer) {
            log.error('Failed to extract resource buffer');
            process.exit(1);
        }

        log.info(`Successfully extracted buffer of size: ${buffer.length} bytes`);

        // Detect version
        const version = SimDataVersionHandler.detectVersion(buffer);
        if (version === undefined) {
            log.error('Could not detect SimData version');
            process.exit(1);
        }

        log.info(`Detected SimData version: ${version}`);

        // Try to parse the SimData
        const simData = simDataParser.parse(buffer);
        if (simData) {
            log.info(`Successfully parsed SimData version ${version}`);
            log.info(`Schema name: ${simData.schema?.name || 'Unknown'}`);
            log.info(`Column count: ${simData.schema?.columns?.length || 0}`);
            log.info(`Instance count: ${simData.instances?.length || 0}`);

            // Log some column names if available
            if (simData.schema?.columns && simData.schema.columns.length > 0) {
                log.info('Column names:');
                simData.schema.columns.slice(0, 5).forEach(col => {
                    log.info(`- ${col.name} (type: ${col.type})`);
                });

                if (simData.schema.columns.length > 5) {
                    log.info(`... and ${simData.schema.columns.length - 5} more columns`);
                }
            }

            // Log some instance data if available
            if (simData.instances && simData.instances.length > 0) {
                log.info('Instance data:');
                simData.instances.slice(0, 3).forEach((instance, idx) => {
                    log.info(`Instance ${idx + 1}: ${instance.name} (ID: ${instance.instanceId})`);

                    // Log a few values
                    const values = instance.values;
                    if (values) {
                        const keys = Object.keys(values).slice(0, 3);
                        keys.forEach(key => {
                            log.info(`  ${key}: ${JSON.stringify(values[key])}`);
                        });

                        if (Object.keys(values).length > 3) {
                            log.info(`  ... and ${Object.keys(values).length - 3} more values`);
                        }
                    }
                });

                if (simData.instances.length > 3) {
                    log.info(`... and ${simData.instances.length - 3} more instances`);
                }
            }
        } else {
            log.warn(`Failed to parse SimData version ${version}`);
        }

        await db.close();
        log.info('Test completed successfully');
    } catch (error) {
        log.error(`Error: ${error}`);
    }
}

main().catch(error => {
    log.error(`Unhandled error: ${error}`);
    process.exit(1);
});
