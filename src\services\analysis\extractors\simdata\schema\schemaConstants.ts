/**
 * Constants for SimData schema analysis
 */

/**
 * Known schema inheritance patterns in Sims 4
 */
export const KNOWN_SCHEMA_PARENTS = new Set([
    'Object', 'Sim', 'Trait', 'Buff', 'Interaction', 'Recipe', 'Career',
    'Aspiration', 'Lot', 'Venue', 'Situation', 'Relationship', 'Skill',
    'Commodity', 'Statistic', 'Mood', 'Reward', 'Tutorial', 'Objective',
    'Animation', 'Posture', 'Walkstyle', 'Outfit', 'CAS', 'Household'
]);

/**
 * Schema categories for better organization
 */
export const SCHEMA_CATEGORIES: Record<string, string[]> = {
    'Sim': ['Sim', 'SimData', 'SimInfo', 'SimPersonality', 'SimTrait'],
    'Object': ['Object', 'ObjectData', 'ObjectState', 'ObjectComponent'],
    'Gameplay': ['Trait', 'Buff', 'Interaction', 'Recipe', 'Career', 'Aspiration', 'Skill'],
    'World': ['Lot', 'Venue', 'Terrain', 'World', 'Region', 'Neighborhood'],
    'Social': ['Relationship', 'Sentiment', 'RelationshipBit', 'RelationshipTrack'],
    'Animation': ['Animation', 'Posture', 'Walkstyle', 'Reaction', 'Facial'],
    'UI': ['UI', 'HUD', 'CAS', 'Interface', 'Menu', 'Dialog'],
    'System': ['Manager', 'Service', 'System', 'Handler', 'Controller']
};

/**
 * Critical column patterns that affect gameplay
 */
export const CRITICAL_COLUMN_PATTERNS = [
    'multiplier', 'chance', 'probability', 'duration', 'cost', 'value',
    'weight', 'priority', 'threshold', 'score', 'buff', 'motive',
    'skill', 'trait', 'stat', 'tuning', 'level', 'gain', 'decay'
];

/**
 * Common column patterns for categorization
 */
export const COLUMN_PATTERNS: Record<string, string[]> = {
    'key': ['id', 'key', 'guid', 'instance', 'reference', 'ref'],
    'text': ['name', 'description', 'text', 'title', 'label'],
    'gameplay': CRITICAL_COLUMN_PATTERNS,
    'visual': ['color', 'position', 'scale', 'rotation', 'offset', 'visual'],
    'state': ['state', 'status', 'condition', 'mode', 'flag'],
    'time': ['time', 'duration', 'delay', 'interval', 'timeout']
};

/**
 * Known gameplay systems in Sims 4
 */
export const GAMEPLAY_SYSTEMS = [
    'Needs', 'Skills', 'Careers', 'Relationships', 'Traits', 'Aspirations',
    'Emotions', 'Interactions', 'Objects', 'Build', 'CAS', 'Worlds',
    'Clubs', 'Fame', 'Vampires', 'Magic', 'University', 'Eco', 'Seasons'
];

/**
 * SimData type names
 */
export const DATA_TYPE_NAMES: Record<number, string> = {
    1: 'Boolean',
    2: 'Char',
    3: 'Int8',
    4: 'UInt8',
    5: 'Int16',
    6: 'UInt16',
    7: 'Int32',
    8: 'UInt32',
    9: 'Int64',
    10: 'UInt64',
    11: 'Float',
    12: 'String',
    13: 'HashedString',
    14: 'Object',
    15: 'Vector',
    16: 'Float2',
    17: 'Float3',
    18: 'Float4',
    19: 'TableSetReference',
    20: 'ResourceKey',
    21: 'LocalizationKey',
    22: 'VariantList'
};
