﻿﻿// Corrected import
import { <PERSON>Key } from '../../../types/resource/interfaces.js';
// Import central conflict types
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/index.js';
// Import consolidated ML types
import { LLMConfig, LLMResponse, LLMService } from '../types.js';

export interface AgentAnalysisResult {
  conflicts: ConflictInfo[]; // Use imported ConflictInfo
  metrics: {
    totalConflicts: number;
    severityDistribution: Record<ConflictSeverity, number>; // Use imported ConflictSeverity
    affectedResourceCount: number;
  };
  recommendations: string[];
}
