﻿﻿// Corrected imports
import { BinaryResourceType, BinaryResourceTypeValue } from '../../types/resource/core.js'; // Import both type and value
import { ResourceKey } from '../../types/resource/interfaces.js';
import { convertS4TKResourceKey, createResourceKey } from '../resource/helpers.js'; // Corrected relative path
import { promises as fs } from 'fs';
import { createHash } from 'crypto'; // Import createHash
import { Package } from '@s4tk/models';

import { ModCategory, OrganizedPackages, PackageMetadata } from '../../types/resource/Package.js';
import { metadataCache } from '../../lib/package/metadataCache.js';
import { ConflictType, ConflictSeverity, ConflictInfo } from '../../types/conflict/index.js';


// --- Categorization Logic (Revised) ---

function mapResourceTypeToCategory(type: BinaryResourceType): ModCategory {
  switch (type) {
    // Use the correct S4TK property names
    case BinaryResourceTypeValue.CombinedTuning: case BinaryResourceTypeValue.SimData:
    case BinaryResourceTypeValue.StringTable: case BinaryResourceTypeValue.ObjectCatalog: case BinaryResourceTypeValue.RegionDescription: return ModCategory.TUNING;
    case BinaryResourceTypeValue.CasPart: case BinaryResourceTypeValue.CasPartThumbnail: case BinaryResourceTypeValue.CasPreset:
    case BinaryResourceTypeValue.ObjectDefinition: case BinaryResourceTypeValue.Footprint:
    case BinaryResourceTypeValue.Model: case BinaryResourceTypeValue.ModelLod: case BinaryResourceTypeValue.Slot:
    case BinaryResourceTypeValue.AnimationStateMachine: return ModCategory.ASSET;
    case BinaryResourceTypeValue.DdsImage: case BinaryResourceTypeValue.PngImage: case BinaryResourceTypeValue.Rle2Image:
    case BinaryResourceTypeValue.RlesImage: case BinaryResourceTypeValue.OpenTypeFont: case BinaryResourceTypeValue.TrueTypeFont:
     return ModCategory.DATA;
    default: return ModCategory.OTHER;
  }
}

export async function categorizePackage(filePath: string): Promise<ModCategory> {
  if (!filePath || typeof filePath !== 'string' || filePath.trim() === '') return ModCategory.OTHER;
  try {
    await fs.access(filePath);
    const resources: ResourceKey[] = await metadataCache.getResources(filePath);
    if (!resources || resources.length === 0) return ModCategory.OTHER;
    const categoryCounts: Record<ModCategory, number> = {
      [ModCategory.SCRIPT]: 0, [ModCategory.TUNING]: 0, [ModCategory.ASSET]: 0, [ModCategory.DATA]: 0, [ModCategory.OTHER]: 0,
    };
    resources.forEach(key => {
      if (key?.type !== undefined) categoryCounts[mapResourceTypeToCategory(key.type as BinaryResourceType)]++;
      else categoryCounts[ModCategory.OTHER]++;
    });
    let dominantCategory = ModCategory.OTHER; let maxCount = 0;
    for (const category in categoryCounts) {
      if (categoryCounts[category as ModCategory] > maxCount) {
        maxCount = categoryCounts[category as ModCategory]; dominantCategory = category as ModCategory;
      }
    }
    if (categoryCounts[ModCategory.SCRIPT] > 0) return ModCategory.SCRIPT;
    return dominantCategory;
  } catch (error) { return ModCategory.OTHER; }
}

export async function organizePackages(filePaths: string[]): Promise<OrganizedPackages> {
    const result: OrganizedPackages = {
        [ModCategory.SCRIPT]: [], [ModCategory.TUNING]: [], [ModCategory.ASSET]: [], [ModCategory.DATA]: [], [ModCategory.OTHER]: [],
    };
    if (!filePaths?.length) return result;
    for (const filePath of filePaths) {
        let category = ModCategory.OTHER; let packageMeta: PackageMetadata | null = null;
        try {
            if (!filePath?.trim()) continue;
            await fs.access(filePath);
            const resourceKeys = await metadataCache.getResources(filePath);
            category = getPackageCategoryFromKeys(resourceKeys);
            const stats = await fs.stat(filePath); const fileBuffer = await fs.readFile(filePath);
            packageMeta = {
                name: filePath.split(/[\\/]/).pop() || filePath, path: filePath, size: stats.size,
                hash: createHash('sha256').update(fileBuffer).digest('hex'), timestamp: stats.mtimeMs, resources: [],
            };
        } catch (error) {
            category = ModCategory.OTHER; packageMeta = { name: filePath?.split(/[\\/]/).pop() || 'invalid-path', path: filePath || 'invalid-path', size: 0, hash: '', timestamp: Date.now(), resources: [] };
        }
        if (packageMeta) { if (!result[category]) result[category] = []; result[category]?.push(packageMeta); }
    }
    Object.keys(result).forEach(key => { if (!result[key as ModCategory]?.length) delete result[key as ModCategory]; });
    return result;
}

export function addPackageMetadataToCategory(result: OrganizedPackages, category: ModCategory, packageMeta: PackageMetadata): void {
  if (!result || !packageMeta) return;
  const validCategory = Object.values(ModCategory).includes(category) ? category : ModCategory.OTHER;
  if (!result[validCategory]) result[validCategory] = [];
  result[validCategory]?.push(packageMeta);
}

export function getSuggestedFolderName(category: ModCategory): string {
  switch (category) {
    case ModCategory.SCRIPT: return 'Script Mods'; case ModCategory.TUNING: return 'Tuning and Data Mods';
    case ModCategory.ASSET: return 'Asset Mods (CAS, BuildBuy, etc)'; case ModCategory.DATA: return 'Data Mods';
    case ModCategory.OTHER: return 'Other Mods'; default: const exhaustiveCheck: never = category; return 'Uncategorized Mods';
  }
}

export function isValidPackageFile(filePath: string): boolean {
  if (!filePath?.trim()) return false;
  const lcPath = filePath.toLowerCase(); return lcPath.endsWith('.package') || lcPath.endsWith('.ts4script');
}

// --- Resource Key Utilities ---
export function getResourceTypeCategoryString(key: ResourceKey): string {
  return (key?.type !== undefined) ? mapResourceTypeToCategory(key.type as BinaryResourceType).toString().toLowerCase() : 'unknown';
}

export function compareResourceKeys(key1: ResourceKey, key2: ResourceKey): boolean {
    return !!key1 && !!key2 && key1.type === key2.type && key1.group === key2.group && key1.instance === key2.instance;
}

export function isCASResource(key: ResourceKey): boolean {
    if (!key?.type) return false; const type = key.type as BinaryResourceType;
    return type === BinaryResourceTypeValue.CasPart || type === BinaryResourceTypeValue.CasPartThumbnail || type === BinaryResourceTypeValue.CasPreset; // Use value and correct member names
}

export function getResourceType(key: ResourceKey): BinaryResourceType { return key?.type ?? 0; }
function isBinaryResourceType(type: number): boolean {
  return typeof type === 'number' && type in BinaryResourceTypeValue;
}

export function getResourceCategoryFromName(resourceType: number): string {
  if (!isBinaryResourceType(resourceType)) return 'Other';
  const name = BinaryResourceTypeValue[resourceType] || 'UNKNOWN';
  if (typeof name !== 'string') return 'Other';
  if (name.includes('Cas')) return 'CAS';
  if (name.includes('Footprint') || name.includes('Object')) return 'Build/Buy';
  if (name.includes('Model') || name.includes('Rig')) return 'Geometry';
  if (name.includes('Image')) return 'Images';
  if (name.includes('SimData') || name.includes('StringTable')) return 'Tuning/Data';
  return 'Other';
}

// --- Mod Organization and Conflict Detection ---
export interface ModOrganizationResult {
  conflicts: ConflictInfo[]; // Use local ConflictInfo
  recommendations: string[];
  metadata: PackageMetadata;
}

export class ModOrganizer {
  private static readonly AUDIO_TYPES: BinaryResourceType[] = [ /* No audio types in S4TK enum */ ];
  private static readonly SCRIPT_TYPES: BinaryResourceType[] = [ /* No script types in S4TK enum */ ];
  private static readonly TUNING_TYPES: BinaryResourceType[] = [ BinaryResourceTypeValue.CombinedTuning, BinaryResourceTypeValue.SimData ];

  public static analyzeInternalConflicts(packageMeta: PackageMetadata): ModOrganizationResult {
    const conflicts: ConflictInfo[] = []; const recommendations: string[] = [];
    // @ts-ignore - getCachedResourcesSync needs adding to MetadataCache class definition
    const resourceKeys = metadataCache.getCachedResourcesSync(packageMeta.path);
    if (resourceKeys?.length) {
        // Use proper ConflictType and ConflictSeverity enums
        conflicts.push(...this.checkConflictsForTypes(resourceKeys, this.AUDIO_TYPES, ConflictType.SOUND, ConflictSeverity.MEDIUM, 'Multiple audio resources detected'));
        conflicts.push(...this.checkConflictsForTypes(resourceKeys, this.SCRIPT_TYPES, ConflictType.SCRIPT, ConflictSeverity.HIGH, 'Multiple script resources detected'));
        conflicts.push(...this.checkConflictsForTypes(resourceKeys, this.TUNING_TYPES, ConflictType.TUNING, ConflictSeverity.MEDIUM, 'Multiple tuning resources detected'));
    } else { recommendations.push(`Could not retrieve resource keys for ${packageMeta.path} to check internal conflicts.`); }
    if (conflicts.length > 0) recommendations.push(...this.generateRecommendations(conflicts));
    return { conflicts, recommendations, metadata: packageMeta };
  }

  // Use proper ConflictType and ConflictSeverity types
  private static checkConflictsForTypes(resourceKeys: ResourceKey[], typesToCheck: BinaryResourceType[], conflictType: ConflictType, severity: ConflictSeverity, description: string): ConflictInfo[] {
      const relevantResources = resourceKeys.filter(key => key?.type !== undefined && typesToCheck.includes(key.type as BinaryResourceType));
      // Pass the types through to createConflict
      return (relevantResources.length > 1) ? [createConflict(conflictType, severity, `${description} (${relevantResources.length} found)`, relevantResources)] : [];
  }

  // Accepts ConflictInfo[]
  private static generateRecommendations(conflicts: ConflictInfo[]): string[] {
    // Access resolution.description correctly
    return conflicts.map(c => c.resolution?.description || `Review conflict: ${c.description}`).filter((rec, i, self) => rec && self.indexOf(rec) === i);
  }
}

export async function extractResourceKeys(packagePath: string): Promise<ResourceKey[]> {
  try { const streamedEntries = await Package.streamResourcesAsync(packagePath); const keys: ResourceKey[] = [];
    for await (const entry of streamedEntries) { keys.push(convertS4TKResourceKey(entry.key)); } return keys;
  } catch (error) { console.error(`Error extracting resource keys from ${packagePath}:`, error); throw error; }
}

export function mapAffectedResources(resources: any[]): ResourceKey[] {
  // Convert group and instance to BigInt before passing to createResourceKey
  return (resources || []).map(r => createResourceKey(Number(r?.type || 0), BigInt(r?.group || 0), BigInt(r?.instance || 0), String(r?.name || ''), String(r?.path || ''))).filter(k => k) as ResourceKey[];
}

export function getPackageCategoryFromKeys(resourceKeys: ResourceKey[]): ModCategory {
    if (!resourceKeys?.length) return ModCategory.OTHER;
    const counts: Record<ModCategory, number> = { [ModCategory.SCRIPT]: 0, [ModCategory.TUNING]: 0, [ModCategory.ASSET]: 0, [ModCategory.DATA]: 0, [ModCategory.OTHER]: 0 };
    resourceKeys.forEach(k => { counts[k?.type !== undefined ? mapResourceTypeToCategory(k.type as BinaryResourceType) : ModCategory.OTHER]++; });
    let domCat = ModCategory.OTHER; let max = 0;
    for (const cat in counts) { if (counts[cat as ModCategory] > max) { max = counts[cat as ModCategory]; domCat = cat as ModCategory; } }
    return (counts[ModCategory.SCRIPT] > 0) ? ModCategory.SCRIPT : domCat;
}

export function categorizePackages(packages: PackageMetadata[]): Record<ModCategory, PackageMetadata[]> {
  const result: Record<ModCategory, PackageMetadata[]> = { [ModCategory.SCRIPT]: [], [ModCategory.TUNING]: [], [ModCategory.ASSET]: [], [ModCategory.DATA]: [], [ModCategory.OTHER]: [] };
  for (const pkg of packages) { let cat = ModCategory.OTHER;
    if (pkg.name.toLowerCase().includes('script')) cat = ModCategory.SCRIPT;
    else if (pkg.name.toLowerCase().includes('tuning')) cat = ModCategory.TUNING;
    else if (pkg.name.toLowerCase().includes('cas') || pkg.name.toLowerCase().includes('hair') || pkg.name.toLowerCase().includes('object')) cat = ModCategory.ASSET;
    if (!result[cat]) result[cat] = []; result[cat].push(pkg);
  }
  Object.keys(result).forEach(k => { if (!result[k as ModCategory]?.length) delete result[k as ModCategory]; }); return result;
}

export function filterResourcesByType(resources: ResourceKey[], type: BinaryResourceType): ResourceKey[] {
    return (resources || []).filter(res => res?.type === type);
}

// Returns ConflictInfo[]
export function createConflictsForResources(resources: ResourceKey[]): ConflictInfo[] {
  const audio: ResourceKey[] = []; // No audio types in S4TK enum
  const script: ResourceKey[] = []; // No script types in S4TK enum
  const tuning = filterResourcesByType(resources, BinaryResourceTypeValue.CombinedTuning).concat(filterResourcesByType(resources, BinaryResourceTypeValue.SimData));
  const conflicts: ConflictInfo[] = [];
  // Use proper ConflictType and ConflictSeverity enums
  if (audio.length > 1) conflicts.push(createConflict(ConflictType.SOUND, ConflictSeverity.MEDIUM, `Multiple audio resources detected (${audio.length} found)`, audio));
  if (script.length > 1) conflicts.push(createConflict(ConflictType.SCRIPT, ConflictSeverity.HIGH, `Multiple script resources detected (${script.length} found)`, script));
  if (tuning.length > 1) conflicts.push(createConflict(ConflictType.TUNING, ConflictSeverity.MEDIUM, `Multiple tuning/SimData resources detected (${tuning.length} found)`, tuning));
  return conflicts;
}

// Use proper ConflictType and ConflictSeverity types
export function createConflict(type: ConflictType, severity: ConflictSeverity, description: string, affectedResources: ResourceKey[]): ConflictInfo {
  return {
    id: `${type}-${affectedResources[0]?.id || 'unknown'}`,
    type,
    severity,
    description,
    affectedResources,
    timestamp: Date.now(),
    recommendations: [],
    resolution: {
      action: 'Manual review required',
      description: 'Review the affected resources to resolve potential conflicts.'
    }
  };
}

export function filterCASResources(resources: ResourceKey[]): ResourceKey[] {
    const casTypes = [ BinaryResourceTypeValue.CasPart, BinaryResourceTypeValue.CasPartThumbnail, BinaryResourceTypeValue.CasPreset ]; // Use value and correct member names
    return (resources || []).filter(e => e?.type !== undefined && casTypes.includes(e.type as number));
}


