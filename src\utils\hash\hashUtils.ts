/**
 * Hash utility functions
 *
 * This file provides utility functions for calculating hashes of various data types.
 */

import { createHash } from 'crypto';

/**
 * Calculate a SHA-256 hash of a buffer
 * @param buffer The buffer to hash
 * @returns A hex string representation of the hash
 */
export function calculateHash(buffer: Buffer): string {
  return createHash('sha256').update(buffer).digest('hex');
}

/**
 * Calculate a SHA-256 hash of a string
 * @param content The string to hash
 * @returns A hex string representation of the hash
 */
export function calculateStringHash(content: string): string {
  return createHash('sha256').update(content).digest('hex');
}

/**
 * Calculate a simple hash based on the first and last bytes of a buffer
 * This is faster than a full hash but less reliable for conflict detection
 * @param buffer The buffer to hash
 * @param sampleSize Number of bytes to sample from each end (default: 1024)
 * @returns A hex string representation of the hash
 */
export function calculateQuickHash(buffer: Buffer, sampleSize = 1024): string {
  // If buffer is smaller than twice the sample size, just hash the whole thing
  if (buffer.length <= sampleSize * 2) {
    return calculateHash(buffer);
  }

  // Create a new buffer with the first and last bytes
  const combined = Buffer.alloc(sampleSize * 2);

  // Copy the first sampleSize bytes
  buffer.copy(combined, 0, 0, sampleSize);

  // Copy the last sampleSize bytes
  buffer.copy(combined, sampleSize, buffer.length - sampleSize, buffer.length);

  // Calculate hash of the combined buffer
  return calculateHash(combined);
}

/**
 * Calculate a hash of an object by converting to JSON and hashing
 * @param obj The object to hash
 * @returns A hex string representation of the hash
 */
export function calculateObjectHash(obj: any): string {
  return calculateStringHash(JSON.stringify(obj));
}
