/**
 * Consolidated Resource Provider
 *
 * This module provides a unified interface for accessing resources from various sources.
 * It combines the best features of ResourceStreamProvider and StreamingPackageReader,
 * with improved memory management, error handling, and performance.
 */

import { Readable, PassThrough } from 'stream';
import { createReadStream } from 'fs';
import { FileHandle } from 'fs/promises';
import { Logger } from '../../../utils/logging/logger.js';
import { EnhancedBufferPool } from '../../../utils/memory/enhancedBufferPool.js';
import EnhancedMemoryManager from '../../../utils/memory/enhancedMemoryManager.js';
import { ResourceProviderBase, ResourceStreamOptions, ResourceMetadata, IResourceProvider } from './resourceProviderBase.js';
import { PackageIndexEntry } from './index.js';
import zlib from 'zlib';
import { promisify } from 'util';

// Promisify zlib functions
const inflateAsync = promisify(zlib.inflate);

// Create a logger for this module
const logger = new Logger('ConsolidatedResourceProvider');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get buffer pool instance
const bufferPool = EnhancedBufferPool.getInstance();

/**
 * Consolidated resource provider options
 */
export interface ConsolidatedResourceProviderOptions {
    // Buffer pool options
    bufferPoolSize?: number;
    maxBufferSize?: number;

    // Chunk size for streaming
    chunkSize?: number;

    // Hybrid approach thresholds
    directBufferThreshold?: number; // Maximum size for direct buffer approach (default: 5MB)
    chunkedProcessingThreshold?: number; // Maximum size for chunked processing (default: 50MB)

    // Timeout options
    defaultTimeout?: number;

    // File handle cache options
    maxFileHandles?: number;
}

/**
 * Consolidated resource provider implementation
 */
export class ConsolidatedResourceProvider extends ResourceProviderBase {
    private bufferPool: EnhancedBufferPool;
    private chunkSize: number;
    private directBufferThreshold: number;
    private chunkedProcessingThreshold: number;
    private maxFileHandles: number;

    /**
     * Create a new consolidated resource provider
     * @param options Provider options
     */
    constructor(options: ConsolidatedResourceProviderOptions = {}) {
        super();

        // Initialize buffer pool
        this.bufferPool = EnhancedBufferPool.getInstance();

        // Set chunk size
        this.chunkSize = options.chunkSize || 64 * 1024; // 64KB default

        // Set thresholds for hybrid approach
        this.directBufferThreshold = options.directBufferThreshold || 5 * 1024 * 1024; // 5MB default
        this.chunkedProcessingThreshold = options.chunkedProcessingThreshold || 50 * 1024 * 1024; // 50MB default

        // Set timeout
        this.defaultTimeout = options.defaultTimeout || 30000; // 30 seconds default

        // Set max file handles
        this.maxFileHandles = options.maxFileHandles || 100;

        logger.info('Created consolidated resource provider');
    }

    /**
     * Create a readable stream for a resource
     * @param packagePath Path to the package file
     * @param entry Resource entry
     * @param options Stream options
     */
    public async createResourceStream(
        packagePath: string,
        entry: PackageIndexEntry,
        options: ResourceStreamOptions = {}
    ): Promise<Readable> {
        // Default options
        const defaultOptions = {
            highWaterMark: this.chunkSize,
            emitClose: true,
            timeout: this.defaultTimeout,
            autoClose: false // We'll manage file handles ourselves
        };

        const streamOptions = { ...defaultOptions, ...options };

        // Adjust thresholds based on memory pressure
        this.adjustThresholds();

        // Determine the appropriate approach based on resource size
        const resourceSize = entry.compressed ? entry.memSize : entry.fileSize;

        logger.debug(`Creating stream for resource ${entry.type.toString(16)}:${entry.group}:${entry.instanceHi}:${entry.instanceLo} (size: ${this.formatBytes(resourceSize)})`);

        try {
            // For uncompressed resources, use a simple file stream
            if (!entry.compressed) {
                // Validate the entry offset and size
                const fileHandle = await this.getFileHandle(packagePath);
                const stats = await fileHandle.stat();

                if (entry.offset < 0 || entry.offset >= stats.size) {
                    throw new Error(`Invalid resource offset: ${entry.offset}, file size: ${stats.size}`);
                }

                // Ensure we don't read beyond the file size
                const safeEnd = Math.min(entry.offset + entry.fileSize - 1, stats.size - 1);
                if (safeEnd < entry.offset) {
                    throw new Error(`Invalid resource size: ${entry.fileSize} at offset ${entry.offset}, file size: ${stats.size}`);
                }

                const fileStream = createReadStream(packagePath, {
                    start: entry.offset,
                    end: safeEnd,
                    highWaterMark: streamOptions.highWaterMark
                });

                // Add timeout handling
                const timeoutMs = streamOptions.timeout || this.defaultTimeout;
                let timeoutId: NodeJS.Timeout | undefined;

                const resetTimeout = () => {
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }

                    timeoutId = setTimeout(() => {
                        const error = new Error(`Timeout reading resource after ${timeoutMs}ms`);
                        fileStream.destroy(error);
                    }, timeoutMs);
                };

                // Reset timeout on data events
                fileStream.on('data', () => {
                    resetTimeout();
                });

                // Clear timeout on end or error
                fileStream.on('end', () => {
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                        timeoutId = undefined;
                    }
                });

                fileStream.on('error', () => {
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                        timeoutId = undefined;
                    }
                });

                // Start the timeout
                resetTimeout();

                return fileStream;
            } else {
                // For compressed resources, we need to decompress
                // Create a PassThrough stream that will be our output
                const outputStream = new PassThrough({
                    highWaterMark: streamOptions.highWaterMark
                });

                // Get the file handle
                const fileHandle = await this.getFileHandle(packagePath);

                // Read the compressed data
                const buffer = this.bufferPool.getBuffer(entry.fileSize);

                try {
                    // Read the compressed data
                    await fileHandle.read(buffer, 0, entry.fileSize, entry.offset);

                    // Try to decompress the buffer
                    const decompressedBuffer = await inflateAsync(buffer);

                    // Push the decompressed data to the output stream in chunks
                    const chunkSize = this.chunkSize;
                    for (let offset = 0; offset < decompressedBuffer.length; offset += chunkSize) {
                        const end = Math.min(offset + chunkSize, decompressedBuffer.length);
                        const chunk = decompressedBuffer.subarray(offset, end);
                        outputStream.write(chunk);
                    }

                    // End the stream
                    outputStream.end();
                } catch (error: any) {
                    // Handle decompression error
                    logger.error(`Error decompressing resource: ${error.message}`);
                    outputStream.destroy(error);
                } finally {
                    // Return the buffer to the pool
                    this.bufferPool.returnBuffer(buffer);
                }

                return outputStream;
            }
        } catch (error: any) {
            logger.error(`Error creating resource stream: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get metadata about a resource without reading its content
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    public async getResourceMetadata(
        packagePath: string,
        entry: PackageIndexEntry
    ): Promise<ResourceMetadata> {
        try {
            // Get file handle
            const fileHandle = await this.getFileHandle(packagePath);

            // Check if the offset is valid
            const stats = await fileHandle.stat();
            const exists = entry.offset < stats.size;

            return {
                size: entry.compressed ? entry.memSize : entry.fileSize,
                compressed: entry.compressed,
                memSize: entry.memSize,
                fileSize: entry.fileSize,
                offset: entry.offset,
                exists
            };
        } catch (error: any) {
            logger.error(`Failed to get resource metadata: ${error.message}`);

            // Return default metadata with exists=false
            return {
                size: entry.compressed ? entry.memSize : entry.fileSize,
                compressed: entry.compressed,
                memSize: entry.memSize,
                fileSize: entry.fileSize,
                offset: entry.offset,
                exists: false
            };
        }
    }

    /**
     * Check if a resource exists
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    public async resourceExists(
        packagePath: string,
        entry: PackageIndexEntry
    ): Promise<boolean> {
        try {
            // Get file handle
            const fileHandle = await this.getFileHandle(packagePath);

            // Check if the offset is valid
            const stats = await fileHandle.stat();
            return entry.offset < stats.size;
        } catch (error: any) {
            logger.error(`Failed to check if resource exists: ${error.message}`);
            return false;
        }
    }

    /**
     * Adjust thresholds based on memory pressure
     * @private
     */
    private adjustThresholds(): void {
        const memoryPressure = memoryManager.getMemoryPressure();

        // More aggressive memory management with lower thresholds
        if (memoryPressure > 0.9) {
            // Extreme memory pressure - force streaming for everything
            this.directBufferThreshold = 512 * 1024; // 512KB
            this.chunkedProcessingThreshold = 2 * 1024 * 1024; // 2MB

            // Force garbage collection
            if (global.gc) {
                global.gc();
            }
        } else if (memoryPressure > 0.8) {
            // High memory pressure - use more streaming
            this.directBufferThreshold = 1 * 1024 * 1024; // 1MB
            this.chunkedProcessingThreshold = 5 * 1024 * 1024; // 5MB
        } else if (memoryPressure > 0.6) {
            // Medium memory pressure
            this.directBufferThreshold = 2 * 1024 * 1024; // 2MB
            this.chunkedProcessingThreshold = 10 * 1024 * 1024; // 10MB
        } else if (memoryPressure > 0.4) {
            // Moderate memory pressure
            this.directBufferThreshold = 3 * 1024 * 1024; // 3MB
            this.chunkedProcessingThreshold = 20 * 1024 * 1024; // 20MB
        } else {
            // Low memory pressure - can use more direct buffers
            this.directBufferThreshold = 5 * 1024 * 1024; // 5MB
            this.chunkedProcessingThreshold = 50 * 1024 * 1024; // 50MB
        }

        // Log threshold adjustments for debugging
        logger.debug(`Adjusted thresholds for memory pressure ${(memoryPressure * 100).toFixed(1)}%: ` +
                     `directBuffer=${(this.directBufferThreshold / 1024 / 1024).toFixed(1)}MB, ` +
                     `chunkedProcessing=${(this.chunkedProcessingThreshold / 1024 / 1024).toFixed(1)}MB`);
    }
}
