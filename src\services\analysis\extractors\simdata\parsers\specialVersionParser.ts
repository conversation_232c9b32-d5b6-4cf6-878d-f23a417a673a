/**
 * Parser for special SimData versions (16708, 48111, etc.)
 * These are non-standard versions used in some mods
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataSchema } from '../simDataTypes.js';
import { <PERSON><PERSON>erReader } from '../utils/bufferReader.js';
import { 
    SimDataErrorContext, 
    createSimDataErrorContext, 
    handleSimDataError 
} from '../error/simDataParserErrorHandler.js';
import { ParsedSimData } from './standardVersionParser.js';

const logger = new Logger('SpecialVersionParser');

/**
 * Parses SimData version 16708
 * This appears to be a special version used in some mods
 * @param buffer SimData buffer
 * @returns Parsed SimData or undefined if parsing fails
 */
export function parseVersion16708(buffer: Buffer): ParsedSimData | undefined {
    try {
        logger.info('Parsing SimData version 16708');
        
        // Create a buffer reader
        const reader = new BufferReader(buffer, 0, 16708);
        
        // Read version and flags
        const version = reader.readUInt16LE();
        if (version === undefined || version !== 16708) {
            logger.error(`Version mismatch: expected 16708, got ${version}`);
            return undefined;
        }
        
        const flags = reader.readUInt16LE();
        if (flags === undefined) {
            logger.error('Failed to read flags');
            return undefined;
        }
        
        // Create a minimal schema with basic information
        // This is a safer approach for version 16708 which has variable structure
        const schema: SimDataSchema = {
            name: "ModData",
            schemaId: 0,
            hash: 0,
            columns: [],
            version: 16708,
            flags: flags
        };
        
        // Try to extract schema name if possible
        try {
            // Version 16708 has a different header structure
            // The schema name is usually at offset 8 or 9
            reader.setPosition(8);
            
            // Try to read schema name length
            const schemaNameLength = reader.readUInt8();
            
            // Validate schema name length is reasonable
            if (schemaNameLength !== undefined && schemaNameLength > 0 && schemaNameLength < 50) {
                const possibleName = reader.readString(schemaNameLength);
                
                // If it looks like a valid name, use it
                if (possibleName && /^[a-zA-Z0-9_]+$/.test(possibleName)) {
                    schema.name = possibleName;
                    logger.info(`Found schema name: ${possibleName}`);
                }
            }
        } catch (e) {
            logger.warn(`Error extracting schema name: ${e}`);
        }
        
        // Try to extract column information if possible
        try {
            // Column information is usually after the schema name and hash
            // But the exact offset varies, so we'll try to find it
            let offset = 16; // Start after header and potential schema name
            
            // Look for a reasonable column count (0-50)
            while (offset + 2 <= buffer.length && offset < 100) {
                reader.setPosition(offset);
                const possibleColumnCount = reader.readUInt16LE();
                
                if (possibleColumnCount !== undefined && possibleColumnCount > 0 && possibleColumnCount <= 50) {
                    logger.info(`Found possible column count: ${possibleColumnCount} at offset ${offset}`);
                    
                    // Try to read a few columns to validate
                    let testOffset = offset + 2;
                    let validColumns = 0;
                    
                    for (let i = 0; i < Math.min(3, possibleColumnCount); i++) {
                        reader.setPosition(testOffset);
                        
                        const columnNameLength = reader.readUInt8();
                        if (columnNameLength === undefined || columnNameLength === 0 || columnNameLength >= 30) {
                            break;
                        }
                        
                        testOffset += 1;
                        const columnName = reader.readString(columnNameLength);
                        if (columnName === undefined) {
                            break;
                        }
                        
                        testOffset += columnNameLength;
                        
                        // Check if column name looks valid
                        if (/^[a-zA-Z0-9_]+$/.test(columnName)) {
                            validColumns++;
                        }
                        
                        // Skip type, flags, and any other data
                        testOffset += 6;
                    }
                    
                    if (validColumns > 0) {
                        logger.info(`Found ${validColumns} valid columns`);
                        break;
                    }
                }
                
                offset += 2;
            }
        } catch (e) {
            logger.warn(`Error extracting column information: ${e}`);
        }
        
        // For version 16708, we'll return a minimal structure
        // with the schema information we were able to extract
        // This is safer than trying to parse the full structure
        // which varies between different mods
        return {
            schema: schema,
            instances: [],
            version: 16708,
            flags: flags
        };
    } catch (error) {
        return handleSimDataError(
            error,
            createSimDataErrorContext(16708, 0, 'parseVersion16708'),
            undefined
        );
    }
}

/**
 * Parses SimData version 48111
 * This appears to be a special version used in some mods
 * @param buffer SimData buffer
 * @returns Parsed SimData or undefined if parsing fails
 */
export function parseVersion48111(buffer: Buffer): ParsedSimData | undefined {
    try {
        logger.info('Parsing SimData version 48111');
        
        // Create a buffer reader
        const reader = new BufferReader(buffer, 0, 48111);
        
        // Read version
        const version = reader.readUInt16LE();
        if (version === undefined || version !== 48111) {
            logger.error(`Version mismatch: expected 48111, got ${version}`);
            return undefined;
        }
        
        // Create a minimal schema
        const schema: SimDataSchema = {
            name: "Version48111Schema",
            schemaId: 0,
            hash: 0,
            columns: [],
            version: 48111
        };
        
        // Try to extract some basic information
        reader.setPosition(4); // Skip version and flags
        
        // Try to read a string at offset 8 (potential schema name)
        try {
            // Check if there's a valid string length
            const nameLength = reader.readUInt16LE();
            
            // Validate string length is reasonable
            if (nameLength !== undefined && nameLength > 0 && nameLength < 100) {
                const possibleName = reader.readString(nameLength);
                
                // If it looks like a valid string, use it as schema name
                if (possibleName && /^[a-zA-Z0-9_]+$/.test(possibleName)) {
                    schema.name = possibleName;
                    logger.info(`Found possible schema name: ${possibleName}`);
                }
            }
        } catch (e) {
            logger.warn(`Error trying to extract schema name from version 48111: ${e}`);
        }
        
        // Return minimal structure
        // Future: Implement more complete parsing as we learn more about this format
        return {
            schema: schema,
            instances: [],
            version: 48111
        };
    } catch (error) {
        return handleSimDataError(
            error,
            createSimDataErrorContext(48111, 0, 'parseVersion48111'),
            undefined
        );
    }
}
