import { ResourceKey as AppResource<PERSON>ey, ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';

const logger = new Logger('StringTableDetector');

/**
 * Checks if the buffer is a binary STBL file and redirects to the string table extractor if so.
 */
export async function checkAndRedirectStbl(
    key: AppR<PERSON>ource<PERSON><PERSON>,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata> | undefined> {
    if (buffer.length >= 4) {
        const magic = buffer.toString('utf8', 0, 4);
        if (magic === 'STBL') {
            logger.info(`Resource ${key.instance.toString(16)} is a binary STBL file, not XML. Redirecting to string table extractor.`);

            // Update the resource type in the database to STRING_TABLE
            await databaseService.executeQuery(
                'UPDATE Resources SET resourceType = ? WHERE id = ?',
                ['STRING_TABLE', resourceId]
            );

            // Import the string table extractor
            const { extractStringTableMetadata } = await import('../extractStringTableMetadata.js');

            // Extract string table metadata
            return {
                ...(await extractStringTableMetadata(key, buffer, resourceId, databaseService)),
                extractorUsed: 'stringtable'
            };
        }

        // Check for binary representation of STBL (0x5354424C)
        const magicNumber = buffer.readUInt32BE(0);
        if (magicNumber === 0x5354424C) { // 'STBL' in hex
            logger.info(`Resource ${key.instance.toString(16)} is a binary STBL file (BE), not XML. Redirecting to string table extractor.`);

            // Update the resource type in the database to STRING_TABLE
            await databaseService.executeQuery(
                'UPDATE Resources SET resourceType = ? WHERE id = ?',
                ['STRING_TABLE', resourceId]
            );

            // Import the string table extractor
            const { extractStringTableMetadata } = await import('../extractStringTableMetadata.js');

            // Extract string table metadata
            return {
                ...(await extractStringTableMetadata(key, buffer, resourceId, databaseService)),
                extractorUsed: 'stringtable'
            };
        }

        // Check for little-endian representation (0x4C425453)
        const magicNumberLE = buffer.readUInt32LE(0);
        if (magicNumberLE === 0x4C425453) { // 'LBTS' in hex (STBL reversed)
            logger.info(`Resource ${key.instance.toString(16)} is a binary STBL file (LE), not XML. Redirecting to string table extractor.`);
            
            // Update the resource type in the database to STRING_TABLE
            await databaseService.executeQuery(
                'UPDATE Resources SET resourceType = ? WHERE id = ?',
                ['STRING_TABLE', resourceId]
            );

            // Import the string table extractor
            const { extractStringTableMetadata } = await import('../extractStringTableMetadata.js');

            // Extract string table metadata
            return {
                ...(await extractStringTableMetadata(key, buffer, resourceId, databaseService)),
                extractorUsed: 'stringtable'
            };
        }
    }
    return undefined;
}

/**
 * Checks if the XML is a string table and redirects if so.
 */
export async function checkAndRedirectXmlStbl(
    rootElement: any,
    rootElementName: string,
    key: AppResourceKey,
    resourceId: number,
    databaseService: DatabaseService,
    xmlString: string
): Promise<Partial<ResourceMetadata> | undefined> {
    // Check if this is an XML-based string table
    if (rootElementName === 'StringTable' || rootElementName === 'Text' || rootElementName === 'Strings') {
        logger.info(`Resource ${key.instance.toString(16)} is an XML-based string table. Redirecting to XML string table extractor.`);

        // Update the resource type in the database to STRING_TABLE
        await databaseService.executeQuery(
            'UPDATE Resources SET resourceType = ? WHERE id = ?',
            ['STRING_TABLE', resourceId]
        );

        // Extract string table entries from XML
        const xmlStringTable = extractXmlStringTable(rootElement, rootElementName);

        // Save parsed content to database
        await databaseService.saveParsedContent({
            resourceId: resourceId,
            contentType: 'stringtable',
            content: JSON.stringify(xmlStringTable)
        });

        // Create metadata for string table
        const stringTableMetadata: Partial<ResourceMetadata> = {
            stblEntryCount: xmlStringTable.entries.length,
            stblLocale: xmlStringTable.locale || 'Unknown',
            stblLocaleId: 0, // Default to English
            stblTotalEntries: xmlStringTable.entries.length,
            contentSnippet: `[XML String Table: Locale=${xmlStringTable.locale || 'Unknown'}, Entries=${xmlStringTable.entries.length}]`,
            extractorUsed: 'xml_stringtable'
        };

        // Categorize strings
        const potentialUIStrings = [];
        const potentialInteractionStrings = [];
        const potentialDescriptionStrings = [];

        // Analyze string content
        for (const entry of xmlStringTable.entries) {
            const value = entry.value;

            // Check for UI elements (buttons, labels, etc.)
            if (value.includes('{ButtonText}') || value.includes('{Label}') ||
                value.match(/^[A-Z][a-z]+(\s[A-Z][a-z]+)*$/) || // Title Case without spaces
                value.length < 20) { // Short strings are often UI elements
                potentialUIStrings.push({
                    key: typeof entry.key === 'string' ? entry.key : entry.key.toString(16).toUpperCase(),
                    value: value.length > 1000 ? value.substring(0, 1000) + '...' : value
                });
            }

            // Check for interaction strings
            else if (value.includes('{SimName}') || value.includes('{TargetSim}') ||
                     value.includes('...') || value.match(/^[A-Z][a-z]+/)) {
                potentialInteractionStrings.push({
                    key: typeof entry.key === 'string' ? entry.key : entry.key.toString(16).toUpperCase(),
                    value: value.length > 1000 ? value.substring(0, 1000) + '...' : value
                });
            }

            // Check for description strings (longer text)
            else if (value.length > 100 || value.includes('\n') || value.includes('.')) {
                potentialDescriptionStrings.push({
                    key: typeof entry.key === 'string' ? entry.key : entry.key.toString(16).toUpperCase(),
                    value: value.length > 1000 ? value.substring(0, 1000) + '...' : value
                });
            }
        }

        // Add string analysis to metadata
        stringTableMetadata.stblUIStringCount = potentialUIStrings.length;
        stringTableMetadata.stblInteractionStringCount = potentialInteractionStrings.length;
        stringTableMetadata.stblDescriptionStringCount = potentialDescriptionStrings.length;

        // Store sample strings
        if (potentialUIStrings.length > 0) {
            stringTableMetadata.stblUIStrings = JSON.stringify(potentialUIStrings.slice(0, 10));
        }

        if (potentialInteractionStrings.length > 0) {
            stringTableMetadata.stblInteractionStrings = JSON.stringify(potentialInteractionStrings.slice(0, 10));
        }
        
        if (potentialDescriptionStrings.length > 0) {
            stringTableMetadata.stblDescriptionStrings = JSON.stringify(potentialDescriptionStrings.slice(0, 10));
        }

        return stringTableMetadata;
    }
    return undefined;
}

/**
 * Extract string table entries from XML
 * @param rootElement The root element of the XML
 * @param rootElementName The name of the root element
 * @returns A StringTableResource object
 */
export function extractXmlStringTable(rootElement: any, rootElementName: string): { entries: { key: string | number; value: string }[]; locale?: string } {
    const entries: { key: string | number; value: string }[] = [];
    let locale: string | undefined = undefined;

    try {
        // Different XML string table formats
        if (rootElementName === 'StringTable') {
            // Format 1: <StringTable><Strings><String key="..." value="..."/></Strings></StringTable>
            if (rootElement.Strings && rootElement.Strings.String) {
                const strings = Array.isArray(rootElement.Strings.String)
                    ? rootElement.Strings.String
                    : [rootElement.Strings.String];

                for (const str of strings) {
                    if (str.$ && str.$.key && (str.$.value || str._)) {
                        entries.push({
                            key: str.$.key,
                            value: str.$.value || str._
                        });
                    }
                }
            }

            // Format 2: <StringTable><String key="..." value="..."/></StringTable>
            else if (rootElement.String) {
                const strings = Array.isArray(rootElement.String)
                    ? rootElement.String
                    : [rootElement.String];

                for (const str of strings) {
                    if (str.$ && str.$.key && (str.$.value || str._)) {
                        entries.push({
                            key: str.$.key,
                            value: str.$.value || str._
                        });
                    }
                }
            }

            // Get locale if available
            if (rootElement.$ && rootElement.$.locale) {
                locale = rootElement.$.locale;
            }
        }
        else if (rootElementName === 'Text' || rootElementName === 'Strings') {
            // Format: <Text><String id="..." text="..."/></Text>
            // or: <Strings><String id="..." text="..."/></Strings>
            if (rootElement.String) {
                const strings = Array.isArray(rootElement.String)
                    ? rootElement.String
                    : [rootElement.String];

                for (const str of strings) {
                    if (str.$) {
                        // Different attribute names for key and value
                        const key = str.$.id || str.$.key || str.$.name;
                        const value = str.$.text || str.$.value || str._ || '';

                        if (key && value) {
                            entries.push({ key, value });
                        }
                    }
                }
            }

            // Get locale if available
            if (rootElement.$ && rootElement.$.locale) {
                locale = rootElement.$.locale;
            } else if (rootElement.$ && rootElement.$.language) {
                locale = rootElement.$.language;
            }
        }

        // If no locale was found, try to determine from content
        if (!locale) {
            // Count occurrences of common words in different languages
            const englishWords = ['the', 'and', 'to', 'of', 'a', 'in', 'is', 'you', 'that', 'it'];
            const spanishWords = ['el', 'la', 'de', 'y', 'en', 'es', 'que', 'un', 'por', 'para'];
            const frenchWords = ['le', 'la', 'de', 'et', 'en', 'est', 'que', 'un', 'pour', 'dans'];
            const germanWords = ['der', 'die', 'das', 'und', 'ist', 'in', 'zu', 'den', 'mit', 'für'];

            let englishCount = 0;
            let spanishCount = 0;
            let frenchCount = 0;
            let germanCount = 0;

            // Check first 10 entries
            const samplesToCheck = Math.min(entries.length, 10);
            for (let i = 0; i < samplesToCheck; i++) {
                const value = entries[i].value.toLowerCase();

                for (const word of englishWords) {
                    if (value.includes(` ${word} `)) englishCount++;
                }

                for (const word of spanishWords) {
                    if (value.includes(` ${word} `)) spanishCount++;
                }

                for (const word of frenchWords) {
                    if (value.includes(` ${word} `)) frenchCount++;
                }

                for (const word of germanWords) {
                    if (value.includes(` ${word} `)) germanCount++;
                }
            }

            // Determine most likely language
            const counts = [
                { lang: 'English', count: englishCount },
                { lang: 'Spanish', count: spanishCount },
                { lang: 'French', count: frenchCount },
                { lang: 'German', count: germanCount }
            ];

            counts.sort((a, b) => b.count - a.count);

            if (counts[0].count > 0) {
                locale = counts[0].lang;
            } else {
                locale = 'Unknown';
            }
        }

    } catch (error) {
        logger.error(`Error extracting XML string table: ${error}`);
    }

    return { entries, locale };
}
