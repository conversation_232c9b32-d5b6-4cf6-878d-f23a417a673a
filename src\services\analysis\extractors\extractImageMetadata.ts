/**
 * Image metadata extractor module for the Sims 4 mod management tool
 *
 * This module is a wrapper for the image extractors in the image/ directory.
 * It delegates extraction to the appropriate extractor based on the resource type.
 */

import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../types/resource/interfaces.js';
import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { extractImageMetadata as extractImageMetadataRefactored } from './image/imageExtractor.js';

const logger = new Logger('ExtractImageMetadata');

/**
 * Extracts metadata specifically from Image resources.
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object for Image resources.
 */
export async function extractImageMetadata(
    key: AppResource<PERSON><PERSON>,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    // Use the refactored image extractor
    return extractImageMetadataRefactored(key, buffer, resourceId, databaseService);
}
