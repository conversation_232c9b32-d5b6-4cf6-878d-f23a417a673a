const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
// const { fileURLToPath } = require('url'); // Not needed for CommonJS __dirname
const { VueLoaderPlugin } = require('vue-loader'); // Import VueLoaderPlugin

// const __filename = fileURLToPath(import.meta.url); // Not needed for CommonJS __dirname
// const __dirname = path.dirname(__filename); // __dirname is available directly in CommonJS

module.exports = {
  cache: false, // Explicitly disable cache
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  entry: {
    // main: './src/frontend/index.ts', // Removed old entry point
    // streamlit: './src/frontend/streamlit/index.ts', // Removed streamlit entry
    electron: './src/frontend/electron/index.ts' // Keep electron entry point
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js',
    clean: true,
    publicPath: '/'
  },
  module: {
    rules: [
      {
        // Use ts-loader for ts/tsx files
        test: /\.tsx?$/,
        loader: 'ts-loader',
        options: {
          // Tell ts-loader to process .vue files
          appendTsSuffixTo: [/\.vue$/],
        },
        exclude: /node_modules/,
      },
      {
        test: /\.vue$/, // Add rule for .vue files
        loader: 'vue-loader'
      },
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1
            }
          }
        ]
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource'
      }
    ]
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.vue'], // Add .vue extension
    extensionAlias: {
      '.ts': ['.js', '.ts'],
      '.tsx': ['.js', '.tsx'],
      '.vue': ['.js', '.vue'] // Add alias for .vue
    },
    alias: {
      '@': path.resolve(__dirname, 'src'),
      // Removed direct mappings as they didn't solve nested issues
    },
    modules: [path.resolve(__dirname, 'src'), 'node_modules'], // Prioritize src directory
    fallback: { "path": false },
    fullySpecified: false
  },
  externals: {
    // Treat native modules as external dependencies
    bufferfromfile: 'commonjs bufferfromfile'
  },
  plugins: [
    // Removed HtmlWebpackPlugin for old 'main' chunk (index.html)
    // Removed Streamlit HtmlWebpackPlugin
    new HtmlWebpackPlugin({
      template: './src/frontend/electron/index.html',
      filename: 'electron.html',
      chunks: ['electron']
    }),
    new MiniCssExtractPlugin({
      filename: '[name].[contenthash].css'
    }),
    new CopyWebpackPlugin({
      patterns: [
        { from: 'public', to: '.' }
      ]
    }),
    new VueLoaderPlugin() // Add VueLoaderPlugin instance
  ],
  devServer: {
    static: {
      directory: path.join(__dirname, 'dist')
    },
    compress: true,
    port: 8081, // Changed port from 8080 to 8081
    hot: true,
    historyApiFallback: {
      rewrites: [
        // { from: /^\/streamlit/, to: '/streamlit.html' }, // Removed streamlit rewrite
        { from: /^\/electron/, to: '/electron.html' },
        { from: /^\/$/, to: '/electron.html' } // Serve electron.html for the root path as well
      ]
    },
    headers: {
      // Adjusted CSP: Added https://fonts.googleapis.com to style-src and https://fonts.gstatic.com to font-src
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:;"
    },
    proxy: {
      '/api': {
        target: 'http://localhost:8501',
        changeOrigin: true
      },
      '/socket.io': {
        target: 'http://localhost:8501',
        ws: true
      }
    }
  },
  optimization: {
    moduleIds: 'deterministic',
    runtimeChunk: 'single',
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  }
};
