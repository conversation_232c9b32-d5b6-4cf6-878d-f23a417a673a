/**
 * TS4Script Pattern Detector
 * 
 * This module provides functionality for detecting common patterns in Sims 4 scripts.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { TS4ScriptModule, TS4ScriptFunction, TS4ScriptDecorator, TS4ScriptCommand, TS4ScriptInjection, TS4ScriptEventHandler, TS4ScriptTuningReference } from '../types.js';

/**
 * Pattern detection result
 */
export interface PatternDetectionResult {
    /**
     * Has injections
     */
    hasInjections: boolean;

    /**
     * Has event handlers
     */
    hasEventHandlers: boolean;

    /**
     * Has commands
     */
    hasCommands: boolean;

    /**
     * Has tuning references
     */
    hasTuningReferences: boolean;

    /**
     * Module type
     */
    moduleType: string;

    /**
     * Detected commands
     */
    commands: TS4ScriptCommand[];

    /**
     * Detected injections
     */
    injections: TS4ScriptInjection[];

    /**
     * Detected event handlers
     */
    eventHandlers: TS4ScriptEventHandler[];

    /**
     * Detected tuning references
     */
    tuningReferences: TS4ScriptTuningReference[];
}

/**
 * TS4Script pattern detector
 */
@singleton()
export class PatternDetector {
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(private logger: Logger = new Logger('PatternDetector')) {}

    /**
     * Detect patterns in module
     * @param module TS4Script module
     * @returns Pattern detection result
     */
    public detectPatterns(module: TS4ScriptModule): PatternDetectionResult {
        try {
            this.logger.debug(`Detecting patterns in module: ${module.name}`);

            // Initialize result
            const result: PatternDetectionResult = {
                hasInjections: false,
                hasEventHandlers: false,
                hasCommands: false,
                hasTuningReferences: false,
                moduleType: 'unknown',
                commands: [],
                injections: [],
                eventHandlers: [],
                tuningReferences: []
            };

            // Detect injections
            result.hasInjections = this.detectInjections(module, result);

            // Detect commands
            result.hasCommands = this.detectCommands(module, result);

            // Detect event handlers
            result.hasEventHandlers = this.detectEventHandlers(module, result);

            // Detect tuning references
            result.hasTuningReferences = this.detectTuningReferences(module, result);

            // Determine module type
            result.moduleType = this.determineModuleType(module, result);

            return result;
        } catch (error) {
            this.logger.error(`Error detecting patterns in module ${module.name}:`, error);
            return {
                hasInjections: false,
                hasEventHandlers: false,
                hasCommands: false,
                hasTuningReferences: false,
                moduleType: 'unknown',
                commands: [],
                injections: [],
                eventHandlers: [],
                tuningReferences: []
            };
        }
    }

    /**
     * Detect injections in module
     * @param module TS4Script module
     * @param result Pattern detection result
     * @returns Has injections
     */
    private detectInjections(module: TS4ScriptModule, result: PatternDetectionResult): boolean {
        try {
            // Check for injection patterns in content
            const content = module.content;
            
            // Check for common injection patterns
            const hasInjectionDecorator = /\@inject(?:or)?/.test(content);
            const hasMonkeyPatching = /setattr\s*\(\s*\w+\s*,\s*['"](\w+)['"]\s*,/.test(content);
            const hasInjectToPattern = /inject_to\s*\(\s*\w+\s*,\s*['"](\w+)['"]\s*\)/.test(content);
            
            // Check for injection function definition
            const hasInjectionFunction = /def\s+inject(?:or)?/.test(content);
            
            // Check for patch function definition
            const hasPatchFunction = /def\s+patch/.test(content);

            // If any injection pattern is found, mark as having injections
            const hasInjections = hasInjectionDecorator || hasMonkeyPatching || hasInjectionFunction || hasPatchFunction || hasInjectToPattern;

            // If injections are found, extract injection details
            if (hasInjections) {
                // Extract injection details using regex
                // This is a simplified implementation
                // A more robust implementation would parse the AST
                
                // Look for decorator-style injections
                const decoratorRegex = /\@inject(?:or)?\s*\(\s*(\w+)\s*,\s*['"](\w+)['"]\s*\)/g;
                let match;
                while ((match = decoratorRegex.exec(content)) !== null) {
                    const targetObject = match[1];
                    const targetFunction = match[2];
                    
                    result.injections.push({
                        targetObject,
                        targetFunction,
                        injectionType: 'decorator',
                        callsOriginal: /original\s*\(/.test(content)
                    });
                }
                
                // Look for monkey patching
                const monkeyPatchRegex = /setattr\s*\(\s*(\w+)\s*,\s*['"](\w+)['"]\s*,\s*(\w+)\s*\)/g;
                while ((match = monkeyPatchRegex.exec(content)) !== null) {
                    const targetObject = match[1];
                    const targetFunction = match[2];
                    
                    result.injections.push({
                        targetObject,
                        targetFunction,
                        injectionType: 'monkey_patch',
                        callsOriginal: /original\s*\(/.test(content)
                    });
                }
                
                // Look for inject_to pattern
                const injectToRegex = /inject_to\s*\(\s*(\w+)\s*,\s*['"](\w+)['"]\s*\)/g;
                while ((match = injectToRegex.exec(content)) !== null) {
                    const targetObject = match[1];
                    const targetFunction = match[2];
                    
                    result.injections.push({
                        targetObject,
                        targetFunction,
                        injectionType: 'inject_to',
                        callsOriginal: /original\s*\(/.test(content)
                    });
                }
            }

            return hasInjections;
        } catch (error) {
            this.logger.error(`Error detecting injections in module ${module.name}:`, error);
            return false;
        }
    }

    /**
     * Detect commands in module
     * @param module TS4Script module
     * @param result Pattern detection result
     * @returns Has commands
     */
    private detectCommands(module: TS4ScriptModule, result: PatternDetectionResult): boolean {
        try {
            // Check for command patterns in content
            const content = module.content;
            
            // Check for command decorator
            const hasCommandDecorator = /@sims4\.commands\.Command/.test(content);
            
            // If command decorator is found, mark as having commands
            const hasCommands = hasCommandDecorator;

            // If commands are found, extract command details
            if (hasCommands) {
                // Extract command details using regex
                // This is a simplified implementation
                // A more robust implementation would parse the AST
                
                // Look for command decorators
                const commandRegex = /@sims4\.commands\.Command\s*\(\s*['"]([^'"]+)['"]\s*(?:,\s*command_type\s*=\s*sims4\.commands\.CommandType\.(\w+))?\s*\)/g;
                let match;
                while ((match = commandRegex.exec(content)) !== null) {
                    const commandName = match[1];
                    const commandType = match[2] || 'Live';
                    
                    // Look for the function definition following the decorator
                    const functionRegex = new RegExp(`def\\s+(\\w+)\\s*\\(([^)]*)\\)\\s*:`, 'g');
                    functionRegex.lastIndex = match.index + match[0].length;
                    const functionMatch = functionRegex.exec(content);
                    
                    if (functionMatch) {
                        const functionName = functionMatch[1];
                        const parameters = functionMatch[2].split(',').map(p => p.trim());
                        
                        // Check if the function has a _connection parameter
                        const hasConnection = parameters.some(p => p.includes('_connection'));
                        
                        result.commands.push({
                            commandName,
                            commandType,
                            parameters: parameters.map(p => {
                                const [name, defaultValue] = p.split('=').map(part => part.trim());
                                return {
                                    name,
                                    defaultValue,
                                    isPositionalOnly: false,
                                    isKeywordOnly: false,
                                    isVariadicPositional: name.startsWith('*') && !name.startsWith('**'),
                                    isVariadicKeyword: name.startsWith('**')
                                };
                            }),
                            hasConnection
                        });
                    }
                }
            }

            return hasCommands;
        } catch (error) {
            this.logger.error(`Error detecting commands in module ${module.name}:`, error);
            return false;
        }
    }

    /**
     * Detect event handlers in module
     * @param module TS4Script module
     * @param result Pattern detection result
     * @returns Has event handlers
     */
    private detectEventHandlers(module: TS4ScriptModule, result: PatternDetectionResult): boolean {
        try {
            // Check for event handler patterns in content
            const content = module.content;
            
            // Check for common event handler patterns
            const hasZoneLoadHandler = /on_zone_load/.test(content);
            const hasSimInitHandler = /on_sim_init/.test(content);
            const hasSimSpawnHandler = /on_sim_spawn/.test(content);
            const hasAddHandler = /on_add/.test(content);
            const hasRemoveHandler = /on_remove/.test(content);
            const hasLoadHandler = /on_load/.test(content);
            const hasSaveHandler = /on_save/.test(content);
            
            // If any event handler pattern is found, mark as having event handlers
            const hasEventHandlers = hasZoneLoadHandler || hasSimInitHandler || hasSimSpawnHandler || 
                                    hasAddHandler || hasRemoveHandler || hasLoadHandler || hasSaveHandler;

            // If event handlers are found, extract event handler details
            if (hasEventHandlers) {
                // Extract event handler details using regex
                // This is a simplified implementation
                // A more robust implementation would parse the AST
                
                // Define event handler patterns to look for
                const eventHandlerPatterns = [
                    { pattern: /on_zone_load/, eventType: 'zone_load' },
                    { pattern: /on_sim_init/, eventType: 'sim_init' },
                    { pattern: /on_sim_spawn/, eventType: 'sim_spawn' },
                    { pattern: /on_add/, eventType: 'add' },
                    { pattern: /on_remove/, eventType: 'remove' },
                    { pattern: /on_load/, eventType: 'load' },
                    { pattern: /on_save/, eventType: 'save' }
                ];
                
                // Look for function definitions matching event handler patterns
                const functionRegex = /def\s+(\w+)\s*\(([^)]*)\)\s*:/g;
                let match;
                while ((match = functionRegex.exec(content)) !== null) {
                    const functionName = match[1];
                    
                    // Check if function name matches any event handler pattern
                    for (const { pattern, eventType } of eventHandlerPatterns) {
                        if (pattern.test(functionName)) {
                            // Extract target object if available
                            let targetObject = null;
                            const targetObjectRegex = new RegExp(`@injector\\.inject_to\\s*\\(\\s*(\\w+)\\s*,\\s*['"]${functionName}['"]\\s*\\)`, 'g');
                            const targetObjectMatch = targetObjectRegex.exec(content);
                            if (targetObjectMatch) {
                                targetObject = targetObjectMatch[1];
                            }
                            
                            result.eventHandlers.push({
                                eventType,
                                targetObject
                            });
                            break;
                        }
                    }
                }
            }

            return hasEventHandlers;
        } catch (error) {
            this.logger.error(`Error detecting event handlers in module ${module.name}:`, error);
            return false;
        }
    }

    /**
     * Detect tuning references in module
     * @param module TS4Script module
     * @param result Pattern detection result
     * @returns Has tuning references
     */
    private detectTuningReferences(module: TS4ScriptModule, result: PatternDetectionResult): boolean {
        try {
            // Check for tuning reference patterns in content
            const content = module.content;
            
            // Check for common tuning reference patterns
            const hasInstanceManagerGet = /get_instance_manager\s*\(\s*Types\.(\w+)\s*\)/.test(content);
            const hasTunableInstanceParam = /TunableInstanceParam\s*\(\s*Types\.(\w+)\s*\)/.test(content);
            const hasResourceKeyPattern = /ResourceKey\s*\(\s*\w+\s*,\s*Types\.(\w+)\s*,/.test(content);
            
            // If any tuning reference pattern is found, mark as having tuning references
            const hasTuningReferences = hasInstanceManagerGet || hasTunableInstanceParam || hasResourceKeyPattern;

            // If tuning references are found, extract tuning reference details
            if (hasTuningReferences) {
                // Extract tuning reference details using regex
                // This is a simplified implementation
                // A more robust implementation would parse the AST
                
                // Look for instance manager get calls
                const instanceManagerRegex = /get_instance_manager\s*\(\s*Types\.(\w+)\s*\)/g;
                let match;
                while ((match = instanceManagerRegex.exec(content)) !== null) {
                    const resourceType = match[1];
                    
                    result.tuningReferences.push({
                        resourceType,
                        accessType: 'get_instance_manager'
                    });
                }
                
                // Look for TunableInstanceParam
                const tunableInstanceParamRegex = /TunableInstanceParam\s*\(\s*Types\.(\w+)\s*\)/g;
                while ((match = tunableInstanceParamRegex.exec(content)) !== null) {
                    const resourceType = match[1];
                    
                    result.tuningReferences.push({
                        resourceType,
                        accessType: 'tunable_instance_param'
                    });
                }
                
                // Look for ResourceKey
                const resourceKeyRegex = /ResourceKey\s*\(\s*\w+\s*,\s*Types\.(\w+)\s*,\s*(\w+)\s*\)/g;
                while ((match = resourceKeyRegex.exec(content)) !== null) {
                    const resourceType = match[1];
                    const instanceId = match[2];
                    
                    result.tuningReferences.push({
                        resourceType,
                        instanceId,
                        accessType: 'resource_key'
                    });
                }
            }

            return hasTuningReferences;
        } catch (error) {
            this.logger.error(`Error detecting tuning references in module ${module.name}:`, error);
            return false;
        }
    }

    /**
     * Determine module type
     * @param module TS4Script module
     * @param result Pattern detection result
     * @returns Module type
     */
    private determineModuleType(module: TS4ScriptModule, result: PatternDetectionResult): string {
        try {
            // Determine module type based on detected patterns
            if (result.hasCommands) {
                return 'command';
            } else if (result.hasInjections) {
                return 'injection';
            } else if (result.hasEventHandlers) {
                return 'event_handler';
            } else if (result.hasTuningReferences) {
                return 'tuning';
            } else if (module.metadata.isPackageInit) {
                return 'package_init';
            } else {
                // Check for other patterns
                const content = module.content;
                
                // Check for service pattern
                if (/class\s+\w+\s*\(\s*Service\s*\)/.test(content)) {
                    return 'service';
                }
                
                // Check for UI pattern
                if (/ui\.ui_dialog/.test(content)) {
                    return 'ui';
                }
                
                // Check for utility pattern
                if (/def\s+\w+\s*\(/.test(content) && !result.hasInjections && !result.hasEventHandlers && !result.hasCommands) {
                    return 'utility';
                }
                
                // Check for interaction pattern
                if (/class\s+\w+\s*\(\s*\w*Interaction\s*\)/.test(content)) {
                    return 'interaction';
                }
                
                // Default to unknown
                return 'unknown';
            }
        } catch (error) {
            this.logger.error(`Error determining module type for module ${module.name}:`, error);
            return 'unknown';
        }
    }
}
