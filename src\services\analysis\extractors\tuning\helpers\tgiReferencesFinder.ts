import { XmlNode, XmlElement } from '../../../../../types/xml.js';
import { XmlElementNode } from '@s4tk/xml-dom';
import { DependencyInfo } from '../../../../../types/database.js';
import { parseTgi } from '../../../../../utils/parsing/tgiParser.js';
import { createDependency } from './dependencyCreator.js';
import { Logger } from '../../../../../utils/logging/logger.js';

const log = new Logger('TgiReferencesFinder');

type ReferenceType = 'TuningReference' | 'AttributeReference' | 'TElementReference';

interface TgiReference {
    type: number;
    group: number | bigint;
    instance: bigint;
    referenceType: ReferenceType;
}

/**
 * Attempts to parse a string as a TGI reference
 * @param value String to parse
 * @param referenceType Type of reference being parsed
 * @returns Parsed TGI reference or null if parsing fails
 */
function tryParseTgiReference(value: string, referenceType: ReferenceType): TgiReference | null {
    try {
        const parsed = parseTgi(value);
        if (parsed) {
            return {
                type: parsed.type,
                group: parsed.group,
                instance: parsed.instance,
                referenceType
            };
        }
    } catch (error) {
        log.debug(`Failed to parse TGI reference: ${error}`);
    }
    return null;
}

/**
 * Attempts to parse a string as an instance ID
 * @param value String to parse
 * @returns BigInt instance ID or null if parsing fails
 */
function tryParseInstanceId(value: string): bigint | null {
    const trimmed = value.trim();
    try {
        if (/^\d+$/.test(trimmed) || /^0x[0-9a-fA-F]+$/.test(trimmed)) {
            return BigInt(trimmed);
        }
    } catch (error) {
        log.debug(`Failed to parse instance ID: ${error}`);
    }
    return null;
}

/**
 * Processes attributes of an XML element for TGI references
 * @param attributes Element attributes
 * @param collection Collection to add dependencies to
 * @param sourceResourceId Source resource ID
 */
function processAttributes(
    attributes: Record<string, string | undefined>,
    collection: DependencyInfo[],
    sourceResourceId: number
): void {
    // Check for the 's' attribute first (standard TGI reference format)
    const tgiString = attributes['s'];
    if (tgiString) {
        const reference = tryParseTgiReference(tgiString, 'TuningReference');
        if (reference) {
            const dependency = createDependency(
                reference.type,
                reference.group,
                reference.instance,
                reference.referenceType
            );
            dependency.resourceId = sourceResourceId;
            collection.push(dependency);
        }
    }

    // Check other attributes
    for (const [attrName, attrValue] of Object.entries(attributes)) {
        if (attrName === 's' || !attrValue) continue;

        // Only check values that might be TGI references
        if (attrValue.includes('-') || attrValue.includes('0x')) {
            const reference = tryParseTgiReference(attrValue, 'AttributeReference');
            if (reference) {
                const dependency = createDependency(
                    reference.type,
                    reference.group,
                    reference.instance,
                    reference.referenceType
                );
                dependency.resourceId = sourceResourceId;
                collection.push(dependency);
            }
        }
    }
}

/**
 * Processes a T element's inner value for instance IDs
 * @param value Element inner value
 * @param collection Collection to add dependencies to
 * @param sourceResourceId Source resource ID
 */
function processTElementValue(
    value: string,
    collection: DependencyInfo[],
    sourceResourceId: number
): void {
    const instanceId = tryParseInstanceId(value);
    if (instanceId) {
        const dependency = createDependency(
            0, // Unknown type
            0, // Unknown group
            instanceId,
            'TElementReference'
        );
        dependency.resourceId = sourceResourceId;
        collection.push(dependency);
    }
}

/**
 * Recursively finds TGI references in XML nodes
 * @param node XML node to process
 * @param collection Collection to add dependencies to
 * @param sourceResourceId Source resource ID
 */
export function findTgiReferencesInNode(
    node: XmlNode,
    collection: DependencyInfo[],
    sourceResourceId: number
): void {
    try {
        // Skip non-element nodes
        if (node.type !== 'element') {
            return;
        }

        const element = node as XmlElement;

        // Process attributes for TGI references
        if (element.attributes) {
            processAttributes(element.attributes, collection, sourceResourceId);
        }

        // Process T element values for instance IDs
        if (element.tag === 'T' && element.innerValue && typeof element.innerValue === 'string') {
            processTElementValue(element.innerValue, collection, sourceResourceId);
        }

        // Recursively process child nodes
        if (element.children) {
            for (const child of element.children) {
                findTgiReferencesInNode(child, collection, sourceResourceId);
            }
        }
    } catch (error) {
        log.error('Error processing XML node for TGI references:', {
            error: error instanceof Error ? error.message : String(error),
            nodeType: node.type,
            tag: node.type === 'element' ? (node as XmlElement).tag : undefined,
            sourceResourceId
        });
    }
} 