/**
 * Python Bytecode Instruction Parser
 * 
 * This module provides functionality for parsing Python bytecode instructions.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { CodeObject, Instruction, PythonVersion } from './types.js';

// Create a logger for this module
const logger = new Logger('BytecodeInstructionParser');

/**
 * Python 3.7 opcodes
 */
export const PYTHON_3_7_OPCODES: Record<number, string> = {
    1: 'POP_TOP',
    2: 'ROT_TWO',
    3: 'ROT_THREE',
    4: 'DUP_TOP',
    5: 'DUP_TOP_TWO',
    9: 'NOP',
    10: 'UNARY_POSITIVE',
    11: 'UNARY_NEGATIVE',
    12: 'UNARY_NOT',
    15: 'UNARY_INVERT',
    16: 'BINARY_MATRIX_MULTIPLY',
    17: 'INPLACE_MATRIX_MULTIPLY',
    19: 'BINARY_POWER',
    20: 'BINARY_MULTIPLY',
    22: 'BINARY_MODULO',
    23: 'BINARY_ADD',
    24: 'BINARY_SUBTRACT',
    25: 'BINARY_SUBSCR',
    26: 'BINARY_FLOOR_DIVIDE',
    27: 'BINARY_TRUE_DIVIDE',
    28: 'INPLACE_FLOOR_DIVIDE',
    29: 'INPLACE_TRUE_DIVIDE',
    50: 'GET_AITER',
    51: 'GET_ANEXT',
    52: 'BEFORE_ASYNC_WITH',
    53: 'BEGIN_FINALLY',
    54: 'END_ASYNC_FOR',
    55: 'INPLACE_ADD',
    56: 'INPLACE_SUBTRACT',
    57: 'INPLACE_MULTIPLY',
    59: 'INPLACE_MODULO',
    60: 'STORE_SUBSCR',
    61: 'DELETE_SUBSCR',
    62: 'BINARY_LSHIFT',
    63: 'BINARY_RSHIFT',
    64: 'BINARY_AND',
    65: 'BINARY_XOR',
    66: 'BINARY_OR',
    67: 'INPLACE_POWER',
    68: 'GET_ITER',
    69: 'GET_YIELD_FROM_ITER',
    70: 'PRINT_EXPR',
    71: 'LOAD_BUILD_CLASS',
    72: 'YIELD_FROM',
    73: 'GET_AWAITABLE',
    75: 'INPLACE_LSHIFT',
    76: 'INPLACE_RSHIFT',
    77: 'INPLACE_AND',
    78: 'INPLACE_XOR',
    79: 'INPLACE_OR',
    81: 'WITH_CLEANUP_START',
    82: 'WITH_CLEANUP_FINISH',
    83: 'RETURN_VALUE',
    84: 'IMPORT_STAR',
    85: 'SETUP_ANNOTATIONS',
    86: 'YIELD_VALUE',
    87: 'POP_BLOCK',
    88: 'END_FINALLY',
    89: 'POP_EXCEPT',
    90: 'STORE_NAME',
    91: 'DELETE_NAME',
    92: 'UNPACK_SEQUENCE',
    93: 'FOR_ITER',
    94: 'UNPACK_EX',
    95: 'STORE_ATTR',
    96: 'DELETE_ATTR',
    97: 'STORE_GLOBAL',
    98: 'DELETE_GLOBAL',
    100: 'LOAD_CONST',
    101: 'LOAD_NAME',
    102: 'BUILD_TUPLE',
    103: 'BUILD_LIST',
    104: 'BUILD_SET',
    105: 'BUILD_MAP',
    106: 'LOAD_ATTR',
    107: 'COMPARE_OP',
    108: 'IMPORT_NAME',
    109: 'IMPORT_FROM',
    110: 'JUMP_FORWARD',
    111: 'JUMP_IF_FALSE_OR_POP',
    112: 'JUMP_IF_TRUE_OR_POP',
    113: 'JUMP_ABSOLUTE',
    114: 'POP_JUMP_IF_FALSE',
    115: 'POP_JUMP_IF_TRUE',
    116: 'LOAD_GLOBAL',
    122: 'SETUP_FINALLY',
    124: 'LOAD_FAST',
    125: 'STORE_FAST',
    126: 'DELETE_FAST',
    130: 'RAISE_VARARGS',
    131: 'CALL_FUNCTION',
    132: 'MAKE_FUNCTION',
    133: 'BUILD_SLICE',
    134: 'MAKE_CLOSURE',
    135: 'LOAD_CLOSURE',
    136: 'LOAD_DEREF',
    137: 'STORE_DEREF',
    138: 'DELETE_DEREF',
    141: 'CALL_FUNCTION_KW',
    142: 'CALL_FUNCTION_EX',
    143: 'SETUP_WITH',
    144: 'EXTENDED_ARG',
    145: 'LIST_APPEND',
    146: 'SET_ADD',
    147: 'MAP_ADD',
    148: 'LOAD_CLASSDEREF',
    149: 'BUILD_LIST_UNPACK',
    150: 'BUILD_MAP_UNPACK',
    151: 'BUILD_MAP_UNPACK_WITH_CALL',
    152: 'BUILD_TUPLE_UNPACK',
    153: 'BUILD_SET_UNPACK',
    154: 'SETUP_ASYNC_WITH',
    155: 'FORMAT_VALUE',
    156: 'BUILD_CONST_KEY_MAP',
    157: 'BUILD_STRING',
    158: 'BUILD_TUPLE_UNPACK_WITH_CALL',
    160: 'LOAD_METHOD',
    161: 'CALL_METHOD',
    162: 'CALL_FINALLY',
    163: 'POP_FINALLY'
};

/**
 * Python 3.8 opcodes
 */
export const PYTHON_3_8_OPCODES: Record<number, string> = {
    ...PYTHON_3_7_OPCODES,
    48: 'RERAISE',
    49: 'WITH_EXCEPT_START',
    121: 'SETUP_FINALLY',
    162: 'CALL_FINALLY',
    163: 'POP_FINALLY'
};

/**
 * Jump opcodes
 */
export const JUMP_OPCODES = [
    'JUMP_FORWARD',
    'JUMP_IF_FALSE_OR_POP',
    'JUMP_IF_TRUE_OR_POP',
    'JUMP_ABSOLUTE',
    'POP_JUMP_IF_FALSE',
    'POP_JUMP_IF_TRUE',
    'FOR_ITER'
];

/**
 * Gets the opcode name for a given opcode number and Python version
 * @param opcode Opcode number
 * @param pythonVersion Python version
 * @returns Opcode name or 'UNKNOWN'
 */
export function getOpcodeName(opcode: number, pythonVersion: PythonVersion): string {
    if (pythonVersion.major === 3) {
        if (pythonVersion.minor === 7) {
            return PYTHON_3_7_OPCODES[opcode] || 'UNKNOWN';
        } else if (pythonVersion.minor === 8) {
            return PYTHON_3_8_OPCODES[opcode] || 'UNKNOWN';
        }
    }
    
    return 'UNKNOWN';
}

/**
 * Checks if an opcode is a jump instruction
 * @param opname Opcode name
 * @returns True if the opcode is a jump instruction
 */
export function isJumpInstruction(opname: string): boolean {
    return JUMP_OPCODES.includes(opname);
}

/**
 * Parses Python bytecode instructions from a code object
 * @param codeObject Code object to parse instructions from
 * @param pythonVersion Python version
 * @returns Array of parsed instructions
 */
export function parseInstructions(codeObject: CodeObject, pythonVersion: PythonVersion): Instruction[] {
    const instructions: Instruction[] = [];
    const code = codeObject.code;
    let i = 0;
    let extendedArg = 0;
    
    try {
        while (i < code.length) {
            const offset = i;
            const opcode = code[i++];
            const opname = getOpcodeName(opcode, pythonVersion);
            
            // Get argument (if any)
            let arg: number | null = null;
            if (i < code.length && opcode >= 90) { // Opcodes >= 90 have arguments
                arg = code[i++];
                if (extendedArg > 0) {
                    arg = (extendedArg << 8) | arg;
                    extendedArg = 0;
                }
                
                if (opname === 'EXTENDED_ARG') {
                    extendedArg = arg;
                    continue;
                }
            }
            
            // Resolve argument value
            let argval: any = null;
            let argrepr = '';
            
            if (arg !== null) {
                switch (opname) {
                    case 'LOAD_CONST':
                        if (arg < codeObject.constants.length) {
                            argval = codeObject.constants[arg];
                            argrepr = String(argval);
                        }
                        break;
                    
                    case 'LOAD_NAME':
                    case 'STORE_NAME':
                    case 'DELETE_NAME':
                    case 'LOAD_GLOBAL':
                    case 'STORE_GLOBAL':
                    case 'DELETE_GLOBAL':
                        if (arg < codeObject.names.length) {
                            argval = codeObject.names[arg];
                            argrepr = argval;
                        }
                        break;
                    
                    case 'LOAD_ATTR':
                    case 'STORE_ATTR':
                    case 'DELETE_ATTR':
                    case 'LOAD_METHOD':
                        if (arg < codeObject.names.length) {
                            argval = codeObject.names[arg];
                            argrepr = argval;
                        }
                        break;
                    
                    case 'LOAD_FAST':
                    case 'STORE_FAST':
                    case 'DELETE_FAST':
                        if (arg < codeObject.varnames.length) {
                            argval = codeObject.varnames[arg];
                            argrepr = argval;
                        }
                        break;
                    
                    default:
                        argval = arg;
                        argrepr = String(arg);
                        break;
                }
            }
            
            // Check if this is a jump instruction
            const isJump = isJumpInstruction(opname);
            let jumpTarget: number | undefined;
            
            if (isJump && arg !== null) {
                if (opname === 'JUMP_FORWARD') {
                    jumpTarget = offset + 2 + arg;
                } else if (opname === 'FOR_ITER') {
                    jumpTarget = offset + 2 + arg;
                } else {
                    jumpTarget = arg;
                }
            }
            
            // Create instruction
            const instruction: Instruction = {
                offset,
                opcode,
                opname,
                arg,
                argval,
                argrepr,
                isJump,
                jumpTarget
            };
            
            instructions.push(instruction);
        }
    } catch (error: any) {
        logger.error(`Error parsing instructions: ${error.message || error}`);
    }
    
    return instructions;
}

/**
 * Extracts imports from instructions
 * @param instructions Array of instructions
 * @param codeObject Code object containing the instructions
 * @returns Array of import statements
 */
export function extractImportsFromInstructions(
    instructions: Instruction[], 
    codeObject: CodeObject
): string[] {
    const imports: string[] = [];
    
    try {
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            // Look for IMPORT_NAME instructions
            if (instruction.opname === 'IMPORT_NAME' && instruction.argval) {
                const moduleName = instruction.argval as string;
                
                // Check if the previous instruction is LOAD_CONST (fromlist)
                if (i > 0 && instructions[i - 1].opname === 'LOAD_CONST') {
                    const fromlist = instructions[i - 1].argval;
                    
                    if (Array.isArray(fromlist) && fromlist.length > 0) {
                        // This is a 'from ... import ...' statement
                        for (const name of fromlist) {
                            imports.push(`from ${moduleName} import ${name}`);
                        }
                    } else {
                        // This is a regular import statement
                        imports.push(`import ${moduleName}`);
                    }
                } else {
                    // Fallback: just record the module name
                    imports.push(`import ${moduleName}`);
                }
            }
        }
    } catch (error: any) {
        logger.error(`Error extracting imports from instructions: ${error.message || error}`);
    }
    
    return imports;
}

/**
 * Extracts class definitions from instructions
 * @param instructions Array of instructions
 * @param codeObject Code object containing the instructions
 * @returns Array of class names
 */
export function extractClassesFromInstructions(
    instructions: Instruction[], 
    codeObject: CodeObject
): string[] {
    const classes: string[] = [];
    
    try {
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            // Look for LOAD_BUILD_CLASS instructions
            if (instruction.opname === 'LOAD_BUILD_CLASS') {
                // The class name is typically stored in a STORE_NAME instruction after LOAD_BUILD_CLASS
                for (let j = i + 1; j < instructions.length && j < i + 10; j++) {
                    if (instructions[j].opname === 'STORE_NAME' && instructions[j].argval) {
                        classes.push(instructions[j].argval as string);
                        break;
                    }
                }
            }
        }
    } catch (error: any) {
        logger.error(`Error extracting classes from instructions: ${error.message || error}`);
    }
    
    return classes;
}

/**
 * Extracts function definitions from instructions
 * @param instructions Array of instructions
 * @param codeObject Code object containing the instructions
 * @returns Array of function names
 */
export function extractFunctionsFromInstructions(
    instructions: Instruction[], 
    codeObject: CodeObject
): string[] {
    const functions: string[] = [];
    
    try {
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            // Look for MAKE_FUNCTION instructions
            if (instruction.opname === 'MAKE_FUNCTION') {
                // The function name is typically stored in a STORE_NAME instruction after MAKE_FUNCTION
                for (let j = i + 1; j < instructions.length && j < i + 5; j++) {
                    if ((instructions[j].opname === 'STORE_NAME' || 
                         instructions[j].opname === 'STORE_FAST') && 
                        instructions[j].argval) {
                        functions.push(instructions[j].argval as string);
                        break;
                    }
                }
            }
        }
    } catch (error: any) {
        logger.error(`Error extracting functions from instructions: ${error.message || error}`);
    }
    
    return functions;
}
