﻿/**
 * Types for machine learning models and features
 */

import { ConflictType, ConflictSeverity } from '../conflict/ConflictTypes.js';
import * as tf from '@tensorflow/tfjs';

export interface MLModelConfig {
  // Model paths
  modelPath: string;
  vocabularyPath?: string;

  // Model architecture
  inputSize: number;
  hiddenLayers: number[];
  outputSize: number;
  embeddingDim: number;
  dropoutRate: number;
  l2Lambda: number;

  // Training parameters
  epochs: number;
  batchSize: number;
  validationSplit: number;
  learningRate: number;
  earlyStoppingPatience: number;
  minDelta: number;
  threshold: number;

  // Model configuration
  optimizer: string;
  loss: string;
  metrics: string[];
  
  // Data configuration
  vocabulary: string[];
  vocabularySize: number;
  maxSequenceLength: number;
  featureWeights: Record<string, number>;

  // Learning rate schedule
  learningRateSchedule?: {
    initialRate: number;
    decaySteps: number;
    decayRate: number;
    staircase: boolean;
  };

  // Data augmentation and balancing
  useClassWeights?: boolean;
  useMixup?: boolean;
  mixupAlpha?: number;
}

export interface TrainingConfig extends MLModelConfig {
  epochs: number;
  batchSize: number;
  validationSplit: number;
  learningRate: number;
  earlyStoppingPatience: number;
  minDelta: number;
  inputSize: number;
  hiddenLayers: number[];
  outputSize: number;
  optimizer: string;
  loss: string;
  metrics: string[];
  embeddingDim: number;
  vocabularySize: number;
}

export interface ModFeatures {
  name: string;
  description?: string;
  version?: string;
  creator?: string;
  resourceTypes?: number[];
  resourceFeatures?: {
    isScript?: boolean;
    isPackage?: boolean;
    resourceType?: string;
    hasDependencies?: boolean;
  };
  creatorInfo?: CreatorInfo;
  versionInfo?: VersionInfo;
  normalized?: tf.Tensor;
  path?: string;
  source?: string;
  semanticFeatures?: {
    topics?: string[];
    sentiment?: number;
    complexity?: number;
  };
  structuralFeatures?: {
    depth?: number;
    breadth?: number;
    cyclomaticComplexity?: number;
    fileSize?: number;
    fileType?: string;
  };
  metadataFeatures?: {
    hasReadme?: boolean;
    hasLicense?: boolean;
    hasChangelog?: boolean;
    hasDocumentation?: boolean;
  };
}

export interface CreatorInfo {
  name: string;
  patterns: string[];
  confidence: number;
  knownCreator?: boolean;
}

export interface VersionInfo {
  version: string;
  type: string;
  major: number;
  minor: number;
  patch?: number;
  suffix?: string;
  isPreRelease?: boolean;
  isAlpha?: boolean;
  isBeta?: boolean;
  buildNumber?: number;
  confidence?: number;
}

export interface ExtendedModFeatures extends ModFeatures {
  // Additional features specific to extended analysis
  semanticFeatures?: {
    topics?: string[];
    sentiment?: number;
    complexity?: number;
  };
  structuralFeatures?: {
    depth?: number;
    breadth?: number;
    cyclomaticComplexity?: number;
  };
  metadataFeatures?: {
    hasReadme?: boolean;
    hasLicense?: boolean;
    hasChangelog?: boolean;
  };
}

export interface MLConflictPrediction {
  type: string;
  severity: string;
  confidence: number;
  details: Record<string, unknown>;
}

export interface MLPerformanceMetrics {
  duration: number;
  memoryUsage: number;
  component: string;
  success: boolean;
  input: Record<string, unknown>;
  output: {
    conflictCount: number;
    resourceCount: number;
    detectionTime: number;
  };
  error?: string;
}

export interface MLFeatureVector {
  features: number[];
  labels: number[];
  weights: number[];
  metadata: Record<string, unknown>;
}

export interface MLPredictionResult<T> {
  value: T;
  confidence: number;
  metadata: Record<string, unknown>;
}

export interface MLTrainingResult {
  model: unknown;
  history: Record<string, number[]>;
  metrics: Record<string, number>;
  validationMetrics: Record<string, number>;
  trainingTime: number;
  epochs: number;
  batchSize: number;
  learningRate: number;
  loss: number;
  accuracy: number;
  validationLoss: number;
  validationAccuracy: number;
  bestEpoch: number;
  bestModel: unknown;
  bestMetrics: Record<string, number>;
  bestValidationMetrics: Record<string, number>;
  bestTrainingTime: number;
  bestEpochs: number;
  bestBatchSize: number;
  bestLearningRate: number;
  bestLoss: number;
  bestAccuracy: number;
  bestValidationLoss: number;
  bestValidationAccuracy: number;
  modelPath: string;
  checkpointPath: string;
  tensorboardPath: string;
  loggingPath: string;
  customResults: Record<string, unknown>;
}

export interface MLValidationResult {
  model: unknown;
  metrics: Record<string, number>;
  validationTime: number;
  batchSize: number;
  loss: number;
  accuracy: number;
  confusionMatrix: number[][];
  classificationReport: Record<string, unknown>;
  rocCurve: Record<string, unknown>;
  precisionRecallCurve: Record<string, unknown>;
  customResults: Record<string, unknown>;
}

export interface MLModelState {
  isInitialized: boolean;
  isTraining: boolean;
  isEvaluating: boolean;
  isPredicting: boolean;
  lastTrainingTime: number;
  lastEvaluationTime: number;
  lastPredictionTime: number;
  totalTrainingTime: number;
  totalEvaluationTime: number;
  totalPredictionTime: number;
  totalTrainingSamples: number;
  totalEvaluationSamples: number;
  totalPredictionSamples: number;
  averageTrainingTime: number;
  averageEvaluationTime: number;
  averagePredictionTime: number;
  trainingMetrics: Record<string, number>;
  evaluationMetrics: Record<string, number>;
  predictionMetrics: Record<string, number>;
  customState: Record<string, unknown>;
}

// Aliases for backward compatibility
export type CreatorPrediction = MLPredictionResult<CreatorInfo>;
export type MLPrediction<T> = MLPredictionResult<T>; 
