/**
 * Safe Buffer Reader for TS4Script
 * 
 * Re-exports the unified BufferReader with TS4Script-specific defaults
 */

import { <PERSON><PERSON><PERSON><PERSON>eader, BufferReadOptions } from '../../../../utils/buffer/bufferReader.js';

/**
 * TS4Script-specific buffer reader that extends the unified BufferReader
 * with defaults suitable for bytecode parsing
 */
export class TS4ScriptBuffer<PERSON>eader extends BufferReader {
    /**
     * Creates a new TS4ScriptBufferReader
     * @param buffer Buffer to read from
     * @param initialPosition Initial position (default: 0)
     * @param options Reading options
     */
    constructor(buffer: Buffer, initialPosition: number = 0, options: BufferReadOptions = {}) {
        super(buffer, initialPosition, {
            throwOnBufferOverflow: false, // TS4Script parsing should be lenient by default
            ...options
        });
    }

    /**
     * Reads a Python bytecode magic number (first 4 bytes)
     * @returns Magic number as UInt32LE or null if failed
     */
    public readMagicNumber(): number | null {
        return this.readUInt32LE('magic_number');
    }

    /**
     * Reads a Python bytecode timestamp (next 4 bytes after magic)
     * @returns Timestamp as UInt32LE or null if failed
     */
    public readTimestamp(): number | null {
        return this.readUInt32LE('timestamp');
    }

    /**
     * Reads bytecode version information
     * @returns Object with magic and timestamp or null if failed
     */
    public readBytecodeHeader(): { magic: number; timestamp: number } | null {
        const magic = this.readMagicNumber();
        const timestamp = this.readTimestamp();
        
        if (magic === null || timestamp === null) {
            return null;
        }
        
        return { magic, timestamp };
    }

    /**
     * Reads marshalled Python object data
     * @param length Length of the marshalled data
     * @returns Marshalled data buffer or null if failed
     */
    public readMarshalledData(length?: number): Buffer | null {
        const dataLength = length || this.remainingBytes();
        return this.readSlice(dataLength, 'marshalled_data');
    }

    /**
     * Legacy method aliases for backward compatibility
     */
    public remaining(): number {
        return this.remainingBytes();
    }
}

// Backward compatibility exports
export { TS4ScriptBufferReader as SafeBufferReader };

/**
 * Create a SafeBufferReader instance (legacy factory function)
 * @param buffer Buffer to read from
 * @param startPosition Starting position
 * @returns SafeBufferReader instance
 */
export function createSafeBufferReader(buffer: Buffer, startPosition: number = 0): TS4ScriptBufferReader {
    return new TS4ScriptBufferReader(buffer, startPosition);
}
