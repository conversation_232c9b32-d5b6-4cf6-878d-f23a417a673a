/**
 * Dependency Graph Visualizer
 * 
 * This class extends the DependencyGraphAnalyzer with visualization capabilities.
 * It provides methods for visualizing the dependency graph, such as generating
 * DOT and JSON representations.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { EnhancedMemoryManager } from '../../../../utils/memory/enhancedMemoryManager.js';
import { DependencyGraphAnalyzer } from './dependencyGraphAnalyzer.js';
import { 
  GraphVisualizationOptions,
  JsonGraph,
  JsonGraphNode,
  JsonGraphLink
} from './types.js';
import { injectable, singleton } from '../../../di/decorators.js';

/**
 * DependencyGraphVisualizer class
 * 
 * This class extends the DependencyGraphAnalyzer with visualization capabilities.
 * It provides methods for visualizing the dependency graph, such as generating
 * DOT and JSON representations.
 */
@injectable()
@singleton()
export class DependencyGraphVisualizer extends DependencyGraphAnalyzer {
  /**
   * Constructor
   * @param databaseService Database service
   * @param memoryManager Memory manager
   */
  constructor(
    databaseService: DatabaseService,
    memoryManager?: EnhancedMemoryManager
  ) {
    super(databaseService, memoryManager);
  }
  
  /**
   * Generate a DOT graph representation of the dependency graph
   * @param options Visualization options
   * @returns DOT graph string
   */
  public generateDotGraph(options: GraphVisualizationOptions = {}): string {
    const logger = new Logger('DependencyGraphVisualizer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return 'digraph G {}';
    }
    
    const maxNodes = options.maxNodes || 100;
    const includeMetadata = options.includeMetadata !== false;
    const includeDependencyMetadata = options.includeDependencyMetadata !== false;
    
    let dot = 'digraph G {\n';
    
    // Add graph attributes
    dot += '  graph [rankdir="LR", fontname="Arial", fontsize="12", overlap="false", splines="true"];\n';
    dot += '  node [shape="box", style="rounded,filled", fontname="Arial", fontsize="10", fillcolor="lightblue"];\n';
    dot += '  edge [fontname="Arial", fontsize="8"];\n\n';
    
    // Get resource IDs
    const resourceIds = this.getResourceIds();
    
    // Limit the number of nodes if necessary
    const limitedResourceIds = resourceIds.length > maxNodes ?
      resourceIds.slice(0, maxNodes) : resourceIds;
    
    // Add nodes
    for (const resourceId of limitedResourceIds) {
      const metadata = this.getResourceMetadata(resourceId);
      let label = resourceId;
      
      if (includeMetadata && metadata) {
        label = `${resourceId}\\n${metadata.name}\\nType: ${metadata.type.toString(16)}\\nGroup: ${metadata.group.toString(16)}\\nInstance: ${metadata.instance.toString(16)}`;
      }
      
      dot += `  "${resourceId}" [label="${label}"];\n`;
    }
    
    dot += '\n';
    
    // Add edges
    for (const resourceId of limitedResourceIds) {
      const dependencies = this.getDependencies(resourceId);
      
      for (const dependencyId of dependencies) {
        // Skip dependencies to resources not in the limited set
        if (!limitedResourceIds.includes(dependencyId)) {
          continue;
        }
        
        let label = '';
        
        if (includeDependencyMetadata) {
          const metadata = this.getDependencyMetadata(resourceId, dependencyId);
          if (metadata) {
            label = `${metadata.referenceType} (${metadata.strength})`;
          }
        }
        
        dot += `  "${resourceId}" -> "${dependencyId}"${label ? ` [label="${label}"]` : ''};\n`;
      }
    }
    
    // If we limited the number of nodes, add a note
    if (resourceIds.length > maxNodes) {
      dot += '\n';
      dot += `  "note" [shape="note", label="Graph limited to ${maxNodes} nodes out of ${resourceIds.length} total", fillcolor="lightyellow"];\n`;
    }
    
    dot += '}';
    
    return dot;
  }
  
  /**
   * Generate a JSON representation of the dependency graph
   * @param options Visualization options
   * @returns JSON object
   */
  public generateJsonGraph(options: GraphVisualizationOptions = {}): JsonGraph {
    const logger = new Logger('DependencyGraphVisualizer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return { nodes: [], links: [] };
    }
    
    const maxNodes = options.maxNodes || 100;
    const includeMetadata = options.includeMetadata !== false;
    const includeDependencyMetadata = options.includeDependencyMetadata !== false;
    
    const nodes: JsonGraphNode[] = [];
    const links: JsonGraphLink[] = [];
    
    // Get resource IDs
    const resourceIds = this.getResourceIds();
    
    // Limit the number of nodes if necessary
    const limitedResourceIds = resourceIds.length > maxNodes ?
      resourceIds.slice(0, maxNodes) : resourceIds;
    
    // Add nodes
    for (const resourceId of limitedResourceIds) {
      const metadata = this.getResourceMetadata(resourceId);
      
      const node: JsonGraphNode = {
        id: resourceId,
        name: metadata ? metadata.name : 'Unknown',
        type: metadata ? metadata.type : 0,
        group: metadata ? metadata.group.toString(16) : '0',
        instance: metadata ? metadata.instance.toString(16) : '0'
      };
      
      // Add additional metadata if requested
      if (includeMetadata && metadata) {
        Object.assign(node, {
          resourceType: metadata.name,
          dependencies: this.getDependencies(resourceId).size,
          dependents: this.getDependents(resourceId).size
        });
      }
      
      nodes.push(node);
    }
    
    // Add links
    for (const resourceId of limitedResourceIds) {
      const dependencies = this.getDependencies(resourceId);
      
      for (const dependencyId of dependencies) {
        // Skip dependencies to resources not in the limited set
        if (!limitedResourceIds.includes(dependencyId)) {
          continue;
        }
        
        const link: JsonGraphLink = {
          source: resourceId,
          target: dependencyId,
          type: 'dependency',
          strength: 1
        };
        
        // Add dependency metadata if requested
        if (includeDependencyMetadata) {
          const metadata = this.getDependencyMetadata(resourceId, dependencyId);
          if (metadata) {
            link.type = metadata.referenceType;
            link.strength = metadata.strength / 100; // Normalize to 0-1
            
            // Add additional metadata
            Object.assign(link, {
              referenceType: metadata.referenceType,
              timestamp: metadata.timestamp
            });
          }
        }
        
        links.push(link);
      }
    }
    
    return { nodes, links };
  }
  
  /**
   * Serialize the dependency graph to a JSON string
   * @returns JSON string representation of the graph
   */
  public serializeGraph(): string {
    const logger = new Logger('DependencyGraphVisualizer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return '{}';
    }
    
    // Convert Maps and Sets to arrays for serialization
    const serializedGraph = {
      dependencyGraph: Array.from(this.getDependencyGraphEntries()).map(([key, value]) => [key, Array.from(value)]),
      reverseDependencyGraph: Array.from(this.getReverseDependencyGraphEntries()).map(([key, value]) => [key, Array.from(value)]),
      resourceMetadata: Array.from(this.getResourceMetadataEntries()),
      dependencyMetadata: Array.from(this.getDependencyMetadataEntries()),
      graphBuilt: this.isGraphBuilt()
    };
    
    return JSON.stringify(serializedGraph);
  }
  
  /**
   * Deserialize a JSON string to a dependency graph
   * @param json JSON string representation of the graph
   */
  public deserializeGraph(json: string): void {
    const logger = new Logger('DependencyGraphVisualizer');
    
    try {
      const serializedGraph = JSON.parse(json);
      
      // Clear existing graph
      this.clear();
      
      // Reconstruct Maps and Sets from arrays
      this.setDependencyGraph(new Map(
        serializedGraph.dependencyGraph.map(([key, value]: [string, string[]]) => [key, new Set(value)])
      ));
      
      this.setReverseDependencyGraph(new Map(
        serializedGraph.reverseDependencyGraph.map(([key, value]: [string, string[]]) => [key, new Set(value)])
      ));
      
      this.setResourceMetadata(new Map(serializedGraph.resourceMetadata));
      this.setDependencyMetadata(new Map(serializedGraph.dependencyMetadata));
      this.setGraphBuilt(serializedGraph.graphBuilt);
      
      logger.info('Dependency graph deserialized successfully');
    } catch (error) {
      logger.error('Error deserializing dependency graph:', error);
      throw error;
    }
  }
  
  /**
   * Export the dependency graph to a file
   * @param format Export format ('dot', 'json', or 'serialized')
   * @param options Visualization options
   * @returns Content to be written to a file
   */
  public exportGraph(format: 'dot' | 'json' | 'serialized', options: GraphVisualizationOptions = {}): string {
    const logger = new Logger('DependencyGraphVisualizer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return '';
    }
    
    switch (format) {
      case 'dot':
        return this.generateDotGraph(options);
      case 'json':
        return JSON.stringify(this.generateJsonGraph(options), null, 2);
      case 'serialized':
        return this.serializeGraph();
      default:
        logger.error(`Unsupported export format: ${format}`);
        return '';
    }
  }
}
