/**
 * Enhanced Conflict Detector
 *
 * This module provides an enhanced conflict detector that combines the rule-based
 * MultiLevelConflictDetector with the LLM-based conflict detection approach.
 * It extends the HybridConflictDetector to maintain compatibility with the existing system.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../types/database.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/index.js';
import { HybridConflictDetector } from '../HybridConflictDetector.js';
import { MultiLevelConflictDetector } from './MultiLevelConflictDetector.js';
import { DatabaseService } from '../../databaseService.js';

// Create a logger for this module
const logger = new Logger('EnhancedConflictDetector');

/**
 * Enhanced conflict detector options
 */
export interface EnhancedConflictDetectorOptions {
    similarityThreshold?: number;
    maxContentSize?: number;
    excludeTypes?: number[];
    includeTypes?: number[];
    enableSignatureFiltering?: boolean;
    enableMetadataFiltering?: boolean;
    enableDeepComparison?: boolean;
    enableLLMDetection?: boolean;
    llmConfidenceThreshold?: number;
}

/**
 * Enhanced Conflict Detector class
 * Extends HybridConflictDetector to maintain compatibility with the existing system
 * but uses MultiLevelConflictDetector for rule-based detection
 */
export class EnhancedConflictDetector extends HybridConflictDetector {
    private multiLevelDetector: MultiLevelConflictDetector;
    private options: EnhancedConflictDetectorOptions;

    /**
     * Create a new enhanced conflict detector
     * @param databaseService Database service
     * @param options Conflict detector options
     * @param logger Logger instance
     */
    constructor(
        databaseService: DatabaseService,
        options: EnhancedConflictDetectorOptions = {},
        logger?: Logger
    ) {
        super(databaseService, logger || new Logger('EnhancedConflictDetector'));

        this.options = {
            similarityThreshold: options.similarityThreshold || 0.8,
            maxContentSize: options.maxContentSize || 1048576, // 1 MB
            excludeTypes: options.excludeTypes || [],
            includeTypes: options.includeTypes || [],
            enableSignatureFiltering: options.enableSignatureFiltering !== false,
            enableMetadataFiltering: options.enableMetadataFiltering !== false,
            enableDeepComparison: options.enableDeepComparison !== false,
            enableLLMDetection: options.enableLLMDetection !== false,
            llmConfidenceThreshold: options.llmConfidenceThreshold || 0.7
        };

        // Create multi-level detector
        this.multiLevelDetector = new MultiLevelConflictDetector(
            databaseService,
            {
                similarityThreshold: this.options.similarityThreshold,
                maxContentSize: this.options.maxContentSize,
                excludeTypes: this.options.excludeTypes,
                includeTypes: this.options.includeTypes,
                enableSignatureFiltering: this.options.enableSignatureFiltering,
                enableMetadataFiltering: this.options.enableMetadataFiltering,
                enableDeepComparison: this.options.enableDeepComparison
            },
            this.logger
        );

        this.logger.info('Enhanced conflict detector created');
    }

    /**
     * Initialize the conflict detector
     * @returns Promise resolving when initialization is complete
     */
    public async initialize(): Promise<void> {
        try {
            this.logger.info('Initializing enhanced conflict detector');

            // Initialize multi-level detector
            await this.multiLevelDetector.initialize();

            // Initialize parent class
            await super.initialize();

            this.logger.info('Enhanced conflict detector initialized');
        } catch (error: any) {
            this.logger.error(`Error initializing enhanced conflict detector: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Detect conflicts between resources
     * @param resources List of resources to check for conflicts
     * @returns Promise resolving to a list of detected conflicts
     */
    public async detectConflicts(resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        try {
            this.logger.info(`Detecting conflicts between ${resources.length} resources using enhanced conflict detector`);

            // Use multi-level detector for rule-based detection
            const ruleBased = await this.multiLevelDetector.detectConflicts(resources);
            this.logger.info(`Multi-level detector found ${ruleBased.length} conflicts`);

            // Use LLM-based detection if enabled
            let llmBased: ConflictInfo[] = [];
            if (this.options.enableLLMDetection) {
                // Use parent class for LLM-based detection
                llmBased = await super.detectConflicts(resources);
                this.logger.info(`LLM-based detector found ${llmBased.length} conflicts`);

                // Filter LLM-based conflicts by confidence threshold
                llmBased = llmBased.filter(conflict =>
                    (conflict.confidence || 0) >= this.options.llmConfidenceThreshold!
                );
                this.logger.info(`After confidence filtering, ${llmBased.length} LLM-based conflicts remain`);
            }

            // Combine results, removing duplicates
            const combined = this.combineConflicts(ruleBased, llmBased);
            this.logger.info(`Combined ${ruleBased.length} rule-based and ${llmBased.length} LLM-based conflicts into ${combined.length} unique conflicts`);

            return combined;
        } catch (error: any) {
            this.logger.error(`Error detecting conflicts: ${error.message || error}`);
            return [];
        }
    }

    /**
     * Combine conflicts from multiple sources, removing duplicates
     * @param ruleBased Rule-based conflicts
     * @param llmBased LLM-based conflicts
     * @returns Combined list of conflicts
     * @private
     */
    private combineConflicts(ruleBased: ConflictInfo[], llmBased: ConflictInfo[]): ConflictInfo[] {
        // Create a map to track conflicts by affected resources
        const conflictMap = new Map<string, ConflictInfo>();

        // Add rule-based conflicts to map
        for (const conflict of ruleBased) {
            const key = this.getConflictKey(conflict);
            conflictMap.set(key, conflict);
        }

        // Add LLM-based conflicts to map, merging with existing conflicts if needed
        for (const conflict of llmBased) {
            const key = this.getConflictKey(conflict);

            if (conflictMap.has(key)) {
                // Merge with existing conflict
                const existing = conflictMap.get(key)!;

                // Use the higher severity
                const severity = Math.max(existing.severity, conflict.severity);

                // Use the higher confidence
                const confidence = Math.max(existing.confidence || 0, conflict.confidence || 0);

                // Combine descriptions
                const description = `${existing.description}\n\nLLM Analysis: ${conflict.description}`;

                // Combine recommendations
                const recommendations = [...new Set([...existing.recommendations, ...conflict.recommendations])];

                // Create merged conflict
                const merged: ConflictInfo = {
                    ...existing,
                    severity,
                    description,
                    recommendations,
                    confidence,
                    metadata: {
                        ...existing.metadata,
                        llmAnalysis: conflict.metadata
                    }
                };

                conflictMap.set(key, merged);
            } else {
                // Add new conflict
                conflictMap.set(key, conflict);
            }
        }

        // Convert map back to array
        return Array.from(conflictMap.values());
    }

    /**
     * Get a unique key for a conflict based on affected resources
     * @param conflict Conflict to get key for
     * @returns Unique key
     * @private
     */
    private getConflictKey(conflict: ConflictInfo): string {
        // Sort affected resources to ensure consistent key
        const resources = [...conflict.affectedResources].sort((a, b) => {
            if (a.type !== b.type) return a.type - b.type;
            if (a.group !== b.group) return Number(a.group - b.group);
            return Number(a.instance - b.instance);
        });

        // Create key from sorted resources
        return resources.map(r => `${r.type}:${r.group}:${r.instance}`).join('|');
    }
}
