/**
 * Index file for SimData version handlers
 */

export { createStandardVersionHandler, getStandardVersionHandlers } from './standardVersionHandler.js';
export { handleVersion16708, handleVersion48111, getSpecialVersionHandlers } from './specialVersionHandler.js';
export { createModVersionHandler, getModVersionHandlers } from './modVersionHandler.js';
export { createExperimentalVersionHandler, getExperimentalVersionHandlers } from './experimentalVersionHandler.js';
export { handleGenericVersion, createGenericVersionHandler } from './genericVersionHandler.js';

import { VersionHandlerFunction } from '../types.js';
import { getStandardVersionHandlers } from './standardVersionHandler.js';
import { getSpecialVersionHandlers } from './specialVersionHandler.js';
import { getModVersionHandlers } from './modVersionHandler.js';
import { getExperimentalVersionHandlers } from './experimentalVersionHandler.js';

/**
 * Get all version handlers
 * @returns Map of version numbers to handler functions
 */
export function getAllVersionHandlers(): Map<number, VersionHandlerFunction> {
    const handlers = new Map<number, VersionHandlerFunction>();
    
    // Add standard version handlers
    for (const [version, handler] of getStandardVersionHandlers()) {
        handlers.set(version, handler);
    }
    
    // Add special version handlers
    for (const [version, handler] of getSpecialVersionHandlers()) {
        handlers.set(version, handler);
    }
    
    // Add mod version handlers
    for (const [version, handler] of getModVersionHandlers()) {
        handlers.set(version, handler);
    }
    
    // Add experimental version handlers
    for (const [version, handler] of getExperimentalVersionHandlers()) {
        handlers.set(version, handler);
    }
    
    return handlers;
}
