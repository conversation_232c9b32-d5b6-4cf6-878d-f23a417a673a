import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey as AppResourceKey } from '../../../types/resource/interfaces.js';
import { ResourceMetadata } from '../../../types/resource/interfaces.js';

const log = new Logger('LightMetadataExtractor');

/**
 * Extracts metadata from Light resources.
 * Light resources define lighting properties for objects.
 * 
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object with light information.
 */
export async function extractLightMetadata(
    key: AppResource<PERSON>ey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};
    
    try {
        // Verify this is a Light resource
        if (key.type !== 0x03B4C61D) {
            log.warn(`Resource ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)} is not a Light resource`);
            return {
                contentSnippet: `[Not a Light resource: ${key.type.toString(16)}]`,
                extractorUsed: 'light'
            };
        }
        
        log.info(`Extracting metadata from Light resource: ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)}`);
        
        // Light resources have a specific binary format
        // First 4 bytes: Version (uint32)
        const version = buffer.readUInt32LE(0);
        extractedMetadata.lightVersion = version;
        
        // Next 4 bytes: Flags (uint32)
        const flags = buffer.readUInt32LE(4);
        extractedMetadata.lightFlags = flags;
        
        // Determine light properties based on flags
        // TODO: Research specific flag bits for more detailed properties (e.g., light type, shadow casting)
        const isElectric = (flags & 0x01) !== 0;
        const isManual = (flags & 0x02) !== 0;
        const hasVisualEffect = (flags & 0x04) !== 0;
        
        extractedMetadata.lightIsElectric = isElectric;
        extractedMetadata.lightIsManual = isManual;
        extractedMetadata.lightHasVisualEffect = hasVisualEffect;
        
        // Extract light intensity information if available
        if (buffer.length >= 12) {
            // Bytes 8-11: Default dimmer value (float)
            const defaultDimmerValue = buffer.readFloatLE(8);
            extractedMetadata.lightDefaultDimmerValue = defaultDimmerValue;
        }
        
        // Extract light color information if available
        if (buffer.length >= 16) {
            // Bytes 12-15: Color (uint32 RGBA)
            const colorValue = buffer.readUInt32LE(12);
            const r = (colorValue & 0xFF);
            const g = ((colorValue >> 8) & 0xFF);
            const b = ((colorValue >> 16) & 0xFF);
            const a = ((colorValue >> 24) & 0xFF);
            
            extractedMetadata.lightColor = `rgba(${r}, ${g}, ${b}, ${a})`;
        }
        
        // Extract material states if available
        if (buffer.length >= 24) {
            // Bytes 16-19: Material state on hash (uint32)
            // Bytes 20-23: Material state off hash (uint32)
            const materialStateOnHash = buffer.readUInt32LE(16);
            const materialStateOffHash = buffer.readUInt32LE(20);
            
            if (materialStateOnHash !== 0) {
                extractedMetadata.lightMaterialStateOn = `0x${materialStateOnHash.toString(16).padStart(8, '0')}`;
            }
            
            if (materialStateOffHash !== 0) {
                extractedMetadata.lightMaterialStateOff = `0x${materialStateOffHash.toString(16).padStart(8, '0')}`;
            }
        }
        
        // Extract visual effect reference if available
        if (hasVisualEffect && buffer.length >= 36) {
            // Bytes 24-27: Visual effect type (uint32)
            // Bytes 28-31: Visual effect group (uint32)
            // Bytes 32-35: Visual effect instance (uint32)
            const effectType = buffer.readUInt32LE(24);
            const effectGroup = buffer.readUInt32LE(28);
            const effectInstance = buffer.readUInt32LE(32);
            
            if (effectType !== 0 || effectGroup !== 0 || effectInstance !== 0) {
                extractedMetadata.lightVisualEffect = `${effectType.toString(16)}:${effectGroup.toString(16)}:${effectInstance.toString(16)}`;
            }
        }
        
        // TODO: Research and implement extraction of other potential light properties
        // e.g., light type (point, directional, spot), intensity, attenuation, shadow properties, color temperature
        
        // Create a content snippet
        let contentSnippet = `Light v${version}`;
        if (isElectric) {
            contentSnippet += ', Electric';
        } else {
            contentSnippet += ', Non-Electric';
        }
        
        if (isManual) {
            contentSnippet += ', Manual';
        } else {
            contentSnippet += ', Automatic';
        }
        
        if (hasVisualEffect) {
            contentSnippet += ', Has Visual Effect';
        }
        
        if (extractedMetadata.lightDefaultDimmerValue !== undefined) {
            contentSnippet += `, Dimmer: ${extractedMetadata.lightDefaultDimmerValue.toFixed(2)}`;
        }
        
        // Add other extracted properties to the snippet as they are implemented
        if (extractedMetadata.lightColor) {
            contentSnippet += `, Color: ${extractedMetadata.lightColor}`;
        }
        if (extractedMetadata.lightMaterialStateOn) {
            contentSnippet += `, Material On: ${extractedMetadata.lightMaterialStateOn}`;
        }
        if (extractedMetadata.lightMaterialStateOff) {
            contentSnippet += `, Material Off: ${extractedMetadata.lightMaterialStateOff}`;
        }
        if (extractedMetadata.lightVisualEffect) {
            contentSnippet += `, VFX: ${extractedMetadata.lightVisualEffect}`;
        }
        
        extractedMetadata.contentSnippet = contentSnippet;
        extractedMetadata.extractorUsed = 'light';
        
        return extractedMetadata;
    } catch (error) {
        log.error(`Error extracting Light metadata: ${error}`);
        return {
            contentSnippet: `[Error extracting Light metadata: ${error}]`,
            extractorUsed: 'light',
            extractionError: String(error)
        };
    }
}
