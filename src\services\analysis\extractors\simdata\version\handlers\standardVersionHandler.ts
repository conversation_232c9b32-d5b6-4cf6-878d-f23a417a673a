/**
 * Handler for standard SimData versions (1-20)
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { ParsedSimData, VersionHandlerFunction } from '../types.js';
import { parseStandardVersion } from '../../parsers/standardVersionParser.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('StandardVersionHandler');

/**
 * Creates a handler function for a standard SimData version
 * @param version SimData version (1-20)
 * @returns Handler function for the specified version
 */
export function createStandardVersionHandler(version: number): VersionHandlerFunction {
    if (version < 1 || version > 20) {
        throw new Error(`Invalid standard version: ${version}. Standard versions must be between 1 and 20.`);
    }

    return (buffer: Buffer): ParsedSimData | undefined => {
        try {
            logger.info(`Handling standard SimData version ${version}`);
            return parseStandardVersion(buffer, version);
        } catch (error) {
            return handleVersionError(
                error,
                createVersionErrorContext(version, 'standardVersionHandler', { bufferLength: buffer.length }),
                undefined
            );
        }
    };
}

/**
 * Get all standard version handlers (1-20)
 * @returns Map of version numbers to handler functions
 */
export function getStandardVersionHandlers(): Map<number, VersionHandlerFunction> {
    const handlers = new Map<number, VersionHandlerFunction>();
    
    // Register handlers for versions 1-20
    for (let version = 1; version <= 20; version++) {
        handlers.set(version, createStandardVersionHandler(version));
    }
    
    return handlers;
}
