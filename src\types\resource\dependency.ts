/**
 * Dependency-related interfaces for resources
 */

import { ResourceKey } from './interfaces.js';

/**
 * Extended resource key interface for dependency analysis
 */
export interface DependencyResourceKey extends ResourceKey {
    /** Reference type for dependency analysis */
    referenceType?: string;
    /** Context where the reference was found */
    context?: string;
    /** Confidence level of the reference (0-100) */
    confidence?: number;
    /** Additional metadata for the reference */
    metadata?: Record<string, any>;
}

/**
 * Dependency information structure
 */
export interface DependencyInfo {
    /** Source resource ID */
    resourceId: number;
    /** Target resource type */
    targetType: number;
    /** Target resource group */
    targetGroup: bigint;
    /** Target resource instance */
    targetInstance: bigint;
    /** Type of dependency reference */
    referenceType?: string;
    /** Context where dependency was found */
    context?: string;
    /** Confidence level of dependency (0-100) */
    confidence?: number;
    /** Additional metadata */
    metadata?: Record<string, any>;
    /** Timestamp when dependency was recorded */
    timestamp?: number;
}

/**
 * Metadata information structure
 */
export interface MetadataInfo {
    /** Resource ID this metadata belongs to */
    resourceId: number;
    /** Metadata key */
    key: string;
    /** Metadata value */
    value: string;
    /** Type of metadata */
    metadataType?: string;
    /** Timestamp when metadata was recorded */
    timestamp: number;
}

/**
 * Dependency event data for streaming analysis
 */
export interface DependencyEventData {
    /** Source resource ID */
    resourceId: number;
    /** Source resource key */
    sourceKey: ResourceKey;
    /** Target resource key with dependency info */
    targetKey: DependencyResourceKey;
    /** Type of dependency */
    dependencyType: string;
    /** Confidence level (0-100) */
    confidence: number;
    /** Additional metadata */
    metadata?: Record<string, any>;
}

/**
 * Dependency analysis options
 */
export interface DependencyAnalysisOptions {
    /** Maximum depth to analyze dependencies */
    maxDepth?: number;
    /** Whether to include gameplay system analysis */
    includeGameplayAnalysis?: boolean;
    /** Whether to include visualization metadata */
    includeVisualizationMetadata?: boolean;
    /** Whether to analyze cross-package dependencies */
    analyzeCrossPackageDependencies?: boolean;
    /** Package ID for context */
    packageId?: number;
    /** Direction of dependency analysis */
    direction?: 'forward' | 'backward' | 'both';
}

/**
 * Dependency chain visualization data
 */
export interface DependencyVisualizationData {
    /** Nodes in the dependency graph */
    nodes: {
        id: number;
        label: string;
        type: string;
        category: string;
        importance: number;
        group: string;
        packageId: number;
        packageName: string;
    }[];
    /** Links between nodes */
    links: {
        source: number;
        target: number;
        type: string;
        strength: number;
    }[];
    /** Visualization metadata */
    metadata: {
        layoutType: string;
        nodeCategories: string[];
        edgeTypes: string[];
    };
}