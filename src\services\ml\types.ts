﻿import { BinaryResourceType } from '../../types/resource/core.js';
import { ConflictInfo, ConflictSeverity } from '../../types/conflict/index.js';

export interface LLMConfig {
  apiKey: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

export interface LLMResponse {
  text: string;
  tokens: number;
  finishReason: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface LLMService {
  initialize(): Promise<void>;
  generateCompletion(prompt: string, options?: Partial<LLMConfig>): Promise<LLMResponse>;
  detectConflicts(text1: string, text2: string): Promise<boolean>;
  analyzeResourceTypeConflicts(type1: BinaryResourceType, type2: BinaryResourceType): Promise<boolean>;
}

// Interface for the expected output from the MCP tool (matching Python's AnalyzeConflictOutput)
export interface PydanticAIResult {
    hasConflict: boolean;
    severity: string; // "LOW", "MEDIUM", "HIGH", "CRITICAL", "UNKNOWN"
    details: string;
    recommendations: string[];
}

export interface AgentAnalysisResult {
  conflicts: ConflictInfo[]; // Use imported ConflictInfo
  metrics: {
    totalConflicts: number;
    severityDistribution: Record<ConflictSeverity, number>; // Use imported ConflictSeverity
    affectedResourceCount: number;
  };
  recommendations: string[];
}
