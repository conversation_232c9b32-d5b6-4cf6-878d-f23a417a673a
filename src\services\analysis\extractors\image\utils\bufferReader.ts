/**
 * Image Buffer Reader
 * 
 * Domain-specific buffer reader for image extraction that extends the unified BufferReader
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BufferReadOptions } from '../../../../../utils/buffer/bufferReader.js';

/**
 * Image-specific buffer reader that extends the unified BufferReader
 * with additional features for image processing
 */
export class ImageBufferReader extends BufferReader {
    /**
     * Creates a new ImageBufferReader
     * @param buffer Buffer to read from
     * @param initialPosition Initial position (default: 0)
     * @param options Reading options
     */
    constructor(buffer: Buffer, initialPosition: number = 0, options: BufferReadOptions = {}) {
        super(buffer, initialPosition, {
            throwOnBufferOverflow: true, // Image extraction should be strict by default
            ...options
        });
    }

    /**
     * Reads DDS header format signature
     * @returns DDS format signature or null if failed
     */
    public readDDSSignature(): string | null {
        return this.readString(4, 'ascii', 'DDS_signature');
    }

    /**
     * Reads PNG header format signature  
     * @returns PNG format signature or null if failed
     */
    public readPNGSignature(): Buffer | null {
        return this.readSlice(8, 'PNG_signature');
    }

    /**
     * Reads image dimensions (width, height) as UInt32LE
     * @returns Object with width and height or null if failed
     */
    public readImageDimensions(): { width: number; height: number } | null {
        const width = this.readUInt32LE('image_width');
        const height = this.readUInt32LE('image_height');
        
        if (width === null || height === null) {
            return null;
        }
        
        return { width, height };
    }

    /**
     * Reads RGBA pixel data
     * @param pixelCount Number of pixels to read
     * @returns RGBA pixel data or null if failed
     */
    public readRGBAPixels(pixelCount: number): Buffer | null {
        const bytesPerPixel = 4; // RGBA = 4 bytes per pixel
        return this.readSlice(pixelCount * bytesPerPixel, 'RGBA_pixels');
    }

    /**
     * Reads RGB pixel data
     * @param pixelCount Number of pixels to read
     * @returns RGB pixel data or null if failed
     */
    public readRGBPixels(pixelCount: number): Buffer | null {
        const bytesPerPixel = 3; // RGB = 3 bytes per pixel
        return this.readSlice(pixelCount * bytesPerPixel, 'RGB_pixels');
    }
}

// Export original interfaces for backward compatibility
export interface BufferReadResult<T> {
    /** The value read from the buffer */
    value: T | undefined;
    /** New offset after reading */
    newOffset: number;
    /** Whether the read operation was successful */
    success: boolean;
    /** Error message if the read operation failed */
    error?: string;
}

// Backward compatibility - export the ImageBufferReader as BufferReader 
export { ImageBufferReader as BufferReader };

// Create factory function for backward compatibility
export function createImageBufferReader(buffer: Buffer, position: number = 0): ImageBufferReader {
    return new ImageBufferReader(buffer, position);
}
