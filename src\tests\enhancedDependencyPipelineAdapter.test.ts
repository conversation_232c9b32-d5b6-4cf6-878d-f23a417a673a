/**
 * Tests for EnhancedDependencyPipelineAdapter
 */

import { describe, it, beforeEach, afterEach, expect, vi, Mock } from 'vitest';
import { Readable } from 'stream';
import { EnhancedDependencyPipelineAdapter } from '../services/analysis/stream/enhancedDependencyPipelineAdapter.js';
import { EnhancedStreamPipeline } from '../services/analysis/stream/index.js';
import { EnhancedDependencyChainAnalyzer } from '../services/analysis/semantic/enhancedDependencyChainAnalyzer.js';
import { DatabaseService } from '../services/databaseService.js';
import { Logger } from '../utils/logging/logger.js';
import * as ResourceTypes from '../constants/resourceTypes.js';

// Mock dependencies
vi.mock('../services/databaseService.js');
vi.mock('../utils/logging/logger.js');
vi.mock('../services/analysis/semantic/enhancedDependencyChainAnalyzer.js');
vi.mock('../services/analysis/stream/index.js', async (importOriginal: () => Promise<any>) => {
  const original = await importOriginal();
  return {
    ...original,
    EnhancedStreamPipeline: vi.fn() // Mock the specific export
  };
});

describe('EnhancedDependencyPipelineAdapter', () => {
  let adapter: EnhancedDependencyPipelineAdapter;
  let mockDatabaseService: any;
  let mockLogger: any;
  let mockDependencyAnalyzer: any;
  let mockStreamPipeline: any;

  // Sample test data
  const testResourceId = 123;
  const testResourceType = ResourceTypes.RESOURCE_TYPE_TUNING;

  beforeEach(() => {
    // Create mock database service
    mockDatabaseService = {
      resources: {
        getResourceById: vi.fn().mockResolvedValue({
          id: testResourceId,
          resourceType: testResourceType,
          group: 0,
          instance: '123456789',
          packageId: 1,
          name: 'TestResource'
        })
      },
      dependencies: {
        saveDependency: vi.fn().mockResolvedValue(undefined)
      },
      metadata: {
        saveMetadata: vi.fn().mockResolvedValue(undefined)
      }
    } as unknown as DatabaseService;

    // Create mock logger
    mockLogger = {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn()
    } as unknown as Logger;

    // Create mock dependency analyzer
    mockDependencyAnalyzer = {
      initialize: vi.fn().mockResolvedValue(undefined),
      analyzeEnhancedDependencyChain: vi.fn().mockResolvedValue({
        root: {
          resourceId: testResourceId,
          resourceName: 'TestResource',
          packageId: 1,
          packageName: 'TestPackage',
          children: []
        },
        totalNodes: 1,
        maxDepth: 0,
        impactScore: 50,
        crossPackageDependencies: [],
        impactedGameplaySystems: [],
        visualizationMetadata: {
          layoutType: 'force-directed',
          nodeCategories: ['tuning'],
          edgeTypes: []
        }
      }),
      analyzePackageDependencies: vi.fn().mockResolvedValue({
        dependencies: [],
        packages: []
      }),
      exportForVisualization: vi.fn().mockReturnValue({
        nodes: [],
        links: [],
        metadata: {}
      }),
      dispose: vi.fn().mockResolvedValue(undefined)
    } as unknown as EnhancedDependencyChainAnalyzer;

    // Create mock stream pipeline
    mockStreamPipeline = {
      createPipeline: vi.fn().mockImplementation((resourceType: number, source: Readable) => {
        // Return the source stream with additional methods
        return source;
      }),
      on: vi.fn(),
      emit: vi.fn(),
      destroy: vi.fn().mockResolvedValue(undefined)
    } as unknown as EnhancedStreamPipeline;

    // Add pipe method to the mockStreamPipeline.createPipeline result
    mockStreamPipeline.createPipeline.mockImplementation((resourceType: number, source: Readable) => {
      const mockPipeline = new Readable({
        read() {}
      });
      
      // Add emit method
      mockPipeline.emit = vi.fn();
      
      // Add pipe method that returns the destination
      mockPipeline.pipe = vi.fn().mockImplementation((destination: any) => {
        // Add emit method to the pipe result
        destination.emit = vi.fn();
        return destination;
      });
      
      // Push data and end the stream immediately
      process.nextTick(() => {
        mockPipeline.push(Buffer.from('test data'));
        mockPipeline.push(null); // End the stream
      });
      
      return mockPipeline;
    });

    // Create adapter
    adapter = new EnhancedDependencyPipelineAdapter(
      mockDatabaseService,
      mockDependencyAnalyzer,
      mockStreamPipeline,
      mockLogger
    );
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should initialize correctly', async () => {
    await adapter.initialize();
    expect(mockDependencyAnalyzer.initialize).toHaveBeenCalled();
  });

  it('should create a pipeline with dependency analysis', async () => {
    // Create source stream
    const source = new Readable({
      read() {}
    });

    // Create pipeline
    const pipeline = await adapter.createPipeline(
      testResourceId,
      testResourceType,
      source,
      { 
        maxDependencyDepth: 3,
        includeGameplayAnalysis: true,
        includeVisualizationMetadata: true
      }
    );

    // Verify pipeline was created
    expect(mockStreamPipeline.createPipeline).toHaveBeenCalledWith(
      testResourceType,
      source,
      expect.objectContaining({
        maxDependencyDepth: 3,
        includeGameplayAnalysis: true,
        includeVisualizationMetadata: true
      })
    );

    // Manually trigger end event to test async analysis
    if (pipeline.emit) {
      pipeline.emit('end');
    }

    // Wait for async tasks
    await new Promise(resolve => setTimeout(resolve, 50));

    // Verify dependency analysis was called
    expect(mockDependencyAnalyzer.analyzeEnhancedDependencyChain).toHaveBeenCalledWith(
      testResourceId,
      3,
      'forward',
      true,
      true
    );

    // Verify metadata was saved
    expect(mockDatabaseService.metadata.saveMetadata).toHaveBeenCalled();
  });

  it('should handle references in chunks', async () => {
    // Create source stream
    const source = new Readable({
      objectMode: true,
      read() {}
    });

    // Create pipeline
    const pipeline = await adapter.createPipeline(
      testResourceId,
      testResourceType,
      source
    );

    // Manually push a chunk with references
    const chunk = {
      references: [
        { type: 0x12345678, group: 0, instance: '987654321' }
      ]
    };

    // Simulate the transform function
    const transformer = (pipeline as any)._transform;
    if (transformer) {
      await transformer(chunk, 'utf8', () => {});

      // Verify dependency was saved
      expect(mockDatabaseService.dependencies.saveDependency).toHaveBeenCalledWith(
        expect.objectContaining({
          resourceId: testResourceId,
          targetType: 0x12345678,
          targetInstance: '987654321'
        })
      );
    }
  });

  it('should dispose resources properly', async () => {
    await adapter.dispose();
    
    expect(mockStreamPipeline.destroy).toHaveBeenCalled();
    expect(mockDependencyAnalyzer.dispose).toHaveBeenCalled();
    expect(mockLogger.info).toHaveBeenCalledWith('Enhanced Dependency Pipeline Adapter disposed successfully');
  });
});