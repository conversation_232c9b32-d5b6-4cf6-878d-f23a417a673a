import { DependencyInfo } from '../../../databaseService.js';

/**
 * VFX resource type IDs
 *
 * Based on research from Sims 4 game files and documentation:
 * - VFX resources are binary resources used for visual effects in the game
 * - There are multiple types of VFX resources in The Sims 4
 * - VFX_MODIFIER defines the visual effect properties
 * - VFX_STATE defines the state transitions for the effect
 * - VISUAL_EFFECT is the legacy format used before the University patch
 * - NEW_VISUAL_EFFECT is the new format used after the University patch
 * - VFX_MAPPING_TABLE maps effect names to their resource locations
 *
 * References:
 * - docs/assets/visual_effect.py
 * - docs/assets/vfx_state.py
 * - docs/assets/vfx_mask.py
 * - https://modthesims.info/showthread.php?t=639339 (Denton's VFX Tool)
 * - https://modthesims.info/showthread.php?t=675646 (VFX Tutorial)
 */
export const VFX_RESOURCE_TYPES = {
    VISUAL_EFFECT: 0xEA5118B0,      // Legacy visual effect resource
    VFX_MODIFIER: 0x0A5DC6B9,       // Visual effect modifier
    VFX_STATE: 0x0A5DC6BA,          // Visual effect state
    NEW_VISUAL_EFFECT: 0x1B192049,  // New visual effect resource (post-University)
    VFX_MAPPING_TABLE: 0x1B19204A   // VFX mapping table resource
};

/**
 * VFX lifetime types (from visual_effect.py)
 */
export enum VfxLifetimeType {
    ONE_SHOT = 'one_shot',           // Play once and stop
    INTERACTION = 'interaction',      // Play for the duration of an interaction
    CONTINUATION = 'continuation',    // Play until explicitly stopped
    ANIMATION_EVENT = 'animation_event' // Play until an animation event
}

/**
 * VFX mask flags (from vfx_mask.py and additional research)
 *
 * These flags control which Sims can see or are affected by the VFX.
 * They can be combined using bitwise OR operations.
 */
export enum VfxMaskFlags {
    MASK_NONE = 0,
    MASK_BABY = 1,
    MASK_TODDLER = 2,
    MASK_CHILD = 4,
    MASK_TEEN = 8,
    MASK_YOUNGADULT = 16,
    MASK_ADULT = 32,
    MASK_ELDER = 64,
    MASK_SKILL_LEVEL_1 = 128,
    MASK_SKILL_LEVEL_2 = 256,
    MASK_SKILL_LEVEL_3 = 512,
    MASK_SKILL_LEVEL_4 = 1024,
    MASK_SKILL_LEVEL_5 = 2048,
    MASK_DREAM_BIG = 4096,
    MASK_VAMPIRE = 8192,
    MASK_PARENTING_SKILL = 16384,
    MASK_CURSED = 32768,
    MASK_SKILL_LEVEL_HIGH = 65536,
    MASK_WITCH_MOTES = 131072,
    MASK_SECRET_SOCIETY = 262144,
    MASK_SPRITE_LOW = 524288,
    MASK_SPRITE_MEDIUM = 1048576,
    MASK_SPRITE_HIGH = 2097152,
    MASK_WEREWOLF = 4194304,
    MASK_HUMAN = 8388608,
    MASK_ALIEN = 16777216,
    MASK_MERMAID = 33554432,
    MASK_WITCH = 67108864,
    MASK_ROBOT = 134217728,
    MASK_GHOST = 268435456,
    MASK_PET = 536870912,
    MASK_SKILL_LOW = 1073741824,
    MASK_SKILL_MEDIUM = 2147483648,
    // Additional masks may exist beyond 32-bit range
}

/**
 * VFX draw modes
 *
 * These control how particles appear in the game.
 */
export enum VfxDrawMode {
    OPAQUE = 128,                    // Particle will be opaque
    LUMINOUS_SEMI_TRANSPARENT = 132  // Particle will be luminous and semi-transparent
}

/**
 * VFX draw flags
 *
 * These control how lighting affects particles.
 */
export enum VfxDrawFlags {
    AFFECTED_BY_ROOM_LIGHTING = 0,    // Particle will be affected by room lighting
    NOT_AFFECTED_BY_ROOM_LIGHTING = 1 // Particle will not be affected by room lighting
}

/**
 * VFX resource structure information based on Sims 4 file format
 * This is a simplified representation for metadata extraction
 */
export interface VfxHeaderInfo {
    format: string;         // Format identifier
    version: number;        // Format version
    flags: number;          // Format-specific flags
    effectType?: string;    // Type of visual effect
    parameterCount?: number; // Number of effect parameters
    animationCount?: number; // Number of animation references
    particleCount?: number;  // Number of particle systems
    parameters?: VfxParameter[]; // Effect parameters
    animationReferences?: string[]; // Animation references
    particleSystems?: string[]; // Particle system references
    timingInfo?: VfxTimingInfo; // Timing information
    stateIndex?: number;    // State index (for VFX_STATE)
    maskFlags?: number;     // VFX mask flags
    targetJoint?: string;   // Target joint name
    lifetimeType?: VfxLifetimeType; // Lifetime type

    // Draw properties
    drawMode?: number;      // How the particle appears (128 for opaque, 132 for luminous)
    drawFlags?: number;     // How lighting affects the particle

    // Texture animation properties
    tileCountU?: number;    // Tile count in U direction for animated textures
    tileCountV?: number;    // Tile count in V direction for animated textures
    frameSpeed?: number;    // Animation speed in frames per second
    frameStart?: number;    // Starting frame for animation

    // Physics properties
    windStrength?: number;  // How much particles are affected by wind
    gravityStrength?: number; // How much particles are affected by gravity
    drag?: number;          // Air resistance affecting particles

    // Size and rotation properties
    sizeCurve?: number[];   // Controls particle size over time
    aspectRatio?: number;   // Controls particle stretching
    rotationCurve?: number[]; // Controls particle rotation over time

    // References
    textureReference?: bigint; // Reference to texture used by the VFX
    soundReference?: bigint;   // Reference to sound used by the VFX
}

/**
 * VFX parameter information
 */
export interface VfxParameter {
    name: string;
    value: any;
}

/**
 * VFX timing information
 */
export interface VfxTimingInfo {
    duration?: number;  // Effect duration
    looping?: boolean;  // Whether the effect loops
    fadeIn?: number;    // Fade-in time
    fadeOut?: number;   // Fade-out time
}

/**
 * Common resource types for VFX dependencies
 *
 * These are the resource types that VFX resources commonly reference.
 * This list is used for scanning buffers to find potential dependencies.
 */
export const VFX_DEPENDENCY_TYPES = [
    // Image resources
    0x00B2D882, // DDS Image
    0x319E4F1D, // PNG Image
    0x3453CF95, // RLE Image
    0xBA856C78, // RLE2 Image
    0x0288B3A2, // Texture
    0x1C4A276C, // Texture Sampler

    // Animation resources
    0x6B20C4F3, // Animation
    0xAC16FBEC, // Animation Map
    0x8EAF13DE, // Animation State Machine
    0x033260E3, // Animation Clip
    0xD0D6AABF, // Animation Controller

    // Sound resources
    0x2026960B, // Sound
    0xFD04E3BE, // Sound Effect
    0x062AE55B, // Sound Bank

    // Model resources
    0x01661233, // MLOD (Model LOD)
    0x01D10F34, // MODL (Model)
    0x0166038C, // GEOM (Geometry)

    // VFX resources
    0x0A5DC6B9, // VFX Modifier
    0x0A5DC6BA, // VFX State

    // Material resources
    0x0333406C, // Material
    0x0355E0A6, // Material Definition
    0x033A1435, // Material Instance
    0x0354796A  // Material Variant
];

/**
 * Scans a buffer for potential VFX dependencies
 * @param buffer The buffer to scan
 * @param resourceId The resource ID
 * @returns An array of dependency information
 */
export function scanForVfxDependencies(buffer: Buffer, resourceId: number): DependencyInfo[] {
    const dependencies: DependencyInfo[] = [];
    const seenDependencies = new Set<string>(); // Track seen dependencies to avoid duplicates

    // Scan for potential dependencies
    // Start at offset 32 to skip header, and ensure we have enough bytes to read a full TGI
    for (let i = 32; i < buffer.length - 16; i += 4) {
        try {
            const potentialType = buffer.readUInt32LE(i);

            // Check if this is a known VFX dependency type
            if (VFX_DEPENDENCY_TYPES.includes(potentialType)) {
                const potentialGroup = buffer.readUInt32LE(i + 4);
                const potentialInstance1 = buffer.readUInt32LE(i + 8);
                const potentialInstance2 = buffer.readUInt32LE(i + 12);

                // Construct potential bigint instance from two 32-bit parts
                const potentialInstance = BigInt(potentialInstance1) | (BigInt(potentialInstance2) << 32n);

                // Skip invalid instances (all zeros or all ones)
                if (potentialInstance === 0n || potentialInstance === BigInt('0xFFFFFFFFFFFFFFFF')) {
                    continue;
                }

                // Skip invalid groups (all zeros or all ones)
                if (potentialGroup === 0 || potentialGroup === 0xFFFFFFFF) {
                    continue;
                }

                // Create a unique key for this dependency to avoid duplicates
                const dependencyKey = `${potentialType}_${potentialGroup}_${potentialInstance}`;

                // Skip if we've already seen this dependency
                if (seenDependencies.has(dependencyKey)) {
                    continue;
                }

                // Add to seen dependencies
                seenDependencies.add(dependencyKey);

                // Determine dependency type based on resource type
                let dependencyType = 'VfxDependency';
                if (potentialType === 0x00B2D882 || potentialType === 0x319E4F1D ||
                    potentialType === 0x3453CF95 || potentialType === 0xBA856C78 ||
                    potentialType === 0x0288B3A2) {
                    dependencyType = 'TextureDependency';
                } else if (potentialType === 0x2026960B || potentialType === 0xFD04E3BE ||
                           potentialType === 0x062AE55B) {
                    dependencyType = 'SoundDependency';
                } else if (potentialType === 0x01661233 || potentialType === 0x01D10F34 ||
                           potentialType === 0x0166038C) {
                    dependencyType = 'ModelDependency';
                } else if (potentialType === 0x0A5DC6B9 || potentialType === 0x0A5DC6BA) {
                    dependencyType = 'VfxDependency';
                } else if (potentialType === 0x0333406C || potentialType === 0x0355E0A6 ||
                           potentialType === 0x033A1435 || potentialType === 0x0354796A) {
                    dependencyType = 'MaterialDependency';
                } else if (potentialType === 0x6B20C4F3 || potentialType === 0xAC16FBEC ||
                           potentialType === 0x8EAF13DE || potentialType === 0x033260E3 ||
                           potentialType === 0xD0D6AABF) {
                    dependencyType = 'AnimationDependency';
                }

                // Add as potential dependency
                dependencies.push({
                    resourceId: resourceId,
                    targetType: potentialType,
                    targetGroup: BigInt(potentialGroup),
                    targetInstance: potentialInstance,
                    referenceType: dependencyType,
                    timestamp: Date.now()
                });
            }
        } catch (error) {
            // Skip any errors in reading buffer (e.g., out of bounds)
            continue;
        }
    }

    return dependencies;
}

/**
 * Creates a content snippet from VFX header information
 * @param header The VFX header information
 * @param resourceType The resource type
 * @param dependencyCount The number of dependencies
 * @returns A content snippet string
 * @deprecated Use createVfxContentSnippet from createVfxContentSnippet.ts instead
 */
function _createVfxContentSnippet(header: VfxHeaderInfo, resourceType: number, dependencyCount: number): string {
    let contentSnippet = `[VFX: ${header.format}, Ver=${header.version}`;

    // Add resource type name for clarity
    let resourceTypeName = "Unknown";
    if (resourceType === VFX_RESOURCE_TYPES.VFX_MODIFIER) {
        resourceTypeName = "VfxModifier";
    } else if (resourceType === VFX_RESOURCE_TYPES.VFX_STATE) {
        resourceTypeName = "VfxState";
    }
    contentSnippet += `, Type=${resourceTypeName}`;

    // Add format-specific information
    if (header.format === 'VFX_MODIFIER') {
        if (header.effectType) contentSnippet += `, Effect=${header.effectType}`;
        if (header.parameterCount) contentSnippet += `, Params=${header.parameterCount}`;
        if (header.animationCount) contentSnippet += `, Animations=${header.animationCount}`;

        if (header.timingInfo) {
            if (header.timingInfo.duration) contentSnippet += `, Duration=${header.timingInfo.duration}s`;
            if (header.timingInfo.looping) contentSnippet += `, Looping=true`;
        }

        if (header.parameters && header.parameters.length > 0) {
            const paramSample = header.parameters.slice(0, 2).map(p => p.name).join(', ');
            contentSnippet += `, ParamNames=[${paramSample}${header.parameters.length > 2 ? '...' : ''}]`;
        }

        if (header.animationReferences && header.animationReferences.length > 0) {
            const animSample = header.animationReferences.slice(0, 2).join(', ');
            contentSnippet += `, AnimRefs=[${animSample}${header.animationReferences.length > 2 ? '...' : ''}]`;
        }

        // Add lifetime type if available
        if (header.lifetimeType) {
            contentSnippet += `, Lifetime=${header.lifetimeType}`;
        }

        // Add target joint if available
        if (header.targetJoint) {
            contentSnippet += `, Target=${header.targetJoint}`;
        }
    } else if (header.format === 'VFX_STATE') {
        if (header.effectType) contentSnippet += `, Effect=${header.effectType}`;
        if (header.parameterCount) contentSnippet += `, Params=${header.parameterCount}`;
        if (header.particleCount) contentSnippet += `, Particles=${header.particleCount}`;

        // Add state index if available
        if (header.stateIndex !== undefined) {
            contentSnippet += `, StateIndex=${header.stateIndex}`;
        }

        if (header.parameters && header.parameters.length > 0) {
            const paramSample = header.parameters.slice(0, 2).map(p => p.name).join(', ');
            contentSnippet += `, ParamNames=[${paramSample}${header.parameters.length > 2 ? '...' : ''}]`;
        }

        if (header.particleSystems && header.particleSystems.length > 0) {
            const particleSample = header.particleSystems.slice(0, 2).join(', ');
            contentSnippet += `, ParticleSystems=[${particleSample}${header.particleSystems.length > 2 ? '...' : ''}]`;
        }
    }

    // Add mask flags if available
    if (header.maskFlags) {
        // Convert mask flags to readable format
        const maskNames: string[] = [];
        for (const [key, value] of Object.entries(VfxMaskFlags)) {
            if (typeof value === 'number' && (header.maskFlags & value) === value) {
                maskNames.push(key.replace('MASK_', ''));
            }
        }

        if (maskNames.length > 0) {
            const maskSample = maskNames.slice(0, 2).join(', ');
            contentSnippet += `, Masks=[${maskSample}${maskNames.length > 2 ? '...' : ''}]`;
        } else {
            contentSnippet += `, MaskFlags=0x${header.maskFlags.toString(16)}`;
        }
    }

    // Add flags in hex format if not already included
    if (header.flags && !contentSnippet.includes('Flags=')) {
        contentSnippet += `, Flags=0x${header.flags.toString(16)}`;
    }

    // Add dependency count if available
    if (dependencyCount > 0) {
        contentSnippet += `, Dependencies=${dependencyCount}`;
    }

    contentSnippet += `]`;

    return contentSnippet;
}

/**
 * Extracts a parameter value from a buffer based on its type
 * @param buffer The buffer to extract from
 * @param offset The offset to start extraction
 * @param paramType The parameter type
 * @returns The extracted parameter value and the new offset
 */
export function extractParameterValue(buffer: Buffer, offset: number, paramType: number): { value: any, newOffset: number } {
    let paramValue: any = null;
    let newOffset = offset;

    if (paramType === 1 && offset + 4 <= buffer.length) {
        // Float value
        paramValue = buffer.readFloatLE(offset);
        newOffset += 4;
    } else if (paramType === 2 && offset + 4 <= buffer.length) {
        // Integer value
        paramValue = buffer.readInt32LE(offset);
        newOffset += 4;
    } else if (paramType === 3 && offset + 16 <= buffer.length) {
        // Vector4 value
        paramValue = {
            x: buffer.readFloatLE(offset),
            y: buffer.readFloatLE(offset + 4),
            z: buffer.readFloatLE(offset + 8),
            w: buffer.readFloatLE(offset + 12)
        };
        newOffset += 16;
    } else if (paramType === 4 && offset + 4 <= buffer.length) {
        // String value
        const strLength = buffer.readUInt32LE(offset);
        newOffset += 4;

        if (newOffset + strLength <= buffer.length) {
            paramValue = buffer.slice(newOffset, newOffset + strLength).toString('utf8');
            newOffset += strLength;
        }
    } else if (paramType === 5 && offset + 1 <= buffer.length) {
        // Boolean value
        paramValue = buffer.readUInt8(offset) !== 0;
        newOffset += 1;
    } else {
        // Skip unknown parameter type
        newOffset += 4; // Assume 4 bytes for unknown types
    }

    return { value: paramValue, newOffset };
}
