/**
 * ResourceTypeRegistry
 *
 * This class maintains a registry of resource types by game version and provides methods to detect new resource types.
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { ResourceTypeDefinition } from './types.js';
import { resourceTypeDefinitions, getResourceTypeDefinitionByTypeId, getResourceTypeDefinitionByName } from './data/resourceTypes.js';

const log = new Logger('ResourceTypeRegistry');

/**
 * Registry for resource types
 */
export class ResourceTypeRegistry {
  private databaseService: DatabaseService;

  /**
   * Create a new ResourceTypeRegistry
   * @param databaseService Database service for storing and retrieving resource type data
   */
  constructor(databaseService: DatabaseService) {
    this.databaseService = databaseService;
    log.info('ResourceTypeRegistry initialized');
  }

  /**
   * Initialize the registry
   */
  public async initialize(): Promise<void> {
    try {
      await this.createTables();
      log.info('ResourceTypeRegistry tables created');
    } catch (error) {
      log.error(`Error initializing ResourceTypeRegistry: ${error}`);
      throw error;
    }
  }

  /**
   * Create the necessary database tables
   */
  private async createTables(): Promise<void> {
    try {
      // Create the resource_types table
      await this.databaseService.executeQuery(`
        CREATE TABLE IF NOT EXISTS resource_types (
          type_id INTEGER PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          introduced_in TEXT NOT NULL,
          last_updated_in TEXT NOT NULL,
          is_official INTEGER NOT NULL,
          is_deprecated INTEGER NOT NULL,
          dependencies TEXT NOT NULL
        )
      `);

      // Create the custom_resource_types table
      await this.databaseService.executeQuery(`
        CREATE TABLE IF NOT EXISTS custom_resource_types (
          type_id INTEGER PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          discovered_in TEXT NOT NULL,
          discovery_timestamp INTEGER NOT NULL,
          sample_count INTEGER NOT NULL,
          sample_resources TEXT NOT NULL
        )
      `);

      // Populate the resource_types table with initial data
      for (const resourceType of resourceTypeDefinitions) {
        await this.databaseService.executeQuery(`
          INSERT OR IGNORE INTO resource_types (
            type_id, name, description, introduced_in, last_updated_in,
            is_official, is_deprecated, dependencies
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          resourceType.typeId,
          resourceType.name,
          resourceType.description,
          resourceType.introducedIn,
          resourceType.lastUpdatedIn,
          resourceType.isOfficial ? 1 : 0,
          resourceType.isDeprecated ? 1 : 0,
          JSON.stringify(resourceType.dependencies)
        ]);
      }
    } catch (error) {
      log.error(`Error creating tables: ${error}`);
      throw error;
    }
  }

  /**
   * Get all resource type definitions
   * @returns Array of all resource type definitions
   */
  public getAllResourceTypes(): ResourceTypeDefinition[] {
    return resourceTypeDefinitions;
  }

  /**
   * Get a resource type definition by type ID
   * @param typeId Resource type ID
   * @returns The resource type definition, or undefined if not found
   */
  public getResourceTypeById(typeId: number): ResourceTypeDefinition | undefined {
    return getResourceTypeDefinitionByTypeId(typeId);
  }

  /**
   * Get resource type info by type ID (alias for getResourceTypeById for compatibility)
   * @param typeId Resource type ID
   * @returns The resource type info, or a default object if not found
   */
  public getInfo(typeId: number): { name: string; description?: string } {
    const resourceType = this.getResourceTypeById(typeId);
    if (resourceType) {
      return {
        name: resourceType.name,
        description: resourceType.description
      };
    }
    return {
      name: `Unknown (0x${typeId.toString(16).toUpperCase().padStart(8, '0')})`
    };
  }

  /**
   * Get a resource type definition by name
   * @param name Resource type name
   * @returns The resource type definition, or undefined if not found
   */
  public getResourceTypeByName(name: string): ResourceTypeDefinition | undefined {
    return getResourceTypeDefinitionByName(name);
  }

  /**
   * Get resource type definitions by game version
   * @param gameVersion Game version
   * @returns Array of resource type definitions available in the specified game version
   */
  public async getResourceTypesByGameVersion(gameVersion: string): Promise<ResourceTypeDefinition[]> {
    try {
      // Get all resource types
      const allTypes = this.getAllResourceTypes();

      // Filter by game version
      return allTypes.filter(type => {
        // Check if the resource type was introduced in or before the specified game version
        // This is a simplified check; in a real implementation, you would compare version numbers
        return type.introducedIn <= gameVersion && !type.isDeprecated;
      });
    } catch (error) {
      log.error(`Error getting resource types for game version ${gameVersion}: ${error}`);
      throw error;
    }
  }

  /**
   * Register a custom resource type
   * @param typeId Resource type ID
   * @param name Resource type name
   * @param description Resource type description
   * @param discoveredIn Game version where the resource type was discovered
   * @param sampleResources Sample resources of this type
   */
  public async registerCustomResourceType(
    typeId: number,
    name: string,
    description: string,
    discoveredIn: string,
    sampleResources: string[]
  ): Promise<void> {
    try {
      // Check if the resource type already exists
      const existingType = this.getResourceTypeById(typeId);
      if (existingType) {
        log.info(`Resource type ${typeId} (${existingType.name}) already exists`);
        return;
      }

      // Insert into custom_resource_types table
      await this.databaseService.executeQuery(`
        INSERT OR REPLACE INTO custom_resource_types (
          type_id, name, description, discovered_in,
          discovery_timestamp, sample_count, sample_resources
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        typeId,
        name,
        description,
        discoveredIn,
        Date.now(),
        sampleResources.length,
        JSON.stringify(sampleResources)
      ]);

      log.info(`Registered custom resource type: ${typeId} (${name})`);
    } catch (error) {
      log.error(`Error registering custom resource type ${typeId} (${name}): ${error}`);
      throw error;
    }
  }

  /**
   * Get all custom resource types
   * @returns Array of custom resource type definitions
   */
  public async getCustomResourceTypes(): Promise<any[]> {
    try {
      const results = await this.databaseService.executeQuery(`
        SELECT * FROM custom_resource_types
      `);

      return results.map((row: any) => ({
        typeId: row.type_id,
        name: row.name,
        description: row.description,
        discoveredIn: row.discovered_in,
        discoveryTimestamp: row.discovery_timestamp,
        sampleCount: row.sample_count,
        sampleResources: JSON.parse(row.sample_resources)
      }));
    } catch (error) {
      log.error(`Error getting custom resource types: ${error}`);
      throw error;
    }
  }

  /**
   * Detect new resource types in a package
   * @param resources Array of resources in the package
   * @returns Array of detected new resource types
   */
  public async detectNewResourceTypes(resources: { type: number; group: number; instance: number }[]): Promise<number[]> {
    try {
      // Get all known resource types
      const knownTypeIds = new Set(resourceTypeDefinitions.map(type => type.typeId));

      // Get all custom resource types
      const customTypes = await this.getCustomResourceTypes();
      for (const type of customTypes) {
        knownTypeIds.add(type.typeId);
      }

      // Find unknown resource types
      const unknownTypeIds = new Set<number>();

      for (const resource of resources) {
        if (!knownTypeIds.has(resource.type)) {
          unknownTypeIds.add(resource.type);
        }
      }

      return Array.from(unknownTypeIds);
    } catch (error) {
      log.error(`Error detecting new resource types: ${error}`);
      throw error;
    }
  }

  /**
   * Dispose of resources used by the registry
   */
  public async dispose(): Promise<void> {
    try {
      log.info('Disposing ResourceTypeRegistry resources');
      log.info('ResourceTypeRegistry resources disposed successfully');
    } catch (error) {
      log.error(`Error disposing ResourceTypeRegistry resources: ${error}`);
      throw error;
    }
  }
}
