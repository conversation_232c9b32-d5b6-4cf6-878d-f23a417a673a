import { create, StateCreator } from 'zustand'; // Import StateCreator

interface ElectronState {
  isElectronConnected: boolean;
  windowState: string;
  isDevToolsOpen: boolean;
  setElectronConnected: (connected: boolean) => void;
  setWindowState: (state: string) => void;
  setDevToolsOpen: (open: boolean) => void;
}

// Remove manual ZustandSetFn type

// Use StateCreator for the callback function type
export const useElectronStore = create<ElectronState>(
  (set) => ({ // Let TypeScript infer 'set' from StateCreator context
    isElectronConnected: false,
    windowState: 'normal',
  isDevToolsOpen: false,
  setElectronConnected: (connected: boolean) => set({ isElectronConnected: connected }),
  setWindowState: (state: string) => set({ windowState: state }),
  setDevToolsOpen: (open: boolean) => set({ isDevToolsOpen: open })
}));
