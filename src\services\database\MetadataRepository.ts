import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';
import { MetadataInfo } from '../../types/database.js';

export class MetadataRepository {
    private db: Database.Database;
    private logger: Logger;

    constructor(db: Database.Database, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    saveMetadata(metadataInfo: MetadataInfo): number {
        try {
            // Convert value to string for storage. Use JSON.stringify for objects/arrays.
            let valueToStore: string | null;
            if (metadataInfo.value === null || metadataInfo.value === undefined) {
                valueToStore = null;
            } else if (typeof metadataInfo.value === 'object') {
                // Handle BigInt serialization if present within objects/arrays
                valueToStore = JSON.stringify(metadataInfo.value, (key, value) =>
                    typeof value === 'bigint' ? value.toString() : value
                );
            } else if (typeof metadataInfo.value === 'bigint') {
                valueToStore = metadataInfo.value.toString(); // Store bigint as string
            }
             else {
                valueToStore = String(metadataInfo.value);
            }

            // Manual Upsert Logic
            const updateStmt = this.db.prepare(`
                UPDATE Metadata SET value = ?
                WHERE resourceId = ? AND key = ?
            `);

            const updateResult = updateStmt.run(
                valueToStore,
                metadataInfo.resourceId,
                metadataInfo.key
            );

            if (updateResult.changes > 0) {
                // Row updated, fetch ID
                const selectStmt = this.db.prepare(`
                    SELECT id FROM Metadata WHERE resourceId = ? AND key = ?
                `);
                const row = selectStmt.get(metadataInfo.resourceId, metadataInfo.key) as { id: number } | undefined;
                if (!row) {
                    throw new Error(`Failed to retrieve ID after successful metadata update for resource ${metadataInfo.resourceId}, key ${metadataInfo.key}.`);
                }
                return row.id;
            } else {
                // Row doesn't exist, insert it
                const insertStmt = this.db.prepare(`
                    INSERT INTO Metadata (resourceId, key, value)
                    VALUES (?, ?, ?)
                    RETURNING id;
                `);
                const insertResult = insertStmt.get(
                    metadataInfo.resourceId,
                    metadataInfo.key,
                    valueToStore
                ) as { id: number };
                return insertResult.id;
            }
        } catch (error) {
            this.logger.error(`Error saving/updating metadata for resource ${metadataInfo.resourceId}, key ${metadataInfo.key}:`, error);
            throw error;
        }
    }

    public getMetadataValue(resourceId: number, key: string): string | undefined {
        this.logger.debug(`[MetadataRepository] Querying for metadata value: resourceId=${resourceId}, key=${key}`);
        const stmt = this.db.prepare(`
            SELECT value FROM Metadata WHERE resourceId = ? AND key = ?
        `);
        try {
            const result = stmt.get(resourceId, key) as { value: string | null } | undefined;
            return result?.value === null ? undefined : result?.value;
        } catch (error) {
            this.logger.error(`[MetadataRepository] Error getting metadata value for resource ${resourceId}, key ${key}:`, error);
            return undefined;
        }
    }

    /**
     * Get all metadata for a resource
     * @param resourceId Resource ID
     * @returns Array of metadata objects
     */
    public getMetadataByResourceId(resourceId: number): { id: number; resourceId: number; key: string; value: string }[] {
        this.logger.debug(`[MetadataRepository] Querying for all metadata for resourceId=${resourceId}`);
        const stmt = this.db.prepare(`
            SELECT id, resourceId, key, value FROM Metadata WHERE resourceId = ?
        `);
        try {
            const results = stmt.all(resourceId) as { id: number; resourceId: number; key: string; value: string }[];
            this.logger.debug(`[MetadataRepository] Found ${results.length} metadata entries for resource ${resourceId}`);
            return results;
        } catch (error) {
            this.logger.error(`[MetadataRepository] Error getting metadata for resource ${resourceId}:`, error);
            return [];
        }
    }

    /**
     * Get all metadata entries by key
     * @param key Metadata key to search for
     * @returns Array of metadata objects with the specified key
     */
    public getMetadataByKey(key: string): { id: number; resourceId: number; key: string; value: string }[] {
        this.logger.debug(`[MetadataRepository] Querying for all metadata with key=${key}`);
        const stmt = this.db.prepare(`
            SELECT id, resourceId, key, value FROM Metadata WHERE key = ?
        `);
        try {
            const results = stmt.all(key) as { id: number; resourceId: number; key: string; value: string }[];
            this.logger.debug(`[MetadataRepository] Found ${results.length} metadata entries with key ${key}`);
            return results;
        } catch (error) {
            this.logger.error(`[MetadataRepository] Error getting metadata by key ${key}:`, error);
            return [];
        }
    }
}