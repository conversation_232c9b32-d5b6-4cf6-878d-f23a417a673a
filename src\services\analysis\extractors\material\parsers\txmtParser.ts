/**
 * Parser for TXMT (Texture Material) format
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DependencyInfo } from '../../../../databaseService.js';
import { MaterialHeaderInfo } from '../materialTypes.js';
import { handleMaterialExtractionError } from '../error/index.js';

const logger = new Logger('TxmtParser');

/**
 * Parses a TXMT (Texture Material) resource buffer
 * @param buffer The resource buffer
 * @param resourceId The resource ID
 * @returns The parsed material header and dependencies
 */
export function parseTxmt(buffer: Buffer, resourceId: number): { header: MaterialHeaderInfo, dependencies: DependencyInfo[] } {
    try {
        const dependencies: DependencyInfo[] = [];

        // Initialize header with default format
        const header: MaterialHeaderInfo = {
            format: 'TXMT',
            version: 0,
            materialCount: 0,
            flags: 0
        };

        if (buffer.length >= 16) {
            header.version = buffer.readUInt32LE(4);
            header.flags = buffer.readUInt32LE(8);
            header.textureCount = buffer.readUInt32LE(12);

            // Extract texture references
            header.textureReferences = [];
            let offset = 16;

            for (let i = 0; i < header.textureCount && offset + 4 <= buffer.length; i++) {
                const typeLength = buffer.readUInt32LE(offset);
                offset += 4;

                if (offset + typeLength <= buffer.length) {
                    const textureType = buffer.slice(offset, offset + typeLength).toString('utf8');
                    offset += typeLength;

                    if (offset + 4 <= buffer.length) {
                        const pathLength = buffer.readUInt32LE(offset);
                        offset += 4;

                        if (offset + pathLength <= buffer.length) {
                            const texturePath = buffer.slice(offset, offset + pathLength).toString('utf8');
                            offset += pathLength;

                            header.textureReferences.push({
                                type: textureType,
                                path: texturePath
                            });

                            // Look for texture TGI reference
                            if (offset + 16 <= buffer.length) {
                                const textureType = buffer.readUInt32LE(offset);
                                const textureGroup = buffer.readUInt32LE(offset + 4);
                                const textureInstance1 = buffer.readUInt32LE(offset + 8);
                                const textureInstance2 = buffer.readUInt32LE(offset + 12);

                                // Only add as dependency if it looks like a valid TGI
                                if (textureType !== 0) {
                                    // Construct bigint instance from two 32-bit parts
                                    const textureInstance = BigInt(textureInstance1) | (BigInt(textureInstance2) << 32n);

                                    // Add as dependency
                                    dependencies.push({
                                        resourceId: resourceId,
                                        targetType: textureType,
                                        targetGroup: BigInt(textureGroup),
                                        targetInstance: textureInstance,
                                        referenceType: 'Texture',
                                        timestamp: Date.now()
                                    });
                                }

                                offset += 16;
                            }
                        }
                    }
                }
            }
        }

        return { header, dependencies };
    } catch (error) {
        return handleMaterialExtractionError(
            error,
            {
                resourceId,
                operation: 'parseTxmt',
                bufferLength: buffer?.length,
                materialFormat: 'TXMT',
                additionalInfo: { format: 'TXMT' }
            },
            {
                header: {
                    format: 'TXMT_ERROR',
                    version: 0,
                    materialCount: 0,
                    flags: 0
                },
                dependencies: []
            }
        );
    }
}
