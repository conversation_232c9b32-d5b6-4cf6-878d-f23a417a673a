/**
 * Streaming Pipeline Tests
 *
 * This module provides test functions for the streaming-first resource processing pipeline.
 * It tests the enhanced error handling, resource management, input validation,
 * and performance optimization components.
 */

import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';
import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../../services/databaseService.js';
import { ConsolidatedResourceProvider } from '../../services/analysis/stream/consolidatedResourceProvider.js';
import { StreamPipeline } from '../../services/analysis/stream/index.js';
import { DatabaseWriter } from '../../services/analysis/stream/databaseWriter.js';
import { PackageIndexEntry } from '../../services/analysis/package/streamingPackageReader.js';

import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { EnhancedBufferPool } from '../../utils/memory/enhancedBufferPool.js';
import { ResourceTracker, ResourceType } from '../../utils/memory/resourceTracker.js';
import { HardwareDetector, HardwareCategory } from '../../utils/performance/hardwareDetector.js';
import { AdaptiveThrottler } from '../../utils/performance/adaptiveThrottler.js';
import { Validator, ValidationLevel } from '../../utils/validation/validator.js';
import { DataValidator } from '../../utils/validation/dataValidator.js';
import { EnhancedErrorHandler } from '../../utils/error/enhancedErrorHandler.js';
import { AppError, ErrorCategory, ErrorCode, ErrorSeverity } from '../../utils/error/errorTypes.js';
import { formatBytes, formatDuration } from '../../utils/formatting/formatUtils.js';
import { createPrintFunction } from '../../utils/console/consoleOutput.js';

// Create a print function for direct output
const print = createPrintFunction();

// Create a logger
const logger = new Logger('StreamingPipelineTests');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get error handler instance
const errorHandler = EnhancedErrorHandler.getInstance();

// Get resource tracker instance
const resourceTracker = ResourceTracker.getInstance();

// Get hardware detector instance
const hardwareDetector = HardwareDetector.getInstance();

// Get adaptive throttler instance
const adaptiveThrottler = AdaptiveThrottler.getInstance();

// Get validator instance
const validator = Validator.getInstance();

// Get data validator instance
const dataValidator = DataValidator.getInstance();

/**
 * Test the streaming pipeline with a real package file
 * @param packagePath Path to the package file or array of package paths
 * @param databaseService Optional database service (if not provided, a mock will be used)
 * @param options Test options
 */
export async function testStreamingPipeline(
    packagePath: string | string[],
    databaseService?: any,
    options: {
        simulateHardwareProfile?: HardwareCategory;
        injectErrors?: boolean;
        corruptData?: boolean;
        logLevel?: string;
        maxPackages?: number;
        progressiveMode?: boolean;
        memoryCheckInterval?: number;
        skipDatabaseOperations?: boolean;
    } = {}
): Promise<{
    success: boolean;
    packagesProcessed: number;
    resourcesProcessed: number;
    totalSize: number;
    duration: number;
    memoryUsage: {
        heapUsed: number;
        heapTotal: number;
        external: number;
        arrayBuffers: number;
    };
    memoryPressure: number;
    peakMemoryUsage: {
        heapUsed: number;
        rss: number;
        external: number;
    };
    errors: AppError[];
}> {
    // Variables to hold resources that need to be cleaned up
    let bufferPool: EnhancedBufferPool | null = null;
    let resourceStreamProvider: ConsolidatedResourceProvider | null = null;
    let streamPipeline: StreamPipeline | null = null;
    let databaseWriter: DatabaseWriter | null = null;

    // Track errors
    const errors: AppError[] = [];

    // Track statistics
    let packagesProcessed = 0;
    let resourcesProcessed = 0;
    let totalSize = 0;
    let peakHeapUsed = 0;
    let peakRss = 0;
    let peakExternal = 0;

    // Set default options
    const maxPackages = options.maxPackages || 1;
    const progressiveMode = options.progressiveMode || false;
    const memoryCheckInterval = options.memoryCheckInterval || 10; // Check memory every 10 resources

    // Set log level
    Logger.setGlobalLogLevel((options.logLevel || 'info') as any);

    // Initialize hardware detector with simulated profile if specified
    if (options.simulateHardwareProfile) {
        print(`Simulating ${options.simulateHardwareProfile} hardware profile...`);

        // Override hardware detection with simulated profile
        const simulatedHardwareInfo = {
            cpu: {
                cores: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 2 :
                       options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 4 : 8,
                logicalCores: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 2 :
                             options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 4 : 8,
                speed: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 1500 :
                       options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 2500 : 3500,
                model: 'Simulated CPU',
                architecture: 'x64'
            },
            memory: {
                total: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 4 * 1024 * 1024 * 1024 :
                       options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 8 * 1024 * 1024 * 1024 : 16 * 1024 * 1024 * 1024,
                free: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 1 * 1024 * 1024 * 1024 :
                      options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 4 * 1024 * 1024 * 1024 : 8 * 1024 * 1024 * 1024,
                available: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 1 * 1024 * 1024 * 1024 :
                          options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 4 * 1024 * 1024 * 1024 : 8 * 1024 * 1024 * 1024,
                percentAvailable: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 25 :
                                 options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 50 : 50
            },
            disk: {
                readSpeed: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 30 :
                          options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 150 : 500,
                writeSpeed: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 20 :
                           options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 100 : 400,
                type: options.simulateHardwareProfile === HardwareCategory.LOW_END ? 'hdd' :
                      options.simulateHardwareProfile === HardwareCategory.MID_RANGE ? 'ssd' : 'nvme'
            },
            category: options.simulateHardwareProfile,
            timestamp: Date.now()
        };

        // @ts-ignore - Access private property for testing
        hardwareDetector.hardwareInfo = simulatedHardwareInfo;

        // Generate recommendations based on simulated hardware
        const recommendations = hardwareDetector.getRecommendations();
        print(`Hardware recommendations: chunkSize=${formatBytes(recommendations.chunkSize)}, concurrency=${recommendations.concurrency}, bufferSize=${formatBytes(recommendations.bufferSize)}`);
    }

    // Convert single package path to array
    const packagePaths = Array.isArray(packagePath) ? packagePath : [packagePath];

    // Limit to max packages
    const packagesToProcess = packagePaths.slice(0, maxPackages);
    print(`Processing ${packagesToProcess.length} packages out of ${packagePaths.length} available`);

    try {
        // Create enhanced buffer pool
        print(`Creating enhanced buffer pool...`);
        bufferPool = EnhancedBufferPool.getInstance({
            trackResources: true,
            enableAdaptiveSizing: true
        });

        // Create resource stream provider
        print(`Creating resource stream provider...`);
        resourceStreamProvider = new ConsolidatedResourceProvider(bufferPool);

        // Create stream pipeline
        print(`Creating stream pipeline...`);
        streamPipeline = new StreamPipeline({
            emitProgress: true,
            emitStats: true
        });

        // Create database writer if database service is provided and operations are not skipped
        if (databaseService && !options.skipDatabaseOperations) {
            print(`Creating database writer...`);
            databaseWriter = new DatabaseWriter(databaseService, {
                batchSize: 10,
                flushInterval: 1000
            });
        } else {
            print(`Skipping database writer creation (using mock)...`);
            // Create a mock database writer
            databaseWriter = {
                setPackageId: (id: number) => {},
                consume: async (stream: any) => {
                    // Just consume the stream without writing to database
                    return new Promise<void>((resolve) => {
                        stream.on('data', () => {});
                        stream.on('end', () => resolve());
                        stream.on('error', () => resolve());
                    });
                }
            } as any;
        }

        // Set up progress reporting
        streamPipeline.on('progress', (progress) => {
            print(`Progress: ${Math.round(progress.progress * 100)}%`);
        });

        // Set up stats reporting
        streamPipeline.on('stats', (stats) => {
            print(`Stats: bytesProcessed=${stats.bytesProcessed}, resourceType=${stats.resourceType.toString(16)}`);
        });

        // Set up error handling
        streamPipeline.on('error', (error) => {
            print(`Pipeline error: ${error.message}`);
            errors.push(error);
        });

        const startTime = Date.now();

        // Process each package
        for (let packageIndex = 0; packageIndex < packagesToProcess.length; packageIndex++) {
            const currentPackagePath = packagesToProcess[packageIndex];

            // Validate package file
            print(`\nValidating package file (${packageIndex + 1}/${packagesToProcess.length}): ${currentPackagePath}`);

            const fileValidation = await validator.validateFileExists(currentPackagePath, {
                throwOnFailure: false
            });

            if (!fileValidation.valid) {
                print(`Package file validation failed: ${fileValidation.message}`);
                continue; // Skip this package
            }

            // Get file stats
            const stats = await fs.promises.stat(currentPackagePath);
            print(`Package file size: ${formatBytes(stats.size)}`);

            // Read package index
            print(`Reading package index...`);

            // Create a package reader to get the index
            const { StreamingPackageReader } = await import('../../services/analysis/package/streamingPackageReader.js');

            try {
                const packageReader = new StreamingPackageReader(currentPackagePath);
                await packageReader.initialize();

            // Get package index
            const index = packageReader.index;
            print(`Package index contains ${index.entries.length} resources`);

            // Process each resource in the package
            for (let i = 0; i < index.entries.length; i++) {
                const entry = index.entries[i];

                // Check memory usage periodically
                if (i % memoryCheckInterval === 0) {
                    const memUsage = process.memoryUsage();
                    peakHeapUsed = Math.max(peakHeapUsed, memUsage.heapUsed);
                    peakRss = Math.max(peakRss, memUsage.rss);
                    peakExternal = Math.max(peakExternal, memUsage.external);

                    // Log memory usage in progressive mode
                    if (progressiveMode) {
                        print(`\nMemory usage after ${resourcesProcessed} resources:`);
                        print(`Heap used: ${formatBytes(memUsage.heapUsed)} / ${formatBytes(memUsage.heapTotal)} (${(memUsage.heapUsed / memUsage.heapTotal * 100).toFixed(1)}%)`);
                        print(`RSS: ${formatBytes(memUsage.rss)}`);
                        print(`External: ${formatBytes(memUsage.external)}`);
                        print(`Memory pressure: ${(memoryManager.getMemoryPressure() * 100).toFixed(1)}%`);
                    }
                }

                // Skip deleted resources
                if (entry.deleted) {
                    continue;
                }

                print(`\nProcessing resource ${i + 1}/${index.entries.length}: type=${entry.type.toString(16)}, group=${entry.group.toString(16)}, instance=${entry.instanceHi.toString(16)}${entry.instanceLo.toString(16)}`);

                try {
                // Get hardware recommendations
                const recommendations = hardwareDetector.getRecommendations();

                // Create resource stream with adaptive parameters
                let resourceStream;
                try {
                    resourceStream = await packageReader.getResourceStream(entry);
                } catch (streamError: any) {
                    print(`Error creating resource stream: ${streamError.message}`);
                    errors.push(errorHandler.createError(
                        ErrorCode.RESOURCE_STREAM_ERROR,
                        streamError.message,
                        {
                            category: ErrorCategory.RESOURCE,
                            severity: ErrorSeverity.ERROR,
                            cause: streamError,
                            context: {
                                resourceType: entry.type,
                                resourceId: `${entry.type.toString(16)}_${entry.group.toString(16)}_${entry.instanceHi.toString(16)}${entry.instanceLo.toString(16)}`
                            }
                        }
                    ));
                    continue; // Skip to next resource
                }

                // Corrupt data if requested
                if (options.corruptData) {
                    print(`Corrupting resource data for testing...`);
                    const corruptedStream = new Readable({
                        read(size) {
                            // Read from the original stream
                            const chunk = resourceStream.read(size);

                            if (chunk) {
                                // Corrupt the chunk by replacing some bytes
                                const corruptedChunk = Buffer.from(chunk);

                                // Replace 10% of bytes with random values
                                const bytesToCorrupt = Math.floor(corruptedChunk.length * 0.1);
                                for (let i = 0; i < bytesToCorrupt; i++) {
                                    const pos = Math.floor(Math.random() * corruptedChunk.length);
                                    corruptedChunk[pos] = Math.floor(Math.random() * 256);
                                }

                                this.push(corruptedChunk);
                            } else if (chunk === null) {
                                this.push(null);
                            }
                        }
                    });

                    // Use the corrupted stream instead
                    resourceStream.destroy();

                    // Process the resource through the pipeline with throttling
                    try {
                        await adaptiveThrottler.throttle(
                            async () => {
                                try {
                                    const outputStream = await streamPipeline.createPipeline(
                                        entry.type,
                                        corruptedStream,
                                        { maxMemoryUsage: recommendations.bufferSize }
                                    );

                                    // Set package ID for database writer
                                    databaseWriter.setPackageId(packageIndex + 1); // Use package index + 1 as ID

                                    // Consume the output stream
                                    await databaseWriter.consume(outputStream);
                                } catch (pipelineError: any) {
                                    // Handle pipeline errors gracefully
                                    print(`Pipeline error: ${pipelineError.message}`);

                                    // Convert to AppError if needed
                                    if (!(pipelineError instanceof AppError)) {
                                        pipelineError = errorHandler.createError(
                                            ErrorCode.RESOURCE_PROCESSING_ERROR,
                                            pipelineError.message,
                                            {
                                                category: ErrorCategory.RESOURCE,
                                                severity: ErrorSeverity.ERROR,
                                                cause: pipelineError
                                            }
                                        );
                                    }

                                    // Add to errors list
                                    errors.push(pipelineError);

                                    // Rethrow to let throttler handle retries
                                    throw pipelineError;
                                }
                            },
                            {
                                category: 'resource',
                                priority: 1,
                                maxRetries: 1 // Reduce retries for corrupted resources
                            }
                        );
                    } catch (throttleError: any) {
                        // Handle throttler errors gracefully
                        print(`Error processing resource: ${throttleError.message}`);

                        // Convert to AppError if needed
                        if (!(throttleError instanceof AppError)) {
                            throttleError = errorHandler.createError(
                                ErrorCode.RESOURCE_PROCESSING_ERROR,
                                throttleError.message,
                                {
                                    category: ErrorCategory.RESOURCE,
                                    severity: ErrorSeverity.ERROR,
                                    cause: throttleError
                                }
                            );
                        }

                        // Add to errors list
                        errors.push(throttleError);

                        // Continue with next resource
                        continue;
                    }
                } else {
                    // Process the resource through the pipeline with throttling
                    try {
                        await adaptiveThrottler.throttle(
                            async () => {
                                try {
                                    const outputStream = await streamPipeline.createPipeline(
                                        entry.type,
                                        resourceStream,
                                        { maxMemoryUsage: recommendations.bufferSize }
                                    );

                                    // Set package ID for database writer
                                    databaseWriter.setPackageId(packageIndex + 1); // Use package index + 1 as ID

                                    // Consume the output stream
                                    await databaseWriter.consume(outputStream);
                                } catch (pipelineError: any) {
                                    // Handle pipeline errors gracefully
                                    print(`Pipeline error: ${pipelineError.message}`);

                                    // Convert to AppError if needed
                                    if (!(pipelineError instanceof AppError)) {
                                        pipelineError = errorHandler.createError(
                                            ErrorCode.RESOURCE_PROCESSING_ERROR,
                                            pipelineError.message,
                                            {
                                                category: ErrorCategory.RESOURCE,
                                                severity: ErrorSeverity.ERROR,
                                                cause: pipelineError
                                            }
                                        );
                                    }

                                    // Add to errors list
                                    errors.push(pipelineError);

                                    // Rethrow to let throttler handle retries
                                    throw pipelineError;
                                }
                            },
                            {
                                category: 'resource',
                                priority: 1,
                                maxRetries: 1 // Reduce retries for corrupted resources
                            }
                        );
                    } catch (throttleError: any) {
                        // Handle throttler errors gracefully
                        print(`Error processing resource: ${throttleError.message}`);

                        // Convert to AppError if needed
                        if (!(throttleError instanceof AppError)) {
                            throttleError = errorHandler.createError(
                                ErrorCode.RESOURCE_PROCESSING_ERROR,
                                throttleError.message,
                                {
                                    category: ErrorCategory.RESOURCE,
                                    severity: ErrorSeverity.ERROR,
                                    cause: throttleError
                                }
                            );
                        }

                        // Add to errors list
                        errors.push(throttleError);

                        // Continue with next resource
                        continue;
                    }
                }

                // Inject error if requested
                if (options.injectErrors && i % 5 === 0) {
                    print(`Injecting error for testing...`);
                    throw errorHandler.createError(
                        ErrorCode.RESOURCE_CORRUPTED,
                        'Simulated resource corruption',
                        {
                            category: ErrorCategory.RESOURCE,
                            severity: ErrorSeverity.ERROR,
                            context: {
                                resourceType: entry.type,
                                resourceId: `${entry.type.toString(16)}_${entry.group.toString(16)}_${entry.instanceHi.toString(16)}${entry.instanceLo.toString(16)}`
                            }
                        }
                    );
                }

                // Update statistics
                resourcesProcessed++;
                totalSize += entry.fileSize;
            } catch (error: any) {
                // Handle error
                print(`Error processing resource: ${error.message}`);

                // Convert to AppError if needed
                if (!(error instanceof AppError)) {
                    error = errorHandler.createError(
                        ErrorCode.UNKNOWN_ERROR,
                        error.message,
                        {
                            category: ErrorCategory.UNKNOWN,
                            severity: ErrorSeverity.ERROR,
                            cause: error
                        }
                    );
                }

                // Add to errors list
                errors.push(error);

                // Continue with next resource
                continue;
                }
            } // End of resource processing loop

            // Close package reader
            await packageReader.close();
            } catch (error: any) {
                print(`Error processing package: ${error.message}`);
                errors.push(errorHandler.createError(
                    ErrorCode.PACKAGE_PROCESSING_ERROR,
                    error.message,
                    {
                        category: ErrorCategory.PACKAGE,
                        severity: ErrorSeverity.ERROR,
                        cause: error,
                        context: {
                            packagePath: currentPackagePath
                        }
                    }
                ));
                continue; // Skip to next package
            }
        } // End of package processing loop

        // Calculate duration
        let endTime = Date.now();
        let duration = endTime - startTime;

        // Get memory usage
        const memoryUsage = process.memoryUsage();

        // Get resource tracker stats
        const resourceStats = resourceTracker.getStats();
        print(`\nResource tracker stats:`);
        print(`Total resources: ${resourceStats.totalResources}`);
        print(`Active resources: ${resourceStats.activeResources}`);
        print(`Idle resources: ${resourceStats.idleResources}`);
        print(`Total size: ${formatBytes(resourceStats.totalSize)}`);

        // Get buffer pool stats
        const bufferStats = bufferPool.getStats();
        print(`\nBuffer pool stats:`);
        print(`Total buffers: ${bufferStats.totalBuffers}`);
        print(`Active buffers: ${bufferStats.activeBuffers}`);
        print(`Idle buffers: ${bufferStats.idleBuffers}`);
        print(`Total memory: ${formatBytes(bufferStats.totalMemory)}`);

        // Get throttler stats
        const throttlerStats = adaptiveThrottler.getStats();
        print(`\nThrottler stats:`);
        print(`Throttling level: ${throttlerStats.throttlingLevel}`);
        print(`Completed operations: ${throttlerStats.completedOperations}`);
        print(`Throttled operations: ${throttlerStats.throttledOperations}`);

        // Return results
        return {
            success: errors.length === 0,
            packagesProcessed: packagesToProcess.length,
            resourcesProcessed,
            totalSize,
            duration,
            memoryUsage: {
                heapUsed: memoryUsage.heapUsed,
                heapTotal: memoryUsage.heapTotal,
                external: memoryUsage.external,
                arrayBuffers: memoryUsage.arrayBuffers
            },
            memoryPressure: memoryManager.getMemoryPressure(),
            peakMemoryUsage: {
                heapUsed: peakHeapUsed,
                rss: peakRss,
                external: peakExternal
            },
            errors
        };
    } catch (error: any) {
        // Handle error
        print(`Error in streaming pipeline test: ${error.message}`);

        // Convert to AppError if needed
        if (!(error instanceof AppError)) {
            error = errorHandler.createError(
                ErrorCode.UNKNOWN_ERROR,
                error.message,
                {
                    category: ErrorCategory.UNKNOWN,
                    severity: ErrorSeverity.ERROR,
                    cause: error
                }
            );
        }

        // Add to errors list
        errors.push(error);

        // Return error results
        return {
            success: false,
            packagesProcessed,
            resourcesProcessed,
            totalSize,
            duration: 0,
            memoryUsage: process.memoryUsage(),
            memoryPressure: memoryManager.getMemoryPressure(),
            peakMemoryUsage: {
                heapUsed: peakHeapUsed,
                rss: peakRss,
                external: peakExternal
            },
            errors
        };
    } finally {
        // Clean up resources
        try {
            if (databaseWriter) {
                print('Cleaning up database writer...');
                await databaseWriter.cleanup();
            }

            if (streamPipeline) {
                print('Cleaning up stream pipeline...');
                await streamPipeline.destroy();
            }

            if (resourceStreamProvider) {
                print('Cleaning up resource stream provider...');
                await resourceStreamProvider.cleanup();
            }

            if (bufferPool) {
                print('Cleaning up buffer pool...');
                bufferPool.dispose();
            }
        } catch (cleanupError: any) {
            print(`Error during cleanup: ${cleanupError.message}`);
        }
    }
}

/**
 * Run progressive streaming pipeline tests with increasing numbers of package files
 * @param packagePaths Array of package file paths
 * @param databaseService Database service
 * @param options Test options
 */
export async function runProgressiveStreamingTests(
    packagePaths: string[],
    databaseService?: any,
    options: {
        simulateHardwareProfile?: HardwareCategory;
        injectErrors?: boolean;
        corruptData?: boolean;
        logLevel?: string;
        progressiveMode?: boolean;
        memoryCheckInterval?: number;
        packageCounts?: number[];
        waitBetweenTests?: number;
        skipDatabaseOperations?: boolean;
    } = {}
): Promise<{
    results: Array<{
        packageCount: number;
        result: {
            success: boolean;
            packagesProcessed: number;
            resourcesProcessed: number;
            totalSize: number;
            duration: number;
            memoryUsage: {
                heapUsed: number;
                heapTotal: number;
                external: number;
                arrayBuffers: number;
            };
            memoryPressure: number;
            peakMemoryUsage: {
                heapUsed: number;
                rss: number;
                external: number;
            };
            errors: AppError[];
        };
    }>;
    summary: {
        totalPackagesProcessed: number;
        totalResourcesProcessed: number;
        totalSize: number;
        totalDuration: number;
        averageMemoryPressure: number;
        peakMemoryUsage: {
            heapUsed: number;
            rss: number;
            external: number;
        };
        totalErrors: number;
        successRate: number;
    };
}> {
    // Default package counts if not provided
    const packageCounts = options.packageCounts || [5, 10, 25, 50, 100];

    // Default wait time between tests (5 seconds)
    const waitBetweenTests = options.waitBetweenTests || 5000;

    // Results array
    const results: Array<{
        packageCount: number;
        result: any;
    }> = [];

    // Summary statistics
    let totalPackagesProcessed = 0;
    let totalResourcesProcessed = 0;
    let totalSize = 0;
    let totalDuration = 0;
    let totalMemoryPressure = 0;
    let peakHeapUsed = 0;
    let peakRss = 0;
    let peakExternal = 0;
    let totalErrors = 0;
    let successfulTests = 0;

    // Print header
    print(`\n===== PROGRESSIVE STREAMING PIPELINE TESTS =====`);
    print(`Will test with increasing numbers of packages: ${packageCounts.join(', ')}`);
    print(`Hardware profile: ${options.simulateHardwareProfile || 'auto-detect'}`);
    print(`Inject errors: ${options.injectErrors ? 'Yes' : 'No'}`);
    print(`Corrupt data: ${options.corruptData ? 'Yes' : 'No'}`);

    // Run tests for each package count
    for (const packageCount of packageCounts) {
        // Print test header
        print(`\n----- Testing with ${packageCount} packages -----`);

        // Force garbage collection before test if available
        if (global.gc) {
            print(`Running garbage collection before test...`);
            global.gc();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for GC to complete
        }

        // Log memory usage before test
        print(`Memory usage before test:`);
        const memoryManager = EnhancedMemoryManager.getInstance();
        memoryManager.logMemoryUsage();

        // Select packages for this test
        const packagesToTest = packagePaths.slice(0, packageCount);

        // Run the test
        const testResult = await testStreamingPipeline(
            packagesToTest,
            databaseService,
            {
                simulateHardwareProfile: options.simulateHardwareProfile,
                injectErrors: options.injectErrors,
                corruptData: options.corruptData,
                logLevel: options.logLevel,
                progressiveMode: options.progressiveMode,
                memoryCheckInterval: options.memoryCheckInterval,
                maxPackages: packageCount, // Use all selected packages
                skipDatabaseOperations: options.skipDatabaseOperations
            }
        );

        // Add result to results array
        results.push({
            packageCount,
            result: testResult
        });

        // Update summary statistics
        totalPackagesProcessed += testResult.packagesProcessed;
        totalResourcesProcessed += testResult.resourcesProcessed;
        totalSize += testResult.totalSize;
        totalDuration += testResult.duration;
        totalMemoryPressure += testResult.memoryPressure;
        peakHeapUsed = Math.max(peakHeapUsed, testResult.peakMemoryUsage.heapUsed);
        peakRss = Math.max(peakRss, testResult.peakMemoryUsage.rss);
        peakExternal = Math.max(peakExternal, testResult.peakMemoryUsage.external);
        totalErrors += testResult.errors.length;
        if (testResult.success) {
            successfulTests++;
        }

        // Log memory usage after test
        print(`\nMemory usage after test with ${packageCount} packages:`);
        memoryManager.logMemoryUsage();

        // Wait between tests to allow for memory stabilization
        if (packageCount !== packageCounts[packageCounts.length - 1]) {
            print(`Waiting for memory stabilization (${waitBetweenTests / 1000} seconds)...`);
            await new Promise(resolve => setTimeout(resolve, waitBetweenTests));
        }
    }

    // Calculate average memory pressure
    const averageMemoryPressure = totalMemoryPressure / packageCounts.length;

    // Calculate success rate
    const successRate = (successfulTests / packageCounts.length) * 100;

    // Print summary
    print(`\n===== PROGRESSIVE TESTS SUMMARY =====`);
    print(`Total packages processed: ${totalPackagesProcessed}`);
    print(`Total resources processed: ${totalResourcesProcessed}`);
    print(`Total data processed: ${formatBytes(totalSize)}`);
    print(`Total duration: ${formatDuration(totalDuration)}`);
    print(`Average memory pressure: ${averageMemoryPressure.toFixed(2)}%`);
    print(`Peak memory usage:`);
    print(`  Heap used: ${formatBytes(peakHeapUsed)}`);
    print(`  RSS: ${formatBytes(peakRss)}`);
    print(`  External: ${formatBytes(peakExternal)}`);
    print(`Total errors: ${totalErrors}`);
    print(`Success rate: ${successRate.toFixed(2)}%`);

    // Return results and summary
    return {
        results,
        summary: {
            totalPackagesProcessed,
            totalResourcesProcessed,
            totalSize,
            totalDuration,
            averageMemoryPressure,
            peakMemoryUsage: {
                heapUsed: peakHeapUsed,
                rss: peakRss,
                external: peakExternal
            },
            totalErrors,
            successRate
        }
    };
}

// No default export needed
