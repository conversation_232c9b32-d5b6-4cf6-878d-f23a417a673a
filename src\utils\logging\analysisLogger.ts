﻿import { createLogger, LoggerInterface } from './loggerConfig.js';

/**
 * Utility class for logging during analysis operations
 * Provides methods for logging different types of messages with consistent formatting
 */
export class AnalysisLogger {
  private resourceId: string;
  private resourceType: string;
  private isDebugEnabled: boolean;
  private logger: LoggerInterface;

  /**
   * Creates a new AnalysisLogger instance
   * @param resourceId The ID of the resource being analyzed
   * @param resourceType The type of the resource being analyzed
   * @param isDebugEnabled Whether debug logging is enabled
   */
  constructor(resourceId: string, resourceType: string, isDebugEnabled: boolean = false) {
    this.resourceId = resourceId;
    this.resourceType = resourceType;
    this.isDebugEnabled = isDebugEnabled;
    this.logger = createLogger(`Analysis:${resourceType}`);
  }

  /**
   * Logs an informational message
   * @param message The message to log
   * @param data Optional data to include with the message
   */
  public info(message: string, data?: unknown): void {
    this.logger.info(`[${this.resourceId}] ${message}`, data);
  }

  /**
   * Logs a warning message
   * @param message The message to log
   * @param data Optional data to include with the message
   */
  public warn(message: string, data?: unknown): void {
    this.logger.warn(`[${this.resourceId}] ${message}`, data);
  }

  /**
   * Logs an error message
   * @param message The message to log
   * @param error Optional error to include with the message
   */
  public error(message: string, error?: unknown): void {
    if (error instanceof Error) {
      this.logger.error(`[${this.resourceId}] ${message}`, {
        errorName: error.name,
        errorMessage: error.message,
        stack: error.stack,
      });
    } else {
      this.logger.error(`[${this.resourceId}] ${message}`, error);
    }
  }

  /**
   * Logs a debug message if debug logging is enabled
   * @param message The message to log
   * @param data Optional data to include with the message
   */
  public debug(message: string, data?: unknown): void {
    if (this.isDebugEnabled) {
      this.logger.debug(`[${this.resourceId}] ${message}`, data);
    }
  }

  /**
   * Logs the start of an analysis operation
   * @param operationName The name of the operation
   */
  public startOperation(operationName: string): void {
    this.info(`Starting ${operationName}`);
  }

  /**
   * Logs the end of an analysis operation
   * @param operationName The name of the operation
   * @param durationMs Optional duration of the operation in milliseconds
   */
  public endOperation(operationName: string, durationMs?: number): void {
    if (durationMs !== undefined) {
      this.info(`Completed ${operationName} in ${durationMs}ms`);
    } else {
      this.info(`Completed ${operationName}`);
    }
  }

  /**
   * Creates a timer for measuring operation duration
   * @param operationName The name of the operation to time
   * @returns A function that, when called, logs the end of the operation with duration
   */
  public timeOperation(operationName: string): () => void {
    const startTime = Date.now();
    this.startOperation(operationName);

    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      this.endOperation(operationName, duration);
    };
  }

  /**
   * Logs an extraction result
   * @param propertyName The name of the property that was extracted
   * @param value The extracted value
   */
  public logExtraction(propertyName: string, value: unknown): void {
    if (this.isDebugEnabled) {
      this.debug(`Extracted ${propertyName}:`, value);
    }
  }

  /**
   * Logs a validation failure
   * @param propertyName The name of the property that failed validation
   * @param value The invalid value
   * @param reason The reason for the validation failure
   */
  public logValidationFailure(propertyName: string, value: unknown, reason: string): void {
    this.warn(`Invalid ${propertyName} (${reason}):`, value);
  }
}

