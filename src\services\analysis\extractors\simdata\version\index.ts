/**
 * SimData Version Handler
 * Provides specialized parsing for different SimData binary formats
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../../databaseService.js';
import { ParsedSimData, VersionHandlerFunction } from './types.js';
import { getAllVersionHandlers, handleGenericVersion, createGenericVersionHandler } from './handlers/index.js';
import { detectVersion } from './detection/index.js';
import { VersionRegistry } from './registry/index.js';
import { createVersionErrorContext, handleVersionError } from './error/index.js';

const logger = new Logger('SimDataVersionHandler');

/**
 * SimData Version Handler
 * Provides specialized parsing for different SimData binary formats
 */
export class SimDataVersionHandler {
    private static versionHandlers: Map<number, VersionHandlerFunction> = new Map();
    private static registry: VersionRegistry | null = null;
    private static databaseService: DatabaseService | null = null;
    private static initialized: boolean = false;

    /**
     * Initialize the SimDataVersionHandler with a database service
     * @param databaseService The database service to use for version tracking
     */
    public static async initialize(databaseService: DatabaseService): Promise<void> {
        if (this.initialized) return;

        try {
            this.databaseService = databaseService;
            this.registry = VersionRegistry.getInstance(databaseService);
            await this.registry.initialize();

            // Register all handlers
            this.versionHandlers = getAllVersionHandlers();

            logger.info(`SimDataVersionHandler initialized with ${this.versionHandlers.size} version handlers`);

            // Check for unknown versions that might need handlers
            await this.checkForUnknownVersions();

            this.initialized = true;
        } catch (error) {
            handleVersionError(
                error,
                createVersionErrorContext(undefined, 'SimDataVersionHandler.initialize'),
                undefined
            );
        }
    }

    /**
     * Check for unknown versions that might need handlers
     */
    private static async checkForUnknownVersions(): Promise<void> {
        if (!this.registry) return;

        try {
            const unknownVersions = this.registry.getUnknownVersions();
            if (unknownVersions.length > 0) {
                logger.info(`Found ${unknownVersions.length} unknown SimData versions that might need handlers`);

                // Register handlers for the most common unknown versions
                for (const versionInfo of unknownVersions.slice(0, 5)) {
                    if (!this.versionHandlers.has(versionInfo.version)) {
                        logger.info(`Automatically registering handler for unknown version ${versionInfo.version} (seen ${versionInfo.count} times)`);
                        this.registerHandler(versionInfo.version, createGenericVersionHandler(versionInfo.version));
                    }
                }
            }
        } catch (error) {
            handleVersionError(
                error,
                createVersionErrorContext(undefined, 'SimDataVersionHandler.checkForUnknownVersions'),
                undefined
            );
        }
    }

    /**
     * Register a handler for a specific SimData version
     * @param version SimData version number
     * @param handler Function to handle parsing for this version
     * @param isStandard Whether this is a standard version
     * @param isSpecial Whether this is a special version
     */
    public static registerHandler(
        version: number,
        handler: VersionHandlerFunction,
        isStandard: boolean = false,
        isSpecial: boolean = false
    ): void {
        this.versionHandlers.set(version, handler);

        // Register with the version registry if available
        if (this.registry) {
            this.registry.registerVersion(
                version,
                'Unknown', // Schema name will be updated when parsing actual data
                'Unknown', // Mod name will be updated when parsing actual data
                isStandard,
                isSpecial,
                true // Has custom handler
            ).catch(error => {
                logger.error(`Error registering version ${version} with registry: ${error}`);
            });
        }
    }

    /**
     * Check if a handler exists for a specific SimData version
     * @param version SimData version number
     * @returns True if a handler exists for this version
     */
    public static canHandle(version: number): boolean {
        return this.versionHandlers.has(version);
    }

    /**
     * Parse a SimData buffer using the appropriate handler for its version
     * @param version SimData version number
     * @param buffer SimData buffer
     * @param modName Optional mod name for tracking
     * @returns Parsed SimData or undefined if parsing fails
     */
    public static handleVersion(version: number, buffer: Buffer, modName: string = 'Unknown'): ParsedSimData | undefined {
        try {
            const handler = this.versionHandlers.get(version);
            if (handler) {
                const result = handler(buffer);

                // Track version usage in registry if available
                if (result && this.registry) {
                    const schemaName = result.schema?.name || 'Unknown';
                    this.registry.registerVersion(
                        version,
                        schemaName,
                        modName,
                        version >= 1 && version <= 20, // isStandard
                        version === 16708 || version === 48111, // isSpecial
                        true // hasCustomHandler
                    ).catch(error => {
                        logger.error(`Error tracking version ${version} usage: ${error}`);
                    });
                }

                return result;
            }

            // If no handler exists but we have a registry, register this unknown version
            if (this.registry) {
                this.registry.registerVersion(
                    version,
                    'Unknown',
                    modName,
                    false,
                    false,
                    false
                ).catch(error => {
                    logger.error(`Error registering unknown version ${version}: ${error}`);
                });
            }

            // Try to handle with generic handler
            logger.warn(`No handler found for version ${version}, using generic handler`);
            return handleGenericVersion(buffer);
        } catch (error) {
            return handleVersionError(
                error,
                createVersionErrorContext(version, 'SimDataVersionHandler.handleVersion', { modName }),
                undefined
            );
        }
    }

    /**
     * Detect and handle a SimData buffer
     * @param buffer SimData buffer
     * @param modName Optional mod name for tracking
     * @returns Parsed SimData or undefined if parsing fails
     */
    public static detectAndHandle(buffer: Buffer, modName: string = 'Unknown'): ParsedSimData | undefined {
        try {
            const version = detectVersion(buffer);
            if (version === undefined) {
                logger.error('Failed to detect SimData version');
                return undefined;
            }

            return this.handleVersion(version, buffer, modName);
        } catch (error) {
            return handleVersionError(
                error,
                createVersionErrorContext(undefined, 'SimDataVersionHandler.detectAndHandle', { modName }),
                undefined
            );
        }
    }

    /**
     * Register a new version handler
     * This can be called by external code to add support for new versions
     * @param version SimData version number
     * @param handler Function to handle parsing for this version
     */
    public static registerCustomHandler(version: number, handler: VersionHandlerFunction): void {
        logger.info(`Registering custom handler for SimData version ${version}`);
        this.registerHandler(version, handler);
    }

    /**
     * Detect the version of a SimData buffer
     * @param buffer SimData buffer
     * @returns SimData version or undefined if detection fails
     */
    public static detectVersion(buffer: Buffer): number | undefined {
        try {
            return detectVersion(buffer);
        } catch (error) {
            return handleVersionError(
                error,
                createVersionErrorContext(undefined, 'SimDataVersionHandler.detectVersion'),
                undefined
            );
        }
    }
}

// Export types and utilities
export * from './types.js';
export * from './handlers/index.js';
export * from './detection/index.js';
export * from './registry/index.js';
export * from './utils/index.js';
export * from './error/index.js';
