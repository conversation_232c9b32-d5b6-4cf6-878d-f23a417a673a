import { Logger } from '../../utils/logging/logger.js';
import { ConflictSeverity } from '../../types/conflict/index.js';
import { resourceTypeRegistry } from '../../utils/resource/resourceTypeRegistry.js';

/**
 * Options for conflict severity calculation
 */
export interface ConflictSeverityCalculatorOptions {
    /**
     * Custom severity mappings for resource types
     * Key: Resource type (number)
     * Value: Severity level (ConflictSeverity)
     */
    customSeverityMappings?: Map<number, ConflictSeverity>;

    /**
     * Default severity for resource types not explicitly mapped
     * Default: ConflictSeverity.MEDIUM
     */
    defaultSeverity?: ConflictSeverity;

    /**
     * Whether to adjust severity based on conflict type
     * Default: true
     */
    adjustForConflictType?: boolean;

    /**
     * Whether to adjust severity based on resource purpose
     * Default: true
     */
    adjustForResourcePurpose?: boolean;
}

/**
 * Calculator for determining the severity of conflicts
 */
export class ConflictSeverityCalculator {
    private logger: Logger;
    private options: ConflictSeverityCalculatorOptions;

    // Critical resource types (conflicts always critical)
    private criticalTypes = [
        0x220557DA, // Tuning XML
        0x545AC67A, // SimData
        0x03B33DDF, // OBJD (Object Definition)
        0x0166038C, // STBL (String Table)
        0x00B2D882, // TXTR (Texture)
        0xE882D22F  // THUM (Thumbnail)
    ];

    // High severity resource types
    private highSeverityTypes = [
        0x0C772E27, // MODL (Model)
        0x736884F1, // MLOD (Model LOD)
        0x01661233, // CASP (CAS Part)
        0x319E4F1D, // BGEO (Bone Geometry)
        0x00AE6C67, // BOND (Bone Data)
        0x02D5DF13, // GEOM (Geometry)
        0x0333406C, // SIMO (Sim Outfit)
        0x025ED6F4  // MTST (Material Set)
    ];

    // Medium severity resource types
    private mediumSeverityTypes = [
        0x0A5DC6B9, // VFX_MODIFIER
        0x0A5DC6BA, // VFX_STATE
        0xEA5118B0, // VISUAL_EFFECT
        0x1B192049, // NEW_VISUAL_EFFECT
        0x1B19204A, // VFX_MAPPING_TABLE
        0x03B4C61D, // LGHT (Light)
        0xD3044521, // SLOT (Slot)
        0x5B282D45, // TRNG (Terrain Geometry)
        0x0C1FE246, // TRNP (Terrain Paint)
        0x3C1AF1F2, // OBJC (Object Catalog)
        0xBA856C78  // MODL (Modular Part)
    ];

    /**
     * Create a new conflict severity calculator
     * @param options Options for conflict severity calculation
     * @param logger Optional logger instance
     */
    constructor(
        options: ConflictSeverityCalculatorOptions = {},
        logger?: Logger
    ) {
        this.logger = logger || new Logger('ConflictSeverityCalculator');
        
        // Set default options
        this.options = {
            customSeverityMappings: options.customSeverityMappings || new Map(),
            defaultSeverity: options.defaultSeverity || ConflictSeverity.MEDIUM,
            adjustForConflictType: options.adjustForConflictType !== false,
            adjustForResourcePurpose: options.adjustForResourcePurpose !== false
        };
        
        this.logger.debug(`ConflictSeverityCalculator initialized with options: ${JSON.stringify({
            defaultSeverity: this.options.defaultSeverity,
            adjustForConflictType: this.options.adjustForConflictType,
            adjustForResourcePurpose: this.options.adjustForResourcePurpose,
            customMappingsCount: this.options.customSeverityMappings?.size || 0
        })}`);
    }

    /**
     * Calculate the severity of a conflict based on the resource type and conflict type
     * @param resourceType The resource type
     * @param conflictType The type of conflict (exact, partial, group, instance)
     * @returns The conflict severity
     */
    calculateSeverity(
        resourceType: number,
        conflictType: 'exact' | 'partial' | 'group' | 'instance' = 'exact'
    ): ConflictSeverity {
        // Check for custom severity mapping
        if (this.options.customSeverityMappings?.has(resourceType)) {
            const customSeverity = this.options.customSeverityMappings.get(resourceType);
            if (customSeverity) {
                // If adjusting for conflict type is disabled, return the custom severity directly
                if (!this.options.adjustForConflictType) {
                    return customSeverity;
                }
                
                // Otherwise, adjust the custom severity based on conflict type
                return this.adjustSeverityForConflictType(customSeverity, conflictType);
            }
        }
        
        // Determine base severity based on resource type
        let baseSeverity: ConflictSeverity;
        
        if (this.criticalTypes.includes(resourceType)) {
            baseSeverity = ConflictSeverity.CRITICAL;
        } else if (this.highSeverityTypes.includes(resourceType)) {
            baseSeverity = ConflictSeverity.HIGH;
        } else if (this.mediumSeverityTypes.includes(resourceType)) {
            baseSeverity = ConflictSeverity.MEDIUM;
        } else {
            baseSeverity = this.options.defaultSeverity;
        }
        
        // Adjust severity based on conflict type if enabled
        if (this.options.adjustForConflictType) {
            return this.adjustSeverityForConflictType(baseSeverity, conflictType);
        }
        
        return baseSeverity;
    }

    /**
     * Adjust severity based on conflict type
     * @param baseSeverity The base severity
     * @param conflictType The type of conflict
     * @returns The adjusted severity
     */
    private adjustSeverityForConflictType(
        baseSeverity: ConflictSeverity,
        conflictType: 'exact' | 'partial' | 'group' | 'instance'
    ): ConflictSeverity {
        // For exact matches, use the base severity
        if (conflictType === 'exact') {
            return baseSeverity;
        }
        
        // For partial matches and instance conflicts, reduce severity by one level
        if (conflictType === 'partial' || conflictType === 'instance') {
            return this.reduceSeverity(baseSeverity, 1);
        }
        
        // For group conflicts, reduce severity by two levels
        if (conflictType === 'group') {
            return this.reduceSeverity(baseSeverity, 2);
        }
        
        // Default to base severity
        return baseSeverity;
    }

    /**
     * Reduce severity by a specified number of levels
     * @param severity The original severity
     * @param levels Number of levels to reduce
     * @returns The reduced severity
     */
    private reduceSeverity(severity: ConflictSeverity, levels: number): ConflictSeverity {
        const severityLevels = [
            ConflictSeverity.CRITICAL,
            ConflictSeverity.HIGH,
            ConflictSeverity.MEDIUM,
            ConflictSeverity.LOW
        ];
        
        const currentIndex = severityLevels.indexOf(severity);
        if (currentIndex === -1) {
            return severity; // Unknown severity, return as is
        }
        
        const newIndex = Math.min(severityLevels.length - 1, currentIndex + levels);
        return severityLevels[newIndex];
    }

    /**
     * Get a human-readable name for a resource type
     * @param resourceType The resource type
     * @returns The resource type name
     */
    getResourceTypeName(resourceType: number): string {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resourceType);
        return resourceTypeInfo?.name || `0x${resourceType.toString(16).toUpperCase()}`;
    }
}
