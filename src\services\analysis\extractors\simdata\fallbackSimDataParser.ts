/**
 * Fallback SimData Parser
 * Provides a simple parser for SimData buffers when S4TK fails
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { SimDataSchema, SimDataInstance, ParsedSimData } from './simDataParser.js';

const log = new Logger('FallbackSimDataParser');

/**
 * Fallback SimData Parser class
 * Provides methods for parsing SimData buffers when S4TK fails
 */
export class FallbackSimDataParser {
    private logger: Logger;

    constructor(logger?: Logger) {
        this.logger = logger || log;
    }

    /**
     * Parse a SimData buffer using a fallback approach
     * @param buffer SimData buffer
     * @returns Parsed SimData or undefined if parsing fails
     */
    public parse(buffer: Buffer): ParsedSimData | undefined {
        if (!buffer || buffer.length < 8) {
            this.logger.warn('Buffer too small for SimData');
            return undefined;
        }

        try {
            // Try to extract basic information from the buffer
            const version = buffer.readUInt16LE(0);
            const flags = buffer.readUInt16LE(2);

            this.logger.debug(`Fallback parser detected version: ${version}, flags: ${flags}`);

            // Try to extract schema name
            let schemaName = 'Unknown';
            let schemaId = 0;
            let schemaHash = 0;

            // Schema name length is at offset 4 (uint16)
            if (buffer.length >= 6) {
                const nameLength = buffer.readUInt16LE(4);
                
                // Check if name length is reasonable
                if (nameLength > 0 && nameLength < 100 && buffer.length >= 6 + nameLength) {
                    // Try to read the schema name
                    schemaName = buffer.toString('utf8', 6, 6 + nameLength);
                    
                    // Try to extract schema ID and hash if available
                    if (buffer.length >= 6 + nameLength + 8) {
                        schemaId = buffer.readUInt32LE(6 + nameLength);
                        schemaHash = buffer.readUInt32LE(6 + nameLength + 4);
                    }
                }
            }

            // Create a minimal schema
            const schema: SimDataSchema = {
                name: schemaName,
                schemaId: schemaId,
                hash: schemaHash,
                columns: [],
                version: version,
                flags: flags
            };

            // Try to extract column information
            if (schemaName !== 'Unknown' && buffer.length > 100) {
                // Column count might be after schema name and ID/hash
                const possibleColumnCountOffset = 6 + schemaName.length + 8;
                
                if (buffer.length >= possibleColumnCountOffset + 4) {
                    const possibleColumnCount = buffer.readUInt32LE(possibleColumnCountOffset);
                    
                    // Check if column count is reasonable
                    if (possibleColumnCount > 0 && possibleColumnCount < 100) {
                        this.logger.debug(`Detected ${possibleColumnCount} columns`);
                        
                        // Add placeholder columns
                        for (let i = 0; i < possibleColumnCount; i++) {
                            schema.columns.push({
                                name: `Column_${i}`,
                                type: 0,
                                flags: 0
                            });
                        }
                    }
                }
            }

            // Create a minimal instance
            const instance: SimDataInstance = {
                name: 'Instance_0',
                instanceId: 0,
                values: {}
            };

            // Return the parsed SimData
            return {
                schema,
                instances: [instance],
                version,
                flags
            };
        } catch (error) {
            this.logger.error(`Error in fallback SimData parser: ${error}`);
            return undefined;
        }
    }
}
