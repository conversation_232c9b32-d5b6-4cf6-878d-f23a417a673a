/**
 * Types and interfaces for image extraction
 */

import { ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { DependencyInfo } from '../../../../types/database.js';

/**
 * Image format types supported by the extractor
 */
export enum ImageFormat {
    UNKNOWN = 'Unknown',
    DDS = 'DDS',
    PNG = 'PNG',
    JPEG = 'JPEG',
    RLE2 = 'RLE2'
}

/**
 * Image compression formats
 */
export enum CompressionFormat {
    UNCOMPRESSED = 'Uncompressed',
    DXT1 = 'DXT1',
    DXT3 = 'DXT3',
    DXT5 = 'DXT5'
}

/**
 * Image header information based on common image formats
 * This is a simplified representation for metadata extraction
 */
export interface ImageHeaderInfo {
    format: ImageFormat;        // Format identifier (e.g., "DDS", "PNG", "JPG")
    width?: number;             // Image width in pixels
    height?: number;            // Image height in pixels
    mipMapCount?: number;       // Number of mipmaps (for DDS)
    compression?: string;       // Compression format
    hasAlpha?: boolean;         // Whether the image has an alpha channel
    bitsPerPixel?: number;      // Bits per pixel
}

/**
 * Image metadata specific to DDS format
 */
export interface DDSMetadata extends ImageHeaderInfo {
    format: ImageFormat.DDS;
    mipMapCount: number;
    compression: string;
    hasAlpha: boolean;
    bitsPerPixel: number;
}

/**
 * Image metadata specific to PNG format
 */
export interface PNGMetadata extends ImageHeaderInfo {
    format: ImageFormat.PNG;
    hasAlpha: boolean;
    bitsPerPixel: number;
}

/**
 * Image metadata specific to JPEG format
 */
export interface JPEGMetadata extends ImageHeaderInfo {
    format: ImageFormat.JPEG;
    hasAlpha: boolean; // Always false for JPEG
}

/**
 * Image metadata specific to RLE2 format (Sims 4 specific)
 */
export interface RLE2Metadata extends ImageHeaderInfo {
    format: ImageFormat.RLE2;
    hasAlpha: boolean; // Typically true for RLE2
}

/**
 * Result of image parsing
 */
export interface ImageParseResult {
    header: ImageHeaderInfo;
    contentSnippet: string;
    dependencies: DependencyInfo[];
    error?: string;
}

/**
 * Error context for image extraction
 */
export interface ImageErrorContext {
    resourceId: number;
    instanceId: string;
    format?: ImageFormat;
    operation: string;
    additionalInfo?: Record<string, any>;
}

/**
 * Image extractor options
 */
export interface ImageExtractorOptions {
    extractDependencies?: boolean;
    saveToDB?: boolean;
    generateContentSnippet?: boolean;
}

/**
 * Image metadata for database storage
 */
export interface ImageDatabaseMetadata {
    resourceId: number;
    format: string;
    width?: number;
    height?: number;
    mipMapCount?: number;
    compression?: string;
    hasAlpha?: boolean;
    bitsPerPixel?: number;
}

/**
 * Extended ResourceMetadata with image-specific fields
 */
export interface ImageResourceMetadata extends Partial<ResourceMetadata> {
    imageFormat?: string;
    imageWidth?: number;
    imageHeight?: number;
    imageMipMapCount?: number;
    imageCompression?: string;
    imageHasAlpha?: boolean;
    imageBitsPerPixel?: number;
}
