<template>
  <div v-if="!isAnalyzing && (hasResults || hasError)" class="results-container mt-4"> <!-- Use Vuetify spacing -->
    <!-- Use Vuetify Card -->
    <v-card class="results-card">
      <v-card-text>
        <p class="text-h6 mb-3">Analysis Results</p> <!-- Vuetify typography -->

        <!-- Error Display using Vuetify Alert -->
        <v-alert v-if="hasError" type="error" density="compact" class="mb-4">
          {{ error }}
        </v-alert>

        <!-- Results Display -->
        <div v-else>
          <!-- Recommendations using Vuetify Expansion Panels -->
          <v-expansion-panels variant="inset" class="mb-4" multiple>
            <v-expansion-panel eager title="Recommendations">
              <v-expansion-panel-text>
                <ul v-if="recommendations.length" class="pl-4">
                  <li v-for="(rec, index) in recommendations" :key="`rec-${index}`">
                    <p class="text-body-2">{{ rec }}</p>
                  </li>
                </ul>
                <p v-else class="text-body-2">No specific recommendations provided.</p>
              </v-expansion-panel-text>
            </v-expansion-panel>

            <!-- Conflicts Table using Vuetify Table -->
            <v-expansion-panel eager :title="`Detected Conflicts (${conflicts.length})`">
              <v-expansion-panel-text>
                <v-table v-if="conflicts.length > 0" density="compact">
                  <thead>
                    <tr>
                      <th class="text-left">Severity</th>
                      <th class="text-left">Type</th>
                      <th class="text-left">Description</th>
                      <th class="text-left">Affected Resources</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="conflict in conflicts" :key="conflict.id">
                      <td>
                        <!-- Use Vuetify Chip for severity -->
                        <v-chip :color="getSeverityChipColor(conflict.severity)" size="small" label>
                          {{ conflict.severity }}
                        </v-chip>
                      </td>
                      <td>{{ conflict.type }}</td>
                      <td><p class="text-body-2" style="white-space: pre-wrap;">{{ conflict.description }}</p></td>
                      <td>
                        <ul class="pl-1">
                          <li v-for="res in conflict.affectedResources" :key="res.id || `${res.type}-${res.group}-${res.instance}`">
                            <p class="text-caption">
                              T:{{ res.type.toString(16) }} G:{{ res.group.toString(16) }} I:{{ res.instance.toString(16) }} ({{ res.path ? getFileName(res.path) : 'N/A' }})
                            </p>
                          </li>
                        </ul>
                      </td>
                    </tr>
                  </tbody>
                </v-table>
                <p v-else class="text-body-2">No conflicts detected.</p>
              </v-expansion-panel-text>
            </v-expansion-panel>

            <!-- Metrics using Vuetify Expansion Panels -->
            <v-expansion-panel eager title="Analysis Metrics">
              <v-expansion-panel-text>
                <ul class="pl-4">
                  <li v-for="(value, key) in metrics" :key="`metric-${key}`">
                    <p class="text-body-2">
                      <strong>{{ formatMetricKey(key) }}:</strong> {{ value }}
                    </p>
                  </li>
                </ul>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAnalysisStore } from '../store/analysis';
import { ConflictSeverity } from '../../../types/conflict/ConflictTypes'; // Corrected path yet again
// Vuetify components are globally available via the plugin

const analysisStore = useAnalysisStore();

// Computed properties from store
const isAnalyzing = computed(() => analysisStore.isAnalyzing);
const conflicts = computed(() => analysisStore.conflicts);
const recommendations = computed(() => analysisStore.recommendations);
const metrics = computed(() => analysisStore.metrics);
const error = computed(() => analysisStore.error);
const hasError = computed(() => analysisStore.hasError);

const hasResults = computed(() => conflicts.value.length > 0 || recommendations.value.length > 0 || Object.keys(metrics.value).length > 0);

// Helper function to map severity to MUI Chip color
const getSeverityChipColor = (severity: ConflictSeverity): "error" | "warning" | "info" | "success" | "default" => {
  const upperSeverity = severity?.toUpperCase() ?? 'UNKNOWN';
  switch (upperSeverity) {
    case 'CRITICAL':
    case 'HIGH':
      return 'error'; // MUI uses 'error' for danger
    case 'MEDIUM':
      return 'warning';
    case 'LOW':
      return 'info';
    case 'NONE':
      return 'success';
    default:
      return 'default'; // MUI uses 'default' for secondary/unknown
  }
};

// Helper to format metric keys for display
const formatMetricKey = (key: string): string => {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase());
};

// Helper to extract filename from path
const getFileName = (filePath: string): string => {
  if (!filePath) return 'N/A';
  return filePath.split(/[\\/]/).pop() || filePath;
};

</script>

<style scoped>
.results-container {
  text-align: left;
}
.results-card {
   margin-bottom: 1.5rem;
}
.panel { /* Style for Accordion */
  margin-bottom: 1rem;
  /* Remove default borders if using MUI elevation */
  /* border: 1px solid #dee2e6; */
  /* background-color: #fff; */
}
.p-mb-3 { /* Compatibility class */
  margin-bottom: 1rem;
}
.p-mt-3 { /* Compatibility class */
   margin-top: 1.5rem;
}
ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
li {
  margin-bottom: 0.3rem;
}
/* Remove deep selectors if not needed */
/* :deep(.p-datatable .p-datatable-thead > tr > th) { ... } */
/* :deep(.p-panel .p-panel-header) { ... } */

/* Add specific styles for MUI Table if needed */
.conflict-table th {
  font-weight: bold;
}
</style>
