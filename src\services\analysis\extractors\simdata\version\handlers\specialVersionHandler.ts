/**
 * Handler for special SimData versions (16708, 48111)
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { ParsedSimData, VersionHandlerFunction } from '../types.js';
import { parseVersion16708, parseVersion48111 } from '../../parsers/specialVersionParser.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('SpecialVersionHandler');

/**
 * Handler for SimData version 16708
 * @param buffer SimData buffer
 * @returns Parsed SimData or undefined if parsing fails
 */
export function handleVersion16708(buffer: Buffer): ParsedSimData | undefined {
    try {
        logger.info('Handling special SimData version 16708');
        return parseVersion16708(buffer);
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(16708, 'handleVersion16708', { bufferLength: buffer.length }),
            undefined
        );
    }
}

/**
 * Handler for SimData version 48111
 * @param buffer SimData buffer
 * @returns Parsed SimData or undefined if parsing fails
 */
export function handleVersion48111(buffer: Buffer): ParsedSimData | undefined {
    try {
        logger.info('Handling special SimData version 48111');
        return parseVersion48111(buffer);
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(48111, 'handleVersion48111', { bufferLength: buffer.length }),
            undefined
        );
    }
}

/**
 * Get all special version handlers (16708, 48111)
 * @returns Map of version numbers to handler functions
 */
export function getSpecialVersionHandlers(): Map<number, VersionHandlerFunction> {
    const handlers = new Map<number, VersionHandlerFunction>();
    
    handlers.set(16708, handleVersion16708);
    handlers.set(48111, handleVersion48111);
    
    return handlers;
}
