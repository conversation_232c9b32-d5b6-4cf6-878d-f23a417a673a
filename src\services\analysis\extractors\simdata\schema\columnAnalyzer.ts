/**
 * Column analyzer for SimData schemas
 * Responsible for analyzing column semantics and determining their purpose
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataColumn } from '../simDataParser.js';
import { ColumnSemanticInfo } from './schemaInterfaces.js';
import { COLUMN_PATTERNS, CRITICAL_COLUMN_PATTERNS, DATA_TYPE_NAMES } from './schemaConstants.js';
import { schemaCatalog } from './schemaCatalog.js';

const log = new Logger('ColumnAnalyzer');

// Additional column purpose patterns
const COLUMN_PURPOSE_PATTERNS: Record<string, string> = {
    // Identification columns
    'id': 'Unique identifier for this entity',
    'instance': 'Instance identifier for this entity',
    'key': 'Key value for this entity',
    'guid': 'Globally unique identifier',

    // Name and description columns
    'name': 'Name or title of this entity',
    'description': 'Description or details about this entity',
    'title': 'Title or heading for this entity',
    'label': 'Label or short name for this entity',

    // Gameplay-affecting columns
    'multiplier': 'Multiplication factor that scales effects or values',
    'chance': 'Probability of an event or outcome occurring',
    'probability': 'Likelihood of an event or outcome occurring',
    'duration': 'Length of time an effect or state lasts',
    'cost': 'Resource cost or price required',
    'value': 'Numeric value or worth',
    'weight': 'Relative importance or influence in calculations',
    'priority': 'Order of importance or execution sequence',
    'threshold': 'Minimum value required to trigger an effect',
    'score': 'Point value or rating',
    'buff': 'Temporary status effect modifier',
    'motive': 'Sim need or motivation value',
    'skill': 'Ability or proficiency level',
    'trait': 'Character attribute or quality',
    'stat': 'Statistical value or measurement',
    'tuning': 'Game configuration parameter',
    'level': 'Progression tier or difficulty setting',
    'gain': 'Rate of increase or acquisition',
    'decay': 'Rate of decrease or deterioration',

    // Visual and appearance columns
    'color': 'Color value or setting',
    'position': 'Spatial location or coordinates',
    'scale': 'Size or scaling factor',
    'rotation': 'Orientation or angle',
    'offset': 'Displacement from reference point',
    'visual': 'Visual appearance parameter',

    // State and condition columns
    'state': 'Current condition or mode',
    'status': 'Current state or standing',
    'condition': 'Required or resulting state',
    'mode': 'Operating mode or behavior setting',
    'flag': 'Boolean indicator or toggle',

    // Time-related columns
    'time': 'Time value or timestamp',
    'delay': 'Waiting period before action',
    'interval': 'Time between recurring events',
    'timeout': 'Maximum time allowed',

    // Reference columns
    'reference': 'Reference to another entity',
    'ref': 'Reference to another entity',
    'tgi': 'Type-Group-Instance reference',

    // Boolean columns
    'is_': 'Boolean state indicator',
    'has_': 'Boolean possession indicator',
    'can_': 'Boolean capability indicator',
    'allow_': 'Boolean permission indicator',
    'enable_': 'Boolean activation indicator',

    // Numeric range columns
    'min_': 'Minimum allowed value',
    'max_': 'Maximum allowed value',
    'lower_': 'Lower bound of range',
    'upper_': 'Upper bound of range',

    // Count and quantity columns
    'count': 'Number of items or occurrences',
    'amount': 'Quantity or volume',
    'total': 'Sum or aggregate value',
    'capacity': 'Maximum containable amount',

    // Modifier columns
    'modifier': 'Value that changes another value',
    'bonus': 'Positive addition to a value',
    'penalty': 'Negative subtraction from a value',
    'factor': 'Multiplication value',

    // Requirement columns
    'required': 'Mandatory condition or value',
    'prerequisite': 'Condition that must be met first',
    'requirement': 'Necessary condition or value'
};

/**
 * Column Analyzer
 * Analyzes SimData columns to determine their semantic meaning and importance
 */
export class ColumnAnalyzer {
    private logger: Logger;

    constructor(logger?: Logger) {
        this.logger = logger || log;
    }

    /**
     * Analyze column semantics
     * @param columns The SimData schema columns
     * @param schemaName Optional schema name for context
     * @returns Record of column semantics by column name
     */
    public analyzeColumns(columns: SimDataColumn[], schemaName?: string): Record<string, ColumnSemanticInfo> {
        const semantics: Record<string, ColumnSemanticInfo> = {};

        // Get schema entry from catalog if available
        const schemaEntry = schemaName ? schemaCatalog.matchSchema(schemaName) : undefined;

        // Analyze column relationships
        const relationships = this.analyzeColumnRelationships(columns);

        // First pass to identify basic semantics
        for (const column of columns) {
            const typeName = this.getDataTypeName(column.type);
            const category = this.determineColumnCategory(column);
            const isCritical = this.isColumnCritical(column, schemaEntry);
            const purpose = this.determineColumnPurpose(column, schemaName);
            const gameplayImpact = isCritical ? this.determineGameplayImpact(column) : undefined;

            // Determine if this is a required column
            const isRequired = this.isRequiredColumn(column, relationships.keyColumns);

            // Determine possible values for enum-like columns
            const possibleValues = this.determinePossibleValues(column);

            semantics[column.name] = {
                name: column.name,
                type: column.type,
                typeName,
                category,
                isCritical,
                description: this.generateColumnDescription(column, typeName, category, purpose),
                gameplayImpact,
                purpose,
                isRequired,
                possibleValues
            };
        }

        // Second pass to identify relationships between columns
        this.analyzeColumnRelationships(columns, semantics);

        return semantics;
    }

    /**
     * Determine the category of a column
     * @param column The SimData column
     * @returns The column category
     */
    private determineColumnCategory(column: SimDataColumn): string {
        const name = column.name.toLowerCase();

        // Check if it's a ResourceKey
        if (column.type === 20) {
            return 'reference';
        }

        // Check patterns for each category
        for (const [category, patterns] of Object.entries(COLUMN_PATTERNS)) {
            if (patterns && patterns.some(pattern => name.includes(pattern))) {
                return category;
            }
        }

        // Check data type for additional categorization
        switch (column.type) {
            case 1: // Boolean
                return 'flag';
            case 11: // Float
                return 'numeric';
            case 12: // String
            case 13: // HashedString
                return 'text';
            case 15: // Vector
            case 16: // Float2
            case 17: // Float3
            case 18: // Float4
                return 'visual';
            case 19: // TableSetReference
                return 'reference';
            case 21: // LocalizationKey
                return 'text';
            case 22: // VariantList
                return 'collection';
        }

        // Default category
        return 'data';
    }

    /**
     * Check if a column is critical for gameplay
     * @param column The SimData column
     * @param schemaEntry Optional schema entry for context
     * @returns True if the column is critical for gameplay
     */
    private isColumnCritical(column: SimDataColumn, schemaEntry?: any): boolean {
        const name = column.name.toLowerCase();

        // Check if this column is listed as critical in the schema catalog
        if (schemaEntry && schemaEntry.criticalColumns &&
            schemaEntry.criticalColumns.includes(column.name)) {
            return true;
        }

        // Check against critical column patterns
        if (CRITICAL_COLUMN_PATTERNS && CRITICAL_COLUMN_PATTERNS.some(pattern => name.includes(pattern))) {
            return true;
        }

        // Check if this is a key column
        if (name.includes('id') || name.includes('key') || name === 'name') {
            return true;
        }

        // Check if this is a resource reference
        if (column.type === 20) { // ResourceKey type
            return true;
        }

        return false;
    }

    /**
     * Determine the purpose of a column
     * @param column The SimData column
     * @param schemaName Optional schema name for context
     * @returns The column purpose
     */
    private determineColumnPurpose(column: SimDataColumn, schemaName?: string): string {
        const name = column.name.toLowerCase();

        // Check for exact matches in purpose patterns
        for (const [pattern, purpose] of Object.entries(COLUMN_PURPOSE_PATTERNS)) {
            if (name === pattern) {
                return purpose;
            }
        }

        // Check for pattern matches
        for (const [pattern, purpose] of Object.entries(COLUMN_PURPOSE_PATTERNS)) {
            if (name.includes(pattern)) {
                return purpose;
            }
        }

        // Check for common prefixes
        if (name.startsWith('is_')) {
            return 'Boolean state indicator';
        } else if (name.startsWith('has_')) {
            return 'Boolean possession indicator';
        } else if (name.startsWith('can_')) {
            return 'Boolean capability indicator';
        } else if (name.startsWith('allow_')) {
            return 'Boolean permission indicator';
        } else if (name.startsWith('enable_')) {
            return 'Boolean activation indicator';
        } else if (name.startsWith('min_')) {
            return 'Minimum allowed value';
        } else if (name.startsWith('max_')) {
            return 'Maximum allowed value';
        }

        // Check data type for generic purpose
        switch (column.type) {
            case 1: // Boolean
                return 'Boolean flag or toggle';
            case 11: // Float
                return 'Numeric value or parameter';
            case 12: // String
                return 'Text or descriptive content';
            case 13: // HashedString
                return 'Hashed text identifier';
            case 15: // Vector
            case 16: // Float2
            case 17: // Float3
            case 18: // Float4
                return 'Spatial coordinates or vector value';
            case 19: // TableSetReference
                return 'Reference to a collection of values';
            case 20: // ResourceKey
                return 'Reference to another game resource';
            case 21: // LocalizationKey
                return 'Reference to localized text';
            case 22: // VariantList
                return 'List of variant values';
        }

        // Default purpose based on schema context
        if (schemaName) {
            if (schemaName.includes('Trait') && name.includes('effect')) {
                return 'Effect applied by a trait';
            } else if (schemaName.includes('Buff') && name.includes('timeout')) {
                return 'Duration before buff expires';
            } else if (schemaName.includes('Interaction') && name.includes('target')) {
                return 'Target of the interaction';
            }
        }

        return 'General data field';
    }

    /**
     * Check if a column is required
     * @param column The SimData column
     * @param keyColumns Array of key column names
     * @returns True if the column is required
     */
    private isRequiredColumn(column: SimDataColumn, keyColumns: string[]): boolean {
        // Key columns are always required
        if (keyColumns.includes(column.name)) {
            return true;
        }

        // Check column flags for required flag (if implemented in the future)
        // For now, we'll use a heuristic based on name
        const name = column.name.toLowerCase();
        return name.includes('required') ||
               name === 'name' ||
               name === 'type' ||
               name === 'value';
    }

    /**
     * Determine possible values for enum-like columns
     * @param column The SimData column
     * @returns Array of possible values or undefined
     */
    private determinePossibleValues(column: SimDataColumn): any[] | undefined {
        const name = column.name.toLowerCase();

        // Boolean columns have two possible values
        if (column.type === 1) { // Boolean
            return [true, false];
        }

        // Common enum-like columns
        if (name === 'gender') {
            return ['male', 'female'];
        } else if (name === 'age') {
            return ['baby', 'toddler', 'child', 'teen', 'youngAdult', 'adult', 'elder'];
        } else if (name === 'species') {
            return ['human', 'dog', 'cat', 'fox', 'horse'];
        } else if (name.includes('state') || name.includes('status')) {
            // Generic state column - can't determine values without context
            return undefined;
        }

        return undefined;
    }

    /**
     * Generate a description for a column
     * @param column The SimData column
     * @param typeName The data type name
     * @param category The column category
     * @param purpose Optional column purpose
     * @returns A description of the column
     */
    private generateColumnDescription(
        column: SimDataColumn,
        typeName: string,
        category: string,
        purpose?: string
    ): string {
        const name = column.name;
        const type = typeName;

        let description = `${name} (${type})`;

        // Add purpose if available
        if (purpose) {
            description += `: ${purpose}`;
            return description;
        }

        // Otherwise use category-based description
        switch (category) {
            case 'key':
                description += ': Identifier or key field';
                break;
            case 'reference':
                description += ': Reference to another resource';
                break;
            case 'gameplay':
                description += ': Critical gameplay parameter';
                break;
            case 'text':
                description += ': Text or descriptive content';
                break;
            case 'visual':
                description += ': Visual appearance parameter';
                break;
            case 'state':
                description += ': State or status information';
                break;
            case 'time':
                description += ': Time-related parameter';
                break;
            case 'flag':
                description += ': Boolean flag or toggle';
                break;
            case 'numeric':
                description += ': Numeric value or parameter';
                break;
            case 'collection':
                description += ': Collection of values';
                break;
            default:
                description += ': General data field';
                break;
        }

        return description;
    }

    /**
     * Determine the gameplay impact of a column
     * @param column The SimData column
     * @returns Description of gameplay impact
     */
    private determineGameplayImpact(column: SimDataColumn): string {
        const name = column.name.toLowerCase();

        // High impact gameplay columns
        if (name.includes('multiplier')) {
            return 'Affects calculation rates or effectiveness';
        } else if (name.includes('chance') || name.includes('probability')) {
            return 'Determines likelihood of events or outcomes';
        } else if (name.includes('duration')) {
            return 'Controls how long effects or states last';
        } else if (name.includes('cost') || name.includes('price')) {
            return 'Affects in-game economy or resource requirements';
        } else if (name.includes('value') || name.includes('score')) {
            return 'Determines point values or ratings';
        } else if (name.includes('weight') || name.includes('priority')) {
            return 'Affects selection or sorting algorithms';
        } else if (name.includes('threshold')) {
            return 'Sets limits or boundaries for game systems';
        } else if (name.includes('buff')) {
            return 'Related to temporary status effects';
        } else if (name.includes('motive')) {
            return 'Affects Sim needs or motivations';
        } else if (name.includes('skill')) {
            return 'Related to Sim abilities or progression';
        } else if (name.includes('trait')) {
            return 'Affects Sim personality or characteristics';
        } else if (name.includes('tuning')) {
            return 'References game configuration settings';
        } else if (name.includes('level')) {
            return 'Determines progression or difficulty tiers';
        } else if (name.includes('gain') || name.includes('decay')) {
            return 'Controls rate of increase or decrease for values';
        }

        // Medium impact gameplay columns
        if (name.includes('requirement') || name.includes('prerequisite')) {
            return 'Sets conditions that must be met for gameplay features';
        } else if (name.includes('unlock') || name.includes('lock')) {
            return 'Controls access to gameplay features or content';
        } else if (name.includes('cooldown')) {
            return 'Controls time between repeated actions';
        } else if (name.includes('range')) {
            return 'Sets distance or scope limitations';
        } else if (name.includes('speed')) {
            return 'Controls movement or action timing';
        } else if (name.includes('limit')) {
            return 'Sets maximum or minimum boundaries';
        } else if (name.includes('bonus')) {
            return 'Provides additional benefits or advantages';
        } else if (name.includes('penalty')) {
            return 'Imposes negative effects or disadvantages';
        }

        // Low impact or contextual gameplay columns
        if (name.includes('flag') || name.includes('toggle')) {
            return 'Enables or disables specific features';
        } else if (name.includes('option') || name.includes('setting')) {
            return 'Controls configurable gameplay options';
        } else if (name.includes('mode')) {
            return 'Sets operational mode for gameplay systems';
        } else if (name.includes('effect')) {
            return 'Defines consequences or results of actions';
        }

        // Resource key columns have gameplay impact through references
        if (column.type === 20) { // ResourceKey
            return 'References other game resources that affect gameplay';
        }

        return 'Affects gameplay in ways that require further analysis';
    }

    /**
     * Gets the name of a SimData data type from its numeric value
     * @param type The numeric data type
     * @returns The data type name
     */
    private getDataTypeName(type: number): string {
        return DATA_TYPE_NAMES[type] || `Type${type}`;
    }

    /**
     * Analyze column relationships within a schema
     * @param columns The SimData schema columns
     * @param semantics Optional existing semantics to update with relationship info
     * @returns Analysis of column relationships
     */
    public analyzeColumnRelationships(
        columns: SimDataColumn[],
        semantics?: Record<string, ColumnSemanticInfo>
    ): {
        keyColumns: string[];
        referenceColumns: string[];
        valueColumns: string[];
        stateColumns: string[];
        relatedColumns: Record<string, string[]>;
    } {
        const keyColumns: string[] = [];
        const referenceColumns: string[] = [];
        const valueColumns: string[] = [];
        const stateColumns: string[] = [];
        const relatedColumns: Record<string, string[]> = {};

        // First pass - identify column types
        for (const column of columns) {
            const name = column.name.toLowerCase();

            // Identify key columns
            if (COLUMN_PATTERNS['key'] && COLUMN_PATTERNS['key'].some(pattern => name.includes(pattern))) {
                keyColumns.push(column.name);
            }

            // Identify reference columns
            if (column.type === 20 || (COLUMN_PATTERNS['reference'] && COLUMN_PATTERNS['reference'].some(pattern => name.includes(pattern)))) {
                referenceColumns.push(column.name);
            }

            // Identify value columns
            if (CRITICAL_COLUMN_PATTERNS && CRITICAL_COLUMN_PATTERNS.some(pattern => name.includes(pattern))) {
                valueColumns.push(column.name);
            }

            // Identify state columns
            if (COLUMN_PATTERNS['state'] && COLUMN_PATTERNS['state'].some(pattern => name.includes(pattern))) {
                stateColumns.push(column.name);
            }

            // Initialize related columns array
            relatedColumns[column.name] = [];
        }

        // Second pass - identify relationships between columns
        for (const column of columns) {
            const columnName = column.name;
            const lowerName = columnName.toLowerCase();

            // Look for prefix/suffix relationships
            for (const otherColumn of columns) {
                if (column === otherColumn) continue;

                const otherName = otherColumn.name;
                const lowerOtherName = otherName.toLowerCase();

                // Check for prefix relationships
                if (lowerOtherName.startsWith(lowerName + '_')) {
                    relatedColumns[columnName].push(otherName);
                    if (semantics) {
                        semantics[columnName].relatedColumns = semantics[columnName].relatedColumns || [];
                        semantics[columnName].relatedColumns.push(otherName);
                    }
                }

                // Check for suffix relationships
                if (lowerOtherName.endsWith('_' + lowerName)) {
                    relatedColumns[columnName].push(otherName);
                    if (semantics) {
                        semantics[columnName].relatedColumns = semantics[columnName].relatedColumns || [];
                        semantics[columnName].relatedColumns.push(otherName);
                    }
                }

                // Check for common prefixes
                const parts = lowerName.split('_');
                if (parts.length > 1) {
                    const prefix = parts[0];
                    if (lowerOtherName.startsWith(prefix + '_') && lowerOtherName !== lowerName) {
                        relatedColumns[columnName].push(otherName);
                        if (semantics) {
                            semantics[columnName].relatedColumns = semantics[columnName].relatedColumns || [];
                            semantics[columnName].relatedColumns.push(otherName);
                        }
                    }
                }
            }

            // Update semantics with relationship info if provided
            if (semantics && semantics[columnName]) {
                if (keyColumns.includes(columnName)) {
                    semantics[columnName].isKey = true;
                }

                if (referenceColumns.includes(columnName)) {
                    semantics[columnName].isReference = true;
                }

                if (valueColumns.includes(columnName)) {
                    semantics[columnName].isValue = true;
                }

                if (stateColumns.includes(columnName)) {
                    semantics[columnName].isState = true;
                }
            }
        }

        return {
            keyColumns,
            referenceColumns,
            valueColumns,
            stateColumns,
            relatedColumns
        };
    }

    /**
     * Analyze column patterns across instances
     * @param columns The SimData schema columns
     * @param instances The SimData instances
     * @returns Analysis of column patterns
     */
    public analyzeColumnPatterns(
        columns: SimDataColumn[],
        instances: any[]
    ): Record<string, {
        uniqueValues: any[];
        commonValues: any[];
        valueDistribution: Record<string, number>;
        hasNullValues: boolean;
        valueRange?: { min: number; max: number };
    }> {
        const patterns: Record<string, any> = {};

        // Skip if no instances
        if (!instances || instances.length === 0) {
            return patterns;
        }

        // Analyze each column
        for (const column of columns) {
            const columnName = column.name;
            const values: any[] = [];
            const valueCount: Record<string, number> = {};
            let hasNullValues = false;
            let min: number | undefined;
            let max: number | undefined;

            // Collect values from all instances
            for (const instance of instances) {
                if (!instance.values) continue;

                const value = instance.values[columnName];

                // Check for null/undefined
                if (value === null || value === undefined) {
                    hasNullValues = true;
                    continue;
                }

                // Add to values array
                values.push(value);

                // Count occurrences
                const valueKey = String(value);
                valueCount[valueKey] = (valueCount[valueKey] || 0) + 1;

                // Track min/max for numeric values
                if (typeof value === 'number') {
                    if (min === undefined || value < min) min = value;
                    if (max === undefined || value > max) max = value;
                }
            }

            // Get unique values
            const uniqueValues = [...new Set(values)];

            // Get most common values (top 5)
            const commonValues = Object.entries(valueCount)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5)
                .map(([value]) => value);

            // Store pattern info
            patterns[columnName] = {
                uniqueValues,
                commonValues,
                valueDistribution: valueCount,
                hasNullValues
            };

            // Add range for numeric values
            if (min !== undefined && max !== undefined) {
                patterns[columnName].valueRange = { min, max };
            }
        }

        return patterns;
    }

    /**
     * Gets the name of a SimData data type from its numeric value
     * @param type The numeric data type
     * @returns The data type name
     */
    private getDataTypeName(type: number): string {
        return DATA_TYPE_NAMES[type] || `Type${type}`;
    }


}
