import { ResourceKey as AppResource<PERSON>ey, ResourceMetadata } from '../../../types/resource/interfaces.js';
import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';

const logger = new Logger('ExtractFontMetadata');

// OpenType/TrueType font file signatures
const OTF_SIGNATURE = 0x4F54544F; // 'OTTO' in ASCII (OpenType with CFF data)
const TTF_SIGNATURE_1 = 0x00010000; // TrueType font (Windows)
const TTF_SIGNATURE_2 = 0x74727565; // 'true' in ASCII (TrueType font on Mac)
const WOFF_SIGNATURE = 0x774F4646; // 'wOFF' in ASCII (Web Open Font Format)
const WOFF2_SIGNATURE = 0x774F4632; // 'wOF2' in ASCII (Web Open Font Format 2)

/**
 * Font metadata interface
 */
interface FontMetadata {
  format: string;
  version?: number;
  numTables?: number;
  fontFamily?: string;
  fontSubfamily?: string;
  fullName?: string;
  postscriptName?: string;
  manufacturer?: string;
  designer?: string;
  description?: string;
  copyright?: string;
  license?: string;
  licenseURL?: string;
  glyphCount?: number;
  hasKerning?: boolean;
  hasCmaps?: boolean;
  hasHinting?: boolean;
  hasLigatures?: boolean;
  hasUnicode?: boolean;
  hasGlyphs?: boolean;
  hasOutlines?: boolean;
  hasBitmaps?: boolean;
  hasColor?: boolean;
  hasVariations?: boolean;
  hasMetrics?: boolean;
  hasNames?: boolean;
  hasOS2?: boolean;
  hasPost?: boolean;
  hasCFF?: boolean;
  hasGSUB?: boolean;
  hasGPOS?: boolean;
  hasGDEF?: boolean;
  hasGAsp?: boolean;
  hasVhea?: boolean;
  hasVmtx?: boolean;
  hasHdmx?: boolean;
  hasLTSH?: boolean;
  hasPCLT?: boolean;
  hasVDMX?: boolean;
  hasMaxp?: boolean;
  hasHead?: boolean;
  hasHhea?: boolean;
  hasHmtx?: boolean;
  hasLoca?: boolean;
  hasGlyf?: boolean;
  hasCmap?: boolean;
  hasFpgm?: boolean;
  hasPrep?: boolean;
  hasCvt?: boolean;
  hasGasp?: boolean;
  hasKern?: boolean;
  hasName?: boolean;
  hasOS2Table?: boolean;
  hasPostTable?: boolean;
}

/**
 * Extracts metadata from OpenType and TrueType font resources.
 * 
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object for Font resources.
 */
export async function extractFontMetadata(
  key: AppResourceKey,
  buffer: Buffer,
  resourceId: number,
  databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
  const extractedMetadata: Partial<ResourceMetadata> = {};
  let contentSnippet: string | undefined = undefined;

  try {
    // Verify this is a Font resource
    if (key.type !== ResourceTypes.RESOURCE_TYPE_OPEN_TYPE_FONT) {
      logger.warn(`Resource ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)} is not a Font resource`);
      return {
        contentSnippet: `[Not a Font resource: ${key.type.toString(16)}]`,
        extractorUsed: 'font'
      };
    }

    // Font resources are binary files with a specific header structure
    if (buffer.length < 12) {
      logger.warn(`Font buffer too small for ${key.instance.toString(16)}`);
      return { contentSnippet: '[Font Buffer Too Small]' };
    }

    // Initialize font metadata
    const metadata: FontMetadata = {
      format: 'Unknown'
    };

    // Check for font signatures
    const signature = buffer.readUInt32BE(0);
    
    if (signature === OTF_SIGNATURE) {
      metadata.format = 'OpenType (CFF)';
      metadata.hasCFF = true;
    } else if (signature === TTF_SIGNATURE_1 || signature === TTF_SIGNATURE_2) {
      metadata.format = 'TrueType';
      metadata.hasGlyf = true;
    } else if (signature === WOFF_SIGNATURE) {
      metadata.format = 'WOFF';
    } else if (signature === WOFF2_SIGNATURE) {
      metadata.format = 'WOFF2';
    } else {
      // Not a recognized font format, but still try to extract what we can
      metadata.format = `Unknown (0x${signature.toString(16)})`;
    }

    // Extract basic font information from the header
    if (metadata.format.startsWith('OpenType') || metadata.format.startsWith('TrueType')) {
      // Extract number of tables
      metadata.numTables = buffer.readUInt16BE(4);
      
      // Extract version
      if (metadata.format.startsWith('TrueType')) {
        const majorVersion = buffer.readUInt16BE(0);
        const minorVersion = buffer.readUInt16BE(2);
        metadata.version = majorVersion + (minorVersion / 10);
      }
      
      // Check for common tables by scanning the table directory
      const tableDirectory: any[] = [];
      for (let i = 0; i < metadata.numTables && i < 50; i++) { // Limit to 50 tables for safety
        const offset = 12 + (i * 16); // Each table entry is 16 bytes
        if (offset + 16 <= buffer.length) {
          const tag = buffer.slice(offset, offset + 4).toString('ascii');
          const checksum = buffer.readUInt32BE(offset + 4);
          const tableOffset = buffer.readUInt32BE(offset + 8);
          const length = buffer.readUInt32BE(offset + 12);
          
          tableDirectory.push({ tag, checksum, offset: tableOffset, length });
          
          // Set flags based on table presence
          switch (tag) {
            case 'cmap': metadata.hasCmap = true; break;
            case 'glyf': metadata.hasGlyf = true; break;
            case 'head': metadata.hasHead = true; break;
            case 'hhea': metadata.hasHhea = true; break;
            case 'hmtx': metadata.hasHmtx = true; break;
            case 'loca': metadata.hasLoca = true; break;
            case 'maxp': metadata.hasMaxp = true; break;
            case 'name': metadata.hasName = true; break;
            case 'OS/2': metadata.hasOS2Table = true; break;
            case 'post': metadata.hasPostTable = true; break;
            case 'kern': metadata.hasKern = true; break;
            case 'GSUB': metadata.hasGSUB = true; break;
            case 'GPOS': metadata.hasGPOS = true; break;
            case 'GDEF': metadata.hasGDEF = true; break;
            case 'CFF ': metadata.hasCFF = true; break;
          }
        }
      }
      
      // Extract font name information from the 'name' table if present
      const nameTable = tableDirectory.find(t => t.tag === 'name');
      if (nameTable && nameTable.offset + 6 <= buffer.length) {
        const nameOffset = nameTable.offset;
        const format = buffer.readUInt16BE(nameOffset);
        const count = buffer.readUInt16BE(nameOffset + 2);
        const stringOffset = buffer.readUInt16BE(nameOffset + 4);
        
        // Process name records
        for (let i = 0; i < count && i < 50; i++) { // Limit to 50 name records for safety
          const recordOffset = nameOffset + 6 + (i * 12); // Each name record is 12 bytes
          
          if (recordOffset + 12 <= buffer.length) {
            const platformID = buffer.readUInt16BE(recordOffset);
            const encodingID = buffer.readUInt16BE(recordOffset + 2);
            const languageID = buffer.readUInt16BE(recordOffset + 4);
            const nameID = buffer.readUInt16BE(recordOffset + 6);
            const length = buffer.readUInt16BE(recordOffset + 8);
            const offset = buffer.readUInt16BE(recordOffset + 10);
            
            // Only process English strings (or any language if we haven't found a name yet)
            if ((languageID === 0x0409 || languageID === 0) && // English (US)
                nameOffset + stringOffset + offset + length <= buffer.length) {
              
              let nameString = '';
              // Handle different encodings
              if (platformID === 0 || (platformID === 3 && encodingID === 1)) {
                // Unicode or Windows Unicode
                nameString = buffer.slice(nameOffset + stringOffset + offset, 
                                         nameOffset + stringOffset + offset + length)
                                  .toString('utf16le');
              } else {
                // ASCII or other encoding
                nameString = buffer.slice(nameOffset + stringOffset + offset, 
                                         nameOffset + stringOffset + offset + length)
                                  .toString('ascii');
              }
              
              // Assign to appropriate metadata field based on nameID
              switch (nameID) {
                case 1: metadata.fontFamily = nameString; break;
                case 2: metadata.fontSubfamily = nameString; break;
                case 4: metadata.fullName = nameString; break;
                case 6: metadata.postscriptName = nameString; break;
                case 8: metadata.manufacturer = nameString; break;
                case 9: metadata.designer = nameString; break;
                case 10: metadata.description = nameString; break;
                case 0: metadata.copyright = nameString; break;
                case 13: metadata.license = nameString; break;
                case 14: metadata.licenseURL = nameString; break;
              }
            }
          }
        }
      }
      
      // Extract glyph count from 'maxp' table if present
      const maxpTable = tableDirectory.find(t => t.tag === 'maxp');
      if (maxpTable && maxpTable.offset + 6 <= buffer.length) {
        metadata.glyphCount = buffer.readUInt16BE(maxpTable.offset + 4);
      }
      
      // Check for kerning from 'kern' table
      metadata.hasKerning = tableDirectory.some(t => t.tag === 'kern');
      
      // Check for hinting from 'fpgm', 'prep', or 'cvt ' tables
      metadata.hasHinting = tableDirectory.some(t => 
        t.tag === 'fpgm' || t.tag === 'prep' || t.tag === 'cvt ');
      
      // Check for ligatures from 'GSUB' table
      metadata.hasLigatures = tableDirectory.some(t => t.tag === 'GSUB');
      
      // Check for Unicode support from 'cmap' table
      metadata.hasUnicode = tableDirectory.some(t => t.tag === 'cmap');
    }
    
    // Store extracted metadata
    extractedMetadata.fontFormat = metadata.format;
    
    if (metadata.version !== undefined) {
      extractedMetadata.fontVersion = metadata.version.toString();
    }
    
    if (metadata.fontFamily) {
      extractedMetadata.fontFamily = metadata.fontFamily;
    }
    
    if (metadata.fontSubfamily) {
      extractedMetadata.fontSubfamily = metadata.fontSubfamily;
    }
    
    if (metadata.fullName) {
      extractedMetadata.fontFullName = metadata.fullName;
    }
    
    if (metadata.postscriptName) {
      extractedMetadata.fontPostscriptName = metadata.postscriptName;
    }
    
    if (metadata.glyphCount !== undefined) {
      extractedMetadata.fontGlyphCount = metadata.glyphCount;
    }
    
    if (metadata.hasKerning !== undefined) {
      extractedMetadata.fontHasKerning = metadata.hasKerning;
    }
    
    if (metadata.hasHinting !== undefined) {
      extractedMetadata.fontHasHinting = metadata.hasHinting;
    }
    
    if (metadata.hasLigatures !== undefined) {
      extractedMetadata.fontHasLigatures = metadata.hasLigatures;
    }
    
    if (metadata.hasUnicode !== undefined) {
      extractedMetadata.fontHasUnicode = metadata.hasUnicode;
    }
    
    // Create content snippet
    contentSnippet = `[Font: ${metadata.format}`;
    if (metadata.fontFamily) {
      contentSnippet += `, Family: ${metadata.fontFamily}`;
    }
    if (metadata.fontSubfamily) {
      contentSnippet += `, Style: ${metadata.fontSubfamily}`;
    }
    if (metadata.glyphCount !== undefined) {
      contentSnippet += `, Glyphs: ${metadata.glyphCount}`;
    }
    contentSnippet += ']';
    
    // Save font metadata for deeper analysis
    try {
      await databaseService.parsedContent.saveParsedContent({
        resourceId: resourceId,
        contentType: 'font_metadata',
        content: JSON.stringify(metadata)
      });
      logger.debug(`Saved font metadata for resource ${resourceId}`);
    } catch (dbError: any) {
      logger.error(`Failed to save font metadata for resource ${resourceId} to DB: ${dbError.message || dbError}`);
    }
    
  } catch (error: any) {
    logger.error(`Error extracting font metadata for ${key.instance.toString(16)}: ${error.message || error}`);
    contentSnippet = `[Font Parse Error: ${error.message || 'Unknown Error'}]`;
  }

  // Return extracted metadata
  return {
    contentSnippet,
    ...extractedMetadata,
  };
}
