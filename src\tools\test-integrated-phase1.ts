/**
 * Integrated Phase 1 Test Runner
 * 
 * This script integrates the Phase 1 Critical Performance Improvements tests
 * with the existing test framework, providing comprehensive testing of both
 * new and existing functionality.
 */

import { redirectConsoleOutput, createPrintFunction, restoreConsole } from '../utils/console/consoleOutput.js';
import { configureEventEmitter } from '../utils/eventEmitterConfig.js';
import { applyMonkeyPatches } from '../utils/monkeyPatch.js';
import { EnhancedMemoryManager } from '../utils/memory/enhancedMemoryManager.js';
import { EnhancedErrorHandler } from '../utils/error/enhancedErrorHandler.js';
import { Logger } from '../utils/logging/logger.js';
import { parseArgs } from './testing/argParser.js';
import { findPackageFiles } from './testing/fileScanner.js';
import { runComprehensiveTestSuite } from './testing/comprehensiveTestOrchestrator.js';
import { runPhase1Tests } from './test-phase1-improvements.js';

// Initialize system
applyMonkeyPatches();
configureEventEmitter();

const logger = new Logger('IntegratedPhase1Test');

/**
 * Integrated test configuration
 */
interface IntegratedTestConfig {
    modsPath: string;
    maxPackages: number;
    runPhase1Tests: boolean;
    runExistingTests: boolean;
    testMode: 'minimal' | 'standard' | 'comprehensive';
    logLevel: string;
}

/**
 * Integrated test results
 */
interface IntegratedTestResults {
    success: boolean;
    duration: number;
    phase1Results?: any;
    existingTestResults?: any;
    summary: {
        totalTests: number;
        passedTests: number;
        failedTests: number;
        phase1Performance: {
            smartFilteringWorking: boolean;
            memoryManagementWorking: boolean;
            systemIntegrationWorking: boolean;
        };
    };
    errors: string[];
}

/**
 * Run integrated Phase 1 and existing tests
 */
async function runIntegratedTests(config: IntegratedTestConfig): Promise<IntegratedTestResults> {
    const startTime = Date.now();
    const print = createPrintFunction();
    
    const results: IntegratedTestResults = {
        success: false,
        duration: 0,
        summary: {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            phase1Performance: {
                smartFilteringWorking: false,
                memoryManagementWorking: false,
                systemIntegrationWorking: false
            }
        },
        errors: []
    };

    try {
        print('🚀 Starting Integrated Phase 1 + Existing Test Suite');
        print('=' .repeat(80));
        print(`Configuration:`);
        print(`  Mods Path: ${config.modsPath}`);
        print(`  Max Packages: ${config.maxPackages}`);
        print(`  Test Mode: ${config.testMode}`);
        print(`  Phase 1 Tests: ${config.runPhase1Tests ? 'Enabled' : 'Disabled'}`);
        print(`  Existing Tests: ${config.runExistingTests ? 'Enabled' : 'Disabled'}`);
        print('');

        // Verify package files exist
        const packageFiles = await findPackageFiles(config.modsPath, {
            maxFiles: config.maxPackages,
            maxDepth: 3
        });

        if (packageFiles.length === 0) {
            results.errors.push('No package files found for testing');
            return results;
        }

        print(`📁 Found ${packageFiles.length} package files for testing`);
        print('');

        // Run Phase 1 tests if enabled
        if (config.runPhase1Tests) {
            print('🧪 Running Phase 1 Critical Performance Improvements Tests...');
            print('-' .repeat(60));
            
            try {
                // Capture Phase 1 test output
                const phase1StartTime = Date.now();
                await runPhase1Tests();
                const phase1Duration = Date.now() - phase1StartTime;
                
                results.phase1Results = {
                    success: true,
                    duration: phase1Duration,
                    components: ['Smart Filtering', 'Memory Management', 'System Integration']
                };
                
                // Mark Phase 1 components as working
                results.summary.phase1Performance.smartFilteringWorking = true;
                results.summary.phase1Performance.memoryManagementWorking = true;
                results.summary.phase1Performance.systemIntegrationWorking = true;
                
                results.summary.totalTests++;
                results.summary.passedTests++;
                
                print(`✅ Phase 1 tests completed successfully in ${phase1Duration}ms`);
                print('');
                
            } catch (error: any) {
                results.errors.push(`Phase 1 tests failed: ${error.message}`);
                results.summary.totalTests++;
                results.summary.failedTests++;
                print(`❌ Phase 1 tests failed: ${error.message}`);
                print('');
            }
        }

        // Run existing comprehensive tests if enabled
        if (config.runExistingTests) {
            print('🔬 Running Existing Comprehensive Test Suite...');
            print('-' .repeat(60));
            
            try {
                const comprehensiveResult = await runComprehensiveTestSuite({
                    modsPath: config.modsPath,
                    maxPackages: Math.min(config.maxPackages, 5), // Limit for integration testing
                    testMode: config.testMode,
                    useInMemoryDatabase: true,
                    enableProgressiveScaling: false,
                    enableConcurrencyTesting: false,
                    enableMemoryStressTesting: false,
                    enableConflictDetectionTesting: true,
                    enableStreamingPipelineTesting: true,
                    enableLargeScaleTesting: false
                });
                
                results.existingTestResults = comprehensiveResult;
                results.summary.totalTests += comprehensiveResult.totalTests;
                results.summary.passedTests += comprehensiveResult.passedTests;
                results.summary.failedTests += comprehensiveResult.failedTests;
                
                if (comprehensiveResult.success) {
                    print(`✅ Existing tests completed successfully`);
                } else {
                    print(`⚠️ Some existing tests had issues`);
                    results.errors.push(...comprehensiveResult.errors);
                }
                print('');
                
            } catch (error: any) {
                results.errors.push(`Existing tests failed: ${error.message}`);
                results.summary.totalTests++;
                results.summary.failedTests++;
                print(`❌ Existing tests failed: ${error.message}`);
                print('');
            }
        }

        // Calculate overall success
        results.success = results.summary.failedTests === 0 && results.summary.passedTests > 0;
        results.duration = Date.now() - startTime;

        // Print summary
        print('📊 Integrated Test Results Summary');
        print('=' .repeat(80));
        print(`Overall Success: ${results.success ? '✅ PASS' : '❌ FAIL'}`);
        print(`Total Duration: ${results.duration}ms`);
        print(`Total Tests: ${results.summary.totalTests}`);
        print(`Passed: ${results.summary.passedTests}`);
        print(`Failed: ${results.summary.failedTests}`);
        print('');
        
        print('Phase 1 Component Status:');
        print(`  Smart Filtering: ${results.summary.phase1Performance.smartFilteringWorking ? '✅' : '❌'}`);
        print(`  Memory Management: ${results.summary.phase1Performance.memoryManagementWorking ? '✅' : '❌'}`);
        print(`  System Integration: ${results.summary.phase1Performance.systemIntegrationWorking ? '✅' : '❌'}`);
        print('');

        if (results.errors.length > 0) {
            print('❌ Errors encountered:');
            results.errors.forEach(error => print(`  - ${error}`));
            print('');
        }

        print('🎉 Integrated test suite completed!');

    } catch (error: any) {
        results.errors.push(`Test suite failed: ${error.message}`);
        results.success = false;
        logger.error('Integrated test suite failed:', error);
    }

    return results;
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
    try {
        // Initialize enhanced systems
        const memoryManager = EnhancedMemoryManager.getInstance();
        const errorHandler = EnhancedErrorHandler.getInstance();
        
        // Parse command line arguments
        const args = parseArgs(process.argv.slice(2));
        
        // Configure test settings
        const config: IntegratedTestConfig = {
            modsPath: args.modsPath,
            maxPackages: args.modCount || 10,
            runPhase1Tests: args.testPhase1Integration || true,
            runExistingTests: args.testComprehensive || false,
            testMode: args.testMode || 'standard',
            logLevel: args.logLevel || 'info'
        };

        // Validate mods path
        if (!config.modsPath) {
            console.error('❌ Error: Mods path is required. Use --mods-path <path>');
            process.exit(1);
        }

        // Run integrated tests
        const results = await runIntegratedTests(config);
        
        // Exit with appropriate code
        process.exit(results.success ? 0 : 1);

    } catch (error: any) {
        console.error('❌ Fatal error:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// Export for use in other modules
export { runIntegratedTests, IntegratedTestConfig, IntegratedTestResults };

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}