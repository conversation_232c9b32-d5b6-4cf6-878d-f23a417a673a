/**
 * Metadata converter for image extraction
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ImageHeaderInfo, ImageResourceMetadata } from '../types.js';

// Create a logger instance
const log = new Logger('ImageMetadataConverter');

/**
 * Converts image header information to resource metadata
 * @param header Image header information
 * @returns Resource metadata
 */
export function convertToResourceMetadata(header: ImageHeaderInfo): ImageResourceMetadata {
    try {
        const metadata: ImageResourceMetadata = {};
        
        // Convert format
        if (header.format) {
            metadata.imageFormat = header.format;
        }
        
        // Convert dimensions
        if (header.width !== undefined) {
            metadata.imageWidth = header.width;
        }
        
        if (header.height !== undefined) {
            metadata.imageHeight = header.height;
        }
        
        // Convert mipmap count
        if (header.mipMapCount !== undefined) {
            metadata.imageMipMapCount = header.mipMapCount;
        }
        
        // Convert compression
        if (header.compression !== undefined) {
            metadata.imageCompression = header.compression;
        }
        
        // Convert alpha information
        if (header.hasAlpha !== undefined) {
            metadata.imageHasAlpha = header.hasAlpha;
        }
        
        // Convert bits per pixel
        if (header.bitsPerPixel !== undefined) {
            metadata.imageBitsPerPixel = header.bitsPerPixel;
        }
        
        return metadata;
    } catch (error: any) {
        log.error(`Error converting to resource metadata: ${error.message || error}`);
        return {};
    }
}

/**
 * Converts image header information to database metadata
 * @param header Image header information
 * @param resourceId Resource ID
 * @returns Database metadata
 */
export function convertToDatabaseMetadata(header: ImageHeaderInfo, resourceId: number): Record<string, any> {
    try {
        const metadata: Record<string, any> = {
            resourceId,
            format: header.format
        };
        
        // Add dimensions if available
        if (header.width !== undefined) {
            metadata.width = header.width;
        }
        
        if (header.height !== undefined) {
            metadata.height = header.height;
        }
        
        // Add mipmap count if available
        if (header.mipMapCount !== undefined) {
            metadata.mipMapCount = header.mipMapCount;
        }
        
        // Add compression if available
        if (header.compression !== undefined) {
            metadata.compression = header.compression;
        }
        
        // Add alpha information if available
        if (header.hasAlpha !== undefined) {
            metadata.hasAlpha = header.hasAlpha;
        }
        
        // Add bits per pixel if available
        if (header.bitsPerPixel !== undefined) {
            metadata.bitsPerPixel = header.bitsPerPixel;
        }
        
        return metadata;
    } catch (error: any) {
        log.error(`Error converting to database metadata: ${error.message || error}`);
        return { resourceId, format: 'Unknown' };
    }
}
