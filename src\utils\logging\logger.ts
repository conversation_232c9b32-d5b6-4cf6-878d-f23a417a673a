import winston from 'winston';
import { createLogger, type LoggerInterface } from './loggerConfig'; // Removed .js

// Cache for logger instances to prevent creating too many Winston loggers
const loggerCache: Map<string, LoggerInterface> = new Map();

export class Logger {
  private logger: LoggerInterface;
  private context: string;

  constructor(context: string) {
    this.context = context;

    // Use cached logger if available, otherwise create a new one
    if (!loggerCache.has(context)) {
      loggerCache.set(context, createLogger(context));
    }

    this.logger = loggerCache.get(context)!;
  }

  private formatMessage(message: string): string {
    return `[${this.context}] ${message}`;
  }

  info(message: string, ...meta: any[]): void {
    this.logger.info(this.formatMessage(message), ...meta);
  }

  error(message: string, ...meta: any[]): void {
    this.logger.error(this.formatMessage(message), ...meta);
  }

  warn(message: string, ...meta: any[]): void {
    this.logger.warn(this.formatMessage(message), ...meta);
  }

  debug(message: string, ...meta: any[]): void {
    this.logger.debug(this.formatMessage(message), ...meta);
  }

  // Add electron logging methods if available
  electronInfo(message: string, ...meta: any[]): void {
    if (this.logger.electron) {
      this.logger.electron.info(this.formatMessage(message), ...meta);
    }
  }

  electronError(message: string, ...meta: any[]): void {
    if (this.logger.electron) {
      this.logger.electron.error(this.formatMessage(message), ...meta);
    }
  }

  electronWarn(message: string, ...meta: any[]): void {
    if (this.logger.electron) {
      this.logger.electron.warn(this.formatMessage(message), ...meta);
    }
  }

  electronDebug(message: string, ...meta: any[]): void {
    if (this.logger.electron) {
      this.logger.electron.debug(this.formatMessage(message), ...meta);
    }
  }

  /**
   * Set the global log level for all loggers
   * @param level The log level to set (debug, info, warn, error)
   */
  static setGlobalLogLevel(level: string): void {
    // Set the log level for Winston loggers
    winston.loggers.loggers.forEach(logger => {
      logger.level = level;
      logger.transports.forEach(transport => {
        if (transport instanceof winston.transports.Console ||
            transport instanceof winston.transports.File) {
          transport.level = level;
        }
      });
    });

    // Set the log level for the default logger
    winston.config.npm.levels[level] = 0;

    // Also set for electron-log if available
    try {
      const electronLog = require('electron-log');
      electronLog.transports.file.level = level;
      electronLog.transports.console.level = level;
    } catch (error) {
      // Electron-log not available, ignore
    }
  }
}

// Create default logger instance
export const logger = new Logger('App');
