/**
 * Interfaces for SimData schema analysis
 */

import { SimDataSchema, SimDataColumn } from '../simDataParser.js';

/**
 * Interface for schema inheritance information
 */
export interface SchemaInheritanceInfo {
    parent: string;
    child: string;
    inheritedColumns: string[];
    addedColumns: string[];
}

/**
 * Interface for schema compatibility information
 */
export interface SchemaCompatibilityInfo {
    isCompatible: boolean;
    compatibilityScore: number; // 0-100
    incompatibleColumns: string[];
    missingColumns: string[];
    extraColumns: string[];
    typeMismatches: Array<{ column: string, expectedType: number, actualType: number }>;
    criticalIssues: boolean;
    gameplayImpact: 'none' | 'low' | 'medium' | 'high';
    conflictDescription: string;
    compatibleWithBaseGame?: boolean;
    requiredPacks?: string[];
    potentialConflicts?: Array<{
        schemaName: string;
        reason: string;
        severity: 'low' | 'medium' | 'high';
    }>;
}

/**
 * Interface for column semantic information
 */
export interface ColumnSemanticInfo {
    name: string;
    type: number;
    typeName: string;
    category: string; // 'key', 'reference', 'gameplay', 'text', 'data', 'visual', 'state', 'time', 'flag', 'numeric', 'collection'
    isCritical: boolean;
    description: string;
    purpose?: string; // Inferred purpose of the column
    possibleValues?: any[];
    defaultValue?: any;
    isRequired?: boolean;
    isKey?: boolean; // Whether this is a key column
    isReference?: boolean; // Whether this is a reference column
    isValue?: boolean; // Whether this is a value column
    isState?: boolean; // Whether this is a state column
    gameplayImpact?: string; // Description of how this column affects gameplay
    relatedColumns?: string[]; // Names of related columns
    relatedTuning?: string[]; // Related tuning XML references
    valuePatterns?: { // Patterns observed in instance values
        uniqueValues: any[];
        commonValues: any[];
        valueDistribution: Record<string, number>;
        hasNullValues: boolean;
        valueRange?: { min: number; max: number };
    };
}

/**
 * Interface for schema analysis result
 */
export interface SchemaAnalysisResult {
    schema: SimDataSchema;
    inheritance?: SchemaInheritanceInfo;
    columnSemantics: Record<string, ColumnSemanticInfo>;
    category: string;
    subcategory?: string;
    complexity: number;
    purpose?: string; // The purpose of this schema in the game
    gameplaySystem?: string; // Which gameplay system this schema belongs to
    relationships?: SchemaRelationshipInfo; // Relationships to other schemas
    compatibility?: SchemaCompatibilityInfo; // Compatibility information
    schemaName?: string; // Name of the schema
    schemaId?: number; // ID of the schema
    schemaHash?: number; // Hash of the schema
}

/**
 * Interface for schema relationship information
 */
export interface SchemaRelationshipInfo {
    schemaName: string;
    relatedSchemas: Array<{
        name: string;
        relationship: 'parent' | 'child' | 'reference' | 'similar';
        confidence: number; // 0-100
    }>;
}

/**
 * Interface for schema purpose information
 */
export interface SchemaPurposeInfo {
    schemaName: string;
    purpose: string;
    gameplaySystem: string;
    confidence: number; // 0-100
    keyColumns: string[];
    criticalColumns: string[];
    subcategory?: string; // Subcategory within the gameplay system
    description?: string; // Detailed description of the schema's purpose
    examples?: string[]; // Example use cases
    commonModTypes?: string[]; // Types of mods that commonly use this schema
}

/**
 * Interface for schema repository entry
 */
export interface SchemaRepositoryEntry {
    schema: SimDataSchema;
    analysis: SchemaAnalysisResult;
    relationships?: SchemaRelationshipInfo;
    purpose?: SchemaPurposeInfo;
    occurrences: number; // How many times this schema has been seen
    modIds: number[]; // IDs of mods that contain this schema
}
