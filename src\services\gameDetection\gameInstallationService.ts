/**
 * Game Installation Service
 * Detects and manages Sims 4 game installations for base game resource filtering
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface GameInstallation {
    id?: number;
    path: string;
    version: string;
    platform: 'steam' | 'origin' | 'ea_app' | 'unknown';
    detectedAt: Date;
    isValid: boolean;
}

export interface GameFile {
    id?: number;
    installationId: number;
    filePath: string;
    fileHash: string;
    packType: 'base' | 'ep' | 'gp' | 'sp';
    fileName: string;
}

export interface GameResource {
    id?: number;
    gameFileId: number;
    resourceType: number;
    resourceGroup: number;
    resourceInstance: string;
    tgiKey: string; // Combined TGI for quick lookup
}

export interface DriveInfo {
    letter: string;
    type: 'fixed' | 'removable' | 'network' | 'unknown';
    available: boolean;
    totalSpace?: number;
    freeSpace?: number;
}

export interface SteamLibrary {
    path: string;
    label: string;
    mounted: boolean;
}

export interface DetectionOptions {
    includeNetworkDrives?: boolean;
    includeRemovableDrives?: boolean;
    customPaths?: string[];
    maxConcurrentScans?: number;
    timeoutMs?: number;
}

export class GameInstallationService {
    private logger: Logger;
    private databaseService: DatabaseService;
    private initialized = false;
    private driveCache: DriveInfo[] | null = null;
    private steamLibraryCache: SteamLibrary[] | null = null;
    private cacheTimestamp = 0;
    private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

    constructor(databaseService: DatabaseService) {
        this.logger = new Logger('GameInstallationService');
        this.databaseService = databaseService;
    }

    /**
     * Initialize the service and create database tables
     */
    public async initialize(): Promise<void> {
        if (this.initialized) return;

        try {
            await this.createTables();
            this.initialized = true;
            this.logger.info('Game Installation Service initialized');
        } catch (error) {
            this.logger.error('Failed to initialize Game Installation Service:', error);
            throw error;
        }
    }

    /**
     * Create database tables for game detection
     */
    private async createTables(): Promise<void> {
        try {
            // Game installations table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS game_installations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    path TEXT NOT NULL UNIQUE,
                    version TEXT,
                    platform TEXT NOT NULL,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_valid BOOLEAN DEFAULT 1
                )
            `);

            // Game files table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS game_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    installation_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    file_hash TEXT,
                    pack_type TEXT NOT NULL,
                    file_name TEXT NOT NULL,
                    FOREIGN KEY (installation_id) REFERENCES game_installations(id),
                    UNIQUE(installation_id, file_path)
                )
            `);

            // Game resources table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS game_resources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_file_id INTEGER NOT NULL,
                    resource_type INTEGER NOT NULL,
                    resource_group INTEGER NOT NULL,
                    resource_instance TEXT NOT NULL,
                    tgi_key TEXT NOT NULL,
                    FOREIGN KEY (game_file_id) REFERENCES game_files(id)
                )
            `);

            // Create indices for performance
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_game_resources_tgi ON game_resources(tgi_key)
            `);

            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_game_resources_type ON game_resources(resource_type)
            `);

            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_game_files_installation ON game_files(installation_id)
            `);

            this.logger.debug('Game detection database tables created successfully');
        } catch (error) {
            this.logger.error('Error creating game detection tables:', error);
            throw error;
        }
    }

    /**
     * Scan for Sims 4 game installations with enhanced multi-drive detection
     */
    public async scanForGameInstallations(options: DetectionOptions = {}): Promise<GameInstallation[]> {
        await this.initialize();

        const installations: GameInstallation[] = [];

        // Set default options
        const scanOptions: Required<DetectionOptions> = {
            includeNetworkDrives: options.includeNetworkDrives ?? false,
            includeRemovableDrives: options.includeRemovableDrives ?? false,
            customPaths: options.customPaths ?? [],
            maxConcurrentScans: options.maxConcurrentScans ?? 5,
            timeoutMs: options.timeoutMs ?? 30000
        };

        this.logger.info('Starting enhanced Sims 4 installation detection...');

        // Get all possible installation paths
        const allPaths = await this.getAllPossibleInstallationPaths(scanOptions);
        this.logger.info(`Scanning ${allPaths.length} potential installation paths across multiple drives...`);

        // Scan paths with concurrency control
        const scanPromises: Promise<GameInstallation | null>[] = [];

        for (const installPath of allPaths) {
            const scanPromise = this.scanPathWithTimeout(installPath, scanOptions.timeoutMs);
            scanPromises.push(scanPromise);
        }

        // Wait for all scans to complete
        const results = await Promise.allSettled(scanPromises);

        // Process results
        for (let i = 0; i < results.length; i++) {
            const result = results[i];
            if (result.status === 'fulfilled' && result.value) {
                installations.push(result.value);
                this.logger.info(`Found valid Sims 4 installation at: ${result.value.path}`);
            } else if (result.status === 'rejected') {
                this.logger.debug(`Error scanning path ${allPaths[i]}:`, result.reason);
            }
        }

        // Save found installations to database
        for (const installation of installations) {
            await this.saveGameInstallation(installation);
        }

        this.logger.info(`Enhanced scan completed. Found ${installations.length} valid Sims 4 installations`);
        return installations;
    }

    /**
     * Get all possible installation paths across multiple drives
     */
    private async getAllPossibleInstallationPaths(options: Required<DetectionOptions>): Promise<string[]> {
        const allPaths: string[] = [];

        try {
            // Get standard paths (legacy method)
            const standardPaths = this.getCommonInstallationPaths();
            allPaths.push(...standardPaths);

            // Get multi-drive paths
            const multiDrivePaths = await this.getMultiDriveInstallationPaths(options);
            allPaths.push(...multiDrivePaths);

            // Get Steam library paths
            const steamPaths = await this.getSteamLibraryInstallationPaths();
            allPaths.push(...steamPaths);

            // Add custom paths
            if (options.customPaths.length > 0) {
                this.logger.info(`Adding ${options.customPaths.length} custom paths to scan`);
                allPaths.push(...options.customPaths);
            }

            // Remove duplicates and invalid paths
            const uniquePaths = [...new Set(allPaths)];
            return uniquePaths.filter(p => p && p.length > 0);
        } catch (error) {
            this.logger.error('Error getting installation paths:', error);
            // Fallback to standard paths
            return this.getCommonInstallationPaths();
        }
    }

    /**
     * Scan a path with timeout protection
     */
    private async scanPathWithTimeout(installPath: string, timeoutMs: number): Promise<GameInstallation | null> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`Scan timeout for path: ${installPath}`));
            }, timeoutMs);

            this.validateGameInstallation(installPath)
                .then(result => {
                    clearTimeout(timeout);
                    resolve(result);
                })
                .catch(error => {
                    clearTimeout(timeout);
                    reject(error);
                });
        });
    }

    /**
     * Get common Sims 4 installation paths based on platform (legacy method)
     */
    private getCommonInstallationPaths(): string[] {
        const platform = os.platform();
        const paths: string[] = [];

        if (platform === 'win32') {
            // Windows paths
            const programFiles = process.env.ProgramFiles || 'C:\\Program Files';
            const programFilesX86 = process.env['ProgramFiles(x86)'] || 'C:\\Program Files (x86)';

            // Steam paths
            paths.push(
                path.join(programFiles, 'Steam', 'steamapps', 'common', 'The Sims 4'),
                path.join(programFilesX86, 'Steam', 'steamapps', 'common', 'The Sims 4'),
                'C:\\Program Files (x86)\\Steam\\steamapps\\common\\The Sims 4'
            );

            // Origin/EA App paths
            paths.push(
                path.join(programFiles, 'Origin Games', 'The Sims 4'),
                path.join(programFilesX86, 'Origin Games', 'The Sims 4'),
                path.join(programFiles, 'EA Games', 'The Sims 4'),
                path.join(programFilesX86, 'EA Games', 'The Sims 4')
            );

            // EA App paths
            paths.push(
                path.join(programFiles, 'EA App', 'games', 'The Sims 4'),
                path.join(programFilesX86, 'EA App', 'games', 'The Sims 4')
            );
        } else if (platform === 'darwin') {
            // macOS paths
            paths.push(
                '/Applications/The Sims 4.app',
                path.join(os.homedir(), 'Applications', 'The Sims 4.app')
            );
        }

        return paths;
    }

    /**
     * Validate if a path contains a valid Sims 4 installation
     */
    private async validateGameInstallation(installPath: string): Promise<GameInstallation | null> {
        try {
            // Check for essential game files
            const gameExecutable = this.findGameExecutable(installPath);
            if (!gameExecutable) {
                return null;
            }

            // Try to read game version
            const version = await this.getGameVersion(installPath);
            const platform = this.detectPlatform(installPath);

            return {
                path: installPath,
                version: version || 'unknown',
                platform,
                detectedAt: new Date(),
                isValid: true
            };
        } catch (error) {
            this.logger.debug(`Invalid game installation at ${installPath}:`, error);
            return null;
        }
    }

    /**
     * Find the game executable in the installation directory
     */
    private findGameExecutable(installPath: string): string | null {
        const possibleExecutables = [
            'Game\\Bin\\TS4_x64.exe',
            'Game\\Bin\\TS4.exe',
            'Contents\\MacOS\\The Sims 4'
        ];

        for (const exe of possibleExecutables) {
            const fullPath = path.join(installPath, exe);
            if (fs.existsSync(fullPath)) {
                return fullPath;
            }
        }

        return null;
    }

    /**
     * Get game version from GameVersion.txt
     */
    private async getGameVersion(installPath: string): Promise<string | null> {
        const versionFile = path.join(installPath, 'GameVersion.txt');
        
        try {
            if (fs.existsSync(versionFile)) {
                const content = fs.readFileSync(versionFile, 'utf8').trim();
                // Clean up version string
                return content.replace(/\0/g, '').replace(/[^0-9.]/g, '');
            }
        } catch (error) {
            this.logger.debug(`Error reading game version from ${versionFile}:`, error);
        }

        return null;
    }

    /**
     * Detect platform based on installation path
     */
    private detectPlatform(installPath: string): 'steam' | 'origin' | 'ea_app' | 'unknown' {
        const lowerPath = installPath.toLowerCase();
        
        if (lowerPath.includes('steam')) {
            return 'steam';
        } else if (lowerPath.includes('origin')) {
            return 'origin';
        } else if (lowerPath.includes('ea app') || lowerPath.includes('ea games')) {
            return 'ea_app';
        }
        
        return 'unknown';
    }

    /**
     * Save game installation to database
     */
    private async saveGameInstallation(installation: GameInstallation): Promise<number> {
        try {
            // Check if installation already exists
            const existing = await this.databaseService.executeQuery(
                'SELECT id FROM game_installations WHERE path = ?',
                [installation.path]
            );

            if (existing.length > 0) {
                // Update existing installation
                await this.databaseService.executeQuery(`
                    UPDATE game_installations 
                    SET version = ?, platform = ?, detected_at = CURRENT_TIMESTAMP, is_valid = ?
                    WHERE path = ?
                `, [installation.version, installation.platform, installation.isValid ? 1 : 0, installation.path]);
                
                return existing[0].id;
            } else {
                // Insert new installation
                const result = await this.databaseService.executeQuery(`
                    INSERT INTO game_installations (path, version, platform, is_valid)
                    VALUES (?, ?, ?, ?)
                `, [installation.path, installation.version, installation.platform, installation.isValid ? 1 : 0]);

                return result.lastInsertRowid;
            }
        } catch (error) {
            this.logger.error('Error saving game installation:', error);
            throw error;
        }
    }

    /**
     * Get all game installations from database
     */
    public async getGameInstallations(): Promise<GameInstallation[]> {
        await this.initialize();

        try {
            const rows = await this.databaseService.executeQuery(`
                SELECT id, path, version, platform, detected_at, is_valid
                FROM game_installations
                WHERE is_valid = 1
                ORDER BY detected_at DESC
            `);

            return rows.map((row: any) => ({
                id: row.id,
                path: row.path,
                version: row.version,
                platform: row.platform,
                detectedAt: new Date(row.detected_at),
                isValid: row.is_valid === 1
            }));
        } catch (error) {
            this.logger.error('Error getting game installations:', error);
            return [];
        }
    }

    /**
     * Get multi-drive installation paths
     */
    private async getMultiDriveInstallationPaths(options: Required<DetectionOptions>): Promise<string[]> {
        const paths: string[] = [];

        try {
            const drives = await this.getAvailableDrives();
            this.logger.info(`Found ${drives.length} available drives for scanning`);

            for (const drive of drives) {
                // Skip drives based on options
                if (!options.includeNetworkDrives && drive.type === 'network') continue;
                if (!options.includeRemovableDrives && drive.type === 'removable') continue;

                // Generate common installation patterns for this drive
                const drivePaths = this.generateDriveInstallationPaths(drive.letter);
                paths.push(...drivePaths);
            }

            this.logger.debug(`Generated ${paths.length} multi-drive installation paths`);
            return paths;
        } catch (error) {
            this.logger.error('Error getting multi-drive paths:', error);
            return [];
        }
    }

    /**
     * Get Steam library installation paths
     */
    private async getSteamLibraryInstallationPaths(): Promise<string[]> {
        const paths: string[] = [];

        try {
            const steamLibraries = await this.getSteamLibraries();
            this.logger.info(`Found ${steamLibraries.length} Steam libraries`);

            for (const library of steamLibraries) {
                if (library.mounted) {
                    const steamPath = path.join(library.path, 'steamapps', 'common', 'The Sims 4');
                    paths.push(steamPath);
                }
            }

            this.logger.debug(`Generated ${paths.length} Steam library paths`);
            return paths;
        } catch (error) {
            this.logger.error('Error getting Steam library paths:', error);
            return [];
        }
    }

    /**
     * Get available drives on Windows
     */
    private async getAvailableDrives(): Promise<DriveInfo[]> {
        // Use cache if available and fresh
        if (this.driveCache && Date.now() - this.cacheTimestamp < this.CACHE_DURATION) {
            return this.driveCache;
        }

        const drives: DriveInfo[] = [];

        try {
            if (os.platform() === 'win32') {
                // Use wmic to get drive information
                const { stdout } = await execAsync('wmic logicaldisk get size,freespace,caption,drivetype /format:csv');
                const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node'));

                for (const line of lines) {
                    const parts = line.split(',');
                    if (parts.length >= 5) {
                        const caption = parts[1]?.trim();
                        const driveType = parseInt(parts[2]?.trim() || '0');
                        const freeSpace = parseInt(parts[3]?.trim() || '0');
                        const totalSpace = parseInt(parts[4]?.trim() || '0');

                        if (caption && caption.length >= 2) {
                            const letter = caption.substring(0, 2); // e.g., "C:"
                            const type = this.mapDriveType(driveType);

                            drives.push({
                                letter,
                                type,
                                available: fs.existsSync(letter + '\\'),
                                totalSpace: totalSpace || undefined,
                                freeSpace: freeSpace || undefined
                            });
                        }
                    }
                }
            } else {
                // For non-Windows, just return common mount points
                const commonMounts = ['/mnt', '/media', '/Volumes'];
                for (const mount of commonMounts) {
                    if (fs.existsSync(mount)) {
                        drives.push({
                            letter: mount,
                            type: 'fixed',
                            available: true
                        });
                    }
                }
            }

            // Update cache
            this.driveCache = drives;
            this.cacheTimestamp = Date.now();

            this.logger.debug(`Detected ${drives.length} drives: ${drives.map(d => d.letter).join(', ')}`);
            return drives;
        } catch (error) {
            this.logger.error('Error detecting drives:', error);
            // Fallback to common drives
            return [
                { letter: 'C:', type: 'fixed', available: true },
                { letter: 'D:', type: 'fixed', available: fs.existsSync('D:\\') },
                { letter: 'E:', type: 'fixed', available: fs.existsSync('E:\\') }
            ];
        }
    }

    /**
     * Generate installation paths for a specific drive
     */
    private generateDriveInstallationPaths(driveLetter: string): string[] {
        const paths: string[] = [];
        const drive = driveLetter.endsWith(':') ? driveLetter : driveLetter + ':';

        // Common installation directories
        const commonDirs = [
            'Program Files',
            'Program Files (x86)',
            'Games',
            'SteamLibrary',
            'Epic Games',
            'Origin Games',
            'EA Games',
            'EA App'
        ];

        // Common game subdirectories
        const gameSubdirs = [
            'The Sims 4',
            'Steam\\steamapps\\common\\The Sims 4',
            'Origin Games\\The Sims 4',
            'EA Games\\The Sims 4',
            'EA App\\games\\The Sims 4'
        ];

        // Generate all combinations
        for (const dir of commonDirs) {
            for (const subdir of gameSubdirs) {
                paths.push(path.join(drive + '\\', dir, subdir));
            }
            // Also check direct installation in common directories
            paths.push(path.join(drive + '\\', dir, 'The Sims 4'));
        }

        // Add root-level installations (less common but possible)
        paths.push(path.join(drive + '\\', 'The Sims 4'));
        paths.push(path.join(drive + '\\', 'Sims4'));

        return paths;
    }

    /**
     * Get Steam libraries from Steam configuration
     */
    private async getSteamLibraries(): Promise<SteamLibrary[]> {
        // Use cache if available and fresh
        if (this.steamLibraryCache && Date.now() - this.cacheTimestamp < this.CACHE_DURATION) {
            return this.steamLibraryCache;
        }

        const libraries: SteamLibrary[] = [];

        try {
            // Common Steam installation paths
            const steamPaths = [
                path.join(process.env.ProgramFiles || 'C:\\Program Files', 'Steam'),
                path.join(process.env['ProgramFiles(x86)'] || 'C:\\Program Files (x86)', 'Steam'),
                'C:\\Program Files (x86)\\Steam',
                'C:\\Steam'
            ];

            for (const steamPath of steamPaths) {
                const configPath = path.join(steamPath, 'config', 'libraryfolders.vdf');

                if (fs.existsSync(configPath)) {
                    this.logger.debug(`Found Steam config at: ${configPath}`);

                    try {
                        const configContent = fs.readFileSync(configPath, 'utf8');
                        const libraryPaths = this.parseSteamLibraryConfig(configContent);

                        for (const libPath of libraryPaths) {
                            libraries.push({
                                path: libPath,
                                label: `Steam Library (${libPath})`,
                                mounted: fs.existsSync(libPath)
                            });
                        }
                    } catch (error) {
                        this.logger.debug(`Error reading Steam config ${configPath}:`, error);
                    }
                }
            }

            // Update cache
            this.steamLibraryCache = libraries;
            this.cacheTimestamp = Date.now();

            this.logger.debug(`Found ${libraries.length} Steam libraries`);
            return libraries;
        } catch (error) {
            this.logger.error('Error getting Steam libraries:', error);
            return [];
        }
    }

    /**
     * Parse Steam library configuration file
     */
    private parseSteamLibraryConfig(content: string): string[] {
        const paths: string[] = [];

        try {
            // Simple VDF parsing - look for "path" entries
            const lines = content.split('\n');

            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed.startsWith('"path"')) {
                    // Extract path from: "path"		"D:\\SteamLibrary"
                    const match = trimmed.match(/"path"\s+"([^"]+)"/);
                    if (match && match[1]) {
                        let libPath = match[1];
                        // Convert forward slashes to backslashes on Windows
                        if (os.platform() === 'win32') {
                            libPath = libPath.replace(/\//g, '\\');
                        }
                        paths.push(libPath);
                    }
                }
            }
        } catch (error) {
            this.logger.debug('Error parsing Steam library config:', error);
        }

        return paths;
    }

    /**
     * Map Windows drive type number to string
     */
    private mapDriveType(driveType: number): 'fixed' | 'removable' | 'network' | 'unknown' {
        switch (driveType) {
            case 2: return 'removable';  // Floppy, USB, etc.
            case 3: return 'fixed';      // Hard disk
            case 4: return 'network';    // Network drive
            case 5: return 'removable';  // CD-ROM
            default: return 'unknown';
        }
    }

    /**
     * Scan custom paths for Sims 4 installations
     */
    public async scanCustomPaths(customPaths: string[]): Promise<GameInstallation[]> {
        await this.initialize();

        const installations: GameInstallation[] = [];
        this.logger.info(`Scanning ${customPaths.length} custom paths for Sims 4 installations...`);

        for (const customPath of customPaths) {
            try {
                if (fs.existsSync(customPath)) {
                    const installation = await this.validateGameInstallation(customPath);
                    if (installation) {
                        installations.push(installation);
                        await this.saveGameInstallation(installation);
                        this.logger.info(`Found valid Sims 4 installation at custom path: ${customPath}`);
                    }
                }
            } catch (error) {
                this.logger.debug(`Error checking custom path ${customPath}:`, error);
            }
        }

        return installations;
    }

    /**
     * Get information about available drives
     */
    public async getDriveInformation(): Promise<DriveInfo[]> {
        return this.getAvailableDrives();
    }

    /**
     * Get Steam library information
     */
    public async getSteamLibraryInformation(): Promise<SteamLibrary[]> {
        return this.getSteamLibraries();
    }

    /**
     * Clear caches to force fresh detection
     */
    public clearCache(): void {
        this.driveCache = null;
        this.steamLibraryCache = null;
        this.cacheTimestamp = 0;
        this.logger.debug('Detection caches cleared');
    }

    /**
     * Check if a TGI key belongs to base game content
     */
    public async isBaseGameResource(tgiKey: string): Promise<boolean> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(
                'SELECT 1 FROM game_resources WHERE tgi_key = ? LIMIT 1',
                [tgiKey]
            );

            return result.length > 0;
        } catch (error) {
            this.logger.error('Error checking if resource is base game:', error);
            return false;
        }
    }
}
