/**
 * Consolidated Stream Pipeline
 *
 * This module provides a unified pipeline for processing resources in a streaming fashion.
 * It combines the best features of StreamPipeline and EnhancedStreamPipeline, with
 * improved memory management, error handling, and performance.
 */

import { Readable, Transform, pipeline } from 'stream';
import { promisify } from 'util';
import { Logger } from '../../../utils/logging/logger.js';
import { IStreamPipeline, StreamPipelineBase, StreamPipelineOptions } from './streamPipelineBase.js';
import { IStreamTransformer } from './baseStreamTransformer.js';
import { SimDataTransformer } from './simDataTransformer.js';
import { ModelResourceTransformer } from './transformers/ModelResourceTransformer.js';
import { AnimationResourceTransformer } from './transformers/AnimationResourceTransformer.js';
import EnhancedMemoryManager from '../../../utils/memory/enhancedMemoryManager.js';
import { AdaptiveProcessingManager, WorkloadType } from '../adaptive/AdaptiveProcessingManager.js';

// Promisify pipeline for async/await usage
const pipelineAsync = promisify(pipeline);

// Create a logger for this module
const logger = new Logger('ConsolidatedStreamPipeline');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get adaptive processing manager instance
const adaptiveProcessingManager = AdaptiveProcessingManager.getInstance();

/**
 * Consolidated stream pipeline options
 */
export interface ConsolidatedStreamPipelineOptions extends StreamPipelineOptions {
    // Hardware-aware options
    enableHardwareAwareness?: boolean;
    workloadType?: WorkloadType;
    resourceSize?: number;

    // Additional transformer options
    modelTransformerOptions?: any;
    animationTransformerOptions?: any;
    simDataTransformerOptions?: any;

    // Performance options
    enablePerformanceLogging?: boolean;
    performanceSamplingRate?: number;
}

/**
 * Consolidated stream pipeline implementation
 */
export class ConsolidatedStreamPipeline extends StreamPipelineBase {
    private transformerRegistry: Map<number, () => IStreamTransformer> = new Map();
    private performanceTrackingEnabled = false;
    private performanceStartTime = 0;
    private performanceResourceType = 0;
    private performanceResourceSize = 0;

    /**
     * Create a new consolidated stream pipeline
     * @param options Pipeline options
     */
    constructor(options: ConsolidatedStreamPipelineOptions = {}) {
        super(options);

        // Initialize transformer registry
        this.initializeTransformerRegistry();

        // Set performance options
        this.performanceTrackingEnabled = options.enablePerformanceLogging || false;

        logger.info('Created consolidated stream pipeline');
    }

    /**
     * Create a pipeline for a specific resource type
     * @param resourceType Resource type
     * @param source Source stream
     * @param options Pipeline options
     */
    public async createPipeline(
        resourceType: number,
        source: Readable,
        options: ConsolidatedStreamPipelineOptions = {}
    ): Promise<Readable> {
        // Merge options
        const mergedOptions = { ...this.options, ...options } as ConsolidatedStreamPipelineOptions;

        // Start performance tracking
        if (mergedOptions.enablePerformanceLogging &&
            Math.random() < (mergedOptions.performanceSamplingRate || 0.1)) {
            this.startPerformanceTracking(resourceType, mergedOptions.resourceSize || 0);
        }

        // Store source
        this.source = source;

        // Initialize stats
        this.stats = {
            resourceType,
            startTime: Date.now(),
            endTime: 0,
            bytesProcessed: 0,
            transformerStats: {}
        };

        // Create transformers based on resource type and options
        this.transformers = this.createTransformersForResourceType(resourceType, mergedOptions);

        // Initialize transformers
        for (const transformer of this.transformers) {
            await transformer.initialize();
        }

        // Set up event listeners for transformers
        this.setupTransformerEvents();

        // Create the pipeline
        const streams: (Readable | Transform)[] = [source, ...this.transformers];

        // Start the pipeline
        this.pipelinePromise = pipelineAsync(streams)
            .then(() => {
                // Update stats
                this.stats.endTime = Date.now();
                this.stats.duration = this.stats.endTime - this.stats.startTime;

                // Update memory usage
                this.stats.memoryUsage = {
                    heapUsed: process.memoryUsage().heapUsed,
                    heapTotal: process.memoryUsage().heapTotal,
                    external: process.memoryUsage().external,
                    arrayBuffers: process.memoryUsage().arrayBuffers || 0
                };

                // End performance tracking
                if (this.performanceTrackingEnabled) {
                    this.endPerformanceTracking();
                }

                logger.debug(`Pipeline completed for resource type ${resourceType.toString(16)}`);

                // Emit final stats
                if (mergedOptions.emitStats) {
                    this.emit('stats', this.getStats());
                }
            })
            .catch((error) => {
                // Update stats with error
                this.stats.endTime = Date.now();
                this.stats.duration = this.stats.endTime - this.stats.startTime;
                this.stats.error = error.message;

                // End performance tracking
                if (this.performanceTrackingEnabled) {
                    this.endPerformanceTracking(error);
                }

                logger.error(`Pipeline error for resource type ${resourceType.toString(16)}: ${error.message}`);

                // Emit error
                this.emit('error', error);

                // Re-throw the error
                throw error;
            });

        // Return the last transformer as the destination
        this.destination = this.transformers[this.transformers.length - 1] as Readable;
        return this.destination;
    }

    /**
     * Register a transformer factory for a resource type
     * @param resourceType Resource type
     * @param factory Factory function to create a transformer
     */
    public registerTransformer(
        resourceType: number,
        factory: () => IStreamTransformer
    ): void {
        this.transformerRegistry.set(resourceType, factory);
        logger.debug(`Registered transformer for resource type ${resourceType.toString(16)}`);
    }

    /**
     * Initialize transformer registry with default transformers
     * @private
     */
    private initializeTransformerRegistry(): void {
        // Register SimData transformer
        this.registerTransformer(0x545AC67A, () => {
            return new SimDataTransformer(this.options.simDataTransformerOptions);
        });

        // Register Model transformers
        const modelTypes = [0x01D10F34, 0x01661233];
        for (const type of modelTypes) {
            this.registerTransformer(type, () => {
                return new ModelResourceTransformer(this.options.modelTransformerOptions);
            });
        }

        // Register Animation transformers
        const animationTypes = [0x6B20C4F3, 0xAC16FBEC];
        for (const type of animationTypes) {
            this.registerTransformer(type, () => {
                return new AnimationResourceTransformer(this.options.animationTransformerOptions);
            });
        }

        logger.debug('Initialized transformer registry with default transformers');
    }

    /**
     * Create transformers for a specific resource type
     * @param resourceType Resource type
     * @param options Pipeline options
     * @returns Array of transformers
     * @private
     */
    private createTransformersForResourceType(
        resourceType: number,
        options: ConsolidatedStreamPipelineOptions
    ): IStreamTransformer[] {
        const transformers: IStreamTransformer[] = [];

        // Check if we have a registered transformer for this resource type
        if (this.transformerRegistry.has(resourceType)) {
            const transformer = this.transformerRegistry.get(resourceType)!();
            transformers.push(transformer);
        } else {
            // Use a pass-through transformer for unknown resource types
            const passThrough = new Transform({
                transform(chunk, encoding, callback) {
                    callback(null, chunk);
                }
            }) as IStreamTransformer;

            // Add basic methods to satisfy IStreamTransformer interface
            passThrough.getName = () => 'PassThrough';
            passThrough.getStats = () => ({
                transformer: 'PassThrough',
                bytesProcessed: 0,
                chunkCount: 0,
                errorCount: 0,
                retryCount: 0,
                startTime: Date.now(),
                endTime: Date.now(),
                duration: 0,
                memoryUsage: 0
            });
            passThrough.getProgress = () => 1;
            passThrough.initialize = async () => {};
            passThrough.cleanup = async () => {};
            passThrough.canRecover = () => false;
            passThrough.recoverFromError = () => {};
            passThrough.reset = () => {};
            passThrough.setOptions = () => {};

            transformers.push(passThrough);
        }

        return transformers;
    }

    /**
     * Start performance tracking
     * @param resourceType Resource type
     * @param resourceSize Resource size
     * @private
     */
    private startPerformanceTracking(resourceType: number, resourceSize: number): void {
        this.performanceStartTime = Date.now();
        this.performanceResourceType = resourceType;
        this.performanceResourceSize = resourceSize;

        logger.debug(`Started performance tracking for resource type ${resourceType.toString(16)}`);
    }

    /**
     * End performance tracking
     * @param error Optional error
     * @private
     */
    private endPerformanceTracking(error?: Error): void {
        const endTime = Date.now();
        const duration = endTime - this.performanceStartTime;

        logger.info(`Performance: Resource type ${this.performanceResourceType.toString(16)}, size ${this.formatBytes(this.performanceResourceSize)}, duration ${duration}ms${error ? ', error: ' + error.message : ''}`);
    }

    /**
     * Format bytes to a human-readable string
     * @param bytes Number of bytes
     * @returns Formatted string
     * @private
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
