/**
 * Error Types
 *
 * This module defines the error types and interfaces used throughout the application.
 * It provides a consistent way to handle errors and report them to users and telemetry.
 */

/**
 * Error categories for classifying errors
 */
export enum ErrorCategory {
    VALIDATION = 'validation',
    RESOURCE = 'resource',
    DATABASE = 'database',
    MEMORY = 'memory',
    SYSTEM = 'system',
    UNKNOWN = 'unknown'
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
    FATAL = 'fatal',
    ERROR = 'error',
    WARNING = 'warning',
    INFO = 'info'
}

/**
 * Error codes for specific error types
 */
export enum ErrorCode {
    // Validation errors (1000-1999)
    INVALID_INPUT = 1000,
    MISSING_REQUIRED_FIELD = 1001,
    INVALID_FORMAT = 1002,
    INVALID_TYPE = 1003,
    INVALID_VALUE = 1004,
    INVALID_STATE = 1005,

    // Resource errors (2000-2999)
    RESOURCE_NOT_FOUND = 2000,
    RESOURCE_ACCESS_DENIED = 2001,
    RESOURCE_CORRUPTED = 2002,
    RESOURCE_ALREADY_EXISTS = 2003,
    RESOURCE_LOCKED = 2004,
    RESOURCE_TIMEOUT = 2005,
    RESOURCE_TOO_LARGE = 2006,
    RESOURCE_PROCESSING_ERROR = 2007,

    // Database errors (3000-3999)
    DATABASE_CONNECTION_ERROR = 3000,
    DATABASE_QUERY_ERROR = 3001,
    DATABASE_TRANSACTION_ERROR = 3002,
    DATABASE_CONSTRAINT_ERROR = 3003,
    DATABASE_TIMEOUT_ERROR = 3004,
    DATABASE_SCHEMA_ERROR = 3005,

    // Memory errors (4000-4999)
    MEMORY_ALLOCATION_ERROR = 4000,
    MEMORY_LIMIT_EXCEEDED = 4001,
    MEMORY_LEAK_DETECTED = 4002,
    BUFFER_OVERFLOW = 4003,

    // System errors (5000-5999)
    SYSTEM_IO_ERROR = 5000,
    SYSTEM_NETWORK_ERROR = 5001,
    SYSTEM_PROCESS_ERROR = 5002,
    SYSTEM_TIMEOUT = 5003,
    OPERATION_REJECTED = 5004,
    OPERATION_TIMEOUT = 5005,

    // Unknown errors (9000-9999)
    UNKNOWN_ERROR = 9000
}

/**
 * Error context information
 */
export interface ErrorContext {
    component?: string;
    operation?: string;
    resource?: {
        type?: string | number;
        id?: string | number;
        name?: string;
        path?: string;
    };
    parameters?: Record<string, any>;
    systemState?: Record<string, any>;
    userAction?: string;
    timestamp?: number;
    additionalInfo?: Record<string, any>;
}

/**
 * User-friendly error message
 */
export interface ErrorMessage {
    summary: string;
    details?: string;
    cause?: string;
    impact?: string;
    resolution?: string;
    nextSteps?: string;
}

/**
 * Recovery options for error handling
 */
export interface RecoveryOptions {
    canRetry: boolean;
    maxRetries?: number;
    retryDelay?: number;
    canFallback: boolean;
    fallbackOperation?: string;
    canSkip: boolean;
    canAbort: boolean;
    canRestart: boolean;
}

/**
 * Error report for telemetry
 */
export interface ErrorReport {
    errorCode: ErrorCode;
    category: ErrorCategory;
    severity: ErrorSeverity;
    message: ErrorMessage;
    context: ErrorContext;
    recovery: RecoveryOptions;
    stackTrace?: string;
    timestamp: number;
    sessionId?: string;
    userId?: string;
    appVersion?: string;
    osInfo?: {
        platform?: string;
        version?: string;
        arch?: string;
    };
}

/**
 * Base application error class
 */
export class AppError extends Error {
    public readonly errorCode: ErrorCode;
    public readonly category: ErrorCategory;
    public readonly severity: ErrorSeverity;
    public readonly context: ErrorContext;
    public readonly userMessage: ErrorMessage;
    public readonly recovery: RecoveryOptions;
    public readonly timestamp: number;

    /**
     * Create a new application error
     * @param errorCode Error code
     * @param message Error message
     * @param options Additional error options
     */
    constructor(
        errorCode: ErrorCode,
        message: string,
        options: {
            category?: ErrorCategory;
            severity?: ErrorSeverity;
            context?: ErrorContext;
            userMessage?: Partial<ErrorMessage>;
            recovery?: Partial<RecoveryOptions>;
            cause?: Error;
        } = {}
    ) {
        super(message, { cause: options.cause });

        this.errorCode = errorCode;
        this.category = options.category || this.getCategoryFromCode(errorCode);
        this.severity = options.severity || this.getSeverityFromCode(errorCode);
        this.context = {
            timestamp: Date.now(),
            ...options.context
        };
        this.userMessage = {
            summary: options.userMessage?.summary || message,
            details: options.userMessage?.details,
            cause: options.userMessage?.cause,
            impact: options.userMessage?.impact,
            resolution: options.userMessage?.resolution,
            nextSteps: options.userMessage?.nextSteps
        };
        this.recovery = {
            canRetry: options.recovery?.canRetry ?? false,
            maxRetries: options.recovery?.maxRetries,
            retryDelay: options.recovery?.retryDelay,
            canFallback: options.recovery?.canFallback ?? false,
            fallbackOperation: options.recovery?.fallbackOperation,
            canSkip: options.recovery?.canSkip ?? false,
            canAbort: options.recovery?.canAbort ?? true,
            canRestart: options.recovery?.canRestart ?? false
        };
        this.timestamp = Date.now();

        // Capture stack trace
        Error.captureStackTrace(this, this.constructor);
    }

    /**
     * Get error category from error code
     * @param errorCode Error code
     * @returns Error category
     * @private
     */
    private getCategoryFromCode(errorCode: ErrorCode): ErrorCategory {
        if (errorCode >= 1000 && errorCode < 2000) return ErrorCategory.VALIDATION;
        if (errorCode >= 2000 && errorCode < 3000) return ErrorCategory.RESOURCE;
        if (errorCode >= 3000 && errorCode < 4000) return ErrorCategory.DATABASE;
        if (errorCode >= 4000 && errorCode < 5000) return ErrorCategory.MEMORY;
        if (errorCode >= 5000 && errorCode < 6000) return ErrorCategory.SYSTEM;
        return ErrorCategory.UNKNOWN;
    }

    /**
     * Get error severity from error code
     * @param errorCode Error code
     * @returns Error severity
     * @private
     */
    private getSeverityFromCode(errorCode: ErrorCode): ErrorSeverity {
        // Fatal errors
        if ([
            ErrorCode.MEMORY_LIMIT_EXCEEDED,
            ErrorCode.DATABASE_CONNECTION_ERROR,
            ErrorCode.SYSTEM_PROCESS_ERROR
        ].includes(errorCode)) {
            return ErrorSeverity.FATAL;
        }

        // Warning errors
        if ([
            ErrorCode.RESOURCE_TIMEOUT,
            ErrorCode.DATABASE_TIMEOUT_ERROR,
            ErrorCode.SYSTEM_TIMEOUT
        ].includes(errorCode)) {
            return ErrorSeverity.WARNING;
        }

        // Default to ERROR severity
        return ErrorSeverity.ERROR;
    }

    /**
     * Create an error report for telemetry
     * @param options Additional report options
     * @returns Error report
     */
    public createReport(options: {
        sessionId?: string;
        userId?: string;
        appVersion?: string;
        osInfo?: {
            platform?: string;
            version?: string;
            arch?: string;
        };
    } = {}): ErrorReport {
        return {
            errorCode: this.errorCode,
            category: this.category,
            severity: this.severity,
            message: this.userMessage,
            context: this.context,
            recovery: this.recovery,
            stackTrace: this.stack,
            timestamp: this.timestamp,
            sessionId: options.sessionId,
            userId: options.userId,
            appVersion: options.appVersion,
            osInfo: options.osInfo
        };
    }

    /**
     * Get a user-friendly error message
     * @returns User-friendly error message
     */
    public getUserMessage(): string {
        let message = this.userMessage.summary;

        if (this.userMessage.details) {
            message += `\n\nDetails: ${this.userMessage.details}`;
        }

        if (this.userMessage.resolution) {
            message += `\n\nResolution: ${this.userMessage.resolution}`;
        }

        if (this.userMessage.nextSteps) {
            message += `\n\nNext steps: ${this.userMessage.nextSteps}`;
        }

        return message;
    }
}
