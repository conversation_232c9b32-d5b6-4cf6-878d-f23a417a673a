import { LSHInterface } from './LSHInterface.js';

/**
 * LSH algorithm type
 */
export enum LSHType {
    SIMHASH = 'simhash',
    MINHASH = 'minhash'
}

/**
 * Options for LSH algorithms
 */
export interface LSHOptions {
    /**
     * Number of bits for SimHash (default: 64)
     */
    hashBits?: number;

    /**
     * Number of hash functions for MinHash (default: 100)
     */
    numHashes?: number;

    /**
     * Random seed for hash functions (default: 42)
     */
    seed?: number;
}

/**
 * Factory class for creating LSH instances
 */
export class LSHFactory {
    private static instances: Map<string, LSHInterface> = new Map();
    
    /**
     * Get an LSH instance
     * @param type LSH algorithm type
     * @param options Options for the LSH algorithm
     * @returns LSH instance
     */
    static getInstance(type: LSHType, options: LSHOptions = {}): LSHInterface {
        const key = `${type}_${JSON.stringify(options)}`;
        
        if (!this.instances.has(key)) {
            let instance: LSHInterface;
            
            switch (type) {
                case LSHType.SIMHASH:
                    // Lazy load SimHash to avoid circular dependencies
                    const { SimHash } = require('./SimHash.js');
                    instance = new SimHash(options.hashBits || 64);
                    break;
                case LSHType.MINHASH:
                    // Lazy load MinHash to avoid circular dependencies
                    const { MinHash } = require('./MinHash.js');
                    instance = new MinHash(options.numHashes || 100, options.seed || 42);
                    break;
                default:
                    throw new Error(`Unknown LSH type: ${type}`);
            }
            
            this.instances.set(key, instance);
        }
        
        return this.instances.get(key)!;
    }
}
