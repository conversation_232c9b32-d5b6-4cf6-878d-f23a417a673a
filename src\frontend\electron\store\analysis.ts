import { defineS<PERSON> } from 'pinia';
import { ConflictInfo } from '../../../types/conflict/index';

// Define the structure of the analysis state
export interface AnalysisState {
  isAnalyzing: boolean;
  progress: number;
  status: string;
  conflicts: ConflictInfo[];
  recommendations: string[];
  metrics: Record<string, any>;
  error: string | null;
  currentFiles: File[]; // Keep track of files being analyzed (or perhaps file paths after refactor)
}

export const useAnalysisStore = defineStore('analysis', {
  state: (): AnalysisState => ({
    isAnalyzing: false,
    progress: 0,
    status: 'Ready',
    conflicts: [],
    recommendations: [],
    metrics: {},
    error: null,
    currentFiles: [], // This might store file paths now instead of File objects
  }),

  actions: {
    // Action called when main process starts analysis (after file dialog)
    // Might need adjustment based on how main process communicates start
    setAnalysisRunning(fileCount: number) {
      this.isAnalyzing = true;
      this.progress = 0;
      this.status = `Analyzing ${fileCount} file(s)...`;
      this.conflicts = [];
      this.recommendations = [];
      this.metrics = {};
      this.error = null;
      // this.currentFiles = []; // Clear previous files? Or store paths? Needs decision.
    },

    // Action to update progress (called via IPC listener)
    updateProgress(progress: number, status: string) {
      if (this.isAnalyzing) {
        this.progress = progress;
        this.status = status;
      }
    },

    // Action to set final results (called via IPC listener)
    // The 'data' type should match the structure returned by the processSelectedFiles method in main.js
    setResults(data: {
        individualResults: any[]; // Replace 'any' with actual PackageAnalysisResult type if imported
        crossPackageConflicts: ConflictInfo[];
        metrics: Record<string, any>
     }) {
      console.log('Setting results with data:', data);

      // Extract conflicts from the data
      this.conflicts = data.crossPackageConflicts || [];

      // Extract recommendations from individual results and conflicts
      const allRecommendations: any[] = [];

      // Add recommendations from individual results if they exist
      if (data.individualResults && Array.isArray(data.individualResults)) {
        for (const result of data.individualResults) {
          if (result.recommendations && Array.isArray(result.recommendations)) {
            allRecommendations.push(...result.recommendations);
          }
        }
      }

      // Add recommendations from conflicts
      if (data.crossPackageConflicts && Array.isArray(data.crossPackageConflicts)) {
        for (const conflict of data.crossPackageConflicts) {
          if (conflict.recommendations && Array.isArray(conflict.recommendations)) {
            allRecommendations.push(...conflict.recommendations);
          }
        }
      }

      // Remove duplicates and set recommendations
      this.recommendations = [...new Set(allRecommendations)];

      // Set metrics
      this.metrics = data.metrics || {};

      // Update UI state
      this.isAnalyzing = false;
      this.progress = 100;
      this.status = 'Analysis complete.';

      // Store file count in currentFiles (as a placeholder)
      if (data.metrics && typeof data.metrics.fileCount === 'number') {
        this.currentFiles = new Array(data.metrics.fileCount).fill(null);
      } else {
        this.currentFiles = [];
      }

      console.log('Results set successfully');
    },

    // Action to set an error state (called via IPC listener)
    setError(errorMessage: string) {
      this.error = errorMessage;
      this.isAnalyzing = false;
      this.status = 'Analysis failed.';
      this.progress = 0;
    },

    // Action to reset the store to its initial state
    reset() {
      this.$reset();
    },

    // Removed initializeIpcListeners action
  },

  getters: {
    hasConflicts: (state) => state.conflicts.length > 0,
    hasError: (state) => state.error !== null,
    // Getter for the count of files from the last analysis run
    analyzedFilesCount: (state) => state.currentFiles.length, // Use currentFiles for count
  },
});
