import { Logger } from '../../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../../types/database.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../../types/conflict/ConflictTypes.js';
import { ResourceKey } from '../../../../types/resource/interfaces.js';
import { DatabaseService } from '../../../databaseService.js';
import { ParsedSimData, SimDataSchema } from '../../../analysis/extractors/simdata/simDataParser.js';
import { SimDataSchemaParser } from '../../../analysis/extractors/simdata/simDataSchemaParser.js';
import { SchemaCompatibilityInfo } from '../../../analysis/extractors/simdata/schema/schemaInterfaces.js';
import { SimDataInstanceAnalyzer } from '../../../analysis/extractors/simdata/simDataInstanceAnalyzer.js';

const log = new Logger('SimDataConflictDetector');

/**
 * Interface for SimData conflict detection options
 */
export interface SimDataConflictDetectionOptions {
    /**
     * Whether to check for schema conflicts
     */
    checkSchemaConflicts: boolean;

    /**
     * Whether to check for instance conflicts
     */
    checkInstanceConflicts: boolean;

    /**
     * Whether to check for value conflicts
     */
    checkValueConflicts: boolean;

    /**
     * Whether to check for critical gameplay conflicts
     */
    checkCriticalConflicts: boolean;

    /**
     * Minimum severity to report
     */
    minSeverity: ConflictSeverity;

    /**
     * Maximum number of conflicts to return
     */
    maxConflicts: number;
}

/**
 * Default options for SimData conflict detection
 */
const DEFAULT_OPTIONS: SimDataConflictDetectionOptions = {
    checkSchemaConflicts: true,
    checkInstanceConflicts: true,
    checkValueConflicts: true,
    checkCriticalConflicts: true,
    minSeverity: ConflictSeverity.LOW,
    maxConflicts: 100
};

/**
 * SimData Conflict Detector
 * Responsible for detecting conflicts between SimData resources
 */
export class SimDataConflictDetector {
    private logger: Logger;
    private databaseService: DatabaseService;
    private schemaParser: SimDataSchemaParser;
    private instanceAnalyzer: SimDataInstanceAnalyzer;

    constructor(databaseService: DatabaseService, logger?: Logger) {
        this.logger = logger || log;
        this.databaseService = databaseService;
        this.schemaParser = new SimDataSchemaParser(databaseService, this.logger);
        this.instanceAnalyzer = new SimDataInstanceAnalyzer(this.logger);
    }

    /**
     * Detect conflicts between two SimData resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @param options Conflict detection options
     * @returns Array of detected conflicts
     */
    public async detectConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        options: Partial<SimDataConflictDetectionOptions> = {}
    ): Promise<ConflictInfo[]> {
        // Merge options with defaults
        const mergedOptions: SimDataConflictDetectionOptions = {
            ...DEFAULT_OPTIONS,
            ...options
        };

        const conflicts: ConflictInfo[] = [];

        try {
            // Get parsed SimData content from database
            const parsedContent1 = await this.getParsedSimData(resource1.id);
            const parsedContent2 = await this.getParsedSimData(resource2.id);

            if (!parsedContent1 || !parsedContent2) {
                this.logger.warn(`Cannot detect conflicts: missing parsed SimData content`);
                return conflicts;
            }

            // Check for schema conflicts
            if (mergedOptions.checkSchemaConflicts) {
                const schemaConflicts = this.detectSchemaConflicts(
                    resource1,
                    resource2,
                    parsedContent1,
                    parsedContent2
                );

                conflicts.push(...schemaConflicts);
            }

            // Check for instance conflicts
            if (mergedOptions.checkInstanceConflicts) {
                const instanceConflicts = this.detectInstanceConflicts(
                    resource1,
                    resource2,
                    parsedContent1,
                    parsedContent2
                );

                conflicts.push(...instanceConflicts);
            }

            // Check for value conflicts
            if (mergedOptions.checkValueConflicts) {
                const valueConflicts = this.detectValueConflicts(
                    resource1,
                    resource2,
                    parsedContent1,
                    parsedContent2,
                    mergedOptions.checkCriticalConflicts
                );

                conflicts.push(...valueConflicts);
            }

            // Filter conflicts by severity
            const filteredConflicts = conflicts.filter(conflict =>
                this.getSeverityValue(conflict.severity) >= this.getSeverityValue(mergedOptions.minSeverity)
            );

            // Limit number of conflicts
            return filteredConflicts.slice(0, mergedOptions.maxConflicts);
        } catch (error) {
            this.logger.error(`Error detecting SimData conflicts: ${error}`);
            return conflicts;
        }
    }

    /**
     * Get parsed SimData content from database
     * @param resourceId Resource ID
     * @returns Parsed SimData or undefined if not found
     */
    private async getParsedSimData(resourceId: number): Promise<ParsedSimData | undefined> {
        try {
            // Get parsed content from database
            const parsedContentRow = await this.databaseService.parsedContent.getParsedContentByResourceId(
                resourceId,
                'simdata'
            );

            if (!parsedContentRow || !parsedContentRow.content) {
                return undefined;
            }

            // Parse JSON content
            return JSON.parse(parsedContentRow.content).simData as ParsedSimData;
        } catch (error) {
            this.logger.error(`Error getting parsed SimData content: ${error}`);
            return undefined;
        }
    }

    /**
     * Detect conflicts between SimData schemas
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param simData1 First SimData content
     * @param simData2 Second SimData content
     * @returns Array of detected conflicts
     */
    private detectSchemaConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        simData1: ParsedSimData,
        simData2: ParsedSimData
    ): ConflictInfo[] {
        const conflicts: ConflictInfo[] = [];

        // Check if both resources have schemas
        if (!simData1.schema || !simData2.schema) {
            return conflicts;
        }

        // Compare schemas
        const compatibility = this.schemaParser.compareSchemas(simData1.schema, simData2.schema);

        // Check for schema name conflicts
        if (simData1.schema.name === simData2.schema.name && simData1.schema.hash !== simData2.schema.hash) {
            conflicts.push({
                id: `schema-name-${resource1.id}-${resource2.id}`,
                type: ConflictType.TUNING,
                severity: ConflictSeverity.HIGH,
                description: `SimData schemas have same name (${simData1.schema.name}) but different content`,
                affectedResources: [
                    { type: resource1.type, group: resource1.group, instance: resource1.instance },
                    { type: resource2.type, group: resource2.group, instance: resource2.instance }
                ],
                timestamp: Date.now(),
                recommendations: [
                    'Check if both mods modify the same game functionality',
                    'Load mods in the correct order to ensure proper overrides',
                    'Consider using only one of the conflicting mods'
                ]
            });
        }

        // Check for schema compatibility conflicts
        if (!compatibility.isCompatible) {
            // Determine severity based on incompatibility
            let severity = ConflictSeverity.MEDIUM;

            if (compatibility.incompatibleColumns.length > 0) {
                severity = ConflictSeverity.HIGH;
            } else if (compatibility.missingColumns.length > 0) {
                severity = ConflictSeverity.MEDIUM;
            }

            conflicts.push({
                id: `schema-compat-${resource1.id}-${resource2.id}`,
                type: ConflictType.TUNING,
                severity,
                description: this.generateSchemaConflictDescription(compatibility),
                affectedResources: [
                    { type: resource1.type, group: resource1.group, instance: resource1.instance },
                    { type: resource2.type, group: resource2.group, instance: resource2.instance }
                ],
                timestamp: Date.now(),
                recommendations: [
                    'Check if both mods modify the same game functionality',
                    'Load mods in the correct order to ensure proper overrides',
                    'Consider using only one of the conflicting mods'
                ]
            });
        }

        return conflicts;
    }

    /**
     * Generate a description for a schema conflict
     * @param compatibility Schema compatibility information
     * @returns Conflict description
     */
    private generateSchemaConflictDescription(compatibility: SchemaCompatibilityInfo): string {
        const parts: string[] = [];

        parts.push(`SimData schemas are incompatible (${compatibility.compatibilityScore}% compatible)`);

        if (compatibility.incompatibleColumns.length > 0) {
            parts.push(`${compatibility.incompatibleColumns.length} columns have type mismatches`);
        }

        if (compatibility.missingColumns.length > 0) {
            parts.push(`${compatibility.missingColumns.length} columns are missing`);
        }

        if (compatibility.extraColumns.length > 0) {
            parts.push(`${compatibility.extraColumns.length} extra columns`);
        }

        return parts.join(', ');
    }

    /**
     * Detect conflicts between SimData instances
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param simData1 First SimData content
     * @param simData2 Second SimData content
     * @returns Array of detected conflicts
     */
    private detectInstanceConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        simData1: ParsedSimData,
        simData2: ParsedSimData
    ): ConflictInfo[] {
        const conflicts: ConflictInfo[] = [];

        // Check if both resources have instances
        if (!simData1.instances || !simData2.instances) {
            return conflicts;
        }

        // Create maps of instances by name for easy lookup
        const instances1 = new Map(simData1.instances.map(instance => [instance.name, instance]));
        const instances2 = new Map(simData2.instances.map(instance => [instance.name, instance]));

        // Check for instances with the same name but different IDs
        for (const [name, instance1] of instances1.entries()) {
            const instance2 = instances2.get(name);

            if (instance2 && instance1.instanceId !== instance2.instanceId) {
                conflicts.push({
                    id: `instance-id-${resource1.id}-${resource2.id}-${name}`,
                    type: ConflictType.TUNING,
                    severity: ConflictSeverity.MEDIUM,
                    description: `SimData instances have same name (${name}) but different IDs`,
                    affectedResources: [
                        { type: resource1.type, group: resource1.group, instance: resource1.instance },
                        { type: resource2.type, group: resource2.group, instance: resource2.instance }
                    ],
                    timestamp: Date.now(),
                    recommendations: [
                        'Check if both mods modify the same game functionality',
                        'Load mods in the correct order to ensure proper overrides',
                        'Consider using only one of the conflicting mods'
                    ]
                });
            }
        }

        // Check for missing instances
        const missingInResource2 = Array.from(instances1.keys())
            .filter(name => !instances2.has(name));

        const missingInResource1 = Array.from(instances2.keys())
            .filter(name => !instances1.has(name));

        if (missingInResource1.length > 0 || missingInResource2.length > 0) {
            conflicts.push({
                id: `instance-missing-${resource1.id}-${resource2.id}`,
                type: ConflictType.TUNING,
                severity: ConflictSeverity.LOW,
                description: this.generateMissingInstancesDescription(missingInResource1, missingInResource2),
                affectedResources: [
                    { type: resource1.type, group: resource1.group, instance: resource1.instance },
                    { type: resource2.type, group: resource2.group, instance: resource2.instance }
                ],
                timestamp: Date.now(),
                recommendations: [
                    'Check if both mods modify the same game functionality',
                    'Load mods in the correct order to ensure proper overrides',
                    'Consider using only one of the conflicting mods'
                ]
            });
        }

        return conflicts;
    }

    /**
     * Generate a description for missing instances
     * @param missingInResource1 Instances missing in resource 1
     * @param missingInResource2 Instances missing in resource 2
     * @returns Conflict description
     */
    private generateMissingInstancesDescription(
        missingInResource1: string[],
        missingInResource2: string[]
    ): string {
        const parts: string[] = [];

        if (missingInResource1.length > 0) {
            parts.push(`${missingInResource1.length} instances missing in first resource`);
        }

        if (missingInResource2.length > 0) {
            parts.push(`${missingInResource2.length} instances missing in second resource`);
        }

        return `SimData instances differ: ${parts.join(', ')}`;
    }

    /**
     * Detect conflicts between SimData values
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param simData1 First SimData content
     * @param simData2 Second SimData content
     * @param checkCriticalOnly Whether to check only critical values
     * @returns Array of detected conflicts
     */
    private detectValueConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        simData1: ParsedSimData,
        simData2: ParsedSimData,
        checkCriticalOnly: boolean
    ): ConflictInfo[] {
        const conflicts: ConflictInfo[] = [];

        // Check if both resources have instances and schemas
        if (!simData1.instances || !simData2.instances || !simData1.schema || !simData2.schema) {
            return conflicts;
        }

        // Create maps of instances by name for easy lookup
        const instances1 = new Map(simData1.instances.map(instance => [instance.name, instance]));
        const instances2 = new Map(simData2.instances.map(instance => [instance.name, instance]));

        // Create maps of columns by name for easy lookup
        const columns1 = new Map(simData1.schema.columns.map(column => [column.name, column]));
        const columns2 = new Map(simData2.schema.columns.map(column => [column.name, column]));

        // Determine critical columns
        const criticalColumns = new Set<string>();

        for (const column of simData1.schema.columns) {
            const name = column.name.toLowerCase();
            const isCritical = [
                'multiplier', 'chance', 'probability', 'duration', 'cost', 'value',
                'weight', 'priority', 'threshold', 'score', 'buff', 'motive',
                'skill', 'trait', 'stat', 'tuning', 'level', 'gain', 'decay'
            ].some(pattern => name.includes(pattern));

            if (isCritical) {
                criticalColumns.add(column.name);
            }
        }

        // Check for value conflicts in common instances
        for (const [name, instance1] of instances1.entries()) {
            const instance2 = instances2.get(name);

            if (!instance2) continue;

            // Check each column in the instance
            for (const [columnName, value1] of Object.entries(instance1.values)) {
                // Skip if column doesn't exist in both schemas
                if (!columns1.has(columnName) || !columns2.has(columnName)) continue;

                // Skip if not a critical column and we're only checking critical columns
                if (checkCriticalOnly && !criticalColumns.has(columnName)) continue;

                const value2 = instance2.values[columnName];

                // Skip if values are the same
                if (this.areValuesEqual(value1, value2)) continue;

                // Determine severity based on column criticality
                const severity = criticalColumns.has(columnName) ?
                    ConflictSeverity.HIGH :
                    ConflictSeverity.LOW;

                conflicts.push({
                    id: `value-${resource1.id}-${resource2.id}-${name}-${columnName}`,
                    type: ConflictType.TUNING,
                    severity,
                    description: `SimData value conflict in instance "${name}", column "${columnName}"`,
                    affectedResources: [
                        { type: resource1.type, group: resource1.group, instance: resource1.instance },
                        { type: resource2.type, group: resource2.group, instance: resource2.instance }
                    ],
                    timestamp: Date.now(),
                    recommendations: [
                        'Check if both mods modify the same game functionality',
                        'Load mods in the correct order to ensure proper overrides',
                        'Consider using only one of the conflicting mods'
                    ]
                });
            }
        }

        return conflicts;
    }

    /**
     * Check if two values are equal
     * @param value1 First value
     * @param value2 Second value
     * @returns True if values are equal
     */
    private areValuesEqual(value1: any, value2: any): boolean {
        // Handle null/undefined
        if (value1 === null || value1 === undefined) {
            return value2 === null || value2 === undefined;
        }

        // Handle different types
        if (typeof value1 !== typeof value2) {
            return false;
        }

        // Handle objects
        if (typeof value1 === 'object') {
            return JSON.stringify(value1) === JSON.stringify(value2);
        }

        // Handle primitives
        return value1 === value2;
    }

    /**
     * Get numeric value for a severity level
     * @param severity Severity level
     * @returns Numeric value
     */
    private getSeverityValue(severity: ConflictSeverity): number {
        switch (severity) {
            case ConflictSeverity.CRITICAL:
                return 4;
            case ConflictSeverity.HIGH:
                return 3;
            case ConflictSeverity.MEDIUM:
                return 2;
            case ConflictSeverity.LOW:
                return 1;
            default:
                return 0;
        }
    }
}
