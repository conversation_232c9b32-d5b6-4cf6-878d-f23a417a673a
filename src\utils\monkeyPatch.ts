/**
 * Monkey Patch Utilities
 *
 * This module provides utilities for monkey patching Node.js classes
 * to fix issues like EventEmitter memory leaks.
 */

import { Logger } from './logging/logger.js';
import { EventEmitter } from 'events';
import * as fs from 'fs';

const logger = new Logger('MonkeyPatch');

/**
 * Apply monkey patches to fix known issues
 */
export function applyMonkeyPatches() {
    try {
        logger.info('Applying monkey patches...');

        // Patch EventEmitter to increase max listeners
        patchEventEmitter();

        // Patch fs.ReadStream and fs.WriteStream to increase max listeners
        patchFileStreams();

        logger.info('Monkey patches applied successfully');
        return true;
    } catch (error) {
        logger.error(`Error applying monkey patches: ${error}`);
        return false;
    }
}

/**
 * Patch EventEmitter to increase max listeners
 */
function patchEventEmitter() {
    try {
        // Store the original setMaxListeners method
        const originalSetMaxListeners = EventEmitter.prototype.setMaxListeners;

        // Override the setMaxListeners method to log calls and handle errors
        EventEmitter.prototype.setMaxListeners = function(n: number) {
            try {
                logger.debug(`Setting max listeners to ${n} for ${this.constructor.name}`);
                return originalSetMaxListeners.call(this, n);
            } catch (error) {
                logger.error(`Error setting max listeners for ${this.constructor.name}: ${error}`);
                // Return this to maintain method chaining
                return this;
            }
        };

        // Set default max listeners to a higher value
        EventEmitter.defaultMaxListeners = 100;
        logger.info('Patched EventEmitter.prototype.setMaxListeners');

        // Patch the addListener method to automatically increase max listeners if needed
        const originalAddListener = EventEmitter.prototype.addListener;

        EventEmitter.prototype.addListener = function(event: string, listener: (...args: any[]) => void) {
            // Get the current number of listeners for this event
            const currentListeners = this.listeners(event).length;

            // If we're about to exceed the max listeners, increase it
            if (currentListeners >= this.getMaxListeners()) {
                const newMax = currentListeners + 10;
                this.setMaxListeners(newMax);
                logger.debug(`Automatically increased max listeners to ${newMax} for ${this.constructor.name}`);
            }

            return originalAddListener.call(this, event, listener);
        };

        // Alias on to addListener
        EventEmitter.prototype.on = EventEmitter.prototype.addListener;

        logger.info('Patched EventEmitter.prototype.addListener');
    } catch (error) {
        logger.error(`Error patching EventEmitter: ${error}`);
    }
}

/**
 * Patch fs.ReadStream and fs.WriteStream to increase max listeners
 */
function patchFileStreams() {
    try {
        // Check if we can patch fs.ReadStream
        if (fs.ReadStream && fs.ReadStream.prototype) {
            try {
                // Try to patch the prototype instead of the constructor
                const originalReadStreamInit = fs.ReadStream.prototype._construct || function() {};

                // Override the _construct method to set max listeners
                fs.ReadStream.prototype._construct = function(callback: any) {
                    try {
                        this.setMaxListeners(100);
                    } catch (error) {
                        // Ignore errors setting max listeners
                    }
                    return originalReadStreamInit.call(this, callback);
                };

                logger.info('Patched fs.ReadStream prototype');
            } catch (error) {
                logger.debug(`Could not patch fs.ReadStream: ${error}`);
            }
        }

        // Check if we can patch fs.WriteStream
        if (fs.WriteStream && fs.WriteStream.prototype) {
            try {
                // Try to patch the prototype instead of the constructor
                const originalWriteStreamInit = fs.WriteStream.prototype._construct || function() {};

                // Override the _construct method to set max listeners
                fs.WriteStream.prototype._construct = function(callback: any) {
                    try {
                        this.setMaxListeners(100);
                    } catch (error) {
                        // Ignore errors setting max listeners
                    }
                    return originalWriteStreamInit.call(this, callback);
                };

                logger.info('Patched fs.WriteStream prototype');
            } catch (error) {
                logger.debug(`Could not patch fs.WriteStream: ${error}`);
            }
        }
    } catch (error) {
        logger.error(`Error patching file streams: ${error}`);
    }
}
