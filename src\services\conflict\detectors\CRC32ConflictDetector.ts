import * as fs from 'fs';
import { Logger } from 'winston';
import { ConflictInfo } from '../../../types/conflict/ConflictTypes.js';
import { ConflictSeverity, ConflictType } from '../../../types/conflict/ConflictTypes.js';
import { ResourceInfo } from '../../../types/resource/interfaces.js';
import { DatabaseService } from '../../database/DatabaseService.js';
import { ConflictDetectorBase } from './ConflictDetectorBase.js';

/**
 * CRC32 conflict detection options
 */
export interface CRC32ConflictDetectionOptions {
    /** Enable/disable the detector */
    enabled?: boolean;

    /** Buffer size for partial file reading (default: 2048 bytes) */
    bufferSize?: number;

    /** Maximum file size to process (default: 150MB) */
    maxFileSize?: number;

    /** Enable detailed logging */
    enableDetailedLogging?: boolean;

    /** Cache CRC32 values in database */
    enableCaching?: boolean;
}

/**
 * CRC32-based conflict detector
 * Uses CRC32 checksums of file endings for fast duplicate detection
 * Based on analysis of existing Sims 4 mod manager approaches
 */
export class CRC32ConflictDetector extends ConflictDetectorBase<CRC32ConflictDetectionOptions> {
    private crc32Cache: Map<string, number> = new Map();
    private cacheHits: number = 0;
    private cacheMisses: number = 0;

    constructor(
        databaseService: DatabaseService,
        options: CRC32ConflictDetectionOptions = {},
        logger?: Logger
    ) {
        super(databaseService, {
            enabled: options.enabled !== false,
            bufferSize: options.bufferSize || 2048,
            maxFileSize: options.maxFileSize || 150 * 1024 * 1024, // 150MB
            enableDetailedLogging: options.enableDetailedLogging || false,
            enableCaching: options.enableCaching !== false,
            ...options
        }, logger);

        this.logger.info('CRC32ConflictDetector initialized', {
            bufferSize: this.options.bufferSize,
            maxFileSize: this.options.maxFileSize,
            cachingEnabled: this.options.enableCaching
        });
    }

    /**
     * Detect conflicts between resources using CRC32 checksums
     * Enhanced with batch processing for better performance
     * @param resources Array of resources to check for conflicts
     * @returns Array of detected conflicts
     */
    public async detectConflicts(resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        if (!this.options.enabled) {
            return [];
        }

        const conflicts: ConflictInfo[] = [];
        const crcMap = new Map<number, ResourceInfo[]>();
        const batchSize = this.calculateOptimalBatchSize(resources.length);

        this.logger.info(`Starting CRC32 conflict detection for ${resources.length} resources (batch size: ${batchSize})`);

        // Process resources in batches for memory efficiency
        for (let i = 0; i < resources.length; i += batchSize) {
            const batch = resources.slice(i, i + batchSize);
            await this.processBatch(batch, crcMap);

            // Memory management: trigger cleanup if needed
            if (this.cacheMisses > 1000) {
                this.performMemoryCleanup();
            }
        }

        // Find groups with multiple resources (duplicates)
        for (const [crc, resourceGroup] of crcMap.entries()) {
            if (resourceGroup.length > 1) {
                const conflict = this.createDuplicateConflict(resourceGroup, crc);
                conflicts.push(conflict);
            }
        }

        this.logger.info(`CRC32 conflict detection completed: ${conflicts.length} conflicts found`);
        this.logger.info(`Cache statistics: ${this.cacheHits} hits, ${this.cacheMisses} misses`);

        return conflicts;
    }

    /**
     * Process a batch of resources for CRC32 calculation
     * @param batch Batch of resources to process
     * @param crcMap Map to store CRC32 to resources mapping
     */
    private async processBatch(batch: ResourceInfo[], crcMap: Map<number, ResourceInfo[]>): Promise<void> {
        const promises = batch.map(async (resource) => {
            try {
                const crc = await this.calculateCRC32(resource);
                if (crc !== null) {
                    if (!crcMap.has(crc)) {
                        crcMap.set(crc, []);
                    }
                    crcMap.get(crc)!.push(resource);
                }
            } catch (error: any) {
                if (this.options.enableDetailedLogging) {
                    this.logger.debug(`Error calculating CRC32 for resource ${resource.id}: ${error.message}`);
                }
            }
        });

        await Promise.all(promises);
    }

    /**
     * Calculate optimal batch size based on resource count and memory pressure
     * @param resourceCount Total number of resources
     * @returns Optimal batch size
     */
    private calculateOptimalBatchSize(resourceCount: number): number {
        // Base batch size
        let batchSize = 100;

        // Adjust based on resource count
        if (resourceCount > 10000) {
            batchSize = 50; // Smaller batches for large collections
        } else if (resourceCount > 1000) {
            batchSize = 75;
        }

        // Adjust based on cache performance
        const hitRate = this.cacheHits / (this.cacheHits + this.cacheMisses || 1);
        if (hitRate < 0.5) {
            batchSize = Math.max(25, batchSize * 0.75); // Reduce batch size if cache is ineffective
        }

        return Math.floor(batchSize);
    }

    /**
     * Perform memory cleanup to prevent memory pressure
     */
    private performMemoryCleanup(): void {
        // Clear cache if it gets too large
        if (this.crc32Cache.size > 5000) {
            const oldSize = this.crc32Cache.size;
            this.crc32Cache.clear();
            this.cacheHits = 0;
            this.cacheMisses = 0;
            this.logger.debug(`Cleared CRC32 cache (was ${oldSize} entries) to prevent memory pressure`);
        }
    }

    /**
     * Detect conflict between two specific resources
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns Conflict info or null if no conflict
     */
    public async detectConflict(resource1: ResourceInfo, resource2: ResourceInfo): Promise<ConflictInfo | null> {
        if (!this.options.enabled) {
            return null;
        }

        // Skip if resources are from the same package
        if (resource1.packagePath === resource2.packagePath) {
            return null;
        }

        try {
            const crc1 = await this.calculateCRC32(resource1);
            const crc2 = await this.calculateCRC32(resource2);

            if (crc1 !== null && crc2 !== null && crc1 === crc2) {
                return this.createDuplicateConflict([resource1, resource2], crc1);
            }
        } catch (error: any) {
            if (this.options.enableDetailedLogging) {
                this.logger.debug(`Error in CRC32 conflict detection: ${error.message}`);
            }
        }

        return null;
    }

    /**
     * Calculate CRC32 checksum for a resource file
     * Uses partial file reading (last 2048 bytes) for performance
     * @param resource Resource to calculate CRC32 for
     * @returns CRC32 checksum or null if error
     */
    private async calculateCRC32(resource: ResourceInfo): Promise<number | null> {
        const filePath = resource.packagePath;
        const cacheKey = `${filePath}:${resource.id}`;

        // Check cache first
        if (this.crc32Cache.has(cacheKey)) {
            this.cacheHits++;
            return this.crc32Cache.get(cacheKey)!;
        }

        this.cacheMisses++;

        try {
            // Check if file exists
            if (!fs.existsSync(filePath)) {
                return null;
            }

            // Check file size
            const stats = fs.statSync(filePath);
            if (stats.size > this.options.maxFileSize!) {
                this.logger.warn(`File too large for CRC32 calculation: ${filePath} (${stats.size} bytes)`);
                return null;
            }

            // Calculate CRC32 using partial file reading (like the analyzed tool)
            const crc = this.calculatePartialCRC32(filePath, stats.size);

            // Cache the result
            if (this.options.enableCaching) {
                this.crc32Cache.set(cacheKey, crc);
            }

            return crc;
        } catch (error: any) {
            this.logger.error(`Error calculating CRC32 for ${filePath}: ${error.message}`);
            return null;
        }
    }

    /**
     * Calculate CRC32 from the last portion of a file
     * This matches the approach used by the analyzed mod manager
     * @param filePath Path to the file
     * @param fileSize Size of the file
     * @returns CRC32 checksum
     */
    private calculatePartialCRC32(filePath: string, fileSize: number): number {
        const fileID = fs.openSync(filePath, 'r');
        let bufferSize = this.options.bufferSize!;

        // Adjust buffer size if file is smaller
        if (bufferSize > fileSize) {
            bufferSize = fileSize - 1;
        }

        const buffer = Buffer.alloc(bufferSize);
        const readPosition = fileSize - bufferSize - 1;

        fs.readSync(fileID, buffer, 0, bufferSize, readPosition);
        fs.closeSync(fileID);

        // Calculate CRC32 using a simple implementation
        return this.calculateCRC32FromBuffer(buffer);
    }

    /**
     * Calculate CRC32 checksum from a buffer
     * Simple CRC32 implementation for duplicate detection
     * @param buffer Buffer to calculate CRC32 for
     * @returns CRC32 checksum
     */
    private calculateCRC32FromBuffer(buffer: Buffer): number {
        const crcTable = this.generateCRC32Table();
        let crc = 0xFFFFFFFF;

        for (let i = 0; i < buffer.length; i++) {
            const byte = buffer[i];
            crc = (crc >>> 8) ^ crcTable[(crc ^ byte) & 0xFF];
        }

        return (crc ^ 0xFFFFFFFF) >>> 0; // Unsigned 32-bit
    }

    /**
     * Generate CRC32 lookup table
     * @returns CRC32 lookup table
     */
    private generateCRC32Table(): number[] {
        const table: number[] = [];
        const polynomial = 0xEDB88320;

        for (let i = 0; i < 256; i++) {
            let crc = i;
            for (let j = 0; j < 8; j++) {
                if (crc & 1) {
                    crc = (crc >>> 1) ^ polynomial;
                } else {
                    crc = crc >>> 1;
                }
            }
            table[i] = crc >>> 0; // Unsigned 32-bit
        }

        return table;
    }

    /**
     * Create a duplicate conflict from a group of resources with the same CRC32
     * @param resources Resources with identical CRC32
     * @param crc The CRC32 value
     * @returns Conflict info
     */
    private createDuplicateConflict(resources: ResourceInfo[], crc: number): ConflictInfo {
        const packagePaths = resources.map(r => r.packagePath);
        const uniquePaths = [...new Set(packagePaths)];

        return {
            id: `crc32-duplicate-${crc.toString(16)}`,
            type: ConflictType.RESOURCE,
            severity: ConflictSeverity.DUPLICATE,
            description: `Duplicate files detected (CRC32: ${crc.toString(16).toUpperCase()})`,
            affectedResources: resources.map(r => ({
                type: r.type,
                group: r.group,
                instance: r.instance
            })),
            packages: uniquePaths,
            timestamp: Date.now(),
            metadata: {
                crc32: crc.toString(16),
                duplicateCount: resources.length,
                detectionMethod: 'CRC32',
                bufferSize: this.options.bufferSize
            },
            recommendations: [
                'Keep only one copy of the duplicate file',
                'Check if files are from different mod versions',
                'Verify which version is more recent or preferred'
            ]
        };
    }

    /**
     * Get cache statistics
     * @returns Cache statistics
     */
    public getCacheStats(): { hits: number; misses: number; size: number; hitRate: number } {
        const total = this.cacheHits + this.cacheMisses;
        return {
            hits: this.cacheHits,
            misses: this.cacheMisses,
            size: this.crc32Cache.size,
            hitRate: total > 0 ? this.cacheHits / total : 0
        };
    }

    /**
     * Clear the CRC32 cache
     */
    public clearCache(): void {
        this.crc32Cache.clear();
        this.cacheHits = 0;
        this.cacheMisses = 0;
        this.logger.info('CRC32 cache cleared');
    }
}
