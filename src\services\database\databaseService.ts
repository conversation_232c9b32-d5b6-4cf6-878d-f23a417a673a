/**
 * Database Service Interface
 * 
 * This module defines the interface for database services.
 */

/**
 * Interface for database services
 */
export interface DatabaseService {
    /**
     * Initialize the database service
     */
    initialize(): Promise<void>;
    
    /**
     * Close the database service
     */
    close(): Promise<void>;
    
    /**
     * Insert data into the database
     * @param table Table name
     * @param data Data to insert
     */
    insert(table: string, data: any): Promise<number>;
    
    /**
     * Insert multiple records into the database
     * @param table Table name
     * @param records Records to insert
     */
    insertMany(table: string, records: any[]): Promise<number[]>;
    
    /**
     * Update data in the database
     * @param table Table name
     * @param id Record ID
     * @param data Data to update
     */
    update(table: string, id: number, data: any): Promise<void>;
    
    /**
     * Delete data from the database
     * @param table Table name
     * @param id Record ID
     */
    delete(table: string, id: number): Promise<void>;
    
    /**
     * Get data from the database
     * @param table Table name
     * @param id Record ID
     */
    get(table: string, id: number): Promise<any>;
    
    /**
     * Query data from the database
     * @param table Table name
     * @param query Query object
     */
    query(table: string, query: any): Promise<any[]>;
    
    /**
     * Execute a raw SQL query
     * @param sql SQL query
     * @param params Query parameters
     */
    execute(sql: string, params?: any[]): Promise<any>;
    
    /**
     * Begin a transaction
     */
    beginTransaction(): Promise<void>;
    
    /**
     * Commit a transaction
     */
    commitTransaction(): Promise<void>;
    
    /**
     * Rollback a transaction
     */
    rollbackTransaction(): Promise<void>;
}
