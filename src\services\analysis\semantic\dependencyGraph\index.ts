/**
 * Dependency Graph Module
 * 
 * This module provides classes for building, analyzing, and visualizing
 * dependency graphs between resources.
 */

// Export types
export * from './types.js';

// Export classes
export { DependencyGraphBuilder } from './dependencyGraphBuilder.js';
export { DependencyGraphAnalyzer } from './dependencyGraphAnalyzer.js';
export { DependencyGraphVisualizer } from './dependencyGraphVisualizer.js';

// Default export
export default {
  DependencyGraphBuilder,
  DependencyGraphAnalyzer,
  DependencyGraphVisualizer
};

// Import classes for the default export
import { DependencyGraphBuilder } from './dependencyGraphBuilder.js';
import { DependencyGraphAnalyzer } from './dependencyGraphAnalyzer.js';
import { DependencyGraphVisualizer } from './dependencyGraphVisualizer.js';
