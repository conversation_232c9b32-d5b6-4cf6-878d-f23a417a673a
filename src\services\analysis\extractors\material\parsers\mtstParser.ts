/**
 * Parser for MTST (Material Set) format
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DependencyInfo } from '../../../../databaseService.js';
import { MaterialHeaderInfo } from '../materialTypes.js';
import { handleMaterialExtractionError } from '../error/index.js';

const logger = new Logger('MtstParser');

/**
 * Parses a MTST (Material Set) resource buffer
 * @param buffer The resource buffer
 * @param resourceId The resource ID
 * @returns The parsed material header and dependencies
 */
export function parseMtst(buffer: Buffer, resourceId: number): { header: MaterialHeaderInfo, dependencies: DependencyInfo[] } {
    try {
        const dependencies: DependencyInfo[] = [];

        // Initialize header with default format
        const header: MaterialHeaderInfo = {
            format: 'MTST',
            version: 0,
            materialCount: 0,
            flags: 0
        };

        if (buffer.length >= 16) {
            header.version = buffer.readUInt32LE(4);
            header.materialCount = buffer.readUInt32LE(8);
            header.flags = buffer.readUInt32LE(12);

            // Extract material names if available
            header.materialNames = [];
            let offset = 16;

            for (let i = 0; i < header.materialCount && offset + 4 <= buffer.length; i++) {
                const nameLength = buffer.readUInt32LE(offset);
                offset += 4;

                if (offset + nameLength <= buffer.length) {
                    const materialName = buffer.slice(offset, offset + nameLength).toString('utf8');
                    header.materialNames.push(materialName);
                    offset += nameLength;

                    // Look for material TGI reference
                    if (offset + 16 <= buffer.length) {
                        const materialType = buffer.readUInt32LE(offset);
                        const materialGroup = buffer.readUInt32LE(offset + 4);
                        const materialInstance1 = buffer.readUInt32LE(offset + 8);
                        const materialInstance2 = buffer.readUInt32LE(offset + 12);

                        // Construct bigint instance from two 32-bit parts
                        const materialInstance = BigInt(materialInstance1) | (BigInt(materialInstance2) << 32n);

                        // Add as dependency
                        dependencies.push({
                            resourceId: resourceId,
                            targetType: materialType,
                            targetGroup: BigInt(materialGroup),
                            targetInstance: materialInstance,
                            referenceType: 'Material',
                            timestamp: Date.now()
                        });

                        offset += 16;
                    }
                }
            }
        }

        return { header, dependencies };
    } catch (error) {
        return handleMaterialExtractionError(
            error,
            {
                resourceId,
                operation: 'parseMtst',
                bufferLength: buffer?.length,
                materialFormat: 'MTST',
                additionalInfo: { format: 'MTST' }
            },
            {
                header: {
                    format: 'MTST_ERROR',
                    version: 0,
                    materialCount: 0,
                    flags: 0
                },
                dependencies: []
            }
        );
    }
}
