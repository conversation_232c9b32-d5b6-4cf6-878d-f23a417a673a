#!/usr/bin/env python3
"""
Launcher script for Sims 4 Mod Conflict Scanner.
This script starts both the TypeScript server and the Electron app.
"""

import subprocess
import sys
import time
import threading
import os

# Define color codes for terminal output
CYAN = "\033[36m"
GREEN = "\033[32m"
YELLOW = "\033[33m"
RED = "\033[31m"
RESET = "\033[0m"


def print_colored(message, color=RESET):
    """Print a colored message to the console."""
    print(f"{color}{message}{RESET}")


def start_typescript_server():
    """Start the TypeScript server using npm."""
    print_colored("Starting TypeScript server...", CYAN)

    # Use shell=True on Windows
    shell_option = True if sys.platform.startswith("win") else False

    try:
        process = subprocess.Popen(
            "npm run dev:ts",
            shell=shell_option,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        # Wait for server to start
        if process and process.stdout:
            for line in process.stdout:
                sys.stdout.write(line)
                if "Server running at http://localhost:3000" in line:
                    print_colored("TypeScript server is running!", GREEN)
                    break
        else:
            print_colored("Warning: process.stdout is None", YELLOW)

        return process
    except Exception as e:
        print_colored(f"Error starting TypeScript server: {e}", RED)
        return None


def start_streamlit_server():
    """Start the Streamlit server."""
    print_colored("Starting Streamlit interface...", CYAN)

    # Use shell=True on Windows
    shell_option = True if sys.platform.startswith("win") else False

    try:
        # Get the absolute path to the Streamlit server script
        base_path = os.path.dirname(os.path.abspath(__file__))
        streamlit_path = os.path.join(
            base_path, "src", "streamlit", "server.py"
        )

        # Verify the file exists
        if not os.path.exists(streamlit_path):
            error_msg = (
                f"Error: Streamlit server file not found at {streamlit_path}"
            )
            print_colored(error_msg, RED)
            return None

        print_colored(f"Found Streamlit server at: {streamlit_path}", CYAN)

        # Start Streamlit server with explicit Python path
        python_path = sys.executable
        cmd = (
            f'"{python_path}" -m streamlit run "{streamlit_path}" '
            '--server.port=7860'
        )
        print_colored(f"Running command: {cmd}", CYAN)

        process = subprocess.Popen(
            cmd,
            shell=shell_option,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        # Wait for server to start
        if process and process.stdout:
            for line in process.stdout:
                sys.stdout.write(line)
                if "You can now view your Streamlit app" in line:
                    print_colored("Streamlit interface is running!", GREEN)
                    break
        else:
            print_colored("Warning: process.stdout is None", YELLOW)

        # Check for errors in stderr
        if process and process.stderr:
            error_output = process.stderr.read()
            if error_output:
                print_colored(f"Streamlit server errors:\n{error_output}", RED)

        return process
    except Exception as e:
        print_colored(f"Error starting Streamlit server: {e}", RED)
        return None


def start_electron_app():
    """Start the Electron app."""
    print_colored("Starting Electron app...", CYAN)

    # Use shell=True on Windows
    shell_option = True if sys.platform.startswith("win") else False

    try:
        process = subprocess.Popen(
            "npm run dev:electron",
            shell=shell_option,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        # Wait for app to start
        if process and process.stdout:
            for line in process.stdout:
                sys.stdout.write(line)
                if "App is ready" in line:
                    print_colored("Electron app is running!", GREEN)
                    break
        else:
            print_colored("Warning: process.stdout is None", YELLOW)

        return process
    except Exception as e:
        print_colored(f"Error starting Electron app: {e}", RED)
        return None


def stream_output(process, prefix):
    """Stream the output of a subprocess with a prefix."""
    color = CYAN if prefix == "TS" else YELLOW

    if process and process.stdout:
        for line in process.stdout:
            print_colored(f"[{prefix}] {line.strip()}", color)
    else:
        print_colored(f"Warning: Cannot stream output for {prefix}", YELLOW)


def main():
    """Main function to start all servers."""
    print_colored("=" * 60, GREEN)
    print_colored("Sims 4 Mod Conflict Scanner Launcher", GREEN)
    print_colored("=" * 60, GREEN)

    # Start TypeScript server
    ts_process = start_typescript_server()
    if not ts_process:
        print_colored("Failed to start TypeScript server. Exiting.", RED)
        sys.exit(1)

    # Wait a bit to ensure TypeScript server is fully started
    time.sleep(3)

    # Start Streamlit server
    st_process = start_streamlit_server()
    if not st_process:
        print_colored("Failed to start Streamlit server. Shutting down.", RED)
        ts_process.terminate()
        sys.exit(1)

    # Wait a bit to ensure Streamlit server is fully started
    time.sleep(3)

    # Start Electron app
    electron_process = start_electron_app()
    if not electron_process:
        print_colored("Failed to start Electron app. Shutting down.", RED)
        ts_process.terminate()
        st_process.terminate()
        sys.exit(1)

    # Create threads to stream output from all processes
    ts_thread = threading.Thread(
        target=stream_output, args=(ts_process, "TS"), daemon=True
    )
    st_thread = threading.Thread(
        target=stream_output, args=(st_process, "ST"), daemon=True
    )
    electron_thread = threading.Thread(
        target=stream_output, args=(electron_process, "EL"), daemon=True
    )

    ts_thread.start()
    st_thread.start()
    electron_thread.start()

    print_colored("\nApplication is running!", GREEN)
    print_colored("TypeScript server: http://localhost:3000", CYAN)
    print_colored("Streamlit interface: http://localhost:7860", YELLOW)
    print_colored("\nPress Ctrl+C to exit", GREEN)

    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print_colored("\nShutting down servers...", YELLOW)
        ts_process.terminate()
        st_process.terminate()
        electron_process.terminate()
        print_colored("Servers stopped. Goodbye!", GREEN)


if __name__ == "__main__":
    main()
