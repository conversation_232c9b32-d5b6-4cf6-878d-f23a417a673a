﻿﻿﻿﻿﻿﻿// Corrected import
import { <PERSON><PERSON>ey, ResourceKeyMetadataPair } from './interfaces.js';
import { ResourceInfo } from '../database.js';
import { PackageMetadata } from './Package.js'; // Assuming PackageMetadata is in Package.ts
export type { ConflictInfo, ConflictType, ConflictSeverity, ConflictResult, ConflictDetectionResult } from '../conflict/index.js';

/**
 * Information about a resource package (used for context in analysis).
 * This seems specific enough to keep here, but ensure it aligns with how packages are represented elsewhere (e.g., PackageMetadata).
 */
export interface ResourcePackageInfo {
  name: string;
  version?: string; // Made optional as it might not always be available
  size?: number; // Made optional
  hash?: string; // Made optional
  timestamp?: number; // Made optional
  author?: string;
  description?: string;
  filePath: string; // Keep filePath as it's crucial context
  resources: ResourceInfo[]; // Contains the ResourceInfo (key + metadata) within this package
}

// Removed duplicate/conflicting definitions:
// - ResourceConflictType enum
// - ResourceConflictSeverity enum
// - ResourceConflict interface (use ConflictInfo)
// - ResourceConflictAnalysis interface (use ConflictDetectionResult or define a more specific analysis result type if needed)
// - ConflictResolutionStrategy interface (define centrally if needed)
