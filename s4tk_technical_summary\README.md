# S4TK Technical Documentation Summary

This document summarizes the capabilities and API details of the Sims 4 Toolkit (S4TK) library, based *exclusively* on the contents of the `docs/technical` directory provided in the project.

## Overview

Sims 4 Toolkit (S4TK) is a collection of Node.js packages designed for reading, writing, and manipulating Sims 4 package files and resources. It aims to provide foundational tools for modding The Sims 4.

*(Note: The documentation indicates that S4TK software is considered pre-release and subject to breaking changes.)*

## Packages Summary

Below are links to detailed summaries for each S4TK package found within the `docs/technical` directory:

*   [Compression (`@s4tk/compression`)](./compression.md)
*   [Encoding (`@s4tk/encoding`)](./encoding.md)
*   [Hashing (`@s4tk/hashing`)](./hashing.md)
*   [Images (`@s4tk/images`)](./images.md)
*   [Models (`@s4tk/models`)](./models.md)
*   [Plugin: BufferFromFile (`@s4tk/plugin-bufferfromfile`)](./plugin-bufferfromfile.md)
*   [XML DOM (`@s4tk/xml-dom`)](./xml-dom.md)

*(This summary will be populated by analyzing the contents of each package's documentation.)*