/**
 * Interface for file watcher options
 */
export interface FileWatcherOptions {
  /**
   * The paths to watch
   */
  paths: string[];

  /**
   * Whether to watch subdirectories
   */
  recursive?: boolean;

  /**
   * File patterns to ignore
   */
  ignored?: string[];

  /**
   * Whether to ignore initial scan
   */
  ignoreInitial?: boolean;

  /**
   * Whether to follow symbolic links
   */
  followSymlinks?: boolean;

  /**
   * The polling interval in milliseconds
   */
  interval?: number;

  /**
   * Whether to use polling instead of native events
   */
  usePolling?: boolean;

  /**
   * The debounce delay in milliseconds
   */
  debounceDelay?: number;

  /**
   * Additional options specific to the file watcher implementation
   */
  options?: Record<string, unknown>;
} 
