/**
 * Dependency Graph Builder
 *
 * This class builds and manages a graph of dependencies between resources.
 * It provides methods for traversing the graph, querying dependencies,
 * and analyzing dependency chains.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { ResourceKey, ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { DependencyInfo } from '../../../../types/database.js';
import { formatTgi } from '../../../../utils/resource/formatters.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { LRUCache } from '../../../../utils/cache/cacheUtils.js';
import { EnhancedMemoryManager } from '../../../../utils/memory/enhancedMemoryManager.js';
import {
  DependencyMetadata,
  DependencyNode,
  DependencyGraphOptions
} from './types.js';

/**
 * DependencyGraphBuilder class
 *
 * This class builds and manages a graph of dependencies between resources.
 * It provides methods for traversing the graph, querying dependencies,
 * and analyzing dependency chains.
 */
@injectable()
@singleton()
export class DependencyGraphBuilder {
  // The dependency graph is a map where:
  // - Key: Resource ID (as string)
  // - Value: Set of resource IDs that this resource depends on
  private dependencyGraph: Map<string, Set<string>> = new Map();

  // The reverse dependency graph is a map where:
  // - Key: Resource ID (as string)
  // - Value: Set of resource IDs that depend on this resource
  private reverseDependencyGraph: Map<string, Set<string>> = new Map();

  // A map of resource metadata for quick lookup
  // - Key: Resource ID (as string)
  // - Value: Resource metadata (type, group, instance, etc.)
  private resourceMetadata: Map<string, ResourceMetadata> = new Map();

  // A map of dependency metadata for quick lookup
  // - Key: Source ID + Target ID (as string)
  // - Value: Dependency metadata (reference type, strength, etc.)
  private dependencyMetadata: Map<string, DependencyMetadata> = new Map();

  // Cache for dependency chains to avoid recalculating
  private dependencyChainCache: LRUCache<string, string[]>;

  // Cache for dependency trees to avoid recalculating
  private dependencyTreeCache: LRUCache<string, DependencyNode>;

  // Flag to indicate if the graph has been built
  private graphBuilt: boolean = false;

  // Memory manager for optimizing memory usage
  private memoryManager: EnhancedMemoryManager;

  // Logger instance
  private logger: Logger;

  /**
   * Constructor
   * @param databaseService Database service
   * @param memoryManager Memory manager
   */
  constructor(
    private databaseService: DatabaseService,
    memoryManager?: EnhancedMemoryManager
  ) {
    this.logger = new Logger('DependencyGraphBuilder');
    this.memoryManager = memoryManager || EnhancedMemoryManager.getInstance();

    // Initialize caches with reasonable sizes
    this.dependencyChainCache = new LRUCache<string, string[]>(1000);
    this.dependencyTreeCache = new LRUCache<string, DependencyNode>(500);

    this.logger.info('DependencyGraphBuilder initialized');
  }

  /**
   * Build the dependency graph for all resources in the database
   * @param options Options for building the graph
   */
  public async buildGraph(options: DependencyGraphOptions = {}): Promise<void> {
    this.logger.info('Building dependency graph for all resources...');

    // Clear existing graph
    this.clear();

    try {
      // Get all resources from the database
      const resources = await this.databaseService.resources.getAllResources();

      this.logger.info(`Found ${resources.length} resources in the database`);

      // Store resource metadata if requested
      if (options.includeResourceMetadata) {
        for (const resource of resources) {
          const resourceId = resource.id.toString();
          this.resourceMetadata.set(resourceId, {
            type: resource.type,
            group: BigInt(resource.group),
            instance: BigInt(resource.instance),
            name: resource.resourceType || 'Unknown',
            // Add other metadata as needed
          });
        }
      }

      // Get all dependencies from the database
      const dependencies = this.databaseService.dependencies.getAllDependencies();

      this.logger.info(`Found ${dependencies.length} dependencies in the database`);

      // Process dependencies in batches to avoid memory pressure
      const batchSize = options.batchSize || 1000;
      const batches = this.chunkArray(dependencies, batchSize);

      this.logger.info(`Processing dependencies in ${batches.length} batches of ${batchSize}`);

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        this.logger.debug(`Processing batch ${i + 1}/${batches.length} (${batch.length} dependencies)`);

        // Add dependencies to the graph
        await this.addDependencies(batch, options);

        // Force garbage collection if available
        this.memoryManager.forceGarbageCollection();
      }

      this.graphBuilt = true;
      this.logger.info('Dependency graph built successfully');
    } catch (error) {
      this.logger.error('Error building dependency graph:', error);
      throw error;
    }
  }

  /**
   * Build the dependency graph for a specific package
   * @param packageId Package ID
   * @param options Options for building the graph
   */
  public async buildGraphForPackage(packageId: number, options: DependencyGraphOptions = {}): Promise<void> {
    this.logger.info(`Building dependency graph for package ${packageId}...`);

    // Clear existing graph
    this.clear();

    try {
      // Get all resources for the package from the database
      const resources = this.databaseService.resources.getResourcesByPackageId(packageId);

      this.logger.info(`Found ${resources.length} resources in package ${packageId}`);

      // Store resource metadata if requested
      if (options.includeResourceMetadata) {
        for (const resource of resources) {
          const resourceId = resource.id.toString();
          this.resourceMetadata.set(resourceId, {
            type: resource.type,
            group: BigInt(resource.group),
            instance: BigInt(resource.instance),
            name: resource.resourceType || 'Unknown',
            // Add other metadata as needed
          });
        }
      }

      // Get all dependencies for the package from the database
      const dependencies = this.databaseService.dependencies.getDependenciesByPackageId(packageId);

      this.logger.info(`Found ${dependencies.length} dependencies in package ${packageId}`);

      // Process dependencies in batches to avoid memory pressure
      const batchSize = options.batchSize || 1000;
      const batches = this.chunkArray(dependencies, batchSize);

      this.logger.info(`Processing dependencies in ${batches.length} batches of ${batchSize}`);

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        this.logger.debug(`Processing batch ${i + 1}/${batches.length} (${batch.length} dependencies)`);

        // Add dependencies to the graph
        await this.addDependencies(batch, options);

        // Force garbage collection if available
        this.memoryManager.forceGarbageCollection();
      }

      this.graphBuilt = true;
      this.logger.info(`Dependency graph for package ${packageId} built successfully`);
    } catch (error) {
      this.logger.error(`Error building dependency graph for package ${packageId}:`, error);
      throw error;
    }
  }

  /**
   * Build the dependency graph for a specific resource
   * @param resourceId Resource ID
   * @param options Options for building the graph
   */
  public async buildGraphForResource(resourceId: number, options: DependencyGraphOptions = {}): Promise<void> {
    this.logger.info(`Building dependency graph for resource ${resourceId}...`);

    // Clear existing graph
    this.clear();

    try {
      // Get the resource from the database
      const resource = this.databaseService.resources.getResourceById(resourceId);

      if (!resource) {
        throw new Error(`Resource ${resourceId} not found in the database`);
      }

      // Store resource metadata if requested
      if (options.includeResourceMetadata) {
        const resourceIdStr = resourceId.toString();
        this.resourceMetadata.set(resourceIdStr, {
          type: resource.type,
          group: BigInt(resource.group),
          instance: BigInt(resource.instance),
          name: resource.resourceType || 'Unknown',
          // Add other metadata as needed
        });
      }

      // Get dependencies for the resource from the database
      const dependencies = this.databaseService.dependencies.getDependencies(resourceId);

      this.logger.info(`Found ${dependencies.length} dependencies for resource ${resourceId}`);

      // Add dependencies to the graph
      await this.addDependencies(dependencies, options);

      // If maxDepth is specified, recursively add dependencies up to the specified depth
      if (options.maxDepth && options.maxDepth > 1) {
        await this.addDependenciesRecursively(resourceId.toString(), 1, options.maxDepth, options);
      }

      this.graphBuilt = true;
      this.logger.info(`Dependency graph for resource ${resourceId} built successfully`);
    } catch (error) {
      this.logger.error(`Error building dependency graph for resource ${resourceId}:`, error);
      throw error;
    }
  }

  /**
   * Add dependencies to the graph
   * @param dependencies Dependencies to add
   * @param options Options for adding dependencies
   */
  public async addDependencies(dependencies: DependencyInfo[], options: DependencyGraphOptions = {}): Promise<void> {
    for (const dependency of dependencies) {
      // Skip weak dependencies if includeWeakDependencies is false
      if (!options.includeWeakDependencies && this.isWeakDependency(dependency)) {
        continue;
      }

      const sourceId = dependency.sourceResourceId?.toString() || dependency.resourceId.toString();
      const targetKey: ResourceKey = {
        type: dependency.targetType,
        group: BigInt(dependency.targetGroup),
        instance: BigInt(dependency.targetInstance)
      };

      // Try to find the target resource in the database
      try {
        const targetResource = this.findTargetResource(targetKey);

        if (targetResource) {
          const targetId = targetResource.id.toString();

          // Add to dependency graph
          if (!this.dependencyGraph.has(sourceId)) {
            this.dependencyGraph.set(sourceId, new Set());
          }
          this.dependencyGraph.get(sourceId)!.add(targetId);

          // Add to reverse dependency graph
          if (!this.reverseDependencyGraph.has(targetId)) {
            this.reverseDependencyGraph.set(targetId, new Set());
          }
          this.reverseDependencyGraph.get(targetId)!.add(sourceId);

          // Store dependency metadata
          const metadataKey = `${sourceId}->${targetId}`;
          this.dependencyMetadata.set(metadataKey, {
            referenceType: dependency.referenceType || 'Unknown',
            strength: this.calculateDependencyStrength(dependency),
            timestamp: dependency.timestamp || Date.now()
          });
        }
      } catch (error) {
        this.logger.error(`Error finding target resource for dependency:`, error);
      }
    }
  }

  /**
   * Get dependencies for a resource
   * @param resourceId Resource ID
   * @returns Set of resource IDs that this resource depends on
   */
  public getDependencies(resourceId: string): Set<string> {
    if (!this.graphBuilt) {
      this.logger.warn('Dependency graph has not been built yet');
      return new Set();
    }

    return this.dependencyGraph.get(resourceId) || new Set();
  }

  /**
   * Get dependents for a resource
   * @param resourceId Resource ID
   * @returns Set of resource IDs that depend on this resource
   */
  public getDependents(resourceId: string): Set<string> {
    if (!this.graphBuilt) {
      this.logger.warn('Dependency graph has not been built yet');
      return new Set();
    }

    return this.reverseDependencyGraph.get(resourceId) || new Set();
  }

  /**
   * Get resource metadata
   * @param resourceId Resource ID
   * @returns Resource metadata or undefined if not found
   */
  public getResourceMetadata(resourceId: string): ResourceMetadata | undefined {
    return this.resourceMetadata.get(resourceId);
  }

  /**
   * Get dependency metadata
   * @param sourceId Source resource ID
   * @param targetId Target resource ID
   * @returns Dependency metadata or undefined if not found
   */
  public getDependencyMetadata(sourceId: string, targetId: string): DependencyMetadata | undefined {
    const metadataKey = `${sourceId}->${targetId}`;
    return this.dependencyMetadata.get(metadataKey);
  }

  /**
   * Check if the graph has been built
   * @returns True if the graph has been built, false otherwise
   */
  public isGraphBuilt(): boolean {
    return this.graphBuilt;
  }

  /**
   * Get all resource IDs in the graph
   * @returns Array of resource IDs
   */
  public getResourceIds(): string[] {
    return [...new Set([...this.dependencyGraph.keys(), ...this.reverseDependencyGraph.keys()])];
  }

  /**
   * Get the number of resources in the graph
   * @returns Number of resources
   */
  public getResourceCount(): number {
    return this.getResourceIds().length;
  }

  /**
   * Clear the dependency graph
   */
  public clear(): void {
    this.dependencyGraph.clear();
    this.reverseDependencyGraph.clear();
    this.resourceMetadata.clear();
    this.dependencyMetadata.clear();
    this.dependencyChainCache.clear();
    this.dependencyTreeCache.clear();
    this.graphBuilt = false;
  }

  /**
   * Check if a dependency is weak
   * @param dependency Dependency to check
   * @returns True if the dependency is weak, false otherwise
   */
  private isWeakDependency(dependency: DependencyInfo): boolean {
    // Implement logic to determine if a dependency is weak
    // For example, dependencies with certain reference types might be considered weak
    const weakReferenceTypes = ['Optional', 'Fallback', 'Alternative'];

    return weakReferenceTypes.includes(dependency.referenceType || '');
  }

  /**
   * Calculate the strength of a dependency
   * @param dependency Dependency to calculate strength for
   * @returns Strength value (0-100)
   */
  private calculateDependencyStrength(dependency: DependencyInfo): number {
    // Implement logic to calculate dependency strength
    // For example, based on reference type, resource types, etc.
    const referenceType = dependency.referenceType || 'Unknown';

    // Define strength values for different reference types
    const strengthMap: Record<string, number> = {
      'Required': 100,
      'Primary': 90,
      'Secondary': 70,
      'Optional': 50,
      'Fallback': 30,
      'Alternative': 20,
      'Unknown': 60
    };

    return strengthMap[referenceType] || 60;
  }

  /**
   * Find a target resource in the database
   * @param key Resource key
   * @returns Target resource or null if not found
   */
  private findTargetResource(key: ResourceKey): any {
    try {
      // Try to find the resource by TGI
      const resources = this.databaseService.resources.getResourcesByTGI(
        key.type,
        key.group.toString(),
        key.instance.toString()
      );

      if (resources && resources.length > 0) {
        return resources[0];
      }

      return null;
    } catch (error) {
      this.logger.error(`Error finding target resource for key ${formatTgi(key)}:`, error);
      return null;
    }
  }

  /**
   * Split an array into chunks of a specified size
   * @param array Array to split
   * @param chunkSize Size of each chunk
   * @returns Array of chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Add dependencies recursively up to a specified depth
   * @param resourceId Resource ID
   * @param currentDepth Current depth
   * @param maxDepth Maximum depth
   * @param options Options for adding dependencies
   */
  private async addDependenciesRecursively(
    resourceId: string,
    currentDepth: number,
    maxDepth: number,
    options: DependencyGraphOptions
  ): Promise<void> {
    if (currentDepth >= maxDepth) {
      return;
    }

    const dependencies = this.getDependencies(resourceId);

    if (!dependencies || dependencies.size === 0) {
      return;
    }

    for (const dependencyId of dependencies) {
      // Get dependencies for this resource
      const subDependencies = this.databaseService.dependencies.getDependencies(parseInt(dependencyId));

      // Add dependencies to the graph
      await this.addDependencies(subDependencies, options);

      // Recursively add dependencies
      await this.addDependenciesRecursively(dependencyId, currentDepth + 1, maxDepth, options);
    }
  }

  /**
   * Get dependency chain from cache
   * @param resourceId Resource ID
   * @returns Dependency chain or undefined if not in cache
   */
  protected getDependencyChainFromCache(resourceId: string): string[] | undefined {
    return this.dependencyChainCache.get(resourceId);
  }

  /**
   * Cache dependency chain
   * @param resourceId Resource ID
   * @param chain Dependency chain
   */
  protected cacheDependencyChain(resourceId: string, chain: string[]): void {
    this.dependencyChainCache.set(resourceId, chain);
  }

  /**
   * Get dependency tree from cache
   * @param resourceId Resource ID
   * @returns Dependency tree or undefined if not in cache
   */
  protected getDependencyTreeFromCache(resourceId: string): DependencyNode | undefined {
    return this.dependencyTreeCache.get(resourceId);
  }

  /**
   * Cache dependency tree
   * @param resourceId Resource ID
   * @param tree Dependency tree
   */
  protected cacheDependencyTree(resourceId: string, tree: DependencyNode): void {
    this.dependencyTreeCache.set(resourceId, tree);
  }

  /**
   * Get dependency graph entries
   * @returns Entries of the dependency graph
   */
  protected getDependencyGraphEntries(): IterableIterator<[string, Set<string>]> {
    return this.dependencyGraph.entries();
  }

  /**
   * Get reverse dependency graph entries
   * @returns Entries of the reverse dependency graph
   */
  protected getReverseDependencyGraphEntries(): IterableIterator<[string, Set<string>]> {
    return this.reverseDependencyGraph.entries();
  }

  /**
   * Get resource metadata entries
   * @returns Entries of the resource metadata map
   */
  protected getResourceMetadataEntries(): IterableIterator<[string, ResourceMetadata]> {
    return this.resourceMetadata.entries();
  }

  /**
   * Get dependency metadata entries
   * @returns Entries of the dependency metadata map
   */
  protected getDependencyMetadataEntries(): IterableIterator<[string, DependencyMetadata]> {
    return this.dependencyMetadata.entries();
  }

  /**
   * Set dependency graph
   * @param graph Dependency graph
   */
  protected setDependencyGraph(graph: Map<string, Set<string>>): void {
    this.dependencyGraph = graph;
  }

  /**
   * Set reverse dependency graph
   * @param graph Reverse dependency graph
   */
  protected setReverseDependencyGraph(graph: Map<string, Set<string>>): void {
    this.reverseDependencyGraph = graph;
  }

  /**
   * Set resource metadata
   * @param metadata Resource metadata
   */
  protected setResourceMetadata(metadata: Map<string, ResourceMetadata>): void {
    this.resourceMetadata = metadata;
  }

  /**
   * Set dependency metadata
   * @param metadata Dependency metadata
   */
  protected setDependencyMetadata(metadata: Map<string, DependencyMetadata>): void {
    this.dependencyMetadata = metadata;
  }

  /**
   * Set graph built flag
   * @param built Graph built flag
   */
  protected setGraphBuilt(built: boolean): void {
    this.graphBuilt = built;
  }
}
