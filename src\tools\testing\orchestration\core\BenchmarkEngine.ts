/**
 * Benchmark Engine
 * 
 * Provides performance benchmarking capabilities for workflow orchestration,
 * measuring execution time, memory usage, and system performance metrics.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { EnhancedMemoryManager } from '../../../../utils/memory/enhancedMemoryManager.js';
import { ScenarioDefinition, BenchmarkResults, BenchmarkCriteria } from './interfaces.js';

/**
 * Performance benchmarking engine for workflow testing
 */
export class BenchmarkEngine {
    private logger: Logger;
    private memoryManager: EnhancedMemoryManager;
    private baselineMetrics: Map<string, any>;

    constructor() {
        this.logger = new Logger('BenchmarkEngine');
        this.memoryManager = EnhancedMemoryManager.getInstance();
        this.baselineMetrics = new Map();
    }

    /**
     * Initialize the benchmark engine
     */
    async initialize(): Promise<void> {
        this.logger.info('Initializing Benchmark Engine...');
        
        // Capture baseline system metrics
        await this.captureBaseline();
        
        this.logger.info('Benchmark Engine initialized successfully');
    }

    /**
     * Run benchmarks for a completed workflow
     */
    async runBenchmarks(
        scenario: ScenarioDefinition,
        actionResults: any[]
    ): Promise<BenchmarkResults> {
        this.logger.info(`Running benchmarks for scenario: ${scenario.metadata.name}`);

        const benchmarks: BenchmarkResults = {
            performance: {
                executionTime: 0,
                memoryPeak: 0,
                cpuAverage: 0,
                ioThroughput: 0
            },
            scalability: {
                testedCounts: [],
                scalingFactor: 1.0,
                degradationPoints: []
            },
            reliability: {
                successRate: 0,
                errorTypes: {},
                recoverySuccess: false
            },
            comparison: {
                baseline: 'initial_run',
                improvement: 0,
                regression: 0
            }
        };

        try {
            // Calculate performance metrics
            benchmarks.performance = await this.calculatePerformanceMetrics(actionResults);

            // Calculate scalability metrics
            benchmarks.scalability = await this.calculateScalabilityMetrics(actionResults);

            // Calculate reliability metrics
            benchmarks.reliability = await this.calculateReliabilityMetrics(actionResults);

            // Compare against baseline
            benchmarks.comparison = await this.compareAgainstBaseline(scenario.metadata.name, benchmarks.performance);

            this.logger.info('Benchmarks completed successfully');

        } catch (error: any) {
            this.logger.error(`Benchmark execution failed: ${error.message}`);
        }

        return benchmarks;
    }

    /**
     * Capture baseline system metrics
     */
    private async captureBaseline(): Promise<void> {
        const baseline = {
            timestamp: Date.now(),
            memory: process.memoryUsage(),
            cpu: process.cpuUsage(),
            system: {
                platform: process.platform,
                arch: process.arch,
                nodeVersion: process.version
            }
        };

        this.baselineMetrics.set('system_baseline', baseline);
        this.logger.debug('System baseline captured');
    }

    /**
     * Calculate performance metrics from action results
     */
    private async calculatePerformanceMetrics(actionResults: any[]): Promise<any> {
        if (actionResults.length === 0) {
            return {
                executionTime: 0,
                memoryPeak: 0,
                cpuAverage: 0,
                ioThroughput: 0
            };
        }

        // Calculate total execution time
        const totalExecutionTime = actionResults.reduce((sum, result) => {
            return sum + (result.duration || 0);
        }, 0);

        // Calculate peak memory usage
        const memoryPeak = actionResults.reduce((peak, result) => {
            const memUsed = result.metrics?.memoryUsed || 0;
            return Math.max(peak, memUsed);
        }, 0);

        // Calculate average CPU time
        const totalCpuTime = actionResults.reduce((sum, result) => {
            return sum + (result.metrics?.cpuTime || 0);
        }, 0);
        const cpuAverage = actionResults.length > 0 ? totalCpuTime / actionResults.length : 0;

        // Calculate IO throughput (simplified)
        const totalIoOps = actionResults.reduce((sum, result) => {
            return sum + (result.metrics?.ioOperations || 0);
        }, 0);
        const ioThroughput = totalExecutionTime > 0 ? totalIoOps / (totalExecutionTime / 1000) : 0;

        return {
            executionTime: totalExecutionTime,
            memoryPeak,
            cpuAverage,
            ioThroughput
        };
    }

    /**
     * Calculate scalability metrics
     */
    private async calculateScalabilityMetrics(actionResults: any[]): Promise<any> {
        // For now, return basic scalability metrics
        // This would be enhanced with actual scaling tests
        return {
            testedCounts: [actionResults.length],
            scalingFactor: 1.0,
            degradationPoints: []
        };
    }

    /**
     * Calculate reliability metrics
     */
    private async calculateReliabilityMetrics(actionResults: any[]): Promise<any> {
        if (actionResults.length === 0) {
            return {
                successRate: 0,
                errorTypes: {},
                recoverySuccess: false
            };
        }

        // Calculate success rate
        const successfulActions = actionResults.filter(result => result.success).length;
        const successRate = (successfulActions / actionResults.length) * 100;

        // Categorize error types
        const errorTypes: Record<string, number> = {};
        actionResults.forEach(result => {
            if (!result.success && result.errors) {
                result.errors.forEach((error: string) => {
                    const errorType = this.categorizeError(error);
                    errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
                });
            }
        });

        // Check if any recovery was successful
        const recoverySuccess = actionResults.some(result => 
            result.warnings && result.warnings.some((warning: string) => 
                warning.includes('retry') || warning.includes('recovery')
            )
        );

        return {
            successRate,
            errorTypes,
            recoverySuccess
        };
    }

    /**
     * Compare performance against baseline
     */
    private async compareAgainstBaseline(scenarioName: string, currentPerformance: any): Promise<any> {
        const baselineKey = `${scenarioName}_baseline`;
        const baseline = this.baselineMetrics.get(baselineKey);

        if (!baseline) {
            // Store current performance as baseline
            this.baselineMetrics.set(baselineKey, currentPerformance);
            return {
                baseline: 'initial_run',
                improvement: 0,
                regression: 0
            };
        }

        // Calculate improvement/regression percentages
        const executionTimeChange = baseline.executionTime > 0 
            ? ((baseline.executionTime - currentPerformance.executionTime) / baseline.executionTime) * 100
            : 0;

        const memoryChange = baseline.memoryPeak > 0
            ? ((baseline.memoryPeak - currentPerformance.memoryPeak) / baseline.memoryPeak) * 100
            : 0;

        const improvement = Math.max(0, (executionTimeChange + memoryChange) / 2);
        const regression = Math.max(0, -(executionTimeChange + memoryChange) / 2);

        return {
            baseline: baselineKey,
            improvement,
            regression
        };
    }

    /**
     * Categorize error types for analysis
     */
    private categorizeError(error: string): string {
        const errorLower = error.toLowerCase();

        if (errorLower.includes('memory') || errorLower.includes('heap')) {
            return 'memory_error';
        }
        if (errorLower.includes('timeout') || errorLower.includes('time')) {
            return 'timeout_error';
        }
        if (errorLower.includes('file') || errorLower.includes('path')) {
            return 'file_error';
        }
        if (errorLower.includes('network') || errorLower.includes('connection')) {
            return 'network_error';
        }
        if (errorLower.includes('validation') || errorLower.includes('invalid')) {
            return 'validation_error';
        }
        if (errorLower.includes('permission') || errorLower.includes('access')) {
            return 'permission_error';
        }

        return 'unknown_error';
    }

    /**
     * Generate performance report
     */
    async generatePerformanceReport(benchmarks: BenchmarkResults): Promise<string> {
        const report = [
            '=== PERFORMANCE BENCHMARK REPORT ===',
            '',
            `Execution Time: ${benchmarks.performance.executionTime}ms`,
            `Memory Peak: ${this.formatBytes(benchmarks.performance.memoryPeak)}`,
            `CPU Average: ${benchmarks.performance.cpuAverage.toFixed(2)}μs`,
            `IO Throughput: ${benchmarks.performance.ioThroughput.toFixed(2)} ops/sec`,
            '',
            `Success Rate: ${benchmarks.reliability.successRate.toFixed(1)}%`,
            `Recovery Success: ${benchmarks.reliability.recoverySuccess ? 'Yes' : 'No'}`,
            '',
            `Performance vs Baseline:`,
            `  Improvement: ${benchmarks.comparison.improvement.toFixed(1)}%`,
            `  Regression: ${benchmarks.comparison.regression.toFixed(1)}%`,
            '',
            '=== END REPORT ==='
        ];

        return report.join('\n');
    }

    /**
     * Format bytes for human-readable output
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    }
}
