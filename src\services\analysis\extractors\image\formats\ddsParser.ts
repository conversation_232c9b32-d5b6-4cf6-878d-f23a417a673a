/**
 * DDS image format parser
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DDSMetadata, ImageFormat } from '../types.js';
import { <PERSON><PERSON>erReader } from '../utils/bufferReader.js';
import { createImageErrorContext, handleImageError } from '../error/imageExtractorErrorHandler.js';

// Create a logger instance
const log = new Logger('DDSParser');

/**
 * DDS header flags
 */
export enum DDSFlags {
    DDSD_CAPS = 0x1,
    DDSD_HEIGHT = 0x2,
    DDSD_WIDTH = 0x4,
    DDSD_PITCH = 0x8,
    DDSD_PIXELFORMAT = 0x1000,
    DDSD_MIPMAPCOUNT = 0x20000,
    DDSD_LINEARSIZE = 0x80000,
    DDSD_DEPTH = 0x800000
}

/**
 * DDS pixel format flags
 */
export enum DDSPixelFormatFlags {
    DDPF_ALPHAPIXELS = 0x1,
    DDPF_ALPHA = 0x2,
    DDPF_FOURCC = 0x4,
    DDPF_RGB = 0x40,
    DDPF_YUV = 0x200,
    DDPF_LUMINANCE = 0x20000
}

/**
 * Parses a DDS image buffer
 * @param buffer DDS image buffer
 * @param resourceId Resource ID for error context
 * @param instanceId Instance ID for error context
 * @returns Parsed DDS metadata
 */
export function parseDDS(buffer: Buffer, resourceId: number, instanceId: string): DDSMetadata {
    try {
        // Create a buffer reader
        const reader = new BufferReader(buffer);
        
        // Validate DDS signature
        const signature = reader.readUInt32LE('DDS Signature');
        if (signature !== 0x20534444) { // "DDS "
            throw new Error('Invalid DDS signature');
        }
        
        // DDS header is 124 bytes after the signature
        const headerSize = reader.readUInt32LE('Header Size');
        if (headerSize !== 124) {
            log.warn(`Unusual DDS header size: ${headerSize} (expected 124)`);
        }
        
        // Read header flags
        const flags = reader.readUInt32LE('Flags');
        
        // Read dimensions
        const height = reader.readUInt32LE('Height');
        const width = reader.readUInt32LE('Width');
        
        // Read pitch or linear size (depending on flags)
        const pitchOrLinearSize = reader.readUInt32LE('Pitch/LinearSize');
        
        // Read depth (usually 1 for 2D textures)
        const depth = reader.readUInt32LE('Depth');
        
        // Read mipmap count
        let mipMapCount = 1; // Default to 1 if not specified
        if (flags & DDSFlags.DDSD_MIPMAPCOUNT) {
            const readMipMapCount = reader.readUInt32LE('MipMapCount');
            if (readMipMapCount !== undefined) {
                mipMapCount = readMipMapCount;
            }
        } else {
            // Skip 4 bytes
            reader.skip(4);
        }
        
        // Skip reserved bytes (11 DWORDs)
        reader.skip(44);
        
        // Read pixel format
        const pfSize = reader.readUInt32LE('PixelFormat Size');
        if (pfSize !== 32) {
            log.warn(`Unusual pixel format size: ${pfSize} (expected 32)`);
        }
        
        const pfFlags = reader.readUInt32LE('PixelFormat Flags');
        const hasAlpha = (pfFlags & DDSPixelFormatFlags.DDPF_ALPHAPIXELS) !== 0;
        
        // Read FourCC
        const fourCC = reader.readString(4, 'ascii', 'FourCC');
        
        // Skip RGB bit counts and masks (4 DWORDs)
        reader.skip(16);
        
        // Read caps
        const caps = reader.readUInt32LE('Caps');
        const caps2 = reader.readUInt32LE('Caps2');
        
        // Skip reserved caps (2 DWORDs)
        reader.skip(8);
        
        // Skip reserved (1 DWORD)
        reader.skip(4);
        
        // Determine compression format and bits per pixel
        let compression = 'Uncompressed';
        let bitsPerPixel = 32; // Default to 32 bits per pixel
        
        if (pfFlags & DDSPixelFormatFlags.DDPF_FOURCC) {
            if (fourCC === 'DXT1') {
                compression = 'DXT1';
                bitsPerPixel = 4; // 4 bits per pixel
            } else if (fourCC === 'DXT3') {
                compression = 'DXT3';
                bitsPerPixel = 8; // 8 bits per pixel
            } else if (fourCC === 'DXT5') {
                compression = 'DXT5';
                bitsPerPixel = 8; // 8 bits per pixel
            } else {
                compression = fourCC || 'Unknown';
                log.warn(`Unknown DDS compression format: ${compression}`);
            }
        } else if (pfFlags & DDSPixelFormatFlags.DDPF_RGB) {
            // Read RGB bit count
            reader.setPosition(88); // Position of RGB bit count
            const rgbBitCount = reader.readUInt32LE('RGB Bit Count');
            if (rgbBitCount !== undefined) {
                bitsPerPixel = rgbBitCount;
            }
        }
        
        // Create and return metadata
        return {
            format: ImageFormat.DDS,
            width: width,
            height: height,
            mipMapCount: mipMapCount,
            compression: compression,
            hasAlpha: hasAlpha,
            bitsPerPixel: bitsPerPixel
        };
    } catch (error) {
        // Handle error and return minimal metadata
        const context = createImageErrorContext(resourceId, instanceId, 'parseDDS');
        const result = handleImageError(error, context);
        
        return {
            format: ImageFormat.DDS,
            width: undefined,
            height: undefined,
            mipMapCount: undefined,
            compression: undefined,
            hasAlpha: undefined,
            bitsPerPixel: undefined
        };
    }
}
