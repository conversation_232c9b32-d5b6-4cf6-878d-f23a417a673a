/**
 * Audio extractor module for the Sims 4 mod management tool
 *
 * This module extracts metadata from audio resources in Sims 4 package files.
 */

import { <PERSON>Key as AppResource<PERSON>ey, ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { ResourceCategory } from '../../../../types/resource/enums.js';
import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { DependencyInfo } from '../../../../types/database.js';
import {
    SoundHeaderInfo,
    detectAudioFormat,
    parseWavHeader,
    parseMp3Header,
    parseOggHeader,
    parseSnrHeader,
    parseSnsHeader,
    parseEaHeaderlessAudio,
    parseEaAudioContainer,
    parseXmlSoundEffect,
    categorizeAudio as determineAudioType,
    generateAudioContentSnippet as createContentSnippet,
    AUDIO_RESOURCE_TYPES,
    AudioCategory,
    AudioCodec,
    AudioBankType
} from './index.js';

// Note: parseEaLayer3Header is now part of parseEaAudioContainer

const logger = new Logger('AudioExtractor');

/**
 * Extracts metadata specifically from Sound resources.
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object for Sound resources.
 */
export async function extractAudioMetadata(
    key: AppResourceKey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};
    let contentSnippet: string | undefined = undefined;
    const tgiDependencies: DependencyInfo[] = [];

    try {
        // Sound resources are binary, so we need to identify the format and extract metadata
        if (buffer.length < 12) {
            logger.warn(`Sound buffer too small for ${key.instance.toString(16)}`);
            return { contentSnippet: '[Sound Buffer Too Small]' };
        }

        let header: SoundHeaderInfo = {};

        // Try to extract resource name from instance ID
        const instanceHex = key.instance.toString(16).toLowerCase();
        try {
            // Try to convert hex to ASCII to see if there's a readable name
            const possibleName = Buffer.from(instanceHex, 'hex').toString('utf8');
            if (possibleName && /^[\x20-\x7E]+$/.test(possibleName)) { // Check if printable ASCII
                header.instanceName = possibleName.trim();
            }
        } catch (e) {
            // Ignore conversion errors
        }

        // Check for specific resource types first
        if (key.type === AUDIO_RESOURCE_TYPES.SOUND_EFFECT_ALT) {
            // This is likely an XML-based animation sound effect
            header = parseXmlSoundEffect(buffer);
            logger.debug(`Parsed XML sound effect: ${header.soundName || 'unnamed'}`);
        } else if (key.type === AUDIO_RESOURCE_TYPES.HEADERLESS_SOUND) {
            // This is an EA headerless audio format
            header = parseEaHeaderlessAudio(buffer);
            logger.debug(`Parsed EA headerless audio: ${header.format}, clips: ${header.soundClipCount || 'unknown'}`);
        } else {
            // For other types, use the general format detection
            const format = detectAudioFormat(buffer);

            // Parse header based on detected format
            if (format === 'WAV') {
                header = parseWavHeader(buffer);
            } else if (format === 'MP3') {
                header = parseMp3Header(buffer);
            } else if (format === 'OGG') {
                header = parseOggHeader(buffer);
            } else if (format === 'SNR') {
                header = parseSnrHeader(buffer);
            } else if (format === 'SNS') {
                header = parseSnsHeader(buffer);
            } else if (format === 'SCH') {
                // Sound Channel format
                header.format = 'Sound Channel';
                header.audioType = AudioCategory.SOUND;
            } else if (format === 'SBK') {
                // Sound Bank format
                header.format = 'Sound Bank';
                header.audioType = AudioCategory.SOUND;
                header.bankType = AudioBankType.SOUND_BANK;
            } else if (format === 'EALayer3') {
                header = parseEaAudioContainer(buffer);
                header.codec = AudioCodec.EALAYER3;
            } else if (format === 'XML') {
                header = parseXmlSoundEffect(buffer);
            } else if (format && format.startsWith('EA Audio')) {
                // EA Audio format with specific codec
                header.format = format;
                header.isCompressed = true;

                if (format.includes('EALayer3')) {
                    header.codec = AudioCodec.EALAYER3;
                } else if (format.includes('EAOpus')) {
                    header.codec = AudioCodec.EAOPUS;
                } else if (format.includes('EAMP3')) {
                    header.codec = AudioCodec.EAMP3;
                } else if (format.includes('EAAC')) {
                    header.codec = AudioCodec.EAAC;
                } else if (format.includes('EAStream')) {
                    header.codec = AudioCodec.EASTREAM;
                } else if (format.includes('XAS')) {
                    header.codec = AudioCodec.XAS;
                }
            } else if (format === 'EA Headerless Audio' || format === 'EA Sound Bank') {
                header = parseEaHeaderlessAudio(buffer);
            } else {
                // Unknown format
                header.format = 'Unknown';
            }
        }

        // If we couldn't determine the audio type from the format, try to determine it from the resource type and instance ID
        if (!(header as any).audioType) {
            // TODO: Fix type mismatch - determineAudioType returns number but audioType expects SoundHeaderInfo
            // (header as any).audioType = determineAudioType(key.type, key.instance) as any;
        }

        // If audio type wasn't determined from the header, try to determine it from the resource key
        if (!(header as any).audioType) {
            // TODO: Fix type mismatch - determineAudioType returns number but audioType expects SoundHeaderInfo
            // (header as any).audioType = determineAudioType(key.type, key.instance) as any;
        }

        // Determine resource name
        const resourceName = header.resourceName || header.soundName || header.instanceName ||
                            `Sound ${key.type.toString(16)}_${key.instance.toString(16)}`;
        (extractedMetadata as any).name = resourceName;

        // Determine resource description
        let description = 'Sound resource';

        if (header.format === 'XML Sound Effect') {
            description = 'Animation Sound Effect Definition';

            if (header.animationName) {
                description = `Animation Sound Effect for ${header.animationName}`;
            }
        } else if (header.format && header.format.includes('Sound Bank')) {
            description = 'Sound Bank Container';

            if (header.soundClipCount) {
                description = `Sound Bank with ${header.soundClipCount} clips`;
            }
        } else if (header.format && header.format.includes('EA Audio Container')) {
            description = 'EA Audio Container';

            if (header.soundClipCount) {
                description = `EA Audio Container with ${header.soundClipCount} clips`;
            }
        } else if (header.audioType) {
            // Set description based on audio type
            switch (header.audioType) {
                case AudioCategory.MUSIC:
                    description = 'Music Track';
                    break;
                case AudioCategory.VOICE:
                    description = 'Voice Audio';
                    break;
                case AudioCategory.EFFECT:
                case AudioCategory.SFX:
                    description = 'Sound Effect';
                    break;
                case AudioCategory.AMBIENCE:
                    description = 'Ambient Sound';
                    break;
                case AudioCategory.UI:
                    description = 'UI Sound';
                    break;
                case AudioCategory.FOOTSTEP:
                    description = 'Footstep Sound';
                    break;
                case AudioCategory.NOTIFICATION:
                    description = 'Notification Sound';
                    break;
                case AudioCategory.STINGER:
                    description = 'Audio Stinger';
                    break;
            }
        }

        (extractedMetadata as any).description = description;

        // Set category and tags
        (extractedMetadata as any).category = (ResourceCategory as any).AUDIO;
        const tags = ['audio'];

        if (header.audioType) {
            tags.push(header.audioType.toLowerCase());
        }

        if (header.format === 'XML Sound Effect') {
            tags.push('animation');
            tags.push('sound_effect');
        }

        if (header.format && header.format.includes('Sound Bank')) {
            tags.push('sound_bank');
        }

        if (header.format && header.format.includes('EA Audio Container')) {
            tags.push('container');
        }

        (extractedMetadata as any).tags = tags;

        // Store basic audio metadata
        if (header.format) {
            (extractedMetadata as any).soundFormat = header.format;
        }

        if (header.sampleRate) {
            (extractedMetadata as any).soundSampleRate = header.sampleRate;
        }

        if (header.channels) {
            (extractedMetadata as any).soundChannels = header.channels;
        }

        if (header.duration) {
            (extractedMetadata as any).soundDuration = header.duration;
        }

        if (header.dataSize) {
            (extractedMetadata as any).soundDataSize = header.dataSize;
        }

        if (header.bitDepth) {
            (extractedMetadata as any).soundBitDepth = header.bitDepth;
        }

        if (header.loopStart) {
            (extractedMetadata as any).soundLoopStart = header.loopStart;
        }

        if (header.loopEnd) {
            (extractedMetadata as any).soundLoopEnd = header.loopEnd;
        }

        if (header.isLooping !== undefined) {
            (extractedMetadata as any).soundIsLooping = header.isLooping;
        }

        if (header.audioType) {
            (extractedMetadata as any).soundType = header.audioType;
        }

        if (header.codec) {
            (extractedMetadata as any).soundCodec = header.codec;
        }

        if (header.isCompressed !== undefined) {
            (extractedMetadata as any).soundIsCompressed = header.isCompressed;
        }

        if (header.version !== undefined) {
            (extractedMetadata as any).soundVersion = header.version;
        }

        // Store XML Sound Effect specific metadata
        if (header.soundName) {
            (extractedMetadata as any).soundName = header.soundName;
        }

        if (header.soundRefCount) {
            (extractedMetadata as any).soundRefCount = header.soundRefCount;
        }

        if (header.animationName) {
            (extractedMetadata as any).soundAnimationName = header.animationName;
        }

        // Store EA Audio Container specific metadata
        if (header.soundClipCount) {
            (extractedMetadata as any).soundClipCount = header.soundClipCount;
        }

        // Store additional Sims 4 specific metadata
        if (header.channelFlags) {
            (extractedMetadata as any).soundChannelFlags = header.channelFlags;
        }

        if (header.bankName) {
            (extractedMetadata as any).soundBankName = header.bankName;
        }

        if (header.bankType) {
            (extractedMetadata as any).soundBankType = header.bankType;
        }

        if (header.category) {
            (extractedMetadata as any).soundCategory = header.category;
        }

        // Store instance name if available
        if (header.instanceName) {
            (extractedMetadata as any).soundInstanceName = header.instanceName;
        }

        // Create content snippet
        contentSnippet = createContentSnippet(header, buffer as any);

        // Save sound metadata for deeper analysis
        try {
            databaseService.parsedContent.saveParsedContent({
                resourceId: resourceId,
                contentType: 'sound_metadata',
                content: JSON.stringify(header)
            });
            logger.debug(`Saved sound metadata for resource ${resourceId}`);
        } catch (dbError: any) {
            logger.error(`Failed to save sound metadata for resource ${resourceId} to DB: ${dbError.message || dbError}`);
        }

        // Sound resources typically don't have TGI dependencies, but they might reference
        // other sound resources in a sound bank or collection

        // Save any found dependencies
        if (tgiDependencies.length > 0) {
            try {
                for (const dependency of tgiDependencies) {
                    await databaseService.dependencies.saveDependency(dependency);
                }
                logger.debug(`Saved ${tgiDependencies.length} dependencies for sound resource ${resourceId}`);
            } catch (depError: any) {
                logger.error(`Failed to save dependencies for sound resource ${resourceId}: ${depError.message || depError}`);
            }
        }

    } catch (error: any) {
        logger.error(`Error extracting sound metadata for ${key.instance.toString(16)}: ${error.message || error}`);
        contentSnippet = '[Sound Parse Error]';
    }

    // TODO: Implement analysis and conflict detection for Audio Resources
    // - Detect potential duplicates based on audio properties (format, sample rate, channels, duration, data size)
    // - Analyze sound bank structure and check for conflicts with other sound banks
    // - Analyze XML Sound Effects for references to non-existent resources

    // Basic potential duplicate detection based on properties
    // In a real scenario, this would involve comparing with a database of known audio resources
    const knownAudioProperties = {
        format: 'WAV',
        sampleRate: 44100,
        channels: 2,
        duration: 10.0, // seconds
    };

    // TODO: Fix header scope issue - this code block needs to be moved to where header is in scope
    // if (header.format === knownAudioProperties.format &&
    //     header.sampleRate === knownAudioProperties.sampleRate &&
    //     header.channels === knownAudioProperties.channels &&
    //     header.duration !== undefined && Math.abs(header.duration - knownAudioProperties.duration) < 0.1) {
    //     (extractedMetadata as any).isPotentialDuplicate = true;
    // } else {
    //     (extractedMetadata as any).isPotentialDuplicate = false;
    // }

    // Placeholder for sound bank analysis results
    // (extractedMetadata as any).soundBankAnalysis = {}; // Store results of sound bank structure analysis and conflict detection

    // Return extracted metadata
    return {
        contentSnippet: contentSnippet,
        ...extractedMetadata,
    };
}

/**
 * Checks if a resource is an audio resource
 * @param typeId The resource type ID
 * @returns True if the resource is an audio resource
 */

/**
 * Checks if a resource is an audio resource
 * @param typeId The resource type ID
 * @returns True if the resource is an audio resource
 */

/**
 * Checks if a resource is an audio resource
 * @param typeId The resource type ID
 * @returns True if the resource is an audio resource
 */
export function isAudioResource(typeId: number): boolean {
    return Object.values(AUDIO_RESOURCE_TYPES).includes(typeId);
}
