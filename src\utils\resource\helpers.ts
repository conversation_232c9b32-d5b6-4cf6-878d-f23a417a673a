import { ResourceKey as S4TKResourceKey } from '@s4tk/models/types';
// Import the re-exported enum from our core file (only for type hints where needed)
import { BinaryResourceType } from '../../types/resource/core.js';
import { ResourceCategory } from '../../types/resource/enums.js';
import { ResourceKey, ResourceMetadata } from '../../types/resource/interfaces.js';
import { Logger } from '../logging/logger.js'; // Import Logger

const logger = new Logger('ResourceHelpers'); // Instantiate logger

// No longer need to log the runtime value here

/**
 * Resource type category map - Map official S4TK BinaryResourceType enum values to our categories.
 * This map should use the numeric values of the enum as keys.
 */
export const ResourceTypeCategoryMap = new Map<number, ResourceCategory>([ // Type annotation uses number for keys
  // Use numeric values from the enum
  [570775514, ResourceCategory.TUNING], // TUNING
  [209137191, ResourceCategory.SCRIPT], // SCRIPT
  [1415235194, ResourceCategory.TUNING], // SIMDATA
  [11720834, ResourceCategory.IMAGE], // IMAGE (Generic DDS/PNG/RLE2)
  [832458525, ResourceCategory.OBJECT], // OBJECT_DEFINITION
  [55242443, ResourceCategory.OBJECT], // CASPART
  [0x034AEECB, ResourceCategory.OBJECT], // Add the other observed CAS Part type ID
  [539399691, ResourceCategory.SOUND], // SOUND_EFFECT
  [193723223, ResourceCategory.SOUND], // SOUND_TRACK
  [1797309683, ResourceCategory.ANIMATION], // ANIMATION_STATE_MACHINE
  [0x01661233, ResourceCategory.OBJECT], // MODEL (Hex value)
  [0x01D10F34, ResourceCategory.OBJECT], // MODEL_LOD (Hex value)
  [3931183280, ResourceCategory.SCRIPT], // SCRIPT_MODULE
  [1659456824, ResourceCategory.TUNING], // COMBINED_TUNING
  [796721156, ResourceCategory.IMAGE], // PNG_IMAGE
  [373075429, ResourceCategory.IMAGE], // RLE2_IMAGE
  [1492744074, ResourceCategory.IMAGE], // CAS_PART_THUMBNAIL
  [55959718, ResourceCategory.OBJECT], // CAS_PRESET
  [3571055591, ResourceCategory.OBJECT], // OBJECT_CATALOG (Confirmed) - Changed from TUNING to OBJECT based on common sense
  [4253767882, ResourceCategory.TUNING], // OBJECT_CATALOG_SET
  [39769844, ResourceCategory.ASSET], // OPEN_TYPE_FONT
  [4223212257, ResourceCategory.OBJECT], // BUILD_PART
  [0xD382BF57, ResourceCategory.OBJECT], // FOOTPRINT (Hex value)
  [55867754, ResourceCategory.ANIMATION], // BONE_DELTA
  [1185127477, ResourceCategory.OBJECT], // PHYSICS
  [3235601127, ResourceCategory.ASSET], // TRAY_ITEM
  [5562537, ResourceCategory.TUNING], // SIMDESCRIPTION
  [0x220557DA, ResourceCategory.TUNING], // STRING_TABLE (Hex value)
  [177661663, ResourceCategory.TUNING], // NAME_MAP
  [1808779248, ResourceCategory.TUNING], // REGION
  [2887187436, ResourceCategory.TUNING], // REGION_DESCRIPTION
  [3321263678, ResourceCategory.IMAGE], // REGION_MAP
  // Added based on observed data, may need refinement
  // [0x319e4f1d, ResourceCategory.IMAGE], // DstImage (ObjectCatalog Thumbnail?) - Removed duplicate key
  [0xb2d882, ResourceCategory.IMAGE], // Generic Image? (Previously mapped to OBJECT)
  [0x15a1849, ResourceCategory.TUNING], // RegionMap - could also be image?
  [0x3453cf95, ResourceCategory.IMAGE], // Rle2Image
  [55959713, ResourceCategory.TUNING], // MODIFIER
  [55959714, ResourceCategory.TUNING], // RECIPE
  [55959715, ResourceCategory.UNKNOWN], // TEXT
  [55959716, ResourceCategory.TUNING], // XML
  // --- Verified Mappings from Logs/Assets ---
  [0x8EAF13DE, ResourceCategory.ANIMATION], // RIG
  [0xD3044521, ResourceCategory.OBJECT], // SLOT
  [0x3B4C61D, ResourceCategory.TUNING], // Light / UNKNOWN_TUNING_A
  [0x81CA1A10, ResourceCategory.TUNING], // UNKNOWN_TUNING_B
  [0x3C2A8647, ResourceCategory.IMAGE], // BUILD_BUY_THUMBNAIL
  // --- Added from test run output ---
  [0xac16fbec, ResourceCategory.OBJECT], // RegionMap
  [0xba856c78, ResourceCategory.IMAGE], // Specular/Cubemap
  [0x3c1d8799, ResourceCategory.TUNING], // XML (Unknown)
  [0x339bc5bd, ResourceCategory.OBJECT], // Unknown Object Type
  [0x545ac67a, ResourceCategory.TUNING], // SimData (Added from test run)
  [0x6017e896, ResourceCategory.OBJECT], // ObjectDefinition (Added from test run)
  [0xb8bf1a63, ResourceCategory.IMAGE], // Specular/Cubemap (Added from test run)
  // --- End Added from test run output ---
  [0, ResourceCategory.UNKNOWN], // UNKNOWN (Fallback)
]);

// Manually create maps for converting between names and values based on core.js
const BinaryResourceTypeValueMap = new Map<string, number>([
  ["UNKNOWN", 0],
  ["TUNING", 570775514],
  ["SCRIPT", 209137191],
  ["SIMDATA", 1415235194],
  ["IMAGE", 11720834],
  ["OBJECT_DEFINITION", 832458525],
  ["CASPART", 55242443],
  ["SOUND_EFFECT", 539399691],
  ["SOUND_TRACK", 193723223],
  ["SCRIPT_MODULE", 3931183280],
  ["COMBINED_TUNING", 1659456824],
  ["PNG_IMAGE", 796721156],
  ["RLE2_IMAGE", 373075429],
  ["MODEL", 23466547],
  ["MODEL_LOD", 30478132],
  ["CAS_PART_THUMBNAIL", 1492744074],
  ["CAS_PRESET", 55959718],
  ["OBJECT_CATALOG", 3571055591],
  ["OBJECT_CATALOG_SET", 4253767882],
  ["ANIMATION_STATE_MACHINE", 1797309683],
  ["OPEN_TYPE_FONT", 39769844],
  ["BUILD_PART", 4223212257],
  ["FOOTPRINT", 3548561239],
  ["SLOT", 171372668],
  ["BONE_DELTA", 55867754],
  ["PHYSICS", 1185127477],
  ["TRAY_ITEM", 3235601127],
  ["SIMDESCRIPTION", 5562537],
  ["STRING_TABLE", 570775514], // Note: Duplicate value with TUNING, but different name
  ["NAME_MAP", 177661663],
  ["REGION", 1808779248],
  ["REGION_DESCRIPTION", 2887187436],
  ["REGION_MAP", 3321263678],
  ["MODIFIER", 55959713],
  ["RECIPE", 55959714],
  ["TEXT", 55959715],
  ["XML", 55959716],
]);

const BinaryResourceTypeNameMap = new Map<number, string>(Array.from(BinaryResourceTypeValueMap.entries()).map(([name, value]) => [value, name]));

// Add custom names not in the core enum to the name map
BinaryResourceTypeNameMap.set(0x8EAF13DE, 'Rig');
BinaryResourceTypeNameMap.set(0xD3044521, 'Slot'); // Added missing Slot mapping
BinaryResourceTypeNameMap.set(0x3B4C61D, 'Light');
BinaryResourceTypeNameMap.set(0x81CA1A10, 'TuningB');
BinaryResourceTypeNameMap.set(0x3C2A8647, 'BuildBuyThumbnail');
BinaryResourceTypeNameMap.set(0xac16fbec, 'RegionMap'); // Added from test run
BinaryResourceTypeNameMap.set(0xba856c78, 'Specular/Cubemap'); // Added from test run
BinaryResourceTypeNameMap.set(0x3c1d8799, 'XML (Unknown)'); // Added from test run
BinaryResourceTypeNameMap.set(0x339bc5bd, 'Unknown Object Type'); // Added from test run
BinaryResourceTypeNameMap.set(0x545ac67a, 'SimData'); // Added from test run
BinaryResourceTypeNameMap.set(0x6017e896, 'ObjectDefinition'); // Added from test run
BinaryResourceTypeNameMap.set(0xb8bf1a63, 'Specular/Cubemap'); // Added from test run


logger.info(`ResourceTypeCategoryMap initialized with ${ResourceTypeCategoryMap.size} entries.`);
logger.debug(`ResourceTypeCategoryMap contents: ${JSON.stringify(Array.from(ResourceTypeCategoryMap.entries()))}`);

/**
 * Creates a ResourceKey object using bigint for group and instance.
 */
export function createResourceKey(
  type: number,
  group: bigint,
  instance: bigint,
  name?: string,
  path?: string
): ResourceKey {
  return {
    type: type,
    group: group,
    instance: instance,
    name: name,
    path: path,
    id: instance.toString(),
  };
}

/**
 * Converts an S4TK ResourceKey object to the application's ResourceKey interface.
 */
export function convertS4TKResourceKey(s4tkKey: S4TKResourceKey): ResourceKey {
  return {
    type: s4tkKey.type,
    group: BigInt(s4tkKey.group),
    instance: s4tkKey.instance,
    id: s4tkKey.instance.toString(),
  };
}

/**
 * Converts the application's ResourceKey interface back to an S4TK ResourceKey object.
 */
export function convertToS4TKResourceKey(appKey: ResourceKey): S4TKResourceKey {
  const result: any = {
    type: appKey.type,
    group: appKey.group,
    instance: appKey.instance,
    equals: (other: S4TKResourceKey | undefined): boolean => {
      if (!other) return false;
      return appKey.type === other.type &&
             appKey.group === BigInt(other.group) &&
             appKey.instance === other.instance;
    },
    toString: () =>
      `T:${appKey.type.toString(16).toUpperCase()} G:${appKey.group.toString()} I:${appKey.instance.toString()}`
  };
  return result as S4TKResourceKey;
}

/**
 * Compares two ResourceKey objects for equality.
 */
export function compareResourceKeys(key1: ResourceKey, key2: ResourceKey): boolean {
  return (
    key1.type === key2.type &&
    key1.group === key2.group &&
    key1.instance === key2.instance
  );
}

/**
 * Determines the category of a resource based on its type number using the consolidated map.
 */
export function getResourceTypeCategory(resourceType: number): ResourceCategory {
  logger.debug(`Getting category for type number: 0x${resourceType.toString(16)}`);
  const category = ResourceTypeCategoryMap.get(resourceType);
  if (category !== undefined) {
    // Use ResourceCategory[category] to log the enum member name for clarity
    logger.debug(`Map lookup result for 0x${resourceType.toString(16)}: ${ResourceCategory[category]}`);
    return category;
  } else {
    logger.warn(`Type 0x${resourceType.toString(16)} not found in ResourceTypeCategoryMap. Defaulting to UNKNOWN.`);
    return ResourceCategory.UNKNOWN;
  }
}


// Helper function to check if a type number corresponds to a known BinaryResourceType
export function isBinaryResourceType(type: number): boolean {
  // Check if the type exists as a key in the category map
  return ResourceTypeCategoryMap.has(type);
}

// Helper function to convert a type number or string name to a ResourceType number
export function convertToResourceType(type: number | string): number { // Return number instead of BinaryResourceType
  if (typeof type === 'number') {
    // If it's a number and exists in the category map, return it
    if (ResourceTypeCategoryMap.has(type)) {
      return type;
    }
  } else if (typeof type === 'string') {
    // Look up the numeric value in the name map
    const numericValue = BinaryResourceTypeValueMap.get(type.toUpperCase()); // Use the value map
    if (numericValue !== undefined && ResourceTypeCategoryMap.has(numericValue)) {
       return numericValue;
    }
  }
  // Default to UNKNOWN (0) if not found
  return 0;
}

// Helper function to convert various types to string
export function convertToString(value: string | number | bigint): string {
  return value.toString();
}

// Helper function to get resource type name from its number value
export function getResourceTypeName(type: number): string {
  // Look up the name in the name map
  const enumKey = BinaryResourceTypeNameMap.get(type);
  if (enumKey) return enumKey; // Return PascalCase name

  // Add custom names for known types not in the official enum
  switch (type) {
    case 0x8EAF13DE: return 'Rig'; // Use PascalCase or consistent naming
    case 0xD3044521: return 'Slot';
    case 0x3B4C61D: return 'Light'; // More descriptive than UNKNOWN_TUNING_A
    case 0x81CA1A10: return 'TuningB'; // Keep if needed, or find better name
    case 0x3C2A8647: return 'BuildBuyThumbnail';
    case 0x220557DA: return 'StringTable'; // Add STBL mapping
    case 0x545ac67a: return 'SimData'; // Added from test run
    case 0x6017e896: return 'ObjectDefinition'; // Added from test run
    case 0xb8bf1a63: return 'Specular/Cubemap'; // Added from test run
    // Add more custom names here if needed
    default: return `UNKNOWN_0x${type.toString(16).toUpperCase()}`; // Include hex for unknowns
  }
}


/**
 * Generates a unique string ID from an AppResourceKey (group and instance are bigint).
 */
export function generateResourceId(key: ResourceKey): string {
    const groupStr = key.group.toString();
    const instanceStr = key.instance.toString();
    return `${key.type}_${groupStr}_${instanceStr}`;
}

/**
 * Helper to generate an ID string from metadata for matching purposes.
 */
export function createResourceKeyFromS4TKWithName(
    s4tkKey: S4TKResourceKey,
    name: string,
    path: string
): ResourceKey {
  return {
    type: s4tkKey.type,
    group: BigInt(s4tkKey.group),
    instance: s4tkKey.instance,
    name: name,
    path: path,
    id: s4tkKey.instance.toString(),
  };
}


/**
 * Helper to generate an ID string from metadata for matching purposes.
 * Uses the ResourceMetadata's bigint instance directly if available, otherwise falls back to parsing name.
 * Note: Expects group as number, as passed from packageAnalysisService.
 */
export function generateResourceIdFromMeta(meta: ResourceMetadata, type: number, group: number): string {
    if (typeof meta.instance === 'bigint') {
        return `${type}_${group}_${meta.instance.toString()}`;
    }

    const nameParts = meta.name.split('_');
    let instanceStr = `fallback_${meta.name}`;

    if (nameParts.length >= 3) {
        const potentialInstance = nameParts[2];
        if (/^(0x[0-9a-fA-F]+|\d+)$/.test(potentialInstance)) {
            try {
                instanceStr = BigInt(potentialInstance).toString();
            } catch (e) {
                console.warn(`Failed to parse instance from meta.name "${meta.name}":`, e);
            }
        }
    }
    return `${type}_${group}_${instanceStr}`;
}
