﻿﻿// Corrected import
import { ResourceMetadata } from '../resource/interfaces.js';
// Import the central ConflictInfo type
import { ConflictInfo } from '../conflict/index.js';

// Note: This local ConflictResult might be redundant or conflict with the central one defined in conflict/index.ts or conflict/ConflictTypes.ts.
// Consider removing this local definition and importing the central one if they serve the same purpose.
export interface ConflictResult {
  type: string;
  severity: string;
  description: string;
  affectedResources: {
    type: number;
    name: string;
    path: string;
  }[];
}

export interface AnalysisStats {
  totalResources: number;
  totalConflicts: number;
  conflictsByType: Record<string, number>;
  conflictsBySeverity: Record<string, number>;
  conflictsByResourceType: Record<string, number>;
}

// Note: This local PackageAnalysisResult might be redundant or conflict with the central one defined in analysis/PackageAnalysisResult.ts.
// Consider removing this local definition and importing the central one if they serve the same purpose.
export interface PackageAnalysisResult {
  resources: ResourceMetadata[];
  conflicts: ConflictInfo[]; // Use the imported ConflictInfo type
  stats: AnalysisStats;
  recommendations: string[];
  timestamp: number;
}
