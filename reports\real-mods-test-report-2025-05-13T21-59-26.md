# Real Mods Test Report

Generated on: 5/13/2025, 10:59:26 PM

## Summary

- Total mods analyzed: 2
- Total resources analyzed: 191
- Resources analyzed for purpose: 0
- Specialized analyzer usage: 0 resources (NaN%)
- Total processing time: 3116ms (3.12s)
- Average processing time per mod: 1558.00ms

## Game Version Detection

- Successful detections: 0
- Unknown versions: 2
- Detection rate: 0.00%
- Average confidence: 0.00%

### Game Versions Detected


## Schema Version Detection

- Successful detections: 0
- Unknown schemas: 0
- Average confidence: 0.00%

### Schema Versions Detected


## Gameplay Systems

- Average confidence: 0.00%


## Resource Purposes

- Average confidence: 0.00%


### Purpose Types


## Critical Parameters

- Total parameters identified: 0
- Modified parameters: 0
- Modification rate: 0%

### Impact Levels


## Cross-Resource Relationships

- Total relationships detected: 0
- Average relationships per mod: 0.00

### Relationship Types


## Dependency Chain Analysis

- Resources with dependency analysis: 0
- Specialized analyzer usage: 0 resources

### Dependency Types


## Resource Types

- 0x34AEECB: 69
- 0x3453CF95: 55
- 0xB2D882: 23
- 0x15A1849: 19
- 0x2BC04EDF: 16
- 0x3C1AF1F2: 4
- 0xAC16FBEC: 2
- 0xBA856C78: 2
- 0x6BF15BBE: 1

## Performance

### Top 10 Slowest Mods

- CasualSims - 030 Hair Jose - Children.package: 2788ms
- Christopher067_Luxury_Earrings_V1.package: 328ms

## Errors

No errors encountered during testing.