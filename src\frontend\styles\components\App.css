.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.app-header {
  text-align: center;
  margin-bottom: 3rem;
}

.app-header h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.app-header .subtitle {
  margin: 0.5rem 0 0;
  color: var(--text-secondary);
  font-size: 1.125rem;
  line-height: 1.5;
}

/* Main Content Styles */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Section Styles */
.upload-section,
.progress-section,
.results-section {
  background: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.upload-section {
  margin-bottom: 1rem;
}

.progress-section {
  margin-bottom: 1rem;
}

.results-section {
  flex: 1;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .app-header .subtitle {
    font-size: 1rem;
  }

  .upload-section,
  .progress-section,
  .results-section {
    padding: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .app-container {
    background: var(--background-color-dark);
  }

  .upload-section,
  .progress-section,
  .results-section {
    background: var(--surface-color-dark);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

/* Loading State */
.app-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* Error State */
.app-container.error {
  border-color: var(--error-color);
}

/* Success State */
.app-container.success {
  border-color: var(--success-color);
} 