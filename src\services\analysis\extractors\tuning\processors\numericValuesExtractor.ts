/**
 * Interface for numeric values extracted from tuning
 */
export interface NumericValuesInfo {
    values: number[];
    ranges: { min: number; max: number }[];
    hasNegatives: boolean;
    hasDecimals: boolean;
}

/**
 * Extracts numeric values from a tuning object
 * @param obj The object to extract values from
 * @returns Object containing numeric value information
 */
export function extractNumericValues(obj: any): NumericValuesInfo {
    const values: number[] = [];
    const ranges: { min: number; max: number }[] = [];
    let hasNegatives = false;
    let hasDecimals = false;

    const processValue = (value: any) => {
        if (typeof value === 'number') {
            values.push(value);
            if (value < 0) hasNegatives = true;
            if (!Number.isInteger(value)) hasDecimals = true;
        } else if (typeof value === 'string') {
            // Try to parse numeric strings
            const num = parseFloat(value);
            if (!isNaN(num)) {
                values.push(num);
                if (num < 0) hasNegatives = true;
                if (!Number.isInteger(num)) hasDecimals = true;
            }
        }
    };

    const processObject = (obj: any) => {
        if (!obj || typeof obj !== 'object') return;

        // Process arrays
        if (Array.isArray(obj)) {
            obj.forEach(item => {
                if (typeof item === 'object') {
                    processObject(item);
                } else {
                    processValue(item);
                }
            });
            return;
        }

        // Look for range-like properties
        if ('min' in obj && 'max' in obj) {
            const min = parseFloat(obj.min);
            const max = parseFloat(obj.max);
            if (!isNaN(min) && !isNaN(max)) {
                ranges.push({ min, max });
                if (min < 0 || max < 0) hasNegatives = true;
                if (!Number.isInteger(min) || !Number.isInteger(max)) hasDecimals = true;
            }
        }

        // Process all properties
        for (const key in obj) {
            const value = obj[key];
            if (typeof value === 'object') {
                processObject(value);
            } else {
                processValue(value);
            }
        }
    };

    processObject(obj);

    return {
        values,
        ranges,
        hasNegatives,
        hasDecimals
    };
} 