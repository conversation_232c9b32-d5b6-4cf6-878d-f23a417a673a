import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  base: './', // Use relative paths for assets in production build
  // Set root to the electron frontend directory
  root: path.resolve(__dirname, 'src/frontend/electron'),
  build: {
    // Output relative to the new root, placing it in the project's root dist folder
    outDir: path.resolve(__dirname, 'dist/frontend/electron'), // Adjusted output directory
    emptyOutDir: false, // Do NOT clean the output directory, keep tsc output
    // Removed explicit rollupOptions.input - Vite will default to index.html in root
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000, // Keep for potential 'vite dev' usage if needed later
    // Proxies might still be relevant if running dev server against backend
    proxy: {
      '/api': {
        target: 'http://localhost:8501', // Ensure this matches backend port
        changeOrigin: true
      },
      '/socket.io': {
        target: 'http://localhost:8501', // Proxy socket.io to backend too
        ws: true
      }
    }
  }
});
