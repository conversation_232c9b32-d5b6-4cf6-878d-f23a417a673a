/**
 * Comprehensive Test Orchestrator
 * 
 * This module orchestrates comprehensive testing of all system components
 * with progressive scaling and real-world validation scenarios.
 */

import { 
    testPackageAnalyzerComprehensive,
    testDatabaseServiceComprehensive,
    testResourceTrackerConcurrency,
    testMemoryManagerStress,
    ComprehensiveTestResult,
    ComprehensiveTestOptions
} from './comprehensiveSystemTests.js';
import { testIntelligentConflictDetection } from './intelligentConflictTests.js';
import { testSemanticConflictDetection } from './semanticConflictTests.js';
import { testStreamingPipeline, runProgressiveStreamingTests } from './streamingPipelineTests.js';
import { findPackageFiles } from './fileScanner.js';
import { Logger } from '../../utils/logging/logger.js';
import { EnhancedMemoryManager } from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker } from '../../utils/memory/resourceTracker.js';

/**
 * Comprehensive test suite result
 */
export interface ComprehensiveTestSuiteResult {
    success: boolean;
    totalTests: number;
    passedTests: number;
    failedTests: number;
    duration: number;
    testResults: {
        packageAnalyzer?: ComprehensiveTestResult;
        databaseService?: ComprehensiveTestResult;
        resourceTracker?: ComprehensiveTestResult;
        memoryManager?: ComprehensiveTestResult;
        conflictDetector?: any;
        semanticConflicts?: any;
        streamingPipeline?: any;
        largeScale?: any;
    };
    overallMetrics: {
        totalResourcesProcessed: number;
        totalConflictsDetected: number;
        peakMemoryUsage: any;
        averageProcessingTime: number;
    };
    errors: string[];
    warnings: string[];
}

/**
 * Test suite options
 */
export interface ComprehensiveTestSuiteOptions {
    modsPath: string;
    maxPackages?: number;
    testMode?: 'minimal' | 'standard' | 'comprehensive' | 'stress';
    logLevel?: string;
    useInMemoryDatabase?: boolean;
    enableProgressiveScaling?: boolean;
    scalingSteps?: number[];
    enableConcurrencyTesting?: boolean;
    enableMemoryStressTesting?: boolean;
    enableConflictDetectionTesting?: boolean;
    enableStreamingPipelineTesting?: boolean;
    enableLargeScaleTesting?: boolean;
}

/**
 * Run comprehensive test suite with progressive scaling
 */
export async function runComprehensiveTestSuite(
    options: ComprehensiveTestSuiteOptions
): Promise<ComprehensiveTestSuiteResult> {
    const startTime = Date.now();
    const logger = new Logger('ComprehensiveTestSuite');
    const memoryManager = EnhancedMemoryManager.getInstance();
    const resourceTracker = ResourceTracker.getInstance();

    const result: ComprehensiveTestSuiteResult = {
        success: false,
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        duration: 0,
        testResults: {},
        overallMetrics: {
            totalResourcesProcessed: 0,
            totalConflictsDetected: 0,
            peakMemoryUsage: {},
            averageProcessingTime: 0
        },
        errors: [],
        warnings: []
    };

    try {
        logger.info('===== COMPREHENSIVE SYSTEM TEST SUITE =====');
        logger.info(`Test mode: ${options.testMode || 'standard'}`);
        logger.info(`Mods path: ${options.modsPath}`);
        logger.info(`Max packages: ${options.maxPackages || 'unlimited'}`);

        // Find available package files
        const packageFiles = await findPackageFiles(options.modsPath, {
            maxFiles: options.maxPackages || 1000,
            maxDepth: 3,
            randomize: true
        });

        if (packageFiles.length === 0) {
            result.errors.push('No package files found for testing');
            return result;
        }

        logger.info(`Found ${packageFiles.length} package files for testing`);

        const testOptions: ComprehensiveTestOptions = {
            maxPackages: options.maxPackages,
            logLevel: options.logLevel,
            useInMemoryDatabase: options.useInMemoryDatabase,
            testMode: options.testMode,
            enableMemoryProfiling: true,
            enablePerformanceProfiling: true,
            testConcurrency: options.enableConcurrencyTesting,
            testErrorRecovery: true
        };

        // Test 1: Package Analyzer Comprehensive Testing
        if (options.testMode !== 'minimal') {
            logger.info('\n----- Running Package Analyzer Comprehensive Test -----');
            result.totalTests++;
            
            try {
                const packageAnalyzerResult = await testPackageAnalyzerComprehensive(
                    options.modsPath,
                    testOptions
                );
                
                result.testResults.packageAnalyzer = packageAnalyzerResult;
                
                if (packageAnalyzerResult.success) {
                    result.passedTests++;
                    logger.info('✅ Package Analyzer test PASSED');
                } else {
                    result.failedTests++;
                    result.errors.push(...packageAnalyzerResult.errors);
                    logger.error('❌ Package Analyzer test FAILED');
                }
                
                result.overallMetrics.totalResourcesProcessed += packageAnalyzerResult.metrics.resourcesProcessed;
                
            } catch (error: any) {
                result.failedTests++;
                result.errors.push(`Package Analyzer test error: ${error.message}`);
                logger.error(`❌ Package Analyzer test ERROR: ${error.message}`);
            }
        }

        // Test 2: Database Service Comprehensive Testing
        logger.info('\n----- Running Database Service Comprehensive Test -----');
        result.totalTests++;
        
        try {
            const databaseResult = await testDatabaseServiceComprehensive(testOptions);
            result.testResults.databaseService = databaseResult;
            
            if (databaseResult.success) {
                result.passedTests++;
                logger.info('✅ Database Service test PASSED');
            } else {
                result.failedTests++;
                result.errors.push(...databaseResult.errors);
                logger.error('❌ Database Service test FAILED');
            }
            
        } catch (error: any) {
            result.failedTests++;
            result.errors.push(`Database Service test error: ${error.message}`);
            logger.error(`❌ Database Service test ERROR: ${error.message}`);
        }

        // Test 3: Resource Tracker Concurrency Testing
        if (options.enableConcurrencyTesting !== false) {
            logger.info('\n----- Running Resource Tracker Concurrency Test -----');
            result.totalTests++;
            
            try {
                const resourceTrackerResult = await testResourceTrackerConcurrency(testOptions);
                result.testResults.resourceTracker = resourceTrackerResult;
                
                if (resourceTrackerResult.success) {
                    result.passedTests++;
                    logger.info('✅ Resource Tracker test PASSED');
                } else {
                    result.failedTests++;
                    result.errors.push(...resourceTrackerResult.errors);
                    logger.error('❌ Resource Tracker test FAILED');
                }
                
            } catch (error: any) {
                result.failedTests++;
                result.errors.push(`Resource Tracker test error: ${error.message}`);
                logger.error(`❌ Resource Tracker test ERROR: ${error.message}`);
            }
        }

        // Test 4: Memory Manager Stress Testing
        if (options.enableMemoryStressTesting !== false) {
            logger.info('\n----- Running Memory Manager Stress Test -----');
            result.totalTests++;
            
            try {
                const memoryManagerResult = await testMemoryManagerStress(testOptions);
                result.testResults.memoryManager = memoryManagerResult;
                
                if (memoryManagerResult.success) {
                    result.passedTests++;
                    logger.info('✅ Memory Manager test PASSED');
                } else {
                    result.failedTests++;
                    result.errors.push(...memoryManagerResult.errors);
                    logger.error('❌ Memory Manager test FAILED');
                }
                
                // Update peak memory usage
                if (memoryManagerResult.metrics.memoryUsage) {
                    result.overallMetrics.peakMemoryUsage = memoryManagerResult.metrics.memoryUsage;
                }
                
            } catch (error: any) {
                result.failedTests++;
                result.errors.push(`Memory Manager test error: ${error.message}`);
                logger.error(`❌ Memory Manager test ERROR: ${error.message}`);
            }
        }

        // Test 5: Conflict Detection Testing
        if (options.enableConflictDetectionTesting !== false) {
            logger.info('\n----- Running Conflict Detection Tests -----');
            result.totalTests++;
            
            try {
                const conflictResult = await testIntelligentConflictDetection({
                    maxTestScenarios: options.testMode === 'stress' ? 100 : 50,
                    testTraitConflicts: true,
                    testBuffConflicts: true,
                    testSeverityClassification: true,
                    testFalsePositiveReduction: true,
                    testConfigurationHandling: true,
                    testPrioritySorting: true,
                    logLevel: options.logLevel,
                    useInMemoryDatabase: options.useInMemoryDatabase,
                    logDetailedResults: false
                });
                
                result.testResults.conflictDetector = conflictResult;
                
                if (conflictResult.success) {
                    result.passedTests++;
                    logger.info('✅ Conflict Detection test PASSED');
                } else {
                    result.failedTests++;
                    result.errors.push(...conflictResult.errors);
                    logger.error('❌ Conflict Detection test FAILED');
                }
                
            } catch (error: any) {
                result.failedTests++;
                result.errors.push(`Conflict Detection test error: ${error.message}`);
                logger.error(`❌ Conflict Detection test ERROR: ${error.message}`);
            }
        }

        // Calculate final metrics
        result.duration = Date.now() - startTime;
        result.success = result.failedTests === 0;
        result.overallMetrics.averageProcessingTime = result.duration / result.totalTests;

        // Log final results
        logger.info('\n===== COMPREHENSIVE TEST SUITE RESULTS =====');
        logger.info(`Total tests: ${result.totalTests}`);
        logger.info(`Passed: ${result.passedTests} ✅`);
        logger.info(`Failed: ${result.failedTests} ❌`);
        logger.info(`Success rate: ${((result.passedTests / result.totalTests) * 100).toFixed(1)}%`);
        logger.info(`Total duration: ${result.duration}ms`);
        logger.info(`Total resources processed: ${result.overallMetrics.totalResourcesProcessed}`);

        if (result.errors.length > 0) {
            logger.error('\nErrors encountered:');
            result.errors.forEach((error, index) => {
                logger.error(`  ${index + 1}. ${error}`);
            });
        }

        return result;

    } catch (error: any) {
        result.errors.push(`Critical test suite error: ${error.message}`);
        result.duration = Date.now() - startTime;
        logger.error(`❌ CRITICAL ERROR: ${error.message}`);
        return result;
    }
}
