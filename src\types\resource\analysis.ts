﻿// Corrected import
import { ResourceKey } from './interfaces.js';
import { ResourceValidationResult } from './validation.js';
import { ExtendedResource } from './extended.js'; // Assuming ExtendedResource is defined correctly here

// Note: AnalysisTier enum was not present in the provided file content, so it's omitted here.
// If it's needed, it should be defined or imported.

export interface ResourceAnalysisResult {
  key: ResourceKey;
  validation: ResourceValidationResult;
  metrics: {
    size: number;
    complexity: number;
    dependencies: number;
    conflicts?: number;
    performance?: number;
  };
  recommendations: string[];
  resources: ExtendedResource[]; // Assuming ExtendedResource is the intended type
  timestamp: number;
  metadata?: {
    packageName?: string;
    packageVersion?: string;
    packageAuthor?: string;
  };
}

export interface ResourceAnalysisOptions {
  analyzeContent?: boolean;
  analyzeMetadata?: boolean;
  analyzeDependencies?: boolean;
  analyzeConflicts?: boolean;
  analyzePerformance?: boolean;
  analyzeComplexity?: boolean;
  skipAnalysis?: string[];
  customAnalysis?: {
    [key: string]: (resource: unknown) => unknown;
  };
}

// Define the LotDefinitionMetadata interface
export interface LotDefinitionMetadata {
  lotId: number;
  worldPosition: { x: number; y: number; z: number };
  rotation: number;
  size: { x: number; z: number };
  flags: number;
  templateHash: bigint;
}

// Define the BlueprintMetadata interface (_BPT - 0x3924DE26)
export interface BlueprintMetadata {
  instanceId: bigint;  // The instance ID that matches the TemplateHash in LotDefinition
  objectCount: number; // Number of objects in the blueprint
  roomCount?: number;  // Number of rooms
  wallCount?: number;  // Number of wall segments
  floorCount?: number; // Number of floor tiles
  lotSize?: { x: number; z: number }; // Size of the lot
  estimatedValue?: number; // Estimated value in simoleons
  objectTypes?: string[]; // Types of objects included (furniture, appliances, etc.)
  hasPool?: boolean;     // Whether the blueprint includes a pool
  hasBasement?: boolean; // Whether the blueprint includes a basement
  thumbnailIncluded?: boolean; // Whether a thumbnail is included
}

// Define the RoomManifestMetadata interface (ROOM - 0x370EFD6E)
export interface RoomManifestMetadata {
  instanceId: bigint;  // The instance ID
  roomCount: number;   // Number of rooms in the manifest
  roomTypes?: string[]; // Types of rooms (bedroom, bathroom, etc.)
  roomSizes?: { id: number; size: number }[]; // Sizes of each room
  moduleCount?: number; // Number of modules/sections
}

// Define the ShellInfoMetadata interface (_SGI - 0x56278554)
export interface ShellInfoMetadata {
  instanceId: bigint;  // The instance ID
  shellType?: number;  // Type of shell/structure
  shellSize?: { x: number; z: number }; // Size of the shell
  foundationHeight?: number; // Height of the foundation
  roofType?: number;   // Type of roof
  exteriorWallType?: number; // Type of exterior walls
  interiorWallType?: number; // Type of interior walls
}

// Define the WorldDefinitionMetadata interface
export interface WorldDefinitionMetadata {
  worldId: number;
  flags: number;
  offsets: number[];
}

// Define the WorldGeometryMetadata interface
export interface WorldGeometryMetadata {
  hasTerrainMesh: boolean;
  sizeInBytes: number;
  // Add other relevant metadata if easily extractable
}
