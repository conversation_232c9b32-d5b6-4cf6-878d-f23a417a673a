﻿﻿﻿﻿import { ResourceTypeResolver } from '../types/resource/resolver.js'; // Added .js

/**
 * Formats bytes into a human-readable string
 */
export function formatBytes(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * Formats a resource type into a human-readable string using the resolver
 */
export function formatResourceType(type: number): string {
  // TODO: Inject or import a concrete implementation of ResourceTypeResolver
  // const resolver = ResourceTypeResolverImpl.getInstance(); // Example
  // const metadata = resolver.getCachedMetadata(type); // Example

  // if (metadata) {
  //   return `${metadata.name} (0x${type.toString(16).toUpperCase()})`;
  // }

  // Fallback formatting without resolver
  return `Type (0x${type.toString(16).toUpperCase()})`;
}
