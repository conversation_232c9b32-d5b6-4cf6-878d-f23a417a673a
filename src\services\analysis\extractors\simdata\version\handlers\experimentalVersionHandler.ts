/**
 * <PERSON><PERSON> for experimental SimData versions (65280, 65290, etc.)
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { ParsedSimData, VersionHandlerFunction } from '../types.js';
import { parseGenericVersion } from '../../parsers/genericVersionParser.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('ExperimentalVersionHandler');

/**
 * Creates a handler function for an experimental SimData version
 * @param version SimData version
 * @returns Handler function for the specified version
 */
export function createExperimentalVersionHandler(version: number): VersionHandlerFunction {
    return (buffer: Buffer): ParsedSimData | undefined => {
        try {
            logger.info(`Handling experimental SimData version ${version}`);
            return parseGenericVersion(buffer);
        } catch (error) {
            return handleVersionError(
                error,
                createVersionErrorContext(version, 'experimentalVersionHandler', { bufferLength: buffer.length }),
                undefined
            );
        }
    };
}

/**
 * Get all experimental version handlers (65280, 65290, etc.)
 * @returns Map of version numbers to handler functions
 */
export function getExperimentalVersionHandlers(): Map<number, VersionHandlerFunction> {
    const handlers = new Map<number, VersionHandlerFunction>();
    
    // Experimental versions (possibly negative values converted to unsigned)
    const experimentalVersions = [65280, 65290, 65500, 65535];
    
    for (const version of experimentalVersions) {
        handlers.set(version, createExperimentalVersionHandler(version));
    }
    
    return handlers;
}
