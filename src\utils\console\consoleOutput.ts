/**
 * Console Output Utility
 * 
 * This module provides utilities for managing console output in both CLI and Electron environments.
 * It allows for redirecting console output, capturing logs, and restoring the original console methods.
 */

import { EventEmitter } from 'events';
import { setMaxListeners } from '../eventEmitterConfig.js';
import { Logger } from '../logging/logger.js';

// Create a logger for this module
const logger = new Logger('ConsoleOutput');

// Create an event emitter for console output events
const consoleEmitter = new EventEmitter();
setMaxListeners(consoleEmitter, 30);

// Store original console methods
const originalConsole = {
    log: console.log,
    info: console.info,
    warn: console.warn,
    error: console.error,
    debug: console.debug
};

// Track if console is currently redirected
let isRedirected = false;

// Buffer for captured output
const capturedOutput = {
    logs: [] as string[],
    errors: [] as string[]
};

/**
 * Redirect console output to use process.stdout.write
 * This is useful for CLI tools and test scripts
 * 
 * @param options Configuration options
 */
export function redirectConsoleOutput(options: {
    useProcessStdout?: boolean;
    captureOutput?: boolean;
    emitEvents?: boolean;
    prefix?: string;
} = {}) {
    if (isRedirected) {
        logger.warn('Console output is already redirected. Call restoreConsole() first.');
        return;
    }

    const {
        useProcessStdout = true,
        captureOutput = false,
        emitEvents = false,
        prefix = ''
    } = options;

    // Clear captured output
    capturedOutput.logs = [];
    capturedOutput.errors = [];

    // Override console methods
    console.log = function(...args) {
        const message = args.join(' ');
        
        // Capture output if requested
        if (captureOutput) {
            capturedOutput.logs.push(message);
        }
        
        // Emit event if requested
        if (emitEvents) {
            consoleEmitter.emit('log', message);
        }
        
        // Output to process.stdout if requested
        if (useProcessStdout) {
            process.stdout.write(`${prefix}${message}\n`);
        } else {
            originalConsole.log(`${prefix}${message}`);
        }
    };

    console.info = function(...args) {
        const message = args.join(' ');
        
        if (captureOutput) {
            capturedOutput.logs.push(message);
        }
        
        if (emitEvents) {
            consoleEmitter.emit('info', message);
        }
        
        if (useProcessStdout) {
            process.stdout.write(`${prefix}INFO: ${message}\n`);
        } else {
            originalConsole.info(`${prefix}${message}`);
        }
    };

    console.warn = function(...args) {
        const message = args.join(' ');
        
        if (captureOutput) {
            capturedOutput.logs.push(message);
        }
        
        if (emitEvents) {
            consoleEmitter.emit('warn', message);
        }
        
        if (useProcessStdout) {
            process.stdout.write(`${prefix}WARN: ${message}\n`);
        } else {
            originalConsole.warn(`${prefix}${message}`);
        }
    };

    console.error = function(...args) {
        const message = args.join(' ');
        
        if (captureOutput) {
            capturedOutput.errors.push(message);
        }
        
        if (emitEvents) {
            consoleEmitter.emit('error', message);
        }
        
        if (useProcessStdout) {
            process.stderr.write(`${prefix}ERROR: ${message}\n`);
        } else {
            originalConsole.error(`${prefix}${message}`);
        }
    };

    console.debug = function(...args) {
        const message = args.join(' ');
        
        if (captureOutput) {
            capturedOutput.logs.push(message);
        }
        
        if (emitEvents) {
            consoleEmitter.emit('debug', message);
        }
        
        if (useProcessStdout) {
            process.stdout.write(`${prefix}DEBUG: ${message}\n`);
        } else {
            originalConsole.debug(`${prefix}${message}`);
        }
    };

    isRedirected = true;
    logger.debug('Console output has been redirected');
}

/**
 * Restore original console methods
 */
export function restoreConsole() {
    if (!isRedirected) {
        return;
    }

    console.log = originalConsole.log;
    console.info = originalConsole.info;
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
    console.debug = originalConsole.debug;

    isRedirected = false;
    logger.debug('Console output has been restored');
}

/**
 * Get captured console output
 */
export function getCapturedOutput() {
    return {
        logs: [...capturedOutput.logs],
        errors: [...capturedOutput.errors]
    };
}

/**
 * Clear captured console output
 */
export function clearCapturedOutput() {
    capturedOutput.logs = [];
    capturedOutput.errors = [];
}

/**
 * Subscribe to console output events
 * @param event Event type ('log', 'info', 'warn', 'error', 'debug')
 * @param listener Callback function
 */
export function subscribeToConsoleOutput(
    event: 'log' | 'info' | 'warn' | 'error' | 'debug',
    listener: (message: string) => void
) {
    consoleEmitter.on(event, listener);
    return () => consoleEmitter.off(event, listener); // Return unsubscribe function
}

/**
 * Create a simple print function for CLI tools
 * @param prefix Optional prefix for all output
 * @returns A function that writes to stdout
 */
export function createPrintFunction(prefix: string = '') {
    return function print(message: string) {
        process.stdout.write(`${prefix}${message}\n`);
    };
}
