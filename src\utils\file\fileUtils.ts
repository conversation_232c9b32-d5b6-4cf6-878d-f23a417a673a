import { promises as fs } from 'fs'; // Revert node: prefix
import path from 'path'; // Revert node: prefix
import { createHash } from 'crypto'; // Revert node: prefix
import { Logger } from '../logging/logger.js'; // Fixed path

const logger = new Logger('FileUtils');
const MAX_FILE_SIZE = 100 * 1024 * 1024; // Consider making this configurable
const SUPPORTED_EXTENSIONS = ['.package', '.script', '.dds', '.png', '.xml']; // Keep consistent

/**
 * Calculates the SHA256 hash of a file.
 */
export async function calculateFileHash(filePath: string): Promise<string> {
    try {
        const buffer = await fs.readFile(filePath);
        return createHash('sha256').update(buffer).digest('hex');
    } catch (error: any) {
        logger.error(`Error calculating hash for ${filePath}: ${error.message || error}`);
        throw new Error(`Failed to calculate hash for ${filePath}`);
    }
}

/**
 * Performs basic file validation (size, extension).
 * Does not validate package format itself.
 */
export async function validateFileMetadata(filePath: string): Promise<void> {
     try {
        const stats = await fs.stat(filePath);
        if (stats.size > MAX_FILE_SIZE) {
            throw new Error(`File ${filePath} exceeds maximum size of ${MAX_FILE_SIZE} bytes`);
        }
        const ext = path.extname(filePath).toLowerCase();
        // Allow any extension if not specifically validating packages? Or keep strict?
        // Keeping strict for now based on original code.
        if (!SUPPORTED_EXTENSIONS.includes(ext)) {
            throw new Error(`Unsupported file extension: ${ext}`);
        }
    } catch (error: any) {
         logger.error(`File metadata validation failed for ${filePath}: ${error.message || error}`);
         // Re-throw specific errors or a generic one
         if ((error as NodeJS.ErrnoException)?.code === 'ENOENT') {
             throw new Error(`File not found: ${filePath}`);
         }
         throw new Error(`Failed to validate file metadata for ${filePath}: ${error.message || 'Unknown error'}`);
    }
}
