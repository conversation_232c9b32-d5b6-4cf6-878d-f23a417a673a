/**
 * Package Analysis Worker
 * 
 * Worker thread implementation for parallel package analysis.
 * This worker handles individual package analysis tasks in isolation,
 * providing memory efficiency and fault tolerance.
 * 
 * Features:
 * - Independent S4TK module loading per worker
 * - Isolated memory space for package operations
 * - Database connection pooling support
 * - Comprehensive error handling and recovery
 */

import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../../services/databaseService.js';
import { PackageAnalyzer } from '../../services/analysis/packageAnalyzer.js';
import { ResourceMetadataExtractor } from '../../services/analysis/package/resourceMetadataExtractor.js';
import { PackageLoader } from '../../services/analysis/package/packageLoader.js';
import { ResourceProcessor } from '../../services/analysis/package/resourceProcessor.js';
import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker } from '../../utils/memory/resourceTracker.js';
import { formatBytes, formatDuration } from '../../utils/formatting/formatUtils.js';
import { initializeS4TKPlugins } from '../../utils/s4tk/s4tkPluginManager.js';
import { configureEventEmitter } from '../../utils/eventEmitterConfig.js';
import { applyMonkeyPatches } from '../../utils/monkeyPatch.js';
import * as path from 'path';

// Worker-specific logger
const logger = new Logger(`PackageAnalysisWorker-${process.pid}`);

/**
 * Worker task interface
 */
interface WorkerTask {
    task: {
        id: string;
        packagePath: string;
        options: {
            batchSize?: number;
            maxConcurrentResources?: number;
            analyzeDependencies?: boolean;
            includeGameplayAnalysis?: boolean;
            trackPerformance?: boolean;
        };
    };
}

/**
 * Worker result interface
 */
interface WorkerResult {
    taskId: string;
    success: boolean;
    result?: any;
    error?: string;
    performance: {
        duration: number;
        memoryUsed: number;
        resourcesProcessed: number;
    };
}

/**
 * Worker state
 */
class WorkerState {
    private memoryManager!: EnhancedMemoryManager;
    private resourceTracker!: ResourceTracker;
    private databaseService!: DatabaseService;
    private packageAnalyzer!: PackageAnalyzer;
    private packageLoader!: PackageLoader;
    private resourceProcessor!: ResourceProcessor;
    private metadataExtractor!: ResourceMetadataExtractor;
    private isInitialized = false;
    private tasksProcessed = 0;
    private totalProcessingTime = 0;

    /**
     * Initialize worker components
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        try {
            logger.info('Initializing PackageAnalysisWorker...');

            // Apply monkey patches and configure event emitters
            applyMonkeyPatches();
            configureEventEmitter();

            // Initialize S4TK plugins
            await initializeS4TKPlugins();
            logger.info('S4TK plugins initialized in worker');

            // Initialize memory management
            this.memoryManager = EnhancedMemoryManager.getInstance({
                thresholds: {
                    warning: 150 * 1024 * 1024,  // 150MB warning threshold per worker
                    critical: 180 * 1024 * 1024, // 180MB critical threshold per worker
                    emergency: 200 * 1024 * 1024 // 200MB emergency threshold per worker
                },
                autoGcEnabled: true,
                trackingEnabled: true,
                trackingIntervalMs: 10000, // Check every 10 seconds
                detailedTracking: false, // Reduce overhead in workers
                logLevel: 'warn' // Reduce logging in workers
            });

            this.resourceTracker = ResourceTracker.getInstance();
            
            // Initialize memory manager
            this.memoryManager.initialize();

            // Initialize database service with worker-specific connection
            this.databaseService = new DatabaseService(':memory:'); // Use in-memory DB for workers
            await this.databaseService.initialize();

            // Use the factory to create a properly configured package analyzer
            const { createPackageAnalyzer } = await import('../../services/analysis/packageAnalyzerFactory.js');
            this.packageAnalyzer = createPackageAnalyzer({
                databaseService: this.databaseService
            });

            this.isInitialized = true;
            logger.info('PackageAnalysisWorker initialized successfully');

        } catch (error) {
            logger.error('Failed to initialize PackageAnalysisWorker:', error);
            throw error;
        }
    }

    /**
     * Process a package analysis task
     */
    async processTask(task: WorkerTask['task']): Promise<WorkerResult> {
        const startTime = Date.now();
        const startMemory = process.memoryUsage().heapUsed;

        try {
            logger.debug(`Processing task ${task.id} for package: ${path.basename(task.packagePath)}`);

            // Ensure worker is initialized
            if (!this.isInitialized) {
                await this.initialize();
            }

            // Check memory pressure before processing
            const memoryPressure = this.memoryManager.getMemoryPressure();
            if (memoryPressure > 0.8) {
                logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%) before processing task ${task.id}`);
                // Force garbage collection
                if (global.gc) {
                    global.gc();
                }
            }

            // Process the package
            const analysisResult = await this.packageAnalyzer.analyzePackage(
                task.packagePath,
                {
                    batchSize: task.options.batchSize || 100,
                    maxConcurrentResources: task.options.maxConcurrentResources || 5,
                    analyzeDependencies: task.options.analyzeDependencies || false,
                    includeGameplayAnalysis: task.options.includeGameplayAnalysis || false,
                    trackPerformance: task.options.trackPerformance || true,
                    cleanupBuffers: true // Always cleanup in workers
                }
            );

            // Calculate performance metrics
            const endTime = Date.now();
            const duration = endTime - startTime;
            const endMemory = process.memoryUsage().heapUsed;
            const memoryUsed = endMemory - startMemory;

            // Update worker stats
            this.tasksProcessed++;
            this.totalProcessingTime += duration;

            // Force cleanup after processing
            await this.cleanup();

            const result: WorkerResult = {
                taskId: task.id,
                success: true,
                result: analysisResult,
                performance: {
                    duration,
                    memoryUsed,
                    resourcesProcessed: analysisResult.resources?.length || 0
                }
            };

            logger.debug(`Task ${task.id} completed in ${formatDuration(duration)}, memory used: ${formatBytes(memoryUsed)}`);
            return result;

        } catch (error: any) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            const endMemory = process.memoryUsage().heapUsed;
            const memoryUsed = endMemory - startMemory;

            logger.error(`Task ${task.id} failed:`, error);

            // Force cleanup after error
            await this.cleanup();

            return {
                taskId: task.id,
                success: false,
                error: error.message || 'Unknown error',
                performance: {
                    duration,
                    memoryUsed,
                    resourcesProcessed: 0
                }
            };
        }
    }

    /**
     * Cleanup worker resources
     */
    async cleanup(): Promise<void> {
        try {
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }

            // Clean up resource tracker
            if (this.resourceTracker) {
                // Resource tracker cleanup - force garbage collection instead
                // Note: ResourceTracker doesn't have a cleanup method
            }

            // Log memory usage
            const memoryUsage = process.memoryUsage();
            const memoryPressure = this.memoryManager?.getMemoryPressure() || 0;
            
            if (memoryPressure > 0.7) {
                logger.warn(`Worker memory pressure: ${(memoryPressure * 100).toFixed(1)}%, heap: ${formatBytes(memoryUsage.heapUsed)}`);
            }

        } catch (error) {
            logger.error('Error during worker cleanup:', error);
        }
    }

    /**
     * Get worker statistics
     */
    getStats() {
        return {
            tasksProcessed: this.tasksProcessed,
            totalProcessingTime: this.totalProcessingTime,
            averageTaskTime: this.tasksProcessed > 0 ? this.totalProcessingTime / this.tasksProcessed : 0,
            memoryUsage: process.memoryUsage(),
            memoryPressure: this.memoryManager?.getMemoryPressure() || 0
        };
    }

    /**
     * Shutdown worker
     */
    async shutdown(): Promise<void> {
        logger.info('Shutting down PackageAnalysisWorker...');

        try {
            // Cleanup resources
            await this.cleanup();

            // Close database connection
            if (this.databaseService) {
                await this.databaseService.close();
            }

            // Log final stats
            const stats = this.getStats();
            logger.info(`Worker processed ${stats.tasksProcessed} tasks in ${formatDuration(stats.totalProcessingTime)}`);
            logger.info(`Average task time: ${formatDuration(stats.averageTaskTime)}`);

        } catch (error) {
            logger.error('Error during worker shutdown:', error);
        }
    }
}

// Worker execution logic
if (!isMainThread) {
    const workerState = new WorkerState();

    // Handle incoming messages
    parentPort?.on('message', async (data: WorkerTask) => {
        try {
            const result = await workerState.processTask(data.task);
            parentPort?.postMessage({
                taskId: data.task.id,
                result: result.result,
                success: result.success,
                error: result.error,
                performance: result.performance,
                status: result.success ? 'completed' : 'failed'
            });
        } catch (error: any) {
            parentPort?.postMessage({
                taskId: data.task.id,
                error: error.message || 'Worker execution error',
                success: false,
                status: 'failed',
                performance: {
                    duration: 0,
                    memoryUsed: 0,
                    resourcesProcessed: 0
                }
            });
        }
    });

    // Handle worker shutdown
    parentPort?.on('close', async () => {
        await workerState.shutdown();
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', async (error) => {
        logger.error('Uncaught exception in worker:', error);
        await workerState.cleanup();
        process.exit(1);
    });

    // Handle unhandled rejections
    process.on('unhandledRejection', async (reason, promise) => {
        logger.error('Unhandled rejection in worker:', reason);
        await workerState.cleanup();
    });

    logger.info('PackageAnalysisWorker ready for tasks');
}

export type { WorkerState, WorkerTask, WorkerResult };