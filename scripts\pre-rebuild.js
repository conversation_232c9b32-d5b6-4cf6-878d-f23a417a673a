const fs = require('fs');
const path = require('path');

const nodeModulesPath = path.resolve(__dirname, '../node_modules');
const originalPath = path.join(nodeModulesPath, 'bufferfromfile');
const disabledPath = path.join(nodeModulesPath, 'bufferfromfile_DISABLED');

console.log('Running pre-rebuild step...');
try {
  if (fs.existsSync(originalPath)) {
    fs.renameSync(originalPath, disabledPath);
    console.log(`Temporarily renamed ${originalPath} to ${disabledPath}`);
  } else {
    console.log(`${originalPath} not found, skipping rename.`);
  }
  console.log('Pre-rebuild step completed.');
} catch (error) {
  console.error('Error during pre-rebuild step:', error);
  // Don't exit with error, allow rebuild to continue if possible
}