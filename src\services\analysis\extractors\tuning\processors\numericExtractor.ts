import { Logger } from '../../../../../utils/logging/logger.js';
import { TuningNumericResult } from '../types.js';
import { getNodeTextContent, getChildElements } from '../utils/xmlUtils.js';

/**
 * Extracts numeric values from a tuning resource
 * @param rootElement The root element
 * @param log The logger instance
 * @returns The extracted numeric values
 */
export function extractNumericValues(
    rootElement: any,
    log: Logger
): TuningNumericResult {
    const result: TuningNumericResult = {
        values: [],
        ranges: [],
        hasNegatives: false,
        hasDecimals: false
    };
    
    try {
        // Function to process an element recursively
        function processElement(element: any) {
            // Skip if not an object
            if (!element || typeof element !== 'object') return;
            
            // Check text content for numeric values
            const textContent = getNodeTextContent(element);
            if (textContent) {
                // Try to parse as number
                const numValue = parseFloat(textContent);
                if (!isNaN(numValue)) {
                    result.values.push(numValue);
                    
                    // Check for negative values
                    if (numValue < 0) {
                        result.hasNegatives = true;
                    }
                    
                    // Check for decimal values
                    if (numValue % 1 !== 0) {
                        result.hasDecimals = true;
                    }
                }
            }
            
            // Check for range elements
            if (element.tag === 'range' || element.name === 'range') {
                const minAttr = element.attributes?.min || element.$?.min;
                const maxAttr = element.attributes?.max || element.$?.max;
                
                if (minAttr !== undefined && maxAttr !== undefined) {
                    const min = parseFloat(minAttr);
                    const max = parseFloat(maxAttr);
                    
                    if (!isNaN(min) && !isNaN(max)) {
                        result.ranges.push({ min, max });
                        
                        // Check for negative values
                        if (min < 0 || max < 0) {
                            result.hasNegatives = true;
                        }
                        
                        // Check for decimal values
                        if (min % 1 !== 0 || max % 1 !== 0) {
                            result.hasDecimals = true;
                        }
                    }
                }
            }
            
            // Process children
            const children = getChildElements(element);
            for (const child of children) {
                processElement(child);
            }
        }
        
        // Start processing from the root element
        processElement(rootElement);
        
        // Sort values for easier analysis
        result.values.sort((a, b) => a - b);
        
        // Sort ranges by min value
        result.ranges.sort((a, b) => a.min - b.min);
        
        log.debug(`Extracted ${result.values.length} numeric values and ${result.ranges.length} ranges`);
        log.debug(`Has negative values: ${result.hasNegatives}, Has decimal values: ${result.hasDecimals}`);
    } catch (error: any) {
        log.error(`Error extracting numeric values: ${error.message || error}`);
    }
    
    return result;
}
