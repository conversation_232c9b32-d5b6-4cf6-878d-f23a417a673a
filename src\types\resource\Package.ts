﻿﻿/**
 * Package types
 *
 * This module contains types for package-related functionality.
 */

// Corrected imports
import { ResourceKey, ResourceMetadata } from './interfaces.js'; // Import from interfaces.js
import { BinaryResourceType } from './core.js'; // Use BinaryResourceType from core
// import { ResourceConflict } from '../conflict/ConflictTypes.js'; // Remove this import
import { ConflictResult } from '../conflict/ConflictTypes.js'; // Keep if used elsewhere
import { ConflictInfo } from '../conflict/index.js'; // Use this for conflicts

export enum ModCategory {
  SCRIPT = 'SCRIPT',
  TUNING = 'TUNING',
  ASSET = 'ASSET',
  DATA = 'DATA',
  OTHER = 'OTHER'
}

// Removed redundant ResourcePackageInfo interface

/**
 * Package metadata interface
 */
export interface PackageMetadata {
  name: string;
  path: string;
  size: number;
  hash: string;
  timestamp: number;
  resources: ResourceMetadata[];
  processedResources?: { resourceId: number; tgiString: string }[]; // For deferred override checks

  dependencies?: ResourceKey[];
  conflicts?: ConflictInfo[]; // Changed type to ConflictInfo[]
  type?: BinaryResourceType;
  version?: string;
  author?: string;
  description?: string;
  customValidators?: {
    [key: string]: (pkg: PackageMetadata) => boolean;
  };
}

export interface PackageOptions {
  validatePackage?: boolean;
  validateResources?: boolean;
  validateDependencies?: boolean;
  validateConflicts?: boolean;
  skipValidation?: string[];
  customValidation?: {
    [key: string]: (pkg: unknown) => boolean; // Renamed 'package' to 'pkg'
  };
}

// Removed duplicate PackageAnalysisResult interface definition.
// The primary definition should be in src/types/analysis/PackageAnalysisResult.ts

/**
 * Package validation result interface
 */
export interface PackageValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  timestamp: number;
  metadata?: {
    validatedFields?: string[];
    skippedValidations?: string[];
    validationDuration?: number;
  };
  details?: {
    [key: string]: unknown;
  };
}

/**
 * Package stats interface
 */
export interface PackageStats {
  totalSize: number;
  compressedSize: number;
  compressionRatio: number;
  resourceCount: number;
  conflictCount: number;
  errorCount: number;
  warningCount: number;
  timestamp: number;
  metadata?: {
    [key: string]: unknown;
  };
}

/**
 * Package analysis options interface
 */
export interface PackageAnalysisOptions {
  analyzePackage?: boolean;
  analyzeResources?: boolean;
  analyzeDependencies?: boolean;
  analyzeConflicts?: boolean;
  skipAnalysis?: string[];
  customAnalysis?: {
    [key: string]: (pkg: unknown) => unknown;
  };
}

/**
 * Organized packages interface
 */
export interface OrganizedPackages {
  [ModCategory.SCRIPT]?: PackageMetadata[];
  [ModCategory.TUNING]?: PackageMetadata[];
  [ModCategory.ASSET]?: PackageMetadata[];
  [ModCategory.DATA]?: PackageMetadata[];
  [ModCategory.OTHER]?: PackageMetadata[];
}

/**
 * Package resource entry interface
 */
export interface PackageResourceEntry {
  key: ResourceKey;
  type: BinaryResourceType;
  name?: string;
  path?: string;
  metadata?: ResourceMetadata;
}

/**
 * Package resource map type
 */
export type PackageResourceMap = Map<string, PackageResourceEntry>;

/**
 * Package resource collection interface
 */
export interface PackageResourceCollection {
  entries: PackageResourceEntry[];
  map: PackageResourceMap;
  add(entry: PackageResourceEntry): void;
  get(key: string): PackageResourceEntry | undefined;
  has(key: string): boolean;
  remove(key: string): boolean;
  clear(): void;
  size(): number;
}
