/**
 * Utility functions for formatting values
 */

/**
 * Format bytes to a human-readable string
 * @param bytes Number of bytes
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted string (e.g., "1.5 KB", "2.3 MB")
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
}

/**
 * Format a duration in milliseconds to a human-readable string
 * @param ms Duration in milliseconds
 * @returns Formatted string (e.g., "2h 30m 45s", "45s 500ms")
 */
export function formatDuration(ms: number): string {
    if (ms < 0) return '0ms';
    
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    const remainingMs = ms % 1000;
    const remainingSeconds = seconds % 60;
    const remainingMinutes = minutes % 60;
    
    let result = '';
    
    if (hours > 0) {
        result += `${hours}h `;
    }
    
    if (hours > 0 || remainingMinutes > 0) {
        result += `${remainingMinutes}m `;
    }
    
    if (hours === 0) {
        if (remainingSeconds > 0 || (remainingMinutes === 0 && remainingMs === 0)) {
            result += `${remainingSeconds}s`;
        }
        
        if (remainingSeconds === 0 && remainingMs > 0) {
            result += ` ${remainingMs}ms`;
        }
    } else {
        result += `${remainingSeconds}s`;
    }
    
    return result.trim();
}

/**
 * Format a date to a human-readable string
 * @param date Date to format
 * @returns Formatted string (e.g., "2023-05-15 14:30:45")
 */
export function formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * Format a number with commas as thousands separators
 * @param num Number to format
 * @returns Formatted string (e.g., "1,234,567")
 */
export function formatNumber(num: number): string {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * Format a percentage
 * @param value Value to format as percentage
 * @param total Total value
 * @param decimals Number of decimal places (default: 1)
 * @returns Formatted string (e.g., "42.5%")
 */
export function formatPercentage(value: number, total: number, decimals: number = 1): string {
    if (total === 0) return '0%';
    
    const percentage = (value / total) * 100;
    return percentage.toFixed(decimals) + '%';
}

/**
 * Format a file size from bytes to the most appropriate unit
 * @param bytes File size in bytes
 * @returns Formatted string with appropriate unit
 */
export function formatFileSize(bytes: number): string {
    return formatBytes(bytes);
}

/**
 * Format a resource type name to be more readable
 * @param resourceType Resource type name (often in uppercase with underscores)
 * @returns Formatted string (e.g., "Object Definition" instead of "OBJECT_DEFINITION")
 */
export function formatResourceType(resourceType: string): string {
    if (!resourceType) return 'Unknown';
    
    // Replace underscores with spaces and convert to title case
    return resourceType
        .replace(/_/g, ' ')
        .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase());
}

/**
 * Format a hex value to a readable string
 * @param value Hex value (number)
 * @param padLength Length to pad to (default: 8)
 * @returns Formatted string (e.g., "0x0166038C")
 */
export function formatHex(value: number, padLength: number = 8): string {
    return '0x' + value.toString(16).toUpperCase().padStart(padLength, '0');
}

/**
 * Format a bigint to a readable hex string
 * @param value Bigint value
 * @param padLength Length to pad to (default: 16)
 * @returns Formatted string
 */
export function formatBigIntHex(value: bigint, padLength: number = 16): string {
    return '0x' + value.toString(16).toUpperCase().padStart(padLength, '0');
}
