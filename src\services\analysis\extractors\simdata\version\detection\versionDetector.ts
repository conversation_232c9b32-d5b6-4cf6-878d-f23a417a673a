/**
 * SimData version detection utilities
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { VersionDetectionResult, VersionCategory, getVersionCategory } from '../types.js';
import { validateSimDataFormat } from './formatValidator.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('VersionDetector');

/**
 * Detects the version of a SimData buffer
 * @param buffer SimData buffer
 * @returns SimData version or undefined if detection fails
 */
export function detectVersion(buffer: Buffer): number | undefined {
    try {
        if (buffer.length < 8) {
            logger.error('Buffer too small to contain version');
            return undefined;
        }

        // Read version from buffer
        const version = buffer.readUInt16LE(0);

        // Validate version
        if (version === 0) {
            logger.error('Invalid version: 0');
            return undefined;
        }

        // Check for S4TK SimData marker
        // S4TK SimData files start with "DATA"
        const possibleMarker = buffer.toString('utf8', 0, 4);
        if (possibleMarker === 'DATA') {
            // This is an S4TK SimData file, not a raw SimData buffer
            logger.error('Buffer appears to be an S4TK SimData file, not a raw SimData buffer');
            return undefined;
        }

        // Perform additional validation to ensure this is likely a SimData buffer
        const formatValid = validateSimDataFormat(buffer, version);
        if (!formatValid) {
            logger.warn(`Buffer does not appear to be a valid SimData buffer for version ${version}`);
        }

        return version;
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(undefined, 'detectVersion', { bufferLength: buffer.length }),
            undefined
        );
    }
}

/**
 * Detects the version of a SimData buffer with confidence level
 * @param buffer SimData buffer
 * @returns Version detection result or undefined if detection fails
 */
export function detectVersionWithConfidence(buffer: Buffer): VersionDetectionResult | undefined {
    try {
        const version = detectVersion(buffer);
        if (version === undefined) {
            return undefined;
        }

        // Determine confidence level based on version and format validation
        let confidence: 'high' | 'medium' | 'low' = 'medium';
        const formatValid = validateSimDataFormat(buffer, version);

        // Standard versions with valid format have high confidence
        if (getVersionCategory(version) === VersionCategory.STANDARD && formatValid) {
            confidence = 'high';
        }
        // Special versions with valid format have high confidence
        else if (getVersionCategory(version) === VersionCategory.SPECIAL && formatValid) {
            confidence = 'high';
        }
        // Mod versions with valid format have medium confidence
        else if (getVersionCategory(version) === VersionCategory.MOD && formatValid) {
            confidence = 'medium';
        }
        // Experimental versions or invalid format have low confidence
        else {
            confidence = 'low';
        }

        // Try to extract schema name
        let possibleSchema: string | undefined;
        try {
            if (version >= 1 && version <= 20 && buffer.length >= 6) {
                const schemaNameLength = buffer.readUInt16LE(4);
                if (schemaNameLength > 0 && schemaNameLength < 100 && buffer.length >= 6 + schemaNameLength) {
                    possibleSchema = buffer.toString('utf8', 6, 6 + schemaNameLength);
                }
            }
        } catch (e) {
            // Ignore errors when trying to extract schema name
        }

        return {
            version,
            confidence,
            formatValid,
            possibleSchema
        };
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(undefined, 'detectVersionWithConfidence', { bufferLength: buffer.length }),
            undefined
        );
    }
}
