import { Logger } from '../logging/logger.js';
import { ResourceCategory } from '../../types/resource/enums.js';
import BinaryResourceType from '@s4tk/models/lib/enums/binary-resources.js';
import { AUDIO_RESOURCE_TYPES } from '../../services/analysis/extractors/audio/index.js';
import { VFX_RESOURCE_TYPES } from '../../services/analysis/extractors/extractVfxMetadata.js';

// Define known resource type constants
const RESOURCE_TYPES = {
    StringTable: 0x0C772E27,
    SimData: 0x545AC67A,
    SimDataAlt: 0x6017E896,
    CasPart: 0x034AEECB,
    ObjectDefinition: 0x00B2D882,
    Tuning: 0x0166038C,
    TuningXml: 0x220557DA,
    ZoneModifier: 0x3C1D8799,
    CombinedTuning: 0x62ECC59,
    Footprint: 0x4D3DD5A6,
    BuildPart: 0xFC6EB1F7,
    Slot: 0xD3044521, // Updated from 0x00000002 based on research
    Light: 0x03B4C61D,
    TerrainGeometry: 0x5B282D45, // Terrain Geometry resource type
    ObjectCatalog: 0x3C1AF1F2, // Object Catalog resource type
    ModularPart: 0xBA856C78, // Modular Part resource type
    TerrainPaint: 0x0C1FE246, // Terrain Paint resource type
    LotDefinition: 0x12952634, // Lot Definition resource type (LDNB)
    LotObjectList: 0x91568FD8, // Lot Object List resource type (LOBJ)
    Blueprint: 0x3924DE26, // Blueprint resource type (_BPT) - Part of Lot Template
    RoomManifest: 0x370EFD6E, // Room Manifest resource type (ROOM) - Part of Lot Template
    ShellInfo: 0x56278554, // Shell Info resource type (_SGI) - Part of Lot Template
    // Terrain Paint resources are implemented using a combination of standard resource types
    TerrainPaintStringTable: 0x220557DA, // String Table resources for terrain paint names and descriptions
    TerrainPaintThumbnail: 0x3C2A8647, // Object Thumbnails for terrain paint swatches
    TerrainPaintTexture: 0x00B2D882, // Image resources for terrain paint textures
    TerrainPaintResource1: 0xEBCBB16C, // Additional resources related to terrain paint
    TerrainPaintResource2: 0x01D0E75D,  // Additional resources related to terrain paint

    // Animation resource types
    Animation: 0x6B20C4F3,           // Standard animation
    AnimationMap: 0xAC16FBEC,        // Animation map
    AnimationStateMachine: 0x8EAF13DE, // Animation state machine
    BoneDelta: 0x55867754,           // Bone delta
    AnimationAlt: 0x2C70ADF2,        // Alternative animation format
    AnimationStateMachineAlt: 0x1797309683, // Alternative animation state machine
    Rig: 0x033260E3,                 // Rig/skeleton
    Skeleton: 0xD382BF57,            // Skeleton

    // Material resource types
    Material: 0x0499A526,            // Material definition
    MaterialVariant: 0x0333406C,     // Material variant
    MaterialAlt: 0xAC16FBEC,         // Alternative material definition
    Texture: 0x0288B3A2,             // Texture definition
    Shader: 0x0354796A,              // Shader definition

    // VFX resource types
    VfxModifier: VFX_RESOURCE_TYPES.VFX_MODIFIER, // Visual effect modifier
    VfxState: VFX_RESOURCE_TYPES.VFX_STATE,       // Visual effect state

    // Audio resource types
    Sound: AUDIO_RESOURCE_TYPES.SOUND,
    SoundEffect: AUDIO_RESOURCE_TYPES.SOUND_EFFECT,
    SoundEffectAlt: AUDIO_RESOURCE_TYPES.SOUND_EFFECT_ALT,
    SoundEffectAlt2: AUDIO_RESOURCE_TYPES.SOUND_EFFECT_ALT2,
    SoundTrack: AUDIO_RESOURCE_TYPES.SOUND_TRACK,
    SoundData: AUDIO_RESOURCE_TYPES.SOUND_DATA,
    SoundBank: AUDIO_RESOURCE_TYPES.SOUND_BANK,
    HeaderlessSound: AUDIO_RESOURCE_TYPES.HEADERLESS_SOUND
};

const logger = new Logger('ResourceTypeRegistry');

/**
 * Information about a resource type, including its name, category, and which extractor to use
 */
export interface ResourceTypeInfo {
    name: string;
    category: ResourceCategory;
    extractor: string;
    description?: string;
    isCommon?: boolean;
}

/**
 * Registry for resource types that can be extended at runtime
 */
class ResourceTypeRegistry {
    private registry: Map<number, ResourceTypeInfo> = new Map();
    private initialized: boolean = false;

    /**
     * Initialize the registry with known resource types
     */
    initialize(): void {
        if (this.initialized) return;

        // Register all official S4TK binary resource types
        // Get all numeric keys from BinaryResourceType
        const typeIds = Object.keys(BinaryResourceType)
            .filter(key => !isNaN(Number(key)))
            .map(key => Number(key));

        // Register each resource type
        for (const typeId of typeIds) {
            // Get the type name from the enum (reverse lookup)
            const typeName = Object.keys(BinaryResourceType)
                .find(key => (BinaryResourceType as any)[key] === typeId) || `Type_0x${typeId.toString(16).toUpperCase().padStart(8, '0')}`;

            // Determine category and extractor based on type name
            let category = ResourceCategory.UNKNOWN;
            let extractor = 'extractGenericMetadata';
            let isCommon = false;

            if (typeName.includes('String') || typeName.includes('SimData') || typeName.includes('Tuning') || typeName.includes('NameMap')) {
                category = ResourceCategory.TUNING;
                extractor = 'extractTuningMetadata';
                isCommon = true;
            } else if (typeName.includes('Cas')) {
                category = ResourceCategory.CASPART;
                extractor = 'extractCasPartMetadata';
                isCommon = true;
            } else if (typeName.includes('Object')) {
                category = ResourceCategory.OBJECT;
                extractor = 'extractObjectMetadata';
                isCommon = true;
            } else if (typeName.includes('Image') || typeName.includes('Thumbnail')) {
                category = ResourceCategory.IMAGE;
                extractor = 'extractImageMetadata';
                isCommon = typeName.includes('Dds') || typeName.includes('Png') || typeName.includes('Rle2');
            } else if (typeName.includes('Model')) {
                category = ResourceCategory.MODEL;
                extractor = 'extractModelMetadata';
                isCommon = true;
            } else if (typeName.includes('Animation') || typeName.includes('Rig')) {
                category = ResourceCategory.ANIMATION;
                extractor = 'extractAnimationMetadata';
                isCommon = true;
            } else if (typeName.includes('Sound') || typeName.includes('Audio')) {
                category = ResourceCategory.SOUND;
                extractor = 'extractSoundMetadata';
                isCommon = false;
            } else if (typeName.includes('Region') || typeName.includes('World')) {
                category = ResourceCategory.WORLD;
                extractor = 'extractGenericMetadata';
                isCommon = false;
            } else if (typeName.includes('Font') || typeName.includes('UI')) {
                category = ResourceCategory.UI;
                extractor = 'extractGenericMetadata';
                isCommon = false;
            } else if (typeName.includes('Script')) {
                category = ResourceCategory.SCRIPT;
                extractor = 'extractScriptMetadata';
                isCommon = true;
            } else if (typeName.includes('Footprint')) {
                category = ResourceCategory.OBJECT;
                extractor = 'extractFootprintMetadata';
                isCommon = true;
            } else if (typeName.includes('BuildPart')) {
                category = ResourceCategory.OBJECT;
                extractor = 'extractBuildPartMetadata';
                isCommon = true;
            } else if (typeName.includes('Slot') || typeName.includes('Light')) {
                category = ResourceCategory.OBJECT;
                extractor = 'extractObjectMetadata';
                isCommon = false;
            } else if (typeName.includes('Sim')) {
                category = ResourceCategory.OBJECT;
                extractor = 'extractObjectMetadata';
                isCommon = false;
            } else if (typeName.includes('Tray')) {
                category = ResourceCategory.OBJECT;
                extractor = 'extractObjectMetadata';
                isCommon = false;
            }

            // Format the name for display
            const formattedName = typeName
                .replace(/([A-Z])/g, '_$1')
                .toUpperCase()
                .replace(/^_/, '');

            // Register the resource type
            this.register(typeId, {
                name: formattedName,
                category: category,
                extractor: extractor,
                isCommon: isCommon,
                description: `${typeName} resource type (0x${typeId.toString(16).toUpperCase().padStart(8, '0')})`
            });

            logger.debug(`Registered ${typeName} (0x${typeId.toString(16).toUpperCase().padStart(8, '0')}) as ${formattedName}`);
        }

        // Add some special cases with better descriptions
        this.register(RESOURCE_TYPES.StringTable, {
            name: 'STRING_TABLE',
            category: ResourceCategory.TUNING,
            extractor: 'extractStringTableMetadata',
            isCommon: true,
            description: 'String table resource containing localized text'
        });

        this.register(RESOURCE_TYPES.SimData, {
            name: 'SIMDATA',
            category: ResourceCategory.TUNING,
            extractor: 'extractSimDataMetadata',
            isCommon: true,
            description: 'SimData resource containing simulation data'
        });

        this.register(RESOURCE_TYPES.SimDataAlt, {
            name: 'SIMDATA',
            category: ResourceCategory.TUNING,
            extractor: 'extractSimDataMetadata',
            isCommon: true,
            description: 'Alternative SimData resource containing simulation data'
        });

        this.register(RESOURCE_TYPES.CasPart, {
            name: 'CASPART',
            category: ResourceCategory.CASPART,
            extractor: 'extractCasPartMetadata',
            isCommon: true,
            description: 'Create-A-Sim part resource'
        });

        this.register(RESOURCE_TYPES.ObjectDefinition, {
            name: 'OBJECT_DEFINITION',
            category: ResourceCategory.OBJECT,
            extractor: 'extractObjectMetadata',
            isCommon: true,
            description: 'Object definition resource'
        });

        // Add tuning resource types
        this.register(RESOURCE_TYPES.Tuning, {
            name: 'TUNING',
            category: ResourceCategory.TUNING,
            extractor: 'extractTuningXmlMetadata',
            isCommon: true,
            description: 'XML tuning resource containing game configuration data'
        });

        this.register(RESOURCE_TYPES.TuningXml, {
            name: 'TUNING_XML',
            category: ResourceCategory.TUNING,
            extractor: 'extractTuningXmlMetadata',
            isCommon: true,
            description: 'XML tuning resource containing game configuration data'
        });

        this.register(RESOURCE_TYPES.ZoneModifier, {
            name: 'ZONE_MODIFIER',
            category: ResourceCategory.TUNING,
            extractor: 'extractTuningXmlMetadata',
            isCommon: true,
            description: 'Zone modifier tuning resource'
        });

        this.register(RESOURCE_TYPES.CombinedTuning, {
            name: 'COMBINED_TUNING',
            category: ResourceCategory.TUNING,
            extractor: 'extractTuningXmlMetadata',
            isCommon: true,
            description: 'Combined tuning resource containing multiple tuning elements'
        });

        // Add Footprint resource type
        this.register(RESOURCE_TYPES.Footprint, {
            name: 'FOOTPRINT',
            category: ResourceCategory.OBJECT,
            extractor: 'extractFootprintMetadata',
            isCommon: true,
            description: 'Footprint resource defining the physical space that objects occupy'
        });

        // Add BuildPart resource type
        this.register(RESOURCE_TYPES.BuildPart, {
            name: 'BUILD_PART',
            category: ResourceCategory.OBJECT,
            extractor: 'extractBuildPartMetadata',
            isCommon: true,
            description: 'Build Part resource defining building components like walls, floors, roofs, etc.'
        });

        // Add Slot resource type
        this.register(RESOURCE_TYPES.Slot, {
            name: 'SLOT',
            category: ResourceCategory.OBJECT,
            extractor: 'extractSlotMetadata',
            isCommon: true,
            description: 'Slot resource defining attachment points on objects where other objects can be placed'
        });

        // Add Light resource type
        this.register(RESOURCE_TYPES.Light, {
            name: 'LIGHT',
            category: ResourceCategory.OBJECT,
            extractor: 'extractLightMetadata',
            isCommon: true,
            description: 'Light resource defining lighting properties for objects'
        });

        // Add Terrain Paint resource type
        this.register(RESOURCE_TYPES.TerrainPaint, {
            name: 'TERRAIN_PAINT',
            category: ResourceCategory.WORLD,
            extractor: 'extractTerrainPaintMetadata',
            isCommon: true,
            description: 'Terrain Paint resource defining textures and properties for terrain'
        });

        // Add Terrain Geometry resource type
        this.register(RESOURCE_TYPES.TerrainGeometry, {
            name: 'TERRAIN_GEOMETRY',
            category: ResourceCategory.WORLD,
            extractor: 'extractTerrainGeometryMetadata',
            isCommon: true,
            description: 'Terrain Geometry resource defining the shape and height of terrain'
        });

         // Add World Definition resource type
         this.register(0xF0633989, {
             name: 'WORLD_DEFINITION',
             category: ResourceCategory.WORLD,
             extractor: 'extractWorldDefinitionMetadata',
             isCommon: false, // World Definitions are in world files, not common in typical mods
             description: 'World Definition resource containing high-level metadata about a world'
         });

         // Add World Geometry resource type
         this.register(0xAE39399F, {
             name: 'WORLD_GEOMETRY',
             category: ResourceCategory.WORLD,
             extractor: 'extractWorldGeometryMetadata',
             isCommon: false, // World Geometry is in world files, not common in typical mods
             description: 'World Geometry resource containing terrain mesh and other environmental geometry'
         });

         // Add Object Catalog resource type
         this.register(RESOURCE_TYPES.ObjectCatalog, {
            name: 'OBJECT_CATALOG',
            category: ResourceCategory.GAMEPLAY,
            extractor: 'extractObjectCatalogMetadata',
            isCommon: true,
            description: 'Object Catalog resource containing information about objects available in the game\'s catalog/build mode'
        });

        // Add Modular Part resource type
        this.register(RESOURCE_TYPES.ModularPart, {
            name: 'MODULAR_PART',
            category: ResourceCategory.OBJECT,
            extractor: 'extractModularPartMetadata',
            isCommon: true,
            description: 'Modular Part resource defining parts that can be combined to create modular objects in the game'
        });

        // Add Lot Definition resource type
        this.register(RESOURCE_TYPES.LotDefinition, {
            name: 'LOT_DEFINITION',
            category: ResourceCategory.WORLD,
            extractor: 'extractLotDefinitionMetadata',
            isCommon: false,
            description: 'Lot Definition (LDNB) resource containing metadata about a lot within a world file'
        });

        // Add Lot Object List resource type
        this.register(RESOURCE_TYPES.LotObjectList, {
            name: 'LOT_OBJECT_LIST',
            category: ResourceCategory.WORLD,
            extractor: 'extractLotObjectListMetadata',
            isCommon: false,
            description: 'Lot Object List (LOBJ) resource containing objects placed on a lot but outside the editable footprint'
        });

        // Add Blueprint resource type (part of Lot Template)
        this.register(RESOURCE_TYPES.Blueprint, {
            name: 'BLUEPRINT',
            category: ResourceCategory.OBJECT,
            extractor: 'extractBlueprintMetadata',
            isCommon: false,
            description: 'Blueprint (_BPT) resource containing objects and transforms for a lot template'
        });

        // Add Room Manifest resource type (part of Lot Template)
        this.register(RESOURCE_TYPES.RoomManifest, {
            name: 'ROOM_MANIFEST',
            category: ResourceCategory.OBJECT,
            extractor: 'extractRoomManifestMetadata',
            isCommon: false,
            description: 'Room Manifest (ROOM) resource containing room definitions for a lot template'
        });

        // Add Shell Info resource type (part of Lot Template)
        this.register(RESOURCE_TYPES.ShellInfo, {
            name: 'SHELL_INFO',
            category: ResourceCategory.OBJECT,
            extractor: 'extractShellInfoMetadata',
            isCommon: false,
            description: 'Shell Info (_SGI) resource containing household shell information for a lot template'
        });

        // Register animation resource types
        this.register(RESOURCE_TYPES.Animation, {
            name: 'ANIMATION',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: true,
            description: 'Animation resource containing animation data'
        });

        this.register(RESOURCE_TYPES.AnimationMap, {
            name: 'ANIMATION_MAP',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: true,
            description: 'Animation map resource containing animation references'
        });

        this.register(RESOURCE_TYPES.AnimationStateMachine, {
            name: 'ANIMATION_STATE_MACHINE',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: true,
            description: 'Animation state machine resource defining animation transitions'
        });

        this.register(RESOURCE_TYPES.BoneDelta, {
            name: 'BONE_DELTA',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: false,
            description: 'Bone delta resource containing bone transformation data'
        });

        this.register(RESOURCE_TYPES.AnimationAlt, {
            name: 'ANIMATION_ALT',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: false,
            description: 'Alternative animation resource format'
        });

        this.register(RESOURCE_TYPES.AnimationStateMachineAlt, {
            name: 'ANIMATION_STATE_MACHINE_ALT',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: false,
            description: 'Alternative animation state machine resource format'
        });

        this.register(RESOURCE_TYPES.Rig, {
            name: 'RIG',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: false,
            description: 'Rig resource defining skeleton structure for animations'
        });

        this.register(RESOURCE_TYPES.Skeleton, {
            name: 'SKELETON',
            category: ResourceCategory.ANIMATION,
            extractor: 'extractAnimationMetadata',
            isCommon: false,
            description: 'Skeleton resource defining bone structure for animations'
        });

        // Register material resource types
        this.register(RESOURCE_TYPES.Material, {
            name: 'MATERIAL_DEFINITION',
            category: ResourceCategory.VISUAL,
            extractor: 'extractMaterialMetadata',
            isCommon: true,
            description: 'Material definition resource defining visual properties for 3D models'
        });

        this.register(RESOURCE_TYPES.MaterialVariant, {
            name: 'MATERIAL_VARIANT',
            category: ResourceCategory.VISUAL,
            extractor: 'extractMaterialMetadata',
            isCommon: false,
            description: 'Material variant resource defining variations of materials'
        });

        this.register(RESOURCE_TYPES.MaterialAlt, {
            name: 'MATERIAL_DEFINITION_ALT',
            category: ResourceCategory.VISUAL,
            extractor: 'extractMaterialMetadata',
            isCommon: false,
            description: 'Alternative material definition format'
        });

        this.register(RESOURCE_TYPES.Texture, {
            name: 'TEXTURE_DEFINITION',
            category: ResourceCategory.VISUAL,
            extractor: 'extractMaterialMetadata',
            isCommon: true,
            description: 'Texture definition resource defining texture properties'
        });

        this.register(RESOURCE_TYPES.Shader, {
            name: 'SHADER_DEFINITION',
            category: ResourceCategory.VISUAL,
            extractor: 'extractMaterialMetadata',
            isCommon: false,
            description: 'Shader definition resource defining shader properties'
        });

        // Register audio resource types
        this.register(RESOURCE_TYPES.Sound, {
            name: 'SOUND',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Sound resource containing audio data'
        });

        this.register(RESOURCE_TYPES.SoundEffect, {
            name: 'SOUND_EFFECT',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Sound effect resource containing short audio clips'
        });

        this.register(RESOURCE_TYPES.SoundEffectAlt, {
            name: 'SOUND_EFFECT_ALT',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Alternative sound effect resource'
        });

        this.register(RESOURCE_TYPES.SoundEffectAlt2, {
            name: 'SOUND_EFFECT_ALT2',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Second alternative sound effect resource'
        });

        this.register(RESOURCE_TYPES.SoundTrack, {
            name: 'SOUND_TRACK',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Sound track resource containing music'
        });

        this.register(RESOURCE_TYPES.SoundData, {
            name: 'SOUND_DATA',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Sound data resource containing audio configuration'
        });

        this.register(RESOURCE_TYPES.SoundBank, {
            name: 'SOUND_BANK',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Sound bank resource containing multiple sound resources'
        });

        this.register(RESOURCE_TYPES.HeaderlessSound, {
            name: 'HEADERLESS_SOUND',
            category: ResourceCategory.SOUND,
            extractor: 'extractSoundMetadata',
            isCommon: true,
            description: 'Headerless sound resource used for certain audio formats'
        });

        // Register VFX resource types
        this.register(RESOURCE_TYPES.VfxModifier, {
            name: 'VFX_MODIFIER',
            category: ResourceCategory.EFFECT,
            extractor: 'extractVfxMetadata',
            isCommon: true,
            description: 'Visual effect modifier resource defining effect parameters and animations'
        });

        this.register(RESOURCE_TYPES.VfxState, {
            name: 'VFX_STATE',
            category: ResourceCategory.EFFECT,
            extractor: 'extractVfxMetadata',
            isCommon: true,
            description: 'Visual effect state resource defining effect states and particle systems'
        });

        // Fallback for unknown resource types
        this.register(0, {
            name: 'UNKNOWN',
            category: ResourceCategory.UNKNOWN,
            extractor: 'extractGenericMetadata',
            isCommon: false,
            description: 'Unknown resource type'
        });

        this.initialized = true;
        logger.info(`ResourceTypeRegistry initialized with ${this.registry.size} entries from official S4TK BinaryResourceType.`);
    }

    /**
     * Register a new resource type
     * @param typeId The numeric resource type ID
     * @param info Information about the resource type
     */
    register(typeId: number, info: ResourceTypeInfo): void {
        this.registry.set(typeId, info);
    }

    /**
     * Get information about a resource type
     * @param typeId The numeric resource type ID
     * @returns Information about the resource type, or the UNKNOWN type if not found
     */
    getInfo(typeId: number): ResourceTypeInfo {
        if (!this.initialized) this.initialize();

        const info = this.registry.get(typeId);
        if (info) return info;

        // If not found, return a dynamically created info with a hex name
        const hexId = typeId.toString(16).toUpperCase().padStart(8, '0');
        const dynamicInfo: ResourceTypeInfo = {
            name: `TYPE_0x${hexId}`,
            category: ResourceCategory.UNKNOWN,
            extractor: 'extractGenericMetadata',
            isCommon: false,
            description: `Unknown resource type 0x${hexId} - This resource type is not officially supported by S4TK`
        };

        // Register this type for future use
        this.register(typeId, dynamicInfo);
        logger.debug(`Registered unknown resource type 0x${hexId}`);

        return dynamicInfo;
    }

    /**
     * Get the appropriate extractor function name for a given resource type
     * @param typeId The numeric resource type ID
     * @returns The name of the extractor function to use
     */
    getExtractor(typeId: number): string {
        return this.getInfo(typeId).extractor;
    }

    /**
     * Get the name of a resource type
     * @param typeId The numeric resource type ID
     * @returns The name of the resource type
     */
    getName(typeId: number): string {
        return this.getInfo(typeId).name;
    }

    /**
     * Get the category of a resource type
     * @param typeId The numeric resource type ID
     * @returns The category of the resource type
     */
    getCategory(typeId: number): ResourceCategory {
        return this.getInfo(typeId).category;
    }

    /**
     * Get all registered resource types
     * @returns A map of all registered resource types
     */
    getAllTypes(): Map<number, ResourceTypeInfo> {
        if (!this.initialized) this.initialize();
        return new Map(this.registry);
    }

    /**
     * Get all common resource types
     * @returns A map of common resource types
     */
    getCommonTypes(): Map<number, ResourceTypeInfo> {
        if (!this.initialized) this.initialize();

        const commonTypes = new Map<number, ResourceTypeInfo>();
        this.registry.forEach((info, typeId) => {
            if (info.isCommon) {
                commonTypes.set(typeId, info);
            }
        });

        return commonTypes;
    }

    /**
     * Dispose of resources used by the registry
     */
    dispose(): Promise<void> {
        return new Promise<void>((resolve) => {
            // Clear the registry
            this.registry.clear();
            this.initialized = false;

            // Log the disposal
            const logger = new Logger('ResourceTypeRegistry');
            logger.info('ResourceTypeRegistry disposed');

            resolve();
        });
    }
}

// Singleton instance
export const resourceTypeRegistry = new ResourceTypeRegistry();

// Initialize the registry
resourceTypeRegistry.initialize();
