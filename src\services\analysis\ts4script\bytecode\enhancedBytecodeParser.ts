/**
 * Enhanced Bytecode Parser
 *
 * This module provides enhanced bytecode parsing capabilities for handling non-standard
 * and corrupted Python bytecode files commonly found in Sims 4 mods.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { BytecodeParserBase } from './bytecodeParserBase.js';
import { CodeObject, BytecodeHeader } from './types.js';
import { SafeBufferReader } from './safeBufferReader.js';
import * as path from 'path';
import { extractStringLiterals } from './utils.js';
import { injectable, singleton } from '../../../di/decorators.js';

/**
 * Enhanced bytecode parser with robust error handling and recovery strategies
 */
@singleton()
@injectable()
export class EnhancedBytecodeParser extends BytecodeParserBase {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.logger = new Logger('EnhancedBytecodeParser');
    }

    /**
     * Parse non-standard bytecode
     * @param buffer Bytecode buffer
     * @param filename Filename
     * @returns Parsed bytecode or null if parsing fails
     */
    public parseNonStandardBytecode(buffer: Buffer, filename: string): CodeObject | null {
        try {
            this.logger.debug(`Attempting to parse non-standard bytecode for ${filename}`);

            // Try multiple parsing strategies in order of likelihood
            const strategies = [
                { name: "EA Python 3.7 format", offset: 16, magicBytes: [0x0A, 0x0D, 0x0D, 0x0A] },
                { name: "EA Python 3.8 format", offset: 16, magicBytes: [0x0A, 0x0D, 0x0D, 0x56] },
                { name: "EA Python 3.9 format", offset: 16, magicBytes: [0x0A, 0x0D, 0x0D, 0x62] },
                { name: "Custom format 1", offset: 8, magicBytes: [0x63, 0x00, 0x00, 0x00] },
                { name: "Custom format 2", offset: 12, magicBytes: [0x63, 0x00, 0x00, 0x00] },
                { name: "Custom format 3", offset: 4, magicBytes: [0x63, 0x00, 0x00, 0x00] },
                { name: "Raw code object", offset: 0, magicBytes: [0x63, 0x00, 0x00, 0x00] },
                { name: "Obfuscated format 1", offset: 20, magicBytes: [0x63, 0x00, 0x00, 0x00] },
                { name: "Obfuscated format 2", offset: 24, magicBytes: [0x63, 0x00, 0x00, 0x00] },
                { name: "Obfuscated format 3", offset: 28, magicBytes: [0x63, 0x00, 0x00, 0x00] },
                { name: "Obfuscated format 4", offset: 32, magicBytes: [0x63, 0x00, 0x00, 0x00] },
            ];

            // Try each strategy
            for (const strategy of strategies) {
                if (buffer.length <= strategy.offset + 4) {
                    this.logger.debug(`Skipping ${strategy.name} strategy: buffer too small (${buffer.length} bytes)`);
                    continue;
                }

                // Check if the buffer matches the magic bytes at the specified offset
                let matches = true;
                for (let i = 0; i < strategy.magicBytes.length; i++) {
                    if (buffer[strategy.offset + i] !== strategy.magicBytes[i]) {
                        matches = false;
                        break;
                    }
                }

                if (!matches) {
                    this.logger.debug(`${strategy.name} strategy: magic bytes don't match`);
                    continue;
                }

                try {
                    this.logger.debug(`Trying ${strategy.name} strategy with offset ${strategy.offset}`);
                    const codeObject = this.parseCodeObjectSafely(buffer.slice(strategy.offset), filename);
                    if (codeObject) {
                        this.logger.debug(`Successfully parsed code object with ${strategy.name} strategy`);
                        return codeObject;
                    }
                } catch (error) {
                    this.logger.debug(`${strategy.name} strategy failed: ${error}`);
                }
            }

            // If all strategies fail, try to find code object markers in the file
            this.logger.debug(`All predefined strategies failed, searching for code object markers`);
            const markerPositions = this.findCodeObjectMarkers(buffer);

            if (markerPositions.length > 0) {
                this.logger.debug(`Found ${markerPositions.length} potential code object markers`);

                for (const position of markerPositions) {
                    try {
                        const codeObject = this.parseCodeObjectSafely(buffer.slice(position), filename);
                        if (codeObject) {
                            this.logger.debug(`Successfully parsed code object at marker position ${position}`);
                            return codeObject;
                        }
                    } catch (error) {
                        this.logger.debug(`Marker position ${position} parsing failed: ${error}`);
                    }
                }
            }

            // If all strategies fail, create a minimal code object with string extraction
            this.logger.warn(`All parsing strategies failed, creating minimal code object with string extraction`);
            return this.createMinimalCodeObject(buffer, filename);
        } catch (error) {
            this.logger.error(`Error in parseNonStandardBytecode: ${error}`);
            return this.createMinimalCodeObject(buffer, filename);
        }
    }

    /**
     * Find code object markers in a buffer
     * @param buffer Buffer to search
     * @returns Array of positions where code object markers were found
     */
    private findCodeObjectMarkers(buffer: Buffer): number[] {
        const positions: number[] = [];
        const markers = [0x63, 0x43]; // 'c' and 'C' in ASCII

        // Search for markers with reasonable context
        for (let i = 0; i < buffer.length - 12; i++) {
            if (markers.includes(buffer[i])) {
                // Check if followed by reasonable values for a code object
                // In Python bytecode, a code object marker is typically followed by:
                // - An argument count (usually small)
                // - A locals count (usually small)
                // - A stack size (usually small)
                // - Flags (usually small)

                // Use safe buffer reader to prevent out-of-range errors
                const reader = new SafeBufferReader(buffer, i + 1);

                // Read potential argument count
                const argCount = reader.readUInt32LE();
                if (argCount !== null && argCount < 256) {
                    // This looks promising, add the position
                    positions.push(i);
                }
            }
        }

        return positions;
    }

    /**
     * Parse code object safely with enhanced error handling
     * @param buffer Buffer to parse
     * @param filename Filename
     * @returns Parsed code object or null if parsing fails
     */
    private parseCodeObjectSafely(buffer: Buffer, filename: string): CodeObject | null {
        try {
            // Create a safe buffer reader
            const reader = new SafeBufferReader(buffer);

            // Read the type of the code object (should be 'c' for code)
            const typeByte = reader.readUInt8();
            if (typeByte === null) {
                return null;
            }

            const type = String.fromCharCode(typeByte);
            if (type !== 'c' && type !== 'C') {
                this.logger.debug(`Invalid code object type: ${type}`);
                return null;
            }

            // Read the argument count
            const argCount = reader.readUInt32LE() ?? 0;

            // Sanity check: argument count should be reasonable
            if (argCount > 255) {
                this.logger.debug(`Unreasonable argument count: ${argCount}`);
                return null;
            }

            // Read the keyword-only argument count
            const kwOnlyArgCount = reader.readUInt32LE() ?? 0;

            // Sanity check: keyword-only argument count should be reasonable
            if (kwOnlyArgCount > 255) {
                this.logger.debug(`Unreasonable keyword-only argument count: ${kwOnlyArgCount}`);
                return null;
            }

            // Read the number of local variables
            const nLocals = reader.readUInt32LE() ?? 0;

            // Sanity check: number of local variables should be reasonable
            if (nLocals > 10000) {
                this.logger.debug(`Unreasonable number of local variables: ${nLocals}`);
                return null;
            }

            // Read the stack size
            const stackSize = reader.readUInt32LE() ?? 0;

            // Sanity check: stack size should be reasonable
            if (stackSize > 10000) {
                this.logger.debug(`Unreasonable stack size: ${stackSize}`);
                return null;
            }

            // Read the flags
            const flags = reader.readUInt32LE() ?? 0;

            // Read the bytecode
            const bytecodeSize = reader.readUInt32LE() ?? 0;

            // Sanity check: bytecode size should be reasonable
            if (bytecodeSize > buffer.length || bytecodeSize > 1000000) {
                this.logger.debug(`Unreasonable bytecode size: ${bytecodeSize}`);
                return null;
            }

            const bytecode = reader.readSlice(bytecodeSize) ?? Buffer.alloc(0);

            // If we couldn't read the bytecode, this is not a valid code object
            if (bytecode.length === 0 && bytecodeSize > 0) {
                this.logger.debug(`Failed to read bytecode`);
                return null;
            }

            // Read the constants
            const constantsCount = reader.readUInt32LE() ?? 0;

            // Sanity check: constants count should be reasonable
            if (constantsCount > 10000) {
                this.logger.debug(`Unreasonable constants count: ${constantsCount}`);
                return null;
            }

            // At this point, we've read enough to confirm this is likely a valid code object
            // We'll create a minimal code object with the information we have

            // Extract string literals as a fallback for constants
            const stringLiterals = extractStringLiterals(buffer);

            return {
                type: 'module',
                argCount,
                posOnlyArgCount: 0,
                kwOnlyArgCount,
                nLocals,
                stackSize,
                flags,
                bytecode,
                constants: stringLiterals,
                names: stringLiterals.filter(s => /^[A-Za-z_][A-Za-z0-9_]*$/.test(s)),
                varNames: [],
                freeVars: [],
                cellVars: [],
                filename,
                name: path.basename(filename, path.extname(filename)),
                firstLineNo: 1,
                lnotab: Buffer.alloc(0),
                nestedCodeObjects: []
            };
        } catch (error) {
            this.logger.debug(`Error in parseCodeObjectSafely: ${error}`);
            return null;
        }
    }

    /**
     * Create a minimal code object with string extraction
     * @param buffer Buffer to extract strings from
     * @param filename Filename
     * @returns Minimal code object
     */
    private createMinimalCodeObject(buffer: Buffer, filename: string): CodeObject {
        // Extract string literals
        const stringLiterals = extractStringLiterals(buffer);

        // Filter for likely Python names (functions, classes, variables)
        const likelyNames = stringLiterals.filter(s => /^[A-Za-z_][A-Za-z0-9_]*$/.test(s));

        // Create minimal code object
        return {
            type: 'module',
            argCount: 0,
            posOnlyArgCount: 0,
            kwOnlyArgCount: 0,
            nLocals: 0,
            stackSize: 0,
            flags: 0,
            bytecode: Buffer.alloc(0),
            constants: stringLiterals,
            names: likelyNames,
            varNames: [],
            freeVars: [],
            cellVars: [],
            filename,
            name: path.basename(filename, path.extname(filename)),
            firstLineNo: 1,
            lnotab: Buffer.alloc(0),
            nestedCodeObjects: []
        };
    }
}
