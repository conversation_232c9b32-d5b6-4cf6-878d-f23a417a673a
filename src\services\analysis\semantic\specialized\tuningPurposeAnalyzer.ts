/**
 * Tuning Purpose Analyzer
 * 
 * Specialized analyzer for Tuning XML resources to determine their purpose
 * and semantic meaning with high accuracy.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { ResourceKey } from '../../../../types/resource/interfaces.js';
import { ResourcePurposeType } from '../interfaces/resourcePurpose.js';
import { injectable, singleton } from '../../../di/decorators.js';
import * as ResourceTypes from '../../../../constants/resourceTypes.js';

/**
 * Tuning purpose analysis result
 */
export interface TuningPurposeAnalysisResult {
    /**
     * Tuning type
     */
    tuningType: string;
    
    /**
     * Module name
     */
    moduleName: string;
    
    /**
     * Class name
     */
    className: string;
    
    /**
     * Instance name
     */
    instanceName: string;
    
    /**
     * Resource purpose type
     */
    purposeType: ResourcePurposeType;
    
    /**
     * Tuning purpose
     */
    tuningPurpose: string;
    
    /**
     * Confidence score (0-100)
     */
    confidence: number;
    
    /**
     * Related gameplay system
     */
    gameplaySystem?: string;
    
    /**
     * Is this an override?
     */
    isOverride: boolean;
    
    /**
     * Key elements in the tuning
     */
    keyElements: string[];
    
    /**
     * Critical attributes in the tuning
     */
    criticalAttributes: Array<{
        element: string;
        attribute: string;
        value: string;
    }>;
    
    /**
     * Explanation of the purpose determination
     */
    explanation: string[];
}

/**
 * Tuning type purpose mapping
 */
interface TuningTypePurposeMapping {
    /**
     * Tuning type pattern (regex)
     */
    tuningTypePattern: RegExp;
    
    /**
     * Module name pattern (regex)
     */
    moduleNamePattern?: RegExp;
    
    /**
     * Class name pattern (regex)
     */
    classNamePattern?: RegExp;
    
    /**
     * Tuning purpose
     */
    purpose: string;
    
    /**
     * Resource purpose type for new content
     */
    newContentPurposeType: ResourcePurposeType;
    
    /**
     * Resource purpose type for overrides
     */
    overridePurposeType: ResourcePurposeType;
    
    /**
     * Related gameplay system
     */
    gameplaySystem?: string;
    
    /**
     * Base confidence score (0-100)
     */
    baseConfidence: number;
    
    /**
     * Key element patterns
     */
    keyElementPatterns: string[];
    
    /**
     * Critical attribute patterns
     */
    criticalAttributePatterns: Array<{
        element: string;
        attribute: string;
    }>;
}

/**
 * Specialized analyzer for Tuning XML resources
 */
@singleton()
export class TuningPurposeAnalyzer {
    private logger: Logger;
    private tuningTypePurposeMappings: TuningTypePurposeMapping[] = [];
    
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(logger?: Logger) {
        this.logger = logger || new Logger('TuningPurposeAnalyzer');
        this.initializeTuningTypeMappings();
    }
    
    /**
     * Initialize tuning type purpose mappings
     */
    private initializeTuningTypeMappings(): void {
        // Trait tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Trait/i,
            moduleNamePattern: /traits/i,
            classNamePattern: /Trait/i,
            purpose: 'trait_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'traits',
            baseConfidence: 90,
            keyElementPatterns: ['display_name', 'trait_description', 'icon', 'trait_type'],
            criticalAttributePatterns: [
                { element: 'trait_type', attribute: 'value' },
                { element: 'display_name', attribute: 'value' }
            ]
        });
        
        // Buff tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Buff/i,
            moduleNamePattern: /buffs/i,
            classNamePattern: /Buff/i,
            purpose: 'buff_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'emotions',
            baseConfidence: 90,
            keyElementPatterns: ['buff_name', 'buff_description', 'mood_type', 'mood_weight'],
            criticalAttributePatterns: [
                { element: 'mood_type', attribute: 'value' },
                { element: 'mood_weight', attribute: 'value' }
            ]
        });
        
        // Career tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Career/i,
            moduleNamePattern: /careers/i,
            classNamePattern: /Career/i,
            purpose: 'career_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'careers',
            baseConfidence: 90,
            keyElementPatterns: ['career_name', 'career_description', 'icon', 'career_levels'],
            criticalAttributePatterns: [
                { element: 'career_name', attribute: 'value' },
                { element: 'career_levels', attribute: 'value' }
            ]
        });
        
        // Skill tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Skill/i,
            moduleNamePattern: /skills/i,
            classNamePattern: /Skill/i,
            purpose: 'skill_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'skills',
            baseConfidence: 90,
            keyElementPatterns: ['skill_name', 'skill_description', 'icon', 'skill_levels'],
            criticalAttributePatterns: [
                { element: 'skill_name', attribute: 'value' },
                { element: 'skill_levels', attribute: 'value' }
            ]
        });
        
        // Aspiration tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Aspiration/i,
            moduleNamePattern: /aspirations/i,
            classNamePattern: /Aspiration/i,
            purpose: 'aspiration_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'aspirations',
            baseConfidence: 90,
            keyElementPatterns: ['display_name', 'display_description', 'icon', 'objectives'],
            criticalAttributePatterns: [
                { element: 'display_name', attribute: 'value' },
                { element: 'objectives', attribute: 'value' }
            ]
        });
        
        // Interaction tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Interaction/i,
            moduleNamePattern: /interactions/i,
            classNamePattern: /Interaction/i,
            purpose: 'interaction_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'interactions',
            baseConfidence: 90,
            keyElementPatterns: ['display_name', 'display_tooltip', 'icon', 'basic_extras'],
            criticalAttributePatterns: [
                { element: 'display_name', attribute: 'value' },
                { element: 'target', attribute: 'value' }
            ]
        });
        
        // Object tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Object/i,
            moduleNamePattern: /objects/i,
            classNamePattern: /Object/i,
            purpose: 'object_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'objects',
            baseConfidence: 90,
            keyElementPatterns: ['name', 'description', 'icon', 'price'],
            criticalAttributePatterns: [
                { element: 'name', attribute: 'value' },
                { element: 'price', attribute: 'value' }
            ]
        });
        
        // Recipe tuning
        this.tuningTypePurposeMappings.push({
            tuningTypePattern: /Recipe/i,
            moduleNamePattern: /recipes/i,
            classNamePattern: /Recipe/i,
            purpose: 'recipe_definition',
            newContentPurposeType: ResourcePurposeType.ADDS_CONTENT,
            overridePurposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'objects',
            baseConfidence: 90,
            keyElementPatterns: ['name', 'description', 'icon', 'ingredients'],
            criticalAttributePatterns: [
                { element: 'name', attribute: 'value' },
                { element: 'ingredients', attribute: 'value' }
            ]
        });
    }
    
    /**
     * Analyze Tuning XML resource purpose
     * @param resourceKey Resource key
     * @param tuningContent Tuning XML content
     * @param metadata Resource metadata
     * @returns Tuning purpose analysis result
     */
    public analyzeTuningPurpose(
        resourceKey: ResourceKey,
        tuningContent: string,
        metadata: Record<string, any>
    ): TuningPurposeAnalysisResult {
        try {
            // Default result
            const result: TuningPurposeAnalysisResult = {
                tuningType: 'Unknown',
                moduleName: '',
                className: '',
                instanceName: '',
                purposeType: ResourcePurposeType.UNKNOWN,
                tuningPurpose: 'unknown',
                confidence: 0,
                isOverride: false,
                keyElements: [],
                criticalAttributes: [],
                explanation: []
            };
            
            // Check if this is a Tuning XML resource
            if (resourceKey.type !== ResourceTypes.RESOURCE_TYPE_TUNING) {
                result.explanation.push('Not a Tuning XML resource');
                return result;
            }
            
            // Extract tuning type, module name, class name, and instance name from metadata
            result.tuningType = metadata.tuningType || '';
            result.moduleName = metadata.moduleName || '';
            result.className = metadata.className || '';
            result.instanceName = metadata.instanceName || '';
            
            // Check if this is an override
            result.isOverride = metadata.isOverride || false;
            
            // If we don't have tuning type, try to extract from content
            if (!result.tuningType && tuningContent) {
                // Extract tuning type from content
                const typeMatch = tuningContent.match(/<I\s+n="([^"]+)"/);
                if (typeMatch && typeMatch[1]) {
                    result.tuningType = typeMatch[1];
                    result.explanation.push(`Extracted tuning type from content: ${typeMatch[1]}`);
                }
                
                // Extract module name from content
                const moduleMatch = tuningContent.match(/m="([^"]+)"/);
                if (moduleMatch && moduleMatch[1]) {
                    result.moduleName = moduleMatch[1];
                    result.explanation.push(`Extracted module name from content: ${moduleMatch[1]}`);
                }
                
                // Extract class name from content
                const classMatch = tuningContent.match(/c="([^"]+)"/);
                if (classMatch && classMatch[1]) {
                    result.className = classMatch[1];
                    result.explanation.push(`Extracted class name from content: ${classMatch[1]}`);
                }
                
                // Extract instance name from content
                const instanceMatch = tuningContent.match(/i="([^"]+)"/);
                if (instanceMatch && instanceMatch[1]) {
                    result.instanceName = instanceMatch[1];
                    result.explanation.push(`Extracted instance name from content: ${instanceMatch[1]}`);
                }
                
                // Check for override indicators
                if (tuningContent.includes('instance_reference') || 
                    tuningContent.includes('_override') || 
                    tuningContent.includes('_patch')) {
                    result.isOverride = true;
                    result.explanation.push('Detected override indicators in content');
                }
            }
            
            // If we have tuning type, module name, or class name, try to match to a purpose
            if (result.tuningType || result.moduleName || result.className) {
                for (const mapping of this.tuningTypePurposeMappings) {
                    let matches = false;
                    
                    // Check tuning type
                    if (result.tuningType && mapping.tuningTypePattern.test(result.tuningType)) {
                        matches = true;
                        result.explanation.push(`Tuning type "${result.tuningType}" matches pattern for ${mapping.purpose}`);
                    }
                    
                    // Check module name
                    if (result.moduleName && mapping.moduleNamePattern && mapping.moduleNamePattern.test(result.moduleName)) {
                        matches = true;
                        result.explanation.push(`Module name "${result.moduleName}" matches pattern for ${mapping.purpose}`);
                    }
                    
                    // Check class name
                    if (result.className && mapping.classNamePattern && mapping.classNamePattern.test(result.className)) {
                        matches = true;
                        result.explanation.push(`Class name "${result.className}" matches pattern for ${mapping.purpose}`);
                    }
                    
                    if (matches) {
                        result.tuningPurpose = mapping.purpose;
                        result.purposeType = result.isOverride ? mapping.overridePurposeType : mapping.newContentPurposeType;
                        result.gameplaySystem = mapping.gameplaySystem;
                        result.confidence = mapping.baseConfidence;
                        
                        // Check for key elements in content
                        if (tuningContent) {
                            const matchedElements: string[] = [];
                            
                            for (const elementPattern of mapping.keyElementPatterns) {
                                const regex = new RegExp(`<[^>]*n="${elementPattern}"[^>]*>`, 'i');
                                if (regex.test(tuningContent)) {
                                    matchedElements.push(elementPattern);
                                    result.keyElements.push(elementPattern);
                                }
                            }
                            
                            if (matchedElements.length > 0) {
                                result.confidence += Math.min(10, matchedElements.length * 2);
                                result.explanation.push(`Found ${matchedElements.length} key elements: ${matchedElements.join(', ')}`);
                            }
                            
                            // Check for critical attributes
                            for (const attrPattern of mapping.criticalAttributePatterns) {
                                const regex = new RegExp(`<[^>]*n="${attrPattern.element}"[^>]*${attrPattern.attribute}="([^"]*)"`, 'i');
                                const match = tuningContent.match(regex);
                                if (match && match[1]) {
                                    result.criticalAttributes.push({
                                        element: attrPattern.element,
                                        attribute: attrPattern.attribute,
                                        value: match[1]
                                    });
                                    result.explanation.push(`Found critical attribute: ${attrPattern.element}.${attrPattern.attribute} = ${match[1]}`);
                                }
                            }
                        }
                        
                        break;
                    }
                }
            }
            
            return result;
        } catch (error) {
            this.logger.error(`Error analyzing Tuning purpose for resource ${resourceKey.type}:${resourceKey.instance}:`, error);
            
            // Return default result on error
            return {
                tuningType: 'Unknown',
                moduleName: '',
                className: '',
                instanceName: '',
                purposeType: ResourcePurposeType.UNKNOWN,
                tuningPurpose: 'unknown',
                confidence: 0,
                isOverride: false,
                keyElements: [],
                criticalAttributes: [],
                explanation: ['Error analyzing Tuning purpose']
            };
        }
    }
}
