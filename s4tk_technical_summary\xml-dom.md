# S4TK Package: @s4tk/xml-dom

Based on documentation found in `docs/technical/xml-dom/`.

## Overview

This package provides an XML Document Object Model (DOM) specifically tailored for working with Sims 4 tuning files, although it can be used for other XML-based resources like SimData XML representations or ASMs. It allows parsing XML into a tree of node objects and serializing it back to a string.

## Installation

```sh
npm i @s4tk/xml-dom
```

## API

### Core Node Interface: `XmlNode`

Base interface for all nodes in the DOM.

**Properties:**

*   `attributes: Attributes`: Get/set attributes (`{ [key: string]: any }`). Undefined for nodes that cannot have attributes (comments, values).
*   `child: XmlNode`: Get/set the first child node.
*   `children: XmlNode[]`: Get/set an array of all child nodes. Undefined for nodes that cannot have children.
*   `hasChildren: boolean`: True if the node can have children.
*   `id: string | number | bigint`: Shorthand getter/setter for the `s` attribute.
*   `innerValue: XmlValue`: Get/set the value of the first child node (if it's a value node).
*   `name: string`: Shorthand getter/setter for the `n` attribute.
*   `numChildren: number`: Number of child nodes.
*   `tag: string`: Get/set the element tag name (e.g., "I", "T", "L").
*   `type: string`: Shorthand getter/setter for the `t` attribute.
*   `value: XmlValue`: Get/set the node's value (only applicable to `XmlValueNode`, `XmlCommentNode`). `XmlValue` is `number | bigint | boolean | string`.

**Methods:**

*   `addChildren(...children: XmlNode[]): void`: Appends child nodes by reference.
*   `addClones(...children: XmlNode[]): void`: Clones and appends child nodes.
*   `clone(): XmlNode`: Returns a deep copy of the node.
*   `deepSort(compareFn?): void`: Sorts children recursively.
*   `equals(other: XmlNode, options?: XmlNodeComparisonOptions): boolean`: Compares this node to another, with options to ignore attributes or limit recursion.
*   `findChild(name: string): XmlNode`: Finds the first child element where the `n` attribute matches the given name.
*   `sort(compareFn?): void`: Sorts immediate children (defaults to sorting by `n` attribute).
*   `toXml(options?: XmlFormattingOptions): string`: Serializes the node and its descendants to an XML string with formatting options.

### Node Classes (Implement `XmlNode`)

*   **`XmlDocumentNode`**: Represents the entire XML document.
    *   `declaration: Attributes`: Get/set attributes for the `<?xml ... ?>` declaration (default: `{ version: "1.0", encoding: "utf-8" }`).
    *   `static from(xml: string | Buffer, options?: XmlParsingOptions): XmlDocumentNode`: Parses an XML string or buffer into a document node.
    *   `static fromAsync(...)`: Async version.
    *   `static fromRecycled(...)`: Parses while reusing identical nodes in memory (advanced).
*   **`XmlElementNode`**: Represents an element with a tag, attributes, and children (e.g., `<T n="name">Value</T>`).
    *   `constructor({ tag, attributes?, children? })`
*   **`XmlValueNode`**: Represents a text value within an element.
    *   `constructor(value?)`
*   **`XmlCommentNode`**: Represents an XML comment (`<!-- ... -->`).
    *   `constructor(value?)`
*   **`XmlWrapperNode`**: Represents a processing instruction (`<? ... ?>`).
    *   `constructor({ tag, children? })`

### Options Interfaces

*   **`XmlParsingOptions`**: Options for `XmlDocumentNode.from()`:
    *   `ignoreComments?: boolean` (default: false)
    *   `ignoreProcessingInstructions?: boolean` (default: false)
    *   `recycleNodes?: boolean` (default: false) - Use with caution if modifying DOM.
    *   `recycledNodesCache?`, `recycledNodesSeed?`: Advanced options for node recycling.
*   **`XmlFormattingOptions`**: Options for `XmlNode.toXml()`:
    *   `indents?: number` (default: 0)
    *   `minify?: boolean` (default: false)
    *   `spacesPerIndent?: number` (default: 2)
    *   `writeComments?: boolean` (default: true)
    *   `writeProcessingInstructions?: boolean` (default: true)
    *   `writeXmlDeclaration?: boolean` (default: true) - Controls `<?xml ... ?>`.
*   **`XmlNodeComparisonOptions`**: Options for `XmlNode.equals()`:
    *   `excludeAttributes?: string[]`: Attribute keys to ignore on the top-level node.
    *   `excludeAttributesRecursive?: string[]`: Attribute keys to ignore on all descendant nodes.
    *   `recursionLevels?: number`: How many levels deep to compare children (default: infinite).
    *   `strictTypes?: boolean`: If true, compares value types strictly (e.g., `true !== "True"`). If false, compares based on string representation.

## Usage Notes

*   This DOM provides convenient shorthands (`.name`, `.id`, `.type`) for accessing common Sims 4 tuning attributes (`n`, `s`, `t`).
*   Parsing expects uncompressed XML data.
*   Modifying the DOM via getters/setters might require explicit cache updates (e.g., `node.dom = node.dom`) or using the `updateDom`/`updateRoot` methods for safety.