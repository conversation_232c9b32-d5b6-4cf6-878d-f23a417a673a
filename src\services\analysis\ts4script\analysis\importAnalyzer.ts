/**
 * TS4Script Import Analyzer
 * 
 * This module provides functionality for analyzing imports in Sims 4 scripts.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { BytecodeInstruction, CodeObject } from '../bytecode/types.js';
import { TS4ScriptImport, TS4ScriptDependency } from '../types.js';

/**
 * TS4Script import analyzer
 */
@singleton()
export class ImportAnalyzer {
    /**
     * EA module prefixes
     */
    private readonly eaModulePrefixes = [
        'sims4.',
        'services.',
        'interactions.',
        'objects.',
        'server.',
        'ui.',
        'zone.',
        'world.',
        'animation.',
        'autonomy.',
        'buffs.',
        'careers.',
        'cas.',
        'clubs.',
        'crafting.',
        'distributor.',
        'event_testing.',
        'filters.',
        'gsi.',
        'holidays.',
        'narrative.',
        'objects.',
        'performance.',
        'pets.',
        'postures.',
        'protocolbuffers.',
        'routing.',
        'seasons.',
        'sims.',
        'situations.',
        'socials.',
        'statistics.',
        'story_progression.',
        'venues.',
        'visualization.',
        'whims.'
    ];

    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(private logger: Logger = new Logger('ImportAnalyzer')) {}

    /**
     * Analyze imports in code object
     * @param codeObject Code object
     * @param moduleName Module name
     * @param instructions Bytecode instructions
     * @returns Imports
     */
    public analyzeImports(codeObject: CodeObject, moduleName: string, instructions: BytecodeInstruction[]): TS4ScriptImport[] {
        try {
            this.logger.debug(`Analyzing imports in module: ${moduleName}`);

            const imports: TS4ScriptImport[] = [];
            
            // Find import statements in the bytecode
            const importStatements = this.findImportStatements(instructions);
            
            // Process each import statement
            for (const importStatement of importStatements) {
                // Create import object
                const importObj: TS4ScriptImport = {
                    importedModule: importStatement.module,
                    importedNames: importStatement.names,
                    isFromImport: importStatement.isFromImport,
                    isEAModule: this.isEAModule(importStatement.module),
                    isRelativeImport: importStatement.module.startsWith('.'),
                    importLevel: this.calculateImportLevel(importStatement.module),
                    lineNumber: importStatement.lineNumber
                };
                
                imports.push(importObj);
            }
            
            return imports;
        } catch (error) {
            this.logger.error(`Error analyzing imports in module ${moduleName}:`, error);
            return [];
        }
    }

    /**
     * Find import statements in bytecode
     * @param instructions Bytecode instructions
     * @returns Import statements
     */
    private findImportStatements(instructions: BytecodeInstruction[]): any[] {
        const importStatements: any[] = [];
        
        // Look for IMPORT_NAME opcode
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            if (instruction.opcodeName === 'IMPORT_NAME') {
                // Import statement found
                const moduleName = instruction.argValue as string;
                
                // Check if it's a from-import statement
                const isFromImport = i > 0 && instructions[i - 1].opcodeName === 'LOAD_CONST';
                
                // Find imported names
                const importedNames: string[] = [];
                
                if (isFromImport) {
                    // Look for IMPORT_FROM instructions
                    let j = i + 1;
                    while (j < instructions.length && instructions[j].opcodeName === 'IMPORT_FROM') {
                        importedNames.push(instructions[j].argValue as string);
                        j++;
                    }
                }
                
                importStatements.push({
                    module: moduleName,
                    names: importedNames,
                    isFromImport,
                    lineNumber: instruction.lineNo
                });
            }
        }
        
        return importStatements;
    }

    /**
     * Check if module is an EA module
     * @param moduleName Module name
     * @returns Is EA module
     */
    private isEAModule(moduleName: string): boolean {
        // Check if module name starts with an EA module prefix
        for (const prefix of this.eaModulePrefixes) {
            if (moduleName.startsWith(prefix)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Calculate import level
     * @param moduleName Module name
     * @returns Import level
     */
    private calculateImportLevel(moduleName: string): number {
        // Calculate import level based on the number of dots at the beginning
        if (moduleName.startsWith('.')) {
            let level = 0;
            for (let i = 0; i < moduleName.length; i++) {
                if (moduleName[i] === '.') {
                    level++;
                } else {
                    break;
                }
            }
            return level;
        }
        
        return 0;
    }

    /**
     * Analyze dependencies
     * @param imports Imports
     * @returns Dependencies
     */
    public analyzeDependencies(imports: TS4ScriptImport[]): TS4ScriptDependency[] {
        try {
            this.logger.debug(`Analyzing dependencies from ${imports.length} imports`);

            const dependencies: TS4ScriptDependency[] = [];
            
            // Process each import
            for (const importObj of imports) {
                // Create dependency object
                const dependency: TS4ScriptDependency = {
                    dependsOnModuleName: importObj.importedModule,
                    dependencyType: importObj.isFromImport ? 'from_import' : 'import',
                    isEADependency: importObj.isEAModule
                };
                
                // Check if dependency already exists
                const existingDependency = dependencies.find(d => 
                    d.dependsOnModuleName === dependency.dependsOnModuleName && 
                    d.dependencyType === dependency.dependencyType
                );
                
                if (!existingDependency) {
                    dependencies.push(dependency);
                }
            }
            
            return dependencies;
        } catch (error) {
            this.logger.error(`Error analyzing dependencies:`, error);
            return [];
        }
    }

    /**
     * Build dependency graph
     * @param modules Modules
     * @returns Dependency graph
     */
    public buildDependencyGraph(modules: any[]): Map<string, string[]> {
        try {
            this.logger.debug(`Building dependency graph for ${modules.length} modules`);

            const dependencyGraph = new Map<string, string[]>();
            
            // Process each module
            for (const module of modules) {
                const moduleName = module.name;
                const dependencies: string[] = [];
                
                // Process each import
                for (const importObj of module.imports) {
                    dependencies.push(importObj.importedModule);
                }
                
                dependencyGraph.set(moduleName, dependencies);
            }
            
            return dependencyGraph;
        } catch (error) {
            this.logger.error(`Error building dependency graph:`, error);
            return new Map<string, string[]>();
        }
    }

    /**
     * Find entry points
     * @param dependencyGraph Dependency graph
     * @returns Entry points
     */
    public findEntryPoints(dependencyGraph: Map<string, string[]>): string[] {
        try {
            this.logger.debug(`Finding entry points in dependency graph`);

            const entryPoints: string[] = [];
            
            // Find modules that are not imported by any other module
            const allModules = Array.from(dependencyGraph.keys());
            const allDependencies = Array.from(dependencyGraph.values()).flat();
            
            for (const module of allModules) {
                if (!allDependencies.includes(module)) {
                    entryPoints.push(module);
                }
            }
            
            return entryPoints;
        } catch (error) {
            this.logger.error(`Error finding entry points:`, error);
            return [];
        }
    }

    /**
     * Find main module
     * @param entryPoints Entry points
     * @param modules Modules
     * @returns Main module
     */
    public findMainModule(entryPoints: string[], modules: any[]): string | undefined {
        try {
            this.logger.debug(`Finding main module from ${entryPoints.length} entry points`);

            // Look for modules with specific patterns
            for (const entryPoint of entryPoints) {
                const module = modules.find(m => m.name === entryPoint);
                
                if (module) {
                    // Check if module has specific patterns
                    if (module.metadata.hasCommands || module.metadata.hasInjections || module.metadata.hasEventHandlers) {
                        return entryPoint;
                    }
                    
                    // Check if module name indicates a main module
                    if (entryPoint.includes('main') || entryPoint.includes('init') || entryPoint === '__init__') {
                        return entryPoint;
                    }
                }
            }
            
            // If no specific pattern is found, return the first entry point
            return entryPoints.length > 0 ? entryPoints[0] : undefined;
        } catch (error) {
            this.logger.error(`Error finding main module:`, error);
            return undefined;
        }
    }
}
