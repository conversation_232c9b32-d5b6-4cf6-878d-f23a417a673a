import type { ResourceEntry } from '@s4tk/models';
import { createHash } from 'crypto';
import { ResourceKey, ResourceMetadata } from '../../types/resource/interfaces.js';
import { createResourceKey, getResourceTypeCategory } from './helpers.js'; // Assuming helpers.ts is in the same dir or adjust path
import path from 'path';

// Define the intermediate interfaces used by the conversion functions
// These might not need to be exported if only used internally here.
interface S4TKCompatibleResource {
  type: number; // S4TK uses number for type
  group: bigint;
  instance: bigint;
  name: string;
  path: string;
  id: string;
  key: ResourceKey;
  metadata: ResourceMetadata;
  timestamp: number;
  getBuffer: () => Buffer;
}

interface ExtendedResource extends S4TKCompatibleResource {
  resourceType: number; // Consider if this is redundant vs base 'type'
  category: string;
}

// Define the basic Resource interface if needed by createResource
// (Alternatively, import if defined centrally and needed elsewhere)
interface Resource {
  type: number;
  name: string;
  path: string;
  key: ResourceKey;
  metadata: ResourceMetadata;
  timestamp: number;
}


/**
 * Creates an S4TK-compatible resource representation from an S4TK ResourceEntry.
 * Extracts buffer, calculates hash, and builds initial metadata.
 * NOTE: This assumes entry.value contains the buffer, which might load it into memory.
 */
export function createS4TKCompatibleResource(entry: ResourceEntry, filePath: string): S4TKCompatibleResource {
  // Explicitly cast group and instance to BigInt for createResourceKey
  const key = createResourceKey(entry.key.type, BigInt(entry.key.group), BigInt(entry.key.instance), entry.key.toString(), filePath);
  // Use key.group.toString() and key.instance.toString() for bigints
  const resourceId = key.id || `${key.type.toString(16)}_${key.group.toString()}_${key.instance.toString()}`;

  // Explicitly cast entry.value to access buffer, assuming it's a Resource instance with a buffer property
  // WARNING: This might load the full buffer into memory here.
  const buffer = (entry.value as any)?.buffer;
  if (!buffer || !(buffer instanceof Buffer)) {
      throw new Error(`Could not get buffer for resource ${resourceId} in ${filePath}`);
  }
  const size = buffer.length;
  const hash = createHash('sha256').update(buffer).digest('hex');

  return {
    type: key.type,
    group: BigInt(entry.key.group), // Ensure group is BigInt
    instance: BigInt(entry.key.instance), // Ensure instance is BigInt
    name: key.name || entry.key.toString(),
    path: filePath,
    id: resourceId,
    key: key, // Our internal ResourceKey with numbers
    metadata: {
        name: key.name || entry.key.toString(),
        path: filePath,
        size: size,
        hash: hash,
        timestamp: Date.now(), // Consider file timestamp?
        version: 'N/A',
        author: 'N/A',
        description: 'N/A',
        dependencies: [],
        conflicts: [],
        source: path.basename(filePath)
    },
    timestamp: Date.now(),
    getBuffer: () => buffer // Provides access to the buffer obtained earlier
  };
}

/**
 * Extends the base S4TKCompatibleResource with category information.
 */
export function createExtendedResource(base: S4TKCompatibleResource): ExtendedResource {
  return {
    ...base,
    resourceType: base.type, // Keep for potential explicit use?
    category: getResourceTypeCategory(base.type) // Use helper to get category
  };
}

/**
 * Creates a simplified Resource object from an ExtendedResource.
 */
export function createResource(extended: ExtendedResource): Resource {
  // Select only the properties needed for the basic Resource interface
  return {
    type: extended.type,
    name: extended.name,
    path: extended.path,
    key: extended.key,
    metadata: extended.metadata,
    timestamp: extended.timestamp
  };
}

/**
 * Converts an array of ExtendedResource to an array of simplified Resource objects.
 */
export function createResourceArray(resources: ExtendedResource[]): Resource[] {
  return resources.map(createResource);
}

/**
 * Converts an array of S4TKCompatibleResource to an array of ExtendedResource objects.
 * (Seems redundant if the input is already ExtendedResource, but useful if starting from S4TKCompatibleResource)
 */
export function createExtendedResourceArray(resources: S4TKCompatibleResource[]): ExtendedResource[] {
  return resources.map(createExtendedResource);
}
