import { Buffer } from 'buffer'; // Ensure Buffer is imported
import { Logger } from '../logging/logger'; // Assuming logger path

const logger = new Logger('CaspParser');


// Enum for CASP Age/Gender Flags (Combined Bitfield)
// Based on community findings (e.g., S4S, ModTheSims)
export enum CasAgeGenderCombinedFlag {
    // Female
    TF = 0x00000001, // Teen Female
    YF = 0x00000002, // Young Adult Female
    AF = 0x00000004, // Adult Female
    EF = 0x00000008, // Elder Female
    // Male
    TM = 0x00000100, // Teen Male
    YM = 0x00000200, // Young Adult Male
    AM = 0x00000400, // Adult Male
    EM = 0x00000800, // Elder Male
    // Child (Gender Neutral)
    CF = 0x00010000, // Child Female (Often combined with CU)
    CM = 0x00020000, // Child Male (Often combined with CU)
    CU = 0x00040000, // Child Unisex/Both
    // Toddler (Gender Neutral)
    PF = 0x00100000, // Toddler Female (Often combined with PU)
    PM = 0x00200000, // Toddler Male (Often combined with PU)
    PU = 0x00400000, // Toddler Unisex/Both
    // Baby (Gender Neutral) - Less common in CASP flags directly
    BF = 0x01000000, // Baby Female (Often combined with BU)
    BM = 0x02000000, // Baby Male (Often combined with BU)
    BU = 0x04000000, // Baby Unisex/Both
}

// Function to interpret the combined Age/Gender flags based on known bitfield layout
export function interpretAgeGenderFlags(flags: number): string[] {
    const interpreted: string[] = [];

    // Check Female Ages
    if (flags & CasAgeGenderCombinedFlag.TF) interpreted.push('TeenFemale');
    if (flags & CasAgeGenderCombinedFlag.YF) interpreted.push('YoungAdultFemale');
    if (flags & CasAgeGenderCombinedFlag.AF) interpreted.push('AdultFemale');
    if (flags & CasAgeGenderCombinedFlag.EF) interpreted.push('ElderFemale');

    // Check Male Ages
    if (flags & CasAgeGenderCombinedFlag.TM) interpreted.push('TeenMale');
    if (flags & CasAgeGenderCombinedFlag.YM) interpreted.push('YoungAdultMale');
    if (flags & CasAgeGenderCombinedFlag.AM) interpreted.push('AdultMale');
    if (flags & CasAgeGenderCombinedFlag.EM) interpreted.push('ElderMale');

    // Check Child (Unisex is often the primary flag used)
    if (flags & CasAgeGenderCombinedFlag.CU) interpreted.push('Child');
    else if (flags & CasAgeGenderCombinedFlag.CF) interpreted.push('ChildFemale'); // Less common standalone
    else if (flags & CasAgeGenderCombinedFlag.CM) interpreted.push('ChildMale');   // Less common standalone

    // Check Toddler (Unisex is often the primary flag used)
    if (flags & CasAgeGenderCombinedFlag.PU) interpreted.push('Toddler');
    else if (flags & CasAgeGenderCombinedFlag.PF) interpreted.push('ToddlerFemale'); // Less common standalone
    else if (flags & CasAgeGenderCombinedFlag.PM) interpreted.push('ToddlerMale');   // Less common standalone

     // Check Baby (Unisex is often the primary flag used)
    if (flags & CasAgeGenderCombinedFlag.BU) interpreted.push('Baby');
    else if (flags & CasAgeGenderCombinedFlag.BF) interpreted.push('BabyFemale'); // Less common standalone
    else if (flags & CasAgeGenderCombinedFlag.BM) interpreted.push('BabyMale');   // Less common standalone

    // Fallback/Error check (if flags is 0 or uninterpretable)
    if (interpreted.length === 0 && flags !== 0) {
        logger.warn(`Uninterpretable Age/Gender flags value: 0x${flags.toString(16)}`);
        interpreted.push('Unknown');
    } else if (flags === 0) {
         logger.warn(`Age/Gender flags value is zero.`);
         interpreted.push('None');
    }


    return interpreted;
}

// Placeholder for the structure we'll return
export interface ParsedCaspData { // <-- Add export
    version?: number;
    tgiListOffset?: number;
    presetCount?: number;
    name?: string;
    sortPriority?: number;
    secondarySortIndex?: number;
    propertyId?: number;
    auralMaterialHash?: number;
    parmFlags?: number;
    excludePartFlags?: bigint;
    excludeModifierRegionFlags?: number;
    flags?: { category: number; value: number }[];
    simoleonPrice?: number;
    partTitleKey?: number;
    partDescriptionKey?: number;
    uniqueTextureSpace?: number;
    bodyType?: number;
    ageGender?: number;
    swatchColors?: string[];
    buffKey?: { type: number; group: number; instance: bigint };
    variantThumbnailKey?: { type: number; group: number; instance: bigint };
    nakedKey?: { type: number; group: number; instance: bigint };
    parentKey?: { type: number; group: number; instance: bigint };
    sortLayer?: number;
    lodBlocks?: {
        level: number;
        assets: {
            castShadow: number;
            sorting: number;
            specLevel: number;
        }[];
        keyIndices: number[];
        unused?: number;
        
    }[];
    slotKeys?: { type: number; group: number; instance: bigint }[];
    tgiIndices?: number[];
    tgiList?: { type: number; group: number; instance: bigint }[];
}

export function parseCasp(buffer: Buffer): ParsedCaspData | null {
    logger.info(`[CaspParser ENTRY] Received buffer of size ${buffer?.length ?? 'undefined'} for parsing.`);
    if (!buffer || buffer.length < 20) { // Basic sanity check for minimum size
        logger.error('Invalid or too small buffer provided for CASP parsing.');
        return null;
    }

    const data: ParsedCaspData = {};
    let offset = 0;

    try { // Wrap the entire parsing logic in a try...catch
        // logger.debug(`Entering parseCasp for buffer of size ${buffer?.length ?? 'undefined'}`);

        // logger.debug(`[CaspParser TRY] Attempting first read (Version) at offset ${offset}`);
        data.version = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Version: 0x${data.version.toString(16)}`);

        // logger.debug(`Offset before reading TGI List Offset: ${offset}`);
        data.tgiListOffset = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Offset after reading TGI List Offset: ${offset}`);
        // logger.debug(`TGI List Offset: ${data.tgiListOffset}`);

        data.presetCount = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Preset Count: ${data.presetCount}`);

        const nameLength = buffer.readUInt8(offset);
        offset += 1;
        // logger.debug(`Name Length: ${nameLength}`);

        if (offset + nameLength > buffer.length) {
             logger.error(`Buffer too small for name. Offset: ${offset}, NameLength: ${nameLength}, BufferSize: ${buffer.length}`);
             throw new Error('Buffer too small for name');
        }
        data.name = buffer.toString('utf8', offset, offset + nameLength);
        offset += nameLength;
        // logger.debug(`Name: ${data.name}`);

        // Assuming fields from Fandom example follow directly after name
        if (offset + 4 > buffer.length) throw new Error('Buffer too small for SortPriority');
        data.sortPriority = buffer.readFloatLE(offset);
        offset += 4;
        // logger.debug(`Sort Priority: ${data.sortPriority}`);

        if (offset + 2 > buffer.length) throw new Error('Buffer too small for SecondarySortIndex');
        data.secondarySortIndex = buffer.readUInt16LE(offset);
        offset += 2;
        // logger.debug(`Secondary Sort Index: 0x${data.secondarySortIndex.toString(16)}`);

        if (offset + 2 > buffer.length) throw new Error('Buffer too small for PropertyID');
        data.propertyId = buffer.readUInt16LE(offset);
        offset += 2;
        // logger.debug(`Property ID: 0x${data.propertyId.toString(16)}`);

        if (offset + 4 > buffer.length) throw new Error('Buffer too small for AuralMaterialHash');
        data.auralMaterialHash = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Aural Material Hash: 0x${data.auralMaterialHash.toString(16)}`);

        if (offset + 4 > buffer.length) throw new Error('Buffer too small for ParmFlags');
        data.parmFlags = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Parm Flags: 0x${data.parmFlags.toString(16)}`);

        if (offset + 8 > buffer.length) throw new Error('Buffer too small for ExcludePartFlags');
        data.excludePartFlags = buffer.readBigUInt64LE(offset);
        offset += 8;
        // logger.debug(`Exclude Part Flags: 0x${data.excludePartFlags.toString(16)}`);

        if (offset + 4 > buffer.length) throw new Error('Buffer too small for ExcludeModifierRegionFlags');
        data.excludeModifierRegionFlags = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Exclude Modifier Region Flags: 0x${data.excludeModifierRegionFlags.toString(16)}`);

        // --- FlagList ---
        if (offset + 4 > buffer.length) throw new Error('Buffer too small for FlagListCount');
        const flagListCount = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Flag List Count: ${flagListCount}`);
        data.flags = [];
        for (let i = 0; i < flagListCount; i++) {
            if (offset + 4 > buffer.length) {
                logger.warn(`Buffer too small for Flag ${i}. Skipping remaining flags.`);
                break;
            }
            const category = buffer.readUInt16LE(offset);
            offset += 2;
            const value = buffer.readUInt16LE(offset);
            offset += 2;
            data.flags.push({ category, value });
            // logger.debug(`Flag[${i}]: Category=0x${category.toString(16)}, Value=0x${value.toString(16)}`);
        }

        // --- Fields after FlagList (Based on Fandom example order) ---
        if (offset + 4 > buffer.length) throw new Error('Buffer too small for SimoleonPrice');
        data.simoleonPrice = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Simoleon Price: ${data.simoleonPrice}`);

        if (offset + 4 > buffer.length) throw new Error('Buffer too small for PartTitleKey');
        data.partTitleKey = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Part Title Key: 0x${data.partTitleKey.toString(16)}`);

        if (offset + 4 > buffer.length) throw new Error('Buffer too small for PartDescriptionKey');
        data.partDescriptionKey = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Part Description Key: 0x${data.partDescriptionKey.toString(16)}`);

        if (offset + 1 > buffer.length) throw new Error('Buffer too small for UniqueTextureSpace');
        data.uniqueTextureSpace = buffer.readUInt8(offset);
        offset += 1;
        // logger.debug(`Unique Texture Space: ${data.uniqueTextureSpace}`);

        if (offset + 4 > buffer.length) throw new Error('Buffer too small for BodyType');
        data.bodyType = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`Body Type: 0x${data.bodyType.toString(16)}`);

        // NOTE: Skipping Unused1 (4 bytes) here based on summary, but DBPFReader.js doesn't show it.
        if (offset + 4 > buffer.length) throw new Error('Buffer too small for Unused1');
        offset += 4;

        if (offset + 4 > buffer.length) throw new Error('Buffer too small for AgeGender');
        data.ageGender = buffer.readUInt32LE(offset);
        offset += 4;
        // logger.debug(`AgeGender: 0x${data.ageGender.toString(16)}`);

        // Conditional skip based on DBPFReader.js logic
        // logger.debug(`Offset before conditional skip: ${offset}`);
        if (data.version === undefined) {
             throw new Error('Cannot determine conditional skip amount: Version is undefined.');
        } else if (data.version >= 0x25) {
            // logger.debug(`Version >= 0x25, skipping 16 bytes`);
            if (offset + 16 > buffer.length) throw new Error('Buffer too small for 16-byte skip');
            offset += 16;
        } else if (data.version === 0x20) {
            // logger.debug(`Version == 0x20, skipping 6 bytes`);
            if (offset + 6 > buffer.length) throw new Error('Buffer too small for 6-byte skip');
            offset += 6;
        } else {
            // logger.debug(`Version < 0x25 and != 0x20, skipping 2 bytes`);
            if (offset + 2 > buffer.length) throw new Error('Buffer too small for 2-byte skip');
            offset += 2;
        }
        // logger.debug(`Offset after conditional skip: ${offset}`);

        // --- SwatchColorList ---
        if (offset + 4 > buffer.length) throw new Error('Buffer too small for SwatchColorListCount');
        // Read Swatch Color List Count as UInt8 based on DBPFReader.js
        if (offset + 1 > buffer.length) throw new Error('Buffer too small for SwatchColorListCount (UInt8)');
        const swatchColorListCount = buffer.readUInt8(offset);
        offset += 1;
        // logger.debug(`Swatch Color List Count: ${swatchColorListCount} (Read as UInt8)`);
        data.swatchColors = [];
        for (let i = 0; i < swatchColorListCount; i++) {
            if (offset + 4 > buffer.length) throw new Error(`Buffer too small for Swatch Color ${i}`);
            const colorInt = buffer.readUInt32LE(offset);
            offset += 4;
            // Format as hex color string (e.g., #RRGGBB), assuming alpha is the highest byte (ARGB) or lowest (RGBA) - needs verification, using RGBA for now
            // const alpha = (colorInt >> 24) & 0xFF; // If ARGB
            const blue = (colorInt >> 16) & 0xFF;
            const green = (colorInt >> 8) & 0xFF;
            const red = colorInt & 0xFF;
            const hexColor = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`.toUpperCase();
            data.swatchColors.push(hexColor);
            // logger.debug(`Swatch[${i}]: ${hexColor}`);
        }

        // --- Post-Swatch Conditional Skips (from DBPFReader.js) ---
        // logger.debug(`Offset before post-swatch conditional skip: ${offset}`);
        if (data.version === undefined) {
             throw new Error('Cannot determine post-swatch conditional skip amount: Version is undefined.');
        } else if (data.version === 0x1A) {
            // logger.debug(`Version == 0x1A, skipping 9 bytes`);
            if (offset + 9 > buffer.length) throw new Error('Buffer too small for 9-byte skip');
            offset += 9;
        } else if (data.version <= 0x1C) {
            // logger.debug(`Version <= 0x1C, skipping 16 bytes`);
            if (offset + 16 > buffer.length) throw new Error('Buffer too small for 16-byte skip');
            offset += 16;
        } else if (data.version === 0x1E || data.version === 0x1F) {
            // logger.debug(`Version == 0x1E or 0x1F, skipping 29 bytes`);
            if (offset + 29 > buffer.length) throw new Error('Buffer too small for 29-byte skip');
            offset += 29;
        } else if (data.version <= 0x24) {
            // logger.debug(`Version <= 0x24, skipping 16 bytes`);
            if (offset + 16 > buffer.length) throw new Error('Buffer too small for 16-byte skip');
            offset += 16;
        } else if (data.version <= 0x2A) {
            // logger.debug(`Version 0x25-0x2A, skipping 0 bytes`);
            // No skip needed
        } else { // Version > 0x2A
            // logger.debug(`Version > 0x2A, skipping 16 bytes`);
            if (offset + 16 > buffer.length) throw new Error('Buffer too small for 16-byte skip');
            offset += 16;
        }
        // logger.debug(`Offset after post-swatch conditional skip: ${offset}`);

        // --- LODBlockList (Attempting read immediately after post-swatch skips) ---
        // Read LOD Block List Count as UInt8 (Hypothesis based on DBPFReader.js)
        if (offset + 1 > buffer.length) throw new Error('Buffer too small for LODBlockListCount (UInt8)');
        const lodBlockListCount = buffer.readUInt8(offset);
        offset += 1; // Increment by 1 byte
        // logger.debug(`LOD Block List Count: ${lodBlockListCount}`);
        data.lodBlocks = [];

        for (let i = 0; i < lodBlockListCount; i++) {
            const lodBlock: NonNullable<ParsedCaspData['lodBlocks']>[number] = {
                level: 0,
                assets: [],
                keyIndices: []
            };

            if (offset + 1 > buffer.length) throw new Error(`Buffer too small for LOD ${i} Level`);
            lodBlock.level = buffer.readUInt8(offset);
            offset += 1;
            // logger.debug(`  LOD[${i}] Level: ${lodBlock.level}`);

            // Read AssetListCount as UInt8 (Previous fix)
            if (offset + 1 > buffer.length) throw new Error(`Buffer too small for LOD ${i} AssetListCount`);
            const lodAssetListCount = buffer.readUInt8(offset); // <-- Read as UInt8 (1 byte)
            offset += 1; // Adjust offset increment
            // logger.debug(`  LOD[${i}] Asset Count: ${lodAssetListCount} at offset ${offset - 4}`); // Adjust log offset

            for (let j = 0; j < lodAssetListCount; j++) {
                // logger.debug(`    Attempting read for Asset[${j}] at offset ${offset}. Need 12 bytes. Buffer length: ${buffer.length}`); // Moved inside loop
                if (offset + 12 > buffer.length) throw new Error(`Buffer too small for LOD ${i} Asset ${j}`);
                const castShadow = buffer.readUInt32LE(offset);
                offset += 4;
                const sorting = buffer.readUInt32LE(offset);
                offset += 4;
                const specLevel = buffer.readUInt32LE(offset);
                offset += 4;
                lodBlock.assets.push({ castShadow, sorting, specLevel });
                // logger.debug(`    Asset[${j}]: Shadow=0x${castShadow.toString(16)}, Sort=0x${sorting.toString(16)}, Spec=0x${specLevel.toString(16)}`);
            }

            // Read KeyListCount first
            // Read KeyListCount as UInt8 (Previous fix)
            if (offset + 1 > buffer.length) throw new Error(`Buffer too small for LOD ${i} KeyListCount`);
            const lodKeyListCount = buffer.readUInt8(offset); // <-- Read as UInt8 (1 byte)
            offset += 1; // Adjust offset increment
            // logger.debug(`  LOD[${i}] Key Index Count: ${lodKeyListCount} at offset ${offset - 4}`); // Adjust log offset

            for (let k = 0; k < lodKeyListCount; k++) {
                if (offset + 1 > buffer.length) throw new Error(`Buffer too small for LOD ${i} Key Index ${k}`);
                const keyIndex = buffer.readUInt8(offset);
                offset += 1;
                lodBlock.keyIndices.push(keyIndex);
                // logger.debug(`    KeyIndex[${k}]: ${keyIndex}`);
            }
            // Read the 'Unused' DWORD after KeyIndices
            if (offset + 4 > buffer.length) throw new Error(`Buffer too small for LOD ${i} Unused`);
            lodBlock.unused = buffer.readUInt32LE(offset);
            offset += 4;
            // logger.debug(`  LOD[${i}] Unused: 0x${lodBlock.unused.toString(16)}`);
            // logger.debug(`  LOD[${i}] Unused: 0x${lodBlock.unused.toString(16)}`);

            data.lodBlocks.push(lodBlock);
        }

logger.info(`Successfully parsed CASP fields up to LOD Blocks at offset ${offset}`);

// --- TGI Keys (Moved after LOD Blocks) ---
// Read BuffResKey (Type: uint32, Group: uint32, Instance: uint64)
if (offset + 16 > buffer.length) throw new Error('Buffer too small for BuffResKey');
data.buffKey = {
    type: buffer.readUInt32LE(offset),
    group: buffer.readUInt32LE(offset + 4),
    instance: buffer.readBigUInt64LE(offset + 8)
};
offset += 16;
// logger.debug(`BuffResKey: T=0x${data.buffKey.type.toString(16)}, G=0x${data.buffKey.group.toString(16)}, I=0x${data.buffKey.instance.toString(16)}`);

// Read VarientThumbnailKey
if (offset + 16 > buffer.length) throw new Error('Buffer too small for VarientThumbnailKey');
data.variantThumbnailKey = {
    type: buffer.readUInt32LE(offset),
    group: buffer.readUInt32LE(offset + 4),
    instance: buffer.readBigUInt64LE(offset + 8)
};
offset += 16;
// logger.debug(`VarientThumbnailKey: T=0x${data.variantThumbnailKey.type.toString(16)}, G=0x${data.variantThumbnailKey.group.toString(16)}, I=0x${data.variantThumbnailKey.instance.toString(16)}`);

// Read NakedKey
if (offset + 16 > buffer.length) throw new Error('Buffer too small for NakedKey');
data.nakedKey = {
    type: buffer.readUInt32LE(offset),
    group: buffer.readUInt32LE(offset + 4),
    instance: buffer.readBigUInt64LE(offset + 8)
};
offset += 16;
// logger.debug(`NakedKey: T=0x${data.nakedKey.type.toString(16)}, G=0x${data.nakedKey.group.toString(16)}, I=0x${data.nakedKey.instance.toString(16)}`);

// Read ParentKey
if (offset + 16 > buffer.length) throw new Error('Buffer too small for ParentKey');
data.parentKey = {
    type: buffer.readUInt32LE(offset),
    group: buffer.readUInt32LE(offset + 4),
    instance: buffer.readBigUInt64LE(offset + 8)
};
offset += 16;
// logger.debug(`ParentKey: T=0x${data.parentKey.type.toString(16)}, G=0x${data.parentKey.group.toString(16)}, I=0x${data.parentKey.instance.toString(16)}`);

// --- Read Sort Layer & Padding (Moved after LOD Blocks) ---
if (offset + 2 > buffer.length) throw new Error('Buffer too small for SortLayer');
data.sortLayer = buffer.readUInt16LE(offset);
offset += 2;
// logger.debug(`Sort Layer: ${data.sortLayer}`);

// Skip 2 bytes of padding
if (offset + 2 > buffer.length) throw new Error('Buffer too small for Padding after SortLayer');
offset += 2;
// logger.debug(`Skipped 2 bytes padding, new offset: ${offset}`);

// --- SlotKeyList ---
// logger.debug(`Attempting to parse SlotKeyList at offset ${offset}`);
if (offset + 1 > buffer.length) throw new Error('Buffer too small for SlotKeyListCount');
const slotKeyListCount = buffer.readUInt8(offset);
offset += 1;
// logger.debug(`SlotKeyListCount: ${slotKeyListCount}`);

data.slotKeys = [];
for (let i = 0; i < slotKeyListCount; i++) {
    if (offset + 16 > buffer.length) throw new Error(`Buffer too small for SlotKey ${i}`);
    const type = buffer.readUInt32LE(offset);
    const group = buffer.readUInt32LE(offset + 4);
    const instance = buffer.readBigUInt64LE(offset + 8);
    data.slotKeys.push({ type, group, instance });
    offset += 16;
    // logger.debug(`SlotKey[${i}]: T=0x${type.toString(16)}, G=0x${group.toString(16)}, I=0x${instance.toString(16)}`);
}

// --- TGI Indices and TGI List (Attempted Parsing) ---
logger.warn('Attempting to parse TGI Indices and TGI List (UNVERIFIED).');

try {
  // logger.debug(`Attempting to parse TGI Indices and TGI List. TGI List Offset: ${data.tgiListOffset}`);

  if (!data.tgiListOffset) {
    throw new Error('TGI List Offset is undefined.');
  }

  // --- TGI Indices ---
  const tgiIndicesStart = offset; // Assuming TGI Indices start at current offset
  // logger.debug(`TGI Indices Start: ${tgiIndicesStart}`);
  const tgiIndicesLength = data.tgiListOffset - tgiIndicesStart;
  // logger.debug(`TGI Indices Length: ${tgiIndicesLength}`);

  if (tgiIndicesStart < 0 || tgiIndicesLength <= 0 || tgiIndicesStart + tgiIndicesLength > buffer.length) {
    logger.warn(`Invalid TGI Indices range. Skipping TGI Indices parsing. Start: ${tgiIndicesStart}, Length: ${tgiIndicesLength}, BufferLength: ${buffer.length}`);
  } else {
    data.tgiIndices = [];
    for (let i = 0; i < tgiIndicesLength; i++) {
      if (offset + 1 > buffer.length) throw new Error(`Buffer too small for TGI Index ${i}`);
      const tgiIndex = buffer.readUInt8(tgiIndicesStart + i);
      data.tgiIndices.push(tgiIndex);
      // logger.debug(`TGI Index[${i}]: ${tgiIndex} at offset ${tgiIndicesStart + i}`);
    }
  }

  // --- TGI List ---
  if (data.tgiListOffset < 0 || data.tgiListOffset + 1 > buffer.length) {
    logger.warn('Invalid TGI List Offset. Skipping TGI List parsing.');
  } else {
    const tgiListCount = buffer.readUInt8(data.tgiListOffset);
    // logger.debug(`TGI List Count: ${tgiListCount} at offset ${data.tgiListOffset}`);

    data.tgiList = [];
    let tgiOffset = data.tgiListOffset + 1;

    for (let i = 0; i < tgiListCount; i++) {
      if (tgiOffset + 16 > buffer.length) {
        logger.warn(`Buffer too small for TGI List entry ${i}. Skipping.`);
        break;
      }
      const instance = buffer.readBigUInt64LE(tgiOffset);
      const group = buffer.readUInt32LE(tgiOffset + 8);
      const type = buffer.readUInt32LE(tgiOffset + 12);
      data.tgiList.push({ type, group, instance });
      // logger.debug(`TGI List[${i}]: T=0x${type.toString(16)}, G=0x${group.toString(16)}, I=0x${instance.toString(16)} at offset ${tgiOffset}`);
      tgiOffset += 16;
    }
  }
} catch (e: any) {
  logger.warn(`Error parsing TGI Indices and TGI List: ${e.message}`);
}

        logger.info(`Potentially finished parsing known fields at offset ${offset}`);

        return data; // Return the parsed data if successful

    } catch (error: any) {
        // Log the specific error encountered within parseCasp
        logger.error(`Error parsing CASP buffer at offset ${offset}: ${error.message}`, error);
        // Return null on any parsing error
        return null;
    }
}
