import { Logger } from '../../../../utils/logging/logger.js';
import { ParsedSimData } from './simDataParser.js';
import { ResourceMetadata } from '../../../../types/resource/interfaces.js';

const log = new Logger('SimDataSchemaAnalyzer');

// Known schema inheritance patterns in Sims 4
const KNOWN_SCHEMA_PARENTS = new Set([
    'Object', 'Sim', 'Trait', 'Buff', 'Interaction', 'Recipe', 'Career',
    'Aspiration', 'Lot', 'Venue', 'Situation', 'Relationship', 'Skill',
    'Commodity', 'Statistic', 'Mood', 'Reward', 'Tutorial', 'Objective',
    'Animation', 'Posture', 'Walkstyle', 'Outfit', 'CAS', 'Household'
]);

// Schema categories for better organization
const SCHEMA_CATEGORIES: Record<string, string[]> = {
    'Sim': ['Sim', 'SimData', 'SimInfo', 'SimPersonality', 'SimTrait'],
    'Object': ['Object', 'ObjectData', 'ObjectState', 'ObjectComponent'],
    'Gameplay': ['Trait', 'Buff', 'Interaction', 'Recipe', 'Career', 'Aspiration', 'Skill'],
    'World': ['Lot', 'Venue', 'Terrain', 'World', 'Region', 'Neighborhood'],
    'Social': ['Relationship', 'Sentiment', 'RelationshipBit', 'RelationshipTrack'],
    'Animation': ['Animation', 'Posture', 'Walkstyle', 'Reaction', 'Facial'],
    'UI': ['UI', 'HUD', 'CAS', 'Interface', 'Menu', 'Dialog'],
    'System': ['Manager', 'Service', 'System', 'Handler', 'Controller']
};

// Critical gameplay column patterns
const CRITICAL_COLUMN_PATTERNS = [
    'multiplier', 'chance', 'probability', 'duration', 'cost', 'value',
    'weight', 'priority', 'threshold', 'score', 'buff', 'motive',
    'skill', 'trait', 'stat', 'tuning', 'level', 'gain', 'decay',
    'cooldown', 'timer', 'interval', 'delay', 'speed', 'rate'
];

// Column purpose categories
const COLUMN_PURPOSE_CATEGORIES: Record<string, string[]> = {
    'Identification': ['id', 'key', 'name', 'instance', 'reference', 'ref', 'guid'],
    'Gameplay': ['value', 'score', 'weight', 'priority', 'threshold', 'multiplier'],
    'Visual': ['color', 'texture', 'model', 'visual', 'appearance', 'icon'],
    'Audio': ['sound', 'audio', 'music', 'voice', 'sfx'],
    'Behavior': ['behavior', 'action', 'reaction', 'response', 'trigger'],
    'State': ['state', 'status', 'condition', 'mode', 'flag'],
    'Timing': ['time', 'duration', 'cooldown', 'timer', 'interval', 'delay'],
    'Relationship': ['relationship', 'social', 'sentiment', 'opinion'],
    'Statistic': ['stat', 'skill', 'trait', 'buff', 'motive', 'need'],
    'Resource': ['resource', 'tgi', 'key', 'reference', 'ref']
};

/**
 * Analyzes SimData schemas to extract detailed information about their structure,
 * relationships, and purpose.
 */
export class SimDataSchemaAnalyzer {
    private logger: Logger;

    constructor(logger?: Logger) {
        this.logger = logger || log;
    }

    /**
     * Analyzes a SimData schema to extract detailed information.
     * @param simData The parsed SimData object
     * @returns A partial ResourceMetadata object with schema analysis results
     */
    public analyzeSchema(simData: ParsedSimData): Partial<ResourceMetadata> {
        const metadata: Partial<ResourceMetadata> = {};

        if (!simData.schema) {
            this.logger.warn('No schema found in SimData');
            return metadata;
        }

        try {
            // Basic schema information
            const schemaName = simData.schema.name || 'Unknown';
            metadata.simDataSchemaName = schemaName;
            metadata.simDataSchemaId = simData.schema.schemaId?.toString();
            metadata.simDataSchemaHash = simData.schema.hash?.toString(16);
            metadata.simDataVersion = simData.version;
            metadata.simDataFlags = simData.flags;

            // Analyze schema inheritance
            this.analyzeSchemaInheritance(simData.schema.name, metadata);

            // Analyze schema category
            this.analyzeSchemaCategory(simData.schema.name, metadata);

            // Analyze columns
            if (simData.schema.columns) {
                this.analyzeSchemaColumns(simData.schema.columns, metadata);
            }

            // Calculate schema complexity
            this.calculateSchemaComplexity(simData.schema, metadata);

            return metadata;
        } catch (error) {
            this.logger.error(`Error analyzing SimData schema: ${error}`);
            return metadata;
        }
    }

    /**
     * Analyzes schema inheritance patterns.
     * @param schemaName The name of the schema
     * @param metadata The metadata object to update
     */
    private analyzeSchemaInheritance(schemaName: string, metadata: Partial<ResourceMetadata>): void {
        if (!schemaName) return;

        // Check for underscore pattern (Parent_Child)
        if (schemaName.includes('_')) {
            const parts = schemaName.split('_');
            const potentialParent = parts[0];

            // Verify if this is a known parent schema
            if (KNOWN_SCHEMA_PARENTS.has(potentialParent)) {
                metadata.simDataSchemaInheritance = true;
                metadata.simDataSchemaParent = potentialParent;
                metadata.simDataSchemaChild = parts.slice(1).join('_');
            }
        }

        // Check for camelCase pattern (ParentChild)
        for (const parent of KNOWN_SCHEMA_PARENTS) {
            if (schemaName.startsWith(parent) && schemaName !== parent) {
                const remainder = schemaName.substring(parent.length);
                // Make sure the remainder starts with an uppercase letter (indicating camelCase)
                if (remainder.length > 0 && remainder[0] === remainder[0].toUpperCase()) {
                    metadata.simDataSchemaInheritance = true;
                    metadata.simDataSchemaParent = parent;
                    metadata.simDataSchemaChild = remainder;
                    break;
                }
            }
        }
    }

    /**
     * Analyzes the schema category based on its name.
     * @param schemaName The name of the schema
     * @param metadata The metadata object to update
     */
    private analyzeSchemaCategory(schemaName: string, metadata: Partial<ResourceMetadata>): void {
        if (!schemaName) return;

        // Check each category for matching patterns
        for (const [category, patterns] of Object.entries(SCHEMA_CATEGORIES)) {
            for (const pattern of patterns) {
                if (schemaName.includes(pattern)) {
                    metadata.simDataSchemaCategory = category;
                    return;
                }
            }
        }

        // Default category if no match found
        metadata.simDataSchemaCategory = 'Miscellaneous';
    }

    /**
     * Analyzes schema columns to extract detailed information.
     * @param columns The schema columns
     * @param metadata The metadata object to update
     */
    private analyzeSchemaColumns(columns: any[], metadata: Partial<ResourceMetadata>): void {
        metadata.simDataColumnCount = columns.length;

        // Create a summary of column types
        const columnTypes: Record<string, number> = {};
        const columnPurposes: Record<string, string> = {};
        const criticalGameplayColumns: string[] = [];
        const resourceKeyColumns: string[] = [];

        for (const column of columns) {
            // Track column types
            const typeName = this.getDataTypeName(column.type);
            columnTypes[typeName] = (columnTypes[typeName] || 0) + 1;

            // Identify column purpose
            const purpose = this.identifyColumnPurpose(column.name, column.type);
            columnPurposes[column.name] = purpose;

            // Identify critical gameplay columns
            if (this.isCriticalGameplayColumn(column.name)) {
                criticalGameplayColumns.push(column.name);
            }

            // Identify resource key columns
            if (column.type === 20 || this.isResourceKeyColumn(column.name)) {
                resourceKeyColumns.push(column.name);
            }
        }

        // Add column names for better understanding (limit length to avoid huge strings)
        const columnNames = columns.map(col => col.name).join(', ');
        metadata.simDataColumnNames = columnNames.length > 500 ?
            columnNames.substring(0, 497) + '...' : columnNames;

        // Add analysis results to metadata
        metadata.simDataColumnTypes = JSON.stringify(columnTypes);
        metadata.simDataColumnPurposes = JSON.stringify(columnPurposes);
        metadata.simDataCriticalGameplayColumns = JSON.stringify(criticalGameplayColumns);
        metadata.simDataResourceKeyColumns = JSON.stringify(resourceKeyColumns);
    }

    /**
     * Calculates the complexity of a schema based on various factors.
     * @param schema The schema to analyze
     * @param metadata The metadata object to update
     */
    private calculateSchemaComplexity(schema: any, metadata: Partial<ResourceMetadata>): void {
        const complexityFactors = [
            schema.columns.length * 2, // More columns = more complex
            metadata.simDataSchemaInheritance ? 10 : 0, // Schema inheritance adds complexity
            Object.keys(JSON.parse(metadata.simDataCriticalGameplayColumns || '[]')).length * 5, // Critical gameplay columns add complexity
            Object.keys(JSON.parse(metadata.simDataResourceKeyColumns || '[]')).length * 3, // Resource key columns add complexity
        ];

        // Calculate raw score and normalize to 0-100 range
        const rawComplexityScore = complexityFactors.reduce((sum, factor) => sum + factor, 0);
        const normalizedScore = Math.min(100, Math.max(0, Math.round(rawComplexityScore / 2)));

        metadata.simDataSchemaComplexity = normalizedScore;
    }

    /**
     * Determines if a column is a critical gameplay column.
     * @param columnName The name of the column
     * @returns True if the column is a critical gameplay column
     */
    private isCriticalGameplayColumn(columnName: string): boolean {
        const lowerName = columnName.toLowerCase();
        return CRITICAL_COLUMN_PATTERNS.some(pattern => lowerName.includes(pattern));
    }

    /**
     * Determines if a column is a resource key column.
     * @param columnName The name of the column
     * @returns True if the column is a resource key column
     */
    private isResourceKeyColumn(columnName: string): boolean {
        const lowerName = columnName.toLowerCase();
        return lowerName.includes('key') || 
               lowerName.includes('reference') || 
               lowerName.includes('ref') || 
               lowerName.includes('resource') || 
               lowerName.includes('tgi');
    }

    /**
     * Identifies the purpose of a column based on its name and type.
     * @param columnName The name of the column
     * @param columnType The type of the column
     * @returns The purpose of the column
     */
    private identifyColumnPurpose(columnName: string, columnType: number): string {
        const lowerName = columnName.toLowerCase();

        // Check for resource key type
        if (columnType === 20) {
            return 'Resource';
        }

        // Check each purpose category
        for (const [purpose, patterns] of Object.entries(COLUMN_PURPOSE_CATEGORIES)) {
            for (const pattern of patterns) {
                if (lowerName.includes(pattern)) {
                    return purpose;
                }
            }
        }

        // Default purpose if no match found
        return 'Unknown';
    }

    /**
     * Gets a human-readable name for a SimData type.
     * @param type The numeric type
     * @returns A human-readable type name
     */
    private getDataTypeName(type: number): string {
        switch (type) {
            case 1: return 'Boolean';
            case 2: return 'Char';
            case 3: return 'Int8';
            case 4: return 'UInt8';
            case 5: return 'Int16';
            case 6: return 'UInt16';
            case 7: return 'Int32';
            case 8: return 'UInt32';
            case 9: return 'Int64';
            case 10: return 'UInt64';
            case 11: return 'Float';
            case 12: return 'String';
            case 13: return 'HashedString';
            case 14: return 'Object';
            case 15: return 'Vector';
            case 16: return 'Float2';
            case 17: return 'Float3';
            case 18: return 'Float4';
            case 19: return 'TableSetReference';
            case 20: return 'ResourceKey';
            case 21: return 'LocalizationKey';
            case 22: return 'VariantList';
            default: return `Type${type}`;
        }
    }
}
