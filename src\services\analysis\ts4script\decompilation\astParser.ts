/**
 * AST Parser for Python source code
 *
 * This service provides functionality to parse Python source code into an Abstract Syntax Tree (AST)
 * and extract useful information from it.
 *
 * Features:
 * - Parse Python source code into AST
 * - Extract classes, functions, imports, and other structures
 * - Identify EA-specific patterns in the code
 * - Map relationships between different code elements
 */

import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { Logger } from '../../../common/logger.js';

/**
 * Python class information extracted from AST
 */
export interface AstClass {
    name: string;
    parentClasses: string[];
    methods: AstFunction[];
    properties: string[];
    decorators: string[];
    docstring?: string;
    startLine: number;
    endLine: number;
    isEAClass: boolean; // Whether this class follows EA patterns
}

/**
 * Python function information extracted from AST
 */
export interface AstFunction {
    name: string;
    parameters: string[];
    decorators: string[];
    isMethod: boolean;
    isStaticMethod: boolean;
    isClassMethod: boolean;
    isProperty: boolean;
    isCommand: boolean;
    isInjection: boolean;
    isEventHandler: boolean;
    docstring?: string;
    startLine: number;
    endLine: number;
    calledFunctions: string[]; // Functions called by this function
    accessedAttributes: string[]; // Attributes accessed by this function
}

/**
 * Python import information extracted from AST
 */
export interface AstImport {
    module: string;
    name: string;
    alias?: string;
    isFromImport: boolean;
    line: number;
}

/**
 * Python tuning reference extracted from AST
 */
export interface AstTuningReference {
    type: string; // e.g., 'INSTANCE', 'CLASS', 'TRAIT', etc.
    group: string;
    instance: string;
    line: number;
}

/**
 * Result of AST parsing
 */
export interface AstParseResult {
    success: boolean;
    classes: AstClass[];
    functions: AstFunction[];
    imports: AstImport[];
    tuningReferences: AstTuningReference[];
    globalVariables: string[];
    constants: Record<string, any>;
    error?: string;
}

/**
 * Options for AST parsing
 */
export interface AstParseOptions {
    tempDir?: string;
    timeout?: number; // milliseconds
    cacheResults?: boolean;
    extractDocstrings?: boolean;
    analyzeEAPatterns?: boolean;
}

/**
 * Service for parsing Python source code into AST
 */
export class AstParser {
    private logger: Logger;
    private tempDir: string;
    private timeout: number;
    private cacheDir: string;
    private pythonAvailable: boolean;

    /**
     * Create a new AstParser
     * @param logger Logger instance
     */
    constructor(logger: Logger) {
        this.logger = logger;
        this.tempDir = os.tmpdir();
        this.timeout = 5000; // 5 seconds default timeout
        this.cacheDir = path.join(os.tmpdir(), 'ts4script_ast_cache');

        // Create cache directory if it doesn't exist
        if (!fs.existsSync(this.cacheDir)) {
            try {
                fs.mkdirSync(this.cacheDir, { recursive: true });
            } catch (error) {
                this.logger.warn(`[AstParser] Failed to create cache directory: ${error}`);
            }
        }

        // Check if Python is available
        this.pythonAvailable = this.checkPythonAvailability();

        if (!this.pythonAvailable) {
            this.logger.warn('[AstParser] Python is not available, AST parsing will be limited');
        }
    }

    /**
     * Check if Python is available in the system
     * @returns true if available, false otherwise
     */
    private checkPythonAvailability(): boolean {
        try {
            const result = spawn('python', ['--version'], { shell: true });
            return result.pid !== undefined;
        } catch (error) {
            return false;
        }
    }

    /**
     * Parse Python source code into AST
     * @param sourceCode Python source code
     * @param moduleName Name of the module (for caching)
     * @param options AST parsing options
     * @returns AST parse result
     */
    public async parseSourceCode(
        sourceCode: string,
        moduleName: string,
        options?: AstParseOptions
    ): Promise<AstParseResult> {
        const opts = this.mergeOptions(options);

        // Check cache first if enabled
        if (opts.cacheResults) {
            const cachedResult = this.checkCache(sourceCode, moduleName);
            if (cachedResult) {
                this.logger.debug(`[AstParser] Using cached AST for ${moduleName}`);
                return cachedResult;
            }
        }

        if (!this.pythonAvailable) {
            return this.fallbackParse(sourceCode);
        }

        try {
            const result = await this.runPythonAstParser(sourceCode, moduleName, opts);

            // Cache successful result if enabled
            if (opts.cacheResults && result.success) {
                this.cacheResult(sourceCode, moduleName, result);
            }

            return result;
        } catch (error) {
            this.logger.error(`[AstParser] Error parsing AST: ${error}`);
            return this.fallbackParse(sourceCode);
        }
    }

    /**
     * Merge default options with provided options
     * @param options User-provided options
     * @returns Merged options
     */
    private mergeOptions(options?: AstParseOptions): AstParseOptions {
        return {
            tempDir: options?.tempDir || this.tempDir,
            timeout: options?.timeout || this.timeout,
            cacheResults: options?.cacheResults !== undefined ? options.cacheResults : true,
            extractDocstrings: options?.extractDocstrings !== undefined ? options.extractDocstrings : true,
            analyzeEAPatterns: options?.analyzeEAPatterns !== undefined ? options.analyzeEAPatterns : true
        };
    }

    /**
     * Run Python AST parser on the source code
     * @param sourceCode Python source code
     * @param moduleName Name of the module
     * @param options AST parsing options
     * @returns AST parse result
     */
    private async runPythonAstParser(
        sourceCode: string,
        moduleName: string,
        options: AstParseOptions
    ): Promise<AstParseResult> {
        const tempFilePath = path.join(options.tempDir, `${moduleName}_${Date.now()}.py`);
        const scriptPath = path.join(__dirname, 'python', 'ast_parser.py');

        try {
            // Write source code to temporary file
            fs.writeFileSync(tempFilePath, sourceCode);

            // Prepare command arguments
            const args = [
                scriptPath,
                tempFilePath,
                '--json'
            ];

            if (options.analyzeEAPatterns) {
                args.push('--ea-patterns');
            }

            if (options.extractDocstrings) {
                args.push('--docstrings');
            }

            // Run Python AST parser
            return new Promise<AstParseResult>((resolve) => {
                const process = spawn('python', args, { shell: true });

                let stdout = '';
                let stderr = '';
                let timeout: NodeJS.Timeout | null = null;

                // Set timeout
                if (options.timeout) {
                    timeout = setTimeout(() => {
                        process.kill();
                        resolve({
                            success: false,
                            classes: [],
                            functions: [],
                            imports: [],
                            tuningReferences: [],
                            globalVariables: [],
                            constants: {},
                            error: `AST parsing timed out after ${options.timeout}ms`
                        });
                    }, options.timeout);
                }

                // Collect stdout
                process.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                // Collect stderr
                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                // Handle process completion
                process.on('close', (code) => {
                    if (timeout) {
                        clearTimeout(timeout);
                    }

                    // Clean up temporary file
                    try {
                        fs.unlinkSync(tempFilePath);
                    } catch (error) {
                        this.logger.warn(`[AstParser] Failed to delete temporary file: ${error}`);
                    }

                    if (code === 0 && stdout.trim()) {
                        try {
                            // Parse JSON output
                            const result = JSON.parse(stdout) as AstParseResult;
                            resolve(result);
                        } catch (error) {
                            resolve({
                                success: false,
                                classes: [],
                                functions: [],
                                imports: [],
                                tuningReferences: [],
                                globalVariables: [],
                                constants: {},
                                error: `Failed to parse AST result: ${error.message}`
                            });
                        }
                    } else {
                        // Fallback to regex-based parsing
                        const fallbackResult = this.fallbackParse(sourceCode);
                        fallbackResult.error = stderr || `Python AST parser exited with code ${code}`;
                        resolve(fallbackResult);
                    }
                });

                // Handle process error
                process.on('error', (error) => {
                    if (timeout) {
                        clearTimeout(timeout);
                    }

                    // Clean up temporary file
                    try {
                        fs.unlinkSync(tempFilePath);
                    } catch (fileError) {
                        this.logger.warn(`[AstParser] Failed to delete temporary file: ${fileError}`);
                    }

                    // Fallback to regex-based parsing
                    const fallbackResult = this.fallbackParse(sourceCode);
                    fallbackResult.error = `Python AST parser error: ${error.message}`;
                    resolve(fallbackResult);
                });
            });
        } catch (error) {
            // Clean up temporary file
            try {
                if (fs.existsSync(tempFilePath)) {
                    fs.unlinkSync(tempFilePath);
                }
            } catch (fileError) {
                this.logger.warn(`[AstParser] Failed to delete temporary file: ${fileError}`);
            }

            // Fallback to regex-based parsing
            const fallbackResult = this.fallbackParse(sourceCode);
            fallbackResult.error = `Failed to run Python AST parser: ${error.message}`;
            return fallbackResult;
        }
    }

    /**
     * Fallback parsing when Python is not available
     * Uses regex-based parsing to extract basic information
     * @param sourceCode Python source code
     * @returns AST parse result with limited information
     */
    private fallbackParse(sourceCode: string): AstParseResult {
        const classes: AstClass[] = [];
        const functions: AstFunction[] = [];
        const imports: AstImport[] = [];
        const tuningReferences: AstTuningReference[] = [];
        const globalVariables: string[] = [];
        const constants: Record<string, any> = {};

        try {
            // Extract classes using regex
            const classRegex = /class\s+(\w+)(?:\(([^)]*)\))?:/g;
            let classMatch;
            while ((classMatch = classRegex.exec(sourceCode)) !== null) {
                const className = classMatch[1];
                const parentClasses = classMatch[2] ?
                    classMatch[2].split(',').map(p => p.trim()) :
                    [];

                // Find class end (indentation level)
                const classStartPos = classMatch.index;
                const classStartLine = this.getLineNumber(sourceCode, classStartPos);
                const classEndLine = this.findBlockEnd(sourceCode, classStartLine);

                classes.push({
                    name: className,
                    parentClasses,
                    methods: [],
                    properties: [],
                    decorators: [],
                    startLine: classStartLine,
                    endLine: classEndLine,
                    isEAClass: false
                });
            }

            // Extract functions using regex
            const functionRegex = /def\s+(\w+)\s*\(([^)]*)\):/g;
            let functionMatch;
            while ((functionMatch = functionRegex.exec(sourceCode)) !== null) {
                const functionName = functionMatch[1];
                const parameters = functionMatch[2] ?
                    functionMatch[2].split(',').map(p => p.trim()) :
                    [];

                // Find function end (indentation level)
                const functionStartPos = functionMatch.index;
                const functionStartLine = this.getLineNumber(sourceCode, functionStartPos);
                const functionEndLine = this.findBlockEnd(sourceCode, functionStartLine);

                // Check if it's a method (inside a class)
                const isMethod = classes.some(cls =>
                    functionStartLine > cls.startLine &&
                    functionEndLine < cls.endLine
                );

                // Check for EA-specific patterns
                const functionCode = sourceCode.substring(
                    functionMatch.index,
                    this.getPositionFromLine(sourceCode, functionEndLine + 1)
                );

                const isCommand = /Command\s*\(/.test(functionCode);
                const isInjection = /inject\s*\(/.test(functionCode) || /injected\s*\(/.test(functionCode);
                const isEventHandler = /register_event_handler\s*\(/.test(functionCode) || /on_event\s*\(/.test(functionCode);

                functions.push({
                    name: functionName,
                    parameters,
                    decorators: [],
                    isMethod,
                    isStaticMethod: false,
                    isClassMethod: false,
                    isProperty: false,
                    isCommand,
                    isInjection,
                    isEventHandler,
                    startLine: functionStartLine,
                    endLine: functionEndLine,
                    calledFunctions: [],
                    accessedAttributes: []
                });

                // If it's a method, add it to the class
                if (isMethod) {
                    for (const cls of classes) {
                        if (functionStartLine > cls.startLine && functionEndLine < cls.endLine) {
                            cls.methods.push(functions[functions.length - 1]);
                            break;
                        }
                    }
                }
            }

            // Extract imports using regex
            const importRegex = /^(?:from\s+(\S+)\s+)?import\s+(.+?)(?:\s+as\s+(\S+))?$/gm;
            let importMatch;
            while ((importMatch = importRegex.exec(sourceCode)) !== null) {
                const fromModule = importMatch[1] || '';
                const importNames = importMatch[2].split(',').map(name => name.trim());
                const alias = importMatch[3] || '';
                const line = this.getLineNumber(sourceCode, importMatch.index);

                for (const name of importNames) {
                    imports.push({
                        module: fromModule,
                        name,
                        alias: alias || undefined,
                        isFromImport: !!fromModule,
                        line
                    });
                }
            }

            // Extract global variables
            const globalVarRegex = /^(\w+)\s*=\s*(?!class|def)/gm;
            let globalVarMatch;
            while ((globalVarMatch = globalVarRegex.exec(sourceCode)) !== null) {
                const varName = globalVarMatch[1];
                if (!globalVariables.includes(varName) &&
                    !varName.startsWith('__') &&
                    !classes.some(c => c.name === varName) &&
                    !functions.some(f => f.name === varName)) {
                    globalVariables.push(varName);

                    // Check if it's a constant (all uppercase)
                    if (varName === varName.toUpperCase()) {
                        constants[varName] = '[value not extracted]';
                    }
                }
            }

            // Extract tuning references
            const tuningRegex = /TunableReference\s*\(\s*manager\s*=\s*(\w+)/g;
            let tuningMatch;
            while ((tuningMatch = tuningRegex.exec(sourceCode)) !== null) {
                const type = tuningMatch[1];
                const line = this.getLineNumber(sourceCode, tuningMatch.index);

                tuningReferences.push({
                    type,
                    group: 'unknown',
                    instance: 'unknown',
                    line
                });
            }

            return {
                success: true,
                classes,
                functions,
                imports,
                tuningReferences,
                globalVariables,
                constants
            };
        } catch (error) {
            return {
                success: false,
                classes: [],
                functions: [],
                imports: [],
                tuningReferences: [],
                globalVariables: [],
                constants: {},
                error: `Fallback parsing failed: ${error.message}`
            };
        }
    }

    /**
     * Get line number from character position
     * @param text Source code
     * @param position Character position
     * @returns Line number (1-based)
     */
    private getLineNumber(text: string, position: number): number {
        const textBefore = text.substring(0, position);
        return (textBefore.match(/\n/g) || []).length + 1;
    }

    /**
     * Get character position from line number
     * @param text Source code
     * @param line Line number (1-based)
     * @returns Character position
     */
    private getPositionFromLine(text: string, line: number): number {
        const lines = text.split('\n');
        let position = 0;

        for (let i = 0; i < line - 1 && i < lines.length; i++) {
            position += lines[i].length + 1; // +1 for the newline
        }

        return position;
    }

    /**
     * Find the end line of a block based on indentation
     * @param text Source code
     * @param startLine Start line of the block (1-based)
     * @returns End line of the block (1-based)
     */
    private findBlockEnd(text: string, startLine: number): number {
        const lines = text.split('\n');
        if (startLine > lines.length) {
            return startLine;
        }

        // Get indentation of the block start
        const startLineText = lines[startLine - 1];
        const indentMatch = startLineText.match(/^(\s*)/);
        const blockIndent = indentMatch ? indentMatch[1].length : 0;

        // Find the first line with same or less indentation
        for (let i = startLine; i < lines.length; i++) {
            const line = lines[i];

            // Skip empty lines
            if (line.trim() === '') {
                continue;
            }

            const lineIndentMatch = line.match(/^(\s*)/);
            const lineIndent = lineIndentMatch ? lineIndentMatch[1].length : 0;

            if (lineIndent <= blockIndent && i > startLine) {
                return i;
            }
        }

        return lines.length;
    }

    /**
     * Check cache for AST parse result
     * @param sourceCode Python source code
     * @param moduleName Name of the module
     * @returns Cached AST parse result or null if not found
     */
    private checkCache(sourceCode: string, moduleName: string): AstParseResult | null {
        try {
            // Generate cache key based on source code hash
            const hash = require('crypto').createHash('md5').update(sourceCode).digest('hex');
            const cacheFile = path.join(this.cacheDir, `${moduleName}_${hash}.json`);

            if (fs.existsSync(cacheFile)) {
                const cacheData = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
                return cacheData as AstParseResult;
            }
        } catch (error) {
            this.logger.warn(`[AstParser] Cache check failed: ${error}`);
        }

        return null;
    }

    /**
     * Cache AST parse result
     * @param sourceCode Python source code
     * @param moduleName Name of the module
     * @param result AST parse result
     */
    private cacheResult(sourceCode: string, moduleName: string, result: AstParseResult): void {
        try {
            // Generate cache key based on source code hash
            const hash = require('crypto').createHash('md5').update(sourceCode).digest('hex');
            const cacheFile = path.join(this.cacheDir, `${moduleName}_${hash}.json`);

            fs.writeFileSync(cacheFile, JSON.stringify(result), 'utf8');
        } catch (error) {
            this.logger.warn(`[AstParser] Cache write failed: ${error}`);
        }
    }
}
