/**
 * Enhanced string similarity utilities using the string-comparison library
 */
import * as stringComparison from 'string-comparison';
import { Logger } from '../../utils/logging/logger.js';

// Create instances of the comparison algorithms
const levenshtein = stringComparison.levenshtein;
const jaro = stringComparison.jaro;
const jaroWinkler = stringComparison.jaroWinkler;
const cosine = stringComparison.cosine;
const dice = stringComparison.dice;
const jaccard = stringComparison.jaccard;

// Logger instance
const logger = new Logger('EnhancedStringSimilarity');

/**
 * Algorithm types for string similarity
 */
export enum SimilarityAlgorithm {
    LEVENSHTEIN = 'levenshtein',
    JARO = 'jaro',
    JARO_WINKLER = 'jaroWinkler',
    COSINE = 'cosine',
    DICE = 'dice',
    JACCARD = 'jaccard',
    BEST = 'best'
}

/**
 * Options for string similarity calculation
 */
export interface SimilarityOptions {
    /**
     * The algorithm to use for similarity calculation
     * Default: SimilarityAlgorithm.BEST
     */
    algorithm?: SimilarityAlgorithm;

    /**
     * Whether to normalize strings before comparison (lowercase, trim)
     * Default: true
     */
    normalize?: boolean;

    /**
     * Whether to tokenize strings into words before comparison
     * Only applies to COSINE, DICE, and JACCARD algorithms
     * Default: false
     */
    tokenize?: boolean;

    /**
     * N-gram size for DICE and JACCARD algorithms
     * Default: 2
     */
    ngramSize?: number;
}

/**
 * Calculate the similarity between two strings using the specified algorithm
 * @param str1 First string
 * @param str2 Second string
 * @param options Options for similarity calculation
 * @returns Similarity score between 0.0 and 1.0
 */
export function calculateSimilarity(
    str1: string,
    str2: string,
    options: SimilarityOptions = {}
): number {
    // Set default options
    const algorithm = options.algorithm || SimilarityAlgorithm.BEST;
    const normalize = options.normalize !== false;
    const tokenize = options.tokenize || false;
    const ngramSize = options.ngramSize || 2;

    // Handle edge cases
    if (str1 === str2) {
        return 1.0;
    }
    if (str1.length === 0 || str2.length === 0) {
        return 0.0;
    }

    // Normalize strings if needed
    let s1 = str1;
    let s2 = str2;
    if (normalize) {
        s1 = str1.toLowerCase().trim();
        s2 = str2.toLowerCase().trim();
    }

    // Tokenize strings if needed
    if (tokenize && (algorithm === SimilarityAlgorithm.COSINE ||
                     algorithm === SimilarityAlgorithm.DICE ||
                     algorithm === SimilarityAlgorithm.JACCARD)) {
        s1 = s1.split(/\W+/).filter(word => word.length > 0).join(' ');
        s2 = s2.split(/\W+/).filter(word => word.length > 0).join(' ');
    }

    try {
        // Calculate similarity using the specified algorithm
        let result = 0;

        switch (algorithm) {
            case SimilarityAlgorithm.LEVENSHTEIN:
                result = levenshtein && typeof levenshtein.similarity === 'function' ?
                    levenshtein.similarity(s1, s2) : fallbackSimilarity(s1, s2);
                break;
            case SimilarityAlgorithm.JARO:
                result = jaro && typeof jaro.similarity === 'function' ?
                    jaro.similarity(s1, s2) : fallbackSimilarity(s1, s2);
                break;
            case SimilarityAlgorithm.JARO_WINKLER:
                result = jaroWinkler && typeof jaroWinkler.similarity === 'function' ?
                    jaroWinkler.similarity(s1, s2) : fallbackSimilarity(s1, s2);
                break;
            case SimilarityAlgorithm.COSINE:
                result = cosine && typeof cosine.similarity === 'function' ?
                    cosine.similarity(s1, s2) : fallbackSimilarity(s1, s2);
                break;
            case SimilarityAlgorithm.DICE:
                result = dice && typeof dice.similarity === 'function' ?
                    dice.similarity(s1, s2) : fallbackSimilarity(s1, s2);
                break;
            case SimilarityAlgorithm.JACCARD:
                result = jaccard && typeof jaccard.similarity === 'function' ?
                    jaccard.similarity(s1, s2) : fallbackSimilarity(s1, s2);
                break;
            case SimilarityAlgorithm.BEST:
                result = calculateBestSimilarity(s1, s2, ngramSize);
                break;
            default:
                logger.warn(`Unknown similarity algorithm: ${algorithm}, using BEST instead`);
                result = calculateBestSimilarity(s1, s2, ngramSize);
                break;
        }

        return result;
    } catch (error) {
        logger.error(`Error calculating string similarity: ${error.message || error}`);
        // Fallback to our own implementation
        return fallbackSimilarity(s1, s2);
    }
}

/**
 * Fallback similarity calculation using Levenshtein distance
 * @param str1 First string
 * @param str2 Second string
 * @returns Similarity score between 0.0 and 1.0
 */
function fallbackSimilarity(str1: string, str2: string): number {
    // Handle edge cases
    if (str1 === str2) {
        return 1.0;
    }
    if (str1.length === 0 || str2.length === 0) {
        return 0.0;
    }

    // Calculate Levenshtein distance
    const distance = levenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);

    // Convert distance to similarity (1.0 means identical, 0.0 means completely different)
    return 1.0 - (distance / maxLength);
}

/**
 * Calculate Levenshtein distance between two strings
 * @param str1 First string
 * @param str2 Second string
 * @returns Levenshtein distance
 */
function levenshteinDistance(str1: string, str2: string): number {
    const m = str1.length;
    const n = str2.length;

    // Create a matrix of size (m+1) x (n+1)
    const dp: number[][] = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));

    // Initialize the matrix
    for (let i = 0; i <= m; i++) {
        dp[i][0] = i;
    }
    for (let j = 0; j <= n; j++) {
        dp[0][j] = j;
    }

    // Fill the matrix
    for (let i = 1; i <= m; i++) {
        for (let j = 1; j <= n; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            dp[i][j] = Math.min(
                dp[i - 1][j] + 1,      // deletion
                dp[i][j - 1] + 1,      // insertion
                dp[i - 1][j - 1] + cost // substitution
            );
        }
    }

    // Return the distance
    return dp[m][n];
}

/**
 * Calculate the similarity between two strings using multiple algorithms
 * and return the highest similarity score
 * @param str1 First string
 * @param str2 Second string
 * @param ngramSize N-gram size for DICE and JACCARD algorithms
 * @returns The highest similarity score between 0.0 and 1.0
 */
function calculateBestSimilarity(str1: string, str2: string, ngramSize: number = 2): number {
    try {
        // Calculate similarity using different algorithms
        const similarities: number[] = [];

        // Try each algorithm and add valid results to the array
        if (levenshtein && typeof levenshtein.similarity === 'function') {
            similarities.push(levenshtein.similarity(str1, str2));
        }

        if (jaro && typeof jaro.similarity === 'function') {
            similarities.push(jaro.similarity(str1, str2));
        }

        if (jaroWinkler && typeof jaroWinkler.similarity === 'function') {
            similarities.push(jaroWinkler.similarity(str1, str2));
        }

        if (cosine && typeof cosine.similarity === 'function') {
            similarities.push(cosine.similarity(str1, str2));
        }

        if (dice && typeof dice.similarity === 'function') {
            similarities.push(dice.similarity(str1, str2));
        }

        if (jaccard && typeof jaccard.similarity === 'function') {
            similarities.push(jaccard.similarity(str1, str2));
        }

        // Add our fallback similarity as well
        similarities.push(fallbackSimilarity(str1, str2));

        // Return the highest similarity score
        return similarities.length > 0 ? Math.max(...similarities) : fallbackSimilarity(str1, str2);
    } catch (error) {
        logger.error(`Error calculating best string similarity: ${error.message || error}`);
        // Fallback to our own implementation
        return fallbackSimilarity(str1, str2);
    }
}

/**
 * Calculate the best similarity algorithm for the given strings
 * @param str1 First string
 * @param str2 Second string
 * @returns The best similarity algorithm for the given strings
 */
export function getBestSimilarityAlgorithm(str1: string, str2: string): SimilarityAlgorithm {
    // For very short strings, Jaro-Winkler is usually better
    if (str1.length < 10 && str2.length < 10) {
        return SimilarityAlgorithm.JARO_WINKLER;
    }

    // For very different length strings, Cosine is usually better
    if (Math.abs(str1.length - str2.length) > str1.length * 0.5) {
        return SimilarityAlgorithm.COSINE;
    }

    // For longer strings, Dice is usually better
    if (str1.length > 100 && str2.length > 100) {
        return SimilarityAlgorithm.DICE;
    }

    // Default to Levenshtein
    return SimilarityAlgorithm.LEVENSHTEIN;
}

/**
 * Calculate the similarity between two strings using the best algorithm for the given strings
 * @param str1 First string
 * @param str2 Second string
 * @param options Options for similarity calculation
 * @returns Similarity score between 0.0 and 1.0
 */
export function calculateAdaptiveSimilarity(
    str1: string,
    str2: string,
    options: Omit<SimilarityOptions, 'algorithm'> = {}
): number {
    const algorithm = getBestSimilarityAlgorithm(str1, str2);
    return calculateSimilarity(str1, str2, { ...options, algorithm });
}
