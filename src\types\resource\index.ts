﻿﻿/**
 * Centralized re-export for resource-related types.
 */

// Re-export core resource type enums
export * from './core.js';

// Re-export resource category enums
export * from './enums.js';

// Re-export resource interfaces
export * from './interfaces.js';

// Re-export package-specific types (excluding PackageAnalysisResult)
export type {
  ModCategory,
  PackageMetadata,
  PackageOptions,
  PackageValidationResult,
  PackageStats,
  PackageAnalysisOptions,
  OrganizedPackages,
  PackageResourceEntry,
  PackageResourceMap,
  PackageResourceCollection
} from './Package.js';

// Note: Helper functions are now located in 'src/utils/resource/helpers.ts'
// Note: Types from metadata.ts, resolver.ts, resourceAnalysis.ts, conflicts.ts
// should be imported directly from their specific modules if needed elsewhere.
