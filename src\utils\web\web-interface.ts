import { Server as SocketIOServer } from 'socket.io';
import { Logger } from './logging/logger.js';
import { PackageAnalysisResult } from '../types/analysis/PackageAnalysisResult.js';
import { ConflictResult } from '../types/conflict/ConflictTypes.js';

const logger = new Logger('WebInterface');

export interface WebInterfaceUpdate {
    type: 'progress' | 'results' | 'recommendations' | 'error' | 'mod_files';
    data: {
        current?: number;
        total?: number;
        message?: string;
        status?: 'processing' | 'complete' | 'error';
        conflicts?: ConflictResult[];
        metrics?: {
            total: number;
            conflictsFound: number;
            analysisTime: number;
            resourceCount: number;
            uniqueConflicts: number;
        };
        recommendations?: string[];
        files?: string[];
    };
}

export interface WebInterfaceConfig {
    port: number;
    title: string;
    description: string;
}

export interface WebInterfaceData {
    progress: number;
    status: string;
    results?: PackageAnalysisResult[];
    conflicts?: ConflictResult[];
    recommendations?: string[];
    error?: string;
    modFiles?: string[];
}

export class WebInterface {
    private static instance: WebInterface;
    private config: WebInterfaceConfig;
    private data: WebInterfaceData;
    private io: SocketIOServer | undefined;
    private updateQueue: WebInterfaceUpdate[] = [];
    private isProcessing: boolean = false;

    private constructor(config: WebInterfaceConfig) {
        this.config = config;
        this.data = {
            progress: 0,
            status: 'Initializing...'
        };
    }

    public static getInstance(config: WebInterfaceConfig): WebInterface {
        if (!WebInterface.instance) {
            WebInterface.instance = new WebInterface(config);
        }
        return WebInterface.instance;
    }

    public setSocketIO(io: SocketIOServer): void {
        this.io = io;
        logger.info('Socket.IO server initialized');
    }

    private async processUpdateQueue(): Promise<void> {
        if (this.isProcessing || this.updateQueue.length === 0) {
            return;
        }

        this.isProcessing = true;
        try {
            while (this.updateQueue.length > 0) {
                const update = this.updateQueue.shift();
                if (!update) continue;

                await this.emitUpdate(update);
            }
        } catch (error) {
            logger.error('Error processing update queue:', error);
        } finally {
            this.isProcessing = false;
        }
    }

    private async emitUpdate(update: WebInterfaceUpdate): Promise<void> {
        if (!this.io) {
            logger.warn('Socket.IO not initialized');
            return;
        }

        try {
            switch (update.type) {
                case 'progress':
                    this.io.emit('progress', {
                        current: update.data.current,
                        total: update.data.total,
                        message: update.data.message,
                        status: update.data.status
                    });
                    break;

                case 'results':
                    this.io.emit('results', {
                        conflicts: update.data.conflicts,
                        metrics: update.data.metrics,
                        status: update.data.status
                    });
                    break;

                case 'recommendations':
                    this.io.emit('recommendations', {
                        recommendations: update.data.recommendations,
                        status: update.data.status
                    });
                    break;

                case 'error':
                    this.io.emit('error', {
                        message: update.data.message,
                        status: update.data.status
                    });
                    break;

                case 'mod_files':
                    this.io.emit('mod_files', {
                        files: update.data.files,
                        status: update.data.status
                    });
                    break;
            }
        } catch (error) {
            logger.error('Error emitting update:', error);
        }
    }

    public async update(update: WebInterfaceUpdate): Promise<void> {
        this.updateQueue.push(update);
        await this.processUpdateQueue();
    }

    public async updateProgress(progress: number, status: string, message?: string): Promise<void> {
        await this.update({
            type: 'progress',
            data: {
                current: progress,
                total: 100,
                message,
                status: 'processing'
            }
        });
    }

    public async updateResults(conflicts: ConflictResult[], metrics: any): Promise<void> {
        await this.update({
            type: 'results',
            data: {
                conflicts,
                metrics,
                status: 'complete'
            }
        });
    }

    public async updateRecommendations(recommendations: string[]): Promise<void> {
        await this.update({
            type: 'recommendations',
            data: {
                recommendations,
                status: 'complete'
            }
        });
    }

    public async updateError(error: string): Promise<void> {
        await this.update({
            type: 'error',
            data: {
                message: error,
                status: 'error'
            }
        });
    }

    public async updateModFiles(files: string[]): Promise<void> {
        await this.update({
            type: 'mod_files',
            data: {
                files,
                status: 'complete'
            }
        });
    }
}

let webInterface: WebInterface | undefined;

export function setSocketIO(socketIO: SocketIOServer) {
    if (!webInterface) {
        webInterface = WebInterface.getInstance({
            port: parseInt(process.env.PORT || '3000'),
            title: 'Mod Conflict Scanner',
            description: 'Analyze and detect conflicts in Sims 4 mods'
        });
    }
    webInterface.setSocketIO(socketIO);
}

export async function updateWebInterface(update: WebInterfaceUpdate): Promise<void> {
    if (!webInterface) {
        webInterface = WebInterface.getInstance({
            port: parseInt(process.env.PORT || '3000'),
            title: 'Mod Conflict Scanner',
            description: 'Analyze and detect conflicts in Sims 4 mods'
        });
    }
    await webInterface.update(update);
} 