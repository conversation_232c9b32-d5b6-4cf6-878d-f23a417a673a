/**
 * Stream Manager
 *
 * This module provides utilities for managing streams, including tracking active streams,
 * cleaning up resources, and preventing memory leaks.
 */

import { Readable, Writable, Duplex, Transform, Stream } from 'stream';
import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import { createTrackedEmitter } from '../eventEmitterConfig.js';

// Create a logger for this module
const logger = new Logger('StreamManager');

// Create an event emitter for stream events
const streamEvents = createTrackedEmitter('StreamEvents', 20);

// Track active streams
const activeStreams = new Map<string, Stream>();
let streamCounter = 0;

/**
 * Register a stream with the manager
 * @param stream The stream to register
 * @param name Optional name for the stream (for logging)
 * @returns A unique ID for the stream
 */
export function registerStream(stream: Stream, name?: string): string {
    const streamId = `stream_${++streamCounter}_${Date.now()}_${name || 'unnamed'}`;

    // Store the stream
    activeStreams.set(streamId, stream);

    // Log registration
    logger.debug(`Registered stream: ${streamId}`);

    // Set up automatic cleanup when the stream ends or errors
    const cleanup = () => {
        if (activeStreams.has(streamId)) {
            closeStream(streamId);
        }
    };

    // Add event listeners for automatic cleanup
    stream.once('end', cleanup);
    stream.once('close', cleanup);
    stream.once('error', (err) => {
        logger.error(`Stream error: ${streamId}`, err);
        cleanup();
    });

    // Emit event
    streamEvents.emit('stream:registered', streamId, name);

    return streamId;
}

/**
 * Close and unregister a stream
 * @param streamId The ID of the stream to close
 * @returns True if the stream was found and closed, false otherwise
 */
export function closeStream(streamId: string): boolean {
    const stream = activeStreams.get(streamId);

    if (!stream) {
        logger.warn(`Attempted to close unknown stream: ${streamId}`);
        return false;
    }

    try {
        // Remove all listeners to prevent memory leaks
        stream.removeAllListeners();

        // Close the stream based on its type
        if (stream instanceof Readable || stream instanceof Writable) {
            if (typeof (stream as any).destroy === 'function') {
                (stream as any).destroy();
            } else if (typeof (stream as any).close === 'function') {
                (stream as any).close();
            }
        }

        // Remove from active streams
        activeStreams.delete(streamId);

        // Emit event
        streamEvents.emit('stream:closed', streamId);

        logger.debug(`Closed stream: ${streamId}`);
        return true;
    } catch (error) {
        logger.error(`Error closing stream ${streamId}:`, error);

        // Still remove from active streams to prevent memory leaks
        activeStreams.delete(streamId);

        return false;
    }
}

/**
 * Get a registered stream by ID
 * @param streamId The ID of the stream to get
 * @returns The stream, or undefined if not found
 */
export function getStream(streamId: string): Stream | undefined {
    return activeStreams.get(streamId);
}

/**
 * Get the number of active streams
 * @returns The number of active streams
 */
export function getActiveStreamCount(): number {
    return activeStreams.size;
}

/**
 * Get information about all active streams
 * @returns Array of stream information
 */
export function getActiveStreams(): { id: string, type: string }[] {
    return Array.from(activeStreams.entries()).map(([id, stream]) => {
        let type = 'unknown';
        if (stream instanceof Readable) type = 'readable';
        else if (stream instanceof Writable) type = 'writable';
        else if (stream instanceof Duplex) type = 'duplex';
        else if (stream instanceof Transform) type = 'transform';

        return { id, type };
    });
}

/**
 * Close all active streams
 * @returns The number of streams closed
 */
export function closeAllStreams(): number {
    const streamIds = Array.from(activeStreams.keys());
    let closedCount = 0;

    for (const id of streamIds) {
        if (closeStream(id)) {
            closedCount++;
        }
    }

    logger.info(`Closed ${closedCount} streams`);
    return closedCount;
}

/**
 * Create a readable stream with automatic tracking
 * @param options Stream options
 * @param name Optional name for the stream
 * @returns The created stream and its ID
 */
export function createTrackedReadableStream(
    options?: any,
    name?: string
): { stream: Readable; id: string } {
    const stream = new Readable(options);
    const id = registerStream(stream, name);
    return { stream, id };
}

/**
 * Create a writable stream with automatic tracking
 * @param options Stream options
 * @param name Optional name for the stream
 * @returns The created stream and its ID
 */
export function createTrackedWritableStream(
    options?: any,
    name?: string
): { stream: Writable; id: string } {
    const stream = new Writable(options);
    const id = registerStream(stream, name);
    return { stream, id };
}

/**
 * Subscribe to stream manager events
 * @param event Event name
 * @param listener Event listener
 * @returns Function to unsubscribe
 */
export function subscribeToStreamEvents(
    event: 'stream:registered' | 'stream:closed',
    listener: (...args: any[]) => void
): () => void {
    streamEvents.on(event, listener);
    return () => streamEvents.off(event, listener);
}

/**
 * Check for stream manager event leaks
 */
export function checkForStreamEventLeaks(): void {
    (streamEvents as any).checkForLeaks();
}

/**
 * Initialize the stream manager
 */
export function initializeStreamManager(): void {
    logger.info('Initializing stream manager');

    // Increase max listeners for file streams
    // This is necessary because the S4TK library adds multiple listeners to file streams
    if (process.stdout) {
        process.stdout.setMaxListeners(30);
    }

    if (process.stderr) {
        process.stderr.setMaxListeners(30);
    }

    // Increase max listeners for file streams
    try {
        const fs = require('fs');
        if (fs.ReadStream && fs.ReadStream.prototype) {
            fs.ReadStream.prototype.setMaxListeners(30);
        }
        if (fs.WriteStream && fs.WriteStream.prototype) {
            fs.WriteStream.prototype.setMaxListeners(30);
        }
    } catch (error) {
        logger.warn('Failed to set max listeners for file streams', error);
    }

    // Register process exit handler to clean up streams
    process.on('exit', () => {
        const count = closeAllStreams();
        logger.info(`Cleaned up ${count} streams on exit`);

        // Check for event leaks
        checkForStreamEventLeaks();
    });
}

// Export the stream events emitter
export { streamEvents };
