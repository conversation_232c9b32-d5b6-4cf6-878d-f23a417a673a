import { readFile } from 'fs/promises';
import { basename } from 'path';
import { createHash } from 'crypto';
import { Logger } from '../../../utils/logging/logger.js';
import { PackageRepository } from '../../database/PackageRepository.js';
import { PackageInfo } from '../../../types/database.js';
import fs from 'fs';
import { calculateFileHash, readFileToBuffer } from '../../../utils/file/safeFileStream.js';
import resourcePool from '../../../utils/resource/resourcePoolManager.js';

/**
 * Result of loading a package file.
 */
export interface LoadedPackageInfo {
    packageId: number;
    packageInfo: PackageInfo;
    // fileBuffer is no longer returned in streaming mode
}

/**
 * Handles loading package files and saving initial package information to the database.
 */
export class PackageLoader {
    private packageRepository: PackageRepository;
    private logger: Logger;

    /**
     * Create a new PackageLoader.
     * @param packageRepository The package repository instance.
     * @param logger The logger instance.
     */
    constructor(packageRepository: PackageRepository, logger: Logger) {
        this.packageRepository = packageRepository;
        this.logger = logger;
    }

    /**
     * Reads a package file, calculates its hash and size, saves initial info to the database,
     * and returns the package ID and file buffer.
     * @param filePath Path to the package file.
     * @returns A Promise resolving to the loaded package information.
     */
    public async loadPackage(filePath: string): Promise<LoadedPackageInfo> {
        this.logger.info(`Loading package file: ${filePath}`);

        try {
            // Get file stats first to avoid loading the entire file just for size
            const stats = await fs.promises.stat(filePath);
            const fileSize = stats.size;
            const fileName = basename(filePath);

            // Use the resource pool to calculate the hash
            const fileHash = await resourcePool.submit(`hash_${fileName}`, async () => {
                return calculateFileHash(filePath, 'md5');
            });

            // Prepare package info
            const packageInfo: PackageInfo = {
                name: fileName,
                path: filePath,
                hash: fileHash,
                size: fileSize,
                lastModified: Date.now()
            };

            // Save package info to database using PackageRepository
            const packageId = this.packageRepository.savePackage(packageInfo);

            this.logger.info(`Package info saved to database with ID: ${packageId}`);

            return {
                packageId,
                packageInfo,
                // fileBuffer is no longer returned in streaming mode
            };
        } catch (error: any) {
            this.logger.error(`Error loading package file ${filePath}: ${error.message || error}`);
            throw error;
        }
    }
}