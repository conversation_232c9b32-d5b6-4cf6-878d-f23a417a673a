/**
 * Comprehensive System Tests
 *
 * This module provides comprehensive testing for all core system components:
 * - PackageAnalyzer with all extractors
 * - DatabaseService CRUD operations
 * - ResourceTracker concurrent operations
 * - EnhancedMemoryManager stress testing
 * - ConflictDetector all 4 detectors
 * - Large-scale scenarios (10,000+ resources)
 * - Error handling and recovery
 */

import { DatabaseService } from '../../services/databaseService.js';
import { PackageAnalyzer } from '../../services/analysis/packageAnalyzer.js';
import { createPackageAnalyzer } from '../../services/analysis/packageAnalyzerFactory.js';
import { ConflictDetector } from '../../services/conflict/ConflictDetector.js';
import { EnhancedMemoryManager } from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import { Logger } from '../../utils/logging/logger.js';
import { findPackageFiles } from './fileScanner.js';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Comprehensive test result interface
 */
export interface ComprehensiveTestResult {
    success: boolean;
    testName: string;
    duration: number;
    details: {
        packageAnalyzer?: any;
        databaseService?: any;
        resourceTracker?: any;
        memoryManager?: any;
        conflictDetector?: any;
        extractors?: any;
        largeScale?: any;
        errorHandling?: any;
        performance?: any;
    };
    errors: string[];
    warnings: string[];
    metrics: {
        memoryUsage: any;
        processingTime: number;
        resourcesProcessed: number;
        conflictsDetected: number;
    };
}

/**
 * Test options interface
 */
export interface ComprehensiveTestOptions {
    maxPackages?: number;
    logLevel?: string;
    useInMemoryDatabase?: boolean;
    testMode?: 'minimal' | 'standard' | 'comprehensive' | 'stress';
    enableMemoryProfiling?: boolean;
    enablePerformanceProfiling?: boolean;
    testConcurrency?: boolean;
    testErrorRecovery?: boolean;
}

/**
 * Test PackageAnalyzer with all extractors
 */
export async function testPackageAnalyzerComprehensive(
    modsPath: string,
    options: ComprehensiveTestOptions = {}
): Promise<ComprehensiveTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};
    let metrics: any = {};

    const logger = new Logger('ComprehensivePackageAnalyzerTest');
    const memoryManager = EnhancedMemoryManager.getInstance();
    const resourceTracker = ResourceTracker.getInstance();

    try {
        logger.info('===== COMPREHENSIVE PACKAGE ANALYZER TEST =====');

        // Initialize database
        const databaseService = new DatabaseService(options.useInMemoryDatabase ? ':memory:' : 'test_comprehensive.db');
        await databaseService.initialize();

        // Track database for cleanup
        const testId = Date.now();
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `packageAnalyzerTest_${testId}`,
            async () => await databaseService.close(),
            { id: `db_${testId}`, state: ResourceState.ACTIVE }
        );

        // Create package analyzer
        const packageAnalyzer = createPackageAnalyzer(databaseService, 'streaming');
        await packageAnalyzer.initialize();

        // Test with real package files
        const packageFiles = await findPackageFiles(modsPath, {
            maxFiles: options.maxPackages || 50,
            maxDepth: 3,
            randomize: true
        });

        logger.info(`Found ${packageFiles.length} package files for testing`);

        if (packageFiles.length === 0) {
            warnings.push('No package files found for testing');
            return {
                success: false,
                testName: 'Comprehensive Package Analyzer Test',
                duration: Date.now() - startTime,
                details,
                errors: ['No package files found'],
                warnings,
                metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
            };
        }

        // Test different batch sizes
        const batchSizes = [1, 5, 10, 25];
        const extractorResults: any = {};

        for (const batchSize of batchSizes) {
            if (packageFiles.length < batchSize) continue;

            logger.info(`Testing batch size: ${batchSize}`);
            const batchStartTime = Date.now();
            const batch = packageFiles.slice(0, batchSize);

            let resourcesProcessed = 0;
            const extractorCounts: Record<string, number> = {};

            for (const packageFile of batch) {
                try {
                    logger.debug(`Processing: ${path.basename(packageFile)}`);

                    const result = await packageAnalyzer.analyzePackage(packageFile);

                    if (result.success && result.resources) {
                        resourcesProcessed += result.resources.length;

                        // Count extractor usage
                        for (const resource of result.resources) {
                            const extractorType = resource.resourceType || 'UNKNOWN';
                            extractorCounts[extractorType] = (extractorCounts[extractorType] || 0) + 1;
                        }
                    }

                } catch (error: any) {
                    errors.push(`Error processing ${packageFile}: ${error.message}`);
                }
            }

            extractorResults[`batch_${batchSize}`] = {
                duration: Date.now() - batchStartTime,
                resourcesProcessed,
                extractorCounts,
                averageTimePerResource: resourcesProcessed > 0 ? (Date.now() - batchStartTime) / resourcesProcessed : 0
            };

            logger.info(`Batch ${batchSize}: ${resourcesProcessed} resources processed in ${Date.now() - batchStartTime}ms`);
        }

        details.packageAnalyzer = {
            totalPackagesAvailable: packageFiles.length,
            batchResults: extractorResults,
            memoryUsage: memoryManager.getMemoryStats()
        };

        metrics = {
            memoryUsage: memoryManager.getMemoryStats(),
            processingTime: Date.now() - startTime,
            resourcesProcessed: Object.values(extractorResults).reduce((sum: number, batch: any) => sum + batch.resourcesProcessed, 0),
            conflictsDetected: 0
        };

        // Cleanup
        await resourceTracker.releaseResourcesByOwner(`packageAnalyzerTest_${testId}`);

        return {
            success: errors.length === 0,
            testName: 'Comprehensive Package Analyzer Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        return {
            success: false,
            testName: 'Comprehensive Package Analyzer Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
        };
    }
}

/**
 * Test DatabaseService CRUD operations comprehensively
 */
export async function testDatabaseServiceComprehensive(
    options: ComprehensiveTestOptions = {}
): Promise<ComprehensiveTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};

    const logger = new Logger('ComprehensiveDatabaseTest');
    const resourceTracker = ResourceTracker.getInstance();

    try {
        logger.info('===== COMPREHENSIVE DATABASE SERVICE TEST =====');

        // Initialize database
        const databaseService = new DatabaseService(options.useInMemoryDatabase ? ':memory:' : 'test_db_comprehensive.db');
        await databaseService.initialize();

        const testId = Date.now();
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `databaseTest_${testId}`,
            async () => await databaseService.close(),
            { id: `db_test_${testId}`, state: ResourceState.ACTIVE }
        );

        // Test resource operations
        logger.info('Testing resource CRUD operations...');
        const resourceTests = await testResourceCRUD(databaseService);

        // Test metadata operations
        logger.info('Testing metadata CRUD operations...');
        const metadataTests = await testMetadataCRUD(databaseService);

        // Test dependency operations
        logger.info('Testing dependency CRUD operations...');
        const dependencyTests = await testDependencyCRUD(databaseService);

        // Test conflict operations
        logger.info('Testing conflict CRUD operations...');
        const conflictTests = await testConflictCRUD(databaseService);

        details.databaseService = {
            resourceTests,
            metadataTests,
            dependencyTests,
            conflictTests
        };

        // Cleanup
        await resourceTracker.releaseResourcesByOwner(`databaseTest_${testId}`);

        return {
            success: errors.length === 0,
            testName: 'Comprehensive Database Service Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: Date.now() - startTime, resourcesProcessed: 0, conflictsDetected: 0 }
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        return {
            success: false,
            testName: 'Comprehensive Database Service Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
        };
    }
}

// Helper functions for database testing
async function testResourceCRUD(databaseService: DatabaseService): Promise<any> {
    const results: any = { operations: [], errors: [] };

    try {
        // Test CREATE operations
        const testResource = {
            packageId: 1,
            type: 0x0333406C,
            group: 0x80000000,
            instance: 0x12345678,
            resourceType: 'TRAIT',
            name: 'TestTrait',
            size: 1024,
            hash: 'testhash123',
            content: Buffer.from('test content'),
            extractedAt: new Date()
        };

        const resourceId = await databaseService.resources.create(testResource);
        results.operations.push({ operation: 'CREATE', success: true, resourceId });

        // Test READ operations
        const retrievedResource = await databaseService.resources.findById(resourceId);
        results.operations.push({
            operation: 'READ',
            success: retrievedResource !== null,
            matches: retrievedResource?.name === testResource.name
        });

        // Test UPDATE operations
        await databaseService.resources.update(resourceId, { name: 'UpdatedTestTrait' });
        const updatedResource = await databaseService.resources.findById(resourceId);
        results.operations.push({
            operation: 'UPDATE',
            success: updatedResource?.name === 'UpdatedTestTrait'
        });

        // Test batch operations
        const batchResources = Array.from({ length: 10 }, (_, i) => ({
            ...testResource,
            instance: testResource.instance + i,
            name: `BatchTrait${i}`
        }));

        const batchIds = await Promise.all(
            batchResources.map(resource => databaseService.resources.create(resource))
        );
        results.operations.push({
            operation: 'BATCH_CREATE',
            success: batchIds.length === 10,
            count: batchIds.length
        });

        // Test DELETE operations
        await databaseService.resources.delete(resourceId);
        const deletedResource = await databaseService.resources.findById(resourceId);
        results.operations.push({
            operation: 'DELETE',
            success: deletedResource === null
        });

    } catch (error: any) {
        results.errors.push(`Resource CRUD error: ${error.message}`);
    }

    return results;
}

async function testMetadataCRUD(databaseService: DatabaseService): Promise<any> {
    const results: any = { operations: [], errors: [] };

    try {
        // Test metadata operations
        const testMetadata = {
            resourceId: 1,
            extractorType: 'TRAIT',
            metadata: {
                gameplaySystemsAffected: ['Traits'],
                modificationSeverity: 'functional',
                conflictPotential: 75
            },
            extractedAt: new Date()
        };

        const metadataId = await databaseService.metadata.create(testMetadata);
        results.operations.push({ operation: 'CREATE_METADATA', success: true, metadataId });

        const retrievedMetadata = await databaseService.metadata.findByResourceId(1);
        results.operations.push({
            operation: 'READ_METADATA',
            success: retrievedMetadata.length > 0,
            count: retrievedMetadata.length
        });

    } catch (error: any) {
        results.errors.push(`Metadata CRUD error: ${error.message}`);
    }

    return results;
}

async function testDependencyCRUD(databaseService: DatabaseService): Promise<any> {
    const results: any = { operations: [], errors: [] };

    try {
        // Test dependency operations
        const testDependency = {
            sourceResourceId: 1,
            targetResourceId: 2,
            dependencyType: 'REFERENCE',
            strength: 0.8,
            context: 'trait_reference'
        };

        const dependencyId = await databaseService.dependencies.create(testDependency);
        results.operations.push({ operation: 'CREATE_DEPENDENCY', success: true, dependencyId });

        const dependencies = await databaseService.dependencies.findBySourceId(1);
        results.operations.push({
            operation: 'READ_DEPENDENCIES',
            success: dependencies.length > 0,
            count: dependencies.length
        });

    } catch (error: any) {
        results.errors.push(`Dependency CRUD error: ${error.message}`);
    }

    return results;
}

async function testConflictCRUD(databaseService: DatabaseService): Promise<any> {
    const results: any = { operations: [], errors: [] };

    try {
        // Test conflict operations
        const testConflict = {
            resource1Id: 1,
            resource2Id: 2,
            conflictType: 'TGI_MATCH',
            severity: 'HIGH',
            confidence: 0.95,
            description: 'Test conflict',
            detectedAt: new Date()
        };

        const conflictId = await databaseService.conflicts.create(testConflict);
        results.operations.push({ operation: 'CREATE_CONFLICT', success: true, conflictId });

        const conflicts = await databaseService.conflicts.findByResourceId(1);
        results.operations.push({
            operation: 'READ_CONFLICTS',
            success: conflicts.length > 0,
            count: conflicts.length
        });

    } catch (error: any) {
        results.errors.push(`Conflict CRUD error: ${error.message}`);
    }

    return results;
}

/**
 * Test ResourceTracker concurrent operations
 */
export async function testResourceTrackerConcurrency(
    options: ComprehensiveTestOptions = {}
): Promise<ComprehensiveTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};

    const logger = new Logger('ResourceTrackerConcurrencyTest');
    const resourceTracker = ResourceTracker.getInstance();

    try {
        logger.info('===== RESOURCE TRACKER CONCURRENCY TEST =====');

        const concurrentOperations = options.testMode === 'stress' ? 100 : 20;
        const testId = Date.now();

        // Test concurrent resource tracking
        const trackingPromises = Array.from({ length: concurrentOperations }, async (_, i) => {
            const resourceId = `concurrent_resource_${testId}_${i}`;

            return resourceTracker.trackResource(
                ResourceType.OPERATION,
                `concurrentTest_${testId}`,
                async () => {
                    // Simulate cleanup work
                    await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
                },
                {
                    id: resourceId,
                    state: ResourceState.ACTIVE,
                    size: Math.floor(Math.random() * 1024 * 1024) // Random size up to 1MB
                }
            );
        });

        const trackedResources = await Promise.all(trackingPromises);

        // Test concurrent resource release
        const releaseStartTime = Date.now();
        await resourceTracker.releaseResourcesByOwner(`concurrentTest_${testId}`);
        const releaseTime = Date.now() - releaseStartTime;

        details.resourceTracker = {
            concurrentOperations,
            trackedResources: trackedResources.length,
            releaseTime,
            averageReleaseTime: releaseTime / concurrentOperations
        };

        logger.info(`Successfully tracked and released ${trackedResources.length} resources concurrently`);

        return {
            success: errors.length === 0,
            testName: 'Resource Tracker Concurrency Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: {
                memoryUsage: {},
                processingTime: Date.now() - startTime,
                resourcesProcessed: trackedResources.length,
                conflictsDetected: 0
            }
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        return {
            success: false,
            testName: 'Resource Tracker Concurrency Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
        };
    }
}

/**
 * Test EnhancedMemoryManager stress scenarios
 */
export async function testMemoryManagerStress(
    options: ComprehensiveTestOptions = {}
): Promise<ComprehensiveTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};

    const logger = new Logger('MemoryManagerStressTest');
    const memoryManager = EnhancedMemoryManager.getInstance();

    try {
        logger.info('===== MEMORY MANAGER STRESS TEST =====');

        const testId = Date.now();
        const memorySnapshots: any[] = [];

        // Test memory tracking under stress
        const stressOperations = options.testMode === 'stress' ? 1000 : 100;

        for (let i = 0; i < stressOperations; i++) {
            const resourceType = `stress_resource_${i % 10}`;
            const resourceSize = Math.floor(Math.random() * 10 * 1024 * 1024); // Up to 10MB

            memoryManager.trackResource(resourceType, resourceSize);

            // Take memory snapshots periodically
            if (i % 50 === 0) {
                memorySnapshots.push({
                    iteration: i,
                    memoryStats: memoryManager.getMemoryStats(),
                    memoryPressure: memoryManager.getMemoryPressure(),
                    timestamp: Date.now()
                });
            }
        }

        // Test memory cleanup
        for (let i = 0; i < stressOperations; i++) {
            const resourceType = `stress_resource_${i % 10}`;
            const resourceSize = Math.floor(Math.random() * 10 * 1024 * 1024);

            memoryManager.untrackResource(resourceType, resourceSize);
        }

        const finalMemoryStats = memoryManager.getMemoryStats();

        details.memoryManager = {
            stressOperations,
            memorySnapshots,
            finalMemoryStats,
            peakMemoryPressure: Math.max(...memorySnapshots.map(s => s.memoryPressure))
        };

        logger.info(`Completed ${stressOperations} memory operations successfully`);

        return {
            success: errors.length === 0,
            testName: 'Memory Manager Stress Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: {
                memoryUsage: finalMemoryStats,
                processingTime: Date.now() - startTime,
                resourcesProcessed: stressOperations,
                conflictsDetected: 0
            }
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        return {
            success: false,
            testName: 'Memory Manager Stress Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics: { memoryUsage: {}, processingTime: 0, resourcesProcessed: 0, conflictsDetected: 0 }
        };
    }
}