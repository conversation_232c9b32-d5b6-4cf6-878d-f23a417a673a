# Electron Integration

This directory contains the Electron integration for the Sims 4 Mod Conflict Scanner. The Electron app provides a native desktop experience while maintaining the functionality of the web interface.

## Architecture

The application is built with a three-layer architecture:

1. **TypeScript Server** (`src/server.ts`)
   - Handles mod analysis and conflict detection
   - Runs on port 3000
   - Communicates via Socket.IO

2. **Streamlit Interface** (`src/streamlit/`)
   - Provides the web interface
   - Runs on port 7860
   - Handles user interactions and displays results

3. **Electron App** (`src/electron/`)
   - Provides native desktop experience
   - Manages application lifecycle
   - Handles IPC communication

## File Structure

- `main.js`: Main Electron process
- `preload.js`: Preload script for secure IPC communication

## Development

### Prerequisites

- Node.js >= 18.0.0
- Python >= 3.10
- Electron >= 24.0.0

### Running the Application

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the application:
   ```bash
   python run_app.py
   ```

This will start:
- TypeScript server (port 3000)
- Streamlit interface (port 7860)
- Electron app

### Building for Distribution

To create a distributable package:

```bash
npm run build
```

This will create platform-specific installers in the `dist` directory.

## IPC Communication

The application uses Electron's IPC (Inter-Process Communication) system to handle updates:

1. **Progress Updates**
   - Event: `analysis-progress`
   - Data: `{ message: string, current: number, total: number }`

2. **Results Updates**
   - Event: `analysis-results`
   - Data: Analysis results object

3. **Recommendations Updates**
   - Event: `analysis-recommendations`
   - Data: Recommendations object

4. **Error Updates**
   - Event: `analysis-error`
   - Data: Error message string

## Security Considerations

- The preload script uses `contextBridge` to expose only necessary IPC methods
- Node integration is disabled in the renderer process
- All IPC communication is validated and sanitized

## Troubleshooting

1. **Application Not Starting**
   - Check if all required ports (3000, 7860) are available
   - Verify all dependencies are installed
   - Check the logs in the Electron DevTools

2. **Updates Not Showing**
   - Verify Socket.IO connection is established
   - Check IPC communication in DevTools
   - Ensure the Streamlit interface is running

3. **Build Issues**
   - Clear the `dist` directory
   - Run `npm install` again
   - Check for platform-specific requirements 