﻿﻿console.log('[Main] TOP LEVEL: main.ts execution started.'); // DEBUG
﻿﻿import { app, BrowserWindow, ipcMain, shell, session, IpcMainInvokeEvent, dialog, WebContents } from 'electron';
import path from 'path';
import { Logger } from '../../utils/logging/logger';
import { PackageAnalysisService } from '../../services/analysis/packageAnalysisService';
import { ModConflictOrchestrator } from '../../services/ml/ModConflictOrchestrator';
import { DatabaseService } from '../../services/databaseService';
import { promises as fsPromises } from 'fs';
import { ResourceKey, ResourceMetadata } from '../../types/resource/interfaces';
import { createResourceKey, getResourceTypeCategory } from '../../utils/resource/helpers';
import { ResourceCategory } from '../../types/resource/enums';
import { PackageMetadata } from '../../types/resource/Package';
import { PackageAnalysisResult } from '../../types/analysis/PackageAnalysisResult';
import { ResourcePackageInfo } from '../../types/resource/conflicts';
import { ConflictInfo } from '../../types/conflict/index';
import { configureEventEmitter } from '../../utils/eventEmitterConfig';
// S4TK plugin registration removed to attempt fallback JS implementation
console.log('[Main] Attempting to run S4TK without native plugin.');

// Configure EventEmitter to avoid memory leaks
configureEventEmitter();

// __dirname is available in CommonJS

let mainAppInstance: MainApplication | null = null; // Variable to hold the instance
const topLevelLogger = new Logger('MainTopLevel'); // Use a separate logger instance if needed

// --- Listener moved inside app.ready ---


class MainApplication {
  private mainWindow: BrowserWindow | null = null;
  private logger: Logger;
  private packageAnalysisService: PackageAnalysisService;

  constructor() {
    console.log('[Main] CONSTRUCTOR: MainApplication constructor entered.'); // DEBUG
    this.logger = new Logger('MainProcess');
    this.packageAnalysisService = PackageAnalysisService.getInstance(this.logger, DatabaseService.getInstance());
    mainAppInstance = this; // Assign the instance
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    app.on('ready', async () => {
      session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
        callback({
          responseHeaders: {
            ...details.responseHeaders,
            'Content-Security-Policy': [ "default-src 'self' 'unsafe-inline' 'unsafe-eval' data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:;" ]
          }
        })
      });

      try {
          this.logger.info("Initializing analysis service...");
          await this.packageAnalysisService.initialize();
          this.logger.info("Service initialized.");
      } catch (initError: any) {
          this.logger.error(`Failed to initialize services: ${initError.message || initError}`);
      }

      // --- Register open-file-dialog handler BEFORE creating window ---
      ipcMain.handle('open-file-dialog', async (event) => { // Use handle instead of on
        this.logger.info('[MainProcess Handler] Received open-file-dialog invoke.'); // Keep logger
        // 'this' should refer to the MainApplication instance here
        // The event object is the first argument for handle
        await this.handleFileDialog();
        // handle should ideally return something, but void is acceptable if nothing is needed back immediately
        return;
      });
      this.logger.info('[MainProcess] Registered open-file-dialog handler inside app.ready.');
      // --- End open-file-dialog Handler ---

      // --- Register test-invoke-channel handler ---
      ipcMain.handle('test-invoke-channel', async (event, message: string) => {
        this.logger.info(`[MainProcess Handler] Received test-invoke-channel with message: "${message}"`);
        return `Main process received: "${message}"`; // Return a confirmation
      });
      this.logger.info('[MainProcess] Registered test-invoke-channel handler inside app.ready.');
      // --- End test-invoke-channel Handler ---

      this.createWindow(); // Create window AFTER handler is registered

      // --- Simple IPC Test Listener (can remain here or be moved) ---
      ipcMain.on('test-ping', (event, arg) => {
        this.logger.info(`[MainProcess] Received test-ping: ${arg}`);
      });
      this.logger.info('[MainProcess] Registered test-ping listener inside app.ready.');
      // --- End Simple IPC Test Listener ---

    }); // End of app.on('ready')

    // Listeners previously here are now handled within app.ready

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Removed the class-internal 'open-file-dialog' listener

    // Other handlers remain
    ipcMain.handle('start-analysis', async (event: IpcMainInvokeEvent, args) => {
      this.logger.warn(`Received direct 'start-analysis' request. Ensure this doesn't conflict with dialog flow.`);
      if (!args || !Array.isArray(args.filePaths) || args.filePaths.length === 0) {
        this.logger.error('Invalid arguments received for start-analysis. Expected filePaths array.');
        throw new Error('No valid file paths provided for analysis.');
      }
      return await this._runAnalysis(args.filePaths, event.sender);
    });

    ipcMain.handle('analyze-file', async (event: IpcMainInvokeEvent, filePath: string) => {
      this.logger.warn("IPC handler 'analyze-file' is deprecated, use 'start-analysis'.");
      try {
        const result = await this.packageAnalysisService.analyzePackage(filePath);
        return {
            individualResults: [result],
            crossPackageConflicts: [],
            metrics: {
                fileCount: 1,
                analyzedFileCount: result.isValid ? 1 : 0,
                totalResourceCount: result.resourceCount,
                analysisTimeMs: result.analysisTime,
                conflictsFound: result.conflicts?.length || 0
            }
        };
      } catch (error: any) {
        this.logger.error(`Error analyzing file ${filePath}:`, error);
        throw error;
      }
    });

    ipcMain.handle('analyze-files', async (event: IpcMainInvokeEvent, filePaths: string[]) => {
       this.logger.warn("IPC handler 'analyze-files' is deprecated, use 'start-analysis'.");
      try {
        const results = await this.packageAnalysisService.analyzeMultiplePackages(filePaths);
         const analysisTimeMs = results.reduce((sum, r) => sum + r.analysisTime, 0);
         const totalConflicts = results.reduce((sum, r) => sum + (r.conflicts?.length || 0), 0);
         return {
             individualResults: results,
             crossPackageConflicts: results.flatMap(r => r.conflicts || []), // Keep this one
             metrics: { // Keep this one
                 fileCount: filePaths.length,
                 analyzedFileCount: results.filter(r => r.isValid).length,
                 totalResourceCount: results.reduce((sum, r) => sum + r.resourceCount, 0),
                 analysisTimeMs: analysisTimeMs,
                 conflictsFound: totalConflicts
             }
             // Removed duplicate crossPackageConflicts and metrics keys
         };
      } catch (error: any) {
        this.logger.error('Error analyzing multiple files:', error);
        throw error;
      }
    });
  }


  // Extracted core analysis logic to be callable from multiple handlers
  private async _runAnalysis(filePaths: string[], eventSender: WebContents) {
      const sendProgress = (progress: number, status: string) => {
        if (!eventSender.isDestroyed()) {
            eventSender.send('analysis-progress', { progress, status });
        } else {
             this.logger.warn("Cannot send progress, eventSender (webContents) is destroyed.");
        }
      };

      try {
        sendProgress(10, 'Initializing analysis run...');
        this.logger.info(`[_runAnalysis] Starting analysis for ${filePaths.length} files...`);
        const overallStartTime = Date.now();

        let results: PackageAnalysisResult[];
        if (filePaths.length === 1) {
            this.logger.info(`[_runAnalysis] Calling analyzePackage for single file...`);
            sendProgress(50, `Analyzing package ${path.basename(filePaths[0])}...`);
            const singleResult = await this.packageAnalysisService.analyzePackage(filePaths[0]);
            this.logger.info(`[_runAnalysis] analyzePackage completed.`);
            results = [singleResult];
        } else {
             this.logger.info(`[_runAnalysis] Calling analyzeMultiplePackages for ${filePaths.length} files...`);
            sendProgress(30, `Analyzing ${filePaths.length} packages...`);
            results = await this.packageAnalysisService.analyzeMultiplePackages(filePaths);
             this.logger.info(`[_runAnalysis] analyzeMultiplePackages completed.`);
            sendProgress(85, 'Detecting cross-package conflicts...');
             this.logger.info(`[_runAnalysis] Cross-package conflict detection finished (or skipped).`);
        }

        sendProgress(100, 'Analysis complete.');
        const analysisTimeMs = Date.now() - overallStartTime;
        const totalConflicts = results.reduce((sum, r) => sum + (r.conflicts?.length || 0), 0);
        this.logger.info(`Overall analysis complete in ${analysisTimeMs}ms. Found ${totalConflicts} conflicts.`);

        const finalResults = {
          individualResults: results,
          crossPackageConflicts: results.flatMap(r => r.conflicts || []),
          metrics: {
              fileCount: filePaths.length,
              analyzedFileCount: results.filter(r => r.isValid).length,
              totalResourceCount: results.reduce((sum, r) => sum + r.resourceCount, 0),
              analysisTimeMs: analysisTimeMs,
              conflictsFound: totalConflicts
          }
        };
        if (!eventSender.isDestroyed()) {
             this.logger.info(`[_runAnalysis] Sending analysis-complete event.`);
             eventSender.send('analysis-complete', finalResults);
        } else {
             this.logger.warn('[_runAnalysis] Cannot send analysis-complete, eventSender destroyed.');
        }
        return finalResults;

      } catch (error: any) {
         this.logger.error(`[_runAnalysis] Caught error: ${error.message || error}`, error);
        sendProgress(0, `Error: ${error.message || 'Unknown analysis error'}`);
         if (!eventSender.isDestroyed()) {
             eventSender.send('analysis-error', error.message || 'Unknown analysis error');
         } else {
              this.logger.warn('[_runAnalysis] Cannot send analysis-error, eventSender destroyed.');
         }
        throw new Error(`Analysis failed: ${error.message || 'Unknown error'}`); // Re-throw after logging/sending
      }
  }


  // Re-add handleFileDialog method (make public if called from global listener)
  public async handleFileDialog(): Promise<void> {
    this.logger.info('[handleFileDialog Method] Triggered.');
    if (!this.mainWindow) {
      this.logger.error('Cannot show dialog, mainWindow is null.');
      return;
    }
    const targetWebContents = this.mainWindow.webContents; // Capture webContents

    try {
      const { canceled, filePaths } = await dialog.showOpenDialog(this.mainWindow, {
        title: 'Select Sims 4 Package Files',
        filters: [{ name: 'Sims 4 Package', extensions: ['package'] }],
        properties: ['openFile', 'multiSelections', 'dontAddToRecent']
      });

      if (!canceled && filePaths && filePaths.length > 0) {
        this.logger.info(`User selected ${filePaths.length} files via dialog.`);
        if (targetWebContents && !targetWebContents.isDestroyed()) {
           this.logger.info(`[_runAnalysis Trigger] WebContents found. Calling _runAnalysis...`);
          // Wrap the call in a try/catch as well
          try {
            await this._runAnalysis(filePaths, targetWebContents);
          } catch(runAnalysisError) {
              this.logger.error('[handleFileDialog] Error executing _runAnalysis:', runAnalysisError);
              if (!targetWebContents.isDestroyed()) {
                  const errorMessage = runAnalysisError instanceof Error ? runAnalysisError.message : 'Unknown analysis error';
                  targetWebContents.send('analysis-error', `Error during analysis: ${errorMessage}`);
              }
           }
         } else {
           this.logger.error('Cannot run analysis after dialog, webContents is invalid or destroyed.');
           // Send error even if webContents became invalid after dialog
           if (targetWebContents && !targetWebContents.isDestroyed()) {
               targetWebContents.send('analysis-error', 'Window content unavailable after dialog.');
           }
        }
      } else {
        this.logger.info('User cancelled file dialog.');
         if (targetWebContents && !targetWebContents.isDestroyed()) {
             targetWebContents.send('analysis-error', 'File selection canceled.');
         }
      }
    } catch (error: any) {
      this.logger.error(`Error showing open file dialog: ${error.message || error}`);
       if (targetWebContents && !targetWebContents.isDestroyed()) {
            targetWebContents.send('analysis-error', `Error opening dialog: ${error.message || 'Unknown error'}`);
       }
    }
  }


  private createWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        preload: path.join(__dirname, 'preload.cjs'), // Load .cjs (copied CJS)
        webviewTag: true,
        contextIsolation: true, // Recommended for security
        nodeIntegration: false, // Recommended for security
      },
    });

    // Adjust path for loading the renderer HTML
    // It's built by Vite into dist/frontend/electron, relative to project root
    // Path needs to go up from dist/main-process/frontend/electron to dist/electron.html
    // __dirname points to dist/main-process/frontend/electron
    const rendererHtmlPath = path.join(__dirname, '..', '..', '..', 'electron.html'); // Go up three levels to dist/

    const startUrl = process.env.NODE_ENV === 'development'
      ? 'http://localhost:8081/electron.html' // Use correct port and filename
      : `file://${rendererHtmlPath}`; // Use corrected production path

    this.logger.info(`Loading URL: ${startUrl}`); // Add logging
    this.mainWindow.loadURL(startUrl);

    // if (process.env.NODE_ENV === 'development') { // Keep DevTools closed for production test
    //   this.mainWindow.webContents.openDevTools();
    // }

    this.mainWindow.on('closed', () => {
        this.mainWindow = null;
    });
  }
}

// Start the application
new MainApplication();
