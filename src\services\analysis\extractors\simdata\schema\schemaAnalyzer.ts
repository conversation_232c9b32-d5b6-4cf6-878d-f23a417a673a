/**
 * Schema analyzer for SimData schemas
 * Responsible for analyzing schema structure, inheritance, and purpose
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataSchema } from '../simDataParser.js';
import { SchemaInheritanceInfo, SchemaAnalysisResult, ColumnSemanticInfo } from './schemaInterfaces.js';
import { KNOWN_SCHEMA_PARENTS, SCHEMA_CATEGORIES, GAMEPLAY_SYSTEMS } from './schemaConstants.js';
import { ColumnAnalyzer } from './columnAnalyzer.js';

const log = new Logger('SchemaAnalyzer');

/**
 * Schema Analyzer
 * Analyzes SimData schemas to determine their structure, inheritance, and purpose
 */
export class SchemaAnalyzer {
    private columnAnalyzer: ColumnAnalyzer;

    constructor() {
        this.columnAnalyzer = new ColumnAnalyzer();
    }

    /**
     * Analyze a SimData schema
     * @param schema The SimData schema
     * @returns Schema analysis result
     */
    public analyzeSchema(schema: SimDataSchema): SchemaAnalysisResult {
        // Analyze column semantics
        const columnSemantics = this.columnAnalyzer.analyzeColumns(schema.columns);

        // Analyze schema inheritance
        const inheritance = this.analyzeSchemaInheritance(schema);

        // Determine schema category
        const category = this.determineSchemaCategory(schema.name);

        // Calculate schema complexity
        const complexity = this.calculateSchemaComplexity(schema, columnSemantics);

        // Determine schema purpose
        const purpose = this.determineSchemaPurpose(schema, columnSemantics, category);

        // Determine gameplay system
        const gameplaySystem = this.determineGameplaySystem(schema, columnSemantics, category);

        return {
            schema,
            inheritance,
            columnSemantics,
            category,
            complexity,
            purpose,
            gameplaySystem
        };
    }

    /**
     * Analyze schema inheritance
     * @param schema The SimData schema
     * @returns Schema inheritance information or undefined if no inheritance detected
     */
    private analyzeSchemaInheritance(schema: SimDataSchema): SchemaInheritanceInfo | undefined {
        if (!schema.name || !schema.name.includes('_')) {
            return undefined;
        }

        const parts = schema.name.split('_');
        if (parts.length < 2) {
            return undefined;
        }

        const potentialParent = parts[0];

        // Check if this is a known parent schema
        if (!KNOWN_SCHEMA_PARENTS.has(potentialParent)) {
            return undefined;
        }

        // We can't determine inherited vs. added columns without the parent schema
        // So we'll just return the basic inheritance info
        return {
            parent: potentialParent,
            child: schema.name,
            inheritedColumns: [], // Would need parent schema to determine
            addedColumns: [] // Would need parent schema to determine
        };
    }

    /**
     * Determine the category of a schema
     * @param schemaName The schema name
     * @returns The schema category
     */
    private determineSchemaCategory(schemaName: string): string {
        if (!schemaName) {
            return 'Unknown';
        }

        // Check each category
        for (const [category, patterns] of Object.entries(SCHEMA_CATEGORIES)) {
            if (patterns && patterns.some(pattern => schemaName.includes(pattern))) {
                return category;
            }
        }

        // Check for inheritance patterns
        if (schemaName.includes('_')) {
            const parts = schemaName.split('_');
            const potentialParent = parts[0];

            for (const [category, patterns] of Object.entries(SCHEMA_CATEGORIES)) {
                if (patterns && patterns.some(pattern => potentialParent === pattern)) {
                    return category;
                }
            }
        }

        return 'Other';
    }

    /**
     * Calculate the complexity of a schema
     * @param schema The SimData schema
     * @param columnSemantics The column semantics
     * @returns A complexity score (0-100)
     */
    private calculateSchemaComplexity(
        schema: SimDataSchema,
        columnSemantics: Record<string, ColumnSemanticInfo>
    ): number {
        // Factors that contribute to complexity
        const factors = [
            // Number of columns (more columns = more complex)
            Math.min(50, schema.columns.length * 2),

            // Number of critical columns (more critical columns = more complex)
            Object.values(columnSemantics).filter(col => col.isCritical).length * 5,

            // Number of reference columns (more references = more complex)
            Object.values(columnSemantics).filter(col => col.category === 'reference').length * 3,

            // Schema inheritance (inheritance = more complex)
            schema.parent ? 10 : 0
        ];

        // Calculate raw score and normalize to 0-100 range
        const rawScore = factors.reduce((sum, factor) => sum + factor, 0);
        return Math.min(100, Math.max(0, rawScore));
    }

    /**
     * Determine the purpose of a schema
     * @param schema The SimData schema
     * @param columnSemantics The column semantics
     * @param category The schema category
     * @returns The schema purpose
     */
    private determineSchemaPurpose(
        schema: SimDataSchema,
        columnSemantics: Record<string, ColumnSemanticInfo>,
        category: string
    ): string {
        const name = schema.name.toLowerCase();

        // Check for common schema purposes based on name
        if (name.includes('trait')) {
            return 'Defines Sim traits and their effects';
        } else if (name.includes('buff')) {
            return 'Defines temporary status effects for Sims';
        } else if (name.includes('interaction')) {
            return 'Defines interactions between Sims or objects';
        } else if (name.includes('recipe')) {
            return 'Defines crafting or cooking recipes';
        } else if (name.includes('career')) {
            return 'Defines career paths and progression';
        } else if (name.includes('aspiration')) {
            return 'Defines Sim aspirations and goals';
        } else if (name.includes('skill')) {
            return 'Defines Sim skills and progression';
        } else if (name.includes('object')) {
            return 'Defines game objects and their properties';
        } else if (name.includes('sim')) {
            return 'Defines Sim properties and characteristics';
        } else if (name.includes('lot')) {
            return 'Defines lot properties and characteristics';
        } else if (name.includes('venue')) {
            return 'Defines venue types and properties';
        } else if (name.includes('relationship')) {
            return 'Defines relationship properties and dynamics';
        }

        // Use category to determine general purpose
        switch (category) {
            case 'Sim':
                return 'Related to Sim properties or characteristics';
            case 'Object':
                return 'Related to game object properties or behavior';
            case 'Gameplay':
                return 'Defines gameplay mechanics or systems';
            case 'World':
                return 'Related to world, lot, or venue properties';
            case 'Social':
                return 'Related to social interactions or relationships';
            case 'Animation':
                return 'Related to animations or visual effects';
            case 'UI':
                return 'Related to user interface elements';
            case 'System':
                return 'Related to game systems or management';
            default:
                return 'Unknown purpose';
        }
    }

    /**
     * Determine which gameplay system a schema belongs to
     * @param schema The SimData schema
     * @param columnSemantics The column semantics
     * @param category The schema category
     * @returns The gameplay system
     */
    private determineGameplaySystem(
        schema: SimDataSchema,
        columnSemantics: Record<string, ColumnSemanticInfo>,
        category: string
    ): string {
        const name = schema.name.toLowerCase();

        // Check for common gameplay systems based on name
        for (const system of GAMEPLAY_SYSTEMS) {
            if (name.includes(system.toLowerCase())) {
                return system;
            }
        }

        // Use category to determine general gameplay system
        switch (category) {
            case 'Sim':
                return 'Sims';
            case 'Object':
                return 'Objects';
            case 'Gameplay':
                return 'Gameplay';
            case 'World':
                return 'Worlds';
            case 'Social':
                return 'Relationships';
            case 'Animation':
                return 'Animations';
            case 'UI':
                return 'User Interface';
            case 'System':
                return 'Game Systems';
            default:
                return 'Unknown';
        }
    }
}
