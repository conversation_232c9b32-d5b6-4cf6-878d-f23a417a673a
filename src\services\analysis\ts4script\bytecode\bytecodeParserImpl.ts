/**
 * Python Bytecode Parser Implementation
 * 
 * This module provides the main functionality for parsing Python bytecode in TS4Script files.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { 
    BytecodeHeader, 
    BytecodeParsingResult, 
    CodeObject, 
    Instruction, 
    ParsedPythonModule, 
    PythonVersion 
} from './types.js';
import { parseHeader } from './headerParser.js';
import { parseCodeObject } from './codeObjectParser.js';
import { 
    parseInstructions, 
    extractImportsFromInstructions,
    extractClassesFromInstructions,
    extractFunctionsFromInstructions
} from './instructionParser.js';
import { extractStringLiterals, hasPythonBytecodeHeader } from './utils.js';

// Create a logger for this module
const logger = new Logger('BytecodeParser');

/**
 * Parses Python bytecode from a buffer
 * @param buffer Buffer containing Python bytecode
 * @param moduleName Optional module name
 * @returns Bytecode parsing result
 */
export function parseBytecode(buffer: Buffer, moduleName: string = 'unknown'): BytecodeParsingResult {
    try {
        logger.info(`Parsing bytecode for module: ${moduleName}`);
        
        // Check if the buffer contains a valid Python bytecode header
        if (!hasPythonBytecodeHeader(buffer)) {
            return {
                success: false,
                error: 'Not a valid Python bytecode file',
                confidence: 0,
                parsingMethod: 'header_check'
            };
        }
        
        // Parse the bytecode header
        const header = parseHeader(buffer);
        
        // If we couldn't determine the Python version, we can't parse the bytecode
        if (!header.pythonVersion) {
            return {
                success: false,
                error: `Unknown Python version: magic=0x${header.magic.toString(16)}`,
                confidence: 0.1,
                parsingMethod: 'header_parse'
            };
        }
        
        // Parse the code object
        const [codeObject, offset] = parseCodeObject(buffer, 16, header.pythonVersion);
        
        if (!codeObject) {
            return {
                success: false,
                error: 'Failed to parse code object',
                confidence: 0.2,
                parsingMethod: 'code_object_parse'
            };
        }
        
        // Parse instructions
        const instructions = parseInstructions(codeObject, header.pythonVersion);
        
        // Extract string literals
        const stringLiterals = buffer.length > 1000 
            ? extractStringLiterals(buffer) 
            : [];
        
        // Extract imports
        const imports = extractImportsFromInstructions(instructions, codeObject);
        
        // Extract classes
        const classes = extractClassesFromInstructions(instructions, codeObject);
        
        // Extract functions
        const functions = extractFunctionsFromInstructions(instructions, codeObject);
        
        // Create parsed module
        const module: ParsedPythonModule = {
            name: moduleName,
            header,
            codeObject,
            instructions,
            constants: codeObject.constants,
            names: codeObject.names,
            stringLiterals,
            imports,
            classes,
            functions
        };
        
        logger.info(`Successfully parsed bytecode for module: ${moduleName}`);
        logger.debug(`Found ${imports.length} imports, ${classes.length} classes, ${functions.length} functions`);
        
        return {
            success: true,
            module,
            confidence: 0.9,
            parsingMethod: 'standard'
        };
    } catch (error: any) {
        logger.error(`Error parsing bytecode for module ${moduleName}: ${error.message || error}`);
        
        return {
            success: false,
            error: error.message || String(error),
            confidence: 0,
            parsingMethod: 'error'
        };
    }
}

/**
 * Attempts to parse Python bytecode using multiple strategies
 * @param buffer Buffer containing Python bytecode
 * @param moduleName Optional module name
 * @returns Bytecode parsing result
 */
export function parseWithFallback(buffer: Buffer, moduleName: string = 'unknown'): BytecodeParsingResult {
    try {
        logger.info(`Parsing bytecode with fallback for module: ${moduleName}`);
        
        // Strategy 1: Standard parsing
        const standardResult = parseBytecode(buffer, moduleName);
        if (standardResult.success) {
            logger.info(`Successfully parsed bytecode using standard method for module: ${moduleName}`);
            return standardResult;
        }
        
        // Strategy 2: Try with offset 8 (some EA bytecode might have a different header size)
        try {
            logger.debug(`Trying with offset 8 for module: ${moduleName}`);
            const header = parseHeader(buffer);
            
            if (header.pythonVersion) {
                const [codeObject, offset] = parseCodeObject(buffer, 8, header.pythonVersion);
                
                if (codeObject) {
                    const instructions = parseInstructions(codeObject, header.pythonVersion);
                    const stringLiterals = buffer.length > 1000 ? extractStringLiterals(buffer) : [];
                    const imports = extractImportsFromInstructions(instructions, codeObject);
                    const classes = extractClassesFromInstructions(instructions, codeObject);
                    const functions = extractFunctionsFromInstructions(instructions, codeObject);
                    
                    const module: ParsedPythonModule = {
                        name: moduleName,
                        header,
                        codeObject,
                        instructions,
                        constants: codeObject.constants,
                        names: codeObject.names,
                        stringLiterals,
                        imports,
                        classes,
                        functions
                    };
                    
                    logger.info(`Successfully parsed bytecode with offset 8 for module: ${moduleName}`);
                    
                    return {
                        success: true,
                        module,
                        confidence: 0.7,
                        parsingMethod: 'offset_8'
                    };
                }
            }
        } catch (error) {
            logger.debug(`Failed to parse with offset 8: ${error}`);
        }
        
        // Strategy 3: Heuristic parsing - extract what we can from the buffer
        try {
            logger.debug(`Trying heuristic parsing for module: ${moduleName}`);
            
            // Extract string literals
            const stringLiterals = extractStringLiterals(buffer);
            
            // Try to identify imports, classes, and functions from string literals
            const imports: string[] = [];
            const classes: string[] = [];
            const functions: string[] = [];
            
            for (const literal of stringLiterals) {
                // Potential imports
                if (literal.includes('.') && !literal.includes(' ') && !literal.includes('(')) {
                    imports.push(literal);
                }
                
                // Potential classes (capitalized names)
                if (/^[A-Z][a-zA-Z0-9_]*$/.test(literal)) {
                    classes.push(literal);
                }
                
                // Potential functions (lowercase names followed by parentheses)
                if (/^[a-z][a-zA-Z0-9_]*\(/.test(literal)) {
                    const funcName = literal.split('(')[0];
                    functions.push(funcName);
                }
            }
            
            // Create a minimal parsed module
            const header = parseHeader(buffer);
            
            const module: ParsedPythonModule = {
                name: moduleName,
                header,
                codeObject: {
                    argCount: 0,
                    kwonlyArgCount: 0,
                    nlocals: 0,
                    stackSize: 0,
                    flags: 0,
                    code: Buffer.alloc(0),
                    constants: [],
                    names: [],
                    varnames: [],
                    freevars: [],
                    cellvars: [],
                    filename: '',
                    name: moduleName,
                    firstLineNo: 0,
                    lnotab: Buffer.alloc(0)
                },
                instructions: [],
                constants: [],
                names: [],
                stringLiterals,
                imports,
                classes,
                functions
            };
            
            logger.info(`Extracted ${stringLiterals.length} string literals, ${imports.length} potential imports, ${classes.length} potential classes, ${functions.length} potential functions using heuristic method`);
            
            return {
                success: true,
                module,
                confidence: 0.3,
                parsingMethod: 'heuristic'
            };
        } catch (error) {
            logger.debug(`Failed heuristic parsing: ${error}`);
        }
        
        // If all strategies fail, return the original error
        return standardResult;
    } catch (error: any) {
        logger.error(`Error in parseWithFallback for module ${moduleName}: ${error.message || error}`);
        
        return {
            success: false,
            error: error.message || String(error),
            confidence: 0,
            parsingMethod: 'fallback_error'
        };
    }
}
