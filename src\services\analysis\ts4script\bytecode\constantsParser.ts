/**
 * Python Bytecode Constants Parser
 *
 * This module provides functionality for parsing Python bytecode constants.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { CodeObject } from './types.js';
import { SafeBufferReader } from './safeBufferReader.js';

// Create a logger for this module
const logger = new Logger('BytecodeConstantsParser');

/**
 * Type codes for Python objects in bytecode
 */
export enum TypeCode {
    NULL = '0',
    NONE = 'N',
    FALSE = 'F',
    TRUE = 'T',
    STOPITER = 'S',
    ELLIPSIS = '.',
    INT = 'i',
    INT64 = 'I',
    FLOAT = 'f',
    BINARY_FLOAT = 'g',
    COMPLEX = 'x',
    BINARY_COMPLEX = 'y',
    LONG = 'l',
    STRING = 's',
    INTERNED = 't',
    REF = 'r',
    TUPLE = '(',
    LIST = '[',
    DICT = '{',
    CODE = 'c',
    UNICODE = 'u',
    UNKNOWN = '?',
    SET = '<',
    FROZENSET = '>',
    ASCII = 'a',
    ASCII_INTERNED = 'A',
    SMALL_TUPLE = ')',
    SHORT_ASCII = 'z',
    SHORT_ASCII_INTERNED = 'Z'
}

/**
 * Reads a 32-bit signed integer from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The integer value and the new offset
 */
export function readLong(buffer: Buffer, offset: number): [number, number] {
    const value = buffer.readInt32LE(offset);
    return [value, offset + 4];
}

/**
 * Reads a 64-bit signed integer from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The integer value and the new offset
 */
export function readLong64(buffer: Buffer, offset: number): [bigint, number] {
    const value = buffer.readBigInt64LE(offset);
    return [value, offset + 8];
}

/**
 * Reads a 32-bit floating point number from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The float value and the new offset
 */
export function readFloat(buffer: Buffer, offset: number): [number, number] {
    const value = buffer.readFloatLE(offset);
    return [value, offset + 4];
}

/**
 * Reads a 64-bit floating point number from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The float value and the new offset
 */
export function readDouble(buffer: Buffer, offset: number): [number, number] {
    const value = buffer.readDoubleLE(offset);
    return [value, offset + 8];
}

/**
 * Reads a string from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The string value and the new offset
 */
export function readString(buffer: Buffer, offset: number): [string, number] {
    // Read the string length
    const [length, newOffset] = readLong(buffer, offset);

    // Read the string data
    const stringData = buffer.slice(newOffset, newOffset + length);
    const value = stringData.toString('utf-8');

    return [value, newOffset + length];
}

/**
 * Reads a Unicode string from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The string value and the new offset
 */
export function readUnicode(buffer: Buffer, offset: number): [string, number] {
    // Read the string length
    const [length, newOffset] = readLong(buffer, offset);

    // Read the string data
    const stringData = buffer.slice(newOffset, newOffset + length);
    const value = stringData.toString('utf-16le');

    return [value, newOffset + length];
}

/**
 * Reads an ASCII string from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The string value and the new offset
 */
export function readAscii(buffer: Buffer, offset: number): [string, number] {
    // Read the string length
    const [length, newOffset] = readLong(buffer, offset);

    // Read the string data
    const stringData = buffer.slice(newOffset, newOffset + length);
    const value = stringData.toString('ascii');

    return [value, newOffset + length];
}

/**
 * Reads a short ASCII string from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The string value and the new offset
 */
export function readShortAscii(buffer: Buffer, offset: number): [string, number] {
    // Read the string length (1 byte)
    const length = buffer.readUInt8(offset);

    // Read the string data
    const stringData = buffer.slice(offset + 1, offset + 1 + length);
    const value = stringData.toString('ascii');

    return [value, offset + 1 + length];
}

/**
 * Parses a Python constant from a buffer
 * @param buffer Buffer to parse from
 * @param offset Offset to start parsing from
 * @param codeObjects Map of code objects by reference
 * @returns The parsed constant and the new offset
 */
export function parseConstant(
    buffer: Buffer,
    offset: number,
    codeObjects: Map<number, CodeObject> = new Map()
): [any, number] {
    try {
        // Create a safe buffer reader
        const reader = new SafeBufferReader(buffer, offset);

        // Read the type code
        const typeByte = reader.readUInt8();
        if (typeByte === null) {
            logger.error('Failed to read type code byte');
            return [null, offset];
        }

        const typeCode = String.fromCharCode(typeByte) as TypeCode;
        let newOffset = reader.getPosition();

        switch (typeCode) {
            case TypeCode.NULL:
                return [null, newOffset];

            case TypeCode.NONE:
                return [null, newOffset];

            case TypeCode.FALSE:
                return [false, newOffset];

            case TypeCode.TRUE:
                return [true, newOffset];

            case TypeCode.INT:
                const intValue = reader.readInt32LE();
                if (intValue === null) {
                    logger.error('Failed to read INT value');
                    return [0, reader.getPosition()];
                }
                return [intValue, reader.getPosition()];

            case TypeCode.INT64:
                // Since SafeBufferReader doesn't have readBigInt64LE, we'll use the buffer directly
                // but with proper bounds checking
                if (reader.getPosition() + 8 <= reader.getLength()) {
                    const int64Value = buffer.readBigInt64LE(reader.getPosition());
                    reader.skip(8);
                    return [int64Value, reader.getPosition()];
                } else {
                    logger.error('Buffer too small to read INT64 value');
                    return [BigInt(0), reader.getPosition()];
                }

            case TypeCode.FLOAT:
                // Since SafeBufferReader doesn't have readFloatLE, we'll use the buffer directly
                // but with proper bounds checking
                if (reader.getPosition() + 4 <= reader.getLength()) {
                    const floatValue = buffer.readFloatLE(reader.getPosition());
                    reader.skip(4);
                    return [floatValue, reader.getPosition()];
                } else {
                    logger.error('Buffer too small to read FLOAT value');
                    return [0.0, reader.getPosition()];
                }

            case TypeCode.BINARY_FLOAT:
                // Since SafeBufferReader doesn't have readDoubleLE, we'll use the buffer directly
                // but with proper bounds checking
                if (reader.getPosition() + 8 <= reader.getLength()) {
                    const doubleValue = buffer.readDoubleLE(reader.getPosition());
                    reader.skip(8);
                    return [doubleValue, reader.getPosition()];
                } else {
                    logger.error('Buffer too small to read BINARY_FLOAT value');
                    return [0.0, reader.getPosition()];
                }

            case TypeCode.STRING:
            case TypeCode.INTERNED:
                const strLengthValue = reader.readInt32LE();
                if (strLengthValue === null) {
                    logger.error('Failed to read string length');
                    return ['', reader.getPosition()];
                }

                const strData = reader.readSlice(strLengthValue);
                if (strData === null) {
                    logger.error('Failed to read string data');
                    return ['', reader.getPosition()];
                }

                return [strData.toString('utf-8'), reader.getPosition()];

            case TypeCode.UNICODE:
                const unicodeLengthValue = reader.readInt32LE();
                if (unicodeLengthValue === null) {
                    logger.error('Failed to read unicode string length');
                    return ['', reader.getPosition()];
                }

                const unicodeData = reader.readSlice(unicodeLengthValue);
                if (unicodeData === null) {
                    logger.error('Failed to read unicode string data');
                    return ['', reader.getPosition()];
                }

                return [unicodeData.toString('utf-16le'), reader.getPosition()];

            case TypeCode.ASCII:
            case TypeCode.ASCII_INTERNED:
                const asciiLengthValue = reader.readInt32LE();
                if (asciiLengthValue === null) {
                    logger.error('Failed to read ASCII string length');
                    return ['', reader.getPosition()];
                }

                const asciiData = reader.readSlice(asciiLengthValue);
                if (asciiData === null) {
                    logger.error('Failed to read ASCII string data');
                    return ['', reader.getPosition()];
                }

                return [asciiData.toString('ascii'), reader.getPosition()];

            case TypeCode.SHORT_ASCII:
            case TypeCode.SHORT_ASCII_INTERNED:
                const shortAsciiLength = reader.readUInt8();
                if (shortAsciiLength === null) {
                    logger.error('Failed to read short ASCII string length');
                    return ['', reader.getPosition()];
                }

                const shortAsciiData = reader.readSlice(shortAsciiLength);
                if (shortAsciiData === null) {
                    logger.error('Failed to read short ASCII string data');
                    return ['', reader.getPosition()];
                }

                return [shortAsciiData.toString('ascii'), reader.getPosition()];

            default:
                logger.warn(`Unsupported type code: ${typeCode}`);
                return [null, newOffset];
        }
    } catch (error: any) {
        logger.error(`Error parsing constant: ${error.message || error}`);
        return [null, offset + 1];
    }
}

/**
 * Parses an array of constants from a buffer
 * @param buffer Buffer to parse from
 * @param offset Offset to start parsing from
 * @param count Number of constants to parse
 * @returns The parsed constants and the new offset
 */
export function parseConstants(buffer: Buffer, offset: number, count: number): [any[], number] {
    const constants: any[] = [];
    let currentOffset = offset;

    try {
        // Limit the count to a reasonable number to prevent infinite loops
        const safeCount = Math.min(count, 10000);

        for (let i = 0; i < safeCount; i++) {
            // Check if we've reached the end of the buffer
            if (currentOffset >= buffer.length) {
                logger.warn(`Reached end of buffer while parsing constants at index ${i}/${safeCount}`);
                break;
            }

            const [constant, newOffset] = parseConstant(buffer, currentOffset);
            constants.push(constant);

            // Check if the offset didn't advance, which could cause an infinite loop
            if (newOffset <= currentOffset) {
                logger.warn(`Offset didn't advance while parsing constant at index ${i}/${safeCount}`);
                currentOffset++;
                break;
            }

            currentOffset = newOffset;

            // If we've parsed all the constants, break out of the loop
            if (i === count - 1) {
                break;
            }
        }
    } catch (error: any) {
        logger.error(`Error parsing constants: ${error.message || error}`);
    }

    return [constants, currentOffset];
}
