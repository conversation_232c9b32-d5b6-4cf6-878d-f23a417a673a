import { ConflictDetectionConfig, ConflictAnalysisResult } from '../../types/conflict/interfaces.js';
import { ConflictDetectionResult, ConflictInfo, ConflictSeverity, ConflictType } from '../../types/conflict/index.js';
import { ResourceInfo } from '../../types/resource/interfaces.js';
import { Logger } from '../../utils/logging/logger.js';
import { RuleBasedConflictDetector, DependencyMap } from './RuleBasedConflictDetector.js';
import { LlmConflictDetector } from './LlmConflictDetector.js';

const logger = new Logger('HybridConflictDetector');

/**
 * Hybrid conflict detector that combines rule-based and LLM-based approaches
 * for comprehensive conflict detection.
 */
export class HybridConflictDetector {
    private ruleBasedDetector: RuleBasedConflictDetector;
    private llmDetector: LlmConflictDetector;
    private config: ConflictDetectionConfig;

    /**
     * Create a new hybrid conflict detector
     * @param config Configuration for conflict detection
     */
    constructor(config: ConflictDetectionConfig) {
        this.ruleBasedDetector = new RuleBasedConflictDetector();
        this.llmDetector = new LlmConflictDetector();
        this.config = config;

        logger.info(`Initialized HybridConflictDetector with config: ${JSON.stringify(config)}`);
    }

    /**
     * Detect conflicts between resources using both rule-based and LLM-based approaches
     * with optimized memory usage and performance
     * @param resources List of resources to check for conflicts
     * @param dependencies Map of resource IDs to their dependencies
     * @returns Result of conflict detection
     */
    async detectConflicts(
        resources: ResourceInfo[],
        dependencies: DependencyMap
    ): Promise<ConflictDetectionResult> {
        const startTime = Date.now();
        const conflicts: ConflictInfo[] = [];
        let resourcesCompared = 0;

        // Determine which detection methods to use
        const useRuleBased = this.config.useRuleBased !== false; // Default to true
        const useLlm = this.config.useLlm === true; // Default to false
        const enhanceWithLlm = this.config.enhanceWithLlm === true; // Default to false

        // Limit the number of resources to compare if specified
        const maxResourcesToCompare = this.config.maxResourcesToCompare || resources.length;
        const resourcesToCheck = resources.slice(0, maxResourcesToCompare);

        logger.info(`Starting conflict detection with ${resourcesToCheck.length} resources`);

        // Group resources by TGI to reduce the number of comparisons
        const resourcesByTGI = this.groupResourcesByTGI(resourcesToCheck);
        logger.info(`Grouped resources into ${resourcesByTGI.size} TGI groups`);

        // Process each TGI group
        let groupIndex = 0;
        for (const [tgiKey, resourceGroup] of resourcesByTGI.entries()) {
            groupIndex++;

            // Skip groups with only one resource (no conflicts possible)
            if (resourceGroup.length <= 1) {
                continue;
            }

            logger.debug(`Processing TGI group ${groupIndex}/${resourcesByTGI.size} with ${resourceGroup.length} resources`);

            // Process resources in batches to avoid memory issues
            const batchSize = 50; // Process 50 resource pairs at a time
            for (let i = 0; i < resourceGroup.length; i++) {
                const resource1 = resourceGroup[i];

                // Compare with other resources in the same group
                for (let j = i + 1; j < resourceGroup.length; j++) {
                    const resource2 = resourceGroup[j];
                    resourcesCompared++;

                    // Rule-based detection
                    if (useRuleBased) {
                        await this.performRuleBasedDetection(resource1, resource2, dependencies, conflicts);
                    }

                    // LLM-based detection
                    if (useLlm) {
                        await this.performLlmBasedDetection(resource1, resource2, conflicts);
                    }

                    // Force garbage collection every batchSize comparisons
                    if (resourcesCompared % batchSize === 0) {
                        if (global.gc) {
                            global.gc();
                        }
                    }
                }
            }
        }

        // Enhance rule-based conflicts with LLM if configured
        if (enhanceWithLlm && conflicts.length > 0) {
            await this.enhanceConflictsWithLlm(conflicts, resources);
        }

        // Limit the number of conflicts to return if specified
        const maxConflictsToReturn = this.config.maxConflictsToReturn || conflicts.length;
        const limitedConflicts = conflicts.slice(0, maxConflictsToReturn);

        // Determine which method was used
        let method: 'rule-based' | 'llm-based' | 'hybrid' = 'rule-based';
        if (useRuleBased && useLlm) {
            method = 'hybrid';
        } else if (useLlm) {
            method = 'llm-based';
        }

        return {
            conflicts: limitedConflicts,
            resourcesCompared,
            timestamp: Date.now(),
            method
        };
    }

    /**
     * Perform rule-based conflict detection
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @param dependencies Map of resource IDs to their dependencies
     * @param conflicts Array to add detected conflicts to
     */
    private async performRuleBasedDetection(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        dependencies: DependencyMap,
        conflicts: ConflictInfo[]
    ): Promise<void> {
        try {
            // Check for TGI conflicts
            const tgiConflict = this.ruleBasedDetector.detectTgiConflicts(resource1, resource2);
            if (tgiConflict) {
                conflicts.push(tgiConflict);
                logger.debug(`Detected TGI conflict between ${resource1.id} and ${resource2.id}`);
            }

            // Check for dependency conflicts
            const dependencyConflict = this.ruleBasedDetector.detectDependencyConflicts(
                resource1, resource2, dependencies
            );
            if (dependencyConflict) {
                conflicts.push(dependencyConflict);
                logger.debug(`Detected dependency conflict between ${resource1.id} and ${resource2.id}`);
            }

            // Check for content conflicts
            const contentConflict = this.ruleBasedDetector.detectContentConflicts(resource1, resource2);
            if (contentConflict) {
                conflicts.push(contentConflict);
                logger.debug(`Detected content conflict between ${resource1.id} and ${resource2.id}`);
            }
        } catch (error: any) {
            logger.error(`Error in rule-based detection: ${error.message || error}`);
        }
    }

    /**
     * Perform LLM-based conflict detection
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @param conflicts Array to add detected conflicts to
     */
    private async performLlmBasedDetection(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        conflicts: ConflictInfo[]
    ): Promise<void> {
        try {
            const llmConflict = await this.llmDetector.detectConflicts(resource1, resource2);
            if (llmConflict) {
                conflicts.push(llmConflict);
                logger.debug(`Detected LLM conflict between ${resource1.id} and ${resource2.id}`);
            }
        } catch (error: any) {
            logger.error(`Error in LLM-based detection: ${error.message || error}`);
        }
    }

    /**
     * Group resources by their TGI (Type, Group, Instance) values to optimize conflict detection
     * @param resources List of resources to group
     * @returns Map of TGI keys to arrays of resources
     */
    private groupResourcesByTGI(resources: ResourceInfo[]): Map<string, ResourceInfo[]> {
        const resourcesByTGI = new Map<string, ResourceInfo[]>();

        for (const resource of resources) {
            // Skip resources without key information
            if (!resource.key || !resource.key.type) {
                continue;
            }

            // Create a TGI key for grouping
            // We use different grouping strategies based on resource type
            let tgiKey: string;

            // For most resources, group by type only to catch conflicts between resources of the same type
            tgiKey = `${resource.key.type}`;

            // Add resource to the appropriate group
            if (!resourcesByTGI.has(tgiKey)) {
                resourcesByTGI.set(tgiKey, []);
            }
            resourcesByTGI.get(tgiKey)!.push(resource);

            // For resources with instance and group, also add to a more specific group
            if (resource.key.instance !== undefined && resource.key.group !== undefined) {
                const specificKey = `${resource.key.type}-${resource.key.group}-${resource.key.instance}`;
                if (!resourcesByTGI.has(specificKey)) {
                    resourcesByTGI.set(specificKey, []);
                }
                resourcesByTGI.get(specificKey)!.push(resource);
            }
        }

        return resourcesByTGI;
    }

    /**
     * Enhance rule-based conflicts with LLM analysis
     * @param conflicts List of conflicts to enhance
     * @param resources List of resources
     */
    private async enhanceConflictsWithLlm(
        conflicts: ConflictInfo[],
        resources: ResourceInfo[]
    ): Promise<void> {
        // Skip if no conflicts to enhance
        if (conflicts.length === 0) return;

        logger.info(`Enhancing ${conflicts.length} conflicts with LLM`);

        // Create a map of resource IDs to resources for quick lookup
        const resourceMap = new Map<number, ResourceInfo>();
        resources.forEach(resource => {
            if (resource.id) {
                resourceMap.set(resource.id, resource);
            }
        });

        // Enhance each conflict
        for (const conflict of conflicts) {
            try {
                // Find the resources involved in the conflict
                const affectedResourceIds: number[] = [];

                // Find resources by key
                for (const resourceKey of conflict.affectedResources) {
                    const matchingResource = resources.find(r =>
                        r.key.type === resourceKey.type &&
                        r.key.group === resourceKey.group &&
                        r.key.instance === resourceKey.instance
                    );

                    if (matchingResource && matchingResource.id) {
                        affectedResourceIds.push(matchingResource.id);
                    }
                }

                // Skip if we can't find at least two resources
                if (affectedResourceIds.length < 2) {
                    logger.warn(`Could not find resources for conflict ${conflict.id}`);
                    continue;
                }

                // Get the resources
                const resource1 = resourceMap.get(affectedResourceIds[0]);
                const resource2 = resourceMap.get(affectedResourceIds[1]);

                // Skip if we can't find both resources
                if (!resource1 || !resource2) {
                    logger.warn(`Could not find resources for conflict ${conflict.id}`);
                    continue;
                }

                // Enhance the conflict
                const enhancedInfo = await this.llmDetector.enhanceConflictInfo(
                    conflict, resource1, resource2
                );

                // Update the conflict with enhanced info
                if (enhancedInfo) {
                    if (enhancedInfo.description) {
                        conflict.description = enhancedInfo.description;
                    }

                    if (enhancedInfo.recommendations) {
                        conflict.recommendations = enhancedInfo.recommendations;
                    }

                    if (enhancedInfo.confidence) {
                        conflict.confidence = enhancedInfo.confidence;
                    }

                    logger.debug(`Enhanced conflict ${conflict.id} with LLM`);
                }
            } catch (error: any) {
                logger.error(`Error enhancing conflict ${conflict.id}: ${error.message || error}`);
            }
        }
    }
}
