/**
 * Utility functions for JSON serialization
 */

/**
 * Custom JSON replacer function that handles BigInt values and circular references
 * @param key The key being stringified
 * @param value The value being stringified
 * @returns The processed value
 */
export function jsonReplacer(key: string, value: any): any {
  // Handle circular references
  if (typeof value === 'object' && value !== null) {
    if ((this as any).seen && (this as any).seen.has(value)) {
      return '[Circular]';
    }
    if ((this as any).seen) {
      (this as any).seen.add(value);
    }
  }

  // Handle BigInt
  if (typeof value === 'bigint') {
    return value.toString();
  }

  return value;
}

/**
 * Custom JSON reviver function that restores BigInt values
 * @param key The key being parsed
 * @param value The value being parsed
 * @returns The processed value
 */
export function jsonReviver(key: string, value: any): any {
  if (typeof value === 'string' && /^\d+n$/.test(value)) {
    return BigInt(value.slice(0, -1));
  }
  return value;
}

/**
 * Safely stringify a value that may contain BigInt values and circular references
 * @param value The value to stringify
 * @returns The JSON string
 */
export function safeStringify(value: any): string {
  const context = { seen: new WeakSet() };
  return JSON.stringify(value, jsonReplacer.bind(context));
}

/**
 * Safely parse a JSON string that may contain BigInt values
 * @param text The JSON string to parse
 * @param defaultValue Optional default value to return if parsing fails
 * @returns The parsed value or default value if parsing fails
 */
export function safeParse<T>(text: string, defaultValue?: T): any {
  try {
    return JSON.parse(text, jsonReviver);
  } catch (error) {
    console.error(`Error parsing JSON: ${error}`);
    return defaultValue !== undefined ? defaultValue : null;
  }
}

/**
 * Convert an object to a safe JSON object that can be stringified
 * @param obj The object to convert
 * @param seen WeakSet to track circular references
 * @returns A safe JSON object
 */
export function toSafeJsonObject(obj: any, seen?: WeakSet<object>): any {
  // Initialize seen set if not provided
  if (!seen) {
    seen = new WeakSet();
  }

  // Handle null/undefined
  if (obj === null || obj === undefined) {
    return null;
  }

  // Handle BigInt
  if (typeof obj === 'bigint') {
    return obj.toString();
  }

  // Handle primitives
  if (typeof obj !== 'object') {
    return obj;
  }

  // Handle circular references
  if (seen.has(obj)) {
    return '[Circular]';
  }

  // Add object to seen set
  seen.add(obj);

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => toSafeJsonObject(item, seen));
  }

  // Handle objects
  const result: Record<string, any> = {};
  for (const [key, value] of Object.entries(obj)) {
    result[key] = toSafeJsonObject(value, seen);
  }

  return result;
}
