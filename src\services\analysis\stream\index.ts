/**
 * Stream Module Index
 * 
 * This module exports all the stream-related components.
 */

// Core interfaces and base classes
export { IStreamPipeline, StreamPipelineBase, StreamPipelineOptions, PipelineStats } from './streamPipelineBase.js';
export { IResourceProvider, ResourceProviderBase, ResourceStreamOptions, ResourceMetadata } from './resourceProviderBase.js';

// Consolidated implementations
export { ConsolidatedStreamPipeline, ConsolidatedStreamPipelineOptions } from './consolidatedStreamPipeline.js';
export { ConsolidatedResourceProvider, ConsolidatedResourceProviderOptions } from './consolidatedResourceProvider.js';

// Compatibility layers
export { StreamPipeline } from './compatibility/streamPipelineCompat.js';
export { EnhancedStreamPipeline, EnhancedStreamPipelineOptions } from './compatibility/enhancedStreamPipelineCompat.js';
export { ResourceStreamProvider } from './compatibility/resourceStreamProviderCompat.js';
export { StreamingPackageReader, StreamingPackageReaderOptions, PackageIndexEntry } from './compatibility/streamingPackageReaderCompat.js';

// Re-export transformer interfaces and implementations
export { IStreamTransformer, StreamTransformerStats, StreamTransformerOptions } from './baseStreamTransformer.js';
