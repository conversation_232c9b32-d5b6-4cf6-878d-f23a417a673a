/**
 * Python Bytecode Parser
 *
 * This module provides functionality for parsing Python bytecode files (.pyc)
 * found in TS4Script files.
 *
 * Enhanced with multi-strategy parsing and EA-specific format support.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import * as path from 'path';
import {
    getPythonVersionFromMagic,
    extractStringLiterals as extractStringLiteralsFromBuffer,
    hasPythonBytecodeHeader
} from './utils.js';
import { parseHeader as parseHeaderFromBuffer } from './headerParser.js';
import { parseCodeObject as parseCodeObjectFromBuffer } from './codeObjectParser.js';
import {
    parseInstructions as parseInstructionsFromCodeObject,
    extractImportsFromInstructions,
    extractClassesFromInstructions,
    extractFunctionsFromInstructions
} from './instructionParser.js';
import { SafeBufferReader } from './safeBufferReader.js';
import { BytecodeParserBase } from './bytecodeParserBase.js';

/**
 * Python bytecode magic numbers
 */
export const PYTHON_MAGIC_NUMBERS = {
    // Python 3.6
    '3.6.0': 0x0A0D0D33,
    '3.6.1': 0x0A0D0D33,
    '3.6.2': 0x0A0D0D33,
    '3.6.3': 0x0A0D0D33,
    '3.6.4': 0x0A0D0D33,
    '3.6.5': 0x0A0D0D33,
    '3.6.6': 0x0A0D0D33,
    '3.6.7': 0x0A0D0D33,
    '3.6.8': 0x0A0D0D33,
    '3.6.9': 0x0A0D0D33,
    '3.6.10': 0x0A0D0D33,
    '3.6.11': 0x0A0D0D33,
    '3.6.12': 0x0A0D0D33,
    '3.6.13': 0x0A0D0D33,
    '3.6.14': 0x0A0D0D33,
    '3.6.15': 0x0A0D0D33,

    // Python 3.7
    '3.7.0': 0x0A0D0D42,
    '3.7.1': 0x0A0D0D42,
    '3.7.2': 0x0A0D0D42,
    '3.7.3': 0x0A0D0D42,
    '3.7.4': 0x0A0D0D42,
    '3.7.5': 0x0A0D0D42,
    '3.7.6': 0x0A0D0D42,
    '3.7.7': 0x0A0D0D42,
    '3.7.8': 0x0A0D0D42,
    '3.7.9': 0x0A0D0D42,
    '3.7.10': 0x0A0D0D42,
    '3.7.11': 0x0A0D0D42,
    '3.7.12': 0x0A0D0D42,

    // Python 3.8
    '3.8.0': 0x0A0D0D55,
    '3.8.1': 0x0A0D0D55,
    '3.8.2': 0x0A0D0D55,
    '3.8.3': 0x0A0D0D55,
    '3.8.4': 0x0A0D0D55,
    '3.8.5': 0x0A0D0D55,
    '3.8.6': 0x0A0D0D55,
    '3.8.7': 0x0A0D0D55,
    '3.8.8': 0x0A0D0D55,
    '3.8.9': 0x0A0D0D55,
    '3.8.10': 0x0A0D0D55,

    // Python 3.9
    '3.9.0': 0x0A0D0D61,
    '3.9.1': 0x0A0D0D61,
    '3.9.2': 0x0A0D0D61,
    '3.9.3': 0x0A0D0D61,
    '3.9.4': 0x0A0D0D61,
    '3.9.5': 0x0A0D0D61,
    '3.9.6': 0x0A0D0D61,
    '3.9.7': 0x0A0D0D61,
};

/**
 * EA-specific magic numbers based on research and observation
 * These are magic numbers found in Sims 4 mods that don't match standard Python magic numbers
 */
export const EA_MAGIC_NUMBERS: Record<number, string> = {
    // EA Python 3.7 variants
    0x0A0D0D43: 'EA Python 3.7',
    0x0A0D0D44: 'EA Python 3.7 (Variant 2)',
    0x0A0D0D45: 'EA Python 3.7 (Variant 3)',
    0x0A0D0D46: 'EA Python 3.7 (Variant 4)',
    0x0A0D0D47: 'EA Python 3.7 (Variant 5)',
    0x0A0D0D48: 'EA Python 3.7 (Variant 6)',
    0x0A0D0D49: 'EA Python 3.7 (Variant 7)',

    // EA Python 3.8 variants
    0x0A0D0D56: 'EA Python 3.8',
    0x0A0D0D57: 'EA Python 3.8 (Variant 2)',
    0x0A0D0D58: 'EA Python 3.8 (Variant 3)',
    0x0A0D0D59: 'EA Python 3.8 (Variant 4)',

    // EA Python 3.9 variants
    0x0A0D0D62: 'EA Python 3.9',
    0x0A0D0D63: 'EA Python 3.9 (Variant 2)',

    // Common custom magic numbers found in Sims 4 mods
    0x0A0D0D0A: 'Custom Python (Variant 1)',
    0x0A0D0D0B: 'Custom Python (Variant 2)',
    0x0A0D0D0C: 'Custom Python (Variant 3)',
    0x0A0D0D0D: 'Custom Python (Variant 4)',
    0x0A0D0D0E: 'Custom Python (Variant 5)',
    0x0A0D0D0F: 'Custom Python (Variant 6)',
};

/**
 * Python bytecode header
 */
export interface BytecodeHeader {
    /**
     * Magic number
     */
    magicNumber: number;

    /**
     * Python version
     */
    pythonVersion: string;

    /**
     * Timestamp
     */
    timestamp: number;

    /**
     * Source size
     */
    sourceSize: number;
}

/**
 * Python code object
 */
export interface CodeObject {
    /**
     * Code object type
     */
    type: 'module' | 'function' | 'class' | 'comprehension';

    /**
     * Argument count
     */
    argCount: number;

    /**
     * Positional-only argument count
     */
    posOnlyArgCount: number;

    /**
     * Keyword-only argument count
     */
    kwOnlyArgCount: number;

    /**
     * Number of local variables
     */
    nLocals: number;

    /**
     * Stack size
     */
    stackSize: number;

    /**
     * Flags
     */
    flags: number;

    /**
     * Bytecode
     */
    bytecode: Buffer;

    /**
     * Constants
     */
    constants: any[];

    /**
     * Names
     */
    names: string[];

    /**
     * Variable names
     */
    varNames: string[];

    /**
     * Free variables
     */
    freeVars: string[];

    /**
     * Cell variables
     */
    cellVars: string[];

    /**
     * Filename
     */
    filename: string;

    /**
     * Name
     */
    name: string;

    /**
     * First line number
     */
    firstLineNo: number;

    /**
     * Line number table
     */
    lnotab: Buffer;

    /**
     * Nested code objects
     */
    nestedCodeObjects: CodeObject[];
}

/**
 * Python bytecode instruction
 */
export interface BytecodeInstruction {
    /**
     * Offset
     */
    offset: number;

    /**
     * Opcode
     */
    opcode: number;

    /**
     * Opcode name
     */
    opcodeName: string;

    /**
     * Argument
     */
    arg?: number;

    /**
     * Argument value
     */
    argValue?: any;

    /**
     * Line number
     */
    lineNo?: number;
}

/**
 * Python bytecode parser
 *
 * Enhanced with multi-strategy parsing and EA-specific format support.
 */
@singleton()
export class BytecodeParser extends BytecodeParserBase {
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(private readonly loggerInstance: Logger = new Logger('BytecodeParser')) {
        super();
        this.logger = loggerInstance;
    }

    /**
     * Parse bytecode file
     * @param buffer Bytecode buffer
     * @param filename Filename
     * @returns Parsed code object
     */
    public parseBytecode(buffer: Buffer, filename: string): CodeObject | null {
        try {
            this.logger.info(`Parsing bytecode for ${filename}`);

            // Check if the buffer is too small to be valid bytecode
            if (buffer.length < 8) {
                this.logger.error(`Buffer too small to be valid bytecode: ${filename} (${buffer.length} bytes)`);
                return null;
            }

            // Check if the buffer contains a valid Python bytecode header
            const hasValidHeader = hasPythonBytecodeHeader(buffer);
            if (!hasValidHeader) {
                this.logger.warn(`No standard Python bytecode header detected in ${filename}, will try alternative parsing strategies`);
                // Create a minimal code object with string extraction
                return this.createMinimalCodeObject(buffer, filename);
            }

            // Parse header
            const header = this.parseHeader(buffer);
            if (!header) {
                this.logger.warn(`Failed to parse standard bytecode header for ${filename}, will try alternative parsing strategies`);
                // Create a minimal code object with string extraction
                return this.createMinimalCodeObject(buffer, filename);
            }

            this.logger.debug(`Parsed bytecode header for ${filename}: Python ${header.pythonVersion}, timestamp ${new Date(header.timestamp * 1000).toISOString()}`);

            // Get Python version (default to 3.7 if unknown)
            const pythonVersion = header ? this.getPythonVersionObject(header.pythonVersion) : { major: 3, minor: 7, micro: 0 };

            // Parse the code object
            const codeObject = this.parseCodeObject(buffer.slice(8), filename);
            if (!codeObject) {
                this.logger.error(`Failed to parse code object for ${filename}, will try alternative parsing strategies`);
                // Create a minimal code object with string extraction
                return this.createMinimalCodeObject(buffer, filename);
            }

            return codeObject;
        } catch (error) {
            this.logger.error(`Error parsing bytecode for ${filename}:`, error);
            // Create a minimal code object as a fallback
            try {
                return this.createMinimalCodeObject(buffer, filename);
            } catch (fallbackError) {
                this.logger.error(`Minimal code object creation also failed for ${filename}:`, fallbackError);
                return null;
            }
        }
    }

    /**
     * Create a minimal code object with string extraction
     * @param buffer Buffer to extract strings from
     * @param filename Filename
     * @returns Minimal code object
     */
    private createMinimalCodeObject(buffer: Buffer, filename: string): CodeObject {
        // Extract string literals and potential Python identifiers
        const stringLiterals = extractStringLiteralsFromBuffer(buffer);
        const pythonIdentifiers = this.extractPythonIdentifiers(buffer);

        // Combine unique strings
        const allStrings = [...new Set([...stringLiterals, ...pythonIdentifiers])];

        // Filter for likely Python names (functions, classes, variables)
        const likelyNames = allStrings.filter(s => /^[A-Za-z_][A-Za-z0-9_]*$/.test(s));

        // Create minimal code object
        return {
            type: 'module',
            argCount: 0,
            posOnlyArgCount: 0,
            kwOnlyArgCount: 0,
            nLocals: 0,
            stackSize: 0,
            flags: 0,
            bytecode: Buffer.alloc(0),
            constants: allStrings,
            names: likelyNames,
            varNames: [],
            freeVars: [],
            cellVars: [],
            filename,
            name: path.basename(filename, path.extname(filename)),
            firstLineNo: 1,
            lnotab: Buffer.alloc(0),
            nestedCodeObjects: []
        };
    }

    /**
     * Check if a code object is valid
     * @param codeObject Code object to check
     * @returns True if the code object is valid
     */
    private isValidCodeObject(codeObject: CodeObject): boolean {
        // Check for minimum required properties
        if (!codeObject.bytecode || !codeObject.constants || !codeObject.names) {
            return false;
        }

        // Check for reasonable values
        if (codeObject.bytecode.length === 0) {
            return false;
        }

        // Check for reasonable stack size
        if (codeObject.stackSize < 0 || codeObject.stackSize > 1000) {
            return false;
        }

        return true;
    }

    /**
     * Find potential code object markers in a buffer
     * @param buffer Buffer to search
     * @returns Array of positions where code object markers were found
     */
    private findCodeObjectMarkers(buffer: Buffer): number[] {
        const positions: number[] = [];

        // Common code object markers (type codes for code objects in Python marshalled data)
        const markers = [0x63, 0x43]; // 'c' and 'C' in ASCII

        // Search for markers
        for (let i = 0; i < buffer.length - 8; i++) {
            if (markers.includes(buffer[i])) {
                // Check if followed by reasonable values for a code object
                const argCount = buffer.readUInt32LE(i + 4);
                if (argCount < 256) { // Reasonable max number of arguments
                    positions.push(i);
                }
            }
        }

        return positions;
    }

    /**
     * Extract potential Python identifiers from a buffer
     * @param buffer Buffer to extract from
     * @returns Array of potential Python identifiers
     */
    private extractPythonIdentifiers(buffer: Buffer): string[] {
        const identifiers: string[] = [];
        let currentIdentifier = '';

        // Common Python keywords and built-ins to help identify Python code
        const pythonKeywords = new Set([
            'import', 'from', 'class', 'def', 'return', 'if', 'else', 'elif', 'for', 'while',
            'try', 'except', 'finally', 'with', 'as', 'lambda', 'yield', 'global', 'nonlocal',
            'True', 'False', 'None', 'and', 'or', 'not', 'is', 'in', 'self', 'super',
            'sims4', 'services', 'objects', 'interactions', 'buffs', 'traits', 'tuning'
        ]);

        // Scan buffer for ASCII characters that could be Python identifiers
        for (let i = 0; i < buffer.length; i++) {
            const char = buffer[i];

            // Check if character is a valid Python identifier character
            if ((char >= 65 && char <= 90) || // A-Z
                (char >= 97 && char <= 122) || // a-z
                (char === 95) || // underscore
                (currentIdentifier.length > 0 && char >= 48 && char <= 57)) { // 0-9 (not first char)

                currentIdentifier += String.fromCharCode(char);
            } else if (currentIdentifier.length > 0) {
                // End of identifier
                if (currentIdentifier.length >= 2) { // Ignore single-character identifiers
                    // Prioritize known Python keywords
                    if (pythonKeywords.has(currentIdentifier)) {
                        identifiers.push(currentIdentifier);
                    } else if (/^[A-Za-z_][A-Za-z0-9_]*$/.test(currentIdentifier)) {
                        identifiers.push(currentIdentifier);
                    }
                }
                currentIdentifier = '';
            }
        }

        // Add final identifier if there is one
        if (currentIdentifier.length >= 2) {
            if (pythonKeywords.has(currentIdentifier) || /^[A-Za-z_][A-Za-z0-9_]*$/.test(currentIdentifier)) {
                identifiers.push(currentIdentifier);
            }
        }

        // Return unique identifiers
        return [...new Set(identifiers)];
    }

    /**
     * Parse bytecode with fallback strategies
     * @param buffer Bytecode buffer
     * @param filename Filename
     * @returns Parsed code object and confidence level
     */
    public parseBytecodeWithFallback(buffer: Buffer, filename: string): { codeObject: CodeObject | null, confidence: number } {
        try {
            // Try standard parsing
            const codeObject = this.parseBytecode(buffer, filename);
            if (codeObject) {
                // Determine confidence level based on available information
                let confidence = 0.5;

                if (codeObject.constants.length > 0) confidence += 0.1;
                if (codeObject.names.length > 0) confidence += 0.1;
                if (codeObject.bytecode.length > 0) confidence += 0.2;
                if (codeObject.nestedCodeObjects.length > 0) confidence += 0.1;

                return { codeObject, confidence };
            }

            return { codeObject: null, confidence: 0 };
        } catch (error) {
            this.logger.error(`Error in parseBytecodeWithFallback for ${filename}:`, error);
            return { codeObject: null, confidence: 0 };
        }
    }

    /**
     * Get Python version object from version string
     * @param versionString Python version string (e.g., '3.7.0')
     * @returns Python version object or null if not recognized
     */
    private getPythonVersionObject(versionString: string): { major: number, minor: number, micro: number } | null {
        try {
            // Check if version string is in the format 'x.y.z'
            const match = versionString.match(/^(\d+)\.(\d+)\.(\d+)$/);
            if (match) {
                const major = parseInt(match[1], 10);
                const minor = parseInt(match[2], 10);
                const micro = parseInt(match[3], 10);

                return { major, minor, micro };
            }

            // Check if version string is in the format 'x.y'
            const shortMatch = versionString.match(/^(\d+)\.(\d+)$/);
            if (shortMatch) {
                const major = parseInt(shortMatch[1], 10);
                const minor = parseInt(shortMatch[2], 10);

                return { major, minor, micro: 0 };
            }

            // Check if version string contains 'Python' (e.g., 'Python 3.7')
            const pythonMatch = versionString.match(/Python\s+(\d+)\.(\d+)/i);
            if (pythonMatch) {
                const major = parseInt(pythonMatch[1], 10);
                const minor = parseInt(pythonMatch[2], 10);

                return { major, minor, micro: 0 };
            }

            // Check if version string is 'unknown' or empty
            if (versionString === 'unknown' || !versionString) {
                // Default to Python 3.7 (most common in Sims 4)
                return { major: 3, minor: 7, micro: 0 };
            }

            return null;
        } catch (error) {
            this.logger.error(`Error parsing Python version string: ${versionString}`, error);
            return null;
        }
    }

    /**
     * Parse bytecode header
     * @param buffer Bytecode buffer
     * @returns Parsed header
     */
    private parseHeader(buffer: Buffer): BytecodeHeader | null {
        try {
            if (buffer.length < 16) {
                this.logger.error(`Bytecode buffer too small: ${buffer.length} bytes`);
                return null;
            }

            // Read magic number
            const magicNumber = buffer.readUInt32LE(0);

            // Determine Python version
            let pythonVersion = 'unknown';
            for (const [version, magic] of Object.entries(PYTHON_MAGIC_NUMBERS)) {
                if (magic === magicNumber) {
                    pythonVersion = version;
                    break;
                }
            }

            // Read timestamp
            const timestamp = buffer.readUInt32LE(4);

            // Read source size (Python 3.7+)
            const sourceSize = buffer.readUInt32LE(8);

            return {
                magicNumber,
                pythonVersion,
                timestamp,
                sourceSize
            };
        } catch (error) {
            this.logger.error('Error parsing bytecode header:', error);
            return null;
        }
    }

    /**
     * Parse code object
     * @param buffer Bytecode buffer
     * @param filename Filename
     * @returns Parsed code object
     */
    private parseCodeObject(buffer: Buffer, filename: string): CodeObject | null {
        try {
            this.logger.debug(`Parsing code object for ${filename}`);

            // Create a safe buffer reader
            const reader = new SafeBufferReader(buffer);

            // Read the type of the code object (should be 'c' for code)
            const typeByte = reader.readUInt8();
            if (typeByte === null) {
                this.logger.error('Failed to read type code byte');
                return null;
            }

            const type = String.fromCharCode(typeByte);
            if (type !== 'c') {
                this.logger.error(`Invalid code object type: ${type}`);
                return null;
            }

            // Read the argument count
            const argCount = reader.readUInt32LE() ?? 0;

            // Read the positional-only argument count (Python 3.8+, set to 0 for Python 3.7)
            const posOnlyArgCount = 0;

            // Read the keyword-only argument count
            const kwOnlyArgCount = reader.readUInt32LE() ?? 0;

            // Read the number of local variables
            const nLocals = reader.readUInt32LE() ?? 0;

            // Read the stack size
            const stackSize = reader.readUInt32LE() ?? 0;

            // Read the flags
            const flags = reader.readUInt32LE() ?? 0;

            // Read the bytecode
            const bytecodeSize = reader.readUInt32LE() ?? 0;
            const bytecode = reader.readSlice(bytecodeSize) ?? Buffer.alloc(0);

            // Read the constants
            const constantsCount = reader.readUInt32LE() ?? 0;
            const constants: any[] = [];

            for (let i = 0; i < constantsCount; i++) {
                const constantTypeByte = reader.readUInt8();
                if (constantTypeByte === null) {
                    this.logger.warn(`Failed to read constant type byte at index ${i}`);
                    constants.push(null);
                    continue;
                }

                const constantType = String.fromCharCode(constantTypeByte);

                switch (constantType) {
                    case 'N': // None
                        constants.push(null);
                        break;
                    case 'T': // True
                        constants.push(true);
                        break;
                    case 'F': // False
                        constants.push(false);
                        break;
                    case 'i': // Integer
                        const intValue = reader.readInt32LE();
                        constants.push(intValue !== null ? intValue : 0);
                        break;
                    case 'f': // Float
                        // Since SafeBufferReader doesn't have readDoubleLE, we'll use the buffer directly
                        // but with proper bounds checking
                        if (reader.getPosition() + 8 <= reader.getLength()) {
                            constants.push(buffer.readDoubleLE(reader.getPosition()));
                            reader.skip(8);
                        } else {
                            this.logger.warn(`Buffer too small to read float at position ${reader.getPosition()}`);
                            constants.push(0.0);
                        }
                        break;
                    case 's': // String
                    case 'u': // Unicode string
                        const strSize = reader.readUInt32LE() ?? 0;
                        const strData = reader.readSlice(strSize);
                        if (strData !== null) {
                            constants.push(strData.toString('utf8'));
                        } else {
                            this.logger.warn(`Failed to read string data of size ${strSize}`);
                            constants.push('');
                        }
                        break;
                    case 'c': // Code object
                        // Recursively parse nested code object
                        if (reader.remaining() > 0) {
                            const nestedCodeObject = this.parseCodeObject(buffer.slice(reader.getPosition()), filename);
                            if (nestedCodeObject) {
                                constants.push(nestedCodeObject);
                                // Skip the nested code object in the buffer
                                // This is a simplification; in a real implementation, we would need to
                                // calculate the size of the nested code object
                                reader.skip(Math.min(1000, reader.remaining())); // Arbitrary large value with safety check
                            } else {
                                constants.push(null);
                            }
                        } else {
                            this.logger.warn('Buffer too small to read nested code object');
                            constants.push(null);
                        }
                        break;
                    case 't': // Tuple
                        const tupleSize = reader.readUInt32LE() ?? 0;
                        const tuple: any[] = [];
                        for (let j = 0; j < tupleSize; j++) {
                            // Recursively parse tuple elements
                            // This is a simplification; in a real implementation, we would need to
                            // parse the tuple elements properly
                            tuple.push(null);
                        }
                        constants.push(tuple);
                        break;
                    default:
                        this.logger.warn(`Unknown constant type: ${constantType}`);
                        constants.push(null);
                        break;
                }
            }

            // Read the names
            const namesCount = reader.readUInt32LE() ?? 0;
            const names: string[] = [];

            for (let i = 0; i < namesCount; i++) {
                const nameSize = reader.readUInt32LE() ?? 0;
                const nameData = reader.readSlice(nameSize);
                if (nameData !== null) {
                    names.push(nameData.toString('utf8'));
                } else {
                    this.logger.warn(`Failed to read name data of size ${nameSize}`);
                    names.push('');
                }
            }

            // Read the variable names
            const varNamesCount = reader.readUInt32LE() ?? 0;
            const varNames: string[] = [];

            for (let i = 0; i < varNamesCount; i++) {
                const varNameSize = reader.readUInt32LE() ?? 0;
                const varNameData = reader.readSlice(varNameSize);
                if (varNameData !== null) {
                    varNames.push(varNameData.toString('utf8'));
                } else {
                    this.logger.warn(`Failed to read varName data of size ${varNameSize}`);
                    varNames.push('');
                }
            }

            // Read the free variables
            const freeVarsCount = reader.readUInt32LE() ?? 0;
            const freeVars: string[] = [];

            for (let i = 0; i < freeVarsCount; i++) {
                const freeVarSize = reader.readUInt32LE() ?? 0;
                const freeVarData = reader.readSlice(freeVarSize);
                if (freeVarData !== null) {
                    freeVars.push(freeVarData.toString('utf8'));
                } else {
                    this.logger.warn(`Failed to read freeVar data of size ${freeVarSize}`);
                    freeVars.push('');
                }
            }

            // Read the cell variables
            const cellVarsCount = reader.readUInt32LE() ?? 0;
            const cellVars: string[] = [];

            for (let i = 0; i < cellVarsCount; i++) {
                const cellVarSize = reader.readUInt32LE() ?? 0;
                const cellVarData = reader.readSlice(cellVarSize);
                if (cellVarData !== null) {
                    cellVars.push(cellVarData.toString('utf8'));
                } else {
                    this.logger.warn(`Failed to read cellVar data of size ${cellVarSize}`);
                    cellVars.push('');
                }
            }

            // Read the filename
            const filenameSize = reader.readUInt32LE() ?? 0;
            let codeFilename = '';
            const filenameData = reader.readSlice(filenameSize);
            if (filenameData !== null) {
                codeFilename = filenameData.toString('utf8');
            } else {
                this.logger.warn(`Failed to read filename data of size ${filenameSize}`);
            }

            // Read the name
            const nameSize = reader.readUInt32LE() ?? 0;
            let name = '';
            const nameData = reader.readSlice(nameSize);
            if (nameData !== null) {
                name = nameData.toString('utf8');
            } else {
                this.logger.warn(`Failed to read name data of size ${nameSize}`);
            }

            // Read the first line number
            const firstLineNo = reader.readUInt32LE() ?? 1;

            // Read the line number table
            const lnotabSize = reader.readUInt32LE() ?? 0;
            const lnotab = reader.readSlice(lnotabSize) ?? Buffer.alloc(0);

            // Extract nested code objects from constants
            const nestedCodeObjects: CodeObject[] = [];
            for (const constant of constants) {
                if (constant && typeof constant === 'object' && 'type' in constant) {
                    nestedCodeObjects.push(constant as CodeObject);
                }
            }

            // Determine code object type
            let codeObjectType: 'module' | 'function' | 'class' | 'comprehension' = 'function';
            if (name === '<module>') {
                codeObjectType = 'module';
            } else if (name.startsWith('<genexpr>') || name.startsWith('<listcomp>') || name.startsWith('<dictcomp>') || name.startsWith('<setcomp>')) {
                codeObjectType = 'comprehension';
            } else if (name.startsWith('<locals>')) {
                codeObjectType = 'class';
            }

            return {
                type: codeObjectType,
                argCount,
                posOnlyArgCount,
                kwOnlyArgCount,
                nLocals,
                stackSize,
                flags,
                bytecode,
                constants,
                names,
                varNames,
                freeVars,
                cellVars,
                filename: codeFilename || filename,
                name,
                firstLineNo,
                lnotab,
                nestedCodeObjects
            };
        } catch (error) {
            this.logger.error(`Error parsing code object for ${filename}:`, error);
            return null;
        }
    }

    /**
     * Disassemble bytecode
     * @param codeObject Code object
     * @returns Bytecode instructions
     */
    public disassemble(codeObject: CodeObject): BytecodeInstruction[] {
        try {
            this.logger.debug(`Disassembling bytecode for ${codeObject.name}`);

            const bytecode = codeObject.bytecode;
            const instructions: BytecodeInstruction[] = [];

            // Parse line number table
            const lineNumberTable = this.parseLineNumberTable(codeObject.lnotab, codeObject.firstLineNo);

            // Disassemble bytecode
            let offset = 0;
            while (offset < bytecode.length) {
                // Read opcode
                const opcode = bytecode[offset];
                offset += 1;

                // Get opcode name
                const opcodeName = this.getOpcodeName(opcode);

                // Create instruction
                const instruction: BytecodeInstruction = {
                    offset: offset - 1,
                    opcode,
                    opcodeName
                };

                // Check if opcode has argument
                if (opcode >= 90) { // HAVE_ARGUMENT
                    // Read argument
                    const arg = bytecode[offset] + (bytecode[offset + 1] << 8);
                    offset += 2;

                    instruction.arg = arg;

                    // Resolve argument value based on opcode
                    switch (opcode) {
                        case 90: // STORE_NAME
                        case 91: // DELETE_NAME
                        case 92: // LOAD_NAME
                        case 100: // LOAD_CONST
                        case 101: // LOAD_ATTR
                        case 106: // LOAD_GLOBAL
                        case 124: // LOAD_METHOD
                            instruction.argValue = this.resolveArgValue(opcode, arg, codeObject);
                            break;
                    }
                }

                // Determine line number
                instruction.lineNo = this.getLineNumber(instruction.offset, lineNumberTable);

                instructions.push(instruction);
            }

            return instructions;
        } catch (error) {
            this.logger.error(`Error disassembling bytecode for ${codeObject.name}:`, error);
            return [];
        }
    }

    /**
     * Parse line number table
     * @param lnotab Line number table
     * @param firstLineNo First line number
     * @returns Line number table
     */
    private parseLineNumberTable(lnotab: Buffer, firstLineNo: number): Map<number, number> {
        const lineNumberTable = new Map<number, number>();

        let line = firstLineNo;
        let addr = 0;

        for (let i = 0; i < lnotab.length; i += 2) {
            addr += lnotab[i];
            line += lnotab[i + 1];
            lineNumberTable.set(addr, line);
        }

        return lineNumberTable;
    }

    /**
     * Get line number for offset
     * @param offset Bytecode offset
     * @param lineNumberTable Line number table
     * @returns Line number
     */
    private getLineNumber(offset: number, lineNumberTable: Map<number, number>): number {
        let lineNo = 0;
        let lastAddr = 0;

        for (const [addr, line] of lineNumberTable.entries()) {
            if (addr > offset) {
                break;
            }
            lastAddr = addr;
            lineNo = line;
        }

        return lineNo;
    }

    /**
     * Get opcode name
     * @param opcode Opcode
     * @returns Opcode name
     */
    private getOpcodeName(opcode: number): string {
        // Python 3.7 opcodes
        const opcodeNames: Record<number, string> = {
            1: 'POP_TOP',
            2: 'ROT_TWO',
            3: 'ROT_THREE',
            4: 'DUP_TOP',
            5: 'DUP_TOP_TWO',
            9: 'NOP',
            10: 'UNARY_POSITIVE',
            11: 'UNARY_NEGATIVE',
            12: 'UNARY_NOT',
            15: 'UNARY_INVERT',
            16: 'BINARY_MATRIX_MULTIPLY',
            17: 'INPLACE_MATRIX_MULTIPLY',
            19: 'BINARY_POWER',
            20: 'BINARY_MULTIPLY',
            22: 'BINARY_MODULO',
            23: 'BINARY_ADD',
            24: 'BINARY_SUBTRACT',
            25: 'BINARY_SUBSCR',
            26: 'BINARY_FLOOR_DIVIDE',
            27: 'BINARY_TRUE_DIVIDE',
            28: 'INPLACE_FLOOR_DIVIDE',
            29: 'INPLACE_TRUE_DIVIDE',
            50: 'GET_AITER',
            51: 'GET_ANEXT',
            52: 'BEFORE_ASYNC_WITH',
            53: 'BEGIN_FINALLY',
            54: 'END_ASYNC_FOR',
            55: 'INPLACE_ADD',
            56: 'INPLACE_SUBTRACT',
            57: 'INPLACE_MULTIPLY',
            59: 'INPLACE_MODULO',
            60: 'STORE_SUBSCR',
            61: 'DELETE_SUBSCR',
            62: 'BINARY_LSHIFT',
            63: 'BINARY_RSHIFT',
            64: 'BINARY_AND',
            65: 'BINARY_XOR',
            66: 'BINARY_OR',
            67: 'INPLACE_POWER',
            68: 'GET_ITER',
            69: 'GET_YIELD_FROM_ITER',
            70: 'PRINT_EXPR',
            71: 'LOAD_BUILD_CLASS',
            72: 'YIELD_FROM',
            73: 'GET_AWAITABLE',
            75: 'INPLACE_LSHIFT',
            76: 'INPLACE_RSHIFT',
            77: 'INPLACE_AND',
            78: 'INPLACE_XOR',
            79: 'INPLACE_OR',
            81: 'WITH_CLEANUP_START',
            82: 'WITH_CLEANUP_FINISH',
            83: 'RETURN_VALUE',
            84: 'IMPORT_STAR',
            85: 'SETUP_ANNOTATIONS',
            86: 'YIELD_VALUE',
            87: 'POP_BLOCK',
            88: 'END_FINALLY',
            89: 'POP_EXCEPT',
            90: 'STORE_NAME',
            91: 'DELETE_NAME',
            92: 'UNPACK_SEQUENCE',
            93: 'FOR_ITER',
            94: 'UNPACK_EX',
            95: 'STORE_ATTR',
            96: 'DELETE_ATTR',
            97: 'STORE_GLOBAL',
            98: 'DELETE_GLOBAL',
            100: 'LOAD_CONST',
            101: 'LOAD_NAME',
            102: 'BUILD_TUPLE',
            103: 'BUILD_LIST',
            104: 'BUILD_SET',
            105: 'BUILD_MAP',
            106: 'LOAD_ATTR',
            107: 'COMPARE_OP',
            108: 'IMPORT_NAME',
            109: 'IMPORT_FROM',
            110: 'JUMP_FORWARD',
            111: 'JUMP_IF_FALSE_OR_POP',
            112: 'JUMP_IF_TRUE_OR_POP',
            113: 'JUMP_ABSOLUTE',
            114: 'POP_JUMP_IF_FALSE',
            115: 'POP_JUMP_IF_TRUE',
            116: 'LOAD_GLOBAL',
            122: 'SETUP_FINALLY',
            124: 'LOAD_FAST',
            125: 'STORE_FAST',
            126: 'DELETE_FAST',
            130: 'RAISE_VARARGS',
            131: 'CALL_FUNCTION',
            132: 'MAKE_FUNCTION',
            133: 'BUILD_SLICE',
            134: 'LOAD_CLOSURE',
            135: 'LOAD_DEREF',
            136: 'STORE_DEREF',
            137: 'DELETE_DEREF',
            138: 'CALL_FUNCTION_KW',
            141: 'CALL_FUNCTION_EX',
            142: 'SETUP_WITH',
            143: 'EXTENDED_ARG',
            144: 'LIST_APPEND',
            145: 'SET_ADD',
            146: 'MAP_ADD',
            147: 'LOAD_CLASSDEREF',
            148: 'BUILD_LIST_UNPACK',
            149: 'BUILD_MAP_UNPACK',
            150: 'BUILD_MAP_UNPACK_WITH_CALL',
            151: 'BUILD_TUPLE_UNPACK',
            152: 'BUILD_SET_UNPACK',
            153: 'SETUP_ASYNC_WITH',
            154: 'FORMAT_VALUE',
            155: 'BUILD_CONST_KEY_MAP',
            156: 'BUILD_STRING',
            157: 'BUILD_TUPLE_UNPACK_WITH_CALL',
            160: 'LOAD_METHOD',
            161: 'CALL_METHOD'
        };

        return opcodeNames[opcode] || `UNKNOWN_OPCODE_${opcode}`;
    }

    /**
     * Resolve argument value
     * @param opcode Opcode
     * @param arg Argument
     * @param codeObject Code object
     * @returns Argument value
     */
    private resolveArgValue(opcode: number, arg: number, codeObject: CodeObject): any {
        switch (opcode) {
            case 90: // STORE_NAME
            case 91: // DELETE_NAME
            case 92: // LOAD_NAME
            case 101: // LOAD_ATTR
            case 106: // LOAD_GLOBAL
            case 124: // LOAD_METHOD
                return arg < codeObject.names.length ? codeObject.names[arg] : `<unknown name at ${arg}>`;
            case 100: // LOAD_CONST
                return arg < codeObject.constants.length ? codeObject.constants[arg] : `<unknown constant at ${arg}>`;
            default:
                return arg;
        }
    }

    /**
     * Extract imports from bytecode
     * @param codeObject Code object
     * @returns Array of import statements
     */
    public extractImports(codeObject: CodeObject): string[] {
        try {
            const pythonVersion = this.getPythonVersionObject('3.7.0'); // Default to Python 3.7
            if (!pythonVersion) {
                return [];
            }

            // Disassemble bytecode
            const instructions = this.disassemble(codeObject);

            // Extract imports from instructions
            const imports: string[] = [];

            for (let i = 0; i < instructions.length; i++) {
                const instruction = instructions[i];

                // Look for IMPORT_NAME instructions
                if (instruction.opcodeName === 'IMPORT_NAME' && instruction.argValue) {
                    const moduleName = instruction.argValue as string;

                    // Check if the previous instruction is LOAD_CONST (fromlist)
                    if (i > 0 && instructions[i - 1].opcodeName === 'LOAD_CONST') {
                        const fromlist = instructions[i - 1].argValue;

                        if (Array.isArray(fromlist) && fromlist.length > 0) {
                            // This is a 'from ... import ...' statement
                            for (const name of fromlist) {
                                imports.push(`from ${moduleName} import ${name}`);
                            }
                        } else {
                            // This is a regular import statement
                            imports.push(`import ${moduleName}`);
                        }
                    } else {
                        // Fallback: just record the module name
                        imports.push(`import ${moduleName}`);
                    }
                }
            }

            // Also check constants for potential imports
            for (const constant of codeObject.constants) {
                if (typeof constant === 'string' &&
                    constant.includes('.') &&
                    !constant.includes(' ') &&
                    !constant.includes('(') &&
                    !constant.includes('{')) {
                    // This might be a module name
                    if (!imports.includes(`import ${constant}`)) {
                        imports.push(`import ${constant}`);
                    }
                }
            }

            // Check nested code objects
            for (const nestedCodeObject of codeObject.nestedCodeObjects) {
                imports.push(...this.extractImports(nestedCodeObject));
            }

            return [...new Set(imports)]; // Remove duplicates
        } catch (error) {
            this.logger.error(`Error extracting imports:`, error);
            return [];
        }
    }

    /**
     * Extract classes from bytecode
     * @param codeObject Code object
     * @returns Array of class names
     */
    public extractClasses(codeObject: CodeObject): string[] {
        try {
            const pythonVersion = this.getPythonVersionObject('3.7.0'); // Default to Python 3.7
            if (!pythonVersion) {
                return [];
            }

            // Disassemble bytecode
            const instructions = this.disassemble(codeObject);

            // Extract classes from instructions
            const classes: string[] = [];

            for (let i = 0; i < instructions.length; i++) {
                const instruction = instructions[i];

                // Look for LOAD_BUILD_CLASS instructions
                if (instruction.opcodeName === 'LOAD_BUILD_CLASS') {
                    // The class name is typically stored in a STORE_NAME instruction after LOAD_BUILD_CLASS
                    for (let j = i + 1; j < instructions.length && j < i + 10; j++) {
                        if (instructions[j].opcodeName === 'STORE_NAME' && instructions[j].argValue) {
                            classes.push(instructions[j].argValue as string);
                            break;
                        }
                    }
                }
            }

            // Also check names for potential classes (capitalized names)
            for (const name of codeObject.names) {
                if (/^[A-Z][a-zA-Z0-9_]*$/.test(name) && !classes.includes(name)) {
                    classes.push(name);
                }
            }

            // Check nested code objects
            for (const nestedCodeObject of codeObject.nestedCodeObjects) {
                classes.push(...this.extractClasses(nestedCodeObject));
            }

            return [...new Set(classes)]; // Remove duplicates
        } catch (error) {
            this.logger.error(`Error extracting classes:`, error);
            return [];
        }
    }

    /**
     * Extract functions from bytecode
     * @param codeObject Code object
     * @returns Array of function names
     */
    public extractFunctions(codeObject: CodeObject): string[] {
        try {
            const pythonVersion = this.getPythonVersionObject('3.7.0'); // Default to Python 3.7
            if (!pythonVersion) {
                return [];
            }

            // Disassemble bytecode
            const instructions = this.disassemble(codeObject);

            // Extract functions from instructions
            const functions: string[] = [];

            for (let i = 0; i < instructions.length; i++) {
                const instruction = instructions[i];

                // Look for MAKE_FUNCTION instructions
                if (instruction.opcodeName === 'MAKE_FUNCTION') {
                    // The function name is typically stored in a STORE_NAME instruction after MAKE_FUNCTION
                    for (let j = i + 1; j < instructions.length && j < i + 5; j++) {
                        if ((instructions[j].opcodeName === 'STORE_NAME' ||
                             instructions[j].opcodeName === 'STORE_FAST') &&
                            instructions[j].argValue) {
                            functions.push(instructions[j].argValue as string);
                            break;
                        }
                    }
                }
            }

            // Also check nested code objects for function names
            for (const nestedCodeObject of codeObject.nestedCodeObjects) {
                if (nestedCodeObject.type === 'function' && nestedCodeObject.name) {
                    functions.push(nestedCodeObject.name);
                }

                // Recursively check nested code objects
                functions.push(...this.extractFunctions(nestedCodeObject));
            }

            return [...new Set(functions)]; // Remove duplicates
        } catch (error) {
            this.logger.error(`Error extracting functions:`, error);
            return [];
        }
    }

    /**
     * Get Python version from bytecode
     * @param buffer Bytecode buffer
     * @returns Python version string
     */
    public getPythonVersion(buffer: Buffer): string {
        try {
            if (buffer.length < 4) {
                return 'Unknown';
            }

            const magicNumber = buffer.readUInt32LE(0);

            // First check standard Python magic numbers
            const standardVersion = getPythonVersionFromMagic(magicNumber);
            if (standardVersion !== 'Unknown') {
                return standardVersion;
            }

            // Then check EA-specific magic numbers
            if (EA_MAGIC_NUMBERS[magicNumber]) {
                return EA_MAGIC_NUMBERS[magicNumber];
            }

            // If we can't determine the version from the magic number,
            // try to make an educated guess based on bytecode patterns

            // Check for Python 3.7 bytecode patterns (common in Sims 4)
            if (this.hasPython37BytecodePatterns(buffer)) {
                return 'Python 3.7 (Pattern Match)';
            }

            // Check for Python 3.8 bytecode patterns
            if (this.hasPython38BytecodePatterns(buffer)) {
                return 'Python 3.8 (Pattern Match)';
            }

            // Check for Python 3.9 bytecode patterns
            if (this.hasPython39BytecodePatterns(buffer)) {
                return 'Python 3.9 (Pattern Match)';
            }

            // If all else fails, return the magic number in hex
            return `Unknown (Magic: 0x${magicNumber.toString(16).toUpperCase().padStart(8, '0')})`;
        } catch (error) {
            this.logger.error('Error getting Python version:', error);
            return 'Unknown';
        }
    }

    /**
     * Check if buffer contains Python 3.7 bytecode patterns
     * @param buffer Bytecode buffer
     * @returns True if Python 3.7 patterns are detected
     */
    private hasPython37BytecodePatterns(buffer: Buffer): boolean {
        // Look for common Python 3.7 opcodes in sequence
        // LOAD_CONST, LOAD_NAME, CALL_FUNCTION pattern (common in module initialization)
        for (let i = 0; i < buffer.length - 6; i++) {
            // Python 3.7 opcodes: LOAD_CONST (100), LOAD_NAME (101), CALL_FUNCTION (131)
            if (buffer[i] === 100 && buffer[i + 2] === 101 && buffer[i + 4] === 131) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if buffer contains Python 3.8 bytecode patterns
     * @param buffer Bytecode buffer
     * @returns True if Python 3.8 patterns are detected
     */
    private hasPython38BytecodePatterns(buffer: Buffer): boolean {
        // Look for common Python 3.8 opcodes in sequence
        // Python 3.8 added LOAD_METHOD (160) and CALL_METHOD (161)
        for (let i = 0; i < buffer.length - 6; i++) {
            if (buffer[i] === 160 && buffer[i + 2] === 161) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if buffer contains Python 3.9 bytecode patterns
     * @param buffer Bytecode buffer
     * @returns True if Python 3.9 patterns are detected
     */
    private hasPython39BytecodePatterns(buffer: Buffer): boolean {
        // Look for common Python 3.9 opcodes in sequence
        // Python 3.9 added IS_OP (117) and CONTAINS_OP (118)
        for (let i = 0; i < buffer.length - 4; i++) {
            if (buffer[i] === 117 || buffer[i] === 118) {
                return true;
            }
        }

        return false;
    }
}
