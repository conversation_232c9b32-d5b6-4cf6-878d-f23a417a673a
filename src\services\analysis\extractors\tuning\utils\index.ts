/**
 * Utility functions for tuning extraction
 */

// Export buffer utilities
export {
    inspectBuffer,
    preprocessBuffer,
    isValidXmlBuffer,
    createBufferInspectionContext
} from './bufferUtils.js';

// Export XML utilities
export {
    cleanXmlString,
    getNodePath,
    createNodeContext,
    getNodeTextContent,
    getNodeAttribute,
    getChildElements
} from './xmlUtils.js';

// Export TGI utilities
export {
    createDependency,
    tryParseTgi,
    tryParseInstanceId,
    determineResourceTypeFromContext,
    determineReferenceTypeFromContext,
    determineReferenceTypeFromAttribute
} from './tgiUtils.js';
