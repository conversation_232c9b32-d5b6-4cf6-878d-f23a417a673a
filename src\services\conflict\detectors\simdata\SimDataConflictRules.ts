import { ConflictSeverity } from '../../../../types/conflict/ConflictTypes.js';
import { ParsedSimData, SimDataSchema, SimDataColumn, SimDataInstance } from '../../../analysis/extractors/simdata/simDataParser.js';

/**
 * Interface for a SimData conflict rule
 */
export interface SimDataConflictRule {
    /**
     * Name of the rule
     */
    name: string;
    
    /**
     * Description of the rule
     */
    description: string;
    
    /**
     * Default severity of conflicts detected by this rule
     */
    defaultSeverity: ConflictSeverity;
    
    /**
     * Check if the rule applies to the given SimData resources
     * @param simData1 First SimData resource
     * @param simData2 Second SimData resource
     * @returns True if the rule applies
     */
    applies(simData1: ParsedSimData, simData2: ParsedSimData): boolean;
    
    /**
     * Detect conflicts between the given SimData resources
     * @param simData1 First SimData resource
     * @param simData2 Second SimData resource
     * @returns Array of conflict descriptions, or empty array if no conflicts
     */
    detect(simData1: ParsedSimData, simData2: ParsedSimData): Array<{
        description: string;
        severity: ConflictSeverity;
        details?: any;
    }>;
}

/**
 * Rule for detecting schema name conflicts
 */
export const SchemaNameConflictRule: SimDataConflictRule = {
    name: 'SchemaNameConflict',
    description: 'Detects conflicts between SimData resources with the same schema name but different content',
    defaultSeverity: ConflictSeverity.HIGH,
    
    applies(simData1: ParsedSimData, simData2: ParsedSimData): boolean {
        return !!(simData1.schema && simData2.schema);
    },
    
    detect(simData1: ParsedSimData, simData2: ParsedSimData): Array<{
        description: string;
        severity: ConflictSeverity;
        details?: any;
    }> {
        const conflicts: Array<{
            description: string;
            severity: ConflictSeverity;
            details?: any;
        }> = [];
        
        if (!simData1.schema || !simData2.schema) {
            return conflicts;
        }
        
        // Check if schemas have the same name but different content
        if (simData1.schema.name === simData2.schema.name && 
            (simData1.schema.hash !== simData2.schema.hash || 
             simData1.schema.schemaId !== simData2.schema.schemaId)) {
            
            conflicts.push({
                description: `SimData schemas have same name (${simData1.schema.name}) but different content`,
                severity: ConflictSeverity.HIGH,
                details: {
                    schemaName: simData1.schema.name,
                    schema1Hash: simData1.schema.hash.toString(16),
                    schema2Hash: simData2.schema.hash.toString(16),
                    schema1Id: simData1.schema.schemaId,
                    schema2Id: simData2.schema.schemaId
                }
            });
        }
        
        return conflicts;
    }
};

/**
 * Rule for detecting schema column conflicts
 */
export const SchemaColumnConflictRule: SimDataConflictRule = {
    name: 'SchemaColumnConflict',
    description: 'Detects conflicts between SimData resources with incompatible schema columns',
    defaultSeverity: ConflictSeverity.MEDIUM,
    
    applies(simData1: ParsedSimData, simData2: ParsedSimData): boolean {
        return !!(simData1.schema && simData2.schema);
    },
    
    detect(simData1: ParsedSimData, simData2: ParsedSimData): Array<{
        description: string;
        severity: ConflictSeverity;
        details?: any;
    }> {
        const conflicts: Array<{
            description: string;
            severity: ConflictSeverity;
            details?: any;
        }> = [];
        
        if (!simData1.schema || !simData2.schema) {
            return conflicts;
        }
        
        // Create maps of columns by name for easy lookup
        const columns1 = new Map(simData1.schema.columns.map(col => [col.name, col]));
        const columns2 = new Map(simData2.schema.columns.map(col => [col.name, col]));
        
        // Check for columns with the same name but different types
        for (const [name, column1] of columns1.entries()) {
            const column2 = columns2.get(name);
            
            if (column2 && column1.type !== column2.type) {
                conflicts.push({
                    description: `Column "${name}" has different types: ${column1.type} vs ${column2.type}`,
                    severity: ConflictSeverity.HIGH,
                    details: {
                        columnName: name,
                        column1Type: column1.type,
                        column2Type: column2.type
                    }
                });
            }
        }
        
        // Check for missing columns
        const missingInSchema2 = Array.from(columns1.keys())
            .filter(name => !columns2.has(name));
            
        if (missingInSchema2.length > 0) {
            conflicts.push({
                description: `${missingInSchema2.length} columns missing in second schema: ${missingInSchema2.join(', ')}`,
                severity: ConflictSeverity.MEDIUM,
                details: {
                    missingColumns: missingInSchema2
                }
            });
        }
        
        const missingInSchema1 = Array.from(columns2.keys())
            .filter(name => !columns1.has(name));
            
        if (missingInSchema1.length > 0) {
            conflicts.push({
                description: `${missingInSchema1.length} columns missing in first schema: ${missingInSchema1.join(', ')}`,
                severity: ConflictSeverity.MEDIUM,
                details: {
                    missingColumns: missingInSchema1
                }
            });
        }
        
        return conflicts;
    }
};

/**
 * Rule for detecting instance conflicts
 */
export const InstanceConflictRule: SimDataConflictRule = {
    name: 'InstanceConflict',
    description: 'Detects conflicts between SimData resources with the same instance names but different IDs',
    defaultSeverity: ConflictSeverity.MEDIUM,
    
    applies(simData1: ParsedSimData, simData2: ParsedSimData): boolean {
        return simData1.instances.length > 0 && simData2.instances.length > 0;
    },
    
    detect(simData1: ParsedSimData, simData2: ParsedSimData): Array<{
        description: string;
        severity: ConflictSeverity;
        details?: any;
    }> {
        const conflicts: Array<{
            description: string;
            severity: ConflictSeverity;
            details?: any;
        }> = [];
        
        // Create maps of instances by name for easy lookup
        const instances1 = new Map(simData1.instances.map(instance => [instance.name, instance]));
        const instances2 = new Map(simData2.instances.map(instance => [instance.name, instance]));
        
        // Check for instances with the same name but different IDs
        for (const [name, instance1] of instances1.entries()) {
            const instance2 = instances2.get(name);
            
            if (instance2 && instance1.instanceId !== instance2.instanceId) {
                conflicts.push({
                    description: `Instance "${name}" has different IDs: ${instance1.instanceId} vs ${instance2.instanceId}`,
                    severity: ConflictSeverity.MEDIUM,
                    details: {
                        instanceName: name,
                        instance1Id: instance1.instanceId,
                        instance2Id: instance2.instanceId
                    }
                });
            }
        }
        
        // Check for missing instances
        const missingInResource2 = Array.from(instances1.keys())
            .filter(name => !instances2.has(name));
            
        if (missingInResource2.length > 0) {
            conflicts.push({
                description: `${missingInResource2.length} instances missing in second resource: ${missingInResource2.join(', ')}`,
                severity: ConflictSeverity.LOW,
                details: {
                    missingInstances: missingInResource2
                }
            });
        }
        
        const missingInResource1 = Array.from(instances2.keys())
            .filter(name => !instances1.has(name));
            
        if (missingInResource1.length > 0) {
            conflicts.push({
                description: `${missingInResource1.length} instances missing in first resource: ${missingInResource1.join(', ')}`,
                severity: ConflictSeverity.LOW,
                details: {
                    missingInstances: missingInResource1
                }
            });
        }
        
        return conflicts;
    }
};

/**
 * Rule for detecting critical value conflicts
 */
export const CriticalValueConflictRule: SimDataConflictRule = {
    name: 'CriticalValueConflict',
    description: 'Detects conflicts in critical gameplay values between SimData resources',
    defaultSeverity: ConflictSeverity.HIGH,
    
    applies(simData1: ParsedSimData, simData2: ParsedSimData): boolean {
        return simData1.instances.length > 0 && 
               simData2.instances.length > 0 && 
               !!simData1.schema && 
               !!simData2.schema;
    },
    
    detect(simData1: ParsedSimData, simData2: ParsedSimData): Array<{
        description: string;
        severity: ConflictSeverity;
        details?: any;
    }> {
        const conflicts: Array<{
            description: string;
            severity: ConflictSeverity;
            details?: any;
        }> = [];
        
        if (!simData1.schema || !simData2.schema) {
            return conflicts;
        }
        
        // Create maps of instances by name for easy lookup
        const instances1 = new Map(simData1.instances.map(instance => [instance.name, instance]));
        const instances2 = new Map(simData2.instances.map(instance => [instance.name, instance]));
        
        // Determine critical columns
        const criticalColumns = new Set<string>();
        
        for (const column of simData1.schema.columns) {
            const name = column.name.toLowerCase();
            const isCritical = [
                'multiplier', 'chance', 'probability', 'duration', 'cost', 'value',
                'weight', 'priority', 'threshold', 'score', 'buff', 'motive',
                'skill', 'trait', 'stat', 'tuning', 'level', 'gain', 'decay'
            ].some(pattern => name.includes(pattern));
            
            if (isCritical) {
                criticalColumns.add(column.name);
            }
        }
        
        // Check for value conflicts in common instances
        for (const [name, instance1] of instances1.entries()) {
            const instance2 = instances2.get(name);
            
            if (!instance2) continue;
            
            // Check each critical column in the instance
            for (const columnName of criticalColumns) {
                const value1 = instance1.values[columnName];
                const value2 = instance2.values[columnName];
                
                // Skip if either value is undefined
                if (value1 === undefined || value2 === undefined) continue;
                
                // Skip if values are the same
                if (this.areValuesEqual(value1, value2)) continue;
                
                conflicts.push({
                    description: `Critical value conflict in instance "${name}", column "${columnName}": ${value1} vs ${value2}`,
                    severity: ConflictSeverity.HIGH,
                    details: {
                        instanceName: name,
                        columnName,
                        value1,
                        value2
                    }
                });
            }
        }
        
        return conflicts;
    },
    
    /**
     * Check if two values are equal
     * @param value1 First value
     * @param value2 Second value
     * @returns True if values are equal
     */
    areValuesEqual(value1: any, value2: any): boolean {
        // Handle null/undefined
        if (value1 === null || value1 === undefined) {
            return value2 === null || value2 === undefined;
        }
        
        // Handle different types
        if (typeof value1 !== typeof value2) {
            return false;
        }
        
        // Handle objects
        if (typeof value1 === 'object') {
            return JSON.stringify(value1) === JSON.stringify(value2);
        }
        
        // Handle primitives
        return value1 === value2;
    }
};

/**
 * Rule for detecting version conflicts
 */
export const VersionConflictRule: SimDataConflictRule = {
    name: 'VersionConflict',
    description: 'Detects conflicts between SimData resources with different versions',
    defaultSeverity: ConflictSeverity.MEDIUM,
    
    applies(simData1: ParsedSimData, simData2: ParsedSimData): boolean {
        return simData1.version !== undefined && simData2.version !== undefined;
    },
    
    detect(simData1: ParsedSimData, simData2: ParsedSimData): Array<{
        description: string;
        severity: ConflictSeverity;
        details?: any;
    }> {
        const conflicts: Array<{
            description: string;
            severity: ConflictSeverity;
            details?: any;
        }> = [];
        
        if (simData1.version === undefined || simData2.version === undefined) {
            return conflicts;
        }
        
        // Check for version conflicts
        if (simData1.version !== simData2.version) {
            conflicts.push({
                description: `SimData version conflict: ${simData1.version} vs ${simData2.version}`,
                severity: ConflictSeverity.MEDIUM,
                details: {
                    version1: simData1.version,
                    version2: simData2.version
                }
            });
        }
        
        return conflicts;
    }
};

/**
 * All SimData conflict rules
 */
export const SimDataConflictRules: SimDataConflictRule[] = [
    SchemaNameConflictRule,
    SchemaColumnConflictRule,
    InstanceConflictRule,
    CriticalValueConflictRule,
    VersionConflictRule
];
