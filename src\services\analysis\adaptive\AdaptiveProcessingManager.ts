/**
 * Adaptive Processing Manager
 *
 * This class serves as a bridge between hardware detection and the streaming pipeline system.
 * It provides adaptive processing parameters based on hardware capabilities and system load.
 */
import { EventEmitter } from 'events';
import { Logger } from '../../../utils/logging/logger.js';
import { HardwareDetector, HardwareCategory, HardwareInfo, PerformanceRecommendations } from '../../../utils/performance/hardwareDetector.js';
import { AdaptiveThrottler, ThrottlingLevel } from '../../../utils/performance/adaptiveThrottler.js';
import EnhancedMemoryManager from '../../../utils/memory/enhancedMemoryManager.js';

// Create a logger for this module
const logger = new Logger('AdaptiveProcessingManager');

// Get instances of dependencies
const hardwareDetector = HardwareDetector.getInstance();
const throttler = AdaptiveThrottler.getInstance();
const memoryManager = EnhancedMemoryManager.getInstance();

/**
 * Workload type
 */
export enum WorkloadType {
    IO_BOUND = 'io_bound',
    CPU_BOUND = 'cpu_bound',
    MEMORY_BOUND = 'memory_bound',
    BALANCED = 'balanced'
}

/**
 * Processing parameters
 */
export interface ProcessingParameters {
    threadCount: number;
    batchSize: number;
    bufferSize: number;
    chunkSize: number;
    directBufferThreshold: number;
    chunkedProcessingThreshold: number;
    maxConcurrentResources: number;
    cacheSize: number;
    timeout: number;
}

/**
 * Adaptive processing options
 */
export interface AdaptiveProcessingOptions {
    enableDynamicThreading?: boolean;
    enableDynamicBatching?: boolean;
    enableDynamicBuffering?: boolean;
    minThreads?: number;
    maxThreads?: number;
    cpuUtilizationTarget?: number;
    memoryUtilizationTarget?: number;
    refreshInterval?: number;
    detailedMetrics?: boolean;
}

/**
 * Adaptive processing manager class
 */
export class AdaptiveProcessingManager extends EventEmitter {
    private static instance: AdaptiveProcessingManager;
    private options: AdaptiveProcessingOptions;
    private hardwareInfo?: HardwareInfo;
    private recommendations?: PerformanceRecommendations;
    private parameters: Record<WorkloadType, ProcessingParameters> = {} as any;
    private refreshTimer?: NodeJS.Timeout;
    private isInitialized: boolean = false;
    
    /**
     * Create a new adaptive processing manager
     * @param options Adaptive processing options
     */
    private constructor(options: AdaptiveProcessingOptions = {}) {
        super();
        
        this.options = {
            enableDynamicThreading: true,
            enableDynamicBatching: true,
            enableDynamicBuffering: true,
            minThreads: 1,
            maxThreads: 16,
            cpuUtilizationTarget: 0.7, // 70%
            memoryUtilizationTarget: 0.7, // 70%
            refreshInterval: 60000, // 1 minute
            detailedMetrics: false,
            ...options
        };
        
        logger.info('Adaptive processing manager initialized');
    }
    
    /**
     * Get the adaptive processing manager instance
     * @param options Adaptive processing options
     * @returns Adaptive processing manager instance
     */
    public static getInstance(options?: AdaptiveProcessingOptions): AdaptiveProcessingManager {
        if (!AdaptiveProcessingManager.instance) {
            AdaptiveProcessingManager.instance = new AdaptiveProcessingManager(options);
        } else if (options) {
            // Update options if provided
            AdaptiveProcessingManager.instance.options = {
                ...AdaptiveProcessingManager.instance.options,
                ...options
            };
        }
        
        return AdaptiveProcessingManager.instance;
    }
    
    /**
     * Initialize the adaptive processing manager
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }
        
        try {
            // Detect hardware
            this.hardwareInfo = await hardwareDetector.detectHardware();
            
            // Get recommendations
            this.recommendations = hardwareDetector.getRecommendations();
            
            // Generate processing parameters
            this.generateProcessingParameters();
            
            // Start refresh timer
            this.startRefreshTimer();
            
            // Listen for hardware changes
            hardwareDetector.on('hardwareDetected', (info) => {
                this.hardwareInfo = info;
                this.recommendations = hardwareDetector.getRecommendations();
                this.generateProcessingParameters();
                
                // Emit parameters updated event
                this.emit('parametersUpdated', this.parameters);
            });
            
            // Listen for throttling level changes
            throttler.on('throttlingLevelChanged', (data) => {
                this.adjustParametersForThrottling(data.newLevel);
                
                // Emit parameters updated event
                this.emit('parametersUpdated', this.parameters);
            });
            
            this.isInitialized = true;
            logger.info('Adaptive processing manager initialized successfully');
        } catch (error: any) {
            logger.error(`Error initializing adaptive processing manager: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * Get optimal thread count for a specific workload type
     * @param workloadType Workload type
     * @returns Optimal thread count
     */
    public getOptimalThreadCount(workloadType: WorkloadType = WorkloadType.BALANCED): number {
        if (!this.isInitialized) {
            throw new Error('Adaptive processing manager is not initialized. Call initialize() first.');
        }
        
        // Ensure parameters for this workload type exist
        this.ensureWorkloadTypeParameters(workloadType);
        
        return this.parameters[workloadType].threadCount;
    }
    
    /**
     * Get optimal batch size for a specific workload type
     * @param workloadType Workload type
     * @returns Optimal batch size
     */
    public getOptimalBatchSize(workloadType: WorkloadType = WorkloadType.BALANCED): number {
        if (!this.isInitialized) {
            throw new Error('Adaptive processing manager is not initialized. Call initialize() first.');
        }
        
        // Ensure parameters for this workload type exist
        this.ensureWorkloadTypeParameters(workloadType);
        
        return this.parameters[workloadType].batchSize;
    }
    
    /**
     * Get processing parameters for a specific workload type
     * @param workloadType Workload type
     * @returns Processing parameters
     */
    public getProcessingParameters(workloadType: WorkloadType = WorkloadType.BALANCED): ProcessingParameters {
        if (!this.isInitialized) {
            throw new Error('Adaptive processing manager is not initialized. Call initialize() first.');
        }
        
        // Ensure parameters for this workload type exist
        this.ensureWorkloadTypeParameters(workloadType);
        
        return { ...this.parameters[workloadType] };
    }
    
    /**
     * Get processing parameters adjusted for a specific resource type
     * @param resourceType Resource type
     * @param resourceSize Resource size
     * @returns Processing parameters
     */
    public getResourceSpecificParameters(resourceType: number, resourceSize: number): ProcessingParameters {
        if (!this.isInitialized) {
            throw new Error('Adaptive processing manager is not initialized. Call initialize() first.');
        }
        
        // Determine workload type based on resource type
        const workloadType = this.determineWorkloadType(resourceType);
        
        // Get base parameters for workload type
        const baseParams = this.getProcessingParameters(workloadType);
        
        // Adjust parameters based on resource size
        const adjustedParams = { ...baseParams };
        
        // Adjust buffer size based on resource size
        if (resourceSize > baseParams.bufferSize) {
            const memoryPressure = memoryManager.getMemoryPressure();
            
            if (memoryPressure < 0.5) {
                // Low memory pressure, we can use larger buffers
                adjustedParams.bufferSize = Math.min(resourceSize * 1.5, baseParams.bufferSize * 3);
            } else if (memoryPressure < 0.8) {
                // Medium memory pressure, use resource size as buffer size
                adjustedParams.bufferSize = resourceSize;
            } else {
                // High memory pressure, use smaller buffers
                adjustedParams.bufferSize = Math.min(resourceSize, baseParams.bufferSize);
            }
        }
        
        // Adjust thresholds based on resource type and size
        switch (resourceType) {
            // SimData type
            case 0x545AC67A:
                // SimData is typically small, use direct buffer approach more aggressively
                adjustedParams.directBufferThreshold = Math.max(baseParams.directBufferThreshold, 10 * 1024 * 1024); // 10MB
                break;
                
            // Tuning XML
            case 0x0166038C:
                // Tuning XML can sometimes be large, but is simple to process
                adjustedParams.directBufferThreshold = Math.max(baseParams.directBufferThreshold, 5 * 1024 * 1024); // 5MB
                break;
                
            // Image resources (DDS, etc.)
            case 0x00B2D882:
            case 0xB6C8B6A0:
                // Images can be large, use chunked processing more aggressively
                adjustedParams.directBufferThreshold = Math.min(baseParams.directBufferThreshold, 1 * 1024 * 1024); // 1MB
                break;
                
            // Model resources
            case 0x01D10F34:
            case 0x01661233:
                // Models can be very large, use streaming approach more aggressively
                adjustedParams.directBufferThreshold = Math.min(baseParams.directBufferThreshold, 512 * 1024); // 512KB
                adjustedParams.chunkedProcessingThreshold = Math.min(baseParams.chunkedProcessingThreshold, 5 * 1024 * 1024); // 5MB
                break;
                
            // Audio resources
            case 0x2026960B:
            case 0x0B5CACE4:
                // Audio resources can be moderately sized
                adjustedParams.directBufferThreshold = Math.min(baseParams.directBufferThreshold, 2 * 1024 * 1024); // 2MB
                break;
                
            default:
                // For unknown types, keep base parameters
                break;
        }
        
        return adjustedParams;
    }
    
    /**
     * Determine workload type based on resource type
     * @param resourceType Resource type
     * @returns Workload type
     * @private
     */
    private determineWorkloadType(resourceType: number): WorkloadType {
        // Map resource types to workload types
        switch (resourceType) {
            // SimData type
            case 0x545AC67A:
                return WorkloadType.CPU_BOUND;
                
            // Tuning XML
            case 0x0166038C:
                return WorkloadType.CPU_BOUND;
                
            // Image resources (DDS, etc.)
            case 0x00B2D882:
            case 0xB6C8B6A0:
                return WorkloadType.MEMORY_BOUND;
                
            // Model resources
            case 0x01D10F34:
            case 0x01661233:
                return WorkloadType.MEMORY_BOUND;
                
            // Audio resources
            case 0x2026960B:
            case 0x0B5CACE4:
                return WorkloadType.IO_BOUND;
                
            // String table
            case 0x220557DA:
                return WorkloadType.CPU_BOUND;
                
            // Python script
            case 0xEBCBB16C:
                return WorkloadType.CPU_BOUND;
                
            default:
                return WorkloadType.BALANCED;
        }
    }
    
    /**
     * Generate processing parameters based on hardware capabilities
     * @private
     */
    private generateProcessingParameters(): void {
        if (!this.hardwareInfo || !this.recommendations) {
            logger.warn('Hardware info or recommendations not available, using default parameters');
            this.parameters = this.getDefaultParameters();
            return;
        }
        
        // Generate parameters for each workload type
        const cpuCores = this.hardwareInfo.cpu.cores;
        
        // IO-bound workload
        this.parameters[WorkloadType.IO_BOUND] = {
            threadCount: Math.min(cpuCores * 2, 16), // 2x CPU cores for IO-bound workloads
            batchSize: this.recommendations.batchSize * 2, // IO can handle larger batches
            bufferSize: this.recommendations.bufferSize / 2, // IO needs less memory
            chunkSize: this.recommendations.chunkSize,
            directBufferThreshold: 1 * 1024 * 1024, // 1MB
            chunkedProcessingThreshold: 50 * 1024 * 1024, // 50MB
            maxConcurrentResources: Math.min(cpuCores * 2, 16),
            cacheSize: this.recommendations.cacheSize / 2,
            timeout: this.recommendations.timeout * 2 // IO operations might take longer
        };
        
        // CPU-bound workload
        this.parameters[WorkloadType.CPU_BOUND] = {
            threadCount: Math.max(cpuCores - 1, 1), // Use all but one CPU core
            batchSize: this.recommendations.batchSize,
            bufferSize: this.recommendations.bufferSize,
            chunkSize: this.recommendations.chunkSize,
            directBufferThreshold: 5 * 1024 * 1024, // 5MB
            chunkedProcessingThreshold: 50 * 1024 * 1024, // 50MB
            maxConcurrentResources: Math.max(cpuCores - 1, 1),
            cacheSize: this.recommendations.cacheSize,
            timeout: this.recommendations.timeout
        };
        
        // Memory-bound workload
        this.parameters[WorkloadType.MEMORY_BOUND] = {
            threadCount: Math.max(Math.floor(cpuCores / 2), 1), // Use half CPU cores
            batchSize: Math.floor(this.recommendations.batchSize / 2), // Smaller batches for memory-bound
            bufferSize: this.recommendations.bufferSize * 2, // More buffer space for memory-bound
            chunkSize: this.recommendations.chunkSize * 2, // Larger chunks for memory-bound
            directBufferThreshold: 1 * 1024 * 1024, // 1MB
            chunkedProcessingThreshold: 20 * 1024 * 1024, // 20MB
            maxConcurrentResources: Math.max(Math.floor(cpuCores / 2), 1),
            cacheSize: this.recommendations.cacheSize * 2,
            timeout: this.recommendations.timeout * 1.5 // Memory operations might take longer
        };
        
        // Balanced workload
        this.parameters[WorkloadType.BALANCED] = {
            threadCount: Math.max(Math.floor(cpuCores * 0.75), 1), // Use 75% CPU cores
            batchSize: this.recommendations.batchSize,
            bufferSize: this.recommendations.bufferSize,
            chunkSize: this.recommendations.chunkSize,
            directBufferThreshold: 2 * 1024 * 1024, // 2MB
            chunkedProcessingThreshold: 30 * 1024 * 1024, // 30MB
            maxConcurrentResources: Math.max(Math.floor(cpuCores * 0.75), 1),
            cacheSize: this.recommendations.cacheSize,
            timeout: this.recommendations.timeout
        };
        
        // Apply options constraints
        for (const type of Object.values(WorkloadType)) {
            const params = this.parameters[type];
            
            // Apply thread count constraints
            if (this.options.minThreads !== undefined) {
                params.threadCount = Math.max(params.threadCount, this.options.minThreads);
            }
            
            if (this.options.maxThreads !== undefined) {
                params.threadCount = Math.min(params.threadCount, this.options.maxThreads);
            }
        }
        
        logger.info(`Generated processing parameters for hardware category: ${this.hardwareInfo.category}`);
    }
    
    /**
     * Adjust parameters based on throttling level
     * @param throttlingLevel Throttling level
     * @private
     */
    private adjustParametersForThrottling(throttlingLevel: ThrottlingLevel): void {
        // Skip if no parameters available
        if (!this.parameters[WorkloadType.BALANCED]) {
            return;
        }
        
        // Apply adjustments based on throttling level
        const adjustmentFactor = this.getAdjustmentFactorForThrottling(throttlingLevel);
        
        for (const type of Object.values(WorkloadType)) {
            const params = this.parameters[type];
            
            // Adjust thread count
            if (this.options.enableDynamicThreading) {
                const newThreadCount = Math.max(
                    1,
                    Math.floor(params.threadCount * adjustmentFactor)
                );
                params.threadCount = newThreadCount;
                params.maxConcurrentResources = newThreadCount;
            }
            
            // Adjust batch size
            if (this.options.enableDynamicBatching) {
                params.batchSize = Math.max(
                    5,
                    Math.floor(params.batchSize * adjustmentFactor)
                );
            }
            
            // Adjust buffer size
            if (this.options.enableDynamicBuffering) {
                params.bufferSize = Math.max(
                    64 * 1024, // 64KB minimum
                    Math.floor(params.bufferSize * adjustmentFactor)
                );
            }
        }
        
        logger.info(`Adjusted processing parameters for throttling level: ${throttlingLevel}`);
    }
    
    /**
     * Get adjustment factor for throttling level
     * @param throttlingLevel Throttling level
     * @returns Adjustment factor
     * @private
     */
    private getAdjustmentFactorForThrottling(throttlingLevel: ThrottlingLevel): number {
        switch (throttlingLevel) {
            case 'none':
                return 1.0;
            case 'low':
                return 0.9;
            case 'medium':
                return 0.7;
            case 'high':
                return 0.5;
            case 'critical':
                return 0.3;
            default:
                return 1.0;
        }
    }
    
    /**
     * Ensure parameters exist for a workload type
     * @param workloadType Workload type
     * @private
     */
    private ensureWorkloadTypeParameters(workloadType: WorkloadType): void {
        if (!this.parameters[workloadType]) {
            // Generate default parameters for this workload type
            const defaultParams = this.getDefaultParameters();
            this.parameters[workloadType] = defaultParams[workloadType];
        }
    }
    
    /**
     * Get default processing parameters
     * @returns Default processing parameters
     * @private
     */
    private getDefaultParameters(): Record<WorkloadType, ProcessingParameters> {
        return {
            [WorkloadType.IO_BOUND]: {
                threadCount: 8,
                batchSize: 100,
                bufferSize: 2 * 1024 * 1024, // 2MB
                chunkSize: 64 * 1024, // 64KB
                directBufferThreshold: 1 * 1024 * 1024, // 1MB
                chunkedProcessingThreshold: 50 * 1024 * 1024, // 50MB
                maxConcurrentResources: 8,
                cacheSize: 50 * 1024 * 1024, // 50MB
                timeout: 60000 // 60 seconds
            },
            [WorkloadType.CPU_BOUND]: {
                threadCount: 4,
                batchSize: 50,
                bufferSize: 4 * 1024 * 1024, // 4MB
                chunkSize: 256 * 1024, // 256KB
                directBufferThreshold: 5 * 1024 * 1024, // 5MB
                chunkedProcessingThreshold: 50 * 1024 * 1024, // 50MB
                maxConcurrentResources: 4,
                cacheSize: 100 * 1024 * 1024, // 100MB
                timeout: 30000 // 30 seconds
            },
            [WorkloadType.MEMORY_BOUND]: {
                threadCount: 2,
                batchSize: 25,
                bufferSize: 8 * 1024 * 1024, // 8MB
                chunkSize: 512 * 1024, // 512KB
                directBufferThreshold: 1 * 1024 * 1024, // 1MB
                chunkedProcessingThreshold: 20 * 1024 * 1024, // 20MB
                maxConcurrentResources: 2,
                cacheSize: 200 * 1024 * 1024, // 200MB
                timeout: 45000 // 45 seconds
            },
            [WorkloadType.BALANCED]: {
                threadCount: 4,
                batchSize: 50,
                bufferSize: 4 * 1024 * 1024, // 4MB
                chunkSize: 256 * 1024, // 256KB
                directBufferThreshold: 2 * 1024 * 1024, // 2MB
                chunkedProcessingThreshold: 30 * 1024 * 1024, // 30MB
                maxConcurrentResources: 4,
                cacheSize: 100 * 1024 * 1024, // 100MB
                timeout: 30000 // 30 seconds
            }
        };
    }
    
    /**
     * Start refresh timer
     * @private
     */
    private startRefreshTimer(): void {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            this.refreshParameters().catch(error => {
                logger.error(`Error refreshing parameters: ${error.message}`);
            });
        }, this.options.refreshInterval);
        
        logger.debug(`Started parameter refresh timer with interval ${this.options.refreshInterval}ms`);
    }
    
    /**
     * Refresh hardware information and update parameters
     */
    public async refreshParameters(): Promise<void> {
        try {
            // Refresh hardware info
            this.hardwareInfo = await hardwareDetector.refreshHardwareInfo();
            
            // Get recommendations
            this.recommendations = hardwareDetector.getRecommendations();
            
            // Generate processing parameters
            this.generateProcessingParameters();
            
            // Emit parameters updated event
            this.emit('parametersUpdated', this.parameters);
            
            logger.info('Processing parameters refreshed');
        } catch (error: any) {
            logger.error(`Error refreshing processing parameters: ${error.message}`);
        }
    }
    
    /**
     * Dispose the adaptive processing manager
     */
    public dispose(): void {
        // Clear refresh timer
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = undefined;
        }
        
        // Remove event listeners
        this.removeAllListeners();
        
        logger.info('Adaptive processing manager disposed');
    }
}