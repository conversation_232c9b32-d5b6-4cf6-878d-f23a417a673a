import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';
import { ParsedContentInfo } from '../../types/database.js';

export class ParsedContentRepository {
    private db: Database.Database;
    private logger: Logger;

    constructor(db: Database.Database, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    saveParsedContent(parsedContentInfo: ParsedContentInfo): number {
        try {
            const updateStmt = this.db.prepare(`
                UPDATE ParsedContent SET contentType = ?, content = ?
                WHERE resourceId = ?
            `);

            const contentString = typeof parsedContentInfo.content === 'string'
                ? parsedContentInfo.content
                : JSON.stringify(parsedContentInfo.content);

            const updateResult = updateStmt.run(
                parsedContentInfo.contentType,
                contentString,
                parsedContentInfo.resourceId
            );

            if (updateResult.changes > 0) {
                 const selectStmt = this.db.prepare(`
                     SELECT id FROM ParsedContent WHERE resourceId = ?
                 `);
                 const row = selectStmt.get(parsedContentInfo.resourceId) as { id: number } | undefined;
                 if (!row) {
                     throw new Error(`[ParsedContentRepository] Failed to retrieve ID after successful ParsedContent update for resource ${parsedContentInfo.resourceId}.`);
                 }
                 return row.id;
            } else {
                const insertStmt = this.db.prepare(`
                    INSERT INTO ParsedContent (resourceId, contentType, content, timestamp)
                    VALUES (?, ?, ?, ?)
                    RETURNING id;
                `);
                const insertResult = insertStmt.get(
                    parsedContentInfo.resourceId,
                    parsedContentInfo.contentType,
                    contentString,
                    Date.now()
                ) as { id: number };
                return insertResult.id;
            }
        } catch (error) {
            this.logger.error(`[ParsedContentRepository] Error saving/updating parsed content for resource ${parsedContentInfo.resourceId}:`, error);
            throw error;
        }
    }

    /**
     * Get parsed content by resource ID and content type
     * @param resourceId The ID of the resource
     * @param contentType Optional content type filter
     * @returns The parsed content or undefined if not found
     */
    getParsedContentByResourceId(resourceId: number, contentType?: string): { id: number; resourceId: number; contentType: string; content: string } | undefined {
        try {
            let query = `
                SELECT id, resourceId, contentType, content FROM ParsedContent
                WHERE resourceId = ?
            `;

            const params: any[] = [resourceId];

            if (contentType) {
                query += ` AND contentType = ?`;
                params.push(contentType);
            }

            const stmt = this.db.prepare(query);
            const result = stmt.get(...params) as { id: number; resourceId: number; contentType: string; content: string } | undefined;

            return result;
        } catch (error) {
            this.logger.error(`[ParsedContentRepository] Error getting parsed content for resource ${resourceId}:`, error);
            return undefined;
        }
    }
}