﻿﻿﻿// Corrected imports
import { ResourceKey } from '../../types/resource/interfaces.js';
import { convertS4TKResourceKey } from '../../utils/resource/helpers.js'; // Moved helper
import { Package } from '@s4tk/models';
import { EventEmitter } from 'events';

export interface MetadataCacheEvent {
  type: 'update' | 'clear' | 'error';
  data?: any;
}

export interface CacheErrorInfo {
  error: Error;
  timestamp: number;
  context?: string;
}

export class MetadataCache extends EventEmitter {
  private static instance: MetadataCache;
  private cache: Map<string, ResourceKey[]> = new Map();
  private readonly CACHE_TTL = 1000 * 60 * 60; // 1 hour

  private constructor() {
    super();
  }

  public static getInstance(): MetadataCache {
    if (!MetadataCache.instance) {
      MetadataCache.instance = new MetadataCache();
    }
    return MetadataCache.instance;
  }

  public async getResources(filePath: string): Promise<ResourceKey[]> {
    const cached = this.cache.get(filePath);
    if (cached) {
      return cached;
    }

    try {
      // Removed unused Package.fromAsync call
      const resources = await Package.streamResourcesAsync(filePath); // Assuming this returns { key: S4TKResourceKey, ... }[]

      const resourceKeys: ResourceKey[] = resources.map(resource => {
        const key = convertS4TKResourceKey(resource.key);
        // Add timestamp separately if needed, or other properties from 'resource'
        // For now, just converting the key and adding timestamp
        return {
          ...key,
          // name: resource.name, // Example: if streamResourcesAsync provides name
          // path: filePath, // Example: associate with the package path
          timestamp: Date.now()
        };
      });

      this.cache.set(filePath, resourceKeys);
      this.emit('update', { type: 'update', data: { filePath, resourceCount: resourceKeys.length } });
      
      return resourceKeys;
    } catch (error) {
      const errorInfo: CacheErrorInfo = {
        error: error as Error,
        timestamp: Date.now(),
        context: `Failed to get resources for ${filePath}`
      };
      this.emit('error', { type: 'error', data: errorInfo });
      throw error;
    }
  }

  /**
   * Synchronously retrieves cached resource keys for a given file path.
   * Returns undefined if the path is not in the cache.
   * Note: This does not check TTL; it assumes cached data is valid if present.
   * Use getResources for async fetching and TTL checks.
   */
  public getCachedResourcesSync(filePath: string): ResourceKey[] | undefined {
    return this.cache.get(filePath);
  }

  public clearCache(): void {
    this.cache.clear();
    this.emit('clear', { type: 'clear' });
  }
}

export const metadataCache = MetadataCache.getInstance();
