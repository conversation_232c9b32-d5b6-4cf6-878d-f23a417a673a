/**
 * SimData Buffer Reader
 * 
 * Domain-specific buffer reader for SimData parsing that uses the unified BufferReader
 * with SimData-specific error handling and versioning support
 */

import { <PERSON><PERSON>er<PERSON>eader, BufferReadOptions } from '../../../../../utils/buffer/bufferReader.js';
import { 
    SimDataErrorContext, 
    createBufferErrorContext, 
    handleSimDataError 
} from '../error/simDataParserErrorHandler.js';
import { Logger } from '../../../../../utils/logging/logger.js';

const logger = new Logger('SimDataBufferReader');

/**
 * SimData-specific buffer reader that wraps the unified BufferReader
 * with error handling and versioning support
 */
export class SimDataBufferReader {
    private reader: BufferReader;
    private version?: number;

    /**
     * Creates a new SimDataBufferReader
     * @param buffer Buffer to read from
     * @param initialPosition Initial position (default: 0)
     * @param version SimData version for error context
     * @param options Reading options
     */
    constructor(buffer: Buffer, initialPosition: number = 0, version?: number, options: BufferReadOptions = {}) {
        this.reader = new BufferReader(buffer, initialPosition, {
            throwOnBufferOverflow: false, // SimData parsing should handle errors gracefully
            ...options
        });
        this.version = version;
    }

    /**
     * Creates an error context for the current position
     * @param operation Operation being performed
     * @returns Error context
     */
    private createErrorContext(operation: string): SimDataErrorContext {
        return {
            version: this.version,
            offset: this.reader.getPosition(),
            operation,
            bufferLength: this.reader.getLength(),
            additionalInfo: {
                remainingBytes: this.reader.remainingBytes()
            }
        };
    }

    /**
     * Safe read with SimData error handling
     * @param readFn Function to perform the read
     * @param operation Operation name for error context
     * @returns The read value or undefined if failed
     */
    private safeReadWithErrorHandling<T>(readFn: () => T | null, operation: string): T | undefined {
        try {
            const result = readFn();
            return result === null ? undefined : result;
        } catch (error) {
            return handleSimDataError(
                error,
                this.createErrorContext(operation),
                undefined
            );
        }
    }

    // Delegate position and navigation methods
    public getPosition(): number {
        return this.reader.getPosition();
    }

    public setPosition(position: number): SimDataBufferReader {
        this.reader.setPosition(position);
        return this;
    }

    public skip(bytes: number): SimDataBufferReader {
        this.reader.skip(bytes);
        return this;
    }

    public hasBytes(n: number): boolean {
        return this.reader.hasBytes(n);
    }

    public remainingBytes(): number {
        return this.reader.remainingBytes();
    }

    public isEOF(): boolean {
        return this.reader.isEOF();
    }

    public getBuffer(): Buffer {
        return this.reader.getBuffer();
    }

    public getLength(): number {
        return this.reader.getLength();
    }

    /**
     * Reads a UInt8 from the buffer with SimData error handling
     * @returns The UInt8 value or undefined if reading fails
     */
    public readUInt8(): number | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readUInt8('UInt8'), 'readUInt8');
    }

    /**
     * Reads a UInt16LE from the buffer with SimData error handling
     * @returns The UInt16LE value or undefined if reading fails
     */
    public readUInt16LE(): number | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readUInt16LE('UInt16LE'), 'readUInt16LE');
    }

    /**
     * Reads a UInt32LE from the buffer with SimData error handling
     * @returns The UInt32LE value or undefined if reading fails
     */
    public readUInt32LE(): number | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readUInt32LE('UInt32LE'), 'readUInt32LE');
    }

    /**
     * Reads a Int8 from the buffer with SimData error handling
     * @returns The Int8 value or undefined if reading fails
     */
    public readInt8(): number | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readInt8('Int8'), 'readInt8');
    }

    /**
     * Reads a Int16LE from the buffer with SimData error handling
     * @returns The Int16LE value or undefined if reading fails
     */
    public readInt16LE(): number | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readInt16LE('Int16LE'), 'readInt16LE');
    }

    /**
     * Reads a Int32LE from the buffer with SimData error handling
     * @returns The Int32LE value or undefined if reading fails
     */
    public readInt32LE(): number | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readInt32LE('Int32LE'), 'readInt32LE');
    }

    /**
     * Reads a FloatLE from the buffer with SimData error handling
     * @returns The FloatLE value or undefined if reading fails
     */
    public readFloatLE(): number | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readFloatLE('FloatLE'), 'readFloatLE');
    }

    /**
     * Reads a BigUInt64LE from the buffer with SimData error handling
     * @returns The BigUInt64LE value as a string or undefined if reading fails
     */
    public readBigUInt64LE(): string | undefined {
        return this.safeReadWithErrorHandling(() => {
            const result = this.reader.readBigUInt64LE('BigUInt64LE');
            if (result === null) return null;
            // Convert to hex format if it's a decimal string
            const numericValue = BigInt(result);
            return numericValue.toString(16);
        }, 'readBigUInt64LE');
    }

    /**
     * Reads a string from the buffer with SimData error handling
     * @param length Length of the string to read
     * @returns The string or undefined if reading fails
     */
    public readString(length: number): string | undefined {
        return this.safeReadWithErrorHandling(() => this.reader.readString(length, 'utf8', 'string'), 'readString');
    }

    /**
     * Reads a length-prefixed string with SimData error handling
     * @param lengthSize Size of the length prefix (1, 2, or 4 bytes)
     * @returns The string or undefined if reading fails
     */
    public readLengthPrefixedString(lengthSize: 1 | 2 | 4 = 2): string | undefined {
        try {
            // Read the length prefix
            let length: number | undefined;
            
            switch (lengthSize) {
                case 1:
                    length = this.readUInt8();
                    break;
                case 2:
                    length = this.readUInt16LE();
                    break;
                case 4:
                    length = this.readUInt32LE();
                    break;
                default:
                    throw new Error(`Invalid length size: ${lengthSize}`);
            }
            
            if (length === undefined) {
                return undefined;
            }
            
            // Validate length
            if (length === 0) {
                return '';
            }
            
            if (length > 1000) {
                logger.warn(`Unusually long string length: ${length}`);
                return undefined;
            }
            
            // Read the string
            return this.readString(length);
        } catch (error) {
            return handleSimDataError(
                error,
                this.createErrorContext('readLengthPrefixedString'),
                undefined
            );
        }
    }

    /**
     * Reads SimData schema hash
     * @returns Schema hash as UInt32LE or undefined if failed
     */
    public readSchemaHash(): number | undefined {
        return this.readUInt32LE();
    }

    /**
     * Reads SimData schema name with length prefix
     * @returns Schema name string or undefined if failed
     */
    public readSchemaName(): string | undefined {
        return this.readLengthPrefixedString(4);
    }

    /**
     * Reads SimData column definition
     * @returns Object with column name and type or undefined if failed
     */
    public readColumnDefinition(): { name: string; type: number } | undefined {
        const name = this.readLengthPrefixedString(4);
        const type = this.readUInt32LE();
        
        if (name === undefined || type === undefined) {
            return undefined;
        }
        
        return { name, type };
    }
}

// Backward compatibility exports
export { SimDataBufferReader as BufferReader };

/**
 * Create a SimData BufferReader instance (factory function)
 * @param buffer Buffer to read from
 * @param initialPosition Initial position
 * @param version SimData version
 * @returns SimDataBufferReader instance
 */
export function createSimDataBufferReader(
    buffer: Buffer, 
    initialPosition: number = 0, 
    version?: number
): SimDataBufferReader {
    return new SimDataBufferReader(buffer, initialPosition, version);
}
