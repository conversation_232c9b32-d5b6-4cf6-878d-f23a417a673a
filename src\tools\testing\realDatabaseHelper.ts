/**
 * Real Database Helper for Testing
 *
 * This module provides utilities for creating real in-memory database instances
 * for testing instead of using mock database services. This ensures tests use
 * the actual database functionality and catch real-world issues.
 */

import { DatabaseService } from '../../services/databaseService.js';
import { Logger } from '../../utils/logging/logger.js';

/**
 * Configuration options for creating test database instances
 */
export interface TestDatabaseConfig {
    /** Custom logger to use (optional) */
    logger?: Logger;
    /** Whether to initialize the database immediately (default: true) */
    autoInitialize?: boolean;
    /** Custom database identifier for logging (optional) */
    testId?: string;
}

/**
 * Create a real in-memory database instance for testing
 *
 * This function creates a real DatabaseService instance using SQLite's :memory:
 * database, which provides all the functionality of a real database but stores
 * data in memory for fast, isolated testing.
 *
 * @param config Configuration options
 * @returns Promise<DatabaseService> Initialized database service
 */
export async function createTestDatabase(config: TestDatabaseConfig = {}): Promise<DatabaseService> {
    const {
        logger = new Logger(`TestDatabase-${config.testId || Date.now()}`),
        autoInitialize = true,
        testId
    } = config;

    // Create database service with in-memory database
    const databaseService = new DatabaseService(':memory:', logger);

    if (autoInitialize) {
        await databaseService.initialize();
        logger.debug(`Test database ${testId || 'instance'} initialized successfully`);
    }

    return databaseService;
}

/**
 * Create multiple test database instances for parallel testing
 *
 * @param count Number of database instances to create
 * @param config Base configuration for all instances
 * @returns Promise<DatabaseService[]> Array of initialized database services
 */
export async function createTestDatabases(
    count: number,
    config: TestDatabaseConfig = {}
): Promise<DatabaseService[]> {
    const databases: DatabaseService[] = [];

    for (let i = 0; i < count; i++) {
        const instanceConfig = {
            ...config,
            testId: `${config.testId || 'test'}-${i}`,
            logger: new Logger(`TestDatabase-${config.testId || 'test'}-${i}`)
        };

        const db = await createTestDatabase(instanceConfig);
        databases.push(db);
    }

    return databases;
}

/**
 * Clean up test database instances
 *
 * @param databases Database instances to clean up
 */
export async function cleanupTestDatabases(databases: DatabaseService[]): Promise<void> {
    for (const db of databases) {
        try {
            db.close();
        } catch (error) {
            // Log but don't throw - cleanup should be best effort
            console.warn('Error cleaning up test database:', error);
        }
    }
}

/**
 * Create a test database for real data testing
 *
 * Note: This function creates an empty database that should be populated
 * with real mod data, not synthetic test data. Use the mod analysis system
 * to populate it with actual Sims 4 mod content.
 *
 * @param config Configuration options
 * @returns Promise<DatabaseService> Empty database ready for real data
 */
export async function createTestDatabaseForRealData(config: TestDatabaseConfig = {}): Promise<DatabaseService> {
    const db = await createTestDatabase(config);

    // Database is created empty and ready to be populated with real mod data
    // through the mod analysis system, not synthetic test data

    return db;
}

/**
 * Utility class for managing test database lifecycle
 */
export class TestDatabaseManager {
    private databases: DatabaseService[] = [];
    private logger: Logger;

    constructor(testName: string = 'TestDatabaseManager') {
        this.logger = new Logger(testName);
    }

    /**
     * Create a new test database instance
     */
    async createDatabase(config: TestDatabaseConfig = {}): Promise<DatabaseService> {
        const db = await createTestDatabase({
            ...config,
            testId: `${config.testId || 'managed'}-${this.databases.length}`
        });

        this.databases.push(db);
        return db;
    }

    /**
     * Create a test database ready for real data
     */
    async createDatabaseForRealData(config: TestDatabaseConfig = {}): Promise<DatabaseService> {
        const db = await createTestDatabaseForRealData({
            ...config,
            testId: `${config.testId || 'real-data'}-${this.databases.length}`
        });

        this.databases.push(db);
        return db;
    }

    /**
     * Clean up all managed databases
     */
    async cleanup(): Promise<void> {
        this.logger.debug(`Cleaning up ${this.databases.length} test databases`);
        await cleanupTestDatabases(this.databases);
        this.databases = [];
    }

    /**
     * Get the number of managed databases
     */
    get count(): number {
        return this.databases.length;
    }
}
