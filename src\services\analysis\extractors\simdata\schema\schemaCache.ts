/**
 * Schema Cache
 *
 * This module provides a caching system for SimData schemas to improve performance
 * by reducing database access and repeated parsing.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataSchema } from '../simDataTypes.js';
import { SchemaAnalysisResult } from './schemaInterfaces.js';

const log = {
    info: (message: string) => console.log(`[SchemaCache] INFO: ${message}`),
    error: (message: string) => console.error(`[SchemaCache] ERROR: ${message}`),
    warn: (message: string) => console.warn(`[SchemaCache] WARN: ${message}`),
    debug: (message: string) => console.debug(`[SchemaCache] DEBUG: ${message}`)
};

/**
 * Cache entry with expiration
 */
interface CacheEntry<T> {
    value: T;
    expires: number;
}

/**
 * Schema Cache
 * Provides caching for SimData schemas and analysis results
 */
export class SchemaCache {
    private static instance: SchemaCache;
    private schemaCache: Map<string, CacheEntry<SimDataSchema>> = new Map();
    private analysisCache: Map<string, CacheEntry<SchemaAnalysisResult>> = new Map();
    private defaultTTL: number = 3600000; // 1 hour in milliseconds
    private maxCacheSize: number = 1000;
    private cacheHits: number = 0;
    private cacheMisses: number = 0;

    private constructor() {
        // Start cache cleanup interval
        setInterval(() => this.cleanupExpiredEntries(), 300000); // Clean up every 5 minutes
    }

    /**
     * Get the singleton instance of the cache
     */
    public static getInstance(): SchemaCache {
        if (!SchemaCache.instance) {
            SchemaCache.instance = new SchemaCache();
        }
        return SchemaCache.instance;
    }

    /**
     * Set the default TTL for cache entries
     * @param ttl Time to live in milliseconds
     */
    public setDefaultTTL(ttl: number): void {
        this.defaultTTL = ttl;
    }

    /**
     * Set the maximum cache size
     * @param size Maximum number of entries
     */
    public setMaxCacheSize(size: number): void {
        this.maxCacheSize = size;
    }

    /**
     * Get a schema from the cache
     * @param key Cache key (usually schema name + ID + hash)
     * @returns The cached schema or undefined if not found
     */
    public getSchema(key: string): SimDataSchema | undefined {
        const entry = this.schemaCache.get(key);
        if (entry && entry.expires > Date.now()) {
            this.cacheHits++;
            return entry.value;
        }
        this.cacheMisses++;
        return undefined;
    }

    /**
     * Set a schema in the cache
     * @param key Cache key
     * @param schema The schema to cache
     * @param ttl Optional TTL in milliseconds
     */
    public setSchema(key: string, schema: SimDataSchema, ttl?: number): void {
        // Check if we need to evict entries
        if (this.schemaCache.size >= this.maxCacheSize) {
            this.evictOldestEntries(this.schemaCache);
        }

        const expires = Date.now() + (ttl || this.defaultTTL);
        this.schemaCache.set(key, { value: schema, expires });
    }

    /**
     * Get an analysis result from the cache
     * @param key Cache key
     * @returns The cached analysis result or undefined if not found
     */
    public getAnalysis(key: string): SchemaAnalysisResult | undefined {
        const entry = this.analysisCache.get(key);
        if (entry && entry.expires > Date.now()) {
            this.cacheHits++;
            return entry.value;
        }
        this.cacheMisses++;
        return undefined;
    }

    /**
     * Set an analysis result in the cache
     * @param key Cache key
     * @param analysis The analysis result to cache
     * @param ttl Optional TTL in milliseconds
     */
    public setAnalysis(key: string, analysis: SchemaAnalysisResult, ttl?: number): void {
        // Check if we need to evict entries
        if (this.analysisCache.size >= this.maxCacheSize) {
            this.evictOldestEntries(this.analysisCache);
        }

        const expires = Date.now() + (ttl || this.defaultTTL);
        this.analysisCache.set(key, { value: analysis, expires });
    }

    /**
     * Clear the entire cache
     */
    public clear(): void {
        this.schemaCache.clear();
        this.analysisCache.clear();
        log.info('Cache cleared');
    }

    /**
     * Get cache statistics
     */
    public getStats(): {
        schemaEntries: number;
        analysisEntries: number;
        hits: number;
        misses: number;
        hitRate: number;
    } {
        const totalRequests = this.cacheHits + this.cacheMisses;
        const hitRate = totalRequests > 0 ? (this.cacheHits / totalRequests) * 100 : 0;

        return {
            schemaEntries: this.schemaCache.size,
            analysisEntries: this.analysisCache.size,
            hits: this.cacheHits,
            misses: this.cacheMisses,
            hitRate
        };
    }

    /**
     * Clean up expired entries
     */
    private cleanupExpiredEntries(): void {
        const now = Date.now();
        let expiredCount = 0;

        // Clean up schema cache
        for (const [key, entry] of this.schemaCache.entries()) {
            if (entry.expires <= now) {
                this.schemaCache.delete(key);
                expiredCount++;
            }
        }

        // Clean up analysis cache
        for (const [key, entry] of this.analysisCache.entries()) {
            if (entry.expires <= now) {
                this.analysisCache.delete(key);
                expiredCount++;
            }
        }

        if (expiredCount > 0) {
            log.debug(`Cleaned up ${expiredCount} expired cache entries`);
        }
    }

    /**
     * Evict oldest entries when cache is full
     */
    private evictOldestEntries<T>(cache: Map<string, CacheEntry<T>>): void {
        // Sort entries by expiration time
        const entries = Array.from(cache.entries());
        entries.sort((a, b) => a[1].expires - b[1].expires);

        // Remove 10% of the oldest entries
        const entriesToRemove = Math.max(1, Math.floor(this.maxCacheSize * 0.1));
        for (let i = 0; i < entriesToRemove && i < entries.length; i++) {
            cache.delete(entries[i][0]);
        }

        log.debug(`Evicted ${entriesToRemove} oldest cache entries`);
    }
}
