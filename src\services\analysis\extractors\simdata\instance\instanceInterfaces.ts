/**
 * Instance interfaces for SimData instance analysis
 */

/**
 * Interface for instance value pattern
 */
export interface InstanceValuePattern {
    uniqueValues: any[];
    commonValues: any[];
    valueDistribution: Record<string, number>;
    hasNullValues: boolean;
    valueRange?: { min: number; max: number };
}

/**
 * Interface for instance relationship
 */
export interface InstanceRelationship {
    sourceInstanceId: number;
    sourceName: string;
    targetInstanceId: number;
    targetName: string;
    relationshipType: string;
    confidence: number;
    via: string;
}

/**
 * Interface for instance analysis result
 */
export interface InstanceAnalysisResult {
    count: number;
    valuePatterns: Record<string, InstanceValuePattern>;
    relationships: InstanceRelationship[];
    keyInstances: number[];
    categories: Record<string, number[]>;
}

/**
 * Interface for instance semantic information
 */
export interface InstanceSemanticInfo {
    instanceId: number;
    name: string;
    purpose?: string;
    category?: string;
    isKey?: boolean;
    relationships?: InstanceRelationship[];
    criticalValues?: Record<string, any>;
    gameplayImpact?: string;
}

/**
 * Interface for instance repository entry
 */
export interface InstanceRepositoryEntry {
    instance: any;
    analysis: InstanceSemanticInfo;
    occurrences: number;
    modIds: number[];
}
