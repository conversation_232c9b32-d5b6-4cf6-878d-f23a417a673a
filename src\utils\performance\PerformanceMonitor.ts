﻿import { logger } from '../logging/logger.js';

export interface PerformanceMetrics {
  operationType: string;
  startTime: number;
  endTime: number;
  duration: number;
  startMemory: number;
  endMemory: number;
  memoryUsage: number;
  success: boolean;
  identifier?: string;
  error?: Error;
  additionalMetrics?: Record<string, any>;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];

  private constructor() {}

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  public async recordMetrics(
    operationType: string,
    startTime: number,
    startMemory: number,
    success: boolean,
    identifier?: string,
    error?: Error,
    additionalMetrics?: Record<string, any>
  ): Promise<void> {
    try {
      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;

      const metrics: PerformanceMetrics = {
        operationType,
        startTime,
        endTime,
        duration: endTime - startTime,
        startMemory,
        endMemory,
        memoryUsage: endMemory - startMemory,
        success,
        identifier,
        error,
        additionalMetrics,
      };

      this.metrics.push(metrics);
      await this.logMetrics(metrics);
    } catch (error) {
      logger.error('Error recording performance metrics:', error);
      throw error;
    }
  }

  private async logMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      const logMessage = {
        timestamp: new Date().toISOString(),
        operation: metrics.operationType,
        duration: `${metrics.duration.toFixed(2)}ms`,
        memoryUsage: `${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
        success: metrics.success,
        ...(metrics.identifier && { identifier: metrics.identifier }),
        ...(metrics.error && { error: metrics.error.message }),
        ...(metrics.additionalMetrics && { additionalMetrics: metrics.additionalMetrics }),
      };

      if (metrics.success) {
        logger.info('Performance metrics:', logMessage);
      } else {
        logger.warn('Performance metrics (failed operation):', logMessage);
      }
    } catch (error) {
      logger.error('Error logging performance metrics:', error);
      throw error;
    }
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getMetricsByType(operationType: string): PerformanceMetrics[] {
    return this.metrics.filter(metric => metric.operationType === operationType);
  }

  public getAverageMetrics(operationType: string): {
    averageDuration: number;
    averageMemoryUsage: number;
    successRate: number;
  } {
    const typeMetrics = this.getMetricsByType(operationType);
    if (typeMetrics.length === 0) {
      return {
        averageDuration: 0,
        averageMemoryUsage: 0,
        successRate: 0,
      };
    }

    const totalDuration = typeMetrics.reduce((acc, curr) => acc + curr.duration, 0);
    const totalMemory = typeMetrics.reduce((acc, curr) => acc + curr.memoryUsage, 0);
    const successCount = typeMetrics.filter(metric => metric.success).length;

    return {
      averageDuration: totalDuration / typeMetrics.length,
      averageMemoryUsage: totalMemory / typeMetrics.length,
      successRate: (successCount / typeMetrics.length) * 100,
    };
  }

  public clearMetrics(): void {
    this.metrics = [];
  }
} 

