/**
 * Semantic Conflict Detector
 *
 * This detector identifies conflicts based on semantic understanding of resources,
 * including their purpose, gameplay systems, and relationships.
 *
 * Enhanced with deeper semantic analysis and dependency graph integration.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ConflictDetectorBase } from './ConflictDetectorBase.js';
import { ConflictType, ConflictSeverity, Conflict, ConflictInfo } from '../../../types/conflict/index.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { ResourceInfo } from '../../../types/database.js';
import { ContextAwareAnalyzer, ResourceRelationshipType } from '../../analysis/semantic/contextAwareAnalyzer.js';
import { GameplaySystemRegistry } from '../../analysis/semantic/gameplaySystemRegistry.js';
import { ResourcePurposeAnalyzer } from '../../analysis/semantic/resourcePurposeAnalyzer.js';
import { GameplayImpact, ConflictRisk } from '../../analysis/semantic/types.js';
import { DependencyGraphBuilder } from '../../analysis/semantic/dependencyGraph/dependencyGraphBuilder.js';
import { DependencyGraphAnalyzer } from '../../analysis/semantic/dependencyGraph/dependencyGraphAnalyzer.js';
import { injectable, singleton } from '../../di/decorators.js';

/**
 * Semantic conflict detector with enhanced capabilities
 */
@singleton()
export class SemanticConflictDetector extends ConflictDetectorBase {
    private contextAwareAnalyzer: ContextAwareAnalyzer;
    private gameplaySystemRegistry: GameplaySystemRegistry;
    private resourcePurposeAnalyzer: ResourcePurposeAnalyzer;
    private dependencyGraphAnalyzer: DependencyGraphAnalyzer;

    /**
     * Constructor
     * @param databaseService Database service
     * @param contextAwareAnalyzer Context-aware analyzer
     * @param gameplaySystemRegistry Gameplay system registry
     * @param resourcePurposeAnalyzer Resource purpose analyzer
     * @param dependencyGraphAnalyzer Dependency graph analyzer (optional)
     * @param logger Logger instance
     */
    constructor(
        databaseService: DatabaseService,
        contextAwareAnalyzer: ContextAwareAnalyzer,
        gameplaySystemRegistry: GameplaySystemRegistry,
        resourcePurposeAnalyzer: ResourcePurposeAnalyzer,
        dependencyGraphAnalyzer?: DependencyGraphAnalyzer,
        logger?: Logger
    ) {
        super(databaseService, logger || new Logger('SemanticConflictDetector'));
        this.contextAwareAnalyzer = contextAwareAnalyzer;
        this.gameplaySystemRegistry = gameplaySystemRegistry;
        this.resourcePurposeAnalyzer = resourcePurposeAnalyzer;
        this.dependencyGraphAnalyzer = dependencyGraphAnalyzer || new DependencyGraphAnalyzer(databaseService);
    }

    /**
     * Initialize the detector
     */
    public async initialize(): Promise<void> {
        try {
            // Initialize required services if they're not already initialized
            if (this.contextAwareAnalyzer && typeof this.contextAwareAnalyzer.initialize === 'function') {
                await this.contextAwareAnalyzer.initialize();
            }

            if (this.gameplaySystemRegistry && typeof this.gameplaySystemRegistry.initialize === 'function') {
                await this.gameplaySystemRegistry.initialize();
            }

            if (this.resourcePurposeAnalyzer && typeof this.resourcePurposeAnalyzer.initialize === 'function') {
                await this.resourcePurposeAnalyzer.initialize();
            }

            // Initialize dependency graph analyzer
            if (this.dependencyGraphAnalyzer) {
                // Build the dependency graph for all resources
                await this.dependencyGraphAnalyzer.buildGraph({
                    includeWeakDependencies: true,
                    includeResourceMetadata: true
                });
                this.logger.info('Dependency graph built successfully');
            }

            this.logger.info('Semantic conflict detector initialized');
        } catch (error) {
            this.logger.error('Error initializing semantic conflict detector:', error);
            throw error;
        }
    }

    /**
     * Detect conflicts between resources
     * @param resourceId1 First resource ID
     * @param resourceId2 Second resource ID
     * @returns Array of conflicts
     */
    public async detectConflicts(resourceId1: any, resourceId2: any): Promise<Conflict[]> {
        try {
            const conflicts: Conflict[] = [];

            // Check if resourceId1 and resourceId2 are numbers
            if (typeof resourceId1 !== 'number' || typeof resourceId2 !== 'number') {
                this.logger.error(`Invalid resource IDs: ${resourceId1}, ${resourceId2}`);
                return conflicts;
            }

            // Get resource information
            const resource1 = await this.getResourceInfo(resourceId1);
            const resource2 = await this.getResourceInfo(resourceId2);

            if (!resource1 || !resource2) {
                return conflicts;
            }

            // Skip if resources are from the same package
            if (resource1.packageId === resource2.packageId) {
                return conflicts;
            }

            // Enhanced semantic conflict detection
            // Analyze gameplay impact for both resources
            const impact1 = await this.analyzeGameplayImpact(resource1);
            const impact2 = await this.analyzeGameplayImpact(resource2);

            // Detect functional overlap
            const overlapResult = await this.detectFunctionalOverlap(resource1, resource2);

            // Assess compatibility
            const compatibilityResult = await this.assessCompatibility(resource1, resource2, impact1, impact2, overlapResult);

            // If resources are incompatible, create a conflict
            if (compatibilityResult.isCompatible === false) {
                // Get resource keys
                const resourceKey1: ResourceKey = {
                    type: resource1.type,
                    group: BigInt(resource1.group),
                    instance: BigInt(resource1.instance)
                };

                const resourceKey2: ResourceKey = {
                    type: resource2.type,
                    group: BigInt(resource2.group),
                    instance: BigInt(resource2.instance)
                };

                conflicts.push({
                    id: this.generateConflictId(resourceId1, resourceId2, ConflictType.SEMANTIC),
                    type: ConflictType.SEMANTIC,
                    severity: compatibilityResult.severity,
                    resourceId1,
                    resourceId2,
                    resourceKey1,
                    resourceKey2,
                    packageId1: resource1.packageId,
                    packageId2: resource2.packageId,
                    description: compatibilityResult.reason,
                    details: {
                        impact1,
                        impact2,
                        overlapResult,
                        compatibilityResult
                    },
                    timestamp: Date.now(),
                    recommendations: compatibilityResult.recommendations || []
                });
            } else {
                // Fall back to traditional conflict detection if no semantic conflicts found
                // 1. Check for gameplay system conflicts
                const systemConflicts = await this.detectGameplaySystemConflicts(resourceId1, resourceId2, resource1, resource2);
                conflicts.push(...systemConflicts);

                // 2. Check for purpose conflicts
                const purposeConflicts = await this.detectPurposeConflicts(resourceId1, resourceId2, resource1, resource2);
                conflicts.push(...purposeConflicts);

                // 3. Check for relationship conflicts
                const relationshipConflicts = await this.detectRelationshipConflicts(resourceId1, resourceId2, resource1, resource2);
                conflicts.push(...relationshipConflicts);
            }

            return conflicts;
        } catch (error) {
            this.logger.error(`Error detecting semantic conflicts between resources ${resourceId1} and ${resourceId2}:`, error);
            return [];
        }
    }

    /**
     * Detect gameplay system conflicts
     * @param resourceId1 First resource ID
     * @param resourceId2 Second resource ID
     * @param resource1 First resource information
     * @param resource2 Second resource information
     * @returns Array of conflicts
     */
    private async detectGameplaySystemConflicts(
        resourceId1: number,
        resourceId2: number,
        resource1: any,
        resource2: any
    ): Promise<Conflict[]> {
        try {
            const conflicts: Conflict[] = [];

            // Get gameplay systems for both resources
            const systems1 = await this.getGameplaySystems(resourceId1);
            const systems2 = await this.getGameplaySystems(resourceId2);

            if (!systems1 || !systems2) {
                return conflicts;
            }

            // Check for overlapping systems
            const overlappingSystems = systems1.filter(system => systems2.includes(system));

            if (overlappingSystems.length > 0) {
                // Get resource keys
                const resourceKey1: ResourceKey = {
                    type: resource1.type,
                    group: BigInt(resource1.group),
                    instance: BigInt(resource1.instance)
                };

                const resourceKey2: ResourceKey = {
                    type: resource2.type,
                    group: BigInt(resource2.group),
                    instance: BigInt(resource2.instance)
                };

                // Create conflict
                conflicts.push({
                    id: this.generateConflictId(resourceId1, resourceId2, ConflictType.SEMANTIC_GAMEPLAY_SYSTEM),
                    type: ConflictType.SEMANTIC_GAMEPLAY_SYSTEM,
                    severity: this.calculateSystemConflictSeverity(overlappingSystems),
                    resourceId1,
                    resourceId2,
                    resourceKey1,
                    resourceKey2,
                    packageId1: resource1.packageId,
                    packageId2: resource2.packageId,
                    description: `Resources modify the same gameplay systems: ${overlappingSystems.join(', ')}`,
                    details: {
                        systems1,
                        systems2,
                        overlappingSystems
                    },
                    timestamp: Date.now()
                });
            }

            return conflicts;
        } catch (error) {
            this.logger.error(`Error detecting gameplay system conflicts between resources ${resourceId1} and ${resourceId2}:`, error);
            return [];
        }
    }

    /**
     * Detect purpose conflicts
     * @param resourceId1 First resource ID
     * @param resourceId2 Second resource ID
     * @param resource1 First resource information
     * @param resource2 Second resource information
     * @returns Array of conflicts
     */
    private async detectPurposeConflicts(
        resourceId1: number,
        resourceId2: number,
        resource1: any,
        resource2: any
    ): Promise<Conflict[]> {
        try {
            const conflicts: Conflict[] = [];

            // Get purposes for both resources
            const purpose1 = await this.getResourcePurpose(resourceId1);
            const purpose2 = await this.getResourcePurpose(resourceId2);

            if (!purpose1 || !purpose2) {
                return conflicts;
            }

            // Check if purposes are the same
            if (purpose1.primaryPurpose === purpose2.primaryPurpose) {
                // Get resource keys
                const resourceKey1: ResourceKey = {
                    type: resource1.type,
                    group: BigInt(resource1.group),
                    instance: BigInt(resource1.instance)
                };

                const resourceKey2: ResourceKey = {
                    type: resource2.type,
                    group: BigInt(resource2.group),
                    instance: BigInt(resource2.instance)
                };

                // Create conflict
                conflicts.push({
                    id: this.generateConflictId(resourceId1, resourceId2, ConflictType.SEMANTIC_PURPOSE),
                    type: ConflictType.SEMANTIC_PURPOSE,
                    severity: this.calculatePurposeConflictSeverity(purpose1, purpose2),
                    resourceId1,
                    resourceId2,
                    resourceKey1,
                    resourceKey2,
                    packageId1: resource1.packageId,
                    packageId2: resource2.packageId,
                    description: `Resources have the same primary purpose: ${purpose1.primaryPurpose}`,
                    details: {
                        purpose1,
                        purpose2
                    },
                    timestamp: Date.now()
                });
            }

            return conflicts;
        } catch (error) {
            this.logger.error(`Error detecting purpose conflicts between resources ${resourceId1} and ${resourceId2}:`, error);
            return [];
        }
    }

    /**
     * Detect relationship conflicts
     * @param resourceId1 First resource ID
     * @param resourceId2 Second resource ID
     * @param resource1 First resource information
     * @param resource2 Second resource information
     * @returns Array of conflicts
     */
    private async detectRelationshipConflicts(
        resourceId1: number,
        resourceId2: number,
        resource1: any,
        resource2: any
    ): Promise<Conflict[]> {
        try {
            const conflicts: Conflict[] = [];

            // Get relationships for both resources
            const relationships1 = await this.getResourceRelationships(resourceId1);
            const relationships2 = await this.getResourceRelationships(resourceId2);

            if (!relationships1 || !relationships2) {
                return conflicts;
            }

            // Check for conflicting relationships
            const conflictingRelationships = this.findConflictingRelationships(relationships1, relationships2);

            if (conflictingRelationships.length > 0) {
                // Get resource keys
                const resourceKey1: ResourceKey = {
                    type: resource1.type,
                    group: BigInt(resource1.group),
                    instance: BigInt(resource1.instance)
                };

                const resourceKey2: ResourceKey = {
                    type: resource2.type,
                    group: BigInt(resource2.group),
                    instance: BigInt(resource2.instance)
                };

                // Create conflict
                conflicts.push({
                    id: this.generateConflictId(resourceId1, resourceId2, ConflictType.SEMANTIC_RELATIONSHIP),
                    type: ConflictType.SEMANTIC_RELATIONSHIP,
                    severity: this.calculateRelationshipConflictSeverity(conflictingRelationships),
                    resourceId1,
                    resourceId2,
                    resourceKey1,
                    resourceKey2,
                    packageId1: resource1.packageId,
                    packageId2: resource2.packageId,
                    description: `Resources have conflicting relationships with other resources`,
                    details: {
                        relationships1,
                        relationships2,
                        conflictingRelationships
                    },
                    timestamp: Date.now()
                });
            }

            return conflicts;
        } catch (error) {
            this.logger.error(`Error detecting relationship conflicts between resources ${resourceId1} and ${resourceId2}:`, error);
            return [];
        }
    }

    /**
     * Get resource information
     * @param resourceId Resource ID
     * @returns Resource information
     */
    private async getResourceInfo(resourceId: number): Promise<any> {
        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM Resources WHERE id = ?
            `, [resourceId]);

            if (!result || result.length === 0) {
                return null;
            }

            return result[0];
        } catch (error) {
            this.logger.error(`Error getting resource info for resource ${resourceId}:`, error);
            return null;
        }
    }

    /**
     * Get gameplay systems for a resource
     * @param resourceId Resource ID
     * @returns Array of gameplay system IDs
     */
    private async getGameplaySystems(resourceId: number): Promise<string[]> {
        try {
            // Check if the GameplaySystems table exists
            const tableExists = await this.databaseService.executeQuery(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='GameplaySystems'
            `);

            if (!tableExists || tableExists.length === 0) {
                // Table doesn't exist, return empty array
                return [];
            }

            // Check if the systems column exists
            const columnsInfo = await this.databaseService.executeQuery(`
                PRAGMA table_info(GameplaySystems)
            `);

            // Make sure columnsInfo is an array
            const columnsArray = Array.isArray(columnsInfo) ? columnsInfo : [];
            const hasSystemsColumn = columnsArray.some((col: any) => col.name === 'systems');

            if (!hasSystemsColumn) {
                // Column doesn't exist, return empty array
                return [];
            }

            // Now we can safely query the table
            const result = await this.databaseService.executeQuery(`
                SELECT systems FROM GameplaySystems WHERE resourceId = ?
            `, [resourceId]);

            if (!result || result.length === 0) {
                return [];
            }

            return JSON.parse(result[0].systems);
        } catch (error) {
            this.logger.error(`Error getting gameplay systems for resource ${resourceId}:`, error);
            return [];
        }
    }

    /**
     * Get resource purpose
     * @param resourceId Resource ID
     * @returns Resource purpose
     */
    private async getResourcePurpose(resourceId: number): Promise<any> {
        try {
            // Check if the ResourcePurposes table exists
            const tableExists = await this.databaseService.executeQuery(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='ResourcePurposes'
            `);

            if (!tableExists || tableExists.length === 0) {
                // Table doesn't exist, return null
                return null;
            }

            // Check if the purpose column exists
            const columnsInfo = await this.databaseService.executeQuery(`
                PRAGMA table_info(ResourcePurposes)
            `);

            // Make sure columnsInfo is an array
            const columnsArray = Array.isArray(columnsInfo) ? columnsInfo : [];
            const hasPurposeColumn = columnsArray.some((col: any) => col.name === 'purpose');

            if (!hasPurposeColumn) {
                // Column doesn't exist, return null
                return null;
            }

            // Now we can safely query the table
            const result = await this.databaseService.executeQuery(`
                SELECT purpose FROM ResourcePurposes WHERE resourceId = ?
            `, [resourceId]);

            if (!result || result.length === 0) {
                return null;
            }

            return JSON.parse(result[0].purpose);
        } catch (error) {
            this.logger.error(`Error getting resource purpose for resource ${resourceId}:`, error);
            return null;
        }
    }

    /**
     * Get resource relationships
     * @param resourceId Resource ID
     * @returns Array of resource relationships
     */
    private async getResourceRelationships(resourceId: number): Promise<any[]> {
        try {
            // Check if the ResourceRelationships table exists
            const tableExists = await this.databaseService.executeQuery(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='ResourceRelationships'
            `);

            if (!tableExists || tableExists.length === 0) {
                // Table doesn't exist, return empty array
                return [];
            }

            // Check if the required columns exist
            const columnsInfo = await this.databaseService.executeQuery(`
                PRAGMA table_info(ResourceRelationships)
            `);

            // Make sure columnsInfo is an array
            const columnsArray = Array.isArray(columnsInfo) ? columnsInfo : [];
            const hasSourceColumn = columnsArray.some((col: any) => col.name === 'sourceResourceId');
            const hasTargetColumn = columnsArray.some((col: any) => col.name === 'targetResourceId');

            if (!hasSourceColumn || !hasTargetColumn) {
                // Required columns don't exist, return empty array
                return [];
            }

            // Now we can safely query the table
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM ResourceRelationships
                WHERE sourceResourceId = ? OR targetResourceId = ?
            `, [resourceId, resourceId]);

            if (!result || result.length === 0) {
                return [];
            }

            return result;
        } catch (error) {
            this.logger.error(`Error getting resource relationships for resource ${resourceId}:`, error);
            return [];
        }
    }

    /**
     * Find conflicting relationships
     * @param relationships1 First resource relationships
     * @param relationships2 Second resource relationships
     * @returns Array of conflicting relationships
     */
    private findConflictingRelationships(relationships1: any[], relationships2: any[]): any[] {
        const conflictingRelationships: any[] = [];

        // Check for resources that both resources have relationships with
        const relatedResources1 = new Set<number>();
        const relatedResources2 = new Set<number>();

        // Collect related resources
        for (const relationship of relationships1) {
            if (relationship.sourceResourceId !== relationship.targetResourceId) {
                relatedResources1.add(relationship.targetResourceId);
            }
        }

        for (const relationship of relationships2) {
            if (relationship.sourceResourceId !== relationship.targetResourceId) {
                relatedResources2.add(relationship.targetResourceId);
            }
        }

        // Find common related resources
        const commonResources = [...relatedResources1].filter(id => relatedResources2.has(id));

        // Check for conflicting relationship types
        for (const commonResource of commonResources) {
            const relationshipsToCommon1 = relationships1.filter(r =>
                r.targetResourceId === commonResource || r.sourceResourceId === commonResource
            );

            const relationshipsToCommon2 = relationships2.filter(r =>
                r.targetResourceId === commonResource || r.sourceResourceId === commonResource
            );

            // Check for conflicting relationship types
            for (const rel1 of relationshipsToCommon1) {
                for (const rel2 of relationshipsToCommon2) {
                    if (this.areRelationshipsConflicting(rel1, rel2)) {
                        conflictingRelationships.push({
                            resource1: rel1.sourceResourceId,
                            resource2: rel2.sourceResourceId,
                            commonResource,
                            relationship1: rel1,
                            relationship2: rel2
                        });
                    }
                }
            }
        }

        return conflictingRelationships;
    }

    /**
     * Check if two relationships are conflicting
     * @param relationship1 First relationship
     * @param relationship2 Second relationship
     * @returns Whether the relationships are conflicting
     */
    private areRelationshipsConflicting(relationship1: any, relationship2: any): boolean {
        // Define conflicting relationship types
        const conflictingTypes: [ResourceRelationshipType, ResourceRelationshipType][] = [
            [ResourceRelationshipType.OVERRIDES, ResourceRelationshipType.OVERRIDES],
            [ResourceRelationshipType.EXTENDS, ResourceRelationshipType.OVERRIDES],
            [ResourceRelationshipType.DEPENDS_ON, ResourceRelationshipType.CONFLICTS_WITH]
        ];

        // Check if the relationship types are in the conflicting types list
        return conflictingTypes.some(([type1, type2]) =>
            (relationship1.relationshipType === type1 && relationship2.relationshipType === type2) ||
            (relationship1.relationshipType === type2 && relationship2.relationshipType === type1)
        );
    }

    /**
     * Calculate system conflict severity
     * @param overlappingSystems Overlapping gameplay systems
     * @returns Conflict severity
     */
    private calculateSystemConflictSeverity(overlappingSystems: string[]): ConflictSeverity {
        // More overlapping systems = higher severity
        if (overlappingSystems.length >= 3) {
            return ConflictSeverity.HIGH;
        } else if (overlappingSystems.length >= 2) {
            return ConflictSeverity.MEDIUM;
        } else {
            return ConflictSeverity.LOW;
        }
    }

    /**
     * Calculate purpose conflict severity
     * @param purpose1 First resource purpose
     * @param purpose2 Second resource purpose
     * @returns Conflict severity
     */
    private calculatePurposeConflictSeverity(purpose1: any, purpose2: any): ConflictSeverity {
        // Higher impact and risk = higher severity
        if (
            purpose1.overallGameplayImpact === GameplayImpact.HIGH ||
            purpose1.overallConflictRisk === ConflictRisk.HIGH ||
            purpose2.overallGameplayImpact === GameplayImpact.HIGH ||
            purpose2.overallConflictRisk === ConflictRisk.HIGH
        ) {
            return ConflictSeverity.HIGH;
        } else if (
            purpose1.overallGameplayImpact === GameplayImpact.MEDIUM ||
            purpose1.overallConflictRisk === ConflictRisk.MEDIUM ||
            purpose2.overallGameplayImpact === GameplayImpact.MEDIUM ||
            purpose2.overallConflictRisk === ConflictRisk.MEDIUM
        ) {
            return ConflictSeverity.MEDIUM;
        } else {
            return ConflictSeverity.LOW;
        }
    }

    /**
     * Calculate relationship conflict severity
     * @param conflictingRelationships Conflicting relationships
     * @returns Conflict severity
     */
    private calculateRelationshipConflictSeverity(conflictingRelationships: any[]): ConflictSeverity {
        // More conflicting relationships = higher severity
        if (conflictingRelationships.length >= 3) {
            return ConflictSeverity.HIGH;
        } else if (conflictingRelationships.length >= 2) {
            return ConflictSeverity.MEDIUM;
        } else {
            return ConflictSeverity.LOW;
        }
    }

    /**
     * Generate conflict ID
     * @param resourceId1 First resource ID
     * @param resourceId2 Second resource ID
     * @param type Conflict type
     * @returns Conflict ID
     */
    private generateConflictId(resourceId1: number, resourceId2: number, type: ConflictType): string {
        return `${type}_${Math.min(resourceId1, resourceId2)}_${Math.max(resourceId1, resourceId2)}`;
    }

    /**
     * Detect semantic conflicts between multiple resources
     * @param resources Array of resources to check for conflicts
     * @returns Promise resolving to array of conflicts
     */
    public async detectSemanticConflicts(resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        try {
            this.logger.info(`Detecting semantic conflicts between ${resources.length} resources`);
            const conflicts: ConflictInfo[] = [];

            // Process resources in batches to avoid memory issues
            const batchSize = 50;
            let resourcesProcessed = 0;

            // Group resources by type for more efficient processing
            const resourcesByType = new Map<number, ResourceInfo[]>();
            for (const resource of resources) {
                if (!resourcesByType.has(resource.key.type)) {
                    resourcesByType.set(resource.key.type, []);
                }
                resourcesByType.get(resource.key.type)!.push(resource);
            }

            // Process each resource type group
            for (const [type, typeResources] of resourcesByType.entries()) {
                this.logger.debug(`Processing ${typeResources.length} resources of type 0x${type.toString(16).toUpperCase()}`);

                // Compare resources within the same type group
                for (let i = 0; i < typeResources.length; i++) {
                    const resource1 = typeResources[i];

                    for (let j = i + 1; j < typeResources.length; j++) {
                        const resource2 = typeResources[j];

                        // Skip if resources are from the same package
                        if (resource1.packageId === resource2.packageId) {
                            continue;
                        }

                        // Analyze gameplay impact for both resources
                        const impact1 = await this.analyzeGameplayImpact(resource1);
                        const impact2 = await this.analyzeGameplayImpact(resource2);

                        // Detect functional overlap
                        const overlapResult = await this.detectFunctionalOverlap(resource1, resource2);

                        // Assess compatibility
                        const compatibilityResult = await this.assessCompatibility(resource1, resource2, impact1, impact2, overlapResult);

                        // If resources are incompatible, create a conflict
                        if (compatibilityResult.isCompatible === false) {
                            conflicts.push({
                                id: `semantic_${resource1.id}_${resource2.id}`,
                                type: ConflictType.SEMANTIC,
                                severity: compatibilityResult.severity,
                                description: compatibilityResult.reason,
                                affectedResources: [resource1.key, resource2.key],
                                timestamp: Date.now(),
                                recommendations: compatibilityResult.recommendations,
                                details: {
                                    impact1,
                                    impact2,
                                    overlapResult,
                                    compatibilityResult
                                }
                            });
                        }

                        resourcesProcessed++;
                        if (resourcesProcessed % batchSize === 0) {
                            this.logger.debug(`Processed ${resourcesProcessed} resource pairs`);
                            // Force garbage collection if available
                            if (global.gc) {
                                global.gc();
                            }
                        }
                    }
                }
            }

            this.logger.info(`Detected ${conflicts.length} semantic conflicts`);
            return conflicts;
        } catch (error) {
            this.logger.error('Error detecting semantic conflicts:', error);
            return [];
        }
    }

    /**
     * Analyze the gameplay impact of a resource
     * @param resource Resource to analyze
     * @returns Promise resolving to gameplay impact analysis
     */
    private async analyzeGameplayImpact(resource: ResourceInfo): Promise<GameplayImpact> {
        try {
            // Get resource purpose
            const purposeAnalysis = await this.resourcePurposeAnalyzer.analyzeResourcePurpose(
                resource.key,
                resource.id,
                resource.metadata || {},
                resource.contentSnippet
            );

            // Get gameplay system categorization
            const systemCategorization = await this.gameplaySystemRegistry.categorizeResource(
                resource.key,
                resource.metadata || {},
                resource.contentSnippet
            );

            // Get resource context
            const resourceContext = await this.contextAwareAnalyzer.buildResourceContext(
                resource.id,
                resource.key,
                resource.metadata || {},
                resource.contentSnippet,
                resource.packageId
            );

            // Determine impact level based on purpose and system
            let impactLevel: 'HIGH' | 'MEDIUM' | 'LOW' = 'LOW';

            // Critical gameplay systems have high impact
            const criticalSystems = ['simulation', 'cas', 'build_buy', 'ui', 'autonomy', 'interactions'];
            if (systemCategorization.primarySystem && criticalSystems.includes(systemCategorization.primarySystem)) {
                impactLevel = 'HIGH';
            } else if (purposeAnalysis.confidence > 70) {
                // High confidence purpose analysis can determine impact
                if (purposeAnalysis.purposeType === 'CORE_GAMEPLAY' || purposeAnalysis.purposeType === 'OVERRIDES_CORE') {
                    impactLevel = 'HIGH';
                } else if (purposeAnalysis.purposeType === 'EXTENDS_GAMEPLAY' || purposeAnalysis.purposeType === 'ADDS_CONTENT') {
                    impactLevel = 'MEDIUM';
                }
            }

            // Check for dependencies
            let dependencyCount = 0;
            let hasCriticalDependencies = false;

            if (this.dependencyGraphAnalyzer && this.dependencyGraphAnalyzer.isGraphBuilt()) {
                const dependencies = this.dependencyGraphAnalyzer.getDependencies(resource.id.toString());
                dependencyCount = dependencies.size;

                // Check if resource has dependencies on critical resources
                for (const depId of dependencies) {
                    const depResource = await this.getResourceInfo(parseInt(depId));
                    if (depResource) {
                        const depSystem = await this.gameplaySystemRegistry.categorizeResource(
                            {
                                type: depResource.type,
                                group: BigInt(depResource.group),
                                instance: BigInt(depResource.instance)
                            },
                            depResource.metadata || {},
                            depResource.contentSnippet
                        );

                        if (depSystem.primarySystem && criticalSystems.includes(depSystem.primarySystem)) {
                            hasCriticalDependencies = true;
                            break;
                        }
                    }
                }
            }

            // Resources with many dependencies or critical dependencies have higher impact
            if (hasCriticalDependencies || dependencyCount > 10) {
                impactLevel = 'HIGH';
            } else if (dependencyCount > 5 && impactLevel === 'LOW') {
                impactLevel = 'MEDIUM';
            }

            return {
                resourceId: resource.id,
                impactLevel,
                affectedSystems: systemCategorization.secondarySystems || [],
                primarySystem: systemCategorization.primarySystem || 'unknown',
                purposeType: purposeAnalysis.purposeType || 'UNKNOWN',
                dependencyCount,
                hasCriticalDependencies,
                confidence: Math.max(purposeAnalysis.confidence, systemCategorization.primaryConfidence) / 100
            };
        } catch (error) {
            this.logger.error(`Error analyzing gameplay impact for resource ${resource.id}:`, error);
            return {
                resourceId: resource.id,
                impactLevel: 'LOW',
                affectedSystems: [],
                primarySystem: 'unknown',
                purposeType: 'UNKNOWN',
                dependencyCount: 0,
                hasCriticalDependencies: false,
                confidence: 0
            };
        }
    }

    /**
     * Detect functional overlap between two resources
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns Promise resolving to overlap result
     */
    private async detectFunctionalOverlap(resource1: ResourceInfo, resource2: ResourceInfo): Promise<OverlapResult> {
        try {
            // Initialize result
            const result: OverlapResult = {
                hasOverlap: false,
                overlapType: 'NONE',
                overlapScore: 0,
                overlapDetails: []
            };

            // Check for TGI overlap
            if (resource1.key.type === resource2.key.type) {
                if (resource1.key.instance === resource2.key.instance) {
                    // Same type and instance, different group
                    result.hasOverlap = true;
                    result.overlapType = 'INSTANCE_MATCH';
                    result.overlapScore = 0.8;
                    result.overlapDetails.push('Resources have the same type and instance ID');
                } else if (resource1.key.group === resource2.key.group) {
                    // Same type and group, different instance
                    result.hasOverlap = true;
                    result.overlapType = 'GROUP_MATCH';
                    result.overlapScore = 0.6;
                    result.overlapDetails.push('Resources have the same type and group ID');
                }
            }

            // Check for gameplay system overlap
            const systems1 = await this.getGameplaySystems(resource1.id);
            const systems2 = await this.getGameplaySystems(resource2.id);

            if (systems1 && systems2 && systems1.length > 0 && systems2.length > 0) {
                const overlappingSystems = systems1.filter(system => systems2.includes(system));

                if (overlappingSystems.length > 0) {
                    result.hasOverlap = true;
                    result.overlapType = result.overlapType === 'NONE' ? 'SYSTEM_OVERLAP' : 'MULTIPLE';
                    result.overlapScore = Math.max(result.overlapScore, 0.5 + (overlappingSystems.length * 0.1));
                    result.overlapDetails.push(`Resources affect the same gameplay systems: ${overlappingSystems.join(', ')}`);
                }
            }

            // Check for purpose overlap
            const purpose1 = await this.getResourcePurpose(resource1.id);
            const purpose2 = await this.getResourcePurpose(resource2.id);

            if (purpose1 && purpose2 && purpose1.primaryPurpose === purpose2.primaryPurpose) {
                result.hasOverlap = true;
                result.overlapType = result.overlapType === 'NONE' ? 'PURPOSE_OVERLAP' : 'MULTIPLE';
                result.overlapScore = Math.max(result.overlapScore, 0.7);
                result.overlapDetails.push(`Resources have the same primary purpose: ${purpose1.primaryPurpose}`);
            }

            // Check for dependency overlap using the dependency graph
            if (this.dependencyGraphAnalyzer && this.dependencyGraphAnalyzer.isGraphBuilt()) {
                const dependencies1 = this.dependencyGraphAnalyzer.getDependencies(resource1.id.toString());
                const dependencies2 = this.dependencyGraphAnalyzer.getDependencies(resource2.id.toString());

                if (dependencies1 && dependencies2) {
                    // Convert to arrays for easier comparison
                    const deps1 = Array.from(dependencies1);
                    const deps2 = Array.from(dependencies2);

                    // Find common dependencies
                    const commonDeps = deps1.filter(dep => deps2.includes(dep));

                    if (commonDeps.length > 0) {
                        result.hasOverlap = true;
                        result.overlapType = result.overlapType === 'NONE' ? 'DEPENDENCY_OVERLAP' : 'MULTIPLE';
                        result.overlapScore = Math.max(result.overlapScore, 0.4 + (commonDeps.length * 0.05));
                        result.overlapDetails.push(`Resources share ${commonDeps.length} common dependencies`);
                    }
                }
            }

            // Check for content similarity if content snippets are available
            if (resource1.contentSnippet && resource2.contentSnippet &&
                resource1.contentSnippet.length > 0 && resource2.contentSnippet.length > 0) {

                // Simple content similarity check
                const similarity = this.calculateContentSimilarity(resource1.contentSnippet, resource2.contentSnippet);

                if (similarity > 0.7) {
                    result.hasOverlap = true;
                    result.overlapType = result.overlapType === 'NONE' ? 'CONTENT_SIMILARITY' : 'MULTIPLE';
                    result.overlapScore = Math.max(result.overlapScore, similarity);
                    result.overlapDetails.push(`Resources have similar content (${Math.round(similarity * 100)}% similar)`);
                }
            }

            return result;
        } catch (error) {
            this.logger.error(`Error detecting functional overlap between resources ${resource1.id} and ${resource2.id}:`, error);
            return {
                hasOverlap: false,
                overlapType: 'NONE',
                overlapScore: 0,
                overlapDetails: []
            };
        }
    }

    /**
     * Calculate content similarity between two strings
     * @param content1 First content string
     * @param content2 Second content string
     * @returns Similarity score (0-1)
     */
    private calculateContentSimilarity(content1: string, content2: string): number {
        // Simple Jaccard similarity for quick comparison
        const tokens1 = new Set(content1.toLowerCase().split(/\W+/).filter(t => t.length > 2));
        const tokens2 = new Set(content2.toLowerCase().split(/\W+/).filter(t => t.length > 2));

        const intersection = new Set([...tokens1].filter(x => tokens2.has(x)));
        const union = new Set([...tokens1, ...tokens2]);

        return intersection.size / union.size;
    }

    /**
     * Assess compatibility between two resources
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param impact1 Gameplay impact of first resource
     * @param impact2 Gameplay impact of second resource
     * @param overlapResult Functional overlap result
     * @returns Promise resolving to compatibility result
     */
    private async assessCompatibility(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        impact1: GameplayImpact,
        impact2: GameplayImpact,
        overlapResult: OverlapResult
    ): Promise<CompatibilityResult> {
        try {
            // Initialize result
            const result: CompatibilityResult = {
                isCompatible: true,
                confidence: 0.5,
                severity: ConflictSeverity.LOW,
                reason: '',
                recommendations: []
            };

            // Check for high overlap score
            if (overlapResult.hasOverlap && overlapResult.overlapScore > 0.8) {
                result.isCompatible = false;
                result.confidence = overlapResult.overlapScore;
                result.severity = ConflictSeverity.HIGH;
                result.reason = `Resources have significant functional overlap: ${overlapResult.overlapDetails.join(', ')}`;
                result.recommendations.push('Consider using only one of these mods');
                result.recommendations.push('Check if these mods are designed to work together');
            }
            // Check for medium overlap with high impact
            else if (overlapResult.hasOverlap && overlapResult.overlapScore > 0.6 &&
                    (impact1.impactLevel === 'HIGH' || impact2.impactLevel === 'HIGH')) {
                result.isCompatible = false;
                result.confidence = overlapResult.overlapScore * 0.9;
                result.severity = ConflictSeverity.MEDIUM;
                result.reason = `Resources have functional overlap and high gameplay impact: ${overlapResult.overlapDetails.join(', ')}`;
                result.recommendations.push('Load these mods in the correct order');
                result.recommendations.push('Check for compatibility patches');
            }
            // Check for dependency conflicts
            else if (this.dependencyGraphAnalyzer && this.dependencyGraphAnalyzer.isGraphBuilt()) {
                // Check for circular dependencies
                const circularDependencies = this.dependencyGraphAnalyzer.findCircularDependencies();
                const resourceId1 = resource1.id.toString();
                const resourceId2 = resource2.id.toString();

                // Check if these resources are part of any circular dependency chain
                const involvedInCircular = circularDependencies.some(chain =>
                    chain.includes(resourceId1) && chain.includes(resourceId2)
                );

                if (involvedInCircular) {
                    result.isCompatible = false;
                    result.confidence = 0.9;
                    result.severity = ConflictSeverity.HIGH;
                    result.reason = 'Resources are part of a circular dependency chain';
                    result.recommendations.push('These mods have circular dependencies and may cause issues');
                    result.recommendations.push('Consider using only one of these mods');
                }

                // Check for dependency chain conflicts
                if (result.isCompatible) {
                    const chain1 = this.dependencyGraphAnalyzer.getDependencyChain(resourceId1);
                    const chain2 = this.dependencyGraphAnalyzer.getDependencyChain(resourceId2);

                    // Check if one resource depends on the other
                    const oneWayDependency = chain1.includes(resourceId2) || chain2.includes(resourceId1);

                    if (oneWayDependency) {
                        // This is not necessarily a conflict, but it's important to note
                        result.isCompatible = true; // Still compatible
                        result.confidence = 0.7;
                        result.severity = ConflictSeverity.LOW;
                        result.reason = 'Resources have a dependency relationship';
                        result.recommendations.push('Ensure these mods are loaded in the correct order');
                    }
                }
            }

            // If still compatible but there's some overlap, add a note
            if (result.isCompatible && overlapResult.hasOverlap && overlapResult.overlapScore > 0.3) {
                result.reason = `Resources have some functional overlap but may be compatible: ${overlapResult.overlapDetails.join(', ')}`;
                result.recommendations.push('Monitor for in-game issues when using these mods together');
            }

            return result;
        } catch (error) {
            this.logger.error(`Error assessing compatibility between resources ${resource1.id} and ${resource2.id}:`, error);
            return {
                isCompatible: true, // Default to compatible in case of error
                confidence: 0.5,
                severity: ConflictSeverity.LOW,
                reason: 'Error assessing compatibility',
                recommendations: ['Monitor for in-game issues']
            };
        }
    }
}

/**
 * Functional overlap result
 */
interface OverlapResult {
    /**
     * Whether there is functional overlap
     */
    hasOverlap: boolean;

    /**
     * Type of overlap
     */
    overlapType: 'NONE' | 'INSTANCE_MATCH' | 'GROUP_MATCH' | 'SYSTEM_OVERLAP' | 'PURPOSE_OVERLAP' | 'DEPENDENCY_OVERLAP' | 'CONTENT_SIMILARITY' | 'MULTIPLE';

    /**
     * Overlap score (0-1)
     */
    overlapScore: number;

    /**
     * Detailed explanation of overlap
     */
    overlapDetails: string[];
}

/**
 * Compatibility assessment result
 */
interface CompatibilityResult {
    /**
     * Whether the resources are compatible
     */
    isCompatible: boolean;

    /**
     * Confidence in the compatibility assessment (0-1)
     */
    confidence: number;

    /**
     * Severity of the conflict if incompatible
     */
    severity: ConflictSeverity;

    /**
     * Reason for incompatibility
     */
    reason: string;

    /**
     * Recommendations for resolving the conflict
     */
    recommendations: string[];
}
