/**
 * Parser for MTDT (Material Data) format
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DependencyInfo } from '../../../../databaseService.js';
import { MaterialHeaderInfo } from '../materialTypes.js';
import { handleMaterialExtractionError } from '../error/index.js';
import { extractParameterValue } from './parameterExtractor.js';

const logger = new Logger('MtdtParser');

/**
 * Parses a MTDT (Material Data) resource buffer
 * @param buffer The resource buffer
 * @param resourceId The resource ID
 * @returns The parsed material header and dependencies
 */
export function parseMtdt(buffer: Buffer, resourceId: number): { header: MaterialHeaderInfo, dependencies: DependencyInfo[] } {
    try {
        const dependencies: DependencyInfo[] = [];

        // Initialize header with default format
        const header: MaterialHeaderInfo = {
            format: 'MTDT',
            version: 0,
            materialCount: 0,
            flags: 0
        };

        if (buffer.length >= 16) {
            header.version = buffer.readUInt32LE(4);
            header.flags = buffer.readUInt32LE(8);
            header.parameterCount = buffer.readUInt32LE(12);

            // Extract parameters
            header.parameters = [];
            let offset = 16;

            for (let i = 0; i < header.parameterCount && offset + 4 <= buffer.length; i++) {
                const nameLength = buffer.readUInt32LE(offset);
                offset += 4;

                if (offset + nameLength <= buffer.length) {
                    const paramName = buffer.slice(offset, offset + nameLength).toString('utf8');
                    offset += nameLength;

                    if (offset + 4 <= buffer.length) {
                        const paramType = buffer.readUInt32LE(offset);
                        offset += 4;

                        // Extract parameter value based on type
                        const { value: paramValue, newOffset } = extractParameterValue(buffer, offset, paramType);
                        offset = newOffset;

                        header.parameters.push({
                            name: paramName,
                            value: paramValue
                        });
                    }
                }
            }
        }

        return { header, dependencies };
    } catch (error) {
        return handleMaterialExtractionError(
            error,
            {
                resourceId,
                operation: 'parseMtdt',
                bufferLength: buffer?.length,
                materialFormat: 'MTDT',
                additionalInfo: { format: 'MTDT' }
            },
            {
                header: {
                    format: 'MTDT_ERROR',
                    version: 0,
                    materialCount: 0,
                    flags: 0
                },
                dependencies: []
            }
        );
    }
}
