/**
 * CriticalParameterIdentifier
 *
 * This class identifies critical parameters in resources that have significant gameplay impact.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { <PERSON><PERSON>ey } from '../../../types/resource/interfaces.js';
import { CriticalParameter, CategorizationResult, GameplayImpact, ConflictRisk } from './types.js';
import { criticalParameters, getCriticalParameterById, getCriticalParametersByResourceType } from './data/criticalParameters.js';
import { calculateXmlPathConfidence, createCategorizationResult } from './utils/confidenceScoring.js';

const log = new Logger('CriticalParameterIdentifier');

/**
 * Identifier for critical parameters in resources
 */
export class CriticalParameterIdentifier {
  private databaseService: DatabaseService;

  /**
   * Create a new CriticalParameterIdentifier
   * @param databaseService Database service for storing and retrieving critical parameter data
   * @param logger Optional logger instance
   */
  constructor(
    databaseService: DatabaseService,
    logger?: Logger
  ) {
    this.databaseService = databaseService;
    log.info('CriticalParameterIdentifier initialized');
  }

  /**
   * Initialize the identifier
   */
  public async initialize(): Promise<void> {
    try {
      await this.createTables();
      log.info('CriticalParameterIdentifier tables created');
    } catch (error) {
      log.error(`Error initializing CriticalParameterIdentifier: ${error}`);
      throw error;
    }
  }

  /**
   * Create the necessary database tables
   */
  private async createTables(): Promise<void> {
    try {
      // Create the critical_parameters table
      await this.databaseService.executeQuery(`
        CREATE TABLE IF NOT EXISTS critical_parameters (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          applicable_resource_types TEXT NOT NULL,
          path TEXT NOT NULL,
          default_value TEXT,
          gameplay_impact TEXT NOT NULL,
          conflict_risk TEXT NOT NULL
        )
      `);

      // Create the resource_critical_parameters table
      await this.databaseService.executeQuery(`
        CREATE TABLE IF NOT EXISTS resource_critical_parameters (
          resource_id INTEGER NOT NULL,
          parameter_id TEXT NOT NULL,
          confidence INTEGER NOT NULL,
          value TEXT,
          modified BOOLEAN NOT NULL,
          timestamp INTEGER NOT NULL,
          PRIMARY KEY (resource_id, parameter_id),
          FOREIGN KEY (resource_id) REFERENCES Resources(id),
          FOREIGN KEY (parameter_id) REFERENCES critical_parameters(id)
        )
      `);

      // Populate the critical_parameters table with initial data
      for (const parameter of criticalParameters) {
        await this.databaseService.executeQuery(`
          INSERT OR IGNORE INTO critical_parameters (
            id, name, description, applicable_resource_types, path,
            default_value, gameplay_impact, conflict_risk
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          parameter.id,
          parameter.name,
          parameter.description,
          JSON.stringify(parameter.applicableResourceTypes),
          parameter.path,
          parameter.defaultValue !== undefined ? JSON.stringify(parameter.defaultValue) : null,
          parameter.gameplayImpact,
          parameter.conflictRisk
        ]);
      }
    } catch (error) {
      log.error(`Error creating tables: ${error}`);
      throw error;
    }
  }

  /**
   * Get all critical parameters
   * @returns Array of all critical parameters
   */
  public getAllParameters(): CriticalParameter[] {
    return criticalParameters;
  }

  /**
   * Get a critical parameter by ID
   * @param id Critical parameter ID
   * @returns The critical parameter, or undefined if not found
   */
  public getParameterById(id: string): CriticalParameter | undefined {
    return getCriticalParameterById(id);
  }

  /**
   * Get critical parameters by applicable resource type
   * @param resourceType Resource type
   * @returns Array of critical parameters applicable to the resource type
   */
  public getParametersByResourceType(resourceType: number): CriticalParameter[] {
    return getCriticalParametersByResourceType(resourceType);
  }

  /**
   * Identify critical parameters in a resource
   * @param resourceKey Resource key
   * @param resourceId Resource ID in the database
   * @param metadata Resource metadata
   * @param content Resource content (e.g., XML, SimData)
   * @returns Object containing identified parameters and their details
   */
  public async identifyCriticalParameters(
    resourceKey: ResourceKey,
    resourceId: number,
    metadata: Record<string, any>,
    content: string
  ): Promise<{ parameters: any[] }> {
    try {
      // Ensure we have valid inputs
      if (!resourceKey) {
        log.warn(`Invalid resourceKey for identifyCriticalParameters`);
        return { parameters: [] };
      }

      const results: CategorizationResult<CriticalParameter>[] = [];

      // Get parameters applicable to the resource type
      const applicableParameters = this.getParametersByResourceType(resourceKey.type);

      // Parse content if it's a string and looks like XML or JSON
      let parsedContent: any = null;
      if (typeof content === 'string') {
        try {
          if (content.trim().startsWith('<')) {
            // Attempt to parse as XML - simplified for now
            // In a real implementation, use a proper XML parser
            parsedContent = { xmlContent: content };
          } else if (content.trim().startsWith('{')) {
            // Attempt to parse as JSON
            parsedContent = JSON.parse(content);
          }
        } catch (e) {
          // If parsing fails, use the original content
          log.debug(`Failed to parse content: ${e}`);
        }
      }

      // Use metadata if available and content parsing failed
      if (!parsedContent && metadata) {
        parsedContent = metadata;
      }

      // If we still don't have usable content, return empty results
      if (!parsedContent) {
        log.debug(`No usable content for parameter identification`);
        return { parameters: [] };
      }

      for (const parameter of applicableParameters) {
        try {
          // Check if the parameter path exists in the content
          const pathParts = parameter.path.split('.');
          let currentContent = parsedContent;
          let pathExists = true;
          let value: any = undefined;

          // Navigate through the content using the path
          for (const part of pathParts) {
            if (!currentContent || typeof currentContent !== 'object' || !(part in currentContent)) {
              pathExists = false;
              break;
            }
            currentContent = currentContent[part as keyof typeof currentContent];
          }

          if (pathExists) {
            value = currentContent;

            // Calculate confidence based on path match
            const confidence = calculateXmlPathConfidence([parameter.path], parameter.path);

            // Check if the value is modified from the default
            const isModified = parameter.defaultValue !== undefined && value !== parameter.defaultValue;

            // Create reasons for the categorization
            const reasons: string[] = [
              `Found critical parameter '${parameter.name}' at path '${parameter.path}'`
            ];

            if (isModified) {
              reasons.push(`Value is modified from default: ${parameter.defaultValue} -> ${value}`);
            }

            // Add impact and risk information
            reasons.push(`Gameplay impact: ${parameter.gameplayImpact}`);
            reasons.push(`Conflict risk: ${parameter.conflictRisk}`);

            results.push(createCategorizationResult(parameter, confidence, reasons));
          }
        } catch (error) {
          log.error(`Error identifying critical parameter ${parameter.id}: ${error}`);
        }
      }

      // Sort by confidence (descending)
      const sortedResults = results.sort((a, b) => b.confidence - a.confidence);

      // Save the results to the database if we have a valid resourceId
      if (resourceId) {
        await this.saveCriticalParameters(resourceId, sortedResults);
      }

      // Format the results for the test script
      return {
        parameters: sortedResults.map(result => ({
          id: result.item.id,
          name: result.item.name,
          path: result.item.path,
          value: result.item.defaultValue,
          modified: result.reasons.some(r => r.startsWith('Value is modified')),
          impact: result.item.gameplayImpact.toLowerCase(),
          confidence: result.confidence
        }))
      };
    } catch (error) {
      log.error(`Error in identifyCriticalParameters: ${error}`);
      return { parameters: [] };
    }
  }

  /**
   * Save critical parameter identifications to the database
   * @param resourceId Resource ID
   * @param parameters Array of critical parameter categorization results
   */
  public async saveCriticalParameters(
    resourceId: number,
    parameters: CategorizationResult<CriticalParameter>[]
  ): Promise<void> {
    try {
      // Validate resourceId
      if (!resourceId) {
        log.warn(`Invalid resourceId (${resourceId}) for saveCriticalParameters, skipping save`);
        return;
      }

      // Validate parameters
      if (!parameters || parameters.length === 0) {
        log.debug(`No parameters to save for resource ${resourceId}`);
        return;
      }

      for (const parameter of parameters) {
        // Skip invalid parameters
        if (!parameter || !parameter.item || !parameter.item.id) {
          log.warn(`Invalid parameter for resource ${resourceId}, skipping`);
          continue;
        }

        // Extract value and modified status from reasons
        let value: any = null;
        let modified = false;

        if (parameter.reasons) {
          for (const reason of parameter.reasons) {
            if (reason && reason.startsWith('Value is modified from default:')) {
              modified = true;
              const valuePart = reason.split('->')[1];
              if (valuePart) {
                value = valuePart.trim();
              }
            }
          }
        }

        try {
          await this.databaseService.executeQuery(`
            INSERT OR REPLACE INTO resource_critical_parameters (
              resource_id, parameter_id, confidence, value, modified, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?)
          `, [
            resourceId,
            parameter.item.id,
            parameter.confidence,
            value !== null ? JSON.stringify(value) : null,
            modified ? 1 : 0,
            Date.now()
          ]);
        } catch (paramError) {
          log.error(`Error saving critical parameter ${parameter.item.id} for resource ${resourceId}: ${paramError}`);
          // Continue with other parameters instead of failing the entire operation
        }
      }
    } catch (error) {
      log.error(`Error saving critical parameters for resource ${resourceId}: ${error}`);
      // Don't throw the error, just log it to prevent cascading failures
    }
  }

  /**
   * Get critical parameters for a resource
   * @param resourceId Resource ID
   * @returns Array of critical parameter categorization results
   */
  public async getResourceCriticalParameters(resourceId: number): Promise<CategorizationResult<CriticalParameter>[]> {
    try {
      const results = await this.databaseService.executeQuery(`
        SELECT parameter_id, confidence, value, modified
        FROM resource_critical_parameters
        WHERE resource_id = ?
      `, [resourceId]);

      return Promise.all(results.map(async (row: any) => {
        const parameter = this.getParameterById(row.parameter_id);
        if (!parameter) {
          return null;
        }

        const reasons: string[] = [
          `Found critical parameter '${parameter.name}' at path '${parameter.path}'`
        ];

        if (row.modified) {
          reasons.push(`Value is modified from default: ${parameter.defaultValue} -> ${row.value}`);
        }

        reasons.push(`Gameplay impact: ${parameter.gameplayImpact}`);
        reasons.push(`Conflict risk: ${parameter.conflictRisk}`);

        return createCategorizationResult(parameter, row.confidence, reasons);
      })).then(results => results.filter(Boolean) as CategorizationResult<CriticalParameter>[]);
    } catch (error) {
      log.error(`Error getting critical parameters for resource ${resourceId}: ${error}`);
      throw error;
    }
  }
}
