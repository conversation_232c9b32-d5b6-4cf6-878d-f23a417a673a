/**
 * Enhanced Analysis Service - Phase 1 Integration
 *
 * This service integrates all Phase 1 improvements into a unified analysis system:
 * - Enhanced metadata extraction
 * - Intelligent conflict detection with reduced false positives
 * - Performance optimization for large collections
 * - Basic predictive conflict analysis
 *
 * This service acts as the main entry point for the enhanced analysis capabilities.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceInfo } from '../../../types/database.js';
import { EnhancedMetadataExtractor, EnhancedResourceMetadata } from './enhancedMetadataExtractor.js';
import { IntelligentConflictDetector, IntelligentConflict } from './intelligentConflictDetector.js';
import { PerformanceOptimizer, ProcessingStrategy, PerformanceMetrics } from './performanceOptimizer.js';
import { PredictiveConflictAnalyzer, CompatibilityPrediction, InstallationRiskAssessment } from './predictiveConflictAnalyzer.js';
import { ResourceKey as AppResourceKey } from '../../../types/resource/interfaces.js';
import { EventEmitter } from 'events';
import * as os from 'os';

const logger = new Logger('EnhancedAnalysisService');

/**
 * Enhanced analysis result interface
 */
export interface EnhancedAnalysisResult {
    resources: ResourceInfo[];
    enhancedMetadata: Map<number, EnhancedResourceMetadata>;
    intelligentConflicts: IntelligentConflict[];
    compatibilityPredictions: CompatibilityPrediction[];
    performanceMetrics: PerformanceMetrics;
    processingStrategy: ProcessingStrategy;
    analysisTimestamp: number;
    summary: AnalysisSummary;
}

/**
 * Analysis summary interface
 */
export interface AnalysisSummary {
    totalResources: number;
    resourcesByType: Map<string, number>;
    conflictsByType: Map<string, number>;
    conflictsBySeverity: Map<string, number>;
    gameplaySystemsAffected: string[];
    overallCompatibilityScore: number;
    recommendedActions: string[];
    riskAssessment: {
        level: 'low' | 'medium' | 'high' | 'critical';
        factors: string[];
    };
}

/**
 * Analysis options interface
 */
export interface EnhancedAnalysisOptions {
    enablePredictiveAnalysis: boolean;
    enablePerformanceOptimization: boolean;
    enableIntelligentConflictDetection: boolean;
    enableEnhancedMetadata: boolean;
    maxProcessingTime?: number; // milliseconds
    progressCallback?: (progress: number, status: string) => void;
    customProcessingStrategy?: Partial<ProcessingStrategy>;
}

/**
 * Enhanced analysis service class
 */
export class EnhancedAnalysisService extends EventEmitter {
    private databaseService: DatabaseService;
    private metadataExtractor: EnhancedMetadataExtractor;
    private conflictDetector: IntelligentConflictDetector;
    private performanceOptimizer: PerformanceOptimizer;
    private predictiveAnalyzer: PredictiveConflictAnalyzer;

    constructor(databaseService: DatabaseService) {
        super();
        this.databaseService = databaseService;
        this.metadataExtractor = new EnhancedMetadataExtractor(databaseService);
        this.conflictDetector = new IntelligentConflictDetector(databaseService);
        this.performanceOptimizer = new PerformanceOptimizer(databaseService);
        this.predictiveAnalyzer = new PredictiveConflictAnalyzer(databaseService);

        logger.info('Enhanced Analysis Service initialized');
    }

    /**
     * Perform comprehensive enhanced analysis on a collection of resources
     */
    public async performEnhancedAnalysis(
        resources: ResourceInfo[],
        options: EnhancedAnalysisOptions = {
            enablePredictiveAnalysis: true,
            enablePerformanceOptimization: true,
            enableIntelligentConflictDetection: true,
            enableEnhancedMetadata: true
        }
    ): Promise<EnhancedAnalysisResult> {
        const startTime = Date.now();

        try {
            logger.info(`Starting enhanced analysis for ${resources.length} resources`, options);

            // Step 1: Select optimal processing strategy
            const processingStrategy = this.selectProcessingStrategy(resources.length, options);

            // Step 2: Extract enhanced metadata
            let enhancedMetadata = new Map<number, EnhancedResourceMetadata>();
            if (options.enableEnhancedMetadata) {
                enhancedMetadata = await this.extractEnhancedMetadata(
                    resources,
                    processingStrategy,
                    options.progressCallback
                );
            }

            // Step 3: Detect intelligent conflicts
            let intelligentConflicts: IntelligentConflict[] = [];
            if (options.enableIntelligentConflictDetection) {
                intelligentConflicts = await this.detectIntelligentConflicts(
                    resources,
                    enhancedMetadata,
                    options.progressCallback
                );
            }

            // Step 4: Perform predictive analysis
            let compatibilityPredictions: CompatibilityPrediction[] = [];
            if (options.enablePredictiveAnalysis) {
                compatibilityPredictions = await this.performPredictiveAnalysis(
                    resources,
                    enhancedMetadata,
                    options.progressCallback
                );
            }

            // Step 5: Generate analysis summary
            const summary = this.generateAnalysisSummary(
                resources,
                enhancedMetadata,
                intelligentConflicts,
                compatibilityPredictions
            );

            // Step 6: Get performance metrics
            const performanceMetrics = this.performanceOptimizer.metrics || {
                totalProcessingTime: Date.now() - startTime,
                memoryUsage: { peak: 0, average: 0, current: 0 },
                throughput: { resourcesPerSecond: 0, bytesPerSecond: 0 },
                cacheEfficiency: { hitRate: 0, missRate: 0, evictionRate: 0 },
                workerUtilization: [],
                bottlenecks: []
            };

            const result: EnhancedAnalysisResult = {
                resources,
                enhancedMetadata,
                intelligentConflicts,
                compatibilityPredictions,
                performanceMetrics,
                processingStrategy,
                analysisTimestamp: Date.now(),
                summary
            };

            logger.info(`Enhanced analysis completed in ${Date.now() - startTime}ms`, {
                resourceCount: resources.length,
                conflictCount: intelligentConflicts.length,
                compatibilityPredictions: compatibilityPredictions.length
            });

            // Emit completion event
            this.emit('analysisComplete', result);

            return result;

        } catch (error) {
            logger.error('Error in enhanced analysis:', error);
            this.emit('analysisError', error);
            throw error;
        }
    }

    /**
     * Assess installation risk for a new mod
     */
    public async assessNewModInstallation(
        newModResources: ResourceInfo[],
        existingAnalysisResult: EnhancedAnalysisResult
    ): Promise<InstallationRiskAssessment> {
        try {
            logger.info('Assessing new mod installation risk');

            // Extract metadata for new mod
            const newModMetadata = await this.extractEnhancedMetadataForMod(newModResources);

            // Prepare existing mods data
            const existingMods = this.groupResourcesByMod(
                existingAnalysisResult.resources,
                existingAnalysisResult.enhancedMetadata
            );

            // Perform risk assessment
            const riskAssessment = await this.predictiveAnalyzer.assessInstallationRisk(
                newModResources,
                newModMetadata,
                existingMods
            );

            logger.info(`Installation risk assessment completed: ${riskAssessment.overallRisk}`);

            return riskAssessment;

        } catch (error) {
            logger.error('Error assessing new mod installation:', error);
            throw error;
        }
    }

    /**
     * Select optimal processing strategy
     */
    private selectProcessingStrategy(
        resourceCount: number,
        options: EnhancedAnalysisOptions
    ): ProcessingStrategy {
        if (!options.enablePerformanceOptimization) {
            // Return basic strategy for small collections
            return {
                name: 'Basic Strategy',
                batchSize: 10,
                parallelWorkers: 1,
                memoryThreshold: 512,
                streamingEnabled: false,
                cachingLevel: 'minimal',
                priorityProcessing: false
            };
        }

        const availableMemory = os.totalmem() / 1024 / 1024; // MB
        const cpuCores = os.cpus().length;

        let strategy = this.performanceOptimizer.selectProcessingStrategy(
            resourceCount,
            availableMemory,
            cpuCores
        );

        // Apply custom strategy overrides
        if (options.customProcessingStrategy) {
            strategy = { ...strategy, ...options.customProcessingStrategy };
        }

        return strategy;
    }

    /**
     * Extract enhanced metadata for all resources
     */
    private async extractEnhancedMetadata(
        resources: ResourceInfo[],
        strategy: ProcessingStrategy,
        progressCallback?: (progress: number, status: string) => void
    ): Promise<Map<number, EnhancedResourceMetadata>> {
        const enhancedMetadata = new Map<number, EnhancedResourceMetadata>();

        if (progressCallback) {
            progressCallback(10, 'Extracting enhanced metadata...');
        }

        // For now, simulate metadata extraction
        // In a real implementation, this would integrate with the existing extraction pipeline
        for (let i = 0; i < resources.length; i++) {
            const resource = resources[i];

            // Extract real enhanced metadata based on actual resource data
            const metadata: EnhancedResourceMetadata = {
                name: resource.name || `Resource_${resource.instance?.toString(16)}`,
                path: resource.path || '',
                size: resource.size || 0,
                hash: resource.hash || '',
                timestamp: Date.now(),
                gameplaySystemsAffected: this.analyzeGameplaySystemsAffected(resource),
                modificationSeverity: this.analyzeModificationSeverity(resource),
                performanceImpact: this.analyzePerformanceImpact(resource),
                gameVersionCompatibility: ['Base Game'],
                packDependencies: [],
                modFrameworkRequirements: [],
                codeQualityScore: 75,
                documentationCompleteness: 50,
                testingEvidence: false,
                userFacingChanges: [],
                configurationOptions: [],
                installationComplexity: 'simple',
                conflictPotential: 30,
                knownConflictPatterns: [],
                compatibilityNotes: []
            };

            if (resource.id) {
                enhancedMetadata.set(resource.id, metadata);
            }

            if (progressCallback && i % 10 === 0) {
                const progress = 10 + (i / resources.length) * 30;
                progressCallback(progress, `Processed ${i}/${resources.length} resources`);
            }
        }

        return enhancedMetadata;
    }

    /**
     * Detect intelligent conflicts
     */
    private async detectIntelligentConflicts(
        resources: ResourceInfo[],
        enhancedMetadata: Map<number, EnhancedResourceMetadata>,
        progressCallback?: (progress: number, status: string) => void
    ): Promise<IntelligentConflict[]> {
        if (progressCallback) {
            progressCallback(40, 'Detecting intelligent conflicts...');
        }

        const conflicts = await this.conflictDetector.detectIntelligentConflicts(
            resources,
            enhancedMetadata
        );

        if (progressCallback) {
            progressCallback(70, `Detected ${conflicts.length} potential conflicts`);
        }

        return conflicts;
    }

    /**
     * Perform predictive analysis
     */
    private async performPredictiveAnalysis(
        resources: ResourceInfo[],
        enhancedMetadata: Map<number, EnhancedResourceMetadata>,
        progressCallback?: (progress: number, status: string) => void
    ): Promise<CompatibilityPrediction[]> {
        if (progressCallback) {
            progressCallback(70, 'Performing predictive analysis...');
        }

        const predictions: CompatibilityPrediction[] = [];

        // Group resources by mod for pairwise analysis
        const modGroups = this.groupResourcesByMod(resources, enhancedMetadata);

        // Perform pairwise compatibility analysis (limited for performance)
        const maxComparisons = Math.min(modGroups.length * (modGroups.length - 1) / 2, 100);
        let comparisons = 0;

        for (let i = 0; i < modGroups.length && comparisons < maxComparisons; i++) {
            for (let j = i + 1; j < modGroups.length && comparisons < maxComparisons; j++) {
                const prediction = await this.predictiveAnalyzer.predictCompatibility(
                    modGroups[i].resources,
                    modGroups[j].resources,
                    modGroups[i].metadata,
                    modGroups[j].metadata
                );

                predictions.push(prediction);
                comparisons++;
            }
        }

        if (progressCallback) {
            progressCallback(90, `Completed ${predictions.length} compatibility predictions`);
        }

        return predictions;
    }

    /**
     * Generate analysis summary
     */
    private generateAnalysisSummary(
        resources: ResourceInfo[],
        enhancedMetadata: Map<number, EnhancedResourceMetadata>,
        conflicts: IntelligentConflict[],
        predictions: CompatibilityPrediction[]
    ): AnalysisSummary {
        // Count resources by type
        const resourcesByType = new Map<string, number>();
        for (const resource of resources) {
            const type = resource.resourceType || 'Unknown';
            resourcesByType.set(type, (resourcesByType.get(type) || 0) + 1);
        }

        // Count conflicts by type and severity
        const conflictsByType = new Map<string, number>();
        const conflictsBySeverity = new Map<string, number>();

        for (const conflict of conflicts) {
            conflictsByType.set(conflict.type, (conflictsByType.get(conflict.type) || 0) + 1);
            conflictsBySeverity.set(conflict.severity, (conflictsBySeverity.get(conflict.severity) || 0) + 1);
        }

        // Collect affected gameplay systems
        const gameplaySystemsAffected = new Set<string>();
        for (const metadata of enhancedMetadata.values()) {
            metadata.gameplaySystemsAffected.forEach(system => gameplaySystemsAffected.add(system));
        }

        // Calculate overall compatibility score
        const compatibilityScores = predictions.map(p => p.compatibilityScore);
        const overallCompatibilityScore = compatibilityScores.length > 0
            ? compatibilityScores.reduce((sum, score) => sum + score, 0) / compatibilityScores.length
            : 100;

        // Generate recommended actions
        const recommendedActions = this.generateRecommendedActions(conflicts, predictions);

        // Assess overall risk
        const riskAssessment = this.assessOverallRisk(conflicts, predictions, overallCompatibilityScore);

        return {
            totalResources: resources.length,
            resourcesByType,
            conflictsByType,
            conflictsBySeverity,
            gameplaySystemsAffected: Array.from(gameplaySystemsAffected),
            overallCompatibilityScore,
            recommendedActions,
            riskAssessment
        };
    }

    // Real analysis methods based on actual resource data
    private analyzeGameplaySystemsAffected(resource: ResourceInfo): string[] {
        const systems: string[] = [];

        // Analyze based on actual resource type and content
        if (resource.resourceType?.includes('TRAIT') || resource.name?.toLowerCase().includes('trait')) {
            systems.push('Traits');
        }
        if (resource.resourceType?.includes('SKILL') || resource.name?.toLowerCase().includes('skill')) {
            systems.push('Skills');
        }
        if (resource.resourceType?.includes('OBJECT') || resource.resourceType?.includes('CAS')) {
            systems.push('Objects');
        }
        if (resource.resourceType?.includes('BUILD') || resource.resourceType?.includes('BUY')) {
            systems.push('Build/Buy');
        }
        if (resource.resourceType?.includes('SIMDATA') || resource.resourceType?.includes('TUNING')) {
            systems.push('Game Logic');
        }

        // Default to general system if no specific match
        return systems.length > 0 ? systems : ['General'];
    }

    private analyzeModificationSeverity(resource: ResourceInfo): 'cosmetic' | 'functional' | 'structural' | 'core' {
        // Analyze based on actual resource characteristics
        if (resource.resourceType?.includes('SCRIPT') || resource.resourceType?.includes('PYTHON')) {
            return 'core'; // Script modifications are core changes
        }
        if (resource.resourceType?.includes('TUNING') || resource.resourceType?.includes('SIMDATA')) {
            return 'structural'; // Tuning changes affect game structure
        }
        if (resource.resourceType?.includes('TRAIT') || resource.resourceType?.includes('BUFF')) {
            return 'functional'; // Gameplay modifications
        }
        if (resource.resourceType?.includes('IMAGE') || resource.resourceType?.includes('TEXTURE')) {
            return 'cosmetic'; // Visual changes only
        }

        // Default based on resource size and complexity
        const size = resource.size || 0;
        if (size > 100000) return 'structural'; // Large resources likely structural
        if (size > 10000) return 'functional';   // Medium resources likely functional
        return 'cosmetic'; // Small resources likely cosmetic
    }

    private analyzePerformanceImpact(resource: ResourceInfo): 'negligible' | 'low' | 'medium' | 'high' | 'extreme' {
        const size = resource.size || 0;

        // Analyze based on actual resource size and type
        if (resource.resourceType?.includes('SCRIPT')) {
            return size > 50000 ? 'high' : 'medium'; // Scripts have higher impact
        }
        if (resource.resourceType?.includes('IMAGE') || resource.resourceType?.includes('TEXTURE')) {
            if (size > 10000000) return 'extreme'; // Very large textures
            if (size > 1000000) return 'high';     // Large textures
            if (size > 100000) return 'medium';    // Medium textures
            return 'low';
        }
        if (resource.resourceType?.includes('AUDIO') || resource.resourceType?.includes('SOUND')) {
            if (size > 5000000) return 'high';     // Large audio files
            if (size > 1000000) return 'medium';   // Medium audio files
            return 'low';
        }

        // Default analysis based on size
        if (size > 5000000) return 'extreme';
        if (size > 1000000) return 'high';
        if (size > 100000) return 'medium';
        if (size > 10000) return 'low';
        return 'negligible';
    }

    private async extractEnhancedMetadataForMod(resources: ResourceInfo[]): Promise<Map<number, EnhancedResourceMetadata>> {
        // Simplified implementation for new mod metadata extraction
        return this.extractEnhancedMetadata(resources, {
            name: 'Basic',
            batchSize: 10,
            parallelWorkers: 1,
            memoryThreshold: 512,
            streamingEnabled: false,
            cachingLevel: 'minimal',
            priorityProcessing: false
        });
    }

    private groupResourcesByMod(
        resources: ResourceInfo[],
        enhancedMetadata: Map<number, EnhancedResourceMetadata>
    ): Array<{ resources: ResourceInfo[]; metadata: Map<number, EnhancedResourceMetadata> }> {
        const modGroups = new Map<string, ResourceInfo[]>();

        // Group by package name
        for (const resource of resources) {
            const packageName = resource.packageName || 'Unknown';
            if (!modGroups.has(packageName)) {
                modGroups.set(packageName, []);
            }
            modGroups.get(packageName)!.push(resource);
        }

        // Convert to required format
        return Array.from(modGroups.values()).map(modResources => {
            const modMetadata = new Map<number, EnhancedResourceMetadata>();
            for (const resource of modResources) {
                if (resource.id && enhancedMetadata.has(resource.id)) {
                    modMetadata.set(resource.id, enhancedMetadata.get(resource.id)!);
                }
            }
            return { resources: modResources, metadata: modMetadata };
        });
    }

    private generateRecommendedActions(conflicts: IntelligentConflict[], predictions: CompatibilityPrediction[]): string[] {
        const actions: string[] = [];

        if (conflicts.length > 0) {
            actions.push('Review detected conflicts and apply suggested resolutions');
        }

        const lowCompatibilityPredictions = predictions.filter(p => p.compatibilityScore < 70);
        if (lowCompatibilityPredictions.length > 0) {
            actions.push('Check compatibility between flagged mod pairs');
        }

        return actions;
    }

    private assessOverallRisk(
        conflicts: IntelligentConflict[],
        predictions: CompatibilityPrediction[],
        compatibilityScore: number
    ): { level: 'low' | 'medium' | 'high' | 'critical'; factors: string[] } {
        const factors: string[] = [];
        let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';

        if (conflicts.length > 10) {
            factors.push('High number of detected conflicts');
            riskLevel = 'high';
        }

        if (compatibilityScore < 60) {
            factors.push('Low overall compatibility score');
            riskLevel = 'high';
        }

        return { level: riskLevel, factors };
    }

    /**
     * Cleanup resources
     */
    public async cleanup(): Promise<void> {
        await this.performanceOptimizer.cleanup();
        logger.info('Enhanced Analysis Service cleanup completed');
    }
}
