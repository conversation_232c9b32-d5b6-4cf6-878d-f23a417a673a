/**
 * SimData Schema Catalog
 * 
 * This file contains a catalog of known SimData schemas in Sims 4,
 * their purposes, relationships, and critical columns.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataSchema } from '../simDataParser.js';
import { SchemaInheritanceInfo, SchemaPurposeInfo, SchemaRelationshipInfo } from './schemaInterfaces.js';
import { KNOWN_SCHEMA_PARENTS, SCHEMA_CATEGORIES, GAMEPLAY_SYSTEMS } from './schemaConstants.js';

const log = new Logger('SimDataSchemaCatalog');

/**
 * Interface for a catalog schema entry
 */
export interface SchemaEntry {
    name: string;
    purpose: string;
    category: string;
    gameplaySystem: string;
    criticalColumns: string[];
    relatedSchemas: string[];
    parentSchema?: string;
    childSchemas?: string[];
    isCore: boolean;
    description: string;
}

/**
 * Class representing a catalog of SimData schemas
 */
export class SimDataSchemaCatalog {
    private schemas: Map<string, SchemaEntry>;
    private schemaIdMap: Map<number, string>;
    private schemaHashMap: Map<number, string>;
    private logger: Logger;

    constructor(logger?: Logger) {
        this.schemas = new Map();
        this.schemaIdMap = new Map();
        this.schemaHashMap = new Map();
        this.logger = logger || log;
        this.initializeCatalog();
    }

    /**
     * Initialize the catalog with known schemas
     */
    private initializeCatalog(): void {
        // Add core schemas
        this.addCoreSchemas();
        
        // Add expansion pack schemas
        this.addExpansionPackSchemas();
        
        // Add game pack schemas
        this.addGamePackSchemas();
        
        // Add stuff pack schemas
        this.addStuffPackSchemas();
        
        this.logger.info(`Initialized SimData schema catalog with ${this.schemas.size} schemas`);
    }

    /**
     * Add core game schemas to the catalog
     */
    private addCoreSchemas(): void {
        // Sim-related schemas
        this.addSchema({
            name: 'SimData',
            purpose: 'Stores basic Sim information',
            category: 'Sim',
            gameplaySystem: 'Sims',
            criticalColumns: ['age', 'gender', 'first_name', 'last_name', 'household_id'],
            relatedSchemas: ['SimInfo', 'Household'],
            isCore: true,
            description: 'Core schema for Sim data storage'
        });

        this.addSchema({
            name: 'Trait',
            purpose: 'Defines Sim traits',
            category: 'Gameplay',
            gameplaySystem: 'Traits',
            criticalColumns: ['display_name', 'trait_description', 'trait_type', 'ages', 'genders'],
            relatedSchemas: ['TraitStatisticModifier', 'TraitEffect', 'Buff'],
            isCore: true,
            description: 'Defines traits that can be assigned to Sims'
        });

        this.addSchema({
            name: 'Buff',
            purpose: 'Defines temporary Sim buffs',
            category: 'Gameplay',
            gameplaySystem: 'Buffs',
            criticalColumns: ['buff_name', 'buff_description', 'mood_type', 'mood_weight', 'timeout_string'],
            relatedSchemas: ['Trait', 'Mood', 'Commodity'],
            isCore: true,
            description: 'Defines temporary buffs that affect Sim behavior and emotions'
        });

        this.addSchema({
            name: 'Interaction',
            purpose: 'Defines Sim interactions',
            category: 'Gameplay',
            gameplaySystem: 'Interactions',
            criticalColumns: ['display_name', 'display_priority', 'allow_autonomous', 'target_type'],
            relatedSchemas: ['InteractionOutcome', 'Loot', 'Buff'],
            isCore: true,
            description: 'Defines interactions that Sims can perform'
        });

        // Object-related schemas
        this.addSchema({
            name: 'ObjectDefinition',
            purpose: 'Defines game objects',
            category: 'Object',
            gameplaySystem: 'Objects',
            criticalColumns: ['name', 'icon', 'price', 'tuning_id', 'footprint'],
            relatedSchemas: ['ObjectState', 'ObjectComponent', 'Interaction'],
            isCore: true,
            description: 'Core schema for defining objects in the game'
        });

        this.addSchema({
            name: 'ObjectState',
            purpose: 'Defines object states',
            category: 'Object',
            gameplaySystem: 'Objects',
            criticalColumns: ['value', 'display_name', 'state_description'],
            relatedSchemas: ['ObjectDefinition', 'ObjectComponent'],
            parentSchema: 'Object',
            isCore: true,
            description: 'Defines possible states for game objects'
        });

        // World-related schemas
        this.addSchema({
            name: 'Lot',
            purpose: 'Defines lot properties',
            category: 'World',
            gameplaySystem: 'Worlds',
            criticalColumns: ['lot_name', 'lot_description', 'lot_size', 'lot_type'],
            relatedSchemas: ['Venue', 'LotTrait'],
            isCore: true,
            description: 'Defines properties for lots in the game'
        });

        this.addSchema({
            name: 'Venue',
            purpose: 'Defines venue types',
            category: 'World',
            gameplaySystem: 'Worlds',
            criticalColumns: ['venue_type', 'venue_cost', 'lot_trait'],
            relatedSchemas: ['Lot', 'LotTrait'],
            isCore: true,
            description: 'Defines venue types for lots'
        });

        // Career-related schemas
        this.addSchema({
            name: 'Career',
            purpose: 'Defines Sim careers',
            category: 'Gameplay',
            gameplaySystem: 'Careers',
            criticalColumns: ['career_name', 'career_description', 'levels', 'salary_level'],
            relatedSchemas: ['CareerLevel', 'CareerTrack', 'Reward'],
            isCore: true,
            description: 'Defines careers that Sims can pursue'
        });

        this.addSchema({
            name: 'CareerLevel',
            purpose: 'Defines career levels',
            category: 'Gameplay',
            gameplaySystem: 'Careers',
            criticalColumns: ['level_name', 'level_description', 'simoleons_per_hour', 'performance_threshold'],
            relatedSchemas: ['Career', 'Reward'],
            parentSchema: 'Career',
            isCore: true,
            description: 'Defines levels within careers'
        });

        // Skill-related schemas
        this.addSchema({
            name: 'Skill',
            purpose: 'Defines Sim skills',
            category: 'Gameplay',
            gameplaySystem: 'Skills',
            criticalColumns: ['skill_name', 'skill_description', 'max_level', 'skill_curve'],
            relatedSchemas: ['SkillLevel', 'Statistic', 'Commodity'],
            isCore: true,
            description: 'Defines skills that Sims can learn'
        });

        // Relationship-related schemas
        this.addSchema({
            name: 'Relationship',
            purpose: 'Defines Sim relationships',
            category: 'Social',
            gameplaySystem: 'Relationships',
            criticalColumns: ['relationship_track', 'initial_value', 'decay_rate'],
            relatedSchemas: ['RelationshipBit', 'RelationshipTrack'],
            isCore: true,
            description: 'Defines relationship data between Sims'
        });

        // Animation-related schemas
        this.addSchema({
            name: 'Animation',
            purpose: 'Defines animations',
            category: 'Animation',
            gameplaySystem: 'Animations',
            criticalColumns: ['actor_name', 'animation_name', 'asm_key'],
            relatedSchemas: ['AnimationState', 'Posture'],
            isCore: true,
            description: 'Defines animations for Sims and objects'
        });

        // UI-related schemas
        this.addSchema({
            name: 'UI',
            purpose: 'Defines UI elements',
            category: 'UI',
            gameplaySystem: 'UI',
            criticalColumns: ['ui_name', 'ui_description', 'icon'],
            relatedSchemas: ['UIDialog', 'UINotification'],
            isCore: true,
            description: 'Defines UI elements in the game'
        });
    }

    /**
     * Add expansion pack schemas to the catalog
     */
    private addExpansionPackSchemas(): void {
        // Seasons-related schemas
        this.addSchema({
            name: 'Season',
            purpose: 'Defines seasons',
            category: 'World',
            gameplaySystem: 'Seasons',
            criticalColumns: ['season_name', 'duration', 'temperature_range'],
            relatedSchemas: ['Weather', 'Holiday'],
            isCore: false,
            description: 'Defines seasons from Seasons expansion pack'
        });

        // University-related schemas
        this.addSchema({
            name: 'University',
            purpose: 'Defines university data',
            category: 'Gameplay',
            gameplaySystem: 'University',
            criticalColumns: ['university_name', 'degrees', 'mascot'],
            relatedSchemas: ['Degree', 'Course', 'Career'],
            isCore: false,
            description: 'Defines university data from Discover University expansion pack'
        });

        // Eco Lifestyle-related schemas
        this.addSchema({
            name: 'EcoFootprint',
            purpose: 'Defines eco footprint data',
            category: 'World',
            gameplaySystem: 'Eco',
            criticalColumns: ['footprint_state', 'threshold', 'effects'],
            relatedSchemas: ['Neighborhood', 'Lot'],
            isCore: false,
            description: 'Defines eco footprint data from Eco Lifestyle expansion pack'
        });
    }

    /**
     * Add game pack schemas to the catalog
     */
    private addGamePackSchemas(): void {
        // Vampires-related schemas
        this.addSchema({
            name: 'Vampire',
            purpose: 'Defines vampire data',
            category: 'Gameplay',
            gameplaySystem: 'Vampires',
            criticalColumns: ['power_points', 'weakness_points', 'rank'],
            relatedSchemas: ['VampirePower', 'VampireWeakness', 'Sim'],
            isCore: false,
            description: 'Defines vampire data from Vampires game pack'
        });

        // Realm of Magic-related schemas
        this.addSchema({
            name: 'Spellcaster',
            purpose: 'Defines spellcaster data',
            category: 'Gameplay',
            gameplaySystem: 'Magic',
            criticalColumns: ['spell_charge', 'rank', 'known_spells'],
            relatedSchemas: ['Spell', 'SpellcasterTrait', 'Sim'],
            isCore: false,
            description: 'Defines spellcaster data from Realm of Magic game pack'
        });
    }

    /**
     * Add stuff pack schemas to the catalog
     */
    private addStuffPackSchemas(): void {
        // Paranormal-related schemas
        this.addSchema({
            name: 'Haunted',
            purpose: 'Defines haunted lot data',
            category: 'World',
            gameplaySystem: 'Paranormal',
            criticalColumns: ['haunting_level', 'ghost_type', 'effects'],
            relatedSchemas: ['Lot', 'Ghost'],
            isCore: false,
            description: 'Defines haunted lot data from Paranormal Stuff pack'
        });
    }

    /**
     * Add a schema to the catalog
     * @param entry Schema entry to add
     */
    public addSchema(entry: SchemaEntry): void {
        this.schemas.set(entry.name, entry);
        this.logger.debug(`Added schema ${entry.name} to catalog`);
    }

    /**
     * Add a schema ID mapping
     * @param schemaId Schema ID
     * @param schemaName Schema name
     */
    public addSchemaIdMapping(schemaId: number, schemaName: string): void {
        this.schemaIdMap.set(schemaId, schemaName);
    }

    /**
     * Add a schema hash mapping
     * @param schemaHash Schema hash
     * @param schemaName Schema name
     */
    public addSchemaHashMapping(schemaHash: number, schemaName: string): void {
        this.schemaHashMap.set(schemaHash, schemaName);
    }

    /**
     * Get a schema entry by name
     * @param name Schema name
     * @returns Schema entry or undefined if not found
     */
    public getSchema(name: string): SchemaEntry | undefined {
        return this.schemas.get(name);
    }

    /**
     * Get a schema entry by ID
     * @param schemaId Schema ID
     * @returns Schema entry or undefined if not found
     */
    public getSchemaById(schemaId: number): SchemaEntry | undefined {
        const schemaName = this.schemaIdMap.get(schemaId);
        if (schemaName) {
            return this.getSchema(schemaName);
        }
        return undefined;
    }

    /**
     * Get a schema entry by hash
     * @param schemaHash Schema hash
     * @returns Schema entry or undefined if not found
     */
    public getSchemaByHash(schemaHash: number): SchemaEntry | undefined {
        const schemaName = this.schemaHashMap.get(schemaHash);
        if (schemaName) {
            return this.getSchema(schemaName);
        }
        return undefined;
    }

    /**
     * Get all schemas in the catalog
     * @returns Array of all schema entries
     */
    public getAllSchemas(): SchemaEntry[] {
        return Array.from(this.schemas.values());
    }

    /**
     * Get schemas by category
     * @param category Schema category
     * @returns Array of schema entries in the category
     */
    public getSchemasByCategory(category: string): SchemaEntry[] {
        return this.getAllSchemas().filter(schema => schema.category === category);
    }

    /**
     * Get schemas by gameplay system
     * @param gameplaySystem Gameplay system
     * @returns Array of schema entries in the gameplay system
     */
    public getSchemasByGameplaySystem(gameplaySystem: string): SchemaEntry[] {
        return this.getAllSchemas().filter(schema => schema.gameplaySystem === gameplaySystem);
    }

    /**
     * Get related schemas for a given schema
     * @param schemaName Schema name
     * @returns Array of related schema entries
     */
    public getRelatedSchemas(schemaName: string): SchemaEntry[] {
        const schema = this.getSchema(schemaName);
        if (!schema) {
            return [];
        }

        const relatedSchemas: SchemaEntry[] = [];
        for (const relatedName of schema.relatedSchemas) {
            const relatedSchema = this.getSchema(relatedName);
            if (relatedSchema) {
                relatedSchemas.push(relatedSchema);
            }
        }

        // Add parent schema if it exists
        if (schema.parentSchema) {
            const parentSchema = this.getSchema(schema.parentSchema);
            if (parentSchema) {
                relatedSchemas.push(parentSchema);
            }
        }

        // Add child schemas if they exist
        if (schema.childSchemas) {
            for (const childName of schema.childSchemas) {
                const childSchema = this.getSchema(childName);
                if (childSchema) {
                    relatedSchemas.push(childSchema);
                }
            }
        }

        return relatedSchemas;
    }

    /**
     * Match a schema name to a known schema
     * @param schemaName Schema name to match
     * @returns Best matching schema entry or undefined if no match
     */
    public matchSchema(schemaName: string): SchemaEntry | undefined {
        // Direct match
        const directMatch = this.getSchema(schemaName);
        if (directMatch) {
            return directMatch;
        }

        // Try to match by prefix
        for (const [name, entry] of this.schemas.entries()) {
            if (schemaName.startsWith(name)) {
                return entry;
            }
        }

        // Try to match by parent schema
        for (const parent of KNOWN_SCHEMA_PARENTS) {
            if (schemaName.startsWith(parent)) {
                const parentSchema = this.getSchema(parent);
                if (parentSchema) {
                    return parentSchema;
                }
            }
        }

        // Try to match by category
        for (const [category, patterns] of Object.entries(SCHEMA_CATEGORIES)) {
            for (const pattern of patterns) {
                if (schemaName.includes(pattern)) {
                    // Return the first schema in this category
                    const categorySchemas = this.getSchemasByCategory(category);
                    if (categorySchemas.length > 0) {
                        return categorySchemas[0];
                    }
                }
            }
        }

        return undefined;
    }

    /**
     * Analyze a schema and extract purpose information
     * @param schema SimData schema to analyze
     * @returns Schema purpose information
     */
    public analyzeSchemaPurpose(schema: SimDataSchema): SchemaPurposeInfo {
        const schemaName = schema.name;
        const catalogEntry = this.matchSchema(schemaName);

        const purposeInfo: SchemaPurposeInfo = {
            schemaName,
            purpose: catalogEntry?.purpose || 'Unknown',
            gameplaySystem: catalogEntry?.gameplaySystem || 'Unknown',
            confidence: catalogEntry ? 80 : 30,
            keyColumns: [],
            criticalColumns: []
        };

        // Extract key columns based on column names
        const columnNames = schema.columns.map(col => col.name);
        
        // Identify key columns
        purposeInfo.keyColumns = columnNames.filter(name => 
            name.includes('id') || 
            name.includes('key') || 
            name.includes('name') || 
            name.includes('type') ||
            name.includes('reference')
        );

        // Identify critical columns
        if (catalogEntry) {
            // Use catalog's critical columns if available
            purposeInfo.criticalColumns = catalogEntry.criticalColumns.filter(name => 
                columnNames.includes(name)
            );
        } else {
            // Otherwise, try to identify critical columns based on patterns
            purposeInfo.criticalColumns = columnNames.filter(name => 
                name.includes('value') || 
                name.includes('multiplier') || 
                name.includes('chance') || 
                name.includes('weight') ||
                name.includes('priority') ||
                name.includes('threshold') ||
                name.includes('score')
            );
        }

        // If we don't have a catalog entry, try to infer purpose from schema name
        if (!catalogEntry) {
            // Try to infer gameplay system
            for (const system of GAMEPLAY_SYSTEMS) {
                if (schemaName.includes(system)) {
                    purposeInfo.gameplaySystem = system;
                    purposeInfo.confidence += 10;
                    break;
                }
            }

            // Try to infer purpose
            if (schemaName.includes('Trait')) {
                purposeInfo.purpose = 'Defines Sim traits';
                purposeInfo.confidence += 20;
            } else if (schemaName.includes('Buff')) {
                purposeInfo.purpose = 'Defines temporary Sim buffs';
                purposeInfo.confidence += 20;
            } else if (schemaName.includes('Interaction')) {
                purposeInfo.purpose = 'Defines Sim interactions';
                purposeInfo.confidence += 20;
            } else if (schemaName.includes('Object')) {
                purposeInfo.purpose = 'Defines game objects';
                purposeInfo.confidence += 20;
            } else if (schemaName.includes('Sim')) {
                purposeInfo.purpose = 'Stores Sim information';
                purposeInfo.confidence += 20;
            }
        }

        return purposeInfo;
    }

    /**
     * Analyze schema relationships
     * @param schema SimData schema to analyze
     * @returns Schema relationship information
     */
    public analyzeSchemaRelationships(schema: SimDataSchema): SchemaRelationshipInfo {
        const schemaName = schema.name;
        const catalogEntry = this.matchSchema(schemaName);

        const relationshipInfo: SchemaRelationshipInfo = {
            schemaName,
            relatedSchemas: []
        };

        // Add relationships from catalog if available
        if (catalogEntry) {
            // Add related schemas from catalog
            for (const relatedName of catalogEntry.relatedSchemas) {
                relationshipInfo.relatedSchemas.push({
                    name: relatedName,
                    relationship: 'reference',
                    confidence: 80
                });
            }

            // Add parent schema if it exists
            if (catalogEntry.parentSchema) {
                relationshipInfo.relatedSchemas.push({
                    name: catalogEntry.parentSchema,
                    relationship: 'parent',
                    confidence: 90
                });
            }

            // Add child schemas if they exist
            if (catalogEntry.childSchemas) {
                for (const childName of catalogEntry.childSchemas) {
                    relationshipInfo.relatedSchemas.push({
                        name: childName,
                        relationship: 'child',
                        confidence: 90
                    });
                }
            }
        } else {
            // Try to infer relationships from schema name
            // Check for parent schema in name
            for (const parent of KNOWN_SCHEMA_PARENTS) {
                if (schemaName.startsWith(parent) && schemaName !== parent) {
                    relationshipInfo.relatedSchemas.push({
                        name: parent,
                        relationship: 'parent',
                        confidence: 70
                    });
                    break;
                }
            }

            // Check for similar schemas based on name patterns
            for (const [name, entry] of this.schemas.entries()) {
                // Skip self
                if (name === schemaName) {
                    continue;
                }

                // Check for similar name patterns
                if (name.includes(schemaName) || schemaName.includes(name)) {
                    relationshipInfo.relatedSchemas.push({
                        name,
                        relationship: 'similar',
                        confidence: 60
                    });
                }
            }
        }

        return relationshipInfo;
    }
}

// Export a singleton instance
export const schemaCatalog = new SimDataSchemaCatalog();
