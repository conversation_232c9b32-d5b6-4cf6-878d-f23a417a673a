/**
 * ResourceStreamProvider Compatibility Layer
 * 
 * This module provides a compatibility layer for the ResourceStreamProvider class.
 * It delegates to the ConsolidatedResourceProvider while maintaining the same API.
 */

import { Readable } from 'stream';
import { Logger } from '../../../../utils/logging/logger.js';
import { ConsolidatedResourceProvider, ConsolidatedResourceProviderOptions } from '../consolidatedResourceProvider.js';
import { ResourceStreamOptions, ResourceMetadata } from '../resourceProviderBase.js';
import { PackageIndexEntry } from '../index.js'; // Points to stream/index.js
// BufferPool import removed as it's deleted and ConsolidatedResourceProvider handles its own

// Create a logger for this module
const logger = new Logger('ResourceStreamProviderCompat');

/**
 * ResourceStreamProvider compatibility implementation
 */
export class ResourceStreamProvider {
    private consolidatedProvider: ConsolidatedResourceProvider;
    // private bufferPool: BufferPool; // Removed
    private defaultTimeout: number;
    
    /**
     * Create a new resource stream provider
     * @param bufferPool Buffer pool for streaming operations
     */
    constructor() { // Removed bufferPool parameter
        // Buffer pool is managed by ConsolidatedResourceProvider
        
        // Set default timeout
        this.defaultTimeout = 30000; // 30 seconds default
        
        // Create consolidated provider
        this.consolidatedProvider = new ConsolidatedResourceProvider({
            defaultTimeout: this.defaultTimeout
        });
        
        logger.debug('Created ResourceStreamProvider compatibility layer');
    }
    
    /**
     * Create a readable stream for a resource
     * @param packagePath Path to the package file
     * @param entry Resource entry
     * @param options Stream options
     */
    public async createResourceStream(
        packagePath: string, 
        entry: PackageIndexEntry, 
        options: ResourceStreamOptions = {}
    ): Promise<Readable> {
        return this.consolidatedProvider.createResourceStream(packagePath, entry, options);
    }
    
    /**
     * Get metadata about a resource without reading its content
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    public async getResourceMetadata(
        packagePath: string, 
        entry: PackageIndexEntry
    ): Promise<ResourceMetadata> {
        return this.consolidatedProvider.getResourceMetadata(packagePath, entry);
    }
    
    /**
     * Check if a resource exists
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    public async resourceExists(
        packagePath: string, 
        entry: PackageIndexEntry
    ): Promise<boolean> {
        return this.consolidatedProvider.resourceExists(packagePath, entry);
    }
    
    /**
     * Close all open file handles
     */
    public async close(): Promise<void> {
        return this.consolidatedProvider.close();
    }
}
