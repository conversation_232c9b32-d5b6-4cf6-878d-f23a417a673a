/**
 * Texture Extractor for Terrain Paint Analysis
 * 
 * Extracts metadata from texture resources in terrain paint mods.
 * Textures are used for rendering terrain paints in the game.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey } from '../../../../../types/resource/interfaces.js';

const log = new Logger('TextureExtractor');

/**
 * Extracts metadata from texture resources
 */
export class TextureExtractor {
    /**
     * Extracts metadata from texture resources
     * @param resources Array of texture resources
     * @returns Metadata extracted from textures
     */
    public async extract(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): Promise<{
        count?: number;
        format?: string;
        dimensions?: string;
    }> {
        if (resources.length === 0) {
            return {};
        }
        
        log.info(`Extracting metadata from ${resources.length} texture resources`);
        
        // Parse each texture resource
        const textures = resources.map(resource => this.parseTexture(resource.buffer));
        
        // Extract metadata from textures
        const count = textures.length;
        const format = this.extractFormat(textures);
        const dimensions = this.extractDimensions(textures);
        
        return {
            count,
            format,
            dimensions
        };
    }
    
    /**
     * Parses a texture buffer
     * @param buffer Texture buffer
     * @returns Parsed texture
     */
    private parseTexture(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
        mipMapCount?: number;
    } {
        try {
            // Check for DDS magic number (0x20534444 or "DDS ")
            if (buffer.length >= 4 && buffer.toString('ascii', 0, 4) === 'DDS ') {
                return this.parseDDSTexture(buffer);
            }
            
            // Check for PNG magic number (0x89504E47)
            if (buffer.length >= 8 && 
                buffer[0] === 0x89 && 
                buffer[1] === 0x50 && 
                buffer[2] === 0x4E && 
                buffer[3] === 0x47 && 
                buffer[4] === 0x0D && 
                buffer[5] === 0x0A && 
                buffer[6] === 0x1A && 
                buffer[7] === 0x0A) {
                return this.parsePNGTexture(buffer);
            }
            
            // Check for JPEG magic number (0xFFD8FF)
            if (buffer.length >= 3 && 
                buffer[0] === 0xFF && 
                buffer[1] === 0xD8 && 
                buffer[2] === 0xFF) {
                return this.parseJPEGTexture(buffer);
            }
            
            // Unknown format
            return {};
        } catch (error) {
            log.error(`Error parsing texture: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a DDS texture buffer
     * @param buffer DDS texture buffer
     * @returns Parsed DDS texture
     */
    private parseDDSTexture(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
        mipMapCount?: number;
    } {
        try {
            // DDS header is 128 bytes
            if (buffer.length < 128) {
                return {};
            }
            
            // Extract width and height from DDS header
            const width = buffer.readUInt32LE(12);
            const height = buffer.readUInt32LE(16);
            
            // Extract mipmap count from DDS header
            const mipMapCount = buffer.readUInt32LE(28);
            
            // Extract format from DDS header
            const fourCC = buffer.toString('ascii', 84, 88);
            
            return {
                width,
                height,
                format: `DDS (${fourCC})`,
                mipMapCount
            };
        } catch (error) {
            log.error(`Error parsing DDS texture: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a PNG texture buffer
     * @param buffer PNG texture buffer
     * @returns Parsed PNG texture
     */
    private parsePNGTexture(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
    } {
        try {
            // PNG header is 8 bytes, followed by IHDR chunk
            if (buffer.length < 24) {
                return {};
            }
            
            // Extract width and height from IHDR chunk
            const width = buffer.readUInt32BE(16);
            const height = buffer.readUInt32BE(20);
            
            return {
                width,
                height,
                format: 'PNG'
            };
        } catch (error) {
            log.error(`Error parsing PNG texture: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a JPEG texture buffer
     * @param buffer JPEG texture buffer
     * @returns Parsed JPEG texture
     */
    private parseJPEGTexture(buffer: Buffer): {
        width?: number;
        height?: number;
        format?: string;
    } {
        try {
            // JPEG format is complex, so we'll just return the format
            return {
                format: 'JPEG'
            };
        } catch (error) {
            log.error(`Error parsing JPEG texture: ${error}`);
            return {};
        }
    }
    
    /**
     * Extracts format from textures
     * @param textures Array of parsed textures
     * @returns Extracted format
     */
    private extractFormat(textures: { width?: number; height?: number; format?: string; mipMapCount?: number; }[]): string | undefined {
        // Find the first texture with a format
        const texture = textures.find(t => t.format);
        
        if (texture && texture.format) {
            return texture.format;
        }
        
        return undefined;
    }
    
    /**
     * Extracts dimensions from textures
     * @param textures Array of parsed textures
     * @returns Extracted dimensions
     */
    private extractDimensions(textures: { width?: number; height?: number; format?: string; mipMapCount?: number; }[]): string | undefined {
        // Find the first texture with width and height
        const texture = textures.find(t => t.width && t.height);
        
        if (texture && texture.width && texture.height) {
            return `${texture.width}x${texture.height}`;
        }
        
        return undefined;
    }
}
