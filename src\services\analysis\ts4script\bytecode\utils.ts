/**
 * Bytecode Parser Utilities
 * 
 * This module provides utility functions for parsing Python bytecode.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { PythonVersion, BytecodeHeader } from './types.js';

// Create a logger for this module
const logger = new Logger('BytecodeUtils');

/**
 * Python version magic numbers
 * Format: [major, minor, micro, magic number]
 */
export const PYTHON_VERSION_MAGIC: [number, number, number, number][] = [
    // Python 3.6
    [3, 6, 0, 0x0A0D0D33],
    [3, 6, 1, 0x0A0D0D33],
    [3, 6, 2, 0x0A0D0D33],
    [3, 6, 3, 0x0A0D0D33],
    [3, 6, 4, 0x0A0D0D33],
    [3, 6, 5, 0x0A0D0D33],
    [3, 6, 6, 0x0A0D0D33],
    [3, 6, 7, 0x0A0D0D33],
    [3, 6, 8, 0x0A0D0D33],
    [3, 6, 9, 0x0A0D0D33],
    [3, 6, 10, 0x0A0D0D33],
    [3, 6, 11, 0x0A0D0D33],
    [3, 6, 12, 0x0A0D0D33],
    [3, 6, 13, 0x0A0D0D33],
    [3, 6, 14, 0x0A0D0D33],
    [3, 6, 15, 0x0A0D0D33],
    
    // Python 3.7
    [3, 7, 0, 0x0A0D0D42],
    [3, 7, 1, 0x0A0D0D42],
    [3, 7, 2, 0x0A0D0D42],
    [3, 7, 3, 0x0A0D0D42],
    [3, 7, 4, 0x0A0D0D42],
    [3, 7, 5, 0x0A0D0D42],
    [3, 7, 6, 0x0A0D0D42],
    [3, 7, 7, 0x0A0D0D42],
    [3, 7, 8, 0x0A0D0D42],
    [3, 7, 9, 0x0A0D0D42],
    [3, 7, 10, 0x0A0D0D42],
    [3, 7, 11, 0x0A0D0D42],
    [3, 7, 12, 0x0A0D0D42],
    
    // Python 3.8
    [3, 8, 0, 0x0A0D0D55],
    [3, 8, 1, 0x0A0D0D55],
    [3, 8, 2, 0x0A0D0D55],
    [3, 8, 3, 0x0A0D0D55],
    [3, 8, 4, 0x0A0D0D55],
    [3, 8, 5, 0x0A0D0D55],
    [3, 8, 6, 0x0A0D0D55],
    [3, 8, 7, 0x0A0D0D55],
    [3, 8, 8, 0x0A0D0D55],
    [3, 8, 9, 0x0A0D0D55],
    [3, 8, 10, 0x0A0D0D55],
    
    // Python 3.9
    [3, 9, 0, 0x0A0D0D61],
    [3, 9, 1, 0x0A0D0D61],
    [3, 9, 2, 0x0A0D0D61],
    [3, 9, 3, 0x0A0D0D61],
    [3, 9, 4, 0x0A0D0D61],
    [3, 9, 5, 0x0A0D0D61],
    [3, 9, 6, 0x0A0D0D61],
    [3, 9, 7, 0x0A0D0D61],
];

/**
 * EA-specific magic numbers (hypothetical)
 * These are placeholders and will need to be updated based on research
 */
export const EA_MAGIC_NUMBERS: Record<number, string> = {
    0x0A0D0D43: 'EA Python 3.7',
    0x0A0D0D44: 'EA Python 3.7 (Variant 2)',
    0x0A0D0D45: 'EA Python 3.7 (Variant 3)',
    0x0A0D0D56: 'EA Python 3.8',
    0x0A0D0D57: 'EA Python 3.8 (Variant 2)',
};

/**
 * Gets Python version information from a magic number
 * @param magic Magic number
 * @returns Python version or null if not recognized
 */
export function getPythonVersionFromMagic(magic: number): PythonVersion | null {
    // Check standard Python versions
    for (const [major, minor, micro, versionMagic] of PYTHON_VERSION_MAGIC) {
        if (magic === versionMagic) {
            return {
                major,
                minor,
                micro,
                magic,
                toString: () => `${major}.${minor}.${micro}`
            };
        }
    }
    
    // Check EA-specific magic numbers
    if (magic in EA_MAGIC_NUMBERS) {
        // For EA magic numbers, we'll make an educated guess about the version
        if (magic >= 0x0A0D0D43 && magic <= 0x0A0D0D45) {
            return {
                major: 3,
                minor: 7,
                micro: 0,
                magic,
                toString: () => `3.7.0 (EA Modified)`
            };
        } else if (magic >= 0x0A0D0D56 && magic <= 0x0A0D0D57) {
            return {
                major: 3,
                minor: 8,
                micro: 0,
                magic,
                toString: () => `3.8.0 (EA Modified)`
            };
        }
    }
    
    // Unknown magic number
    logger.warn(`Unknown Python bytecode magic number: 0x${magic.toString(16)}`);
    return null;
}

/**
 * Checks if a buffer starts with a valid Python bytecode header
 * @param buffer Buffer to check
 * @returns True if the buffer starts with a valid Python bytecode header
 */
export function hasPythonBytecodeHeader(buffer: Buffer): boolean {
    if (buffer.length < 8) {
        return false;
    }
    
    const magic = buffer.readUInt32LE(0);
    
    // Check standard Python versions
    for (const [, , , versionMagic] of PYTHON_VERSION_MAGIC) {
        if (magic === versionMagic) {
            return true;
        }
    }
    
    // Check EA-specific magic numbers
    if (magic in EA_MAGIC_NUMBERS) {
        return true;
    }
    
    return false;
}

/**
 * Extracts string literals from a buffer
 * @param buffer Buffer to extract string literals from
 * @returns Array of string literals
 */
export function extractStringLiterals(buffer: Buffer): string[] {
    const stringLiterals: string[] = [];
    let currentString = '';
    let inString = false;
    
    for (let i = 0; i < buffer.length; i++) {
        const byte = buffer[i];
        
        // Check for printable ASCII characters
        if (byte >= 32 && byte <= 126) {
            const char = String.fromCharCode(byte);
            
            if (!inString) {
                inString = true;
                currentString = char;
            } else {
                currentString += char;
            }
        } else {
            if (inString && currentString.length >= 3) {
                // Only add strings that are at least 3 characters long
                stringLiterals.push(currentString);
            }
            
            inString = false;
            currentString = '';
        }
    }
    
    // Add the last string if we were in the middle of one
    if (inString && currentString.length >= 3) {
        stringLiterals.push(currentString);
    }
    
    return stringLiterals;
}

/**
 * Creates a default bytecode header with error information
 * @param error Error message
 * @returns Default bytecode header
 */
export function createDefaultHeader(error: string): BytecodeHeader {
    logger.error(`Creating default header due to error: ${error}`);
    
    return {
        magic: 0,
        pythonVersion: null,
        timestamp: 0,
        isStandardFormat: false,
        isEAFormat: false,
        formatName: `Unknown (Error: ${error})`
    };
}

/**
 * Formats a timestamp from a Python bytecode header
 * @param timestamp Timestamp from bytecode header
 * @returns Formatted timestamp string
 */
export function formatTimestamp(timestamp: number): string {
    if (timestamp === 0) {
        return 'Unknown';
    }
    
    const date = new Date(timestamp * 1000);
    return date.toISOString();
}
