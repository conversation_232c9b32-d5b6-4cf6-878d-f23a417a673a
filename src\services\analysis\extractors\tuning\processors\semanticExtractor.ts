import { Logger } from '../../../../../utils/logging/logger.js';
import { TuningSemanticInfo } from '../types.js';
import { tuningTypeMap, getTuningCategory } from '../constants/tuningTypeMap.js';
import { getChildElements, getNodeAttribute, getNodeTextContent } from '../utils/xmlUtils.js';

/**
 * Extracts semantic information from tuning XML
 * @param rootElement The root XML element
 * @param tuningName The tuning name
 * @param log The logger instance
 * @returns Object with semantic information
 */
export function extractTuningSemanticInfo(
    rootElement: any, 
    tuningName: string,
    log: Logger
): TuningSemanticInfo {
    const result: TuningSemanticInfo = {
        tuningType: '',
        tuningCategory: '',
        tuningDescription: '',
        tuningTags: []
    };

    try {
        // Determine tuning type from name
        const nameLower = tuningName.toLowerCase();

        // Check against our tuning type map
        for (const [key, value] of Object.entries(tuningTypeMap)) {
            if (nameLower.includes(key)) {
                result.tuningType = value;
                break;
            }
        }

        // If no match found, try to determine from XML structure
        if (!result.tuningType) {
            // Check for common module patterns
            if (rootElement.module) {
                const moduleName = typeof rootElement.module === 'string'
                    ? rootElement.module
                    : getNodeAttribute(rootElement.module, 'name');

                if (moduleName) {
                    const moduleNameLower = moduleName.toLowerCase();

                    // Check against our tuning type map
                    for (const [key, value] of Object.entries(tuningTypeMap)) {
                        if (moduleNameLower.includes(key)) {
                            result.tuningType = value;
                            break;
                        }
                    }
                }
            }
        }

        // Determine category based on type
        if (result.tuningType) {
            result.tuningCategory = getTuningCategory(result.tuningType);
        }

        // Extract tags from XML structure
        result.tuningTags = extractTags(rootElement, log);

        // Generate a description based on type and tags
        if (result.tuningType) {
            result.tuningDescription = `${result.tuningType} tuning`;

            if (result.tuningTags.length > 0) {
                result.tuningDescription += ` with tags: ${result.tuningTags.slice(0, 3).join(', ')}`;
                if (result.tuningTags.length > 3) {
                    result.tuningDescription += ` and ${result.tuningTags.length - 3} more`;
                }
            }
        } else {
            result.tuningDescription = `Tuning XML for ${tuningName}`;
        }
    } catch (error) {
        log.warn(`Error extracting semantic info from tuning: ${error}`);
    }

    return result;
}

/**
 * Extracts tags from XML structure
 * @param obj The XML object
 * @param log The logger instance
 * @returns The extracted tags
 */
function extractTags(obj: any, log: Logger): string[] {
    const tags: string[] = [];
    
    try {
        if (!obj) return tags;

        // Check for direct tag elements
        if (obj.tags || obj.tag) {
            const tagElements = obj.tags || obj.tag;
            if (Array.isArray(tagElements)) {
                tagElements.forEach(tag => {
                    if (typeof tag === 'string') {
                        tags.push(tag);
                    } else if (tag.$ && tag.$.name) {
                        tags.push(tag.$.name);
                    } else if (tag.name) {
                        tags.push(tag.name);
                    } else {
                        const tagName = getNodeAttribute(tag, 'name');
                        if (tagName) {
                            tags.push(tagName);
                        }
                    }
                });
            }
        }

        // Check for flags
        if (obj.flags) {
            const flagElements = obj.flags;
            if (Array.isArray(flagElements)) {
                flagElements.forEach(flag => {
                    if (typeof flag === 'string') {
                        tags.push(`flag:${flag}`);
                    } else if (flag.$ && flag.$.name) {
                        tags.push(`flag:${flag.$.name}`);
                    } else if (flag.name) {
                        tags.push(`flag:${flag.name}`);
                    } else {
                        const flagName = getNodeAttribute(flag, 'name');
                        if (flagName) {
                            tags.push(`flag:${flagName}`);
                        }
                    }
                });
            }
        }

        // Check for traits
        if (obj.traits) {
            const traitElements = obj.traits;
            if (Array.isArray(traitElements)) {
                traitElements.forEach(trait => {
                    if (typeof trait === 'string') {
                        tags.push(`trait:${trait}`);
                    } else if (trait.$ && trait.$.name) {
                        tags.push(`trait:${trait.$.name}`);
                    } else if (trait.name) {
                        tags.push(`trait:${trait.name}`);
                    } else {
                        const traitName = getNodeAttribute(trait, 'name');
                        if (traitName) {
                            tags.push(`trait:${traitName}`);
                        }
                    }
                });
            }
        }

        // Recursively check child elements
        if (typeof obj === 'object') {
            // Get all child elements
            const children = getChildElements(obj);
            
            // Process each child
            for (const child of children) {
                const childTags = extractTags(child, log);
                tags.push(...childTags);
            }
        }
    } catch (error) {
        log.warn(`Error extracting tags: ${error}`);
    }

    // Remove duplicates
    return [...new Set(tags)];
}
