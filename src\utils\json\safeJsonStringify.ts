/**
 * Safely stringify an object to JSON, handling circular references
 * @param obj Object to stringify
 * @returns JSON string representation of the object
 */
export function safeJsonStringify(obj: any): string {
    try {
        // Handle null or undefined
        if (obj === null || obj === undefined) {
            return String(obj);
        }

        // Handle primitive types
        if (typeof obj !== 'object' && typeof obj !== 'function') {
            return String(obj);
        }

        // Handle circular references
        const seen = new WeakSet();
        
        return JSON.stringify(obj, (key, value) => {
            // Handle functions
            if (typeof value === 'function') {
                return '[Function]';
            }
            
            // Handle symbols
            if (typeof value === 'symbol') {
                return value.toString();
            }
            
            // Handle BigInt
            if (typeof value === 'bigint') {
                return value.toString();
            }
            
            // Handle circular references
            if (typeof value === 'object' && value !== null) {
                if (seen.has(value)) {
                    return '[Circular]';
                }
                seen.add(value);
            }
            
            return value;
        }, 2);
    } catch (error) {
        return `[Error stringifying object: ${error instanceof Error ? error.message : String(error)}]`;
    }
}
