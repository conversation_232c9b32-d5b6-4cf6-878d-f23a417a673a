/* App Container */
#app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background-color: white;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.app-header h1 {
  margin: 0;
  color: var(--secondary-color);
  font-size: 1.8rem;
}

.app-content {
  padding: 1rem 0;
}

.app-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  text-align: center;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* Error Notifications */
.error-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--danger-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 4px;
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
  max-width: 400px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  #app-container {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }

  .error-notification {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}

/* Loading State */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* File Upload Area */
.file-upload-container {
  background: var(--light-gray);
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.file-upload-container.drag-over {
  border-color: var(--primary-color);
  background: rgba(74, 144, 226, 0.1);
}

.file-upload-container p {
  margin: 0.5rem 0;
  color: var(--secondary-color);
}

.file-list {
  margin-top: 1rem;
  text-align: left;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.file-item .file-name {
  flex: 1;
  margin-right: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-item .file-size {
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.file-item .remove-file {
  color: var(--danger-color);
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.file-item .remove-file:hover {
  background-color: rgba(231, 76, 60, 0.1);
} 