/**
 * Cache statistics interface
 */
export interface CacheStats {
  /** Number of cache hits */
  hits: number;
  /** Number of cache misses */
  misses: number;
  /** Hit ratio (hits / (hits + misses)) */
  hitRatio: number;
  /** Current number of items in the cache */
  size: number;
  /** Maximum number of items allowed in the cache */
  maxSize: number;
  /** Total number of operations performed on the cache */
  operations: number;
  /** Timestamp when the statistics were last reset */
  resetTimestamp: number;
}

/**
 * Cache serialization options
 */
export interface CacheSerializationOptions {
  /** Whether to include statistics in the serialized data */
  includeStats?: boolean;
  /** Maximum number of items to include in the serialized data */
  maxItems?: number;
  /** Function to determine if an item should be included in the serialized data */
  itemFilter?: <K, V>(key: K, value: V) => boolean;
}

/**
 * Serialized cache data
 */
export interface SerializedCache<K, V> {
  /** Cache items */
  items: { key: K; value: V }[];
  /** Cache statistics */
  stats?: CacheStats;
  /** Timestamp when the cache was serialized */
  timestamp: number;
  /** Version of the serialization format */
  version: string;
}

/**
 * A simple in-memory LRU cache implementation
 */
export class LRUCache<K, V> {
  private cache: Map<K, V>;
  private maxSize: number;
  private keys: K[];
  private hitCount: number = 0;
  private missCount: number = 0;
  private operationCount: number = 0;
  private statsResetTimestamp: number = Date.now();

  /**
   * Creates a new LRU cache
   * @param maxSize Maximum number of items in the cache
   */
  constructor(maxSize: number = 100) {
    this.cache = new Map<K, V>();
    this.maxSize = maxSize;
    this.keys = [];
  }

  /**
   * Gets a value from the cache
   * @param key Cache key
   * @returns The cached value, or undefined if not found
   */
  get(key: K): V | undefined {
    this.operationCount++;

    if (!this.cache.has(key)) {
      this.missCount++;
      return undefined;
    }

    // Move key to end (most recently used)
    this.moveToEnd(key);

    this.hitCount++;
    return this.cache.get(key);
  }

  /**
   * Sets a value in the cache
   * @param key Cache key
   * @param value Value to cache
   */
  set(key: K, value: V): void {
    this.operationCount++;

    // If key already exists, update value and move to end
    if (this.cache.has(key)) {
      this.cache.set(key, value);
      this.moveToEnd(key);
      return;
    }

    // Add new key-value pair
    this.cache.set(key, value);
    this.keys.push(key);

    // If cache is too large, remove least recently used item
    if (this.keys.length > this.maxSize) {
      const oldestKey = this.keys.shift();
      if (oldestKey !== undefined) {
        this.cache.delete(oldestKey);
      }
    }
  }

  /**
   * Checks if a key exists in the cache
   * @param key Cache key
   * @returns True if the key exists in the cache
   */
  has(key: K): boolean {
    this.operationCount++;
    return this.cache.has(key);
  }

  /**
   * Removes a value from the cache
   * @param key Cache key
   * @returns True if the key was found and removed
   */
  delete(key: K): boolean {
    this.operationCount++;

    if (!this.cache.has(key)) {
      return false;
    }

    this.cache.delete(key);

    // Remove key from keys array
    const index = this.keys.indexOf(key);
    if (index !== -1) {
      this.keys.splice(index, 1);
    }

    return true;
  }

  /**
   * Clears the cache
   */
  clear(): void {
    this.operationCount++;
    this.cache.clear();
    this.keys = [];
  }

  /**
   * Gets the number of items in the cache
   */
  get size(): number {
    return this.cache.size;
  }

  /**
   * Gets the maximum size of the cache
   */
  get maxCacheSize(): number {
    return this.maxSize;
  }

  /**
   * Sets the maximum size of the cache
   * @param size New maximum size
   */
  set maxCacheSize(size: number) {
    this.maxSize = size;

    // If new size is smaller than current size, remove oldest items
    while (this.keys.length > this.maxSize) {
      const oldestKey = this.keys.shift();
      if (oldestKey !== undefined) {
        this.cache.delete(oldestKey);
      }
    }
  }

  /**
   * Gets all keys in the cache
   */
  get allKeys(): K[] {
    return [...this.keys];
  }

  /**
   * Gets all values in the cache
   */
  get allValues(): V[] {
    return [...this.cache.values()];
  }

  /**
   * Gets all entries in the cache
   */
  get entries(): [K, V][] {
    return this.keys.map(key => [key, this.cache.get(key)!] as [K, V]);
  }

  /**
   * Gets cache statistics
   */
  getStats(): CacheStats {
    const totalOperations = this.hitCount + this.missCount;
    const hitRatio = totalOperations > 0 ? this.hitCount / totalOperations : 0;

    return {
      hits: this.hitCount,
      misses: this.missCount,
      hitRatio,
      size: this.cache.size,
      maxSize: this.maxSize,
      operations: this.operationCount,
      resetTimestamp: this.statsResetTimestamp,
    };
  }

  /**
   * Resets cache statistics
   */
  resetStats(): void {
    this.hitCount = 0;
    this.missCount = 0;
    this.operationCount = 0;
    this.statsResetTimestamp = Date.now();
  }

  /**
   * Serializes the cache to a JSON-compatible object
   * @param options Serialization options
   * @returns Serialized cache data
   */
  serialize(options: CacheSerializationOptions = {}): SerializedCache<K, V> {
    const { includeStats = true, maxItems = this.maxSize, itemFilter } = options;

    // Get items to serialize
    let items = this.keys.map(key => ({ key, value: this.cache.get(key)! }));

    // Apply filter if provided
    if (itemFilter) {
      items = items.filter(item => itemFilter(item.key, item.value));
    }

    // Limit number of items
    if (maxItems < items.length) {
      items = items.slice(-maxItems);
    }

    // Create serialized data
    const serialized: SerializedCache<K, V> = {
      items,
      timestamp: Date.now(),
      version: '1.0',
    };

    // Include stats if requested
    if (includeStats) {
      serialized.stats = this.getStats();
    }

    return serialized;
  }

  /**
   * Deserializes cache data and loads it into this cache
   * @param data Serialized cache data
   * @param options Options for deserialization
   * @returns Number of items loaded
   */
  deserialize(data: SerializedCache<K, V>, options: { clear?: boolean } = {}): number {
    // Clear existing cache if requested
    if (options.clear) {
      this.clear();
    }

    // Load items
    let loadedCount = 0;
    for (const { key, value } of data.items) {
      this.set(key, value);
      loadedCount++;
    }

    return loadedCount;
  }

  /**
   * Moves a key to the end of the keys array (most recently used)
   * @param key Cache key
   */
  private moveToEnd(key: K): void {
    const index = this.keys.indexOf(key);
    if (index !== -1) {
      this.keys.splice(index, 1);
      this.keys.push(key);
    }
  }
}

/**
 * A simple persistent cache implementation that uses localStorage
 */
export class PersistentCache<V> {
  private prefix: string;
  private maxItems: number;
  private hitCount: number = 0;
  private missCount: number = 0;
  private operationCount: number = 0;
  private statsResetTimestamp: number = Date.now();

  /**
   * Creates a new persistent cache
   * @param prefix Prefix for localStorage keys
   * @param maxItems Maximum number of items in the cache
   */
  constructor(prefix: string, maxItems: number = 100) {
    this.prefix = prefix;
    this.maxItems = maxItems;

    // Try to load stats from localStorage
    this.loadStats();
  }

  /**
   * Gets a value from the cache
   * @param key Cache key
   * @returns The cached value, or undefined if not found
   */
  get(key: string): V | undefined {
    this.operationCount++;

    const itemKey = `${this.prefix}_${key}`;
    const item = localStorage.getItem(itemKey);

    if (!item) {
      this.missCount++;
      this.saveStats();
      return undefined;
    }

    try {
      // Update access time
      this.updateAccessTime(key);

      // Parse and return value
      const parsed = JSON.parse(item);
      this.hitCount++;
      this.saveStats();
      return parsed.value;
    } catch (error) {
      console.error(`Error parsing cached item ${key}:`, error);
      this.missCount++;
      this.saveStats();
      return undefined;
    }
  }

  /**
   * Sets a value in the cache
   * @param key Cache key
   * @param value Value to cache
   */
  set(key: string, value: V): void {
    this.operationCount++;

    const itemKey = `${this.prefix}_${key}`;
    const item = {
      value,
      timestamp: Date.now(),
    };

    try {
      // Store item
      localStorage.setItem(itemKey, JSON.stringify(item));

      // Update cache index
      this.addToIndex(key);

      // Prune cache if necessary
      this.pruneCache();

      // Save stats
      this.saveStats();
    } catch (error) {
      console.error(`Error storing cached item ${key}:`, error);
    }
  }

  /**
   * Checks if a key exists in the cache
   * @param key Cache key
   * @returns True if the key exists in the cache
   */
  has(key: string): boolean {
    this.operationCount++;
    const itemKey = `${this.prefix}_${key}`;
    const exists = localStorage.getItem(itemKey) !== null;
    this.saveStats();
    return exists;
  }

  /**
   * Removes a value from the cache
   * @param key Cache key
   * @returns True if the key was found and removed
   */
  delete(key: string): boolean {
    this.operationCount++;

    const itemKey = `${this.prefix}_${key}`;
    const exists = localStorage.getItem(itemKey) !== null;

    if (exists) {
      localStorage.removeItem(itemKey);
      this.removeFromIndex(key);
      this.saveStats();
    }

    return exists;
  }

  /**
   * Clears all items in this cache
   */
  clear(): void {
    this.operationCount++;

    const index = this.getIndex();

    // Remove all items
    for (const key of index) {
      const itemKey = `${this.prefix}_${key}`;
      localStorage.removeItem(itemKey);
    }

    // Clear index
    localStorage.removeItem(`${this.prefix}_index`);

    // Save stats
    this.saveStats();
  }

  /**
   * Gets the number of items in the cache
   */
  get size(): number {
    return this.getIndex().length;
  }

  /**
   * Gets all keys in the cache
   */
  get allKeys(): string[] {
    return this.getIndex();
  }

  /**
   * Gets all values in the cache
   */
  get allValues(): V[] {
    const values: V[] = [];
    const keys = this.getIndex();

    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        values.push(value);
      }
    }

    return values;
  }

  /**
   * Gets all entries in the cache
   */
  get entries(): [string, V][] {
    const entries: [string, V][] = [];
    const keys = this.getIndex();

    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        entries.push([key, value]);
      }
    }

    return entries;
  }

  /**
   * Gets cache statistics
   */
  getStats(): CacheStats {
    const totalOperations = this.hitCount + this.missCount;
    const hitRatio = totalOperations > 0 ? this.hitCount / totalOperations : 0;

    return {
      hits: this.hitCount,
      misses: this.missCount,
      hitRatio,
      size: this.size,
      maxSize: this.maxItems,
      operations: this.operationCount,
      resetTimestamp: this.statsResetTimestamp,
    };
  }

  /**
   * Resets cache statistics
   */
  resetStats(): void {
    this.hitCount = 0;
    this.missCount = 0;
    this.operationCount = 0;
    this.statsResetTimestamp = Date.now();
    this.saveStats();
  }

  /**
   * Serializes the cache to a JSON-compatible object
   * @param options Serialization options
   * @returns Serialized cache data
   */
  serialize(options: CacheSerializationOptions = {}): SerializedCache<string, V> {
    const { includeStats = true, maxItems = this.maxItems, itemFilter } = options;

    // Get items to serialize
    let items: { key: string; value: V }[] = [];
    const keys = this.getIndex();

    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        // Skip if filter is provided and item doesn't pass
        if (itemFilter && !itemFilter(key, value)) {
          continue;
        }

        items.push({ key, value });
      }
    }

    // Limit number of items
    if (maxItems < items.length) {
      items = items.slice(-maxItems);
    }

    // Create serialized data
    const serialized: SerializedCache<string, V> = {
      items,
      timestamp: Date.now(),
      version: '1.0',
    };

    // Include stats if requested
    if (includeStats) {
      serialized.stats = this.getStats();
    }

    return serialized;
  }

  /**
   * Deserializes cache data and loads it into this cache
   * @param data Serialized cache data
   * @param options Options for deserialization
   * @returns Number of items loaded
   */
  deserialize(data: SerializedCache<string, V>, options: { clear?: boolean } = {}): number {
    // Clear existing cache if requested
    if (options.clear) {
      this.clear();
    }

    // Load items
    let loadedCount = 0;
    for (const { key, value } of data.items) {
      this.set(key, value);
      loadedCount++;
    }

    return loadedCount;
  }

  /**
   * Exports the cache to a JSON string
   * @param options Serialization options
   * @returns JSON string representation of the cache
   */
  exportToJson(options: CacheSerializationOptions = {}): string {
    const serialized = this.serialize(options);
    return JSON.stringify(serialized);
  }

  /**
   * Imports the cache from a JSON string
   * @param json JSON string representation of the cache
   * @param options Options for deserialization
   * @returns Number of items loaded
   */
  importFromJson(json: string, options: { clear?: boolean } = {}): number {
    try {
      const data = JSON.parse(json) as SerializedCache<string, V>;
      return this.deserialize(data, options);
    } catch (error) {
      console.error('Error importing cache from JSON:', error);
      return 0;
    }
  }

  /**
   * Gets the index of all cached keys
   * @returns Array of cache keys
   */
  private getIndex(): string[] {
    const indexKey = `${this.prefix}_index`;
    const index = localStorage.getItem(indexKey);

    if (!index) {
      return [];
    }

    try {
      return JSON.parse(index);
    } catch (error) {
      console.error(`Error parsing cache index:`, error);
      return [];
    }
  }

  /**
   * Adds a key to the cache index
   * @param key Cache key
   */
  private addToIndex(key: string): void {
    const index = this.getIndex();

    // If key already exists in index, remove it
    const keyIndex = index.indexOf(key);
    if (keyIndex !== -1) {
      index.splice(keyIndex, 1);
    }

    // Add key to end of index
    index.push(key);

    // Save index
    localStorage.setItem(`${this.prefix}_index`, JSON.stringify(index));
  }

  /**
   * Removes a key from the cache index
   * @param key Cache key
   */
  private removeFromIndex(key: string): void {
    const index = this.getIndex();

    // Remove key from index
    const keyIndex = index.indexOf(key);
    if (keyIndex !== -1) {
      index.splice(keyIndex, 1);

      // Save index
      localStorage.setItem(`${this.prefix}_index`, JSON.stringify(index));
    }
  }

  /**
   * Updates the access time for a cached item
   * @param key Cache key
   */
  private updateAccessTime(key: string): void {
    const itemKey = `${this.prefix}_${key}`;
    const item = localStorage.getItem(itemKey);

    if (!item) {
      return;
    }

    try {
      const parsed = JSON.parse(item);
      parsed.timestamp = Date.now();

      // Store updated item
      localStorage.setItem(itemKey, JSON.stringify(parsed));

      // Update index
      this.addToIndex(key);
    } catch (error) {
      console.error(`Error updating access time for ${key}:`, error);
    }
  }

  /**
   * Prunes the cache if it exceeds the maximum size
   */
  private pruneCache(): void {
    const index = this.getIndex();

    // If cache is not too large, do nothing
    if (index.length <= this.maxItems) {
      return;
    }

    // Get access times for all items
    const items: { key: string; timestamp: number }[] = [];

    for (const key of index) {
      const itemKey = `${this.prefix}_${key}`;
      const item = localStorage.getItem(itemKey);

      if (item) {
        try {
          const parsed = JSON.parse(item);
          items.push({ key, timestamp: parsed.timestamp });
        } catch (error) {
          console.error(`Error parsing cached item ${key}:`, error);
        }
      }
    }

    // Sort by timestamp (oldest first)
    items.sort((a, b) => a.timestamp - b.timestamp);

    // Remove oldest items
    const itemsToRemove = index.length - this.maxItems;
    for (let i = 0; i < itemsToRemove && i < items.length; i++) {
      this.delete(items[i].key);
    }
  }

  /**
   * Saves cache statistics to localStorage
   */
  private saveStats(): void {
    try {
      const statsKey = `${this.prefix}_stats`;
      const stats = {
        hits: this.hitCount,
        misses: this.missCount,
        operations: this.operationCount,
        resetTimestamp: this.statsResetTimestamp,
      };

      localStorage.setItem(statsKey, JSON.stringify(stats));
    } catch (error) {
      console.error('Error saving cache statistics:', error);
    }
  }

  /**
   * Loads cache statistics from localStorage
   */
  private loadStats(): void {
    try {
      const statsKey = `${this.prefix}_stats`;
      const statsJson = localStorage.getItem(statsKey);

      if (statsJson) {
        const stats = JSON.parse(statsJson);
        this.hitCount = stats.hits || 0;
        this.missCount = stats.misses || 0;
        this.operationCount = stats.operations || 0;
        this.statsResetTimestamp = stats.resetTimestamp || Date.now();
      }
    } catch (error) {
      console.error('Error loading cache statistics:', error);
      // Reset stats if there was an error
      this.resetStats();
    }
  }
}

// Export default
export default {
  LRUCache,
  PersistentCache,
};

declare global {
  interface Window {
    localStorage: Storage;
  }
  var localStorage: Storage;
}
