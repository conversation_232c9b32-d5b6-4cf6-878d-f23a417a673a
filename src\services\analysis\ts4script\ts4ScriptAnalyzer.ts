/**
 * TS4Script Analyzer
 *
 * This module provides analysis for TS4Script files, which contain Python scripts
 * for Sims 4 mods.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { injectable, singleton } from '../../di/decorators.js';
import { DatabaseService } from '../../databaseService.js';
import { ContentSemanticAnalyzer } from '../semantic/contentSemanticAnalyzer.js';
import { ResourcePurposeAnalyzer } from '../semantic/resourcePurposeAnalyzer.js';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import * as ResourceTypes from '../../../constants/resourceTypes.js';
import { BytecodeParser } from './bytecode/bytecodeParser.js';
import { createHash } from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';

// Import enhanced TS4Script analysis components
import { TS4ScriptAnalysisResult, TS4ScriptModule, TS4ScriptClass, TS4ScriptFunction, TS4ScriptImport } from './types.js';
import { ModuleExtractor, TS4ScriptModuleFile } from './extraction/moduleExtractor.js';
import { PatternDetector } from './patterns/patternDetector.js';
import { BytecodeParser } from './bytecode/bytecodeParser.js';
import { ClassAnalyzer } from './analysis/classAnalyzer.js';
import { FunctionAnalyzer } from './analysis/functionAnalyzer.js';
import { ImportAnalyzer } from './analysis/importAnalyzer.js';
import { DependencyAnalyzer } from './analysis/dependencyAnalyzer.js';
import { TS4ScriptRepository } from './database/ts4ScriptRepository.js';
import { ScriptAnalyzer } from './decompilation/scriptAnalyzer.js';
import { EnhancedDependencyChainAnalyzer } from '../semantic/dependencyChainAnalyzer.js';

/**
 * TS4Script analyzer
 */
@singleton()
export class TS4ScriptAnalyzer {
    private logger: Logger;
    private contentSemanticAnalyzer: ContentSemanticAnalyzer;
    private resourcePurposeAnalyzer: ResourcePurposeAnalyzer;
    private moduleExtractor: ModuleExtractor;
    private patternDetector: PatternDetector;
    private bytecodeParser: BytecodeParser;
    private classAnalyzer: ClassAnalyzer;
    private functionAnalyzer: FunctionAnalyzer;
    private importAnalyzer: ImportAnalyzer;
    private dependencyAnalyzer: DependencyAnalyzer;
    private ts4ScriptRepository: TS4ScriptRepository;
    private scriptAnalyzer: ScriptAnalyzer;

    /**
     * Constructor
     * @param databaseService Database service
     * @param contentSemanticAnalyzer Content semantic analyzer
     * @param resourcePurposeAnalyzer Resource purpose analyzer
     * @param logger Logger instance
     */
    constructor(
        private databaseService: DatabaseService,
        contentSemanticAnalyzer?: ContentSemanticAnalyzer,
        resourcePurposeAnalyzer?: ResourcePurposeAnalyzer,
        logger?: Logger
    ) {
        this.logger = logger || new Logger('TS4ScriptAnalyzer');
        this.contentSemanticAnalyzer = contentSemanticAnalyzer || new ContentSemanticAnalyzer(this.logger);
        this.resourcePurposeAnalyzer = resourcePurposeAnalyzer || new ResourcePurposeAnalyzer(databaseService, undefined, undefined, undefined, undefined, this.logger);

        // Initialize enhanced components
        this.moduleExtractor = new ModuleExtractor(this.logger);
        this.patternDetector = new PatternDetector(this.logger);
        this.bytecodeParser = new BytecodeParser(this.logger);
        this.classAnalyzer = new ClassAnalyzer(this.logger);
        this.functionAnalyzer = new FunctionAnalyzer(this.logger);
        this.importAnalyzer = new ImportAnalyzer(this.logger);
        this.dependencyAnalyzer = new DependencyAnalyzer(this.logger);
        this.dependencyAnalyzer.setDatabaseService(databaseService);
        this.ts4ScriptRepository = new TS4ScriptRepository(databaseService, this.logger);
        this.scriptAnalyzer = new ScriptAnalyzer(this.logger);

        // Ensure the ResourcePurposeAnalyzer has a properly initialized DependencyChainAnalyzer
        if (this.resourcePurposeAnalyzer) {
            // Check if the resourcePurposeAnalyzer has a dependencyChainAnalyzer
            if (!this.resourcePurposeAnalyzer.getDependencyChainAnalyzer()) {
                // Create a new EnhancedDependencyChainAnalyzer and set it on the resourcePurposeAnalyzer
                const dependencyChainAnalyzer = new EnhancedDependencyChainAnalyzer(databaseService, this.logger);
                this.resourcePurposeAnalyzer.setDependencyChainAnalyzer(dependencyChainAnalyzer);
            }
        }
    }

    /**
     * Initialize analyzer
     */
    public async initialize(): Promise<void> {
        try {
            this.logger.info('Initializing TS4Script analyzer');

            // Initialize repository
            await this.ts4ScriptRepository.initialize();

            this.logger.info('TS4Script analyzer initialized');
        } catch (error) {
            this.logger.error('Error initializing TS4Script analyzer:', error);
            throw error;
        }
    }

    /**
     * Analyze a TS4Script file
     * @param filePath Path to the TS4Script file
     * @returns TS4Script analysis result
     */
    public async analyzeTS4Script(filePath: string): Promise<TS4ScriptAnalysisResult> {
        try {
            this.logger.info(`Analyzing TS4Script file: ${filePath}`);

            // Check if file exists
            await fs.access(filePath);

            // Get file stats
            const stats = await fs.stat(filePath);

            // Get file name
            const fileName = path.basename(filePath);

            // Save package info to database
            const packageId = await this.savePackageInfo(filePath, fileName, stats.size);

            // Extract Python modules
            const moduleFiles = await this.moduleExtractor.extractModules(filePath);
            this.logger.info(`Extracted ${moduleFiles.length} modules from ${filePath}`);

            // Create and analyze modules
            const modules: TS4ScriptModule[] = [];
            let totalClasses = 0;
            let totalFunctions = 0;
            let totalImports = 0;
            let hasInjections = false;
            let hasEventHandlers = false;
            let hasCommands = false;
            let hasTuningReferences = false;
            let modType = 'unknown';

            for (const moduleFile of moduleFiles) {
                // Create module
                const module = this.moduleExtractor.createModule(moduleFile, packageId);

                // Detect patterns
                const patterns = this.patternDetector.detectPatterns(module);

                // Update module metadata
                module.metadata.hasInjections = patterns.hasInjections;
                module.metadata.hasEventHandlers = patterns.hasEventHandlers;
                module.metadata.hasCommands = patterns.hasCommands;
                module.metadata.hasTuningReferences = patterns.hasTuningReferences;
                module.metadata.moduleType = patterns.moduleType;

                // Update overall statistics
                hasInjections = hasInjections || patterns.hasInjections;
                hasEventHandlers = hasEventHandlers || patterns.hasEventHandlers;
                hasCommands = hasCommands || patterns.hasCommands;
                hasTuningReferences = hasTuningReferences || patterns.hasTuningReferences;

                // Determine mod type based on patterns
                if (modType === 'unknown' && patterns.moduleType !== 'unknown') {
                    modType = patterns.moduleType;
                }

                // Save module to database
                const moduleId = await this.saveModuleToDatabase(module, packageId);
                module.id = moduleId;

                // Add to modules array
                modules.push(module);

                // Update counts
                totalClasses += module.classes.length;
                totalFunctions += module.functions.length;
                totalImports += module.imports.length;
            }

            // Analyze modules with resource purpose analyzer
            const analyzedModules = await this.analyzeModules(modules, packageId);

            // Determine mod category and complexity
            const modCategory = this.determineModCategory(analyzedModules, hasInjections, hasEventHandlers, hasCommands, hasTuningReferences);
            const modComplexity = this.determineModComplexity(analyzedModules, totalClasses, totalFunctions, totalImports);

            // Determine main module and entry points
            const mainModule = this.determineMainModule(analyzedModules);
            const entryPoints = this.determineEntryPoints(analyzedModules);

            // Save TS4Script package info
            await this.ts4ScriptRepository.saveTS4ScriptPackage(
                packageId,
                fileName,
                filePath,
                stats.size,
                modules.length,
                totalClasses,
                totalFunctions,
                totalImports,
                hasInjections,
                hasEventHandlers,
                hasCommands,
                hasTuningReferences,
                modType,
                modCategory,
                modComplexity
            );

            // Return result
            return {
                packageId,
                packageName: fileName,
                packagePath: filePath,
                modules: analyzedModules,
                totalClasses,
                totalFunctions,
                totalImports,
                hasInjections,
                hasEventHandlers,
                hasCommands,
                hasTuningReferences,
                modType,
                modCategory,
                modComplexity,
                mainModule,
                entryPoints,
                timestamp: Date.now()
            };
        } catch (error) {
            this.logger.error(`Error analyzing TS4Script file ${filePath}:`, error);
            throw error;
        }
    }

    /**
     * Save package info to database
     * @param filePath File path
     * @param fileName File name
     * @param fileSize File size
     * @returns Package ID
     */
    private async savePackageInfo(filePath: string, fileName: string, fileSize: number): Promise<number> {
        try {
            this.logger.debug(`Saving package info for ${filePath}`);

            // Use the packages repository to save the package
            const packageId = this.databaseService.packages.savePackage({
                name: fileName,
                path: filePath,
                hash: fileName, // Using filename as hash for simplicity
                size: fileSize,
                lastModified: Date.now()
            });

            this.logger.debug(`Package saved with ID ${packageId}`);
            return packageId;
        } catch (error) {
            this.logger.error(`Error saving package info for ${filePath}:`, error);
            throw error;
        }
    }

    /**
     * Save module to database
     * @param module Module to save
     * @param packageId Package ID
     * @returns Module ID
     */
    private async saveModuleToDatabase(module: TS4ScriptModule, packageId: number): Promise<number> {
        try {
            // Use the resources repository to save the module
            const resourceId = this.databaseService.resources.saveResource({
                packageId,
                type: ResourceTypes.RESOURCE_TYPE_SCRIPT,
                group: '0', // Group
                instance: Date.now().toString(), // Instance (use timestamp as unique ID)
                hash: module.name, // Use module name as hash
                size: module.content.length, // Size
                offset: 0, // Offset
                contentSnippet: module.contentSnippet,
                resourceType: 'TS4Script'
            });

            return resourceId;
        } catch (error) {
            this.logger.error(`Error saving module to database:`, error);
            throw error;
        }
    }

    /**
     * Analyze modules
     * @param modules Modules to analyze
     * @param packageId Package ID
     * @returns Analyzed modules
     */
    private async analyzeModules(modules: TS4ScriptModule[], packageId: number): Promise<TS4ScriptModule[]> {
        const analyzedModules: TS4ScriptModule[] = [];

        for (const module of modules) {
            try {
                this.logger.debug(`Analyzing module: ${module.name}`);

                // Parse bytecode
                const codeObject = this.bytecodeParser.parseBytecode(Buffer.from(module.content), module.name);

                if (codeObject) {
                    // Disassemble bytecode
                    const instructions = this.bytecodeParser.disassemble(codeObject);

                    // Analyze classes
                    const classes = this.classAnalyzer.analyzeClasses(codeObject, module.name, instructions);
                    module.classes = classes;

                    // Analyze functions
                    const functions = this.functionAnalyzer.analyzeFunctions(codeObject, module.name, instructions);
                    module.functions = functions;

                    // Analyze imports
                    const imports = this.importAnalyzer.analyzeImports(codeObject, module.name, instructions);
                    module.imports = imports;

                    // Analyze resource references
                    const resourceReferences = this.dependencyAnalyzer.analyzeResourceReferences(codeObject, module.name, instructions);

                    // Save resource references to database
                    if (module.id) {
                        await this.dependencyAnalyzer.saveResourceReferences(module.id, resourceReferences);
                    }

                    // Add bytecode version information
                    if (module.path.endsWith('.pyc')) {
                        // Try to get Python version from bytecode
                        const pythonVersion = this.bytecodeParser.getPythonVersion(Buffer.from(module.content));
                        if (pythonVersion !== 'Unknown') {
                            module.metadata.pythonVersion = pythonVersion;
                        }
                    }
                } else if (module.path.endsWith('.pyc')) {
                    // If standard parsing failed for a bytecode file, try our enhanced bytecode parser
                    this.logger.debug(`Standard bytecode parsing failed for ${module.name}, trying enhanced parser`);

                    try {
                        // Create a new bytecode parser instance
                        const enhancedParser = new BytecodeParser(this.logger);

                        // Try to parse with fallback strategies
                        const result = enhancedParser.parseBytecodeWithFallback(Buffer.from(module.content), module.name);

                        if (result.codeObject) {
                            this.logger.info(`Successfully parsed bytecode with enhanced parser (confidence: ${result.confidence})`);

                            // Try to decompile and analyze with our script analyzer
                            try {
                                const scriptAnalysisResult = await this.scriptAnalyzer.analyzeScript(
                                    result, // Pass the bytecode parse result
                                    module.name,
                                    {
                                        decompilationOptions: {
                                            cacheResults: true,
                                            timeout: 30000 // 30 seconds
                                        },
                                        astParseOptions: {
                                            analyzeEAPatterns: true,
                                            extractDocstrings: true
                                        }
                                    }
                                );

                                if (scriptAnalysisResult.success && scriptAnalysisResult.ast) {
                                    this.logger.info(`Successfully decompiled and analyzed ${module.name} (confidence: ${scriptAnalysisResult.confidence})`);

                                    // Extract classes from AST
                                    if (scriptAnalysisResult.ast.classes.length > 0) {
                                        module.classes = scriptAnalysisResult.ast.classes.map(cls => ({
                                            id: 0,
                                            name: cls.name,
                                            fullName: `${module.name}.${cls.name}`,
                                            parentClasses: cls.parentClasses,
                                            isEAClass: cls.isEAClass,
                                            methods: cls.methods.map(method => ({
                                                id: 0,
                                                name: method.name,
                                                fullName: `${module.name}.${cls.name}.${method.name}`,
                                                parameters: method.parameters,
                                                isMethod: true,
                                                isStaticMethod: method.isStaticMethod,
                                                isClassMethod: method.isClassMethod,
                                                isProperty: method.isProperty,
                                                isCommand: method.isCommand,
                                                isEventHandler: method.isEventHandler,
                                                isInjection: method.isInjection,
                                                decorators: method.decorators,
                                                docString: method.docstring || ''
                                            })),
                                            properties: cls.properties,
                                            docString: cls.docstring || ''
                                        }));
                                    }

                                    // Extract functions from AST
                                    if (scriptAnalysisResult.ast.functions.length > 0) {
                                        // Filter out methods that are already included in classes
                                        const nonMethodFunctions = scriptAnalysisResult.ast.functions.filter(func => !func.isMethod);

                                        module.functions = nonMethodFunctions.map(func => ({
                                            id: 0,
                                            name: func.name,
                                            fullName: `${module.name}.${func.name}`,
                                            parameters: func.parameters,
                                            isMethod: false,
                                            isStaticMethod: func.isStaticMethod,
                                            isClassMethod: func.isClassMethod,
                                            isProperty: func.isProperty,
                                            isCommand: func.isCommand,
                                            isEventHandler: func.isEventHandler,
                                            isInjection: func.isInjection,
                                            decorators: func.decorators,
                                            docString: func.docstring || ''
                                        }));
                                    }

                                    // Extract imports from AST
                                    if (scriptAnalysisResult.ast.imports.length > 0) {
                                        module.imports = scriptAnalysisResult.ast.imports.map(imp => {
                                            return {
                                                importedModule: imp.module,
                                                importedNames: [imp.name],
                                                isFromImport: imp.isFromImport,
                                                isEAModule: imp.module.startsWith('sims4') ||
                                                           imp.module.startsWith('services') ||
                                                           imp.module.startsWith('objects'),
                                                isRelativeImport: imp.module.startsWith('.'),
                                                importLevel: imp.module.startsWith('.') ?
                                                            imp.module.split('.').length - 1 : 0
                                            };
                                        });
                                    }

                                    // Update module metadata
                                    module.metadata.hasInjections = scriptAnalysisResult.ast.functions.some(func => func.isInjection);
                                    module.metadata.hasCommands = scriptAnalysisResult.ast.functions.some(func => func.isCommand);
                                    module.metadata.hasEventHandlers = scriptAnalysisResult.ast.functions.some(func => func.isEventHandler);
                                    module.metadata.hasTuningReferences = scriptAnalysisResult.ast.tuningReferences.length > 0;
                                    module.metadata.pythonVersion = scriptAnalysisResult.pythonVersion || module.metadata.pythonVersion;

                                    // Store decompiled source code
                                    if (scriptAnalysisResult.sourceCode) {
                                        module.metadata.decompiledSource = scriptAnalysisResult.sourceCode.substring(0, 1000); // Store first 1000 chars
                                        module.metadata.decompilerUsed = scriptAnalysisResult.decompilerUsed;
                                    }
                                } else {
                                    // Fallback to basic extraction if script analysis failed
                                    this.logger.warn(`Script analysis failed for ${module.name}: ${scriptAnalysisResult.error}`);
                                    this.extractBasicInformation(module, result, enhancedParser);
                                }
                            } catch (scriptError) {
                                this.logger.error(`Error in script analysis for ${module.name}:`, scriptError);
                                this.extractBasicInformation(module, result, enhancedParser);
                            }
                        } else {
                            this.logger.warn(`Enhanced bytecode parsing failed for ${module.name}: ${result.error}`);
                        }
                    } catch (error) {
                        this.logger.error(`Error using enhanced bytecode parser for ${module.name}:`, error);
                    }
                }

                // Create resource key
                const resourceKey = {
                    type: ResourceTypes.RESOURCE_TYPE_SCRIPT,
                    group: 0n, // BigInt for group
                    instance: module.id ? BigInt(module.id) : 0n // BigInt for instance, default to 0n if module.id is undefined
                };

                // Analyze purpose
                // Ensure module.id is defined before passing it to analyzeResourcePurpose
                const resourceId = module.id || 0;
                const purposeAnalysis = await this.resourcePurposeAnalyzer.analyzeResourcePurpose(
                    resourceKey,
                    resourceId,
                    module.metadata,
                    module.content
                );

                // Add purpose analysis to module
                module.purposeAnalysis = purposeAnalysis;

                analyzedModules.push(module);
            } catch (error) {
                this.logger.error(`Error analyzing module ${module.name}:`, error);
                analyzedModules.push(module);
            }
        }

        // Build dependency graph and find entry points
        try {
            const dependencyGraph = this.importAnalyzer.buildDependencyGraph(analyzedModules);
            const entryPoints = this.importAnalyzer.findEntryPoints(dependencyGraph);
            const mainModule = this.importAnalyzer.findMainModule(entryPoints, analyzedModules);

            // Update module metadata with entry point information
            for (const module of analyzedModules) {
                if (entryPoints.includes(module.name)) {
                    module.metadata.isEntryPoint = true;
                }

                if (module.name === mainModule) {
                    module.metadata.isMainModule = true;
                }
            }
        } catch (error) {
            this.logger.error(`Error building dependency graph:`, error);
        }

        return analyzedModules;
    }

    /**
     * Determine mod category
     * @param modules Analyzed modules
     * @param hasInjections Has injections
     * @param hasEventHandlers Has event handlers
     * @param hasCommands Has commands
     * @param hasTuningReferences Has tuning references
     * @returns Mod category
     */
    private determineModCategory(
        modules: TS4ScriptModule[],
        hasInjections: boolean,
        hasEventHandlers: boolean,
        hasCommands: boolean,
        hasTuningReferences: boolean
    ): string {
        // Check for service pattern
        const hasServicePattern = modules.some(module =>
            module.content.includes('class') &&
            module.content.includes('Service') &&
            module.content.includes('def __init__')
        );

        // Check for UI pattern
        const hasUIPattern = modules.some(module =>
            module.content.includes('ui.ui_dialog') ||
            module.content.includes('UiDialogOk') ||
            module.content.includes('UiDialogYesNo')
        );

        // Check for interaction pattern
        const hasInteractionPattern = modules.some(module =>
            module.content.includes('Interaction') &&
            module.content.includes('class') &&
            module.content.includes('def _run_interaction_gen')
        );

        // Determine category based on patterns
        if (hasServicePattern) {
            return 'service';
        } else if (hasUIPattern) {
            return 'ui';
        } else if (hasInteractionPattern) {
            return 'interaction';
        } else if (hasCommands) {
            return 'command';
        } else if (hasInjections) {
            return 'injection';
        } else if (hasEventHandlers) {
            return 'event_handler';
        } else if (hasTuningReferences) {
            return 'tuning';
        } else {
            return 'utility';
        }
    }

    /**
     * Determine mod complexity
     * @param modules Analyzed modules
     * @param totalClasses Total classes
     * @param totalFunctions Total functions
     * @param totalImports Total imports
     * @returns Mod complexity
     */
    private determineModComplexity(
        modules: TS4ScriptModule[],
        totalClasses: number,
        totalFunctions: number,
        totalImports: number
    ): string {
        // Calculate complexity score
        const complexityScore =
            (totalClasses * 3) +
            (totalFunctions * 2) +
            (totalImports * 1) +
            (modules.length * 5);

        // Determine complexity based on score
        if (complexityScore < 20) {
            return 'simple';
        } else if (complexityScore < 50) {
            return 'moderate';
        } else if (complexityScore < 100) {
            return 'complex';
        } else {
            return 'very_complex';
        }
    }

    /**
     * Determine main module
     * @param modules Analyzed modules
     * @returns Main module
     */
    private determineMainModule(modules: TS4ScriptModule[]): string | undefined {
        // Look for package init module
        const initModule = modules.find(module => module.metadata.isPackageInit && !module.metadata.isSubpackageInit);
        if (initModule) {
            return initModule.name;
        }

        // Look for module with most classes and functions
        let mainModule: TS4ScriptModule | undefined;
        let maxScore = -1;

        for (const module of modules) {
            const score = (module.classes.length * 3) + (module.functions.length * 2);
            if (score > maxScore) {
                maxScore = score;
                mainModule = module;
            }
        }

        return mainModule?.name;
    }

    /**
     * Determine entry points
     * @param modules Analyzed modules
     * @returns Entry points
     */
    private determineEntryPoints(modules: TS4ScriptModule[]): string[] {
        const entryPoints: string[] = [];

        // Look for modules with injections, commands, or event handlers
        for (const module of modules) {
            if (module.metadata.hasInjections || module.metadata.hasCommands || module.metadata.hasEventHandlers) {
                entryPoints.push(module.name);
            }
        }

        return entryPoints;
    }

    /**
     * Extract basic information from bytecode using the enhanced parser
     * This is a fallback method when script analysis fails
     * @param module Module to update
     * @param result Bytecode parse result
     * @param enhancedParser Enhanced bytecode parser
     */
    private extractBasicInformation(
        module: TS4ScriptModule,
        result: any,
        enhancedParser: BytecodeParser
    ): void {
        // Extract classes
        const classes = enhancedParser.extractClasses(result.codeObject);
        if (classes.length > 0) {
            module.classes = classes.map(className => ({
                id: 0,
                name: className,
                fullName: `${module.name}.${className}`,
                parentClasses: [],
                isEAClass: false,
                methods: [],
                properties: [],
                docString: ''
            }));
        }

        // Extract functions
        const functions = enhancedParser.extractFunctions(result.codeObject);
        if (functions.length > 0) {
            module.functions = functions.map(functionName => ({
                id: 0,
                name: functionName,
                fullName: `${module.name}.${functionName}`,
                parameters: [],
                isMethod: false,
                isStaticMethod: false,
                isClassMethod: false,
                isProperty: false,
                isCommand: functionName.includes('command') || functionName.includes('Command'),
                isEventHandler: functionName.includes('event') || functionName.includes('Event'),
                isInjection: functionName.includes('inject') || functionName.includes('patch'),
                decorators: [],
                docString: ''
            }));
        }

        // Extract imports
        const imports = enhancedParser.extractImports(result.codeObject);
        if (imports.length > 0) {
            module.imports = imports.map(importStr => {
                const isFromImport = importStr.startsWith('from ');
                let moduleName = '';
                let names: string[] = [];

                if (isFromImport) {
                    const match = importStr.match(/from\s+([^\s]+)\s+import\s+(.+)/);
                    if (match) {
                        moduleName = match[1];
                        names = match[2].split(',').map(name => name.trim());
                    }
                } else {
                    const match = importStr.match(/import\s+(.+)/);
                    if (match) {
                        moduleName = match[1];
                        names = [moduleName];
                    }
                }

                return {
                    importedModule: moduleName,
                    importedNames: names,
                    isFromImport,
                    isEAModule: moduleName.startsWith('sims4') || moduleName.startsWith('services') || moduleName.startsWith('objects'),
                    isRelativeImport: moduleName.startsWith('.'),
                    importLevel: moduleName.startsWith('.') ? moduleName.split('.').length - 1 : 0
                };
            });
        }

        // Update module metadata
        module.metadata.hasInjections = imports.some(imp => imp.includes('inject')) ||
                                      functions.some(func => func.includes('inject'));
        module.metadata.hasCommands = imports.some(imp => imp.includes('commands')) ||
                                    functions.some(func => func.includes('Command'));
        module.metadata.hasEventHandlers = imports.some(imp => imp.includes('event')) ||
                                         functions.some(func => func.includes('event'));
        module.metadata.hasTuningReferences = imports.some(imp => imp.includes('tuning')) ||
                                            functions.some(func => func.includes('tuning'));

        // Add bytecode version information
        const pythonVersion = enhancedParser.getPythonVersion(Buffer.from(module.content));
        if (pythonVersion !== 'Unknown') {
            module.metadata.pythonVersion = pythonVersion;
        }
    }
}
