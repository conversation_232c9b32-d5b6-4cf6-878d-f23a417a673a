/**
 * Error handling utilities for VFX extraction
 */

import { ResourceKey as AppResourceKey } from '../../../../../types/resource/interfaces.js';
import { Logger } from '../../../../../utils/logging/logger.js';
import { safeJsonStringify } from '../../../../../utils/json/safeJsonStringify.js';

const logger = new Logger('VfxExtractorError');

/**
 * Context information for VFX extraction errors
 */
export interface VfxExtractionErrorContext {
    resourceId?: number;
    resourceType?: number;
    resourceGroup?: bigint | number;
    resourceInstance?: bigint | number;
    operation?: string;
    extractorName?: string;
    bufferLength?: number;
    additionalInfo?: Record<string, any>;
}

/**
 * Creates an error context object for VFX extraction errors
 * @param key Resource key
 * @param resourceId Resource ID
 * @param extractorName Name of the extractor
 * @param additionalInfo Additional context information
 * @returns Error context object
 */
export function createVfxExtractionContext(
    key: AppResourceKey,
    resourceId: number,
    extractorName: string,
    additionalInfo?: Record<string, any>
): VfxExtractionErrorContext {
    return {
        resourceId,
        resourceType: key.type,
        resourceGroup: key.group,
        resourceInstance: key.instance,
        extractorName,
        additionalInfo
    };
}

/**
 * Handles a VFX extraction error with context
 * @param error Error that occurred
 * @param context Error context
 * @param fallbackValue Optional fallback value to return
 * @returns Fallback value or undefined
 */
export function handleVfxExtractionError<T>(
    error: any,
    context: VfxExtractionErrorContext,
    fallbackValue?: T
): T | undefined {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const contextStr = safeJsonStringify(context);
    
    logger.error(`VFX extraction error: ${errorMessage}. Context: ${contextStr}`);
    
    return fallbackValue;
}

/**
 * Wraps a function with error handling for VFX extraction
 * @param fn Function to wrap
 * @param getContext Function to get error context
 * @param fallbackValue Optional fallback value to return on error
 * @returns Wrapped function with error handling
 */
export function withVfxErrorHandling<T, Args extends any[]>(
    fn: (...args: Args) => T,
    getContext: (...args: Args) => VfxExtractionErrorContext,
    fallbackValue?: T
): (...args: Args) => T | undefined {
    return (...args: Args) => {
        try {
            return fn(...args);
        } catch (error) {
            const context = getContext(...args);
            return handleVfxExtractionError(error, context, fallbackValue);
        }
    };
}

/**
 * Wraps an async function with error handling for VFX extraction
 * @param fn Async function to wrap
 * @param getContext Function to get error context
 * @param fallbackValue Optional fallback value to return on error
 * @returns Wrapped async function with error handling
 */
export function withAsyncVfxErrorHandling<T, Args extends any[]>(
    fn: (...args: Args) => Promise<T>,
    getContext: (...args: Args) => VfxExtractionErrorContext,
    fallbackValue?: T
): (...args: Args) => Promise<T | undefined> {
    return async (...args: Args) => {
        try {
            return await fn(...args);
        } catch (error) {
            const context = getContext(...args);
            return handleVfxExtractionError(error, context, fallbackValue);
        }
    };
}

/**
 * Higher-order function that wraps a synchronous function with error handling
 * @param fn The function to wrap
 * @param key The resource key
 * @param resourceId The resource ID
 * @param extractorName The name of the extractor
 * @returns The wrapped function
 */
export function withVfxExtractionErrorHandling<T, Args extends any[]>(
    fn: (...args: Args) => T,
    key: AppResourceKey,
    resourceId: number,
    extractorName: string
): (...args: Args) => T | null {
    return (...args: Args) => {
        try {
            return fn(...args);
        } catch (error: any) {
            const context = createVfxExtractionContext(key, resourceId, extractorName, {
                operation: fn.name || 'unknown',
                error: error.message || String(error)
            });
            
            handleVfxExtractionError(error, context);
            return null;
        }
    };
}

/**
 * Higher-order function that wraps an async function with error handling
 * @param fn The function to wrap
 * @param key The resource key
 * @param resourceId The resource ID
 * @param extractorName The name of the extractor
 * @returns The wrapped function
 */
export function withAsyncVfxExtractionErrorHandling<T, Args extends any[]>(
    fn: (...args: Args) => Promise<T>,
    key: AppResourceKey,
    resourceId: number,
    extractorName: string
): (...args: Args) => Promise<T | null> {
    return async (...args: Args) => {
        try {
            return await fn(...args);
        } catch (error: any) {
            const context = createVfxExtractionContext(key, resourceId, extractorName, {
                operation: fn.name || 'unknown',
                error: error.message || String(error)
            });
            
            handleVfxExtractionError(error, context);
            return null;
        }
    };
}
