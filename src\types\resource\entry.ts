﻿// Corrected imports
import { Resource<PERSON><PERSON>, ResourceContent } from './interfaces.js';

export interface ResourceEntry {
  key: ResourceKey;
  content: ResourceContent;
  size: number;
  hash: string;
  timestamp: number;
  version?: string;
  source?: string;
  isCompressed?: boolean;
  isEncrypted?: boolean;
  isModified?: boolean;
  modifiedDate?: Date;
  originalPath?: string;
  originalHash?: string;
  originalSize?: number;
  originalTimestamp?: number;
  originalVersion?: string;
  originalDependencies?: ResourceKey[];
  originalConflicts?: ResourceKey[];
  customData?: {
    [key: string]: unknown;
  };
}
