/**
 * Core interfaces for representing resources and analysis results.
 */

import { ResourceCategory } from './enums.js'; // Import ResourceCategory

/**
 * Resource key interface - canonical definition aligning with S4TK's implementation
 * This is the single source of truth for ResourceKey in the application
 */
export interface ResourceKey {
  type: number;     // Resource type ID (number to match S4TK)
  group: bigint;    // Resource group ID (bigint to match S4TK)
  instance: bigint; // Resource instance ID (bigint for precision with large S4TK instances)
  name?: string;    // Optional name derived elsewhere
  path?: string;    // Optional path where the resource resides
  id?: string;      // Optional unique identifier (could be instance as string)
}

/**
 * Resource metadata interface
 */
export interface ResourceMetadata {
  name: string;
  path: string; // Ensure path is always present here
  size: number;
  hash: string;
  timestamp: number;
  author?: string;
  description?: string;
  version?: string;
  dependencies?: ResourceKey[]; // Dependencies identified during analysis
  conflicts?: any[]; // Placeholder for conflicts related to this resource
  tags?: string[];
  customData?: Record<string, unknown>;
  source?: string; // e.g., package file name
  contentSnippet?: string; // Optional snippet of resource content for preview/analysis
  instance?: bigint; // Use bigint for precision
  resourceType?: string; // Added: Type of resource (e.g., 'Tuning', 'SimData', 'Casp')
  resourceTypeHex?: string; // Added: Hex representation of the resource type
  resourceDescription?: string; // Added: Description of the resource type
  isOfficialType?: boolean; // Added: Whether this is an official S4TK resource type
  tuningName?: string; // Added: Extracted tuning name for Tuning resources
  tuningType?: string; // Type of tuning (Trait, Buff, etc.)
  tuningCategory?: string; // Category of tuning (Sim Attributes, Interactions, etc.)
  tuningDescription?: string; // Description of tuning
  tuningTags?: string; // JSON string of tuning tags
  tuningDependencyCount?: number; // Number of dependencies found in tuning
  tuningModuleCount?: number; // Number of modules in tuning
  tuningInstanceCount?: number; // Number of instances in tuning

  // Enhanced Tuning XML fields
  tuningClass?: string; // Tuning class name
  tuningInstance?: string; // Tuning instance ID
  tuningParentClass?: string; // Parent class of this tuning
  tuningClassHierarchy?: string; // JSON string of class hierarchy
  tuningClassHierarchyDepth?: number; // Depth of class hierarchy
  tuningIsOverride?: boolean; // Whether this tuning is an override
  tuningOverrideTarget?: string; // Target of override (if applicable)
  tuningOverrideElements?: string; // JSON string of elements being overridden
  tuningOverrideIntent?: string; // Intent of override (fix, enhance, replace)
  tuningCriticalParameters?: string; // JSON string of critical gameplay parameters
  tuningParameterReferences?: string; // JSON string of parameter references
  tuningRelationshipGraph?: string; // JSON string of relationship graph
  tuningClassificationConfidence?: number; // Confidence level of classification (0-100)
  tuningComplexityScore?: number; // Complexity score (0-100)
  tuningSemanticSystems?: string; // Comma-separated list of semantic systems
  tuningSemanticCategory?: string; // Semantic category
  tuningSemanticIsOfficial?: boolean; // Whether this is an official tuning
  tuningPotentialConflictRisk?: string; // Risk of conflicts (Low, Medium, High)
  tuningImportantValues?: string; // JSON string of important gameplay values
  tuningOverrideNodes?: string; // JSON string of override nodes
  tuningModuleReferences?: string; // JSON string of module references
  extractorUsed?: string; // Added: Name of the extractor used to extract metadata
  extractionError?: string; // Added: Error message if extraction failed

  // Content-addressable storage fields
  contentPath?: string; // Path to content in content-addressable storage
  contentSize?: number; // Size of content in content-addressable storage
  signatureHash?: string; // Hash used for similarity detection

  // Fields for specific resource types

  // Object Definition fields
  catalogNameKey?: bigint; // For Object Definitions (STBL key)
  catalogPrice?: number; // For Object Definitions
  catalogTags?: string; // JSON string of catalog tags
  catalogSwatchColors?: string; // JSON string of catalog swatch colors
  objectTuningName?: string; // Added: Tuning name for Object Definitions
  objectNameKey?: bigint; // Added: Name key for Object Definitions
  objectTuningRefTGI?: string; // Added: Tuning reference TGI for Object Definitions
  objectFootprintTGI?: string; // Added: Footprint TGI for Object Definitions
  footprintWidth?: number; // Added: Width of footprint
  footprintHeight?: number; // Added: Height of footprint
  buildPartVersion?: number; // Added: Version of build part

  // CAS Part fields
  casBodyTypeRaw?: number;
  casAgeGenderFlagsRaw?: number;
  casFlagsRaw?: { a: number; b: number }[]; // Raw flag pairs (consider removing if only storing JSON)
  casFlagsRawJson?: string; // Added: JSON string representation of casFlagsRaw
  casSwatchColors?: string[]; // Parsed swatch colors

  // Script fields
  scriptType?: string; // Type of script (e.g., 'Python')
  scriptSize?: number; // Size of script in bytes
  scriptClasses?: string; // JSON string of class information
  scriptClassCount?: number; // Number of classes in script
  scriptFunctions?: string; // JSON string of function information
  scriptFunctionCount?: number; // Number of functions in script
  scriptImports?: string; // JSON string of import statements
  scriptImportCount?: number; // Number of import statements
  scriptSims4Modules?: string; // JSON string of Sims4 modules imported
  scriptTuningIds?: string; // JSON string of tuning IDs referenced in script
  scriptCommands?: string; // JSON string of commands defined in script
  scriptCommandCount?: number; // Number of commands in script
  scriptDecorators?: string; // JSON string of decorators used in script
  scriptDecoratorCount?: number; // Number of decorators in script
  scriptInjections?: string; // JSON string of injections in script
  scriptInjectionCount?: number; // Number of injections in script
  scriptEventHandlers?: string; // JSON string of event handlers in script
  scriptEventHandlerCount?: number; // Number of event handlers in script
  scriptTuningManagers?: string; // JSON string of tuning managers used in script
  scriptGlobalVariables?: string; // JSON string of global variables
  scriptGlobalVariableCount?: number; // Number of global variables
  scriptDocstrings?: string; // JSON string of docstrings
  scriptDocstringCount?: number; // Number of docstrings
  scriptResourceKeys?: string; // JSON string of ResourceKey instances
  scriptResourceKeyCount?: number; // Number of ResourceKey instances
  scriptTuningReferences?: string; // JSON string of TunableReference managers
  scriptTuningReferenceCount?: number; // Number of TunableReference managers
  scriptRegions?: string; // JSON string of code regions
  scriptRegionCount?: number; // Number of code regions
  modType?: string; // Type of mod (e.g., 'Interaction Mod', 'Buff Mod')
  modCategory?: string; // Category of mod (e.g., 'Gameplay', 'UI')
  modComplexity?: string; // Complexity of mod (e.g., 'Low', 'Medium', 'High')
  potentialConflictRisk?: string; // Risk of conflicts (e.g., 'Low', 'Medium', 'High')

  // SimData fields
  simDataSchemaName?: string; // Name of SimData schema
  simDataSchemaId?: number; // ID of SimData schema
  simDataSchemaHash?: string; // Hash of SimData schema
  simDataColumnCount?: number; // Number of columns in SimData schema
  simDataColumnTypes?: string; // JSON string of column types
  simDataColumnNames?: string; // Comma-separated list of column names
  simDataKeyColumns?: string; // Comma-separated list of key column names
  simDataInstanceCount?: number; // Number of instances in SimData
  simDataInstanceName?: string; // Name of first SimData instance
  simDataInstanceId?: number; // ID of first SimData instance
  simDataSampleValues?: string; // JSON string of sample values from first instance
  simDataMostCommonInstanceName?: string; // Most common instance name
  simDataMostCommonInstanceCount?: number; // Count of most common instance name
  simDataDependencyCount?: number; // Number of dependencies found in SimData
  simDataSemanticType?: string; // Semantic type of SimData (Trait, Buff, etc.)
  simDataSchemaVersion?: number; // Version of the SimData schema
  simDataSchemaFlags?: number; // Flags of the SimData schema
  simDataColumnCategories?: string; // JSON string of column categories (key, data, reference, etc.)
  simDataValueRanges?: string; // JSON string of value ranges for numeric columns
  simDataValuePatterns?: string; // JSON string of value patterns for string columns
  simDataInstanceSummary?: string; // JSON string summarizing all instances
  simDataRelatedTuningId?: string; // ID of related tuning XML resource
  simDataRelationshipType?: string; // Type of relationship with other resources
  simDataSchemaInheritance?: string; // JSON string of schema inheritance information
  simDataCriticalGameplayValues?: string; // JSON string of critical gameplay values
  simDataAnalysisConfidence?: number; // Confidence level of the analysis (0-100)
  simDataComplexityScore?: number; // Complexity score of the SimData (0-100)

  // Enhanced SimData Schema Analysis fields
  simDataSchemaCategory?: string; // Category of the schema (Sim, Object, Gameplay, etc.)
  simDataSchemaComplexity?: number; // Complexity score of the schema (0-100)
  simDataSchemaPurpose?: string; // Purpose of the schema
  simDataGameplaySystem?: string; // Gameplay system the schema belongs to
  simDataSchemaParent?: string; // Parent schema name if inheriting
  simDataSchemaChildren?: string; // JSON string of child schema names
  simDataColumnSemantics?: string; // JSON string of column semantic information
  simDataCriticalColumns?: string; // JSON string of critical columns
  simDataReferenceColumns?: string; // JSON string of reference columns
  simDataPotentialConflicts?: string; // JSON string of potential conflicts with other schemas
  simDataSchemaRelationships?: string; // JSON string of relationships with other schemas
  simDataSchemaUsage?: string; // Common usage patterns of this schema
  simDataSchemaModCount?: number; // Number of mods using this schema

  // String Table fields
  stblEntryCount?: number; // Number of entries in string table
  stblLocale?: string; // Locale name of string table
  stblLocaleId?: number; // Locale ID of string table
  stblAvgStringLength?: number; // Average length of strings in string table
  stblMaxStringLength?: number; // Maximum length of strings in string table
  stblMinStringLength?: number; // Minimum length of strings in string table
  stblPlaceholderCount?: number; // Number of placeholders in string table
  stblSampleEntries?: string; // JSON string of sample entries
  stblUIStringCount?: number; // Number of UI strings in string table
  stblInteractionStringCount?: number; // Number of interaction strings in string table
  stblDescriptionStringCount?: number; // Number of description strings in string table
  stblUIStrings?: string; // JSON string of UI strings
  stblInteractionStrings?: string; // JSON string of interaction strings
  stblDescriptionStrings?: string; // JSON string of description strings
  stblTotalEntries?: number; // Total number of entries in string table (including those not stored)

  // Image fields
  imageType?: string; // Type of image (e.g., 'DDS', 'PNG')
  imageWidth?: number; // Width of image in pixels
  imageHeight?: number; // Height of image in pixels
  imageFormat?: string; // Format of image (e.g., 'DXT1', 'RGBA')
  imageHasAlpha?: boolean; // Whether image has alpha channel
  imageMipMapCount?: number; // Number of mipmaps in image
  imageCompression?: string; // Compression format of image
  imageBitsPerPixel?: number; // Bits per pixel in image

  // Sound fields
  soundFormat?: string;       // Format identifier (e.g., "RIFF", "OGG", "MP3", "SNR", "SNS")
  soundSampleRate?: number;   // Sample rate in Hz
  soundChannels?: number;     // Number of audio channels
  soundBitDepth?: number;     // Bit depth (8, 16, 24, 32)
  soundDuration?: number;     // Duration in seconds (if available)
  soundDataSize?: number;     // Size of audio data
  soundLoopStart?: number;    // Loop start point (samples)
  soundLoopEnd?: number;      // Loop end point (samples)
  soundType?: string;         // Type of audio (music, voice, effect, ambience, ui, instrument)
  soundCodec?: string;        // Audio codec (XAS, EALayer3, PCM, MP3, OGG)
  soundIsCompressed?: boolean; // Whether the audio is compressed
  soundVersion?: number;      // Version of the sound format
  soundFlags?: number;        // Flags for the sound format
  soundHeaderSize?: number;   // Size of the sound header

  // Sound Bank fields
  soundBankVersion?: number;  // Version of the sound bank
  soundBankEntryCount?: number; // Number of entries in the sound bank
  soundBankEntries?: string;  // JSON string of sound bank entries

  // Model fields
  modelFormat?: string; // Format of model (e.g., 'GEOM', 'MLOD')
  modelVersion?: number; // Version of model format
  modelVertexCount?: number; // Number of vertices in model
  modelFaceCount?: number; // Number of faces in model
  modelBoneCount?: number; // Number of bones in model
  modelLodCount?: number; // Number of LOD levels in model
  modelMaterialCount?: number; // Number of materials in model
  modelFlags?: number; // Model flags
  modelHasUVs?: boolean; // Whether model has UV coordinates
  modelHasBones?: boolean; // Whether model has bone weights
  modelHasNormals?: boolean; // Whether model has normal vectors
  modelHasTangents?: boolean; // Whether model has tangent vectors
  modelVertexFormat?: string; // Description of vertex format

  // Animation fields
  animationFormat?: string; // Format of animation (e.g., 'ANIM', 'CLIP', 'CLAF')
  animationVersion?: number; // Version of animation format
  animationFrameCount?: number; // Number of frames in animation
  animationDuration?: number; // Duration of animation in seconds
  animationFlags?: number; // Animation flags
  animationBoneCount?: number; // Number of bones in animation
  animationTrackCount?: number; // Number of tracks in animation
  animationFps?: number; // Frames per second in animation
  animationLooping?: boolean; // Whether animation loops
  animationPriority?: number; // Animation priority
  animationBlendInTime?: number; // Blend-in time in seconds
  animationBlendOutTime?: number; // Blend-out time in seconds
  animationClipNames?: string; // JSON string of clip names

  // Footprint fields
  footprintVersion?: number; // Version of footprint
  footprintFlags?: number; // Footprint flags
  footprintIsMultiTile?: boolean; // Whether footprint is multi-tile
  footprintIsSlotted?: boolean; // Whether footprint has slots
  footprintTileCount?: number; // Number of tiles in footprint
  footprintTilePositions?: string; // JSON string of tile positions

  // Slot fields
  slotVersion?: number; // Version of slot
  slotFlags?: number; // Slot flags
  slotIsDecorative?: boolean; // Whether slot is decorative
  slotIsUserFacing?: boolean; // Whether slot is user-facing
  slotPreventSiblingIntersection?: boolean; // Whether slot prevents sibling intersection
  slotCount?: number; // Number of slots
  slotTypeId?: number; // Slot type ID
  slotPositions?: string; // JSON string of slot positions
  slotSearchRadius?: number; // Search radius for slot

  // Light fields
  lightVersion?: number; // Version of light
  lightFlags?: number; // Light flags
  lightIsElectric?: boolean; // Whether light is electric
  lightIsManual?: boolean; // Whether light is manual
  lightHasVisualEffect?: boolean; // Whether light has visual effect
  lightDefaultDimmerValue?: number; // Default dimmer value
  lightColor?: string; // Light color (RGBA)
  lightMaterialStateOn?: string; // Material state when light is on
  lightMaterialStateOff?: string; // Material state when light is off
  lightVisualEffect?: string; // Visual effect reference

  // Terrain Paint fields
  terrainPaintVersion?: number; // Version of terrain paint
  terrainPaintFlags?: number; // Terrain paint flags
  terrainPaintIsSeasonalVariant?: boolean; // Whether terrain paint is seasonal variant
  terrainPaintIsWaterVariant?: boolean; // Whether terrain paint is water variant
  terrainPaintIsSnowVariant?: boolean; // Whether terrain paint is snow variant
  terrainPaintTextureCount?: number; // Number of textures in terrain paint
  terrainPaintTextures?: string; // JSON string of texture references
  terrainPaintTagCount?: number; // Number of tags in terrain paint
  terrainPaintTags?: string; // JSON string of tags
  terrainPaintMaterialFlags?: number; // Material flags
  terrainPaintHasRoughness?: boolean; // Whether terrain paint has roughness
  terrainPaintHasSpecular?: boolean; // Whether terrain paint has specular
  terrainPaintHasNormal?: boolean; // Whether terrain paint has normal

  // Modular Part fields
  modularPartSize?: number; // Size of modular part in bytes
  modularPartType?: string; // Type of modular part (e.g., 'CAS', 'Build Mode')
  modularPartCategory?: string; // Category of modular part (e.g., 'CAS Hair', 'Sectional Sofa')
  modularPartReferenceCount?: number; // Number of references to other resources
  modularPartSlotCount?: number; // Number of slots in modular part
  modularPartHasAdjacencyInfo?: boolean; // Whether modular part has adjacency information
  modularPartHasPartData?: boolean; // Whether modular part has part data

  // VFX fields
  vfxFormat?: string; // Format of the VFX (e.g., 'VFX_MODIFIER', 'VFX_STATE')
  vfxVersion?: number; // Version of the VFX format
  vfxFlags?: number; // Flags for the VFX
  vfxEffectType?: string; // Type of effect (e.g., 'PARTICLE', 'METAPARTICLE')
  vfxParameterCount?: number; // Number of parameters in the VFX
  vfxAnimationCount?: number; // Number of animations in the VFX
  vfxParticleCount?: number; // Number of particles in the VFX
  vfxParameters?: string; // JSON string of parameters
  vfxAnimationReferences?: string; // JSON string of animation references
  vfxParticleSystems?: string; // JSON string of particle systems
  vfxTimingInfo?: string; // JSON string of timing information
  vfxStateIndex?: number; // State index for VFX_STATE resources
  vfxMaskFlags?: number; // Mask flags for the VFX
  vfxMaskNames?: string; // Human-readable mask names
  vfxTargetJoint?: string; // Target joint for the VFX
  vfxLifetimeType?: string; // Lifetime type (e.g., 'ONE_SHOT', 'INTERACTION', 'CONTINUATION', 'ANIMATION_EVENT')
  vfxDrawMode?: number; // Draw mode for the VFX
  vfxDrawFlags?: number; // Draw flags for the VFX
  vfxTileCountU?: number; // Tile count in U direction for animated textures
  vfxTileCountV?: number; // Tile count in V direction for animated textures
  vfxFrameSpeed?: number; // Animation speed in frames per second
  vfxFrameStart?: number; // Starting frame for animation
  vfxDependencyCount?: number; // Number of dependencies in the VFX
  vfxTextureReference?: string; // Reference to texture used by the VFX
  vfxSoundReference?: string; // Reference to sound used by the VFX
}

/**
 * Combines ResourceKey and ResourceMetadata for convenience
 * This is used in the resource processing pipeline
 * @deprecated Use ResourceInfo from types/database.ts instead for conflict detection
 */
export interface ResourceKeyMetadataPair {
  key: ResourceKey;
  metadata: ResourceMetadata;
  buffer?: Buffer; // Optional buffer containing the resource data
}

export interface ResourceContent {
  data: unknown;
  format: string;
  encoding?: string;
}

// Represents a fully analyzed resource within our system
export interface Resource {
  key: ResourceKey;
  metadata: ResourceMetadata;
  // content?: Buffer; // Avoid storing large content buffers directly if possible
  timestamp: number; // Analysis timestamp
  // Add other analysis-specific fields if needed
  validationResult?: ResourceValidationResult;
}

export interface ResourceValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  timestamp: number;
  // Add more validation details if needed
}

// Result structure for analyzing a single package or file
export interface ResourceAnalysisResult {
  // key: ResourceKey; // Maybe analysis result is for a package, not single resource
  packageName: string;
  packagePath: string;
  validation: ResourceValidationResult;
  metrics: {
    resourceCount: number;
    totalSize: number;
    // Add more metrics
  };
  recommendations: string[];
  resources: Resource[]; // List of resources found and analyzed within the package
  conflicts: any[]; // Placeholder for conflicts found within/between packages
  timestamp: number;
  metadata?: { // Package-level metadata
    packageVersion?: string;
    packageAuthor?: string;
  };
}
