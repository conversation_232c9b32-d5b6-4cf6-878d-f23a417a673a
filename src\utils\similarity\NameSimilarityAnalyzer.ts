﻿import { SIMILARITY_CONSTANTS } from '../../constants/similarity.js';

const {
  SIMILARITY_THRESHOLD,
  NAME_SIMILARITY_THRESHOLD,
  VERSION_SIMILARITY_THRESHOLD,
  COMMON_VARIANTS
} = SIMILARITY_CONSTANTS;

interface SimilarityResult {
  score: number;
  isMatch: boolean;
  details: {
    exactMatch: boolean;
    variantMatch: boolean;
    levenshteinDistance: number;
    normalizedScore: number;
  };
}

export class NameSimilarityAnalyzer {
  private static instance: NameSimilarityAnalyzer;

  private constructor() {}

  public static getInstance(): NameSimilarityAnalyzer {
    if (!NameSimilarityAnalyzer.instance) {
      NameSimilarityAnalyzer.instance = new NameSimilarityAnalyzer();
    }
    return NameSimilarityAnalyzer.instance;
  }

  private calculateLevenshteinDistance(str1: string, str2: string): number {
    const m = str1.length;
    const n = str2.length;
    const dp: number[][] = Array(m + 1)
      .fill(0)
      .map(() => Array(n + 1).fill(0));

    for (let i = 0; i <= m; i++) dp[i][0] = i;
    for (let j = 0; j <= n; j++) dp[0][j] = j;

    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1];
        } else {
          dp[i][j] = 1 + Math.min(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]);
        }
      }
    }

    return dp[m][n];
  }

  private normalizeString(str: string): string {
    return str.toLowerCase().replace(/[^a-z0-9]/g, '');
  }

  private checkVariantMatch(name1: string, name2: string): boolean {
    return COMMON_VARIANTS.some(variant => {
      const normalizedName1 = this.normalizeString(name1);
      const normalizedName2 = this.normalizeString(name2);

      return variant.variants.some(v =>
        normalizedName1.includes(v) && normalizedName2.includes(v)
      );
    });
  }

  public analyzeSimilarity(name1: string, name2: string): SimilarityResult {
    const normalized1 = this.normalizeString(name1);
    const normalized2 = this.normalizeString(name2);

    const exactMatch = normalized1 === normalized2;
    const variantMatch = this.checkVariantMatch(name1, name2);
    const levenshteinDistance = this.calculateLevenshteinDistance(normalized1, normalized2);
    
    const maxLength = Math.max(normalized1.length, normalized2.length);
    const normalizedScore = maxLength > 0 ? 1 - levenshteinDistance / maxLength : 0;

    const score = exactMatch ? 1 : variantMatch ? 0.9 : normalizedScore;
    const isMatch = score >= NAME_SIMILARITY_THRESHOLD;

    return {
      score,
      isMatch,
      details: {
        exactMatch,
        variantMatch,
        levenshteinDistance,
        normalizedScore
      }
    };
  }

  public findSimilarNames(targetName: string, nameList: string[]): string[] {
    const results = nameList
      .map(name => ({
        name,
        similarity: this.analyzeSimilarity(targetName, name)
      }))
      .filter(result => result.similarity.isMatch)
      .sort((a, b) => b.similarity.score - a.similarity.score);

    return results.map(result => result.name);
  }
} 

