/**
 * Database utilities for image extraction
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../../../services/databaseService.js';
import { DependencyInfo } from '../../../../../types/database.js';
import { ImageHeaderInfo } from '../types.js';
import { convertToDatabaseMetadata } from '../utils/metadataConverter.js';

// Create a logger instance
const log = new Logger('ImageDatabaseUtils');

/**
 * Saves image metadata to the database
 * @param header Image header information
 * @param resourceId Resource ID
 * @param databaseService Database service
 * @returns Whether the operation was successful
 */
export async function saveImageMetadata(
    header: ImageHeaderInfo,
    resourceId: number,
    databaseService: DatabaseService
): Promise<boolean> {
    try {
        // Convert header to database metadata
        const metadata = convertToDatabaseMetadata(header, resourceId);
        
        // Save to parsed content table
        await databaseService.parsedContent.saveParsedContent({
            resourceId,
            contentType: 'image_metadata',
            content: JSON.stringify(metadata)
        });
        
        log.debug(`Saved image metadata for resource ${resourceId}`);
        return true;
    } catch (error: any) {
        log.error(`Failed to save image metadata for resource ${resourceId}: ${error.message || error}`);
        return false;
    }
}

/**
 * Saves image dependencies to the database
 * @param dependencies Dependencies to save
 * @param resourceId Resource ID
 * @param databaseService Database service
 * @returns Whether the operation was successful
 */
export async function saveImageDependencies(
    dependencies: DependencyInfo[],
    resourceId: number,
    databaseService: DatabaseService
): Promise<boolean> {
    try {
        if (dependencies.length === 0) {
            log.debug(`No dependencies to save for resource ${resourceId}`);
            return true;
        }
        
        // Save each dependency
        for (const dependency of dependencies) {
            await databaseService.dependencies.saveDependency(dependency);
        }
        
        log.debug(`Saved ${dependencies.length} dependencies for resource ${resourceId}`);
        return true;
    } catch (error: any) {
        log.error(`Failed to save dependencies for resource ${resourceId}: ${error.message || error}`);
        return false;
    }
}
