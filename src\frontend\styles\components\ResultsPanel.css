.results-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1.5rem;
  background: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-container h3 {
  margin: 0 0 1rem;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

/* Conflicts Section */
.conflicts-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.conflict-item {
  padding: 1rem;
  border-radius: 6px;
  background: var(--surface-color-alt);
  border-left: 4px solid var(--border-color);
  transition: all 0.2s ease;
}

.conflict-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conflict-item.severity-high {
  border-left-color: var(--error-color);
}

.conflict-item.severity-medium {
  border-left-color: var(--warning-color);
}

.conflict-item.severity-low {
  border-left-color: var(--info-color);
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.conflict-type {
  font-weight: 600;
  color: var(--text-primary);
}

.conflict-severity {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
}

.severity-high .conflict-severity {
  background: var(--error-color-light);
  color: var(--error-color);
}

.severity-medium .conflict-severity {
  background: var(--warning-color-light);
  color: var(--warning-color);
}

.severity-low .conflict-severity {
  background: var(--info-color-light);
  color: var(--info-color);
}

.conflict-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.conflict-resources {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: var(--surface-color);
  border-radius: 4px;
  font-size: 0.875rem;
}

.resource-type {
  color: var(--text-secondary);
}

.resource-id {
  color: var(--text-primary);
  font-family: monospace;
}

.conflict-recommendations {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.conflict-recommendations h4 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
  font-size: 1rem;
}

.conflict-recommendations ul {
  margin: 0;
  padding-left: 1.5rem;
  color: var(--text-secondary);
}

/* Recommendations Section */
.recommendations-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--surface-color-alt);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.recommendation-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recommendation-item i {
  color: var(--primary-color);
  font-size: 1.25rem;
  margin-top: 0.25rem;
}

.recommendation-item span {
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Metrics Section */
.metrics-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-item {
  padding: 1rem;
  background: var(--surface-color-alt);
  border-radius: 6px;
  text-align: center;
}

.metric-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.metric-value {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .results-container {
    padding: 1rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .conflict-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .conflict-severity {
    align-self: flex-start;
  }
} 