/**
 * Intelligent Conflict Detector - Phase 2: Intelligent Conflict Detection
 * 
 * This is the main orchestrator for intelligent conflict detection, using
 * specialized detectors and gameplay impact analysis to provide accurate
 * conflict assessment with minimal false positives.
 * 
 * Implements the intelligent conflict detection system based on game logic
 * patterns discovered in our comprehensive Sims 4 asset analysis.
 */

import { OfficialResourceType, ResourceCategory } from '../../../types/resource/OfficialResourceTypes.js';
import { getResourceMetadata } from '../../../types/resource/ResourceMetadataRegistry.js';
import { 
  GameplayImpactAnalyzer, 
  ConflictSeverity, 
  GameplayImpact, 
  ConflictAnalysisResult,
  ResourceConflictContext 
} from './GameplayImpactAnalyzer.js';
import { TraitConflictDetector, TraitConflictContext } from './TraitConflictDetector.js';
import { BuffConflictDetector, BuffConflictContext } from './BuffConflictDetector.js';

/**
 * Enhanced Conflict Detection Result
 */
export interface IntelligentConflictResult extends ConflictAnalysisResult {
  conflictId: string;
  detectorUsed: string;
  resourceType: OfficialResourceType;
  resourceCategory: ResourceCategory;
  packageNames: string[];
  resourceIds: string[];
  detectionTimestamp: Date;
  gameplaySystemsAffected: string[];
  userActionRequired: boolean;
  priorityLevel: number; // 1-5, 5 being highest priority
}

/**
 * Conflict Detection Configuration
 */
export interface ConflictDetectionConfig {
  enableTraitDetection: boolean;
  enableBuffDetection: boolean;
  enableInteractionDetection: boolean;
  enableObjectDetection: boolean;
  enableServiceDetection: boolean;
  minimumSeverity: ConflictSeverity;
  includeHarmlessConflicts: boolean;
  confidenceThreshold: number; // 0-1
}

/**
 * Resource Conflict Input
 */
export interface ResourceConflictInput {
  resourceType: OfficialResourceType;
  resourceId: string;
  packageName: string;
  conflictingResources: Array<{
    resourceType: OfficialResourceType;
    resourceId: string;
    packageName: string;
    metadata?: any;
  }>;
  metadata?: any;
}

/**
 * Intelligent Conflict Detector
 * 
 * Main orchestrator for intelligent conflict detection using specialized
 * detectors and gameplay impact analysis.
 */
export class IntelligentConflictDetector {
  private gameplayImpactAnalyzer: GameplayImpactAnalyzer;
  private traitConflictDetector: TraitConflictDetector;
  private buffConflictDetector: BuffConflictDetector;
  
  constructor() {
    this.gameplayImpactAnalyzer = new GameplayImpactAnalyzer();
    this.traitConflictDetector = new TraitConflictDetector();
    this.buffConflictDetector = new BuffConflictDetector();
  }
  
  /**
   * Detect conflicts using intelligent analysis
   */
  public async detectConflicts(
    conflicts: ResourceConflictInput[],
    config: ConflictDetectionConfig = this.getDefaultConfig()
  ): Promise<IntelligentConflictResult[]> {
    const results: IntelligentConflictResult[] = [];
    
    for (const conflict of conflicts) {
      try {
        const result = await this.analyzeConflict(conflict, config);
        if (result && this.shouldIncludeResult(result, config)) {
          results.push(result);
        }
      } catch (error) {
        console.error(`Error analyzing conflict for ${conflict.resourceId}:`, error);
        // Continue with other conflicts even if one fails
      }
    }
    
    // Sort results by priority and severity
    return this.sortResultsByPriority(results);
  }
  
  /**
   * Analyze a single conflict using appropriate detector
   */
  private async analyzeConflict(
    conflict: ResourceConflictInput,
    config: ConflictDetectionConfig
  ): Promise<IntelligentConflictResult | null> {
    const resourceType = conflict.resourceType;
    const metadata = getResourceMetadata(resourceType);
    
    // Skip if resource type detection is disabled
    if (!this.isDetectionEnabled(resourceType, config)) {
      return null;
    }
    
    let analysisResult: ConflictAnalysisResult;
    let detectorUsed: string;
    
    // Use specialized detector based on resource type
    switch (resourceType) {
      case OfficialResourceType.TRAIT:
        if (config.enableTraitDetection) {
          analysisResult = await this.analyzeTraitConflict(conflict);
          detectorUsed = 'TraitConflictDetector';
        } else {
          return null;
        }
        break;
        
      case OfficialResourceType.BUFF:
        if (config.enableBuffDetection) {
          analysisResult = await this.analyzeBuffConflict(conflict);
          detectorUsed = 'BuffConflictDetector';
        } else {
          return null;
        }
        break;
        
      case OfficialResourceType.INTERACTION:
        if (config.enableInteractionDetection) {
          analysisResult = await this.analyzeInteractionConflict(conflict);
          detectorUsed = 'InteractionConflictDetector';
        } else {
          return null;
        }
        break;
        
      case OfficialResourceType.OBJECT:
      case OfficialResourceType.OBJECTDEFINITION:
        if (config.enableObjectDetection) {
          analysisResult = await this.analyzeObjectConflict(conflict);
          detectorUsed = 'ObjectConflictDetector';
        } else {
          return null;
        }
        break;
        
      case OfficialResourceType.SERVICE_NPC:
        if (config.enableServiceDetection) {
          analysisResult = await this.analyzeServiceConflict(conflict);
          detectorUsed = 'ServiceConflictDetector';
        } else {
          return null;
        }
        break;
        
      default:
        // Use general gameplay impact analyzer for other types
        analysisResult = await this.analyzeGeneralConflict(conflict);
        detectorUsed = 'GameplayImpactAnalyzer';
        break;
    }
    
    // Skip if confidence is below threshold
    if (analysisResult.confidence < config.confidenceThreshold) {
      return null;
    }
    
    // Create enhanced result
    const enhancedResult: IntelligentConflictResult = {
      ...analysisResult,
      conflictId: this.generateConflictId(conflict),
      detectorUsed,
      resourceType: conflict.resourceType,
      resourceCategory: metadata?.category || ResourceCategory.UNKNOWN,
      packageNames: [conflict.packageName, ...conflict.conflictingResources.map(r => r.packageName)],
      resourceIds: [conflict.resourceId, ...conflict.conflictingResources.map(r => r.resourceId)],
      detectionTimestamp: new Date(),
      gameplaySystemsAffected: analysisResult.affectedSystems,
      userActionRequired: this.determineUserActionRequired(analysisResult.severity),
      priorityLevel: this.calculatePriorityLevel(analysisResult.severity, analysisResult.impact)
    };
    
    return enhancedResult;
  }
  
  /**
   * Analyze trait conflicts using specialized detector
   */
  private async analyzeTraitConflict(conflict: ResourceConflictInput): Promise<ConflictAnalysisResult> {
    const context: TraitConflictContext = {
      primaryTrait: {
        traitId: conflict.resourceId,
        traitName: conflict.metadata?.name,
        conflictingTraits: conflict.metadata?.conflictingTraits,
        personalityType: conflict.metadata?.personalityType,
        moodEffects: conflict.metadata?.moodEffects,
        skillEffects: conflict.metadata?.skillEffects,
        interactionModifications: conflict.metadata?.interactionModifications
      },
      conflictingTraits: conflict.conflictingResources.map(r => ({
        traitId: r.resourceId,
        traitName: r.metadata?.name,
        conflictingTraits: r.metadata?.conflictingTraits,
        personalityType: r.metadata?.personalityType,
        moodEffects: r.metadata?.moodEffects,
        skillEffects: r.metadata?.skillEffects,
        interactionModifications: r.metadata?.interactionModifications
      })),
      packageNames: [conflict.packageName, ...conflict.conflictingResources.map(r => r.packageName)],
      resourceIds: [conflict.resourceId, ...conflict.conflictingResources.map(r => r.resourceId)]
    };
    
    return this.traitConflictDetector.detectTraitConflicts(context);
  }
  
  /**
   * Analyze buff conflicts using specialized detector
   */
  private async analyzeBuffConflict(conflict: ResourceConflictInput): Promise<ConflictAnalysisResult> {
    const context: BuffConflictContext = {
      primaryBuff: {
        buffId: conflict.resourceId,
        buffName: conflict.metadata?.name,
        moodType: conflict.metadata?.moodType,
        moodWeight: conflict.metadata?.moodWeight,
        duration: conflict.metadata?.duration,
        isVisible: conflict.metadata?.isVisible,
        canStack: conflict.metadata?.canStack,
        maxStacks: conflict.metadata?.maxStacks,
        skillModifiers: conflict.metadata?.skillModifiers,
        needModifiers: conflict.metadata?.needModifiers,
        interactionModifications: conflict.metadata?.interactionModifications,
        conflictingBuffs: conflict.metadata?.conflictingBuffs,
        category: conflict.metadata?.category
      },
      conflictingBuffs: conflict.conflictingResources.map(r => ({
        buffId: r.resourceId,
        buffName: r.metadata?.name,
        moodType: r.metadata?.moodType,
        moodWeight: r.metadata?.moodWeight,
        duration: r.metadata?.duration,
        isVisible: r.metadata?.isVisible,
        canStack: r.metadata?.canStack,
        maxStacks: r.metadata?.maxStacks,
        skillModifiers: r.metadata?.skillModifiers,
        needModifiers: r.metadata?.needModifiers,
        interactionModifications: r.metadata?.interactionModifications,
        conflictingBuffs: r.metadata?.conflictingBuffs,
        category: r.metadata?.category
      })),
      packageNames: [conflict.packageName, ...conflict.conflictingResources.map(r => r.packageName)],
      resourceIds: [conflict.resourceId, ...conflict.conflictingResources.map(r => r.resourceId)]
    };
    
    return this.buffConflictDetector.detectBuffConflicts(context);
  }
  
  /**
   * Analyze interaction conflicts (placeholder for future implementation)
   */
  private async analyzeInteractionConflict(conflict: ResourceConflictInput): Promise<ConflictAnalysisResult> {
    // TODO: Implement specialized interaction conflict detection
    return this.analyzeGeneralConflict(conflict);
  }
  
  /**
   * Analyze object conflicts (placeholder for future implementation)
   */
  private async analyzeObjectConflict(conflict: ResourceConflictInput): Promise<ConflictAnalysisResult> {
    // TODO: Implement specialized object conflict detection
    return this.analyzeGeneralConflict(conflict);
  }
  
  /**
   * Analyze service conflicts (placeholder for future implementation)
   */
  private async analyzeServiceConflict(conflict: ResourceConflictInput): Promise<ConflictAnalysisResult> {
    // TODO: Implement specialized service conflict detection
    return this.analyzeGeneralConflict(conflict);
  }
  
  /**
   * Analyze general conflicts using gameplay impact analyzer
   */
  private async analyzeGeneralConflict(conflict: ResourceConflictInput): Promise<ConflictAnalysisResult> {
    const metadata = getResourceMetadata(conflict.resourceType);
    
    const context: ResourceConflictContext = {
      resourceType: conflict.resourceType,
      resourceCategory: metadata?.category || ResourceCategory.UNKNOWN,
      resourceId: conflict.resourceId,
      conflictingResources: conflict.conflictingResources.map(r => ({
        resourceType: r.resourceType,
        resourceId: r.resourceId,
        packageName: r.packageName
      })),
      metadata: conflict.metadata
    };
    
    return this.gameplayImpactAnalyzer.analyzeConflict(context);
  }
  
  /**
   * Check if detection is enabled for resource type
   */
  private isDetectionEnabled(resourceType: OfficialResourceType, config: ConflictDetectionConfig): boolean {
    switch (resourceType) {
      case OfficialResourceType.TRAIT:
        return config.enableTraitDetection;
      case OfficialResourceType.BUFF:
        return config.enableBuffDetection;
      case OfficialResourceType.INTERACTION:
        return config.enableInteractionDetection;
      case OfficialResourceType.OBJECT:
      case OfficialResourceType.OBJECTDEFINITION:
        return config.enableObjectDetection;
      case OfficialResourceType.SERVICE_NPC:
        return config.enableServiceDetection;
      default:
        return true; // Enable general detection for other types
    }
  }
  
  /**
   * Check if result should be included based on configuration
   */
  private shouldIncludeResult(result: IntelligentConflictResult, config: ConflictDetectionConfig): boolean {
    // Check minimum severity
    const severityOrder = [
      ConflictSeverity.HARMLESS,
      ConflictSeverity.LOW,
      ConflictSeverity.MEDIUM,
      ConflictSeverity.HIGH,
      ConflictSeverity.CRITICAL
    ];
    
    const resultSeverityIndex = severityOrder.indexOf(result.severity);
    const minSeverityIndex = severityOrder.indexOf(config.minimumSeverity);
    
    if (resultSeverityIndex < minSeverityIndex) {
      return false;
    }
    
    // Check if harmless conflicts should be included
    if (result.severity === ConflictSeverity.HARMLESS && !config.includeHarmlessConflicts) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Sort results by priority (severity, impact, confidence)
   */
  private sortResultsByPriority(results: IntelligentConflictResult[]): IntelligentConflictResult[] {
    return results.sort((a, b) => {
      // First by priority level (higher first)
      if (a.priorityLevel !== b.priorityLevel) {
        return b.priorityLevel - a.priorityLevel;
      }
      
      // Then by confidence (higher first)
      if (a.confidence !== b.confidence) {
        return b.confidence - a.confidence;
      }
      
      // Finally by resource type importance
      return this.getResourceTypeImportance(a.resourceType) - this.getResourceTypeImportance(b.resourceType);
    });
  }
  
  /**
   * Generate unique conflict ID
   */
  private generateConflictId(conflict: ResourceConflictInput): string {
    const resourceIds = [conflict.resourceId, ...conflict.conflictingResources.map(r => r.resourceId)].sort();
    const hash = resourceIds.join('|');
    return `conflict_${conflict.resourceType}_${hash.substring(0, 8)}`;
  }
  
  /**
   * Determine if user action is required
   */
  private determineUserActionRequired(severity: ConflictSeverity): boolean {
    return [ConflictSeverity.CRITICAL, ConflictSeverity.HIGH].includes(severity);
  }
  
  /**
   * Calculate priority level (1-5)
   */
  private calculatePriorityLevel(severity: ConflictSeverity, impact: GameplayImpact): number {
    let priority = 3; // Default medium priority
    
    // Adjust based on severity
    switch (severity) {
      case ConflictSeverity.CRITICAL:
        priority = 5;
        break;
      case ConflictSeverity.HIGH:
        priority = 4;
        break;
      case ConflictSeverity.MEDIUM:
        priority = 3;
        break;
      case ConflictSeverity.LOW:
        priority = 2;
        break;
      case ConflictSeverity.HARMLESS:
        priority = 1;
        break;
    }
    
    // Adjust based on impact
    switch (impact) {
      case GameplayImpact.GAME_BREAKING:
        priority = Math.max(priority, 5);
        break;
      case GameplayImpact.FUNCTIONALITY_LOSS:
        priority = Math.max(priority, 4);
        break;
      case GameplayImpact.BEHAVIOR_CHANGE:
        priority = Math.max(priority, 3);
        break;
      case GameplayImpact.VISUAL_GLITCH:
      case GameplayImpact.PERFORMANCE_IMPACT:
        priority = Math.max(priority, 2);
        break;
      case GameplayImpact.NO_IMPACT:
        priority = Math.min(priority, 1);
        break;
    }
    
    return Math.min(5, Math.max(1, priority));
  }
  
  /**
   * Get resource type importance for sorting
   */
  private getResourceTypeImportance(resourceType: OfficialResourceType): number {
    const importanceMap = new Map<OfficialResourceType, number>([
      [OfficialResourceType.TRAIT, 1],
      [OfficialResourceType.BUFF, 2],
      [OfficialResourceType.INTERACTION, 3],
      [OfficialResourceType.CAREER, 4],
      [OfficialResourceType.ASPIRATION, 5],
      [OfficialResourceType.OBJECT, 6],
      [OfficialResourceType.ANIMATION, 7],
      [OfficialResourceType.SITUATION, 8]
    ]);
    
    return importanceMap.get(resourceType) || 10;
  }
  
  /**
   * Get default configuration
   */
  private getDefaultConfig(): ConflictDetectionConfig {
    return {
      enableTraitDetection: true,
      enableBuffDetection: true,
      enableInteractionDetection: true,
      enableObjectDetection: true,
      enableServiceDetection: true,
      minimumSeverity: ConflictSeverity.LOW,
      includeHarmlessConflicts: false,
      confidenceThreshold: 0.6
    };
  }
}
