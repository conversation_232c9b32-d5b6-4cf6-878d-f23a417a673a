/**
 * String Table Extractor for Terrain Paint Analysis
 * 
 * Extracts metadata from string table resources in terrain paint mods.
 * String tables contain names and descriptions for terrain paints.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey } from '../../../../../types/resource/interfaces.js';

const log = new Logger('StringTableExtractor');

/**
 * Extracts metadata from string table resources
 */
export class StringTableExtractor {
    /**
     * Extracts metadata from string table resources
     * @param resources Array of string table resources
     * @returns Metadata extracted from string tables
     */
    public async extract(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): Promise<{
        name?: string;
        description?: string;
        locale?: string;
        entries?: number;
    }> {
        if (resources.length === 0) {
            return {};
        }
        
        log.info(`Extracting metadata from ${resources.length} string table resources`);
        
        // Parse each string table resource
        const stringTables = resources.map(resource => this.parseStringTable(resource.buffer));
        
        // Extract name and description from string tables
        const name = this.extractName(stringTables);
        const description = this.extractDescription(stringTables);
        const locale = this.extractLocale(resources[0].key);
        const entries = stringTables.reduce((total, table) => total + (table.entries?.length || 0), 0);
        
        return {
            name,
            description,
            locale,
            entries
        };
    }
    
    /**
     * Parses a string table buffer
     * @param buffer String table buffer
     * @returns Parsed string table
     */
    private parseStringTable(buffer: Buffer): {
        entries?: { key: number; value: string }[];
    } {
        try {
            // Check for STBL magic number (0x53544258 or "STBX")
            if (buffer.length < 12) {
                return {};
            }
            
            // Check for little-endian representation (0x4C425453)
            const magicNumberLE = buffer.readUInt32LE(0);
            if (magicNumberLE !== 0x4C425453) { // 'LBTS' in hex (STBL reversed)
                return {};
            }
            
            // Parse the string table
            const version = buffer.readUInt32LE(4);
            const count = buffer.readUInt32LE(8);
            
            if (count === 0 || buffer.length < 12 + (count * 8)) {
                return { entries: [] };
            }
            
            const entries: { key: number; value: string }[] = [];
            let offset = 12;
            
            for (let i = 0; i < count; i++) {
                const key = buffer.readUInt32LE(offset);
                const stringOffset = buffer.readUInt32LE(offset + 4);
                
                // Read the string value
                let stringValue = '';
                let stringEnd = stringOffset;
                
                while (stringEnd < buffer.length && buffer[stringEnd] !== 0) {
                    stringEnd++;
                }
                
                if (stringEnd > stringOffset) {
                    stringValue = buffer.toString('utf8', stringOffset, stringEnd);
                }
                
                entries.push({ key, value: stringValue });
                offset += 8;
            }
            
            return { entries };
        } catch (error) {
            log.error(`Error parsing string table: ${error}`);
            return {};
        }
    }
    
    /**
     * Extracts the name from string tables
     * @param stringTables Array of parsed string tables
     * @returns Extracted name
     */
    private extractName(stringTables: { entries?: { key: number; value: string }[] }[]): string | undefined {
        // Look for strings that might be names
        const namePatterns = [
            /terrain/i,
            /paint/i,
            /ground/i,
            /dirt/i,
            /grass/i,
            /sand/i,
            /stone/i,
            /gravel/i,
            /mud/i,
            /snow/i,
            /cement/i,
            /concrete/i,
            /brick/i,
            /wood/i,
            /carpet/i,
            /tile/i,
            /marble/i,
            /metal/i,
            /linoleum/i,
            /leaves/i,
            /water/i,
            /puddle/i
        ];
        
        // Check each string table for entries that match name patterns
        for (const table of stringTables) {
            if (!table.entries || table.entries.length === 0) {
                continue;
            }
            
            // First, look for entries with specific key patterns that might indicate names
            const nameKeyPatterns = [
                0x0000, // Common pattern for names
                0x0001, // Common pattern for names
                0x0100, // Common pattern for names
                0x1000  // Common pattern for names
            ];
            
            for (const pattern of nameKeyPatterns) {
                const entry = table.entries.find(e => e.key === pattern);
                if (entry && entry.value) {
                    return entry.value;
                }
            }
            
            // If no specific key patterns found, look for entries with values that match name patterns
            for (const entry of table.entries) {
                if (!entry.value) {
                    continue;
                }
                
                for (const pattern of namePatterns) {
                    if (pattern.test(entry.value) && entry.value.length < 50) {
                        return entry.value;
                    }
                }
            }
            
            // If no matches found, use the first non-empty entry as a fallback
            const firstNonEmpty = table.entries.find(e => e.value && e.value.length > 0);
            if (firstNonEmpty) {
                return firstNonEmpty.value;
            }
        }
        
        return undefined;
    }
    
    /**
     * Extracts the description from string tables
     * @param stringTables Array of parsed string tables
     * @returns Extracted description
     */
    private extractDescription(stringTables: { entries?: { key: number; value: string }[] }[]): string | undefined {
        // Look for strings that might be descriptions
        const descriptionPatterns = [
            /description/i,
            /desc/i,
            /about/i,
            /info/i
        ];
        
        // Check each string table for entries that match description patterns
        for (const table of stringTables) {
            if (!table.entries || table.entries.length === 0) {
                continue;
            }
            
            // First, look for entries with specific key patterns that might indicate descriptions
            const descKeyPatterns = [
                0x0002, // Common pattern for descriptions
                0x0003, // Common pattern for descriptions
                0x0200, // Common pattern for descriptions
                0x2000  // Common pattern for descriptions
            ];
            
            for (const pattern of descKeyPatterns) {
                const entry = table.entries.find(e => e.key === pattern);
                if (entry && entry.value) {
                    return entry.value;
                }
            }
            
            // If no specific key patterns found, look for entries with values that match description patterns
            for (const entry of table.entries) {
                if (!entry.value) {
                    continue;
                }
                
                for (const pattern of descriptionPatterns) {
                    if (pattern.test(entry.value)) {
                        return entry.value;
                    }
                }
            }
            
            // If no matches found, look for entries with longer text (likely descriptions)
            const longEntries = table.entries
                .filter(e => e.value && e.value.length > 50)
                .sort((a, b) => (b.value?.length || 0) - (a.value?.length || 0));
            
            if (longEntries.length > 0) {
                return longEntries[0].value;
            }
        }
        
        return undefined;
    }
    
    /**
     * Extracts the locale from a resource key
     * @param key Resource key
     * @returns Extracted locale
     */
    private extractLocale(key: ResourceKey): string | undefined {
        // The locale is stored in the lowest byte of the group ID
        const localeId = key.group & 0xFF;
        
        // Map locale ID to locale name
        const localeMap: Record<number, string> = {
            0: 'English',
            1: 'French',
            2: 'German',
            3: 'Italian',
            4: 'Spanish',
            5: 'Dutch',
            6: 'Polish',
            7: 'Portuguese',
            8: 'Russian',
            9: 'Japanese',
            10: 'Chinese (Simplified)',
            11: 'Chinese (Traditional)',
            12: 'Korean',
            13: 'Czech',
            14: 'Danish',
            15: 'Finnish',
            16: 'Norwegian',
            17: 'Swedish',
            18: 'Hungarian',
            19: 'Arabic',
            20: 'Hebrew',
            21: 'Greek',
            22: 'Thai',
            23: 'Turkish',
            24: 'Ukrainian',
            25: 'Romanian',
            26: 'Vietnamese'
        };
        
        return localeMap[localeId] || `Unknown (${localeId})`;
    }
}
