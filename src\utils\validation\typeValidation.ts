/**
 * Basic type validation utilities
 */
export class TypeValidation {
  /**
   * Validates if a value is defined (not null or undefined)
   */
  public static isDefined<T>(value: T | null | undefined, errorMessage?: string): value is T {
    const isDefined = value !== null && value !== undefined;
    if (!isDefined && errorMessage) {
      console.warn(errorMessage);
    }
    return isDefined;
  }

  /**
   * Checks if a value is a number
   */
  public static isNumber(value: unknown, errorMessage?: string): value is number {
    const isValid = typeof value === 'number' && !isNaN(value);
    if (!isValid && errorMessage) {
      throw new Error(errorMessage);
    }
    return isValid;
  }

  /**
   * Checks if a value is a string
   */
  public static isString(value: unknown, errorMessage?: string): value is string {
    const isValid = typeof value === 'string';
    if (!isValid && errorMessage) {
      throw new Error(errorMessage);
    }
    return isValid;
  }

  /**
   * Checks if a value is an array
   */
  public static isArray<T>(value: unknown, errorMessage?: string): value is T[] {
    const isValid = Array.isArray(value);
    if (!isValid && errorMessage) {
      throw new Error(errorMessage);
    }
    return isValid;
  }

  /**
   * Checks if a value is a Buffer
   */
  public static isBuffer(value: unknown, errorMessage?: string): value is Buffer {
    const isValid = Buffer.isBuffer(value);
    if (!isValid && errorMessage) {
      throw new Error(errorMessage);
    }
    return isValid;
  }

  /**
   * Validates if a value is within a range
   */
  public static isInRange(value: number, min: number, max: number, errorMessage?: string): boolean {
    const isInRange = value >= min && value <= max;
    if (!isInRange && errorMessage) {
      console.warn(errorMessage);
    }
    return isInRange;
  }
}
