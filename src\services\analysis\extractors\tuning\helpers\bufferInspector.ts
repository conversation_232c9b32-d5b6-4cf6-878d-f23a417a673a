import { Logger } from '../../../../../utils/logging/logger.js';

const log = new Logger('BufferInspector');

/**
 * Inspects buffer content and logs detailed information
 * @param buffer The buffer to inspect
 * @param label A descriptive label for the log
 * @param instance The instance ID of the resource
 * @param type The type ID of the resource
 */
export function inspectBuffer(buffer: Buffer, label: string, instance: bigint, type: number): void {
    log.debug(`${label} for ${instance.toString(16)} (Type: 0x${type.toString(16)})`);
    log.debug(`  Length: ${buffer.length} bytes`);
    log.debug(`  Hex (first 32 bytes): ${buffer.toString('hex', 0, Math.min(buffer.length, 32))}`);
    log.debug(`  UTF-8 (first 50 chars): "${buffer.toString('utf-8', 0, Math.min(buffer.length, 50))}"`);

    // Check for common XML issues
    const firstChar = buffer.toString('utf-8', 0, 1);
    if (firstChar !== '<') {
        log.debug(`  WARNING: Buffer does not start with '<' character, starts with '${firstChar}' (${buffer[0].toString(16)})`);
    }

    // Check for BOM or other markers
    if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        log.debug(`  WARNING: Buffer starts with UTF-8 BOM (EF BB BF)`);
    }
} 