﻿// Corrected import
import { Resource, ResourceKey } from './resource/interfaces.js'; // Import from interfaces.js

export interface ResourcePackageInfo {
  name: string;
  version: string;
  author?: string;
  description?: string;
  dependencies?: ResourceKey[];
  conflicts?: ResourceKey[];
  resources: Resource[]; // Use imported Resource interface
}

export interface PackageAnalysisResult {
  packageInfo: ResourcePackageInfo;
  validation: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
  metrics: {
    totalResources: number;
    totalSize: number;
    dependencyCount: number;
    conflictCount: number;
  };
  recommendations: string[];
}

export interface PackageValidationOptions {
  strictMode: boolean;
  checkDependencies: boolean;
  checkConflicts: boolean;
}

export interface PackageAnalysisOptions extends PackageValidationOptions {
  includeMetrics: boolean;
  includeRecommendations: boolean;
}
