/**
 * Buff Conflict Detector - Phase 2: Intelligent Conflict Detection
 * 
 * This detector analyzes buff conflicts using the game's mood and behavior
 * system logic, identifying conflicts that would cause gameplay issues.
 * 
 * Based on analysis of the Sims 4 buff system and mood interactions.
 */

import { OfficialResourceType } from '../../../types/resource/OfficialResourceTypes.js';
import { ConflictSeverity, GameplayImpact, ConflictAnalysisResult } from './GameplayImpactAnalyzer.js';

/**
 * Buff Conflict Types based on game logic
 */
export enum BuffConflictType {
  MOOD_CONFLICT = 'MOOD_CONFLICT',               // Buffs affecting opposing moods
  BEHAVIOR_OVERRIDE = 'BEHAVIOR_OVERRIDE',       // Buffs overriding same behaviors
  SKILL_MODIFIER_CONFLICT = 'SKILL_MODIFIER_CONFLICT', // Conflicting skill modifications
  NEED_MODIFIER_CONFLICT = 'NEED_MODIFIER_CONFLICT',   // Conflicting need modifications
  INTERACTION_CONFLICT = 'INTERACTION_CONFLICT', // Conflicting interaction modifications
  DURATION_CONFLICT = 'DURATION_CONFLICT',       // Conflicting duration settings
  STACKING_CONFLICT = 'STACKING_CONFLICT'        // Conflicting stacking rules
}

/**
 * Buff Metadata Interface
 */
export interface BuffMetadata {
  buffId: string;
  buffName?: string;
  moodType?: string;
  moodWeight?: number;
  duration?: number;
  isVisible?: boolean;
  canStack?: boolean;
  maxStacks?: number;
  skillModifiers?: Array<{
    skill: string;
    modifier: number;
  }>;
  needModifiers?: Array<{
    need: string;
    modifier: number;
  }>;
  interactionModifications?: string[];
  conflictingBuffs?: string[];
  category?: string;
}

/**
 * Buff Conflict Context
 */
export interface BuffConflictContext {
  primaryBuff: BuffMetadata;
  conflictingBuffs: BuffMetadata[];
  packageNames: string[];
  resourceIds: string[];
}

/**
 * Buff Conflict Detector
 * 
 * Implements intelligent buff conflict detection using the game's
 * mood and behavior system logic.
 */
export class BuffConflictDetector {
  
  // Mood hierarchy and conflicts from game analysis
  private readonly MOOD_CONFLICTS = new Map<string, string[]>([
    ['Happy', ['Sad', 'Angry', 'Tense']],
    ['Sad', ['Happy', 'Playful', 'Confident']],
    ['Angry', ['Happy', 'Calm', 'Confident']],
    ['Tense', ['Happy', 'Calm', 'Confident']],
    ['Confident', ['Embarrassed', 'Sad', 'Tense']],
    ['Embarrassed', ['Confident', 'Happy', 'Playful']],
    ['Energized', ['Tired', 'Bored']],
    ['Tired', ['Energized', 'Playful']],
    ['Focused', ['Dazed', 'Playful']],
    ['Dazed', ['Focused', 'Inspired']],
    ['Inspired', ['Bored', 'Dazed']],
    ['Playful', ['Sad', 'Tense', 'Bored']],
    ['Bored', ['Inspired', 'Playful', 'Energized']],
    ['Uncomfortable', ['Comfortable', 'Happy']],
    ['Comfortable', ['Uncomfortable', 'Tense']]
  ]);
  
  // Buff categories that typically conflict
  private readonly BUFF_CATEGORIES = new Map<string, string[]>([
    ['mood_positive', ['Happy', 'Confident', 'Energized', 'Inspired', 'Playful']],
    ['mood_negative', ['Sad', 'Angry', 'Tense', 'Embarrassed', 'Uncomfortable']],
    ['skill_boost', ['skill_boost_fitness', 'skill_boost_charisma', 'skill_boost_logic']],
    ['need_boost', ['hunger_boost', 'hygiene_boost', 'bladder_boost', 'energy_boost']],
    ['social_boost', ['social_positive', 'social_negative']],
    ['career_boost', ['work_performance', 'promotion_chance']]
  ]);
  
  /**
   * Detect buff conflicts using game logic
   */
  public detectBuffConflicts(context: BuffConflictContext): ConflictAnalysisResult {
    const conflictType = this.identifyConflictType(context);
    const severity = this.calculateConflictSeverity(conflictType, context);
    const impact = this.determineGameplayImpact(conflictType, severity);
    
    const description = this.generateConflictDescription(conflictType, context);
    const recommendation = this.generateRecommendation(conflictType, severity, context);
    const affectedSystems = this.identifyAffectedSystems(conflictType);
    
    const isRealConflict = severity !== ConflictSeverity.HARMLESS;
    const confidence = this.calculateConfidence(conflictType, context);
    
    return {
      severity,
      impact,
      description,
      recommendation,
      affectedSystems,
      isRealConflict,
      confidence
    };
  }
  
  /**
   * Identify the type of buff conflict
   */
  private identifyConflictType(context: BuffConflictContext): BuffConflictType {
    const primaryBuff = context.primaryBuff;
    const conflictingBuffs = context.conflictingBuffs;
    
    // Check for mood conflicts
    if (this.hasMoodConflicts(primaryBuff, conflictingBuffs)) {
      return BuffConflictType.MOOD_CONFLICT;
    }
    
    // Check for skill modifier conflicts
    if (this.hasSkillModifierConflicts(primaryBuff, conflictingBuffs)) {
      return BuffConflictType.SKILL_MODIFIER_CONFLICT;
    }
    
    // Check for need modifier conflicts
    if (this.hasNeedModifierConflicts(primaryBuff, conflictingBuffs)) {
      return BuffConflictType.NEED_MODIFIER_CONFLICT;
    }
    
    // Check for interaction conflicts
    if (this.hasInteractionConflicts(primaryBuff, conflictingBuffs)) {
      return BuffConflictType.INTERACTION_CONFLICT;
    }
    
    // Check for stacking conflicts
    if (this.hasStackingConflicts(primaryBuff, conflictingBuffs)) {
      return BuffConflictType.STACKING_CONFLICT;
    }
    
    // Check for duration conflicts
    if (this.hasDurationConflicts(primaryBuff, conflictingBuffs)) {
      return BuffConflictType.DURATION_CONFLICT;
    }
    
    // Default to behavior override
    return BuffConflictType.BEHAVIOR_OVERRIDE;
  }
  
  /**
   * Check for mood-related conflicts
   */
  private hasMoodConflicts(primaryBuff: BuffMetadata, conflictingBuffs: BuffMetadata[]): boolean {
    if (!primaryBuff.moodType) return false;
    
    const conflictingMoods = this.MOOD_CONFLICTS.get(primaryBuff.moodType) || [];
    
    return conflictingBuffs.some(buff => 
      buff.moodType && conflictingMoods.includes(buff.moodType)
    );
  }
  
  /**
   * Check for skill modifier conflicts
   */
  private hasSkillModifierConflicts(primaryBuff: BuffMetadata, conflictingBuffs: BuffMetadata[]): boolean {
    if (!primaryBuff.skillModifiers || primaryBuff.skillModifiers.length === 0) {
      return false;
    }
    
    return conflictingBuffs.some(buff => {
      if (!buff.skillModifiers) return false;
      
      return primaryBuff.skillModifiers!.some(primaryMod => 
        buff.skillModifiers!.some(buffMod => 
          primaryMod.skill === buffMod.skill && 
          this.hasConflictingModifiers(primaryMod.modifier, buffMod.modifier)
        )
      );
    });
  }
  
  /**
   * Check for need modifier conflicts
   */
  private hasNeedModifierConflicts(primaryBuff: BuffMetadata, conflictingBuffs: BuffMetadata[]): boolean {
    if (!primaryBuff.needModifiers || primaryBuff.needModifiers.length === 0) {
      return false;
    }
    
    return conflictingBuffs.some(buff => {
      if (!buff.needModifiers) return false;
      
      return primaryBuff.needModifiers!.some(primaryMod => 
        buff.needModifiers!.some(buffMod => 
          primaryMod.need === buffMod.need && 
          this.hasConflictingModifiers(primaryMod.modifier, buffMod.modifier)
        )
      );
    });
  }
  
  /**
   * Check for interaction modification conflicts
   */
  private hasInteractionConflicts(primaryBuff: BuffMetadata, conflictingBuffs: BuffMetadata[]): boolean {
    if (!primaryBuff.interactionModifications || primaryBuff.interactionModifications.length === 0) {
      return false;
    }
    
    return conflictingBuffs.some(buff => {
      if (!buff.interactionModifications) return false;
      
      return primaryBuff.interactionModifications!.some(interaction => 
        buff.interactionModifications!.includes(interaction)
      );
    });
  }
  
  /**
   * Check for stacking rule conflicts
   */
  private hasStackingConflicts(primaryBuff: BuffMetadata, conflictingBuffs: BuffMetadata[]): boolean {
    return conflictingBuffs.some(buff => {
      // Conflict if one allows stacking and another doesn't for same buff
      return primaryBuff.buffId === buff.buffId && 
             primaryBuff.canStack !== buff.canStack;
    });
  }
  
  /**
   * Check for duration conflicts
   */
  private hasDurationConflicts(primaryBuff: BuffMetadata, conflictingBuffs: BuffMetadata[]): boolean {
    return conflictingBuffs.some(buff => {
      // Conflict if same buff has significantly different durations
      return primaryBuff.buffId === buff.buffId && 
             primaryBuff.duration !== undefined && 
             buff.duration !== undefined &&
             Math.abs(primaryBuff.duration - buff.duration) > 3600; // 1 hour difference
    });
  }
  
  /**
   * Check if modifiers are conflicting (opposite signs or very different values)
   */
  private hasConflictingModifiers(modifier1: number, modifier2: number): boolean {
    // Opposite signs indicate conflicting effects
    if ((modifier1 > 0 && modifier2 < 0) || (modifier1 < 0 && modifier2 > 0)) {
      return true;
    }
    
    // Very different values in same direction might also conflict
    if (modifier1 !== 0 && modifier2 !== 0) {
      const ratio = Math.abs(modifier1 / modifier2);
      return ratio > 3 || ratio < 0.33; // 3x difference or more
    }
    
    return false;
  }
  
  /**
   * Calculate conflict severity based on type and context
   */
  private calculateConflictSeverity(conflictType: BuffConflictType, context: BuffConflictContext): ConflictSeverity {
    const conflictCount = context.conflictingBuffs.length;
    
    switch (conflictType) {
      case BuffConflictType.MOOD_CONFLICT:
        return this.calculateMoodConflictSeverity(context);
        
      case BuffConflictType.SKILL_MODIFIER_CONFLICT:
      case BuffConflictType.NEED_MODIFIER_CONFLICT:
        return conflictCount > 2 ? ConflictSeverity.HIGH : ConflictSeverity.MEDIUM;
        
      case BuffConflictType.INTERACTION_CONFLICT:
        return ConflictSeverity.MEDIUM;
        
      case BuffConflictType.STACKING_CONFLICT:
        return ConflictSeverity.HIGH; // Can cause unexpected behavior
        
      case BuffConflictType.DURATION_CONFLICT:
        return ConflictSeverity.LOW; // Usually just affects timing
        
      case BuffConflictType.BEHAVIOR_OVERRIDE:
        return conflictCount > 3 ? ConflictSeverity.HIGH : ConflictSeverity.MEDIUM;
        
      default:
        return ConflictSeverity.MEDIUM;
    }
  }
  
  /**
   * Calculate mood conflict severity based on mood weights and types
   */
  private calculateMoodConflictSeverity(context: BuffConflictContext): ConflictSeverity {
    const primaryBuff = context.primaryBuff;
    const conflictingBuffs = context.conflictingBuffs;
    
    // High severity if strong opposing moods
    const hasStrongOpposition = conflictingBuffs.some(buff => {
      if (!buff.moodType || !buff.moodWeight || !primaryBuff.moodWeight) return false;
      
      const conflictingMoods = this.MOOD_CONFLICTS.get(primaryBuff.moodType!) || [];
      return conflictingMoods.includes(buff.moodType) && 
             (primaryBuff.moodWeight + buff.moodWeight) > 4; // Strong combined weight
    });
    
    if (hasStrongOpposition) {
      return ConflictSeverity.HIGH;
    }
    
    // Medium severity for weaker conflicts
    return ConflictSeverity.MEDIUM;
  }
  
  /**
   * Determine gameplay impact from conflict type
   */
  private determineGameplayImpact(conflictType: BuffConflictType, severity: ConflictSeverity): GameplayImpact {
    switch (conflictType) {
      case BuffConflictType.MOOD_CONFLICT:
        return severity === ConflictSeverity.HIGH 
          ? GameplayImpact.BEHAVIOR_CHANGE 
          : GameplayImpact.VISUAL_GLITCH; // Mood display issues
          
      case BuffConflictType.SKILL_MODIFIER_CONFLICT:
      case BuffConflictType.NEED_MODIFIER_CONFLICT:
        return GameplayImpact.FUNCTIONALITY_LOSS; // Skills/needs don't work as expected
        
      case BuffConflictType.INTERACTION_CONFLICT:
        return GameplayImpact.BEHAVIOR_CHANGE; // Interactions behave differently
        
      case BuffConflictType.STACKING_CONFLICT:
        return GameplayImpact.FUNCTIONALITY_LOSS; // Stacking doesn't work properly
        
      case BuffConflictType.DURATION_CONFLICT:
        return GameplayImpact.BEHAVIOR_CHANGE; // Different timing than expected
        
      case BuffConflictType.BEHAVIOR_OVERRIDE:
        return severity === ConflictSeverity.HIGH 
          ? GameplayImpact.FUNCTIONALITY_LOSS 
          : GameplayImpact.BEHAVIOR_CHANGE;
          
      default:
        return GameplayImpact.BEHAVIOR_CHANGE;
    }
  }
  
  /**
   * Generate human-readable conflict description
   */
  private generateConflictDescription(conflictType: BuffConflictType, context: BuffConflictContext): string {
    const buffName = context.primaryBuff.buffName || context.primaryBuff.buffId;
    const conflictCount = context.conflictingBuffs.length;
    
    switch (conflictType) {
      case BuffConflictType.MOOD_CONFLICT:
        return `The buff "${buffName}" creates mood effects that conflict with ${conflictCount} other buff(s). This may cause mood system instability or unexpected emotional states.`;
        
      case BuffConflictType.SKILL_MODIFIER_CONFLICT:
        return `The buff "${buffName}" modifies skills in ways that conflict with ${conflictCount} other buff(s). This may cause skill progression issues.`;
        
      case BuffConflictType.NEED_MODIFIER_CONFLICT:
        return `The buff "${buffName}" affects Sim needs in ways that conflict with ${conflictCount} other buff(s). This may cause need decay issues.`;
        
      case BuffConflictType.INTERACTION_CONFLICT:
        return `The buff "${buffName}" modifies interactions that are also changed by ${conflictCount} other buff(s). This may cause interaction system issues.`;
        
      case BuffConflictType.STACKING_CONFLICT:
        return `The buff "${buffName}" has conflicting stacking rules with ${conflictCount} other buff modification(s). This may cause unexpected buff behavior.`;
        
      case BuffConflictType.DURATION_CONFLICT:
        return `The buff "${buffName}" has significantly different duration settings compared to ${conflictCount} other modification(s). This may cause timing issues.`;
        
      case BuffConflictType.BEHAVIOR_OVERRIDE:
        return `Multiple mods (${conflictCount + 1}) are modifying the buff "${buffName}". Only one modification will take effect, potentially breaking the others.`;
        
      default:
        return `The buff "${buffName}" has conflicts with ${conflictCount} other buff modification(s).`;
    }
  }
  
  /**
   * Generate recommendation for resolving conflict
   */
  private generateRecommendation(
    conflictType: BuffConflictType, 
    severity: ConflictSeverity, 
    context: BuffConflictContext
  ): string {
    switch (severity) {
      case ConflictSeverity.HIGH:
        return 'RECOMMENDED: Choose one buff mod to keep and remove the others. Test mood and behavior systems carefully.';
        
      case ConflictSeverity.MEDIUM:
        return 'CONSIDER: Test gameplay with all mods active. Remove if mood or behavior issues occur.';
        
      case ConflictSeverity.LOW:
        return 'MONITOR: Watch for timing or visual issues with buffs. Remove if problems are noticed.';
        
      default:
        return 'OPTIONAL: Monitor for unusual buff behavior and remove mods if issues occur.';
    }
  }
  
  /**
   * Identify affected game systems
   */
  private identifyAffectedSystems(conflictType: BuffConflictType): string[] {
    const baseSystems = ['Buff System', 'Mood System'];
    
    switch (conflictType) {
      case BuffConflictType.MOOD_CONFLICT:
        return [...baseSystems, 'Emotion System', 'Behavior System'];
        
      case BuffConflictType.SKILL_MODIFIER_CONFLICT:
        return [...baseSystems, 'Skill System', 'Progression System'];
        
      case BuffConflictType.NEED_MODIFIER_CONFLICT:
        return [...baseSystems, 'Need System', 'Autonomy System'];
        
      case BuffConflictType.INTERACTION_CONFLICT:
        return [...baseSystems, 'Interaction System', 'Social System'];
        
      case BuffConflictType.STACKING_CONFLICT:
      case BuffConflictType.DURATION_CONFLICT:
      case BuffConflictType.BEHAVIOR_OVERRIDE:
        return baseSystems;
        
      default:
        return baseSystems;
    }
  }
  
  /**
   * Calculate confidence in conflict detection
   */
  private calculateConfidence(conflictType: BuffConflictType, context: BuffConflictContext): number {
    let confidence = 0.8; // Base confidence for buff conflicts
    
    switch (conflictType) {
      case BuffConflictType.MOOD_CONFLICT:
        confidence = 0.9; // High confidence for mood conflicts
        break;
        
      case BuffConflictType.SKILL_MODIFIER_CONFLICT:
      case BuffConflictType.NEED_MODIFIER_CONFLICT:
        confidence = 0.85; // High confidence for modifier conflicts
        break;
        
      case BuffConflictType.STACKING_CONFLICT:
        confidence = 0.95; // Very high confidence for stacking conflicts
        break;
        
      case BuffConflictType.INTERACTION_CONFLICT:
      case BuffConflictType.DURATION_CONFLICT:
        confidence = 0.75; // Good confidence
        break;
        
      case BuffConflictType.BEHAVIOR_OVERRIDE:
        confidence = 0.7; // Moderate confidence
        break;
    }
    
    // Adjust confidence based on available metadata
    if (!context.primaryBuff.buffName) {
      confidence -= 0.1;
    }
    
    if (!context.primaryBuff.moodType && conflictType === BuffConflictType.MOOD_CONFLICT) {
      confidence -= 0.2; // Lower confidence for mood conflicts without mood data
    }
    
    return Math.min(1.0, Math.max(0.1, confidence));
  }
}
