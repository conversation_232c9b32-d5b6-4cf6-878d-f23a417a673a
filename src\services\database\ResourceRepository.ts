import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';
import { ResourceInfo } from '../../types/database.js';

export class ResourceRepository {
    private db: Database.Database;
    private logger: Logger;

    constructor(db: Database.Database, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    saveResource(resourceInfo: ResourceInfo): number {
        const isTargetResource = resourceInfo.type === 0x7F4AD89D &&
                                 Number(resourceInfo.group) === 0x80000000 &&
                                 resourceInfo.instance === 87952794550814124n;

        if (isTargetResource) {
            this.logger.debug(`[ResourceRepository] Saving target resource: Type=${resourceInfo.type}, Group=${resourceInfo.group}, Instance=${resourceInfo.instance}`);
            this.logger.debug(`[ResourceRepository] Saving target resource (string conversion): Group="${resourceInfo.group.toString()}", Instance="${resourceInfo.instance.toString()}"`);
        }

        if (isTargetResource) {
            this.logger.debug(`[ResourceRepository] Attempting UPSERT for target resource with packageId: ${resourceInfo.packageId}`);
        }

        try {
            // Check if we have content path and signature hash (from content-addressable storage)
            const hasContentAddressableStorage = resourceInfo.metadata &&
                                               (resourceInfo.metadata.contentPath || resourceInfo.metadata.signatureHash);

            let stmt;
            let params;

            if (hasContentAddressableStorage) {
                // Use the new schema with content-addressable storage fields
                stmt = this.db.prepare(`
                    INSERT INTO Resources (
                        packageId, type, "group", instance, hash, size, offset,
                        contentSnippet, resourceType, signatureHash, contentPath, contentSize
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON CONFLICT(packageId, type, "group", instance) DO UPDATE SET
                        hash = excluded.hash,
                        size = excluded.size,
                        offset = excluded.offset,
                        contentSnippet = excluded.contentSnippet,
                        resourceType = excluded.resourceType,
                        signatureHash = excluded.signatureHash,
                        contentPath = excluded.contentPath,
                        contentSize = excluded.contentSize
                    RETURNING id;
                `);

                params = [
                    resourceInfo.packageId,
                    resourceInfo.type,
                    resourceInfo.group.toString(),     // Store as TEXT
                    resourceInfo.instance.toString(),  // Store as TEXT
                    resourceInfo.hash,
                    resourceInfo.size,
                    resourceInfo.offset,
                    resourceInfo.contentSnippet,
                    resourceInfo.resourceType,
                    resourceInfo.metadata?.signatureHash || null,
                    resourceInfo.metadata?.contentPath || null,
                    resourceInfo.metadata?.contentSize || null
                ];
            } else {
                // Use the original schema for backward compatibility
                stmt = this.db.prepare(`
                    INSERT INTO Resources (packageId, type, "group", instance, hash, size, offset, contentSnippet, resourceType)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON CONFLICT(packageId, type, "group", instance) DO UPDATE SET
                        hash = excluded.hash,
                        size = excluded.size,
                        offset = excluded.offset,
                        contentSnippet = excluded.contentSnippet,
                        resourceType = excluded.resourceType
                    RETURNING id;
                `);

                params = [
                    resourceInfo.packageId,
                    resourceInfo.type,
                    resourceInfo.group.toString(),     // Store as TEXT
                    resourceInfo.instance.toString(),  // Store as TEXT
                    resourceInfo.hash,
                    resourceInfo.size,
                    resourceInfo.offset,
                    resourceInfo.contentSnippet,
                    resourceInfo.resourceType
                ];
            }

            const result = stmt.get(...params) as { id: number };

            if (isTargetResource) {
                this.logger.debug(`[ResourceRepository] Upserted target resource. Returned ID: ${result.id}`);
            }

            return result.id;
        } catch (error) {
            this.logger.error(`Error saving/updating resource (TGI: ${resourceInfo.type}/${resourceInfo.group}/${resourceInfo.instance}):`, error);
            if (isTargetResource) {
                this.logger.error(`[ResourceRepository] Failed to save target resource during upsert.`);
            }
            throw error;
        }
    }

    /**
     * Find a resource by its TGI (Type, Group, Instance), excluding a specific resource ID.
     * @param type The resource type.
     * @param group The resource group (BigInt).
     * @param instance The resource instance (BigInt).
     * @param excludeResourceId The ID of the resource to exclude from the search.
     * @returns The resource ID and package ID if found, otherwise undefined.
     */
    findResourceByTGI(type: number, group: bigint, instance: bigint, excludeResourceId: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint } | undefined {
        const stmt = this.db.prepare(`
            SELECT id, packageId, type, "group", instance FROM Resources
            WHERE type = ? AND "group" = ? AND instance = ? AND id != ?
            ORDER BY id DESC -- Prioritize finding overrides from potentially later-loaded packages if multiple exist
            LIMIT 1;
        `);
        try {
            const groupString = group.toString();
            const instanceString = instance.toString();
            this.logger.debug(`[ResourceRepository] findResourceByTGI: Querying for TGI (${type}:${groupString}:${instanceString}) excluding ID ${excludeResourceId}`);
            const result = stmt.get(type, groupString, instanceString, excludeResourceId) as { id: number; packageId: number; type: number; group: string; instance: string } | undefined;

            if (!result) {
                this.logger.debug(`[ResourceRepository] findResourceByTGI: Result for TGI (${type}:${groupString}:${instanceString}): undefined`);
                return undefined;
            }

            const resolvedResource = {
                id: result.id,
                packageId: result.packageId,
                type: result.type,
                group: BigInt(result.group),
                instance: BigInt(result.instance)
            };

            this.logger.debug(`[ResourceRepository] findResourceByTGI: Result for TGI (${type}:${groupString}:${instanceString}): ${JSON.stringify(resolvedResource)}`);
            return resolvedResource;

        } catch (error) {
            this.logger.error(`[ResourceRepository] Error finding resource by TGI (${type}:${group}:${instance}) excluding ID ${excludeResourceId}:`, error);
            return undefined;
        }
    }

    /**
     * Get a resource by its ID
     * @param resourceId The ID of the resource to get
     * @returns The resource or undefined if not found
     */
    public getResourceById(resourceId: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint; resourceType: string } | undefined {
        try {
            const stmt = this.db.prepare(`
                SELECT id, packageId, type, "group", instance, resourceType FROM Resources
                WHERE id = ?
            `);

            const result = stmt.get(resourceId) as { id: number; packageId: number; type: number; group: string; instance: string; resourceType: string } | undefined;

            if (!result) {
                return undefined;
            }

            return {
                id: result.id,
                packageId: result.packageId,
                type: result.type,
                group: BigInt(result.group),
                instance: BigInt(result.instance),
                resourceType: result.resourceType
            };
        } catch (error) {
            this.logger.error(`Error getting resource by ID ${resourceId}: ${error}`);
            return undefined;
        }
    }

    /**
     * Get all resources in a package
     * @param packageId The ID of the package
     * @returns Array of resources
     */
    public getResourcesByPackageId(packageId: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint; resourceType: string }[] {
        try {
            const stmt = this.db.prepare(`
                SELECT id, packageId, type, "group", instance, resourceType FROM Resources
                WHERE packageId = ?
            `);

            const results = stmt.all(packageId) as { id: number; packageId: number; type: number; group: string; instance: string; resourceType: string }[];

            return results.map(result => ({
                id: result.id,
                packageId: result.packageId,
                type: result.type,
                group: BigInt(result.group),
                instance: BigInt(result.instance),
                resourceType: result.resourceType
            }));
        } catch (error) {
            this.logger.error(`Error getting resources for package ${packageId}: ${error}`);
            return [];
        }
    }

    /**
     * Find resources by type in a specific package
     * @param type The resource type to find
     * @param packageId The package ID to search in
     * @returns Array of matching resources
     */
    public findResourcesByTypeInPackage(type: number | number[], packageId: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint }[] {
        try {
            // Handle both single type and array of types
            if (Array.isArray(type)) {
                if (type.length === 0) {
                    return [];
                }

                if (type.length === 1) {
                    // If there's only one type in the array, use the simple query
                    return this.findResourcesByTypeInPackage(type[0], packageId);
                }

                // For multiple types, use IN clause
                const placeholders = type.map(() => '?').join(',');
                const query = `
                    SELECT id, packageId, type, "group", instance FROM Resources
                    WHERE type IN (${placeholders}) AND packageId = ?
                `;

                // Create parameters array with all types followed by packageId
                const params = [...type, packageId];

                const stmt = this.db.prepare(query);
                const results = stmt.all(...params) as { id: number; packageId: number; type: number; group: string; instance: string }[];

                return results.map(result => ({
                    id: result.id,
                    packageId: result.packageId,
                    type: result.type,
                    group: BigInt(result.group),
                    instance: BigInt(result.instance)
                }));
            } else {
                // Single type case
                const stmt = this.db.prepare(`
                    SELECT id, packageId, type, "group", instance FROM Resources
                    WHERE type = ? AND packageId = ?
                `);

                const results = stmt.all(type, packageId) as { id: number; packageId: number; type: number; group: string; instance: string }[];

                return results.map(result => ({
                    id: result.id,
                    packageId: result.packageId,
                    type: result.type,
                    group: BigInt(result.group),
                    instance: BigInt(result.instance)
                }));
            }
        } catch (error) {
            const typeStr = Array.isArray(type) ? type.map(t => t.toString(16)).join(',') : type.toString(16);
            this.logger.error(`Error finding resources by type ${typeStr} in package ${packageId}: ${error}`);
            return [];
        }
    }

    /**
     * [DEBUG ONLY] Executes a simplified query to find a resource by its instance string.
     * @param instanceString The instance ID as a string.
     * @returns The resource row if found, otherwise undefined.
     */
    public debugGetResourceByInstanceString(instanceString: string): any | undefined {
        this.logger.debug(`[DEBUG] Executing simplified query for instance string: ${instanceString}`);
        const stmt = this.db.prepare(`
            SELECT id, packageId, type, "group", instance FROM Resources WHERE instance = ?
        `);
        try {
            const result = stmt.get(instanceString);
            this.logger.debug(`[DEBUG] Simplified query result: ${JSON.stringify(result)}`);
            return result;
        } catch (error) {
            this.logger.error(`[DEBUG] Error in simplified query for instance ${instanceString}:`, error);
            return undefined;
        }
    }

    /**
     * Update the resource type for a given resource ID.
     * @param resourceId The ID of the resource to update.
     * @param resourceType The new resource type string.
     */
    public updateResourceType(resourceId: number, resourceType: string): void {
        try {
            const stmt = this.db.prepare(`
                UPDATE Resources SET resourceType = ? WHERE id = ?
            `);
            stmt.run(resourceType, resourceId);
            this.logger.debug(`[ResourceRepository] Updated resourceType for resource ID ${resourceId} to ${resourceType}`);
        } catch (error) {
            this.logger.error(`[ResourceRepository] Error updating resourceType for resource ID ${resourceId}:`, error);
            throw error;
        }
    }

    /**
     * Get all resources from the database
     * @param limit Optional limit on the number of resources to return
     * @param offset Optional offset for pagination
     * @returns Array of all resources
     */
    public getAllResources(limit?: number, offset?: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint; resourceType: string }[] {
        try {
            let query = `
                SELECT id, packageId, type, "group", instance, resourceType FROM Resources
            `;

            const params: any[] = [];

            if (limit !== undefined) {
                query += ` LIMIT ?`;
                params.push(limit);

                if (offset !== undefined) {
                    query += ` OFFSET ?`;
                    params.push(offset);
                }
            }

            const stmt = this.db.prepare(query);
            const results = stmt.all(...params) as { id: number; packageId: number; type: number; group: string; instance: string; resourceType: string }[];

            return results.map(result => ({
                id: result.id,
                packageId: result.packageId,
                type: result.type,
                group: BigInt(result.group),
                instance: BigInt(result.instance),
                resourceType: result.resourceType
            }));
        } catch (error) {
            this.logger.error(`Error getting all resources: ${error}`);
            return [];
        }
    }

    /**
     * Get resources by their resource type string
     * @param resourceType The resource type string to find
     * @param limit Optional limit on the number of resources to return
     * @returns Array of matching resources
     */
    public getResourcesByType(resourceType: string, limit?: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint; resourceType: string }[] {
        try {
            let query = `
                SELECT id, packageId, type, "group", instance, resourceType FROM Resources
                WHERE resourceType = ?
            `;

            if (limit) {
                query += ` LIMIT ${limit}`;
            }

            const stmt = this.db.prepare(query);

            const results = stmt.all(resourceType) as { id: number; packageId: number; type: number; group: string; instance: string; resourceType: string }[];

            return results.map(result => ({
                id: result.id,
                packageId: result.packageId,
                type: result.type,
                group: BigInt(result.group),
                instance: BigInt(result.instance),
                resourceType: result.resourceType
            }));
        } catch (error) {
            this.logger.error(`Error getting resources by type ${resourceType}: ${error}`);
            return [];
        }
    }

    /**
     * Get resources by their numeric type
     * @param type The numeric resource type to find
     * @param limit Optional limit on the number of resources to return
     * @returns Array of matching resources
     */
    public getResourcesByNumericType(type: number, limit?: number): { id: number; packageId: number; type: number; group: bigint; instance: bigint; resourceType: string }[] {
        try {
            let query = `
                SELECT id, packageId, type, "group", instance, resourceType FROM Resources
                WHERE type = ?
            `;

            if (limit) {
                query += ` LIMIT ${limit}`;
            }

            const stmt = this.db.prepare(query);

            const results = stmt.all(type) as { id: number; packageId: number; type: number; group: string; instance: string; resourceType: string }[];

            return results.map(result => ({
                id: result.id,
                packageId: result.packageId,
                type: result.type,
                group: BigInt(result.group),
                instance: BigInt(result.instance),
                resourceType: result.resourceType
            }));
        } catch (error) {
            this.logger.error(`Error getting resources by numeric type ${type.toString(16)}: ${error}`);
            return [];
        }
    }

    /**
     * Get resources by their TGI (Type, Group, Instance)
     * @param type The resource type
     * @param group The resource group (as string)
     * @param instance The resource instance (as string)
     * @returns Array of matching resources
     */
    public getResourcesByTGI(type: number, group: string, instance: string): { id: number; packageId: number; type: number; group: bigint; instance: bigint; resourceType: string }[] {
        try {
            const stmt = this.db.prepare(`
                SELECT id, packageId, type, "group", instance, resourceType FROM Resources
                WHERE type = ? AND "group" = ? AND instance = ?
            `);

            const results = stmt.all(type, group, instance) as { id: number; packageId: number; type: number; group: string; instance: string; resourceType: string }[];

            return results.map(result => ({
                id: result.id,
                packageId: result.packageId,
                type: result.type,
                group: BigInt(result.group),
                instance: BigInt(result.instance),
                resourceType: result.resourceType
            }));
        } catch (error) {
            this.logger.error(`Error getting resources by TGI (${type}:${group}:${instance}): ${error}`);
            return [];
        }
    }
}