/**
 * Game Version Detector
 *
 * Detects Sims 4 game versions from mod files using fingerprinting
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { GameVersion, GameVersionDetectionResult, GameVersionFingerprintType } from './interfaces/gameVersion.js';
import { ALL_GAME_VERSIONS } from './constants/gameVersions.js';
import { VersionDatabase } from './database/versionDatabase.js';
import { ResourceKey } from '../../types/resource/interfaces.js';
import * as ResourceTypes from '../../constants/resourceTypes.js';

/**
 * Detector for Sims 4 game versions
 */
export class GameVersionDetector {
    private logger: Logger;
    private versionDatabase: VersionDatabase;
    private versions: Map<string, GameVersion> = new Map();
    private initialized: boolean = false;

    /**
     * Constructor
     * @param databaseService The database service
     * @param logger The logger instance
     */
    constructor(private databaseService: DatabaseService, logger?: Logger) {
        this.logger = logger || new Logger('GameVersionDetector');
        this.versionDatabase = new VersionDatabase(databaseService, this.logger);
    }

    /**
     * Initialize the detector
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Initialize the version database
            await this.versionDatabase.initialize();

            // Load all game versions into memory
            for (const version of ALL_GAME_VERSIONS) {
                this.versions.set(version.versionNumber, version);

                // Save to database
                await this.versionDatabase.saveGameVersion(version);
            }

            this.initialized = true;
            this.logger.info(`Initialized GameVersionDetector with ${this.versions.size} versions`);
        } catch (error) {
            this.logger.error('Error initializing GameVersionDetector:', error);
            throw error;
        }
    }

    /**
     * Detect the game version from a package's resources
     * @param packageId The package ID in the database
     * @param resources Array of resources in the package
     * @param resourceContents Map of resource content snippets (key: resourceId, value: content)
     * @returns Game version detection result
     */
    public async detectGameVersion(
        packageId: number,
        resources: Array<{ key: ResourceKey, resourceId: number }>,
        resourceContents: Map<number, string>
    ): Promise<GameVersionDetectionResult> {
        await this.initialize();

        try {
            // Track version matches and their confidence scores
            const versionMatches: Map<string, number> = new Map();
            const matchedFingerprints: Array<{
                fingerprintType: GameVersionFingerprintType;
                pattern: string;
                weight: number;
            }> = [];
            let matchExplanations: string[] = [];

            // Check each resource against version fingerprints
            for (const resource of resources) {
                const resourceContent = resourceContents.get(resource.resourceId) || '';

                // Check each version's fingerprints
                for (const [versionNumber, version] of this.versions.entries()) {
                    for (const fingerprint of version.fingerprints) {
                        let isMatch = false;

                        switch (fingerprint.type) {
                            case GameVersionFingerprintType.RESOURCE_TGI:
                                // Check if resource TGI matches the fingerprint pattern
                                isMatch = this.matchResourceTGI(resource.key, fingerprint.pattern);
                                break;

                            case GameVersionFingerprintType.CONTENT_PATTERN:
                                // Check if resource content contains the pattern
                                isMatch = resourceContent.includes(fingerprint.pattern);
                                break;

                            case GameVersionFingerprintType.RESOURCE_HASH:
                                // Not implemented yet - would need resource hash
                                break;

                            case GameVersionFingerprintType.SIMDATA_SCHEMA:
                                // Not implemented yet - would need SimData schema
                                break;

                            case GameVersionFingerprintType.TUNING_PATTERN:
                                // Check if resource is tuning XML and contains the pattern
                                if (resource.key.type === ResourceTypes.TUNING_XML) {
                                    isMatch = resourceContent.includes(fingerprint.pattern);
                                }
                                break;

                            case GameVersionFingerprintType.SCRIPT_PATTERN:
                                // Check if resource is script and contains the pattern
                                if (resource.key.type === ResourceTypes.SCRIPT) {
                                    isMatch = resourceContent.includes(fingerprint.pattern);
                                }
                                break;
                        }

                        if (isMatch) {
                            // Add fingerprint weight to version score
                            versionMatches.set(versionNumber, (versionMatches.get(versionNumber) || 0) + fingerprint.weight);

                            // Record matched fingerprint
                            matchedFingerprints.push({
                                fingerprintType: fingerprint.type,
                                pattern: fingerprint.pattern,
                                weight: fingerprint.weight
                            });

                            // Add explanation
                            matchExplanations.push(`Resource ${resource.key.type.toString(16)}:${resource.key.group}:${resource.key.instance} matched ${fingerprint.type} fingerprint for version ${versionNumber}: ${fingerprint.description}`);

                            // If this is a unique fingerprint, give it a big boost
                            if (fingerprint.isUnique) {
                                versionMatches.set(versionNumber, (versionMatches.get(versionNumber) || 0) + 100);
                                matchExplanations.push(`Unique fingerprint match for version ${versionNumber}`);
                            }
                        }
                    }
                }
            }

            // Convert matches to sorted array
            const sortedMatches = Array.from(versionMatches.entries())
                .sort((a, b) => b[1] - a[1]);

            // Default detection result
            let detectionResult: GameVersionDetectionResult = {
                version: 'unknown',
                confidence: 0,
                matchedFingerprints: [],
                explanation: 'No matching game version found',
                timestamp: Date.now()
            };

            // If we have matches, update the detection result
            if (sortedMatches.length > 0) {
                const [detectedVersion, score] = sortedMatches[0];

                // Normalize confidence to 0-100 scale
                const normalizedConfidence = Math.min(100, score);

                detectionResult = {
                    version: detectedVersion,
                    confidence: normalizedConfidence,
                    matchedFingerprints,
                    explanation: matchExplanations.join('. '),
                    timestamp: Date.now()
                };
            }

            // Save to database if packageId is defined
            if (packageId) {
                try {
                    await this.versionDatabase.saveModGameVersion(detectionResult, packageId);
                } catch (dbError) {
                    this.logger.error(`Error saving game version detection result for package ${packageId}:`, dbError);
                    // Continue despite database error
                }
            } else {
                this.logger.warn(`Cannot save game version detection result: packageId is undefined`);
            }

            return detectionResult;
        } catch (error) {
            this.logger.error(`Error detecting game version for package ${packageId || 'undefined'}:`, error);

            // Return default result on error
            return {
                version: 'unknown',
                confidence: 0,
                matchedFingerprints: [],
                explanation: 'Error detecting game version',
                timestamp: Date.now()
            };
        }
    }

    /**
     * Match a resource TGI against a fingerprint pattern
     * @param resourceKey The resource key
     * @param pattern The fingerprint pattern
     * @returns True if the resource matches the pattern
     */
    private matchResourceTGI(resourceKey: ResourceKey, pattern: string): boolean {
        try {
            // Parse pattern into type, group, instance
            const [typeStr, groupStr, instanceStr] = pattern.split(':');

            // Convert type to number (handling hex)
            const typeMatch = typeStr.startsWith('0x')
                ? parseInt(typeStr, 16) === resourceKey.type
                : parseInt(typeStr) === resourceKey.type;

            // If type doesn't match, return false
            if (!typeMatch) return false;

            // If pattern only specifies type, it's a match
            if (!groupStr) return true;

            // Check group match (could be wildcard '*')
            const groupMatch = groupStr === '*' || groupStr === resourceKey.group.toString();

            // If group doesn't match, return false
            if (!groupMatch) return false;

            // If pattern only specifies type and group, it's a match
            if (!instanceStr) return true;

            // Check instance match (could be wildcard '*')
            const instanceMatch = instanceStr === '*' || instanceStr === resourceKey.instance.toString();

            return instanceMatch;
        } catch (error) {
            this.logger.error(`Error matching resource TGI against pattern ${pattern}:`, error);
            return false;
        }
    }

    /**
     * Get a game version by version number
     * @param versionNumber The version number
     * @returns The game version or undefined if not found
     */
    public async getVersion(versionNumber: string): Promise<GameVersion | undefined> {
        await this.initialize();
        return this.versions.get(versionNumber);
    }

    /**
     * Get all game versions
     * @returns Array of all game versions
     */
    public async getAllVersions(): Promise<GameVersion[]> {
        await this.initialize();
        return Array.from(this.versions.values());
    }

    /**
     * Get all game versions (alias for getAllVersions)
     * @returns Array of all game versions
     */
    public async getAllGameVersions(): Promise<GameVersion[]> {
        return this.getAllVersions();
    }

    /**
     * Get major game versions
     * @returns Array of major game versions
     */
    public async getMajorVersions(): Promise<GameVersion[]> {
        await this.initialize();
        return Array.from(this.versions.values()).filter(v => v.isMajor);
    }

    /**
     * Get the detected game version for a mod
     * @param packageId The package ID
     * @returns The game version detection result or undefined if not found
     */
    public async getDetectedVersion(packageId: number): Promise<GameVersionDetectionResult | undefined> {
        await this.initialize();
        return this.versionDatabase.getModGameVersion(packageId);
    }

    /**
     * Register a custom game version
     * @param version The game version to register
     */
    public async registerVersion(version: GameVersion): Promise<void> {
        await this.initialize();

        try {
            // Add to memory cache
            this.versions.set(version.versionNumber, version);

            // Save to database
            await this.versionDatabase.saveGameVersion(version);

            this.logger.info(`Registered custom game version: ${version.versionNumber}`);
        } catch (error) {
            this.logger.error(`Error registering custom game version ${version.versionNumber}:`, error);
            throw error;
        }
    }

    /**
     * Dispose of resources used by the detector
     */
    public async dispose(): Promise<void> {
        try {
            this.logger.info('Disposing GameVersionDetector resources');

            // Clear versions map
            this.versions.clear();

            this.initialized = false;
            this.logger.info('GameVersionDetector resources disposed successfully');
        } catch (error) {
            this.logger.error('Error disposing GameVersionDetector resources:', error);
            throw error;
        }
    }
}
