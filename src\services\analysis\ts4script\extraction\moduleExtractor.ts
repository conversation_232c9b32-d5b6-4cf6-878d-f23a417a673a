/**
 * TS4Script Module Extractor
 *
 * This module provides functionality for extracting Python modules from TS4Script files.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { TS4ScriptModule, TS4ScriptModuleMetadata } from '../types.js';
import * as fs from 'fs/promises';
import * as path from 'path';
import JSZip from 'jszip';

/**
 * TS4Script module file
 */
export interface TS4ScriptModuleFile {
    /**
     * Module name
     */
    name: string;

    /**
     * Module path
     */
    path: string;

    /**
     * File content
     */
    content: Buffer;

    /**
     * Is Python bytecode
     */
    isPythonBytecode: boolean;

    /**
     * Is package init
     */
    isPackageInit: boolean;

    /**
     * Is subpackage init
     */
    isSubpackageInit: boolean;
}

/**
 * TS4Script module extractor
 */
@singleton()
export class ModuleExtractor {
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(private logger: Logger = new Logger('ModuleExtractor')) {}

    /**
     * Extract modules from TS4Script file
     * @param filePath TS4Script file path
     * @returns Extracted modules
     */
    public async extractModules(filePath: string): Promise<TS4ScriptModuleFile[]> {
        try {
            this.logger.info(`Extracting modules from TS4Script file: ${filePath}`);

            // Read file content
            const fileContent = await fs.readFile(filePath);

            // Load ZIP file
            const zip = await JSZip.loadAsync(fileContent);

            // Extract modules
            const modules: TS4ScriptModuleFile[] = [];

            // Process each file in the ZIP
            for (const [filename, file] of Object.entries(zip.files)) {
                // Skip directories
                if (file.dir) {
                    continue;
                }

                // Skip non-Python files
                if (!filename.endsWith('.py') && !filename.endsWith('.pyc')) {
                    continue;
                }

                // Get file content
                const content = await file.async('nodebuffer');

                // Determine if this is a Python bytecode file
                const isPythonBytecode = filename.endsWith('.pyc');

                // Determine if this is a package init file
                const isPackageInit = path.basename(filename) === '__init__.py' || path.basename(filename) === '__init__.pyc';

                // Determine if this is a subpackage init file
                const isSubpackageInit = isPackageInit && path.dirname(filename) !== '';

                // Create module file
                const moduleFile: TS4ScriptModuleFile = {
                    name: this.getModuleName(filename),
                    path: filename,
                    content,
                    isPythonBytecode,
                    isPackageInit,
                    isSubpackageInit
                };

                modules.push(moduleFile);
            }

            this.logger.info(`Extracted ${modules.length} modules from TS4Script file: ${filePath}`);
            return modules;
        } catch (error) {
            this.logger.error(`Error extracting modules from TS4Script file ${filePath}:`, error);
            return [];
        }
    }

    /**
     * Get module name from file path
     * @param filePath File path
     * @returns Module name
     */
    private getModuleName(filePath: string): string {
        // Remove extension
        let moduleName = filePath.replace(/\.(py|pyc)$/, '');

        // Replace directory separators with dots
        moduleName = moduleName.replace(/[\/\\]/g, '.');

        // Handle __init__ modules
        if (moduleName.endsWith('.__init__')) {
            moduleName = moduleName.replace(/\.__init__$/, '');
        }

        return moduleName;
    }

    /**
     * Create TS4Script module from module file
     * @param moduleFile Module file
     * @param packageId Package ID
     * @returns TS4Script module
     */
    public createModule(moduleFile: TS4ScriptModuleFile, packageId: number): TS4ScriptModule {
        try {
            // Convert content to string if not bytecode
            const content = moduleFile.isPythonBytecode
                ? `[Python Bytecode File: ${moduleFile.path}]`
                : moduleFile.content.toString('utf-8');

            // Create content snippet
            const contentSnippet = content.substring(0, 1000) + (content.length > 1000 ? '...' : '');

            // Create module metadata
            const metadata: TS4ScriptModuleMetadata = {
                moduleType: 'unknown',
                hasInjections: false,
                hasEventHandlers: false,
                hasCommands: false,
                hasTuningReferences: false,
                isPackageInit: moduleFile.isPackageInit,
                isSubpackageInit: moduleFile.isSubpackageInit
            };

            // Create module
            const module: TS4ScriptModule = {
                id: 0, // Will be set after saving to database
                name: moduleFile.name,
                path: moduleFile.path,
                content,
                contentSnippet,
                classes: [],
                functions: [],
                imports: [],
                metadata
            };

            return module;
        } catch (error) {
            this.logger.error(`Error creating module from module file ${moduleFile.path}:`, error);
            throw error;
        }
    }
}
