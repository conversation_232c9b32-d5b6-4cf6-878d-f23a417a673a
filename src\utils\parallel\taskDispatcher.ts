/**
 * Task Dispatcher
 *
 * This module provides a task dispatcher for managing and distributing tasks to the worker pool.
 * It handles task queuing, prioritization, and execution.
 */

import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { Logger } from '../logging/logger.js';
import WorkerPool, { Task, TaskType, TaskPriority, TaskResult } from './workerPool.js';

// Create a logger for this module
const logger = new Logger('TaskDispatcher');

/**
 * Task queue entry
 */
interface TaskQueueEntry<T = any, R = any> {
    task: Task<T, R>;
    resolve: (result: TaskResult<R>) => void;
    reject: (error: Error) => void;
}

/**
 * Batch processing options
 */
export interface BatchOptions {
    initialBatchSize?: number;
    minBatchSize?: number;
    maxBatchSize?: number;
    concurrency?: number;
    priority?: TaskPriority;
    timeout?: number;
    retries?: number;
    onProgress?: (completed: number, total: number) => void;
    abortSignal?: AbortSignal;
}

/**
 * Task dispatcher options
 */
export interface TaskDispatcherOptions {
    maxConcurrentTasks?: number;
    workerPoolOptions?: any;
    defaultBatchOptions?: BatchOptions;
}

/**
 * Task dispatcher class for managing and distributing tasks
 */
export class TaskDispatcher extends EventEmitter {
    private static instance: TaskDispatcher;
    private workerPool: WorkerPool;
    private taskQueue: TaskQueueEntry[] = [];
    private activeTasks = new Map<string, TaskQueueEntry>();
    private maxConcurrentTasks: number;
    private isProcessing = false;
    private isInitialized = false;

    /**
     * Private constructor to enforce Singleton pattern
     * @param options Task dispatcher options
     */
    private defaultBatchOptions: BatchOptions;

    private constructor(options: TaskDispatcherOptions = {}) {
        super();
        this.setMaxListeners(100); // Set high max listeners
        this.maxConcurrentTasks = options.maxConcurrentTasks || 10;
        this.workerPool = WorkerPool.getInstance(options.workerPoolOptions);

        // Set default batch options
        this.defaultBatchOptions = options.defaultBatchOptions || {
            initialBatchSize: 10,
            minBatchSize: 1,
            maxBatchSize: 100,
            concurrency: Math.max(1, Math.floor(this.maxConcurrentTasks / 2)),
            priority: TaskPriority.NORMAL,
            timeout: 60000, // 1 minute
            retries: 3
        };

        // Set up event listeners
        this.workerPool.on('taskCompleted', (result: TaskResult) => {
            this.emit('taskCompleted', result);
        });

        this.workerPool.on('taskFailed', (result: TaskResult) => {
            this.emit('taskFailed', result);
        });

        this.workerPool.on('error', (error: Error) => {
            this.emit('error', error);
        });

        logger.info(`Task dispatcher created with max concurrent tasks: ${this.maxConcurrentTasks}`);
    }

    /**
     * Get the TaskDispatcher instance (Singleton pattern)
     * @param options Task dispatcher options (only used on first call)
     * @returns The TaskDispatcher instance
     */
    public static getInstance(options?: TaskDispatcherOptions): TaskDispatcher {
        if (!TaskDispatcher.instance) {
            TaskDispatcher.instance = new TaskDispatcher(options);
        }
        return TaskDispatcher.instance;
    }

    /**
     * Initialize the task dispatcher
     * @returns Promise that resolves when the dispatcher is initialized
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            logger.warn('Task dispatcher is already initialized');
            return;
        }

        try {
            await this.workerPool.initialize();
            this.isInitialized = true;
            logger.info('Task dispatcher initialized');
        } catch (error) {
            logger.error('Failed to initialize task dispatcher:', error);
            throw error;
        }
    }

    /**
     * Dispatch a task to the worker pool
     * @param type Task type
     * @param data Task data
     * @param priority Task priority
     * @returns Promise that resolves with the task result
     */
    public async dispatch<T, R>(
        type: TaskType,
        data: T,
        priority: TaskPriority = TaskPriority.NORMAL
    ): Promise<R> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Create a task
        const task: Task<T, R> = {
            id: uuidv4(),
            type,
            data,
            priority,
            createdAt: Date.now()
        };

        // Create a promise that will be resolved when the task is completed
        return new Promise<R>((resolve, reject) => {
            // Add the task to the queue
            this.taskQueue.push({
                task,
                resolve: (result: TaskResult<R>) => resolve(result.result),
                reject
            });

            // Sort the queue by priority (higher priority first) and then by creation time (older first)
            this.taskQueue.sort((a, b) => {
                if (a.task.priority !== b.task.priority) {
                    return b.task.priority - a.task.priority;
                }
                return a.task.createdAt - b.task.createdAt;
            });

            // Process the queue
            this.processQueue();
        });
    }

    /**
     * Process the task queue
     * @private
     */
    private async processQueue(): Promise<void> {
        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;

        try {
            // Process tasks until the queue is empty or we reach the maximum number of concurrent tasks
            while (this.taskQueue.length > 0 && this.activeTasks.size < this.maxConcurrentTasks) {
                // Get the next task from the queue
                const entry = this.taskQueue.shift()!;
                const { task } = entry;

                // Add the task to the active tasks map
                this.activeTasks.set(task.id, entry);

                // Submit the task to the worker pool
                this.workerPool.submitTask(task)
                    .then((result) => {
                        // Remove the task from the active tasks map
                        this.activeTasks.delete(task.id);

                        // Resolve the promise
                        entry.resolve(result);

                        // Process the queue again
                        this.processQueue();
                    })
                    .catch((error) => {
                        // Remove the task from the active tasks map
                        this.activeTasks.delete(task.id);

                        // Reject the promise
                        entry.reject(error);

                        // Process the queue again
                        this.processQueue();
                    });
            }
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Get the number of tasks in the queue
     * @returns The number of tasks in the queue
     */
    public getQueueLength(): number {
        return this.taskQueue.length;
    }

    /**
     * Get the number of active tasks
     * @returns The number of active tasks
     */
    public getActiveTaskCount(): number {
        return this.activeTasks.size;
    }

    /**
     * Get task dispatcher statistics
     * @returns Task dispatcher statistics
     */
    public getStats() {
        return {
            queueLength: this.taskQueue.length,
            activeTasks: this.activeTasks.size,
            maxConcurrentTasks: this.maxConcurrentTasks,
            workerPoolStats: this.workerPool.getStats()
        };
    }

    /**
     * Process a batch of items using a processor function
     * @param items Array of items to process
     * @param processor Function that processes a batch of items
     * @param options Batch processing options
     * @returns Promise that resolves with the results
     */
    public async dispatchBatch<T, R>(
        items: T[],
        processor: (batch: T[]) => Promise<R[]>,
        options?: BatchOptions
    ): Promise<R[]> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Merge options with defaults
        const opts: Required<BatchOptions> = {
            initialBatchSize: options?.initialBatchSize ?? this.defaultBatchOptions.initialBatchSize ?? 10,
            minBatchSize: options?.minBatchSize ?? this.defaultBatchOptions.minBatchSize ?? 1,
            maxBatchSize: options?.maxBatchSize ?? this.defaultBatchOptions.maxBatchSize ?? 100,
            concurrency: options?.concurrency ?? this.defaultBatchOptions.concurrency ?? Math.max(1, Math.floor(this.maxConcurrentTasks / 2)),
            priority: options?.priority ?? this.defaultBatchOptions.priority ?? TaskPriority.NORMAL,
            timeout: options?.timeout ?? this.defaultBatchOptions.timeout ?? 60000,
            retries: options?.retries ?? this.defaultBatchOptions.retries ?? 3,
            onProgress: options?.onProgress ?? this.defaultBatchOptions.onProgress,
            abortSignal: options?.abortSignal
        };

        // Check if we have items to process
        if (!items || items.length === 0) {
            return [];
        }

        // Create batches
        let batchSize = opts.initialBatchSize;
        let batches = this.createBatches(items, batchSize);

        logger.info(`Processing ${items.length} items in ${batches.length} batches (size: ${batchSize})`);

        // Process batches
        const results: R[] = [];
        let completed = 0;
        let lastAdjustmentTime = Date.now();
        let lastBatchDuration = 0;

        // Create a promise that resolves when all batches are processed
        return new Promise<R[]>(async (resolve, reject) => {
            try {
                // Create a queue of batch indices
                const batchQueue = Array.from({ length: batches.length }, (_, i) => i);
                const activeBatches = new Set<number>();
                const batchResults = new Array<R[]>(batches.length);

                // Process batches concurrently
                const processBatch = async (batchIndex: number) => {
                    if (opts.abortSignal?.aborted) {
                        return;
                    }

                    activeBatches.add(batchIndex);
                    const batch = batches[batchIndex];
                    const startTime = Date.now();

                    try {
                        // Process the batch
                        const result = await this.dispatch<T[], R[]>(
                            TaskType.BATCH_PROCESSING,
                            batch,
                            opts.priority
                        );

                        // Store the result
                        batchResults[batchIndex] = result;

                        // Update completed count
                        completed += batch.length;

                        // Call progress callback
                        if (opts.onProgress) {
                            opts.onProgress(completed, items.length);
                        }

                        // Calculate batch duration
                        const endTime = Date.now();
                        lastBatchDuration = endTime - startTime;

                        // Adjust batch size if needed
                        if (endTime - lastAdjustmentTime > 5000) { // Adjust every 5 seconds
                            batchSize = this.adjustBatchSize(lastBatchDuration, batchSize, opts);
                            lastAdjustmentTime = endTime;

                            // Recreate remaining batches with new size
                            const remainingItems = batchQueue.flatMap(i => batches[i]);
                            if (remainingItems.length > 0) {
                                const newBatches = this.createBatches(remainingItems, batchSize);

                                // Update batch queue and batches
                                batchQueue.length = 0;
                                for (let i = 0; i < newBatches.length; i++) {
                                    batchQueue.push(batches.length + i);
                                }
                                batches = [...batches.slice(0, batches.length), ...newBatches];

                                logger.info(`Adjusted batch size to ${batchSize}, ${batchQueue.length} batches remaining`);
                            }
                        }
                    } catch (error) {
                        logger.error(`Error processing batch ${batchIndex}:`, error);
                        reject(error);
                        return;
                    } finally {
                        activeBatches.delete(batchIndex);
                    }
                };

                // Process batches until queue is empty
                while (batchQueue.length > 0 || activeBatches.size > 0) {
                    // Check if aborted
                    if (opts.abortSignal?.aborted) {
                        reject(new Error('Batch processing aborted'));
                        return;
                    }

                    // Start new batches if we have capacity
                    while (batchQueue.length > 0 && activeBatches.size < opts.concurrency) {
                        const batchIndex = batchQueue.shift()!;
                        processBatch(batchIndex);
                    }

                    // Wait a bit before checking again
                    if (batchQueue.length > 0 || activeBatches.size > 0) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }

                // Combine results
                for (const result of batchResults) {
                    if (result) {
                        results.push(...result);
                    }
                }

                resolve(results);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Create batches from an array of items
     * @param items Array of items
     * @param batchSize Size of each batch
     * @returns Array of batches
     * @private
     */
    private createBatches<T>(items: T[], batchSize: number): T[][] {
        const batches: T[][] = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * Adjust batch size based on processing time and memory pressure
     * @param processingTime Processing time of the last batch in milliseconds
     * @param currentBatchSize Current batch size
     * @param options Batch processing options
     * @returns New batch size
     * @private
     */
    private adjustBatchSize(
        processingTime: number,
        currentBatchSize: number,
        options: Required<BatchOptions>
    ): number {
        // Get memory pressure
        const memUsage = process.memoryUsage();
        const memoryPressure = memUsage.heapUsed / memUsage.heapTotal;

        // High memory pressure: reduce batch size
        if (memoryPressure > 0.8) {
            return Math.max(options.minBatchSize, Math.floor(currentBatchSize * 0.5));
        }

        // Processing time too long: reduce batch size
        if (processingTime > options.timeout * 0.8) {
            return Math.max(options.minBatchSize, Math.floor(currentBatchSize * 0.7));
        }

        // Processing time too short and low memory pressure: increase batch size
        if (processingTime < options.timeout * 0.2 && memoryPressure < 0.5) {
            return Math.min(options.maxBatchSize, Math.floor(currentBatchSize * 1.5));
        }

        // Keep current batch size
        return currentBatchSize;
    }

    /**
     * Wait for all active tasks to complete
     * @param timeout Timeout in milliseconds (default: 30000)
     * @returns Promise that resolves when all tasks are completed or timeout is reached
     */
    public async waitForAll(timeout: number = 30000): Promise<void> {
        if (this.activeTasks.size === 0 && this.taskQueue.length === 0) {
            return;
        }

        logger.info(`Waiting for ${this.activeTasks.size} active tasks and ${this.taskQueue.length} queued tasks to complete...`);

        return new Promise<void>((resolve) => {
            // Create a check interval
            const checkInterval = setInterval(() => {
                if (this.activeTasks.size === 0 && this.taskQueue.length === 0) {
                    clearInterval(checkInterval);
                    clearTimeout(timeoutId);
                    resolve();
                }
            }, 100);

            // Create a timeout
            const timeoutId = setTimeout(() => {
                clearInterval(checkInterval);
                logger.warn(`Timeout waiting for tasks to complete, ${this.activeTasks.size} active tasks and ${this.taskQueue.length} queued tasks remaining`);
                resolve();
            }, timeout);
        });
    }

    /**
     * Cancel all tasks
     */
    public cancelAll(): void {
        logger.info(`Cancelling ${this.taskQueue.length} queued tasks`);

        // Clear the task queue
        const queuedTasks = [...this.taskQueue];
        this.taskQueue = [];

        // Reject all queued tasks
        for (const entry of queuedTasks) {
            entry.reject(new Error('Task cancelled'));
        }

        logger.info(`Cancelled ${queuedTasks.length} queued tasks`);
    }

    /**
     * Terminate the task dispatcher
     * @param force If true, forcefully terminate without waiting for tasks to complete
     * @returns Promise that resolves when the dispatcher is terminated
     */
    public async terminate(force: boolean = false): Promise<void> {
        if (!this.isInitialized) {
            logger.warn('Task dispatcher is not initialized');
            return;
        }

        try {
            if (!force) {
                // Wait for all active tasks to complete
                if (this.activeTasks.size > 0) {
                    logger.info(`Waiting for ${this.activeTasks.size} active tasks to complete...`);
                    await Promise.all(
                        Array.from(this.activeTasks.values()).map(
                            entry => new Promise<void>((resolve) => {
                                const originalResolve = entry.resolve;
                                entry.resolve = (result) => {
                                    originalResolve(result);
                                    resolve();
                                };
                            })
                        )
                    );
                }
            } else {
                // Cancel all queued tasks
                this.cancelAll();
            }

            // Terminate the worker pool
            await this.workerPool.terminate(force);

            this.isInitialized = false;
            logger.info('Task dispatcher terminated');
        } catch (error) {
            logger.error('Failed to terminate task dispatcher:', error);
            throw error;
        }
    }
}

// Export the TaskDispatcher class
export default TaskDispatcher;
