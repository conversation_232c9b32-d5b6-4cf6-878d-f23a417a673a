@echo off
echo Starting Sims 4 Mod Conflict Scanner...

:: Start Streamlit server
start /B cmd /c "streamlit run src/frontend/streamlit/pages/main.py --server.port 7860"

:: Wait for Streamlit to start
timeout /t 5

:: Start TypeScript server
start /B cmd /c "npm run dev:ts"

:: Wait for TypeScript server to start
timeout /t 5

:: Start Electron app
start /B cmd /c "npm start"

echo All components started. Please check the windows that opened.
echo Streamlit UI: http://localhost:7860
echo TypeScript Server: http://localhost:3000
