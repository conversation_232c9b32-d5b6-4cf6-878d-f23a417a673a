import { Logger } from '../../../../../utils/logging/logger.js';
import { DependencyInfo } from '../../../../../types/database.js';
import { parseTgi } from '../../../../../utils/parsing/tgiParser.js';

/**
 * Helper function to create a dependency
 * @param type The resource type
 * @param group The resource group
 * @param instance The resource instance
 * @param referenceType The type of reference
 * @returns A DependencyInfo object
 */
export function createDependency(
    type: number,
    group: number | bigint,
    instance: bigint,
    referenceType: string = 'TuningReference'
): DependencyInfo {
    return {
        resourceId: 0, // Will be set by the caller
        sourceResourceId: 0, // Will be set by the caller
        targetType: type,
        targetGroup: typeof group === 'number' ? BigInt(group) : group,
        targetInstance: instance,
        referenceType,
        timestamp: Date.now()
    };
}

/**
 * Tries to parse a string as a TGI reference
 * @param value The string to parse
 * @param log The logger instance
 * @returns The parsed TGI or undefined if parsing failed
 */
export function tryParseTgi(value: string, log: Logger): { type: number, group: bigint, instance: bigint } | undefined {
    try {
        const parsed = parseTgi(value);
        if (parsed) {
            return parsed;
        }
    } catch (error) {
        log.debug(`Failed to parse TGI from string: ${value}`);
    }
    return undefined;
}

/**
 * Tries to parse a string as an instance ID
 * @param value The string to parse
 * @param log The logger instance
 * @returns The parsed instance ID or undefined if parsing failed
 */
export function tryParseInstanceId(value: string, log: Logger): bigint | undefined {
    try {
        // Try to parse as hex
        if (value.startsWith('0x') || /^[0-9a-fA-F]+$/.test(value)) {
            const hexValue = value.startsWith('0x') ? value : `0x${value}`;
            return BigInt(hexValue);
        }

        // Try to parse as decimal
        if (/^\d+$/.test(value)) {
            return BigInt(value);
        }
    } catch (error) {
        log.debug(`Failed to parse instance ID from string: ${value}`);
    }
    return undefined;
}

/**
 * Determines the resource type based on context
 * @param context The node context
 * @returns The resource type
 */
export function determineResourceTypeFromContext(context: { path: string, tag: string }): number {
    // Check for common patterns that indicate specific types
    if (context.path.includes('trait')) {
        return 0xCB5FDDC7; // Trait
    } else if (context.path.includes('buff')) {
        return 0x6017E896; // Buff
    } else if (context.path.includes('interaction')) {
        return 0xE882D22F; // Interaction
    } else if (context.path.includes('object')) {
        return 0x319E4F1D; // Object Definition
    } else if (context.path.includes('simdata') || context.path.includes('sim_data')) {
        return 0x545AC67A; // SimData
    } else if (context.path.includes('loot')) {
        return 0x0914B438; // Loot
    } else if (context.path.includes('snippet')) {
        return 0x2A5A8B7B; // Snippet
    } else if (context.path.includes('aspiration')) {
        return 0x28B64675; // Aspiration
    } else if (context.path.includes('career')) {
        return 0x0E4D15FB; // Career
    } else if (context.path.includes('skill')) {
        return 0xAC16F634; // Skill
    } else if (context.path.includes('recipe')) {
        return 0xEB97F823; // Recipe
    }

    return 0; // Unknown type
}

/**
 * Determines the reference type based on context
 * @param context The node context
 * @returns The reference type
 */
export function determineReferenceTypeFromContext(context: { path: string, tag: string }): string {
    // Check for common patterns that indicate specific reference types
    if (context.path.includes('trait') || context.tag === 'trait') {
        return 'TraitReference';
    } else if (context.path.includes('buff') || context.tag === 'buff') {
        return 'BuffReference';
    } else if (context.path.includes('interaction') || context.tag === 'interaction') {
        return 'InteractionReference';
    } else if (context.path.includes('object') || context.tag === 'object') {
        return 'ObjectReference';
    } else if (context.path.includes('simdata') || context.path.includes('sim_data')) {
        return 'SimDataReference';
    } else if (context.path.includes('loot') || context.tag === 'loot') {
        return 'LootReference';
    } else if (context.path.includes('snippet') || context.tag === 'snippet') {
        return 'SnippetReference';
    } else if (context.tag === 'T') {
        return 'TElementReference';
    } else if (context.tag === 'V') {
        return 'VElementReference';
    } else if (context.tag === 'L') {
        return 'LElementReference';
    } else if (context.tag === 'U') {
        return 'UElementReference';
    } else if (context.tag === 'E') {
        return 'EElementReference';
    } else if (context.tag === 'M') {
        return 'MElementReference';
    }

    return 'TuningReference'; // Default reference type
}

/**
 * Determines the reference type based on attribute name
 * @param attributeName The attribute name
 * @returns The reference type
 */
export function determineReferenceTypeFromAttribute(attributeName: string): string {
    if (attributeName === 'name' || attributeName === 'n') {
        return 'NameReference';
    } else if (attributeName === 'value' || attributeName === 'v') {
        return 'ValueReference';
    } else if (attributeName === 'type' || attributeName === 't') {
        return 'TypeReference';
    } else if (attributeName === 'instance' || attributeName === 'i') {
        return 'InstanceReference';
    } else if (attributeName === 'class' || attributeName === 'c') {
        return 'ClassReference';
    } else if (attributeName === 's') {
        return 'TgiReference';
    }

    return 'AttributeReference'; // Default attribute reference type
}
