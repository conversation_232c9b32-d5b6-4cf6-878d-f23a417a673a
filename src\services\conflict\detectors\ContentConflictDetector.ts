import { Logger } from '../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../types/database.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/index.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { DatabaseService } from '../../databaseService.js';
import { resourceTypeRegistry } from '../../../utils/resource/resourceTypeRegistry.js';
import { ConflictDetectorBase, ConflictDetectionOptionsBase } from './ConflictDetectorBase.js';
import { calculateSimilarity, SimilarityAlgorithm } from '../../../utils/string/enhancedStringSimilarity.js';
import { ParsedContentRepository } from '../../database/ParsedContentRepository.js';
import { hasXMLConflict, getXMLDifferenceSummary, compareXML } from '../../../utils/xml/xmlComparison.js';

/**
 * Options for content-based conflict detection
 */
export interface ContentConflictDetectionOptions extends ConflictDetectionOptionsBase {
    /**
     * Similarity threshold for content comparison (0.0 to 1.0)
     * Default: 0.7
     */
    similarityThreshold?: number;

    /**
     * Whether to detect binary content conflicts (exact matches)
     * Default: true
     */
    detectBinaryConflicts?: boolean;

    /**
     * Whether to detect semantic content conflicts (similar content)
     * Default: true
     */
    detectSemanticConflicts?: boolean;

    /**
     * Whether to detect structural content conflicts (XML, SimData)
     * Default: true
     */
    detectStructuralConflicts?: boolean;

    /**
     * Maximum content size to compare (in bytes)
     * Default: 1048576 (1 MB)
     */
    maxContentSize?: number;

    /**
     * Whether to log detailed comparison information
     * Default: false
     */
    logComparisonDetails?: boolean;
}

/**
 * Detector for content-based conflicts between resources
 */
export class ContentConflictDetector extends ConflictDetectorBase<ContentConflictDetectionOptions> {
    private parsedContentRepository: ParsedContentRepository;

    /**
     * Create a new content conflict detector
     * @param databaseService Database service instance
     * @param options Options for content conflict detection
     * @param logger Optional logger instance
     */
    constructor(
        databaseService: DatabaseService,
        options: ContentConflictDetectionOptions = {},
        logger?: Logger
    ) {
        super(databaseService, {
            enabled: options.enabled !== false,
            excludeTypes: options.excludeTypes || [],
            includeTypes: options.includeTypes || [],
            similarityThreshold: options.similarityThreshold || 0.7, // Reduced from 0.8 to be more sensitive
            detectBinaryConflicts: options.detectBinaryConflicts !== false,
            detectSemanticConflicts: options.detectSemanticConflicts !== false,
            detectStructuralConflicts: options.detectStructuralConflicts !== false,
            maxContentSize: options.maxContentSize || 1048576, // 1 MB
            logComparisonDetails: options.logComparisonDetails || false
        }, logger || new Logger('ContentConflictDetector'));

        this.parsedContentRepository = new ParsedContentRepository(databaseService.db, this.logger);
        this.logger.debug(`ContentConflictDetector initialized with options: ${JSON.stringify(this.options)}`);
    }

    /**
     * Detect content conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    detectConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Validate resources
        if (!this.validateResource(resource1) || !this.validateResource(resource2)) {
            return null;
        }

        // Skip if either resource is excluded by type
        if (this.shouldSkipResource(resource1) || this.shouldSkipResource(resource2)) {
            return null;
        }

        // Skip if resources have different types
        if (resource1.type !== resource2.type) {
            return null;
        }

        // Skip if resources have the same hash (identical content)
        if (resource1.hash === resource2.hash) {
            return null;
        }

        // Skip if resources are too large to compare
        if (resource1.size > this.options.maxContentSize || resource2.size > this.options.maxContentSize) {
            this.logger.debug(`Skipping content comparison for large resources: ${resource1.id} (${resource1.size} bytes) and ${resource2.id} (${resource2.size} bytes)`);
            return null;
        }

        // Check for binary content conflicts
        if (this.options.detectBinaryConflicts) {
            const binaryConflict = this.detectBinaryContentConflict(resource1, resource2);
            if (binaryConflict) {
                return binaryConflict;
            }
        }

        // Check for semantic content conflicts
        if (this.options.detectSemanticConflicts) {
            const semanticConflict = this.detectSemanticContentConflict(resource1, resource2);
            if (semanticConflict) {
                return semanticConflict;
            }
        }

        // Check for structural content conflicts
        if (this.options.detectStructuralConflicts) {
            const structuralConflict = this.detectStructuralContentConflict(resource1, resource2);
            if (structuralConflict) {
                return structuralConflict;
            }
        }

        return null;
    }

    /**
     * Detect binary content conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectBinaryContentConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // For binary conflicts, we compare the hash of the resources
        // If the hashes are different but the resources have the same name or similar metadata,
        // it might indicate a conflict

        // Check if resources have the same name but different content
        if (resource1.name && resource2.name &&
            resource1.name === resource2.name &&
            resource1.hash !== resource2.hash) {

            const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.type);
            const resourceTypeName = resourceTypeInfo?.name || `0x${resource1.type.toString(16).toUpperCase()}`;
            const severity = this.determineSeverity(resource1.type, 'binary');

            return {
                id: `content-binary-${resource1.id}-${resource2.id}`,
                type: ConflictType.RESOURCE,
                severity,
                description: `Binary content conflict for ${resourceTypeName} resource with name "${resource1.name}"`,
                affectedResources: [
                    this.createResourceKey(resource1),
                    this.createResourceKey(resource2)
                ],
                timestamp: Date.now(),
                recommendations: this.getRecommendations(resource1.type, 'binary'),
                confidence: 0.9
            };
        }

        return null;
    }

    /**
     * Detect semantic content conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private async detectSemanticContentConflict(resource1: ResourceInfo, resource2: ResourceInfo): Promise<ConflictInfo | null> {
        // For semantic conflicts, we compare the content snippets of the resources
        // If the content snippets are similar but not identical, it might indicate a conflict

        if (this.options.logComparisonDetails) {
            this.logger.debug(`Comparing content of resources:
            - Resource 1: ${resource1.resourceType || 'Unknown'} ${resource1.id} (${resource1.packagePath || 'Unknown path'})
            - Resource 2: ${resource2.resourceType || 'Unknown'} ${resource2.id} (${resource2.packagePath || 'Unknown path'})`);
        }

        // Import EnhancedMemoryManager for memory management
        const EnhancedMemoryManager = (await import('../../../utils/memory/enhancedMemoryManager.js')).default;
        const memoryManager = EnhancedMemoryManager.getInstance();

        // Check memory usage before processing
        const memoryBefore = memoryManager.getMemoryStats();
        memoryManager.logMemoryUsage();

        // Track these resources for memory management
        const resourceSize1 = resource1.contentSnippet?.length || 0;
        const resourceSize2 = resource2.contentSnippet?.length || 0;
        memoryManager.trackResource('contentComparison', resourceSize1 + resourceSize2);

        // Skip if either resource doesn't have a content snippet
        if (!resource1.contentSnippet || !resource2.contentSnippet) {
            if (this.options.logComparisonDetails) {
                this.logger.debug(`Missing content snippets, skipping content comparison:
                - Resource 1 snippet: ${resource1.contentSnippet ? 'Present' : 'Missing'}
                - Resource 2 snippet: ${resource2.contentSnippet ? 'Present' : 'Missing'}`);
            }
            return null;
        }

        // Handle large content snippets more efficiently
        const maxSnippetSize = 50000; // Reduced from 100KB to 50KB for better memory management
        if (resource1.contentSnippet.length > maxSnippetSize || resource2.contentSnippet.length > maxSnippetSize) {
            this.logger.debug(`Content snippets too large, using optimized comparison:
            - Resource 1: ${resource1.contentSnippet.length} characters
            - Resource 2: ${resource2.contentSnippet.length} characters
            - Max allowed: ${maxSnippetSize} characters`);

            // For large snippets, first try hash comparison
            if (resource1.hash && resource2.hash) {
                const hashSimilarity = resource1.hash === resource2.hash ? 1.0 : 0.0;
                if (hashSimilarity > 0) {
                    return this.createSemanticConflict(resource1, resource2, hashSimilarity, "hash");
                }
            }

            // If hashes don't match or aren't available, use a chunked similarity check
            // This avoids memory issues with large strings by comparing smaller chunks
            try {
                const chunkSize = 10000; // 10KB chunks
                const numChunks = Math.min(5, Math.floor(Math.min(resource1.contentSnippet.length, resource2.contentSnippet.length) / chunkSize));

                if (numChunks > 0) {
                    let totalSimilarity = 0;
                    let chunksCompared = 0;

                    // Compare chunks from the beginning, middle, and end of the content
                    for (let i = 0; i < numChunks; i++) {
                        // Calculate chunk position (spread throughout the content)
                        const position = Math.floor(i * (resource1.contentSnippet.length - chunkSize) / (numChunks - 1 || 1));

                        // Extract chunks
                        const chunk1 = resource1.contentSnippet.substring(position, position + chunkSize);

                        // Find best matching position in resource2 (with some flexibility)
                        const searchStart = Math.max(0, position - chunkSize);
                        const searchEnd = Math.min(resource2.contentSnippet.length - chunkSize, position + chunkSize);

                        let bestSimilarity = 0;
                        for (let j = searchStart; j <= searchEnd; j += chunkSize / 2) {
                            const chunk2 = resource2.contentSnippet.substring(j, j + chunkSize);
                            const similarity = calculateSimilarity(chunk1, chunk2, {
                                algorithm: SimilarityAlgorithm.COSINE,
                                normalize: true,
                                tokenize: true
                            });

                            if (similarity > bestSimilarity) {
                                bestSimilarity = similarity;
                            }
                        }

                        totalSimilarity += bestSimilarity;
                        chunksCompared++;

                        // Force garbage collection after each chunk comparison
                        if (global.gc) {
                            global.gc();
                        }
                    }

                    const averageSimilarity = totalSimilarity / chunksCompared;

                    if (this.options.logComparisonDetails) {
                        this.logger.debug(`Chunked similarity comparison result: ${averageSimilarity.toFixed(4)} (threshold: ${this.options.similarityThreshold.toFixed(4)})`);
                    }

                    if (averageSimilarity >= this.options.similarityThreshold) {
                        return this.createSemanticConflict(resource1, resource2, averageSimilarity, "chunked");
                    }
                } else {
                    // Fall back to simplified similarity for very small content
                    const simplifiedSimilarity = this.calculateSimplifiedSimilarity(resource1, resource2);
                    if (simplifiedSimilarity >= this.options.similarityThreshold) {
                        return this.createSemanticConflict(resource1, resource2, simplifiedSimilarity, "simplified");
                    }
                }
            } catch (error: any) {
                this.logger.error(`Error in chunked similarity comparison: ${error.message || error}`);

                // Fall back to simplified similarity on error
                const simplifiedSimilarity = this.calculateSimplifiedSimilarity(resource1, resource2);
                if (simplifiedSimilarity >= this.options.similarityThreshold) {
                    return this.createSemanticConflict(resource1, resource2, simplifiedSimilarity, "simplified");
                }
            }

            return null;
        }

        // Log content snippet lengths
        if (this.options.logComparisonDetails) {
            this.logger.debug(`Content snippet lengths:
            - Resource 1: ${resource1.contentSnippet.length} characters
            - Resource 2: ${resource2.contentSnippet.length} characters`);

            // Log first 100 characters of each snippet for debugging
            this.logger.debug(`Content snippet samples:
            - Resource 1: ${resource1.contentSnippet.substring(0, 100)}...
            - Resource 2: ${resource2.contentSnippet.substring(0, 100)}...`);
        }


        // Determine the best algorithm based on content type and length
        let algorithm = SimilarityAlgorithm.BEST;

        // For longer content, use algorithms better suited for that
        if (resource1.contentSnippet.length > 1000 || resource2.contentSnippet.length > 1000) {
            algorithm = SimilarityAlgorithm.COSINE;
        }

        // For very different length content, use Cosine
        if (Math.abs(resource1.contentSnippet.length - resource2.contentSnippet.length) >
            Math.min(resource1.contentSnippet.length, resource2.contentSnippet.length) * 0.5) {
            algorithm = SimilarityAlgorithm.COSINE;
        }

        // For XML-like content, use Dice coefficient
        if ((resource1.contentSnippet.startsWith('<') && resource1.contentSnippet.endsWith('>')) ||
            (resource2.contentSnippet.startsWith('<') && resource2.contentSnippet.endsWith('>'))) {
            algorithm = SimilarityAlgorithm.DICE;
        }

        // Calculate similarity between content snippets
        if (this.options.logComparisonDetails) {
            this.logger.debug(`Using similarity algorithm: ${algorithm}`);
        }

        // Try multiple algorithms to find the best match
        const algorithms = [
            SimilarityAlgorithm.LEVENSHTEIN,
            SimilarityAlgorithm.JARO_WINKLER,
            SimilarityAlgorithm.COSINE,
            SimilarityAlgorithm.DICE
        ];

        const similarities = algorithms.map(alg => {
            const sim = calculateSimilarity(
                resource1.contentSnippet!,
                resource2.contentSnippet!,
                {
                    algorithm: alg,
                    normalize: true,
                    tokenize: alg === SimilarityAlgorithm.COSINE || alg === SimilarityAlgorithm.DICE
                }
            );
            if (this.options.logComparisonDetails) {
                this.logger.debug(`Similarity with ${alg}: ${sim.toFixed(4)}`);
            }
            return sim;
        });

        // Use the selected algorithm for the actual comparison
        const similarity = calculateSimilarity(
            resource1.contentSnippet,
            resource2.contentSnippet,
            {
                algorithm,
                normalize: true,
                tokenize: algorithm === SimilarityAlgorithm.COSINE || algorithm === SimilarityAlgorithm.DICE
            }
        );

        if (this.options.logComparisonDetails) {
            this.logger.debug(`Final similarity score: ${similarity.toFixed(4)} (threshold: ${this.options.similarityThreshold.toFixed(4)})`);
        }


        // If similarity is above threshold but not identical, it's a potential conflict
        // We're looking for resources that are similar enough to be related but different enough to be in conflict
        if ((similarity >= this.options.similarityThreshold && similarity < 0.95) ||
            (similarities.some(sim => sim >= this.options.similarityThreshold && sim < 0.95))) {
            this.logger.info(`Potential content conflict detected with similarity ${similarity.toFixed(4)}`);
            if (this.options.logComparisonDetails) {
                this.logger.debug(`Detailed conflict information:
                - Resource 1: ${resource1.resourceType || 'Unknown'} ${resource1.id} (${resource1.packagePath || 'Unknown path'})
                - Resource 2: ${resource2.resourceType || 'Unknown'} ${resource2.id} (${resource2.packagePath || 'Unknown path'})
                - Similarity: ${similarity.toFixed(4)}
                - Threshold: ${this.options.similarityThreshold.toFixed(4)}`);
            }

            const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.type);
            const resourceTypeName = resourceTypeInfo?.name || `0x${resource1.type.toString(16).toUpperCase()}`;
            const severity = this.determineSeverity(resource1.type, 'semantic');

            // Get more detailed information about the similarity
            let detailedDescription = `Semantic content conflict for ${resourceTypeName} resource (${Math.round(similarity * 100)}% similar)`;

            // Add resource identification information
            detailedDescription += `\nResource 1: ${resource1.resourceType || 'Unknown'} from ${resource1.packagePath || 'Unknown path'}`;
            detailedDescription += `\nResource 2: ${resource2.resourceType || 'Unknown'} from ${resource2.packagePath || 'Unknown path'}`;

            // Add algorithm information
            detailedDescription += `\nSimilarity algorithm: ${algorithm}`;

            // Add all algorithm results
            detailedDescription += `\nAll similarity scores:`;
            algorithms.forEach((alg, index) => {
                detailedDescription += `\n  - ${alg}: ${Math.round(similarities[index] * 100)}%`;
            });

            // Add content length information
            detailedDescription += `\nContent lengths: ${resource1.contentSnippet.length} and ${resource2.contentSnippet.length} characters`;

            // Add content snippet samples
            detailedDescription += `\nContent snippet samples:`;
            detailedDescription += `\n  - Resource 1: ${resource1.contentSnippet.substring(0, 50)}...`;
            detailedDescription += `\n  - Resource 2: ${resource2.contentSnippet.substring(0, 50)}...`;

            return {
                id: `content-semantic-${resource1.id}-${resource2.id}`,
                type: ConflictType.RESOURCE,
                severity,
                description: detailedDescription,
                affectedResources: [
                    this.createResourceKey(resource1),
                    this.createResourceKey(resource2)
                ],
                timestamp: Date.now(),
                recommendations: this.getRecommendations(resource1.type, 'semantic'),
                confidence: similarity
            };
        }

        // Cleanup memory
        memoryManager.untrackResource('contentComparison', resourceSize1 + resourceSize2);

        // Force garbage collection if memory usage has increased significantly
        const memoryAfter = memoryManager.getMemoryStats();
        if (memoryAfter.heapUsed > memoryBefore.heapUsed * 1.5) {
            this.logger.debug(`Memory usage increased significantly during content comparison, forcing garbage collection`);
            memoryManager.forceGarbageCollection();
        }

        return null;
    }

    /**
     * Detect structural content conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectStructuralContentConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // For structural conflicts, we need to parse the content and compare the structure
        // This is more complex and depends on the resource type

        // Check resource type to determine appropriate structural comparison
        switch (resource1.resourceType) {
            case 'TUNING':
            case 'XML':
                return this.detectTuningConflict(resource1, resource2);
            case 'SIMDATA':
                return this.detectSimDataConflict(resource1, resource2);
            case 'SCRIPT':
            case 'SCRIPT_MODULE':
                return this.detectScriptConflict(resource1, resource2);
            default:
                return null;
        }
    }

    /**
     * Detect conflicts between tuning XML resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectTuningConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Get parsed content for both resources
        const parsedContent1 = this.getParsedContent(resource1.id, 'XML');
        const parsedContent2 = this.getParsedContent(resource2.id, 'XML');

        // Skip if either resource doesn't have parsed content
        if (!parsedContent1 || !parsedContent2) {
            return null;
        }

        try {
            // Define critical paths for tuning XML
            const criticalPaths = [
                'SimData',
                'I',
                'instance',
                'module_tuning',
                'class',
                'instance_id',
                'guid',
                'type',
                'name',
                'value',
                'key',
                'tunable',
                'reference'
            ];

            // Compare XML content
            const comparisonResult = compareXML(parsedContent1, parsedContent2, {
                ignoreWhitespace: true,
                ignoreComments: true,
                ignoreOrder: false,
                ignoreAttributes: false,
                criticalPaths,
                similarityThreshold: this.options.similarityThreshold
            });

            // If there are significant differences, it's a conflict
            if (comparisonResult.significant) {
                // Get a summary of the differences
                const differenceSummary = getXMLDifferenceSummary(parsedContent1, parsedContent2, {
                    criticalPaths,
                    similarityThreshold: this.options.similarityThreshold
                });

                // Create conflict info
                return {
                    id: `content-tuning-${resource1.id}-${resource2.id}`,
                    type: ConflictType.TUNING,
                    severity: ConflictSeverity.HIGH,
                    description: `Tuning XML conflict for resources with similar structure but different values\n${differenceSummary}`,
                    affectedResources: [
                        this.createResourceKey(resource1),
                        this.createResourceKey(resource2)
                    ],
                    timestamp: Date.now(),
                    recommendations: [
                        'Use Sims 4 Studio or XML Compare to view differences',
                        'Load conflicting mods in the correct order if one should override the other',
                        'Consider merging the changes or choosing one version'
                    ],
                    confidence: comparisonResult.similarity
                };
            }
        } catch (error) {
            this.logger.error(`Error comparing tuning XML resources: ${error.message || error}`);
        }

        return null;
    }

    /**
     * Detect conflicts between SimData resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectSimDataConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Get parsed content for both resources
        const parsedContent1 = this.getParsedContent(resource1.id, 'SIMDATA');
        const parsedContent2 = this.getParsedContent(resource2.id, 'SIMDATA');

        // Skip if either resource doesn't have parsed content
        if (!parsedContent1 || !parsedContent2) {
            return null;
        }

        try {
            // Parse SimData content
            const simData1 = JSON.parse(parsedContent1);
            const simData2 = JSON.parse(parsedContent2);

            // Compare schema and instances
            if (this.hasSimDataConflict(simData1, simData2)) {
                return {
                    id: `content-simdata-${resource1.id}-${resource2.id}`,
                    type: ConflictType.RESOURCE,
                    severity: ConflictSeverity.HIGH,
                    description: `SimData conflict for resources with same schema but different instance values`,
                    affectedResources: [
                        this.createResourceKey(resource1),
                        this.createResourceKey(resource2)
                    ],
                    timestamp: Date.now(),
                    recommendations: [
                        'SimData conflicts can cause gameplay issues',
                        'Check if both mods modify the same game object or functionality',
                        'Load conflicting mods in the correct order if one should override the other',
                        'Consider merging the changes or choosing one version'
                    ],
                    confidence: 0.85
                };
            }
        } catch (error) {
            this.logger.error(`Error comparing SimData resources: ${error.message || error}`);
        }

        return null;
    }

    /**
     * Detect conflicts between script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectScriptConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Get parsed content for both resources
        const parsedContent1 = this.getParsedContent(resource1.id, 'SCRIPT');
        const parsedContent2 = this.getParsedContent(resource2.id, 'SCRIPT');

        // Skip if either resource doesn't have parsed content
        if (!parsedContent1 || !parsedContent2) {
            return null;
        }

        try {
            // Parse script content
            const script1 = JSON.parse(parsedContent1);
            const script2 = JSON.parse(parsedContent2);

            // Check for class conflicts
            const classConflict = this.checkScriptClassConflict(script1, script2);
            if (classConflict) {
                return this.createScriptConflictInfo(
                    resource1,
                    resource2,
                    `Class conflict: ${classConflict}`,
                    ConflictSeverity.CRITICAL,
                    0.95
                );
            }

            // Check for function conflicts
            const functionConflict = this.checkScriptFunctionConflict(script1, script2);
            if (functionConflict) {
                return this.createScriptConflictInfo(
                    resource1,
                    resource2,
                    `Function conflict: ${functionConflict}`,
                    ConflictSeverity.HIGH,
                    0.9
                );
            }

            // Check for import conflicts
            const importConflict = this.checkScriptImportConflict(script1, script2);
            if (importConflict) {
                return this.createScriptConflictInfo(
                    resource1,
                    resource2,
                    `Import conflict: ${importConflict}`,
                    ConflictSeverity.MEDIUM,
                    0.8
                );
            }

            // Check for global variable conflicts
            const globalConflict = this.checkScriptGlobalConflict(script1, script2);
            if (globalConflict) {
                return this.createScriptConflictInfo(
                    resource1,
                    resource2,
                    `Global variable conflict: ${globalConflict}`,
                    ConflictSeverity.HIGH,
                    0.85
                );
            }

            // Check for general script conflicts
            if (this.hasScriptConflict(script1, script2)) {
                return this.createScriptConflictInfo(
                    resource1,
                    resource2,
                    `Script conflict detected between modules ${script1.name} and ${script2.name}`,
                    ConflictSeverity.MEDIUM,
                    0.75
                );
            }
        } catch (error) {
            this.logger.error(`Error comparing script resources: ${error.message || error}`);
        }

        return null;
    }

    /**
     * Create a script conflict info object
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param description Conflict description
     * @param severity Conflict severity
     * @param confidence Confidence score
     * @returns ConflictInfo object
     */
    private createScriptConflictInfo(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        description: string,
        severity: ConflictSeverity,
        confidence: number
    ): ConflictInfo {
        return {
            id: `content-script-${resource1.id}-${resource2.id}`,
            type: ConflictType.SCRIPT,
            severity,
            description,
            affectedResources: [
                this.createResourceKey(resource1),
                this.createResourceKey(resource2)
            ],
            timestamp: Date.now(),
            recommendations: [
                'Script conflicts can cause game crashes or unexpected behavior',
                'Only use one of the conflicting script mods',
                'Check if updated versions are available',
                'Load conflicting mods in the correct order if one should override the other',
                'Contact the mod authors to report the conflict'
            ],
            confidence
        };
    }

    // The hasTuningConflict method has been replaced by the hasXMLConflict utility

    /**
     * Check if two SimData objects have a conflict
     * @param simData1 First SimData object
     * @param simData2 Second SimData object
     * @returns True if a conflict is detected, false otherwise
     */
    private hasSimDataConflict(simData1: any, simData2: any): boolean {
        // Check if both SimData objects have schemas
        if (!simData1.schema || !simData2.schema) {
            return false;
        }

        // Check schema compatibility
        const schemaConflict = this.checkSimDataSchemaConflict(simData1.schema, simData2.schema);
        if (schemaConflict) {
            this.logger.debug(`SimData schema conflict detected: ${schemaConflict}`);
            return true;
        }

        // Check if both SimData objects have instances
        if (!simData1.instances || !simData2.instances) {
            return false;
        }

        // Get all instance IDs from both SimData objects
        const instanceIds1 = Object.keys(simData1.instances);
        const instanceIds2 = Object.keys(simData2.instances);

        // Check if there are common instance IDs
        const commonInstanceIds = instanceIds1.filter(id => instanceIds2.includes(id));
        if (commonInstanceIds.length === 0) {
            return false;
        }

        // Check for conflicts in common instances
        for (const instanceId of commonInstanceIds) {
            const instance1 = simData1.instances[instanceId];
            const instance2 = simData2.instances[instanceId];

            // Check for conflicts in instance values
            const instanceConflict = this.checkSimDataInstanceConflict(instance1, instance2, simData1.schema);
            if (instanceConflict) {
                this.logger.debug(`SimData instance conflict detected for instance ${instanceId}: ${instanceConflict}`);
                return true;
            }
        }

        return false;
    }

    /**
     * Check if two SimData schemas have a conflict
     * @param schema1 First SimData schema
     * @param schema2 Second SimData schema
     * @returns Conflict description if a conflict is detected, null otherwise
     */
    private checkSimDataSchemaConflict(schema1: any, schema2: any): string | null {
        // Check if schemas have the same name
        if (schema1.name !== schema2.name) {
            return `Schema names differ: ${schema1.name} vs ${schema2.name}`;
        }

        // Check if schemas have the same version
        if (schema1.version !== schema2.version) {
            // Different versions might be compatible, so this is not always a conflict
            // For now, we'll consider it a potential conflict
            return `Schema versions differ: ${schema1.version} vs ${schema2.version}`;
        }

        // Check if schemas have the same columns
        if (!schema1.columns || !schema2.columns) {
            return null;
        }

        // Check if the number of columns is the same
        if (schema1.columns.length !== schema2.columns.length) {
            return `Different number of columns: ${schema1.columns.length} vs ${schema2.columns.length}`;
        }

        // Check if the columns are the same
        for (let i = 0; i < schema1.columns.length; i++) {
            const column1 = schema1.columns[i];
            const column2 = schema2.columns[i];

            // Check if column names are the same
            if (column1.name !== column2.name) {
                return `Column names differ at index ${i}: ${column1.name} vs ${column2.name}`;
            }

            // Check if column types are the same
            if (column1.type !== column2.type) {
                return `Column types differ for ${column1.name}: ${column1.type} vs ${column2.type}`;
            }
        }

        return null;
    }

    /**
     * Check if two SimData instances have a conflict
     * @param instance1 First SimData instance
     * @param instance2 Second SimData instance
     * @param schema SimData schema
     * @returns Conflict description if a conflict is detected, null otherwise
     */
    private checkSimDataInstanceConflict(instance1: any, instance2: any, schema: any): string | null {
        // Get all keys from both instances
        const keys1 = Object.keys(instance1);
        const keys2 = Object.keys(instance2);

        // Check if the number of keys is the same
        if (keys1.length !== keys2.length) {
            return `Different number of keys: ${keys1.length} vs ${keys2.length}`;
        }

        // Check if the keys are the same
        const allKeys = new Set([...keys1, ...keys2]);

        // Get critical columns from schema
        const criticalColumns = schema.columns
            ? schema.columns
                .filter((col: any) => this.isSimDataColumnCritical(col.name))
                .map((col: any) => col.name)
            : [];

        // Check each key
        for (const key of allKeys) {
            // Skip if key doesn't exist in one of the instances
            if (!(key in instance1) || !(key in instance2)) {
                continue;
            }

            // Check if values are different
            if (instance1[key] !== instance2[key]) {
                // Check if this is a critical column
                if (criticalColumns.includes(key)) {
                    return `Critical value differs for ${key}: ${instance1[key]} vs ${instance2[key]}`;
                }

                // Non-critical column, but still a difference
                return `Value differs for ${key}: ${instance1[key]} vs ${instance2[key]}`;
            }
        }

        return null;
    }

    /**
     * Check if a SimData column is critical
     * @param columnName Column name
     * @returns True if the column is critical, false otherwise
     */
    private isSimDataColumnCritical(columnName: string): boolean {
        // Critical column names
        const criticalColumns = [
            'id',
            'instance_id',
            'guid',
            'type',
            'name',
            'value',
            'key',
            'tunable',
            'reference',
            'class',
            'module',
            'function'
        ];

        // Check if column name is in the list of critical columns
        return criticalColumns.some(name =>
            columnName.toLowerCase().includes(name.toLowerCase())
        );
    }

    /**
     * Check if two script objects have a conflict
     * @param script1 First script object
     * @param script2 Second script object
     * @returns True if a conflict is detected, false otherwise
     */
    private hasScriptConflict(script1: any, script2: any): boolean {
        // Check if both scripts have names
        if (!script1.name || !script2.name) {
            return false;
        }

        // Check if both scripts have the same module name
        if (script1.name !== script2.name) {
            // Different module names, but check if they're related
            // For example, 'foo_bar' and 'foo_bar_extension' might be related
            const name1 = script1.name.toLowerCase();
            const name2 = script2.name.toLowerCase();

            // Check if one name is a substring of the other
            if (name1.includes(name2) || name2.includes(name1)) {
                this.logger.debug(`Related script modules detected: ${script1.name} and ${script2.name}`);
                // Continue checking for conflicts
            } else {
                // Unrelated modules, no conflict
                return false;
            }
        }

        // Check for class conflicts
        const classConflict = this.checkScriptClassConflict(script1, script2);
        if (classConflict) {
            this.logger.debug(`Script class conflict detected: ${classConflict}`);
            return true;
        }

        // Check for function conflicts
        const functionConflict = this.checkScriptFunctionConflict(script1, script2);
        if (functionConflict) {
            this.logger.debug(`Script function conflict detected: ${functionConflict}`);
            return true;
        }

        // Check for import conflicts
        const importConflict = this.checkScriptImportConflict(script1, script2);
        if (importConflict) {
            this.logger.debug(`Script import conflict detected: ${importConflict}`);
            return true;
        }

        // Check for global variable conflicts
        const globalConflict = this.checkScriptGlobalConflict(script1, script2);
        if (globalConflict) {
            this.logger.debug(`Script global variable conflict detected: ${globalConflict}`);
            return true;
        }

        return false;
    }

    /**
     * Check if two scripts have conflicting classes
     * @param script1 First script object
     * @param script2 Second script object
     * @returns Conflict description if a conflict is detected, null otherwise
     */
    private checkScriptClassConflict(script1: any, script2: any): string | null {
        // Check if both scripts have classes
        if (!script1.classes || !script2.classes) {
            return null;
        }

        // Get class names from both scripts
        const classNames1 = script1.classes.map((cls: any) => cls.name);
        const classNames2 = script2.classes.map((cls: any) => cls.name);

        // Find common class names
        const commonClassNames = classNames1.filter((name: string) => classNames2.includes(name));

        // If there are no common class names, no conflict
        if (commonClassNames.length === 0) {
            return null;
        }

        // Check each common class for conflicts
        for (const className of commonClassNames) {
            // Get class objects
            const class1 = script1.classes.find((cls: any) => cls.name === className);
            const class2 = script2.classes.find((cls: any) => cls.name === className);

            // Check if classes have different base classes
            if (class1.base !== class2.base) {
                return `Class ${className} has different base classes: ${class1.base} vs ${class2.base}`;
            }

            // Check if classes have different methods
            if (class1.methods && class2.methods) {
                const methodNames1 = class1.methods.map((method: any) => method.name);
                const methodNames2 = class2.methods.map((method: any) => method.name);

                // Find common method names
                const commonMethodNames = methodNames1.filter((name: string) => methodNames2.includes(name));

                // If there are common method names, check for conflicts
                for (const methodName of commonMethodNames) {
                    // Get method objects
                    const method1 = class1.methods.find((method: any) => method.name === methodName);
                    const method2 = class2.methods.find((method: any) => method.name === methodName);

                    // Check if methods have different parameter counts
                    if (method1.params && method2.params && method1.params.length !== method2.params.length) {
                        return `Method ${className}.${methodName} has different parameter counts: ${method1.params.length} vs ${method2.params.length}`;
                    }

                    // Check if methods have different code sizes (a rough indicator of different implementations)
                    if (method1.code && method2.code && Math.abs(method1.code.length - method2.code.length) > 10) {
                        return `Method ${className}.${methodName} has significantly different implementations`;
                    }
                }
            }
        }

        // No specific conflicts found, but there are common classes
        // This is still a potential conflict
        return `Common classes found: ${commonClassNames.join(', ')}`;
    }

    /**
     * Check if two scripts have conflicting functions
     * @param script1 First script object
     * @param script2 Second script object
     * @returns Conflict description if a conflict is detected, null otherwise
     */
    private checkScriptFunctionConflict(script1: any, script2: any): string | null {
        // Check if both scripts have functions
        if (!script1.functions || !script2.functions) {
            return null;
        }

        // Get function names from both scripts
        const functionNames1 = script1.functions.map((func: any) => func.name);
        const functionNames2 = script2.functions.map((func: any) => func.name);

        // Find common function names
        const commonFunctionNames = functionNames1.filter((name: string) => functionNames2.includes(name));

        // If there are no common function names, no conflict
        if (commonFunctionNames.length === 0) {
            return null;
        }

        // Check each common function for conflicts
        for (const functionName of commonFunctionNames) {
            // Get function objects
            const function1 = script1.functions.find((func: any) => func.name === functionName);
            const function2 = script2.functions.find((func: any) => func.name === functionName);

            // Check if functions have different parameter counts
            if (function1.params && function2.params && function1.params.length !== function2.params.length) {
                return `Function ${functionName} has different parameter counts: ${function1.params.length} vs ${function2.params.length}`;
            }

            // Check if functions have different code sizes (a rough indicator of different implementations)
            if (function1.code && function2.code && Math.abs(function1.code.length - function2.code.length) > 10) {
                return `Function ${functionName} has significantly different implementations`;
            }
        }

        // No specific conflicts found, but there are common functions
        // This is still a potential conflict
        return `Common functions found: ${commonFunctionNames.join(', ')}`;
    }

    /**
     * Check if two scripts have conflicting imports
     * @param script1 First script object
     * @param script2 Second script object
     * @returns Conflict description if a conflict is detected, null otherwise
     */
    private checkScriptImportConflict(script1: any, script2: any): string | null {
        // Check if both scripts have imports
        if (!script1.imports || !script2.imports) {
            return null;
        }

        // Check if scripts import the same modules but with different aliases
        for (const import1 of script1.imports) {
            for (const import2 of script2.imports) {
                if (import1.module === import2.module && import1.alias !== import2.alias) {
                    return `Module ${import1.module} imported with different aliases: ${import1.alias} vs ${import2.alias}`;
                }
            }
        }

        return null;
    }

    /**
     * Check if two scripts have conflicting global variables
     * @param script1 First script object
     * @param script2 Second script object
     * @returns Conflict description if a conflict is detected, null otherwise
     */
    private checkScriptGlobalConflict(script1: any, script2: any): string | null {
        // Check if both scripts have globals
        if (!script1.globals || !script2.globals) {
            return null;
        }

        // Get global variable names from both scripts
        const globalNames1 = script1.globals.map((global: any) => global.name);
        const globalNames2 = script2.globals.map((global: any) => global.name);

        // Find common global variable names
        const commonGlobalNames = globalNames1.filter((name: string) => globalNames2.includes(name));

        // If there are no common global variable names, no conflict
        if (commonGlobalNames.length === 0) {
            return null;
        }

        // Check each common global variable for conflicts
        for (const globalName of commonGlobalNames) {
            // Get global variable objects
            const global1 = script1.globals.find((global: any) => global.name === globalName);
            const global2 = script2.globals.find((global: any) => global.name === globalName);

            // Check if global variables have different types
            if (global1.type !== global2.type) {
                return `Global variable ${globalName} has different types: ${global1.type} vs ${global2.type}`;
            }

            // Check if global variables have different values
            if (global1.value !== global2.value) {
                return `Global variable ${globalName} has different values: ${global1.value} vs ${global2.value}`;
            }
        }

        // No specific conflicts found, but there are common global variables
        // This is still a potential conflict
        return `Common global variables found: ${commonGlobalNames.join(', ')}`;
    }

    /**
     * Get parsed content from the database
     * @param resourceId Resource ID
     * @param contentType Content type
     * @returns Parsed content or null if not found
     */
    private getParsedContent(resourceId: number, contentType: string): string | null {
        try {
            const parsedContent = this.parsedContentRepository.getParsedContentByResourceId(resourceId, contentType);
            return parsedContent?.content || null;
        } catch (error) {
            this.logger.error(`Error getting parsed content for resource ${resourceId}: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Create a ResourceKey object from a ResourceInfo object
     * @param resource The resource info
     * @returns ResourceKey object
     */
    private createResourceKey(resource: ResourceInfo): ResourceKey {
        return {
            type: resource.type,
            group: BigInt(resource.group),
            instance: BigInt(resource.instance)
        };
    }

    /**
     * Calculate a simplified similarity score between two resources
     * This method is optimized for memory usage and performance
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Similarity score between 0.0 and 1.0
     */
    private calculateSimplifiedSimilarity(resource1: ResourceInfo, resource2: ResourceInfo): number {
        try {
            // If we have hashes, use them for a quick comparison
            if (resource1.hash && resource2.hash) {
                return resource1.hash === resource2.hash ? 1.0 : 0.0;
            }

            // If we don't have content snippets, we can't compare
            if (!resource1.contentSnippet || !resource2.contentSnippet) {
                return 0.0;
            }

            // Compare content length as a simple heuristic
            const lengthRatio = Math.min(
                resource1.contentSnippet.length / resource2.contentSnippet.length,
                resource2.contentSnippet.length / resource1.contentSnippet.length
            );

            // If lengths are very different, they're probably not similar
            if (lengthRatio < 0.5) {
                return 0.0;
            }

            // Compare the first and last 1000 characters
            const prefixSize = Math.min(1000, Math.min(resource1.contentSnippet.length, resource2.contentSnippet.length) / 2);
            const suffixSize = Math.min(1000, Math.min(resource1.contentSnippet.length, resource2.contentSnippet.length) / 2);

            const prefix1 = resource1.contentSnippet.substring(0, prefixSize);
            const prefix2 = resource2.contentSnippet.substring(0, prefixSize);

            const suffix1 = resource1.contentSnippet.substring(resource1.contentSnippet.length - suffixSize);
            const suffix2 = resource2.contentSnippet.substring(resource2.contentSnippet.length - suffixSize);

            // Calculate similarity for prefix and suffix
            const prefixSimilarity = calculateSimilarity(prefix1, prefix2, {
                algorithm: SimilarityAlgorithm.COSINE,
                normalize: true,
                tokenize: true
            });

            const suffixSimilarity = calculateSimilarity(suffix1, suffix2, {
                algorithm: SimilarityAlgorithm.COSINE,
                normalize: true,
                tokenize: true
            });

            // Calculate overall similarity as weighted average
            const overallSimilarity = (prefixSimilarity * 0.6) + (suffixSimilarity * 0.4);

            return overallSimilarity;
        } catch (error: any) {
            this.logger.error(`Error calculating simplified similarity: ${error.message || error}`);
            return 0.0;
        }
    }

    /**
     * Determine the severity of a conflict based on the resource type and conflict type
     * @param resourceType The resource type
     * @param conflictType The type of conflict (binary, semantic, structural)
     * @returns The conflict severity
     */
    private determineSeverity(resourceType: number, conflictType: 'binary' | 'semantic' | 'structural'): ConflictSeverity {
        // Critical resource types
        const criticalTypes = [
            0x220557DA, // Tuning XML
            0x545AC67A, // SimData
            0x03B33DDF, // OBJD (Object Definition)
            0x0166038C  // STBL (String Table)
        ];

        // High severity resource types
        const highSeverityTypes = [
            0x00B2D882, // TXTR (Texture)
            0xE882D22F, // THUM (Thumbnail)
            0x0C772E27, // MODL (Model)
            0x736884F1, // MLOD (Model LOD)
            0x01661233, // CASP (CAS Part)
            0x319E4F1D, // BGEO (Bone Geometry)
            0x00AE6C67, // BOND (Bone Data)
            0x02D5DF13, // GEOM (Geometry)
            0x0333406C, // SIMO (Sim Outfit)
            0x025ED6F4  // MTST (Material Set)
        ];

        // For binary conflicts, use higher severity
        if (conflictType === 'binary') {
            if (criticalTypes.includes(resourceType)) {
                return ConflictSeverity.CRITICAL;
            } else if (highSeverityTypes.includes(resourceType)) {
                return ConflictSeverity.HIGH;
            } else {
                return ConflictSeverity.MEDIUM;
            }
        }

        // For semantic conflicts, reduce severity by one level
        if (conflictType === 'semantic') {
            if (criticalTypes.includes(resourceType)) {
                return ConflictSeverity.HIGH;
            } else if (highSeverityTypes.includes(resourceType)) {
                return ConflictSeverity.MEDIUM;
            } else {
                return ConflictSeverity.LOW;
            }
        }

        // For structural conflicts, use higher severity for critical types
        if (conflictType === 'structural') {
            if (criticalTypes.includes(resourceType)) {
                return ConflictSeverity.CRITICAL;
            } else if (highSeverityTypes.includes(resourceType)) {
                return ConflictSeverity.HIGH;
            } else {
                return ConflictSeverity.MEDIUM;
            }
        }

        // Default to MEDIUM severity
        return ConflictSeverity.MEDIUM;
    }

    /**
     * Get recommendations for resolving a conflict
     * @param resourceType The resource type
     * @param conflictType The type of conflict (binary, semantic, structural)
     * @returns Array of recommendations
     */
    private getRecommendations(resourceType: number, conflictType: 'binary' | 'semantic' | 'structural'): string[] {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resourceType);
        const resourceTypeName = resourceTypeInfo?.name || `0x${resourceType.toString(16).toUpperCase()}`;

        const baseRecommendations = [
            `Check if both mods are compatible with each other`,
            `Load mods in the correct order to ensure proper overrides`,
            `Consider using only one of the conflicting mods`
        ];

        // Add specific recommendations based on resource type
        if (resourceType === 0x220557DA) { // Tuning XML
            return [
                `Tuning XML conflicts can cause gameplay issues`,
                `Use Sims 4 Studio or XML Compare to view differences`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider merging the changes or choosing one version`
            ];
        } else if (resourceType === 0x545AC67A) { // SimData
            return [
                `SimData conflicts can cause gameplay issues`,
                `Check if both mods modify the same game object or functionality`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider merging the changes or choosing one version`
            ];
        } else if (resourceType === 0x03B33DDF) { // OBJD (Object Definition)
            return [
                `Object Definition conflicts can cause objects to behave incorrectly`,
                `Check if both mods modify the same object`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider using only one of the conflicting mods`
            ];
        } else if (resourceType === 0x0166038C) { // STBL (String Table)
            return [
                `String Table conflicts can cause text issues in-game`,
                `Check if both mods modify the same strings`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider merging the string changes or choosing one version`
            ];
        } else if (resourceType === 0x00B2D882) { // TXTR (Texture)
            return [
                `Texture conflicts can cause visual issues`,
                `Check if both mods modify the same texture`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider using only one of the conflicting texture mods`
            ];
        }

        // Default recommendations based on conflict type
        if (conflictType === 'binary') {
            return [
                `Binary content conflict for ${resourceTypeName} resource`,
                `Resources have identical names but different content`,
                ...baseRecommendations
            ];
        } else if (conflictType === 'semantic') {
            return [
                `Semantic content conflict for ${resourceTypeName} resource`,
                `Resources have similar content but with differences`,
                `This may indicate mods that modify the same game feature`,
                ...baseRecommendations
            ];
        } else if (conflictType === 'structural') {
            return [
                `Structural content conflict for ${resourceTypeName} resource`,
                `Resources have similar structure but different values`,
                `This may indicate mods that modify the same game feature in different ways`,
                ...baseRecommendations
            ];
        }

        return baseRecommendations;
    }

    // Using validateResource from ConflictDetectorBase

    /**
     * Create a semantic conflict between two resources
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param similarity Similarity score
     * @param algorithmName Name of the algorithm used
     * @returns ConflictInfo object
     */
    private createSemanticConflict(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        similarity: number,
        algorithmName: string
    ): ConflictInfo {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.type);
        const resourceTypeName = resourceTypeInfo?.name || `0x${resource1.type.toString(16).toUpperCase()}`;
        const severity = this.determineSeverity(resource1.type, 'semantic');

        // Create conflict info
        return {
            id: `content-semantic-${resource1.id}-${resource2.id}`,
            type: ConflictType.RESOURCE,
            severity,
            description: `Semantic content conflict for ${resourceTypeName} resource (${Math.round(similarity * 100)}% similar)`,
            affectedResources: [
                this.createResourceKey(resource1),
                this.createResourceKey(resource2)
            ],
            timestamp: Date.now(),
            recommendations: this.getRecommendations(resource1.type, 'semantic'),
            confidence: similarity
        };
    }

    /**
     * Calculate a simplified similarity score between two resources
     * This method is used for large content snippets to avoid memory issues
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns Similarity score between 0.0 and 1.0
     */
    private calculateSimplifiedSimilarity(resource1: ResourceInfo, resource2: ResourceInfo): number {
        // If we have no content snippets, return 0
        if (!resource1.contentSnippet || !resource2.contentSnippet) {
            return 0;
        }

        // For very large strings, just compare the first and last parts
        if (resource1.contentSnippet.length > 100000 || resource2.contentSnippet.length > 100000) {
            // Take small samples from the beginning and end
            const headSize = 500;
            const tailSize = 500;

            const head1 = resource1.contentSnippet.substring(0, headSize);
            const head2 = resource2.contentSnippet.substring(0, headSize);

            const tail1 = resource1.contentSnippet.substring(Math.max(0, resource1.contentSnippet.length - tailSize));
            const tail2 = resource2.contentSnippet.substring(Math.max(0, resource2.contentSnippet.length - tailSize));

            // Calculate similarity for head and tail
            const headDistance = this.levenshteinDistance(head1, head2);
            const headMaxLength = Math.max(head1.length, head2.length);
            const headSimilarity = 1.0 - (headDistance / headMaxLength);

            const tailDistance = this.levenshteinDistance(tail1, tail2);
            const tailMaxLength = Math.max(tail1.length, tail2.length);
            const tailSimilarity = 1.0 - (tailDistance / tailMaxLength);

            // Return average of head and tail similarity
            return (headSimilarity + tailSimilarity) / 2;
        }

        // For medium-sized strings, use a sampling approach
        const sampleSize = 500; // Characters to sample (reduced from 1000)
        const samples = 3; // Number of samples to take (reduced from 5)

        let totalSimilarity = 0;

        // Take samples from different parts of the content
        for (let i = 0; i < samples; i++) {
            // Calculate position to start sampling
            const position1 = Math.floor(i * (resource1.contentSnippet.length / samples));
            const position2 = Math.floor(i * (resource2.contentSnippet.length / samples));

            // Extract samples
            const sample1 = resource1.contentSnippet.substring(position1, Math.min(position1 + sampleSize, resource1.contentSnippet.length));
            const sample2 = resource2.contentSnippet.substring(position2, Math.min(position2 + sampleSize, resource2.contentSnippet.length));

            // Calculate similarity for this sample using Levenshtein distance
            const distance = this.levenshteinDistance(sample1, sample2);
            const maxLength = Math.max(sample1.length, sample2.length);
            const similarity = 1.0 - (distance / maxLength);

            totalSimilarity += similarity;
        }

        // Return average similarity across all samples
        return totalSimilarity / samples;
    }

    /**
     * Calculate Levenshtein distance between two strings
     * @param str1 First string
     * @param str2 Second string
     * @returns Levenshtein distance
     */
    private levenshteinDistance(str1: string, str2: string): number {
        // Handle edge cases
        if (str1 === str2) return 0;
        if (str1.length === 0) return str2.length;
        if (str2.length === 0) return str1.length;

        // Get the EnhancedMemoryManager instance
        const memoryManager = EnhancedMemoryManager.getInstance();

        // For very large strings, use a more approximate approach
        if (str1.length > 1000 && str2.length > 1000) {
            // Sample the strings at regular intervals
            const sampleCount = 100;
            const step1 = Math.max(1, Math.floor(str1.length / sampleCount));
            const step2 = Math.max(1, Math.floor(str2.length / sampleCount));

            let differences = 0;
            let comparisons = 0;

            for (let i = 0; i < str1.length; i += step1) {
                for (let j = 0; j < str2.length; j += step2) {
                    if (str1[i] !== str2[j]) {
                        differences++;
                    }
                    comparisons++;
                }
            }

            // Estimate the Levenshtein distance based on the sample
            return Math.floor((differences / comparisons) * Math.max(str1.length, str2.length));
        }

        // Memory-efficient implementation that only keeps two rows in memory
        // Swap strings if str2 is shorter to minimize memory usage
        if (str2.length < str1.length) {
            [str1, str2] = [str2, str1];
        }

        // Create two rows for dynamic programming
        let prevRow = new Array(str2.length + 1);
        let currRow = new Array(str2.length + 1);

        // Initialize the first row
        for (let j = 0; j <= str2.length; j++) {
            prevRow[j] = j;
        }

        // Fill in the matrix one row at a time
        for (let i = 1; i <= str1.length; i++) {
            currRow[0] = i;

            for (let j = 1; j <= str2.length; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                currRow[j] = Math.min(
                    prevRow[j] + 1,        // deletion
                    currRow[j - 1] + 1,    // insertion
                    prevRow[j - 1] + cost  // substitution
                );
            }

            // Swap rows for next iteration
            [prevRow, currRow] = [currRow, prevRow];

            // Check memory usage and trigger GC if needed
            if (i % 100 === 0 && str1.length > 500) {
                if (global.gc) {
                    global.gc();
                }
            }
        }

        // The result is in the last cell of the last row we calculated
        // But since we swapped prevRow and currRow, it's in prevRow
        return prevRow[str2.length];
    }
}
