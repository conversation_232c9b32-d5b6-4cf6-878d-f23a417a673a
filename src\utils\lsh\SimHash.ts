import { createHash } from 'crypto';
import { LSHInterface } from './LSHInterface.js';

/**
 * SimHash implementation for text-based resources
 * Based on the algorithm used by Google for near-duplicate detection
 */
export class SimHash implements LSHInterface {
    private hashBits: number;
    
    /**
     * Create a new SimHash instance
     * @param hashBits Number of bits in the hash (default: 64)
     */
    constructor(hashBits: number = 64) {
        this.hashBits = hashBits;
    }
    
    /**
     * Generate a SimHash for the given content
     * @param content Content to hash (string or Buffer)
     * @returns SimHash value as a bigint
     */
    generateHash(content: string | Buffer): bigint {
        // Convert Buffer to string if needed
        const text = Buffer.isBuffer(content) ? content.toString('utf8') : content;
        
        // Tokenize the text (simple whitespace tokenization for now)
        const tokens = text.split(/\s+/).filter(token => token.length > 0);
        
        // Initialize feature vector
        const vector = new Array(this.hashBits).fill(0);
        
        // Process each token
        for (const token of tokens) {
            // Hash the token using SHA-256
            const hash = createHash('sha256').update(token).digest();
            
            // Convert hash to a bigint
            const hashValue = BigInt('0x' + hash.toString('hex'));
            
            // Update feature vector
            for (let i = 0; i < this.hashBits; i++) {
                // Check if bit i is set in the hash
                const bitValue = (hashValue >> BigInt(i)) & BigInt(1);
                
                // Update vector
                vector[i] += bitValue === BigInt(1) ? 1 : -1;
            }
        }
        
        // Generate final hash
        let simHash = BigInt(0);
        for (let i = 0; i < this.hashBits; i++) {
            if (vector[i] > 0) {
                // Set bit i in the hash
                simHash |= (BigInt(1) << BigInt(i));
            }
        }
        
        return simHash;
    }
    
    /**
     * Calculate similarity between two SimHashes using Hamming distance
     * @param hash1 First SimHash
     * @param hash2 Second SimHash
     * @returns Similarity score between 0 and 1
     */
    calculateSimilarity(hash1: bigint, hash2: bigint): number {
        // Calculate Hamming distance
        const distance = this.hammingDistance(hash1, hash2);
        
        // Convert to similarity score (1 - normalized distance)
        return 1 - (distance / this.hashBits);
    }
    
    /**
     * Check if two SimHashes are similar based on a threshold
     * @param hash1 First SimHash
     * @param hash2 Second SimHash
     * @param threshold Similarity threshold (default: 0.7)
     * @returns True if hashes are similar, false otherwise
     */
    areSimilar(hash1: bigint, hash2: bigint, threshold: number = 0.7): boolean {
        return this.calculateSimilarity(hash1, hash2) >= threshold;
    }
    
    /**
     * Calculate Hamming distance between two bigints
     * @param a First bigint
     * @param b Second bigint
     * @returns Hamming distance
     */
    private hammingDistance(a: bigint, b: bigint): number {
        // XOR the two hashes
        const xor = a ^ b;
        
        // Count the number of set bits
        let distance = 0;
        for (let i = 0; i < this.hashBits; i++) {
            if ((xor >> BigInt(i)) & BigInt(1)) {
                distance++;
            }
        }
        
        return distance;
    }
}
