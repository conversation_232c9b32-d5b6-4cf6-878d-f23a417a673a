import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';
import { OverrideInfo } from '../../types/database.js';

export class OverrideRepository {
    private db: Database.Database;
    private logger: Logger;

    constructor(db: Database.Database, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    saveOverride(overrideInfo: OverrideInfo): number {
        try {
            const stmt = this.db.prepare(`
                INSERT INTO Overrides (packageId, overridingResourceId, overriddenTGI, overriddenResourceId)
                VALUES (?, ?, ?, ?)
                ON CONFLICT(packageId, overridingResourceId, overriddenTGI) DO UPDATE SET
                    overriddenResourceId = excluded.overriddenResourceId
                RETURNING id;
            `);

            const result = stmt.get(
                overrideInfo.packageId,
                overrideInfo.overridingResourceId,
                overrideInfo.overriddenTGI,
                overrideInfo.overriddenResourceId
            ) as { id: number };

            this.logger.debug(`[OverrideRepository] Saved/Updated override with ID: ${result.id}`);
            return result.id;
        } catch (error) {
            this.logger.error(`[OverrideRepository] Error saving/updating override (Package: ${overrideInfo.packageId}, Resource: ${overrideInfo.overridingResourceId}, TGI: ${overrideInfo.overriddenTGI}):`, error);
            throw error;
        }
    }

    // Add methods for querying overrides later if needed
    // public getOverridesByPackageId(packageId: number): OverrideInfo[] { ... }
    // public getOverridesByOverriddenTGI(overriddenTGI: string): OverrideInfo[] { ... }
}