import { createHash } from 'crypto';
import { LSHInterface } from './LSHInterface.js';

/**
 * MinHash implementation for binary resources
 * Used for estimating Jaccard similarity between sets
 */
export class MinHash implements LSHInterface {
    private numHashes: number;
    private hashFunctions: ((value: string) => number)[];
    
    /**
     * Create a new MinHash instance
     * @param numHashes Number of hash functions to use (default: 100)
     * @param seed Random seed for hash functions (default: 42)
     */
    constructor(numHashes: number = 100, seed: number = 42) {
        this.numHashes = numHashes;
        this.hashFunctions = this.generateHashFunctions(numHashes, seed);
    }
    
    /**
     * Generate hash functions
     * @param numHashes Number of hash functions to generate
     * @param seed Random seed
     * @returns Array of hash functions
     */
    private generateHashFunctions(numHashes: number, seed: number): ((value: string) => number)[] {
        const hashFunctions: ((value: string) => number)[] = [];
        
        for (let i = 0; i < numHashes; i++) {
            // Create a hash function with a unique salt
            const salt = `salt_${i}_${seed}`;
            
            hashFunctions.push((value: string) => {
                // Hash the value with the salt
                const hash = createHash('sha256').update(salt).update(value).digest('hex');
                
                // Convert to a number
                return parseInt(hash.substring(0, 8), 16);
            });
        }
        
        return hashFunctions;
    }
    
    /**
     * Generate a MinHash signature for the given content
     * @param content Content to hash (string or Buffer)
     * @returns MinHash signature as an array of numbers
     */
    generateHash(content: string | Buffer): number[] {
        // Convert Buffer to string if needed
        const data = Buffer.isBuffer(content) ? content.toString('hex') : content;
        
        // Convert data to a set of shingles (n-grams)
        const shingles = this.generateShingles(data, 4);
        
        // Initialize signature with infinity
        const signature = new Array(this.numHashes).fill(Infinity);
        
        // Process each shingle
        for (const shingle of shingles) {
            // Apply each hash function
            for (let i = 0; i < this.numHashes; i++) {
                const hashValue = this.hashFunctions[i](shingle);
                
                // Keep the minimum hash value
                signature[i] = Math.min(signature[i], hashValue);
            }
        }
        
        return signature;
    }
    
    /**
     * Calculate Jaccard similarity between two MinHash signatures
     * @param hash1 First MinHash signature
     * @param hash2 Second MinHash signature
     * @returns Similarity score between 0 and 1
     */
    calculateSimilarity(hash1: number[], hash2: number[]): number {
        if (hash1.length !== hash2.length) {
            throw new Error('MinHash signatures must have the same length');
        }
        
        // Count the number of matching hash values
        let matches = 0;
        for (let i = 0; i < hash1.length; i++) {
            if (hash1[i] === hash2[i]) {
                matches++;
            }
        }
        
        // Return the estimated Jaccard similarity
        return matches / hash1.length;
    }
    
    /**
     * Check if two MinHash signatures are similar based on a threshold
     * @param hash1 First MinHash signature
     * @param hash2 Second MinHash signature
     * @param threshold Similarity threshold (default: 0.7)
     * @returns True if signatures are similar, false otherwise
     */
    areSimilar(hash1: number[], hash2: number[], threshold: number = 0.7): boolean {
        return this.calculateSimilarity(hash1, hash2) >= threshold;
    }
    
    /**
     * Generate shingles (n-grams) from a string
     * @param data Input string
     * @param n Shingle size
     * @returns Set of shingles
     */
    private generateShingles(data: string, n: number): Set<string> {
        const shingles = new Set<string>();
        
        for (let i = 0; i <= data.length - n; i++) {
            shingles.add(data.substring(i, i + n));
        }
        
        return shingles;
    }
}
