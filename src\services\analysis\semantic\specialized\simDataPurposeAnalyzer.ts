/**
 * SimData Purpose Analyzer
 * 
 * Specialized analyzer for SimData resources to determine their purpose
 * and semantic meaning with high accuracy.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { ResourceKey } from '../../../../types/resource/interfaces.js';
import { ResourcePurposeType } from '../interfaces/resourcePurpose.js';
import { injectable, singleton } from '../../../di/decorators.js';
import * as ResourceTypes from '../../../../constants/resourceTypes.js';

/**
 * SimData purpose analysis result
 */
export interface SimDataPurposeAnalysisResult {
    /**
     * Schema name
     */
    schemaName: string;
    
    /**
     * Schema version
     */
    schemaVersion: number;
    
    /**
     * Schema purpose
     */
    schemaPurpose: string;
    
    /**
     * Resource purpose type
     */
    purposeType: ResourcePurposeType;
    
    /**
     * Confidence score (0-100)
     */
    confidence: number;
    
    /**
     * Related gameplay system
     */
    gameplaySystem?: string;
    
    /**
     * Key columns in the schema
     */
    keyColumns: string[];
    
    /**
     * Critical columns in the schema
     */
    criticalColumns: string[];
    
    /**
     * Explanation of the purpose determination
     */
    explanation: string[];
}

/**
 * SimData schema purpose mapping
 */
interface SimDataSchemaPurposeMapping {
    /**
     * Schema name pattern (regex)
     */
    schemaNamePattern: RegExp;
    
    /**
     * Schema purpose
     */
    purpose: string;
    
    /**
     * Resource purpose type
     */
    purposeType: ResourcePurposeType;
    
    /**
     * Related gameplay system
     */
    gameplaySystem?: string;
    
    /**
     * Base confidence score (0-100)
     */
    baseConfidence: number;
    
    /**
     * Key column patterns
     */
    keyColumnPatterns: string[];
}

/**
 * Specialized analyzer for SimData resources
 */
@singleton()
export class SimDataPurposeAnalyzer {
    private logger: Logger;
    private schemaPurposeMappings: SimDataSchemaPurposeMapping[] = [];
    
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(logger?: Logger) {
        this.logger = logger || new Logger('SimDataPurposeAnalyzer');
        this.initializeSchemaMappings();
    }
    
    /**
     * Initialize schema purpose mappings
     */
    private initializeSchemaMappings(): void {
        // Trait schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Trait/i,
            purpose: 'trait_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'traits',
            baseConfidence: 90,
            keyColumnPatterns: ['trait_type', 'display_name', 'description', 'icon']
        });
        
        // Buff schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Buff/i,
            purpose: 'buff_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'emotions',
            baseConfidence: 90,
            keyColumnPatterns: ['buff_type', 'mood_type', 'mood_weight', 'timeout_string']
        });
        
        // Career schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Career/i,
            purpose: 'career_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'careers',
            baseConfidence: 90,
            keyColumnPatterns: ['career_level', 'salary', 'promotion_text', 'work_schedule']
        });
        
        // Skill schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Skill/i,
            purpose: 'skill_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'skills',
            baseConfidence: 90,
            keyColumnPatterns: ['skill_level', 'level_data', 'skill_description', 'skill_icon']
        });
        
        // Aspiration schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Aspiration/i,
            purpose: 'aspiration_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'aspirations',
            baseConfidence: 90,
            keyColumnPatterns: ['display_name', 'reward_trait', 'objective_count', 'primary_icon']
        });
        
        // Interaction schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Interaction/i,
            purpose: 'interaction_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'interactions',
            baseConfidence: 90,
            keyColumnPatterns: ['display_name', 'display_priority', 'target', 'outcome']
        });
        
        // Object schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Object/i,
            purpose: 'object_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'objects',
            baseConfidence: 90,
            keyColumnPatterns: ['catalog_name', 'catalog_description', 'price', 'tuning_id']
        });
        
        // Recipe schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Recipe/i,
            purpose: 'recipe_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'objects',
            baseConfidence: 90,
            keyColumnPatterns: ['name', 'description', 'preparation_time', 'difficulty']
        });
        
        // Lot schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /Lot/i,
            purpose: 'lot_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'worlds',
            baseConfidence: 90,
            keyColumnPatterns: ['lot_name', 'lot_description', 'lot_size', 'lot_type']
        });
        
        // World schemas
        this.schemaPurposeMappings.push({
            schemaNamePattern: /World/i,
            purpose: 'world_definition',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'worlds',
            baseConfidence: 90,
            keyColumnPatterns: ['world_name', 'world_description', 'region_name', 'lot_count']
        });
    }
    
    /**
     * Analyze SimData resource purpose
     * @param resourceKey Resource key
     * @param simDataBuffer SimData buffer
     * @param metadata Resource metadata
     * @returns SimData purpose analysis result
     */
    public analyzeSimDataPurpose(
        resourceKey: ResourceKey,
        simDataBuffer: Buffer,
        metadata: Record<string, any>
    ): SimDataPurposeAnalysisResult {
        try {
            // Default result
            const result: SimDataPurposeAnalysisResult = {
                schemaName: 'Unknown',
                schemaVersion: 0,
                schemaPurpose: 'unknown',
                purposeType: ResourcePurposeType.UNKNOWN,
                confidence: 0,
                keyColumns: [],
                criticalColumns: [],
                explanation: []
            };
            
            // Check if this is a SimData resource
            if (resourceKey.type !== ResourceTypes.RESOURCE_TYPE_SIMDATA) {
                result.explanation.push('Not a SimData resource');
                return result;
            }
            
            // Extract schema name and version from metadata
            const schemaName = metadata.schemaName || '';
            const schemaVersion = metadata.schemaVersion || 0;
            
            result.schemaName = schemaName;
            result.schemaVersion = schemaVersion;
            
            // If no schema name, try to extract from buffer
            if (!schemaName && simDataBuffer) {
                // Extract schema name from buffer (simplified)
                // In a real implementation, use proper SimData parsing
                const bufferString = simDataBuffer.toString('utf8', 0, 200);
                const schemaMatch = bufferString.match(/Schema:\s*([A-Za-z0-9_]+)/);
                if (schemaMatch && schemaMatch[1]) {
                    result.schemaName = schemaMatch[1];
                    result.explanation.push(`Extracted schema name from buffer: ${schemaMatch[1]}`);
                }
            }
            
            // If we have a schema name, try to match it to a purpose
            if (result.schemaName !== 'Unknown') {
                for (const mapping of this.schemaPurposeMappings) {
                    if (mapping.schemaNamePattern.test(result.schemaName)) {
                        result.schemaPurpose = mapping.purpose;
                        result.purposeType = mapping.purposeType;
                        result.gameplaySystem = mapping.gameplaySystem;
                        result.confidence = mapping.baseConfidence;
                        result.explanation.push(`Schema name "${result.schemaName}" matches pattern for ${mapping.purpose}`);
                        
                        // Check for key columns in metadata
                        if (metadata.columns) {
                            const columns = metadata.columns as string[];
                            const matchedColumns: string[] = [];
                            
                            for (const columnPattern of mapping.keyColumnPatterns) {
                                for (const column of columns) {
                                    if (column.toLowerCase().includes(columnPattern.toLowerCase())) {
                                        matchedColumns.push(column);
                                        result.keyColumns.push(column);
                                    }
                                }
                            }
                            
                            if (matchedColumns.length > 0) {
                                result.confidence += Math.min(10, matchedColumns.length * 2);
                                result.explanation.push(`Found ${matchedColumns.length} key columns: ${matchedColumns.join(', ')}`);
                            }
                        }
                        
                        break;
                    }
                }
            }
            
            // If we still don't have a purpose, try to infer from instance name
            if (result.schemaPurpose === 'unknown' && metadata.instanceName) {
                const instanceName = metadata.instanceName as string;
                
                for (const mapping of this.schemaPurposeMappings) {
                    if (mapping.schemaNamePattern.test(instanceName)) {
                        result.schemaPurpose = mapping.purpose;
                        result.purposeType = mapping.purposeType;
                        result.gameplaySystem = mapping.gameplaySystem;
                        result.confidence = mapping.baseConfidence * 0.7; // Lower confidence for instance name match
                        result.explanation.push(`Instance name "${instanceName}" matches pattern for ${mapping.purpose}`);
                        break;
                    }
                }
            }
            
            return result;
        } catch (error) {
            this.logger.error(`Error analyzing SimData purpose for resource ${resourceKey.type}:${resourceKey.instance}:`, error);
            
            // Return default result on error
            return {
                schemaName: 'Unknown',
                schemaVersion: 0,
                schemaPurpose: 'unknown',
                purposeType: ResourcePurposeType.UNKNOWN,
                confidence: 0,
                keyColumns: [],
                criticalColumns: [],
                explanation: ['Error analyzing SimData purpose']
            };
        }
    }
}
