/**
 * Types for Python bytecode parsing
 * 
 * This module defines the types and interfaces used for parsing Python bytecode
 * in TS4Script files.
 */

/**
 * Python bytecode version information
 */
export interface PythonVersion {
    /**
     * Major version (e.g., 3 for Python 3.7)
     */
    major: number;
    
    /**
     * Minor version (e.g., 7 for Python 3.7)
     */
    minor: number;
    
    /**
     * Micro version (e.g., 0 for Python 3.7.0)
     */
    micro: number;
    
    /**
     * Release level (e.g., 'final', 'alpha', 'beta')
     */
    releaseLevel?: string;
    
    /**
     * Release serial number
     */
    serial?: number;
    
    /**
     * Magic number for this Python version
     */
    magic: number;
    
    /**
     * String representation (e.g., '3.7.0')
     */
    toString(): string;
}

/**
 * Python bytecode header information
 */
export interface BytecodeHeader {
    /**
     * Magic number (4 bytes)
     */
    magic: number;
    
    /**
     * Python version detected from magic number
     */
    pythonVersion: PythonVersion | null;
    
    /**
     * Timestamp (4 bytes)
     */
    timestamp: number;
    
    /**
     * Source size (4 bytes) - Python 3.7+
     */
    sourceSize?: number;
    
    /**
     * Source hash (4 bytes) - Python 3.7+
     */
    sourceHash?: string;
    
    /**
     * Whether this is a standard Python bytecode format
     */
    isStandardFormat: boolean;
    
    /**
     * Whether this is an EA-modified bytecode format
     */
    isEAFormat: boolean;
    
    /**
     * Format name (e.g., 'Python 3.7', 'EA Python 3.7')
     */
    formatName: string;
}

/**
 * Python code object
 */
export interface CodeObject {
    /**
     * Argument count
     */
    argCount: number;
    
    /**
     * Positional-only argument count (Python 3.8+)
     */
    posonlyArgCount?: number;
    
    /**
     * Keyword-only argument count
     */
    kwonlyArgCount: number;
    
    /**
     * Number of local variables
     */
    nlocals: number;
    
    /**
     * Stack size
     */
    stackSize: number;
    
    /**
     * Flags
     */
    flags: number;
    
    /**
     * Bytecode
     */
    code: Buffer;
    
    /**
     * Constants
     */
    constants: any[];
    
    /**
     * Names (variable names)
     */
    names: string[];
    
    /**
     * Variable names
     */
    varnames: string[];
    
    /**
     * Free variables
     */
    freevars: string[];
    
    /**
     * Cell variables
     */
    cellvars: string[];
    
    /**
     * Filename
     */
    filename: string;
    
    /**
     * Name
     */
    name: string;
    
    /**
     * First line number
     */
    firstLineNo: number;
    
    /**
     * Line number table
     */
    lnotab: Buffer;
    
    /**
     * Nested code objects
     */
    nestedCodeObjects?: CodeObject[];
}

/**
 * Python bytecode instruction
 */
export interface Instruction {
    /**
     * Offset in bytecode
     */
    offset: number;
    
    /**
     * Opcode
     */
    opcode: number;
    
    /**
     * Opcode name
     */
    opname: string;
    
    /**
     * Argument
     */
    arg: number | null;
    
    /**
     * Argument value (resolved)
     */
    argval: any;
    
    /**
     * Argument representation
     */
    argrepr: string;
    
    /**
     * Whether this is a jump instruction
     */
    isJump: boolean;
    
    /**
     * Jump target (if isJump is true)
     */
    jumpTarget?: number;
}

/**
 * Parsed Python module
 */
export interface ParsedPythonModule {
    /**
     * Module name
     */
    name: string;
    
    /**
     * Bytecode header
     */
    header: BytecodeHeader;
    
    /**
     * Main code object
     */
    codeObject: CodeObject;
    
    /**
     * Disassembled instructions
     */
    instructions: Instruction[];
    
    /**
     * Extracted constants
     */
    constants: any[];
    
    /**
     * Extracted names
     */
    names: string[];
    
    /**
     * Extracted string literals
     */
    stringLiterals: string[];
    
    /**
     * Detected imports
     */
    imports: string[];
    
    /**
     * Detected classes
     */
    classes: string[];
    
    /**
     * Detected functions
     */
    functions: string[];
}

/**
 * Bytecode parsing result
 */
export interface BytecodeParsingResult {
    /**
     * Whether parsing was successful
     */
    success: boolean;
    
    /**
     * Error message if parsing failed
     */
    error?: string;
    
    /**
     * Parsed module if parsing succeeded
     */
    module?: ParsedPythonModule;
    
    /**
     * Confidence level (0-1)
     */
    confidence: number;
    
    /**
     * Parsing method used
     */
    parsingMethod: string;
}
