/**
 * Constants for Material extraction
 */

// Material file format constants
export const MTST_SIGNATURE = 'MTST'; // Material Set
export const MTRL_SIGNATURE = 'MTRL'; // Material
export const MTDT_SIGNATURE = 'MTDT'; // Material Data
export const TXMT_SIGNATURE = 'TXMT'; // Texture Material

// Material resource type IDs
export const MATERIAL_RESOURCE_TYPES = {
    MATERIAL_DEFINITION: 0x0499A526,    // Material definition
    MATERIAL_VARIANT: 0x0333406C,       // Material variant
    MATERIAL_DEFINITION_ALT: 0xAC16FBEC, // Alternative material definition
    TEXTURE_DEFINITION: 0x0288B3A2,     // Texture definition
    SHADER_DEFINITION: 0x0354796A       // Shader definition
};

// Common resource types for textures
export const TEXTURE_RESOURCE_TYPES = [
    0x00B2D882, // DDS Image
    0x319E4F1D, // PNG Image
    0x3453CF95, // RLE Image
    0x0288B3A2, // Texture
    0xBA856C78  // RLE2 Image
];
