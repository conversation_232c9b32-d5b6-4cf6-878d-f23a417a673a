/**
 * Interface for file metadata
 */
export interface FileMetadata {
  /**
   * The name of the file
   */
  name: string;

  /**
   * The full path to the file
   */
  path: string;

  /**
   * The size of the file in bytes
   */
  size: number;

  /**
   * The last modified timestamp of the file
   */
  lastModified: number;

  /**
   * The file extension
   */
  extension: string;

  /**
   * Whether the file is a directory
   */
  isDirectory: boolean;

  /**
   * Whether the file is a symbolic link
   */
  isSymbolicLink: boolean;

  /**
   * The file permissions
   */
  mode?: number;

  /**
   * Additional metadata specific to the file type
   */
  metadata?: Record<string, unknown>;
}

export interface FileStats {
  // Size stats
  totalSize: number;
  averageSize: number;
  maxSize: number;
  minSize: number;
  
  // Count stats
  totalFiles: number;
  totalDirectories: number;
  totalSymlinks: number;
  
  // Type stats
  extensionCounts: Record<string, number>;
  mimeTypeCounts: Record<string, number>;
  
  // Time stats
  oldestFile: FileMetadata;
  newestFile: FileMetadata;
  mostAccessedFile: FileMetadata;
  
  // Custom stats
  customStats?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

export interface FileOperationResult {
  success: boolean;
  operation: string;
  path: string;
  error?: Error;
  metadata?: FileMetadata;
  stats?: FileStats;
}

export interface FileValidationOptions {
  // Validation rules
  checkPermissions?: boolean;
  checkOwnership?: boolean;
  checkTimestamps?: boolean;
  checkContent?: boolean;
  checkHashes?: boolean;
  
  // Size limits
  minSize?: number;
  maxSize?: number;
  
  // Age limits
  maxAge?: number;
  minAge?: number;
  
  // Content rules
  allowedExtensions?: string[];
  allowedMimeTypes?: string[];
  forbiddenPatterns?: RegExp[];
  
  // Custom rules
  customValidators?: {
    name: string;
    validate: (metadata: FileMetadata) => Promise<boolean>;
  }[];
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  metadata: FileMetadata;
  details?: {
    permissionsValid?: boolean;
    ownershipValid?: boolean;
    timestampsValid?: boolean;
    contentValid?: boolean;
    hashesValid?: boolean;
    sizeValid?: boolean;
    ageValid?: boolean;
    extensionValid?: boolean;
    mimeTypeValid?: boolean;
    patternValid?: boolean;
  };
  customResults?: Record<string, unknown>;
} 
