/**
 * StreamingPackageReader Compatibility Layer
 *
 * This module provides a compatibility layer for the StreamingPackageReader class.
 * It delegates to the ConsolidatedResourceProvider for resource access while maintaining the same API.
 */

import { Readable } from 'stream';
import { FileHandle } from 'fs/promises';
import { open } from 'fs/promises';
import { Logger } from '../../../../utils/logging/logger.js';
import { ConsolidatedResourceProvider } from '../consolidatedResourceProvider.js';
import EnhancedMemoryManager from '../../../../utils/memory/enhancedMemoryManager.js';
import { ResourceKey } from '../../../../types/resource/interfaces.js';
import { Package } from '@s4tk/models';
import { promises as fs } from 'fs';

// Create a logger for this module
const logger = new Logger('StreamingPackageReaderCompat');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

/**
 * Package index entry
 */
export interface PackageIndexEntry {
    type: number;
    group: number;
    instanceHi: number;
    instanceLo: number;
    offset: number;
    fileSize: number;
    memSize: number;
    compressed: boolean;
    deleted: boolean;
}

/**
 * Streaming package reader options
 */
export interface StreamingPackageReaderOptions {
    bufferPoolSize?: number;
    maxBufferSize?: number;
    chunkSize?: number;

    // Hybrid approach options
    directBufferThreshold?: number; // Maximum size for direct buffer approach (default: 5MB)
    chunkedProcessingThreshold?: number; // Maximum size for chunked processing (default: 50MB)
}

/**
 * StreamingPackageReader compatibility implementation
 */
export class StreamingPackageReader {
    private filePath: string;
    private fileHandle: FileHandle | null = null;
    private fileSize: number = 0;
    private indexEntries: PackageIndexEntry[] = [];
    private initialized: boolean = false;
    private consolidatedProvider: ConsolidatedResourceProvider;
    private chunkSize: number;
    private directBufferThreshold: number;
    private chunkedProcessingThreshold: number;

    /**
     * Create a new streaming package reader
     * @param filePath Path to the package file
     * @param options Reader options
     */
    constructor(filePath: string, options: StreamingPackageReaderOptions = {}) {
        this.filePath = filePath;

        // Set chunk size
        this.chunkSize = options.chunkSize || 64 * 1024; // 64KB default

        // Set thresholds for hybrid approach
        this.directBufferThreshold = options.directBufferThreshold || 5 * 1024 * 1024; // 5MB default
        this.chunkedProcessingThreshold = options.chunkedProcessingThreshold || 50 * 1024 * 1024; // 50MB default

        // Create consolidated provider
        this.consolidatedProvider = new ConsolidatedResourceProvider({
            chunkSize: this.chunkSize,
            directBufferThreshold: this.directBufferThreshold,
            chunkedProcessingThreshold: this.chunkedProcessingThreshold
        });

        logger.debug(`Created StreamingPackageReader compatibility layer for ${filePath}`);
    }

    /**
     * Initialize the reader
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Open the file
            this.fileHandle = await open(this.filePath, 'r');

            // Get file size
            const stats = await this.fileHandle.stat();
            this.fileSize = stats.size;

            // Read package header and index
            await this.readHeader();
            await this.readIndex();

            this.initialized = true;
            logger.debug(`Initialized StreamingPackageReader for ${this.filePath}`);
        } catch (error: any) {
            logger.error(`Failed to initialize StreamingPackageReader: ${error.message}`);

            // Close file handle if it was opened
            if (this.fileHandle) {
                await this.fileHandle.close();
                this.fileHandle = null;
            }

            throw error;
        }
    }

    /**
     * Read package header
     */
    private async readHeader(): Promise<void> {
        // Implementation would read the package header
        // For compatibility layer, this is a placeholder
        logger.debug(`Reading header for ${this.filePath}`);
    }

    /**
     * Read package index using S4TK Package.extractResources (fallback approach without BufferFromFile plugin)
     */
    private async readIndex(): Promise<void> {
        try {
            logger.debug(`Reading DBPF index for ${this.filePath} using S4TK Package.extractResources`);

            // Read the entire file into memory (fallback since BufferFromFile plugin is not available)
            const fileBuffer = await fs.readFile(this.filePath);

            // Use S4TK Package.extractResources to get resource key-value pairs
            const resourcePairs = Package.extractResources(fileBuffer, {
                loadRaw: true, // Load as raw resources to avoid parsing overhead
                decompressBuffers: false // Don't decompress to save memory
            });

            // Convert S4TK ResourceKeyPair objects to our PackageIndexEntry format
            this.indexEntries = resourcePairs.map((pair, index) => ({
                type: Number(pair.key.type),
                group: Number(pair.key.group),
                instanceHi: Number(pair.key.instance / 0x100000000n),
                instanceLo: Number(pair.key.instance % 0x100000000n),
                offset: 0, // S4TK doesn't provide offset in this mode
                fileSize: pair.value.buffer?.length || 0,
                memSize: pair.value.buffer?.length || 0,
                compressed: false, // S4TK handles compression transparently
                deleted: false
            }));

            logger.debug(`Successfully read ${this.indexEntries.length} entries from DBPF index`);
        } catch (error: any) {
            logger.error(`Failed to read DBPF index for ${this.filePath}: ${error.message}`);
            this.indexEntries = [];
            throw error;
        }
    }

    /**
     * Get the package index.
     * Ensures that the reader is initialized before accessing the index.
     */
    public get index(): { entries: PackageIndexEntry[] } {
        if (!this.initialized) {
            // This should ideally not happen if initialize() is always called first.
            // Consider throwing an error or triggering async initialization.
            // For now, to match original behavior, assume initialize was called.
            // However, the actual index data comes from consolidatedProvider.
            logger.warn('Accessing StreamingPackageReaderCompat.index before explicit initialize() completed. Index might be empty or from a pending initialization.');
            // To prevent breaking, return empty or try to fetch, but this indicates a usage pattern issue.
            // The `initialize` method should populate `this.indexEntries` via `readIndex`.
            // If `readIndex` is just a placeholder, then `this.indexEntries` will be empty.
            // The correct way is for `initialize` to call `this.consolidatedProvider.getPackageIndex`
            // and store it, or for this getter to fetch it if not present.
            // Let's assume `this.indexEntries` is populated by `initialize()` -> `readIndex()`.
            return { entries: this.indexEntries };
        }
        // If initialize correctly populates this.indexEntries from consolidatedProvider:
        return { entries: this.indexEntries };
    }

    /**
     * Get a resource by its key
     * @param key Resource key
     * @returns Resource data buffer
     */
    public async getResource(key: ResourceKey): Promise<Buffer> {
        await this.initialize();

        // Find the resource in the index
        const entry = this.findResource(key);
        if (!entry) {
            throw new Error(`Resource not found: ${key.type.toString(16)}:${key.group}:${key.instance}`);
        }

        // Create a stream for the resource
        const stream = await this.consolidatedProvider.createResourceStream(this.filePath, entry);

        // Read the entire stream into a buffer
        return new Promise<Buffer>((resolve, reject) => {
            const chunks: Buffer[] = [];

            stream.on('data', (chunk) => {
                chunks.push(chunk);
            });

            stream.on('end', () => {
                resolve(Buffer.concat(chunks));
            });

            stream.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * Get a resource stream
     * @param entry Resource entry
     * @returns Readable stream for the resource
     */
    public async getResourceStream(entry: PackageIndexEntry): Promise<Readable> {
        await this.initialize();

        return this.consolidatedProvider.createResourceStream(this.filePath, entry);
    }

    /**
     * Find a resource by its key
     * @param key Resource key
     * @returns Resource entry or undefined if not found
     */
    private findResource(key: ResourceKey): PackageIndexEntry | undefined {
        // Convert instance to hi/lo
        const instanceHi = Number(key.instance / 0x100000000n);
        const instanceLo = Number(key.instance % 0x100000000n);

        // Find the resource in the index
        return this.indexEntries.find(entry =>
            entry.type === Number(key.type) && // Assuming key.type might also be bigint from ResourceKey
            entry.group === Number(key.group) &&
            entry.instanceHi === instanceHi &&
            entry.instanceLo === instanceLo
        );
    }

    /**
     * Close the reader
     */
    public async close(): Promise<void> {
        if (this.fileHandle) {
            await this.fileHandle.close();
            this.fileHandle = null;
        }

        await this.consolidatedProvider.close();

        logger.debug(`Closed StreamingPackageReader for ${this.filePath}`);
    }
}
