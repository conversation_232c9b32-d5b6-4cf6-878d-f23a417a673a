import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../types/conflict/index.js';
import { ResourceKey } from '../../types/resource/interfaces.js';

// Create a single shared logger instance for all ConflictRepository instances
const logger = new Logger('ConflictRepository');

/**
 * Repository for managing conflict data in the database
 */
export class ConflictRepository {
    private db: Database.Database;
    private initialized: boolean = false;

    constructor(db: Database.Database) {
        this.db = db;
        this.initialize();
    }

    /**
     * Initialize the conflict repository by creating necessary tables
     */
    private initialize(): void {
        // Skip initialization if already initialized
        if (this.initialized) {
            return;
        }

        try {
            // Create Conflicts table
            this.db.prepare(`
                CREATE TABLE IF NOT EXISTS Conflicts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conflictId TEXT NOT NULL,
                    type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    description TEXT NOT NULL,
                    timestamp INTEGER NOT NULL,
                    recommendations TEXT,
                    resolution TEXT,
                    metadata TEXT,
                    confidence REAL,
                    UNIQUE(conflictId)
                )
            `).run();

            // Create ConflictResources table for many-to-many relationship between conflicts and resources
            this.db.prepare(`
                CREATE TABLE IF NOT EXISTS ConflictResources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conflictId INTEGER NOT NULL,
                    resourceType INTEGER NOT NULL,
                    resourceGroup TEXT NOT NULL,
                    resourceInstance TEXT NOT NULL,
                    resourceId INTEGER,
                    FOREIGN KEY (conflictId) REFERENCES Conflicts(id),
                    UNIQUE(conflictId, resourceType, resourceGroup, resourceInstance)
                )
            `).run();

            // Create indexes for better performance
            this.db.prepare(`
                CREATE INDEX IF NOT EXISTS idx_conflicts_type ON Conflicts(type)
            `).run();

            this.db.prepare(`
                CREATE INDEX IF NOT EXISTS idx_conflicts_severity ON Conflicts(severity)
            `).run();

            this.db.prepare(`
                CREATE INDEX IF NOT EXISTS idx_conflict_resources_resource ON ConflictResources(resourceType, resourceGroup, resourceInstance)
            `).run();

            logger.info('Initialized conflict repository');
            this.initialized = true;
        } catch (error) {
            logger.error('Error initializing conflict repository:', error);
            throw error;
        }
    }

    /**
     * Save a conflict to the database
     * @param conflict The conflict to save
     * @returns The ID of the saved conflict
     */
    saveConflict(conflict: ConflictInfo): number {
        try {
            // Begin transaction
            const transaction = this.db.transaction(() => {
                // Insert or replace conflict
                const conflictStmt = this.db.prepare(`
                    INSERT OR REPLACE INTO Conflicts (
                        conflictId, type, severity, description, timestamp,
                        recommendations, resolution, metadata, confidence
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    RETURNING id
                `);

                const result = conflictStmt.get(
                    conflict.id,
                    conflict.type,
                    conflict.severity,
                    conflict.description,
                    conflict.timestamp || Date.now(),
                    JSON.stringify(conflict.recommendations || []),
                    conflict.resolution ? JSON.stringify(conflict.resolution) : null,
                    conflict.metadata ? JSON.stringify(conflict.metadata) : null,
                    conflict.confidence || null
                ) as { id: number };

                const conflictId = result.id;

                // Delete existing resource associations
                const deleteResourcesStmt = this.db.prepare(`
                    DELETE FROM ConflictResources WHERE conflictId = ?
                `);
                deleteResourcesStmt.run(conflictId);

                // Insert resource associations
                if (conflict.affectedResources && conflict.affectedResources.length > 0) {
                    const insertResourceStmt = this.db.prepare(`
                        INSERT OR IGNORE INTO ConflictResources (
                            conflictId, resourceType, resourceGroup, resourceInstance, resourceId
                        ) VALUES (?, ?, ?, ?, ?)
                    `);

                    for (const resource of conflict.affectedResources) {
                        insertResourceStmt.run(
                            conflictId,
                            resource.type,
                            resource.group.toString(),
                            resource.instance.toString(),
                            null // resourceId will be populated later if needed
                        );
                    }
                }

                return conflictId;
            });

            // Execute transaction
            const conflictId = transaction();
            logger.debug(`Saved conflict with ID: ${conflictId}`);
            return conflictId;
        } catch (error) {
            logger.error(`Error saving conflict ${conflict.id}:`, error);
            throw error;
        }
    }

    /**
     * Save multiple conflicts to the database
     * Enhanced with bulk operations for better performance
     * @param conflicts The conflicts to save
     * @returns The number of conflicts saved
     */
    saveConflicts(conflicts: ConflictInfo[]): number {
        try {
            if (conflicts.length === 0) {
                return 0;
            }

            let savedCount = 0;

            // Use bulk operations for better performance
            const transaction = this.db.transaction(() => {
                // Prepare statements once for reuse
                const conflictStmt = this.db.prepare(`
                    INSERT OR REPLACE INTO Conflicts (
                        conflictId, type, severity, description, timestamp,
                        recommendations, resolution, metadata, confidence
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    RETURNING id
                `);

                const deleteResourcesStmt = this.db.prepare(`
                    DELETE FROM ConflictResources WHERE conflictId = ?
                `);

                const insertResourceStmt = this.db.prepare(`
                    INSERT OR IGNORE INTO ConflictResources (
                        conflictId, resourceType, resourceGroup, resourceInstance, resourceId
                    ) VALUES (?, ?, ?, ?, ?)
                `);

                // Process conflicts in batches for memory efficiency
                const batchSize = 100;
                for (let i = 0; i < conflicts.length; i += batchSize) {
                    const batch = conflicts.slice(i, i + batchSize);

                    for (const conflict of batch) {
                        // Insert conflict
                        const result = conflictStmt.get(
                            conflict.id,
                            conflict.type,
                            conflict.severity,
                            conflict.description,
                            conflict.timestamp || Date.now(),
                            JSON.stringify(conflict.recommendations || []),
                            conflict.resolution || null,
                            JSON.stringify(conflict.metadata || {}),
                            conflict.confidence || 1.0
                        ) as { id: number };

                        const conflictId = result.id;

                        // Delete existing resource associations
                        deleteResourcesStmt.run(conflictId);

                        // Insert resource associations in bulk
                        if (conflict.affectedResources && conflict.affectedResources.length > 0) {
                            for (const resource of conflict.affectedResources) {
                                insertResourceStmt.run(
                                    conflictId,
                                    resource.type,
                                    resource.group.toString(),
                                    resource.instance.toString(),
                                    null // resourceId will be populated later if needed
                                );
                            }
                        }

                        savedCount++;
                    }
                }

                return savedCount;
            });

            // Execute transaction
            savedCount = transaction();
            logger.debug(`Saved ${savedCount} conflicts using bulk operations`);
            return savedCount;
        } catch (error) {
            logger.error(`Error saving conflicts:`, error);
            throw error;
        }
    }

    /**
     * Get conflicts from the database
     * @param limit Maximum number of conflicts to return
     * @param offset Offset for pagination
     * @returns Array of conflicts
     */
    getConflicts(limit: number = 100, offset: number = 0): ConflictInfo[] {
        try {
            // Get conflicts
            const conflictsStmt = this.db.prepare(`
                SELECT id, conflictId, type, severity, description, timestamp,
                       recommendations, resolution, metadata, confidence
                FROM Conflicts
                ORDER BY timestamp DESC
                LIMIT ? OFFSET ?
            `);

            const conflicts = conflictsStmt.all(limit, offset) as {
                id: number;
                conflictId: string;
                type: string;
                severity: string;
                description: string;
                timestamp: number;
                recommendations: string;
                resolution: string | null;
                metadata: string | null;
                confidence: number | null;
            }[];

            // Get resources for each conflict
            const resourcesStmt = this.db.prepare(`
                SELECT resourceType, resourceGroup, resourceInstance
                FROM ConflictResources
                WHERE conflictId = ?
            `);

            // Map database results to ConflictInfo objects
            return conflicts.map(conflict => {
                // Get resources for this conflict
                const resources = resourcesStmt.all(conflict.id) as {
                    resourceType: number;
                    resourceGroup: string;
                    resourceInstance: string;
                }[];

                // Map resources to ResourceKey objects
                const affectedResources: ResourceKey[] = resources.map(resource => ({
                    type: resource.resourceType,
                    group: BigInt(resource.resourceGroup),
                    instance: BigInt(resource.resourceInstance)
                }));

                // Create ConflictInfo object
                return {
                    id: conflict.conflictId,
                    type: conflict.type as ConflictType,
                    severity: conflict.severity as ConflictSeverity,
                    description: conflict.description,
                    timestamp: conflict.timestamp,
                    recommendations: JSON.parse(conflict.recommendations || '[]'),
                    resolution: conflict.resolution ? JSON.parse(conflict.resolution) : undefined,
                    metadata: conflict.metadata ? JSON.parse(conflict.metadata) : undefined,
                    confidence: conflict.confidence || undefined,
                    affectedResources
                };
            });
        } catch (error) {
            logger.error(`Error getting conflicts:`, error);
            return [];
        }
    }

    /**
     * Clear all conflicts from the database
     * @returns Number of conflicts cleared
     */
    clearConflicts(): number {
        try {
            // Begin transaction
            const transaction = this.db.transaction(() => {
                // Clear conflict resources first (foreign key constraint)
                const clearResourcesStmt = this.db.prepare('DELETE FROM ConflictResources');
                const resourcesResult = clearResourcesStmt.run();

                // Clear conflicts
                const clearConflictsStmt = this.db.prepare('DELETE FROM Conflicts');
                const conflictsResult = clearConflictsStmt.run();

                return conflictsResult.changes;
            });

            // Execute transaction
            const clearedCount = transaction();
            logger.debug(`Cleared ${clearedCount} conflicts from database`);
            return clearedCount;
        } catch (error) {
            logger.error(`Error clearing conflicts:`, error);
            throw error;
        }
    }
}
