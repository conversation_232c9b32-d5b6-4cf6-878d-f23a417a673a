/**
 * RLE2 image format parser (Sims 4 specific)
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ImageFormat, RLE2Metadata } from '../types.js';
import { BufferReader } from '../utils/bufferReader.js';
import { createImageErrorContext, handleImageError } from '../error/imageExtractorErrorHandler.js';

// Create a logger instance
const log = new Logger('RLE2Parser');

/**
 * Parses an RLE2 image buffer (Sims 4 specific)
 * @param buffer RLE2 image buffer
 * @param resourceId Resource ID for error context
 * @param instanceId Instance ID for error context
 * @returns Parsed RLE2 metadata
 */
export function parseRLE2(buffer: Buffer, resourceId: number, instanceId: string): RLE2Metadata {
    try {
        // Create a buffer reader
        const reader = new BufferReader(buffer);
        
        // Validate RLE2 signature
        const signature = reader.readString(4, 'ascii', 'RLE2 Signature');
        if (signature !== 'RLE2') {
            throw new Error('Invalid RLE2 signature');
        }
        
        // Read header version
        const version = reader.readUInt32LE('Version');
        if (version !== 1) {
            log.warn(`Unusual RLE2 version: ${version}`);
        }
        
        // Read dimensions
        const width = reader.readUInt32LE('Width');
        const height = reader.readUInt32LE('Height');
        
        // Read format (usually 1 for RGBA)
        const format = reader.readUInt32LE('Format');
        
        // Determine if the image has an alpha channel
        // Format 1 is RGBA, which has an alpha channel
        const hasAlpha = format === 1;
        
        // Create and return metadata
        return {
            format: ImageFormat.RLE2,
            width: width,
            height: height,
            hasAlpha: hasAlpha
        };
    } catch (error) {
        // Handle error and return minimal metadata
        const context = createImageErrorContext(resourceId, instanceId, 'parseRLE2');
        const result = handleImageError(error, context);
        
        return {
            format: ImageFormat.RLE2,
            width: undefined,
            height: undefined,
            hasAlpha: undefined
        };
    }
}
