"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConfigWithBasePath = getConfigWithBasePath;
const path_1 = __importDefault(require("path"));
const config = {
    retentionPeriods: {
        analysisFiles: {
            days: 7,
            pattern: ['*.txt', '*.json'],
            directory: 'analysis-output'
        },
        logFiles: {
            days: 7,
            pattern: ['*.log'],
            directory: 'logs'
        },
        backupFiles: {
            days: 30,
            pattern: ['*.bak', '*_backup.*'],
            directory: 'output/backups'
        },
        tempFiles: {
            days: 1,
            pattern: ['*.tmp', '*.temp'],
            directory: 'output'
        }
    },
    excludePatterns: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/coverage/**',
        '**/important-analysis/**'
    ],
    archiveSettings: {
        enabled: true,
        directory: path_1.default.join('analysis-output', 'archive'),
        maxSize: 500, // 500MB
        compressionEnabled: true
    }
};
// Export both the config and a function to get config with custom base path
exports.default = config;
function getConfigWithBasePath(basePath) {
    const configWithBasePath = {
        ...config,
        retentionPeriods: Object.entries(config.retentionPeriods).reduce((acc, [key, value]) => ({
            ...acc,
            [key]: {
                ...value,
                directory: path_1.default.join(basePath, value.directory)
            }
        }), {}),
        archiveSettings: {
            ...config.archiveSettings,
            directory: path_1.default.join(basePath, config.archiveSettings.directory)
        }
    };
    return configWithBasePath;
}
