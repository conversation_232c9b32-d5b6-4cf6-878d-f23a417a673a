# Utilities

This directory contains utility functions and classes used throughout the application.

## Resource Filter Utilities

The `resourceFilterUtils.ts` file contains utilities for filtering resources by various criteria.

### Features

- **Resource Filtering**: Filter resources by type, group, instance, or text content.
- **Resource Grouping**: Group resources by type.
- **Resource Type Helpers**: Get predefined sets of resource types (CAS, Build/Buy, Script, Image, Audio).

### Usage

```typescript
import { 
  createResourceFilter, 
  filterResourcesByKey, 
  filterResourcesByText,
  getCasResourceTypes,
  getBuildBuyResourceTypes
} from '@/utils';

// Create a filter function for specific resource types
const filter = createResourceFilter({
  typeFilter: getCasResourceTypes()
});

// Use the filter function
const isMatch = filter(resourceType, resourceGroup, resourceInstance);

// Filter resources by key
const filteredResources = filterResourcesByKey(resources, {
  typeFilter: getCasResourceTypes(),
  limit: 100
});

// Filter resources by text content
const textFilteredResources = filterResourcesByText(resources, 'search term', {
  ignoreCase: true
});
```

## Caching Utilities

The `cacheUtils.ts` file provides caching implementations to improve performance.

### Features

- **LRU Cache**: In-memory LRU (Least Recently Used) cache for fast access to frequently used data.
- **Persistent Cache**: LocalStorage-based persistent cache for data that should survive page reloads.

### Usage

```typescript
import { LRUCache, PersistentCache } from '@/utils';

// Create an in-memory LRU cache
const cache = new LRUCache<string, any>(100); // Max 100 items

// Set values
cache.set('key1', { data: 'value1' });

// Get values
const value = cache.get('key1');

// Check if key exists
const hasKey = cache.has('key1');

// Delete values
cache.delete('key1');

// Clear cache
cache.clear();

// Create a persistent cache
const persistentCache = new PersistentCache<any>('my-cache-prefix', 100);

// Uses the same API as LRUCache, but persists to localStorage
persistentCache.set('key1', { data: 'value1' });
const persistedValue = persistentCache.get('key1');
```

## Usage with Other Services

These utilities are used by various services in the application, including:

- `ResourceAnalyzerService` for filtering resources during analysis.
- `ConflictDetectionService` for filtering resources during conflict detection.
- `PackageAnalysisService` for caching analysis results.
- `MetadataCache` for caching resource metadata.

The utilities help ensure consistent behavior across these services and avoid code duplication. 
