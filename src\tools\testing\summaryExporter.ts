/**
 * Summary Export Functions
 * 
 * Exports comprehensive test summaries to various file formats for analysis,
 * reporting, and documentation purposes.
 */

import { writeFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { ComprehensiveTestSummary } from './comprehensiveTestSummary.js';
import { formatComprehensiveTestSummary, exportStructuredSummary, exportYamlSummary } from './summaryFormatter.js';
import { Logger } from '../../utils/logging/logger.js';

const logger = new Logger('SummaryExporter');

/**
 * Export options
 */
export interface ExportOptions {
    outputDir?: string;
    filename?: string;
    includeTimestamp?: boolean;
    formats?: ('txt' | 'json' | 'yaml' | 'csv' | 'md')[];
    compress?: boolean;
}

/**
 * Default export options
 */
const DEFAULT_OPTIONS: ExportOptions = {
    outputDir: './test-results',
    filename: 'comprehensive-test-summary',
    includeTimestamp: true,
    formats: ['txt', 'json'],
    compress: false
};

/**
 * Ensure directory exists
 */
function ensureDirectoryExists(dirPath: string): void {
    try {
        mkdirSync(dirPath, { recursive: true });
    } catch (error: any) {
        if (error.code !== 'EEXIST') {
            throw error;
        }
    }
}

/**
 * Generate filename with timestamp
 */
function generateFilename(baseName: string, extension: string, includeTimestamp: boolean): string {
    if (includeTimestamp) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
        return `${baseName}-${timestamp}.${extension}`;
    }
    return `${baseName}.${extension}`;
}

/**
 * Export summary as human-readable text
 */
function exportAsText(summary: ComprehensiveTestSummary, filePath: string): void {
    const content = formatComprehensiveTestSummary(summary);
    writeFileSync(filePath, content, 'utf8');
    logger.info(`Exported text summary to: ${filePath}`);
}

/**
 * Export summary as JSON
 */
function exportAsJson(summary: ComprehensiveTestSummary, filePath: string): void {
    const content = exportStructuredSummary(summary);
    writeFileSync(filePath, content, 'utf8');
    logger.info(`Exported JSON summary to: ${filePath}`);
}

/**
 * Export summary as YAML
 */
function exportAsYaml(summary: ComprehensiveTestSummary, filePath: string): void {
    const content = exportYamlSummary(summary);
    writeFileSync(filePath, content, 'utf8');
    logger.info(`Exported YAML summary to: ${filePath}`);
}

/**
 * Export summary as CSV
 */
function exportAsCsv(summary: ComprehensiveTestSummary, filePath: string): void {
    const csvLines: string[] = [];
    
    // Header
    csvLines.push('Category,Metric,Value,Unit,Status');
    
    // Execution Overview
    csvLines.push(`Execution,Total Duration,${summary.executionOverview.totalDuration},ms,${summary.executionOverview.overallStatus}`);
    csvLines.push(`Execution,Phases,${summary.executionOverview.phaseBreakdown.length},count,info`);
    csvLines.push(`Execution,Memory Snapshots,${summary.executionOverview.memoryProgression.length},count,info`);
    
    // Package Analysis
    csvLines.push(`Package Analysis,Files Processed,${summary.packageAnalysis.filesProcessed.length},count,info`);
    csvLines.push(`Package Analysis,Total Resources,${summary.packageAnalysis.totalResources},count,info`);
    csvLines.push(`Package Analysis,Total Size,${summary.packageAnalysis.totalSize},bytes,info`);
    csvLines.push(`Package Analysis,Processing Speed,${summary.packageAnalysis.processingSpeed.resourcesPerSecond.toFixed(2)},resources/sec,info`);
    
    // Conflict Detection
    csvLines.push(`Conflict Detection,Total Conflicts,${summary.conflictDetection.totalConflicts},count,info`);
    csvLines.push(`Conflict Detection,Detection Accuracy,${summary.conflictDetection.detectionAccuracy.toFixed(2)},percent,info`);
    
    // Quality Assurance
    csvLines.push(`Quality Assurance,Real Data Percentage,${summary.qualityAssurance.realDataVerification.realDataPercentage},percent,${summary.qualityAssurance.realDataVerification.realDataPercentage >= 95 ? 'success' : 'warning'}`);
    csvLines.push(`Quality Assurance,Mock Data Detected,${summary.qualityAssurance.realDataVerification.mockDataDetected ? 'Yes' : 'No'},boolean,${summary.qualityAssurance.realDataVerification.mockDataDetected ? 'warning' : 'success'}`);
    csvLines.push(`Quality Assurance,Stability Score,${summary.qualityAssurance.systemStability.stabilityScore},percent,${summary.qualityAssurance.systemStability.stabilityScore >= 90 ? 'success' : 'warning'}`);
    
    // Performance Analysis
    if (summary.performanceAnalysis.memoryEfficiency.peakUsage > 0) {
        csvLines.push(`Performance,Peak Memory Usage,${summary.performanceAnalysis.memoryEfficiency.peakUsage},bytes,info`);
        csvLines.push(`Performance,Cleanup Effectiveness,${summary.performanceAnalysis.memoryEfficiency.cleanupEffectiveness.toFixed(2)},percent,${summary.performanceAnalysis.memoryEfficiency.cleanupEffectiveness >= 95 ? 'success' : 'warning'}`);
    }
    
    const content = csvLines.join('\n');
    writeFileSync(filePath, content, 'utf8');
    logger.info(`Exported CSV summary to: ${filePath}`);
}

/**
 * Export summary as Markdown
 */
function exportAsMarkdown(summary: ComprehensiveTestSummary, filePath: string): void {
    const lines: string[] = [];
    
    // Header
    lines.push('# Comprehensive Test Summary Report');
    lines.push('');
    lines.push(`**Generated:** ${new Date().toISOString()}`);
    lines.push(`**Test Version:** ${summary.structuredData.version}`);
    lines.push(`**Overall Status:** ${summary.executionOverview.overallStatus.toUpperCase()}`);
    lines.push('');
    
    // Executive Summary
    lines.push('## Executive Summary');
    lines.push('');
    lines.push(`- **Duration:** ${summary.executionOverview.totalDuration}ms`);
    lines.push(`- **Packages Processed:** ${summary.packageAnalysis.filesProcessed.length}`);
    lines.push(`- **Resources Analyzed:** ${summary.packageAnalysis.totalResources.toLocaleString()}`);
    lines.push(`- **Conflicts Detected:** ${summary.conflictDetection.totalConflicts.toLocaleString()}`);
    lines.push(`- **Real Data Usage:** ${summary.qualityAssurance.realDataVerification.realDataPercentage}%`);
    lines.push('');
    
    // Key Metrics
    lines.push('## Key Metrics');
    lines.push('');
    lines.push('| Category | Metric | Value | Status |');
    lines.push('|----------|--------|-------|--------|');
    lines.push(`| Performance | Processing Speed | ${summary.packageAnalysis.processingSpeed.resourcesPerSecond.toFixed(2)} resources/sec | ✅ |`);
    lines.push(`| Quality | Real Data Percentage | ${summary.qualityAssurance.realDataVerification.realDataPercentage}% | ${summary.qualityAssurance.realDataVerification.realDataPercentage >= 95 ? '✅' : '⚠️'} |`);
    lines.push(`| Stability | System Stability | ${summary.qualityAssurance.systemStability.stabilityScore}% | ${summary.qualityAssurance.systemStability.stabilityScore >= 90 ? '✅' : '⚠️'} |`);
    lines.push(`| Detection | Conflict Accuracy | ${summary.conflictDetection.detectionAccuracy.toFixed(2)}% | ${summary.conflictDetection.detectionAccuracy >= 80 ? '✅' : '⚠️'} |`);
    lines.push('');
    
    // Detailed Results
    lines.push('## Detailed Results');
    lines.push('');
    
    // Package Analysis
    lines.push('### Package Analysis');
    lines.push(`- **Files Processed:** ${summary.packageAnalysis.filesProcessed.length}`);
    lines.push(`- **Total Resources:** ${summary.packageAnalysis.totalResources.toLocaleString()}`);
    lines.push(`- **Total Size:** ${(summary.packageAnalysis.totalSize / 1024 / 1024).toFixed(2)} MB`);
    lines.push(`- **Average File Size:** ${(summary.packageAnalysis.averageSize / 1024).toFixed(2)} KB`);
    lines.push('');
    
    // Conflict Detection
    lines.push('### Conflict Detection');
    lines.push(`- **Total Conflicts:** ${summary.conflictDetection.totalConflicts.toLocaleString()}`);
    lines.push(`- **Detection Accuracy:** ${summary.conflictDetection.detectionAccuracy.toFixed(2)}%`);
    if (summary.conflictDetection.problematicMods.length > 0) {
        lines.push('- **Most Problematic Mods:**');
        summary.conflictDetection.problematicMods.slice(0, 5).forEach(mod => {
            lines.push(`  - ${mod.name}: ${mod.conflicts} conflicts`);
        });
    }
    lines.push('');
    
    // Quality Assurance
    lines.push('### Quality Assurance');
    lines.push(`- **Real Data Usage:** ${summary.qualityAssurance.realDataVerification.realDataPercentage}%`);
    lines.push(`- **Mock Data Detected:** ${summary.qualityAssurance.realDataVerification.mockDataDetected ? 'Yes ⚠️' : 'No ✅'}`);
    lines.push(`- **System Stability:** ${summary.qualityAssurance.systemStability.stabilityScore}%`);
    lines.push(`- **Memory Leaks:** ${summary.qualityAssurance.systemStability.memoryLeaks}`);
    lines.push('');
    
    // Recommendations
    if (summary.recommendations.immediate.length > 0 || summary.recommendations.shortTerm.length > 0) {
        lines.push('## Recommendations');
        lines.push('');
        
        if (summary.recommendations.immediate.length > 0) {
            lines.push('### Immediate Actions');
            summary.recommendations.immediate.forEach(rec => {
                lines.push(`- ${rec}`);
            });
            lines.push('');
        }
        
        if (summary.recommendations.shortTerm.length > 0) {
            lines.push('### Short-term Improvements');
            summary.recommendations.shortTerm.forEach(rec => {
                lines.push(`- ${rec}`);
            });
            lines.push('');
        }
    }
    
    // Footer
    lines.push('---');
    lines.push(`*Report generated by Sims 4 Mod Management Tool v${summary.structuredData.version}*`);
    
    const content = lines.join('\n');
    writeFileSync(filePath, content, 'utf8');
    logger.info(`Exported Markdown summary to: ${filePath}`);
}

/**
 * Export comprehensive test summary to multiple formats
 */
export function exportTestSummary(summary: ComprehensiveTestSummary, options: Partial<ExportOptions> = {}): string[] {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    const exportedFiles: string[] = [];
    
    // Ensure output directory exists
    ensureDirectoryExists(opts.outputDir!);
    
    // Export in each requested format
    for (const format of opts.formats!) {
        const filename = generateFilename(opts.filename!, format, opts.includeTimestamp!);
        const filePath = join(opts.outputDir!, filename);
        
        try {
            switch (format) {
                case 'txt':
                    exportAsText(summary, filePath);
                    break;
                case 'json':
                    exportAsJson(summary, filePath);
                    break;
                case 'yaml':
                    exportAsYaml(summary, filePath);
                    break;
                case 'csv':
                    exportAsCsv(summary, filePath);
                    break;
                case 'md':
                    exportAsMarkdown(summary, filePath);
                    break;
                default:
                    logger.warn(`Unsupported export format: ${format}`);
                    continue;
            }
            
            exportedFiles.push(filePath);
        } catch (error: any) {
            logger.error(`Failed to export ${format} summary: ${error.message}`);
        }
    }
    
    if (exportedFiles.length > 0) {
        logger.info(`Successfully exported summary to ${exportedFiles.length} file(s)`);
        exportedFiles.forEach(file => logger.info(`  - ${file}`));
    }
    
    return exportedFiles;
}

/**
 * Quick export with default settings
 */
export function quickExportSummary(summary: ComprehensiveTestSummary): string[] {
    return exportTestSummary(summary, {
        formats: ['txt', 'json', 'md', 'csv'],
        includeTimestamp: true
    });
}

/**
 * Export summary for AI analysis
 */
export function exportForAI(summary: ComprehensiveTestSummary): string[] {
    return exportTestSummary(summary, {
        formats: ['json', 'yaml'],
        filename: 'ai-analysis-summary',
        includeTimestamp: true
    });
}

/**
 * Export summary for reporting
 */
export function exportForReporting(summary: ComprehensiveTestSummary): string[] {
    return exportTestSummary(summary, {
        formats: ['md', 'csv'],
        filename: 'test-report',
        includeTimestamp: true
    });
}
