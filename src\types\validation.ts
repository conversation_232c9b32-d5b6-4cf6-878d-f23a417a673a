﻿﻿// Corrected import
import { ResourceMetadata } from './resource/interfaces.js';

export interface ValidationResult {
  valid: boolean;
  message: string;
  details?: {
    [key: string]: any;
  };
  errors?: string[];
  warnings?: string[];
  timestamp?: number;
}

export interface ValidationRule {
  validate: (resource: ResourceMetadata) => ValidationResult;
  name?: string;
  description?: string;
}

export interface ValidationOptions {
  validateResources?: boolean;
  validateTypes?: boolean;
  validateSizes?: boolean;
  maxResourceSize?: number;
}
