{"name": "sims4-package-analyzer", "version": "1.0.0", "description": "A tool for analyzing Sims 4 package files for conflicts and providing recommendations", "type": "commonjs", "main": "dist/server.js", "types": "dist/types/index.d.ts", "scripts": {"start": "node dist/server.js", "start:electron": "cross-env NODE_ENV=production electron dist/main-process/frontend/electron/main.js", "dev:server": "cross-env PORT=8501 node dist/server.js", "dev:client": "webpack serve --mode development", "dev": "npm run build:server && concurrently \"npm:dev:server\" \"npm:dev:client\" \"npm:dev:electron\"", "dev:electron": "wait-on http://localhost:8081/electron.html && cross-env NODE_ENV=development electron dist/main-process/frontend/electron/main.js", "dev:ts": "tsc --watch", "build": "npm run build:server && npm run build:client", "build:server": "powershell -Command \"Remove-Item -Path dist -Recurse -Force -ErrorAction SilentlyContinue\" && tsc -p tsconfig.main.json && tsc -p tsconfig.preload.json && tsc -p tsconfig.json && powershell -Command \"Rename-Item -Path dist/main-process/frontend/electron/preload.js -NewName preload.cjs -Force -ErrorAction SilentlyContinue; Rename-Item -Path dist/main-process/frontend/electron/preload.js.map -NewName preload.cjs.map -Force -ErrorAction SilentlyContinue; Set-Content -Path dist/main-process/package.json -Value '{\\\"type\\\": \\\"commonjs\\\"}' -Force\"", "build:client": "vite build", "test": "jest", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "rebuild:electron": "electron-rebuild -f --build-path node_modules/bufferfromfile/build/Release"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@executeautomation/playwright-mcp-server": "^1.0.3", "@langchain/core": "^0.3.43", "@langchain/groq": "^0.2.0", "@mui/material": "^7.0.1", "@s4tk/extraction": "^0.3.2", "@s4tk/hashing": "^0.2.0", "@s4tk/models": "^0.6.14", "@s4tk/plugin-bufferfromfile": "^0.1.1", "@s4tk/xml-dom": "^0.2.6", "@tensorflow/tfjs": "^4.17.0", "better-sqlite3": "^11.10.0", "chokidar": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "electron-log": "^5.1.1", "express": "^4.21.2", "fast-xml-parser": "^5.2.3", "groq-sdk": "^0.17.0", "helmet": "^8.1.0", "jszip": "^3.10.1", "langchain": "^0.3.19", "limiter": "^2.1.0", "lru-cache": "^10.4.3", "multer": "^1.4.5-lts.1", "pinia": "^3.0.1", "piscina": "^5.0.0", "primeicons": "^7.0.0", "primevue": "^4.3.3", "sass": "^1.77.8", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "string-comparison": "^1.3.0", "vue": "^3.5.13", "vuetify": "^3.7.1", "winston": "^3.12.0", "xml2js": "^0.6.2", "zustand": "^5.0.3"}, "devDependencies": {"@electron/rebuild": "^3.7.1", "@mui/icons-material": "^7.0.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.10", "@types/multer": "^1.4.11", "@types/node": "^20.17.28", "@types/react": "^19.0.12", "@types/socket.io": "^3.0.2", "@types/socket.io-client": "^3.0.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/compiler-sfc": "^3.5.13", "concurrently": "^8.2.2", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.11.0", "electron": "^35.1.2", "esbuild-loader": "^4.3.0", "eslint": "^8.54.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "prettier": "^3.1.0", "style-loader": "^4.0.0", "ts-jest": "^29.1.1", "ts-loader": "^9.5.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2", "vite": "^6.2.4", "vue-loader": "^17.4.2", "wait-on": "^8.0.3", "webpack": "^5.98.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "MIT", "directories": {"doc": "docs", "test": "tests"}, "keywords": []}