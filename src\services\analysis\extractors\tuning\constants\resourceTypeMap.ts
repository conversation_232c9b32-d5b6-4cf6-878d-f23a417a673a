/**
 * Common Sims 4 resource types and their semantic meanings
 * This map is used to determine the type of resource based on its type ID
 */
export const resourceTypeMap: Record<number, string> = {
    // Tuning resources
    0xCB5FDDC7: 'Trait',
    0x6017E896: 'Buff',
    0xE882D22F: 'Interaction',
    0x319E4F1D: 'Object Definition',
    0x28B64675: 'Aspiration',
    0x0E4D15FB: 'Career',
    0xEB97F823: 'Recipe',
    0x0A5CDDDF: 'Lot',
    0xAC16F634: 'Skill',
    0x0C772E27: 'Relationship',
    0x2F7D0004: 'Situation',
    0x5B02819E: 'Event',
    0x8DE9DAA3: 'Mood',
    0x839D9A7C: 'Commodity',
    0x3FD6243E: 'Statistic',
    0x130F8C9E: 'Venue',
    0x0C1FE3F2: 'Zone Modifier',
    0x366A5A5A: 'Away Action',
    0x43E356CE: 'Service',
    0x9C07855F: 'Reward',
    0x2D650821: 'Tutorial',
    0x2F7D0002: 'UI Dialog',
    0x02D5DF13: 'Animation',
    0x0C93E3AB: 'Posture',
    0x9DF2F1F2: 'Social',
    0x76FA4907: 'Broadcaster',
    0x0914B438: 'Loot',
    0x0C93E3AC: 'Test',
    0x2A5A8B7B: 'Snippet',
    0x0C93E3AD: 'Objective',
    0x0C93E3AE: 'Household',
    
    // Other resources
    0x545AC67A: 'SimData',
    0x00B2D882: 'Image',
    0x736884F1: 'Audio',
    0x220557DA: 'String Table',
    0x02D5DF13: 'Animation',
    0x6B20C4F3: 'VFX',
    0x00AE6C67: 'Model',
    0x00B3E292: 'Texture',
    0x0166038C: 'Material',
    0x0333406C: 'Footprint',
    0x02D5DF13: 'Animation',
    0x0229BDC8: 'Slot',
    0x63A33EA8: 'Light',
    0x0A36F07B: 'Terrain Paint',
    0x762C336D: 'Terrain Geometry',
    0x0A5DE9DC: 'Object Catalog',
    0x034AEECB: 'Modular Part'
};

/**
 * Gets the resource type name from a type ID
 * @param typeId The resource type ID
 * @returns The resource type name
 */
export function getResourceTypeName(typeId: number): string {
    return resourceTypeMap[typeId] || `Unknown (0x${typeId.toString(16)})`;
}

/**
 * Gets the resource type ID from a type name
 * @param typeName The resource type name
 * @returns The resource type ID or undefined if not found
 */
export function getResourceTypeId(typeName: string): number | undefined {
    for (const [typeId, name] of Object.entries(resourceTypeMap)) {
        if (name === typeName) {
            return Number(typeId);
        }
    }
    return undefined;
}
