﻿﻿﻿import { onMounted, onUnmounted } from 'vue';
import { useAnalysisStore } from '../store/analysis'; // Ensure this path is correct

// Define the structure of the exposed API based on preload.ts
interface ElectronAPI {
  openFileDialog: () => Promise<any>; // Assuming it returns something, adjust if needed
  testInvoke: (message: string) => Promise<any>;
  sendPing: (message: string) => void;
  on: (channel: string, callback: (...args: any[]) => void) => void;
  // We might need a way to remove listeners later
}

// Augment the Window interface
declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}

export function useElectronEvents() {
  const analysisStore = useAnalysisStore();

  // --- Event Handlers ---
  // Note: The 'analysis-progress' event from main likely sends both progress and status
  const handleAnalysisProgress = (data: { progress: number; status: string }) => {
    console.log('[Renderer] Received analysis-progress:', data);
    // Assuming the store action expects both progress and status
    analysisStore.updateProgress(data.progress, data.status);
  };

  const handleAnalysisComplete = (results: any) => { // results type should match setResults param
    console.log('[Renderer] Received analysis-complete:', results);
    analysisStore.setResults(results);
    // No separate setLoading or setProgress needed here, setResults handles it
  };

  const handleAnalysisError = (error: any) => { // error type should match setError param
    console.error('[Renderer] Received analysis-error:', error);
    // Ensure error is an object with a message, or provide a default string
    const errorMessage = typeof error === 'object' && error !== null && error.message
      ? error.message
      : typeof error === 'string'
      ? error
      : 'An unknown error occurred during analysis.';
    analysisStore.setError(errorMessage);
    // No separate setLoading or setProgress needed here, setError handles it
  };

  // --- Lifecycle Hooks ---
  onMounted(() => {
    console.log('[useElectronEvents] Mounting and registering listeners...');
    if (window.electronAPI) {
      // Register listeners using the exposed 'on' method
      window.electronAPI.on('analysis-progress', handleAnalysisProgress);
      window.electronAPI.on('analysis-complete', handleAnalysisComplete);
      window.electronAPI.on('analysis-error', handleAnalysisError);
      console.log('[useElectronEvents] Listeners registered via electronAPI.');
    } else {
      console.error('[useElectronEvents] window.electronAPI is not defined. Preload script might have failed or contextIsolation is not working as expected.');
    }
  });

  onUnmounted(() => {
    console.log('[useElectronEvents] Unmounting...');
    // TODO: Implement listener removal if preload script provides a way
    // Example: If window.electronAPI.on returned a cleanup function:
    // cleanupProgressListener?.();
    // cleanupCompleteListener?.();
    // cleanupErrorListener?.();
    console.log('[useElectronEvents] Listener cleanup needs implementation.');
  });

  // --- Exposed Actions ---
  const triggerFileDialog = async () => {
    if (window.electronAPI?.openFileDialog) {
      console.log('[useElectronEvents] Triggering file dialog via electronAPI.openFileDialog');
      try {
        // Assuming openFileDialog is exposed and handles the main process interaction
        const result = await window.electronAPI.openFileDialog();
        console.log('[useElectronEvents] File dialog invoked. Result (if any):', result);
        // Main process likely handles the result and sends back progress/complete/error events
      } catch (error: any) {
        console.error('[useElectronEvents] Error calling openFileDialog:', error);
        analysisStore.setError(error.message || 'Failed to open file dialog.');
      }
    } else {
      const errorMsg = '[useElectronEvents] window.electronAPI.openFileDialog is not available.';
      console.error(errorMsg);
      analysisStore.setError('File dialog functionality is not available.');
    }
  };

  // Add other actions if needed, e.g., for sending pings or test invokes
  // const sendTestPing = (message: string) => {
  //   window.electronAPI?.sendPing?.(message);
  // };

  return {
    // Expose methods needed by components
    triggerFileDialog,
    // sendTestPing,
  };
}
