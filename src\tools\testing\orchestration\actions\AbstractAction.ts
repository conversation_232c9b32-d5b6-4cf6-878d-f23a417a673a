/**
 * Abstract Action Base Class
 * 
 * This provides the foundation for all workflow actions, bridging CLI and future GUI
 * implementations while maintaining consistent interfaces for AI compatibility.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { EnhancedMemoryManager } from '../../../../utils/memory/enhancedMemoryManager.js';
import { ActionDefinition, ActionResult, PlayerPersona, SystemState } from '../core/interfaces.js';

/**
 * Base class for all workflow actions
 */
export abstract class AbstractAction {
    protected logger: Logger;
    protected memoryManager: EnhancedMemoryManager;
    protected actionDef: ActionDefinition;
    protected persona: PlayerPersona;

    constructor(actionDef: ActionDefinition, persona: PlayerPersona) {
        this.logger = new Logger(`Action:${actionDef.type}`);
        this.memoryManager = EnhancedMemoryManager.getInstance();
        this.actionDef = actionDef;
        this.persona = persona;
    }

    /**
     * Execute the action with full lifecycle management
     */
    async execute(
        currentState: SystemState,
        options: {
            realDataPath: string;
            maxMods?: number;
            dryRun?: boolean;
            guiMode?: boolean;
        }
    ): Promise<ActionResult> {
        const startTime = Date.now();
        this.logger.info(`Executing action: ${this.actionDef.name}`);

        const result: ActionResult = {
            actionId: this.actionDef.id,
            actionType: this.actionDef.type,
            startTime,
            endTime: 0,
            duration: 0,
            success: false,
            output: {},
            metrics: {
                memoryUsed: 0,
                cpuTime: 0,
                ioOperations: 0
            },
            validation: {
                criteriaChecked: [],
                criteriaPass: [],
                criteriaFail: []
            },
            errors: [],
            warnings: [],
            stateChanges: {}
        };

        try {
            // Pre-execution validation
            await this.validatePreConditions(currentState);

            // Record initial metrics
            const initialMetrics = await this.captureMetrics();

            // Execute the action based on interface mode
            if (options.guiMode) {
                result.output = await this.executeGUISimulation(currentState, options);
            } else {
                result.output = await this.executeCLI(currentState, options);
            }

            // Record final metrics
            const finalMetrics = await this.captureMetrics();
            result.metrics = this.calculateMetricsDelta(initialMetrics, finalMetrics);

            // Validate post-conditions
            result.validation = await this.validatePostConditions(currentState, result.output);

            // Capture state changes
            result.stateChanges = await this.captureStateChanges(currentState, result.output);

            result.success = result.validation.criteriaFail.length === 0;

        } catch (error: any) {
            this.logger.error(`Action execution failed: ${error.message}`);
            result.errors.push(error.message);
            result.success = false;

            // Handle error according to persona
            await this.handleError(error, result);
        } finally {
            result.endTime = Date.now();
            result.duration = result.endTime - result.startTime;

            // Cleanup resources
            await this.cleanup();
        }

        return result;
    }

    /**
     * CLI implementation of the action (current interface)
     */
    protected abstract executeCLI(
        currentState: SystemState,
        options: any
    ): Promise<any>;

    /**
     * GUI simulation implementation (future interface)
     */
    protected async executeGUISimulation(
        currentState: SystemState,
        options: any
    ): Promise<any> {
        this.logger.info('Simulating GUI interactions for action');

        if (!this.actionDef.guiSimulation) {
            this.logger.warn('No GUI simulation defined, falling back to CLI');
            return this.executeCLI(currentState, options);
        }

        // Simulate GUI interactions
        const simulation = this.actionDef.guiSimulation;
        const simulationResult = {
            interfaceType: simulation.interfaceType,
            interactions: [],
            visualFeedback: [],
            userExperience: {}
        };

        for (const interaction of simulation.userInteractions) {
            const interactionResult = await this.simulateGUIInteraction(interaction, currentState);
            simulationResult.interactions.push(interactionResult);

            // Add delay to simulate real user interaction timing
            await this.delay(interaction.timing.delay);
        }

        // Simulate visual feedback
        for (const feedback of simulation.visualFeedback) {
            simulationResult.visualFeedback.push({
                element: feedback,
                displayed: true,
                timing: Date.now()
            });
        }

        // Execute the actual CLI logic while simulating GUI
        const cliResult = await this.executeCLI(currentState, options);

        return {
            ...cliResult,
            guiSimulation: simulationResult
        };
    }

    /**
     * Simulate a single GUI interaction
     */
    protected async simulateGUIInteraction(
        interaction: any,
        currentState: SystemState
    ): Promise<any> {
        this.logger.debug(`Simulating ${interaction.type} on ${interaction.target}`);

        // This would be expanded to actually test GUI components when they exist
        return {
            type: interaction.type,
            target: interaction.target,
            value: interaction.value,
            success: true,
            response: interaction.expectedResponse,
            timing: {
                started: Date.now(),
                completed: Date.now() + interaction.timing.duration
            }
        };
    }

    /**
     * Validate pre-conditions before action execution
     */
    protected async validatePreConditions(currentState: SystemState): Promise<void> {
        // Check system state requirements
        if (this.actionDef.parameters.requiresDatabase && !currentState.systemHealth.databaseIntegrity) {
            throw new Error('Database integrity check failed');
        }

        // Check memory requirements
        const availableMemory = await this.getAvailableMemory();
        const requiredMemory = this.actionDef.parameters.minMemoryMB || 0;
        if (availableMemory < requiredMemory) {
            throw new Error(`Insufficient memory: ${availableMemory}MB available, ${requiredMemory}MB required`);
        }

        // Persona-specific validations
        await this.validatePersonaRequirements(currentState);
    }

    /**
     * Validate post-conditions after action execution
     */
    protected async validatePostConditions(
        currentState: SystemState,
        output: any
    ): Promise<any> {
        const validation = {
            criteriaChecked: [],
            criteriaPass: [],
            criteriaFail: []
        };

        // Check success criteria
        for (const criterion of this.actionDef.validation.successCriteria) {
            validation.criteriaChecked.push(criterion);
            
            const passed = await this.evaluateCriterion(criterion, currentState, output);
            if (passed) {
                validation.criteriaPass.push(criterion);
            } else {
                validation.criteriaFail.push(criterion);
            }
        }

        // Check performance thresholds
        for (const [metric, threshold] of Object.entries(this.actionDef.validation.performanceThresholds)) {
            const criterion = `${metric}_within_threshold`;
            validation.criteriaChecked.push(criterion);

            const actualValue = await this.getMetricValue(metric, output);
            if (actualValue <= threshold) {
                validation.criteriaPass.push(criterion);
            } else {
                validation.criteriaFail.push(criterion);
                this.logger.warn(`Performance threshold exceeded: ${metric} = ${actualValue}, threshold = ${threshold}`);
            }
        }

        return validation;
    }

    /**
     * Capture system metrics for performance tracking
     */
    protected async captureMetrics(): Promise<any> {
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();

        return {
            timestamp: Date.now(),
            memory: {
                heapUsed: memoryUsage.heapUsed,
                heapTotal: memoryUsage.heapTotal,
                rss: memoryUsage.rss,
                external: memoryUsage.external
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            }
        };
    }

    /**
     * Calculate metrics delta between two snapshots
     */
    protected calculateMetricsDelta(initial: any, final: any): any {
        return {
            memoryUsed: final.memory.heapUsed - initial.memory.heapUsed,
            cpuTime: (final.cpu.user + final.cpu.system) - (initial.cpu.user + initial.cpu.system),
            ioOperations: 0 // Would be implemented with proper IO tracking
        };
    }

    /**
     * Capture state changes caused by the action
     */
    protected async captureStateChanges(
        currentState: SystemState,
        output: any
    ): Promise<Record<string, any>> {
        // This would be implemented to track specific state changes
        // based on the action type and output
        return {};
    }

    /**
     * Handle errors according to persona behavior
     */
    protected async handleError(error: Error, result: ActionResult): Promise<void> {
        switch (this.persona.characteristics.errorHandling) {
            case 'stop_on_error':
                this.logger.error('Stopping execution due to error (persona: stop_on_error)');
                break;
            case 'retry_once':
                this.logger.warn('Error occurred, persona allows one retry');
                result.warnings.push('Retry attempted due to persona settings');
                break;
            case 'skip_and_continue':
                this.logger.warn('Error occurred, continuing due to persona settings');
                result.warnings.push('Error skipped due to persona settings');
                break;
        }
    }

    /**
     * Cleanup resources after action execution
     */
    protected async cleanup(): Promise<void> {
        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }
    }

    // Helper methods
    protected async delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    protected async getAvailableMemory(): Promise<number> {
        const memUsage = process.memoryUsage();
        return Math.max(0, 2048 - (memUsage.heapUsed / 1024 / 1024)); // Assume 2GB limit
    }

    protected async validatePersonaRequirements(currentState: SystemState): Promise<void> {
        // Implement persona-specific validation logic
    }

    protected async evaluateCriterion(
        criterion: string,
        currentState: SystemState,
        output: any
    ): Promise<boolean> {
        // Implement criterion evaluation logic
        return true; // Placeholder
    }

    protected async getMetricValue(metric: string, output: any): Promise<number> {
        // Implement metric value extraction
        return 0; // Placeholder
    }
}
