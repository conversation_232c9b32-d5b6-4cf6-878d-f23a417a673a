/**
 * Utility functions for formatting time values
 */

/**
 * Formats a duration in milliseconds to a human-readable string
 * @param ms Duration in milliseconds
 * @returns Formatted duration string (e.g., "2h 30m 15s")
 */
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  const remainingSeconds = seconds % 60;
  const remainingMinutes = minutes % 60;

  let result = '';

  if (hours > 0) {
    result += `${hours}h `;
  }

  if (remainingMinutes > 0 || hours > 0) {
    result += `${remainingMinutes}m `;
  }

  if (remainingSeconds > 0 || (hours === 0 && remainingMinutes === 0)) {
    result += `${remainingSeconds}s`;
  }

  return result.trim();
}

/**
 * Formats a date to a human-readable string
 * @param date Date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  return date.toLocaleString();
}

/**
 * Calculates the time elapsed since a given date
 * @param date Start date
 * @returns Elapsed time in milliseconds
 */
export function getElapsedTime(date: Date): number {
  return Date.now() - date.getTime();
}

/**
 * Formats the elapsed time since a given date
 * @param date Start date
 * @returns Formatted elapsed time string
 */
export function formatElapsedTime(date: Date): string {
  return formatDuration(getElapsedTime(date));
}
