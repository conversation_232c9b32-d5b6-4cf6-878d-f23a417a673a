/**
 * Types for the Semantic Understanding system
 */

/**
 * Represents a Sims 4 gameplay system
 */
export interface GameplaySystem {
  /** Unique identifier for the gameplay system */
  id: string;

  /** Display name of the gameplay system */
  name: string;

  /** Description of what the gameplay system does */
  description: string;

  /** Keywords associated with this gameplay system */
  keywords: string[];

  /** Resource types commonly associated with this gameplay system */
  relatedResourceTypes: number[];

  /** Other gameplay systems that interact with this one */
  relatedSystems: string[];

  /** When this gameplay system was introduced (base game or pack name) */
  introducedIn: string;

  /** Whether this is a core gameplay system */
  isCore: boolean;
}

/**
 * Represents a resource purpose
 */
export interface ResourcePurpose {
  /** Unique identifier for the resource purpose */
  id: string;

  /** Display name of the resource purpose */
  name: string;

  /** Description of what this purpose means */
  description: string;

  /** Resource types that can have this purpose */
  applicableResourceTypes: number[];

  /** Gameplay impact of resources with this purpose */
  gameplayImpact: GameplayImpact;

  /** Whether this purpose indicates a high risk for conflicts */
  conflictRisk: ConflictRisk;
}

/**
 * Represents a critical parameter in a resource
 */
export interface CriticalParameter {
  /** Unique identifier for the critical parameter */
  id: string;

  /** Display name of the critical parameter */
  name: string;

  /** Description of what this parameter does */
  description: string;

  /** Resource types that can have this parameter */
  applicableResourceTypes: number[];

  /** Path to the parameter in the resource (e.g., XML path) */
  path: string;

  /** Default value of the parameter */
  defaultValue?: any;

  /** Gameplay impact of modifying this parameter */
  gameplayImpact: GameplayImpact;

  /** Whether modifying this parameter indicates a high risk for conflicts */
  conflictRisk: ConflictRisk;
}

/**
 * Represents the gameplay impact of a resource or parameter
 */
export enum GameplayImpact {
  NONE = 'NONE',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * Represents the conflict risk of a resource or parameter
 */
export enum ConflictRisk {
  NONE = 'NONE',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * Represents a categorization result with confidence score
 */
export interface CategorizationResult<T> {
  /** The categorized item */
  item: T;

  /** Confidence score (0-100) */
  confidence: number;

  /** Reasons for this categorization */
  reasons: string[];

  /** Timestamp when this categorization was created */
  timestamp: number;
}

/**
 * Represents a resource purpose analysis result
 */
export interface ResourcePurposeAnalysis {
  /** The primary purpose of the resource */
  primaryPurpose: CategorizationResult<ResourcePurpose>;

  /** Secondary purposes of the resource */
  secondaryPurposes: CategorizationResult<ResourcePurpose>[];

  /** Gameplay systems affected by this resource */
  affectedSystems: CategorizationResult<GameplaySystem>[];

  /** Critical parameters modified by this resource */
  criticalParameters: CategorizationResult<CriticalParameter>[];

  /** Overall gameplay impact of this resource */
  overallGameplayImpact: GameplayImpact;

  /** Overall conflict risk of this resource */
  overallConflictRisk: ConflictRisk;
}
