import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey as AppResourceKey } from '../../../types/resource/interfaces.js';
import { ResourceMetadata } from '../../../types/resource/interfaces.js';

const log = new Logger('TerrainGeometryMetadataExtractor');

/**
 * Extracts metadata from Terrain Geometry resources.
 * Terrain Geometry resources define the shape and height of terrain in the game.
 *
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object with terrain geometry information.
 */
export async function extractTerrainGeometryMetadata(
    key: AppResourceKey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};

    try {
        // Verify this is a Terrain Geometry resource
        if (key.type !== 0x5B282D45) {
            log.warn(`Resource ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)} is not a Terrain Geometry resource`);
            return {
                contentSnippet: `[Not a Terrain Geometry resource: ${key.type.toString(16)}]`,
                extractorUsed: 'terraingeometry'
            };
        }

        log.info(`Extracting metadata from Terrain Geometry resource: ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)}`);

        // Terrain Geometry resources have a specific binary format
        // The format might be different from what we initially expected
        // Let's try to extract meaningful information from the buffer

        if (buffer.length < 4) {
            return {
                contentSnippet: '[Terrain Geometry resource is too small]',
                extractorUsed: 'terraingeometry'
            };
        }

        // Try to find a magic number or signature
        const signature = buffer.slice(0, 4).toString('utf8');
        extractedMetadata.terrainGeometrySignature = signature;

        // Extract the size of the buffer
        extractedMetadata.terrainGeometrySize = buffer.length;

        // Look for common patterns in terrain geometry data
        // Terrain geometry often contains vertex data, which consists of x, y, z coordinates
        // Each coordinate is typically a 32-bit float (4 bytes)
        // So each vertex takes 12 bytes (3 coordinates * 4 bytes)

        // Estimate the number of vertices based on the buffer size
        // This assumes that most of the buffer is vertex data
        // We subtract 32 bytes for the header
        const estimatedVertexCount = Math.floor((buffer.length - 32) / 12);
        extractedMetadata.terrainGeometryEstimatedVertexCount = estimatedVertexCount;

        // Try to extract dimensions from the buffer
        // This is a heuristic and might not be accurate for all terrain geometry resources
        let width = 0;
        let height = 0;

        // Look for width and height values in the buffer
        // These are often stored as 16-bit or 32-bit integers
        // We'll try both little-endian and big-endian formats

        // Try 16-bit integers at various offsets
        for (let offset = 0; offset < Math.min(buffer.length - 4, 32); offset += 2) {
            const value = buffer.readUInt16LE(offset);
            if (value > 0 && value < 1024) { // Reasonable size for terrain dimensions
                if (width === 0) {
                    width = value;
                } else if (height === 0) {
                    height = value;
                    break;
                }
            }
        }

        // If we couldn't find reasonable values, try 32-bit integers
        if (width === 0 || height === 0) {
            for (let offset = 0; offset < Math.min(buffer.length - 8, 32); offset += 4) {
                const value = buffer.readUInt32LE(offset);
                if (value > 0 && value < 1024) { // Reasonable size for terrain dimensions
                    if (width === 0) {
                        width = value;
                    } else if (height === 0) {
                        height = value;
                        break;
                    }
                }
            }
        }

        if (width > 0 && height > 0) {
            extractedMetadata.terrainGeometryWidth = width;
            extractedMetadata.terrainGeometryHeight = height;
            extractedMetadata.terrainGeometryDimensions = `${width}x${height}`;
        } else {
            // If we couldn't find reasonable values, use a default
            extractedMetadata.terrainGeometryDimensions = 'Unknown';
        }

        // Create a content snippet
        let contentSnippet = `Terrain Geometry`;

        if (extractedMetadata.terrainGeometrySignature) {
            contentSnippet += ` (${extractedMetadata.terrainGeometrySignature})`;
        }

        if (extractedMetadata.terrainGeometrySize) {
            contentSnippet += `, ${(extractedMetadata.terrainGeometrySize / 1024).toFixed(2)} KB`;
        }

        if (extractedMetadata.terrainGeometryDimensions && extractedMetadata.terrainGeometryDimensions !== 'Unknown') {
            contentSnippet += `, ${extractedMetadata.terrainGeometryDimensions} grid`;
        }

        if (extractedMetadata.terrainGeometryEstimatedVertexCount) {
            contentSnippet += `, ~${extractedMetadata.terrainGeometryEstimatedVertexCount} vertices`;
        }

        extractedMetadata.contentSnippet = contentSnippet;
        extractedMetadata.extractorUsed = 'terraingeometry';

        return extractedMetadata;
    } catch (error) {
        log.error(`Error extracting Terrain Geometry metadata: ${error}`);
        return {
            contentSnippet: `[Error extracting Terrain Geometry metadata: ${error}]`,
            extractorUsed: 'terraingeometry',
            extractionError: String(error)
        };
    }
}
