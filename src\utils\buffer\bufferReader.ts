/**
 * Unified Buffer Reader
 *
 * Provides safe methods for reading from buffers with comprehensive error handling,
 * boundary checking, and support for all data types used across the application.
 */

import { Logger } from '../logging/logger.js';

const logger = new Logger('BufferReader');

/**
 * Options for buffer reading operations
 */
export interface BufferReadOptions {
    /** Whether to throw an error on buffer overflow (default: false) */
    throwOnBufferOverflow?: boolean;
    /** Whether to advance position after reading (default: true) */
    advancePosition?: boolean;
    /** Field name for error messages */
    fieldName?: string;
}

/**
 * Result of a buffer read operation
 */
export interface BufferReadResult<T> {
    /** The value read from the buffer */
    value: T | null;
    /** New offset after reading */
    newOffset: number;
    /** Whether the read operation was successful */
    success: boolean;
    /** Error message if the read operation failed */
    error?: string;
}

/**
 * Unified Buffer Reader class
 * Provides safe methods for reading data from buffers with comprehensive error handling
 */
export class BufferReader {
    protected buffer: Buffer;
    protected position: number;
    protected length: number;
    protected options: Required<BufferReadOptions>;

    /**
     * Creates a new BufferReader
     * @param buffer Buffer to read from
     * @param initialPosition Initial position (default: 0)
     * @param options Reading options
     */
    constructor(buffer: Buffer, initialPosition: number = 0, options: BufferReadOptions = {}) {
        this.buffer = buffer;
        this.position = initialPosition;
        this.length = buffer.length;
        this.options = {
            throwOnBufferOverflow: false,
            advancePosition: true,
            fieldName: 'field',
            ...options
        };
    }

    /**
     * Gets the current position
     */
    public getPosition(): number {
        return this.position;
    }

    /**
     * Sets the current position
     * @param position New position
     * @returns This reader for chaining
     */
    public setPosition(position: number): BufferReader {
        if (position < 0 || position > this.length) {
            const error = `Invalid position: ${position}, buffer length: ${this.length}`;
            if (this.options.throwOnBufferOverflow) {
                throw new Error(error);
            }
            logger.warn(error);
            return this;
        }
        this.position = position;
        return this;
    }

    /**
     * Advances the position by the specified number of bytes
     * @param bytes Number of bytes to advance
     * @returns This reader for chaining
     */
    public skip(bytes: number): BufferReader {
        return this.setPosition(this.position + bytes);
    }

    /**
     * Checks if there are enough bytes remaining
     * @param bytes Number of bytes needed
     * @returns True if there are enough bytes
     */
    public hasBytes(bytes: number): boolean {
        return this.position + bytes <= this.length;
    }

    /**
     * Gets the number of bytes remaining
     */
    public remainingBytes(): number {
        return Math.max(0, this.length - this.position);
    }

    /**
     * Checks if at end of buffer
     */
    public isEOF(): boolean {
        return this.position >= this.length;
    }

    /**
     * Gets the underlying buffer
     */
    public getBuffer(): Buffer {
        return this.buffer;
    }

    /**
     * Gets the buffer length
     */
    public getLength(): number {
        return this.length;
    }

    /**
     * Safe read operation with error handling
     * @param bytes Number of bytes to read
     * @param readFn Function to perform the read
     * @param fieldName Name of the field being read
     * @param advancePosition Whether to advance position
     * @returns Read result
     */
    protected safeRead<T>(
        bytes: number,
        readFn: (buffer: Buffer, offset: number) => T,
        fieldName: string = 'field',
        advancePosition: boolean = this.options.advancePosition
    ): BufferReadResult<T> {
        if (!this.hasBytes(bytes)) {
            const error = `Buffer too small for ${fieldName} at offset ${this.position} (need ${bytes} bytes, have ${this.remainingBytes()})`;
            
            if (this.options.throwOnBufferOverflow) {
                throw new Error(error);
            }
            
            logger.warn(error);
            return {
                value: null,
                newOffset: this.position,
                success: false,
                error
            };
        }

        try {
            const value = readFn(this.buffer, this.position);
            const newOffset = advancePosition ? this.position + bytes : this.position;
            
            if (advancePosition) {
                this.position = newOffset;
            }

            return {
                value,
                newOffset,
                success: true
            };
        } catch (error: any) {
            const errorMsg = `Error reading ${fieldName} at offset ${this.position}: ${error.message || error}`;
            logger.error(errorMsg);
            
            if (this.options.throwOnBufferOverflow) {
                throw new Error(errorMsg);
            }

            return {
                value: null,
                newOffset: this.position,
                success: false,
                error: errorMsg
            };
        }
    }

    /**
     * Reads a UInt8 value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readUInt8(fieldName: string = 'UInt8', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(1, (buf, offset) => buf.readUInt8(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a UInt16LE value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readUInt16LE(fieldName: string = 'UInt16LE', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(2, (buf, offset) => buf.readUInt16LE(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a UInt16BE value
     * @param fieldName Field name for error messages  
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readUInt16BE(fieldName: string = 'UInt16BE', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(2, (buf, offset) => buf.readUInt16BE(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a UInt32LE value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readUInt32LE(fieldName: string = 'UInt32LE', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(4, (buf, offset) => buf.readUInt32LE(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a UInt32BE value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readUInt32BE(fieldName: string = 'UInt32BE', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(4, (buf, offset) => buf.readUInt32BE(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a Int8 value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readInt8(fieldName: string = 'Int8', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(1, (buf, offset) => buf.readInt8(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a Int16LE value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readInt16LE(fieldName: string = 'Int16LE', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(2, (buf, offset) => buf.readInt16LE(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a Int32LE value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readInt32LE(fieldName: string = 'Int32LE', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(4, (buf, offset) => buf.readInt32LE(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a FloatLE value
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value or null if failed
     */
    public readFloatLE(fieldName: string = 'FloatLE', advancePosition: boolean = this.options.advancePosition): number | null {
        const result = this.safeRead(4, (buf, offset) => buf.readFloatLE(offset), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a BigUInt64LE value and returns as string to avoid precision issues
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read value as string or null if failed
     */
    public readBigUInt64LE(fieldName: string = 'BigUInt64LE', advancePosition: boolean = this.options.advancePosition): string | null {
        const result = this.safeRead(8, (buf, offset) => buf.readBigUInt64LE(offset).toString(), fieldName, advancePosition);
        return result.value;
    }

    /**
     * Reads a slice of the buffer
     * @param length Length of the slice
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns Buffer slice or null if failed
     */
    public readSlice(length: number, fieldName: string = 'slice', advancePosition: boolean = this.options.advancePosition): Buffer | null {
        if (!this.hasBytes(length)) {
            const availableLength = this.remainingBytes();
            logger.warn(`Requested slice length ${length} exceeds remaining bytes ${availableLength} at position ${this.position}`);
            
            if (availableLength > 0) {
                logger.warn(`Returning smaller slice with length ${availableLength}`);
                const slice = this.buffer.slice(this.position, this.position + availableLength);
                if (advancePosition) {
                    this.position += availableLength;
                }
                return slice;
            }
            return null;
        }

        const slice = this.buffer.slice(this.position, this.position + length);
        if (advancePosition) {
            this.position += length;
        }
        return slice;
    }

    /**
     * Reads a string of specified length
     * @param length String length in bytes
     * @param encoding String encoding
     * @param fieldName Field name for error messages
     * @param advancePosition Whether to advance position
     * @returns The read string or null if failed
     */
    public readString(
        length: number, 
        encoding: BufferEncoding = 'utf8', 
        fieldName: string = 'string',
        advancePosition: boolean = this.options.advancePosition
    ): string | null {
        const slice = this.readSlice(length, fieldName, advancePosition);
        if (!slice) {
            return null;
        }

        try {
            return slice.toString(encoding);
        } catch (error: any) {
            logger.error(`Error decoding string: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Reads a length-prefixed string
     * @param lengthSize Size of the length prefix (1, 2, or 4 bytes)
     * @param encoding String encoding
     * @param fieldName Field name for error messages
     * @returns The read string or null if failed
     */
    public readLengthPrefixedString(
        lengthSize: 1 | 2 | 4 = 2,
        encoding: BufferEncoding = 'utf8',
        fieldName: string = 'length-prefixed string'
    ): string | null {
        let length: number | null;

        switch (lengthSize) {
            case 1:
                length = this.readUInt8(`${fieldName}_length`);
                break;
            case 2:
                length = this.readUInt16LE(`${fieldName}_length`);
                break;
            case 4:
                length = this.readUInt32LE(`${fieldName}_length`);
                break;
            default:
                logger.error(`Invalid length size: ${lengthSize}`);
                return null;
        }

        if (length === null || length === 0) {
            return length === 0 ? '' : null;
        }

        return this.readString(length, encoding, fieldName);
    }

    /**
     * Reads a null-terminated string
     * @param maxLength Maximum length to read
     * @param encoding String encoding
     * @param fieldName Field name for error messages
     * @returns The read string or null if failed
     */
    public readNullTerminatedString(
        maxLength: number = this.remainingBytes(),
        encoding: BufferEncoding = 'utf8',
        fieldName: string = 'null-terminated string'
    ): string | null {
        const startPosition = this.position;
        let nullPosition = -1;

        // Find the null terminator
        for (let i = 0; i < maxLength && this.position + i < this.length; i++) {
            const byte = this.buffer.readUInt8(this.position + i);
            if (byte === 0) {
                nullPosition = this.position + i;
                break;
            }
        }

        if (nullPosition === -1) {
            logger.warn(`No null terminator found within ${maxLength} bytes at position ${this.position}`);
            return null;
        }

        const stringLength = nullPosition - this.position;
        const result = this.readString(stringLength, encoding, fieldName);
        
        // Skip the null terminator
        this.skip(1);
        
        return result;
    }
}

/**
 * Create a BufferReader instance
 * @param buffer Buffer to read from
 * @param initialPosition Initial position
 * @param options Reading options
 * @returns BufferReader instance
 */
export function createBufferReader(
    buffer: Buffer, 
    initialPosition: number = 0, 
    options: BufferReadOptions = {}
): BufferReader {
    return new BufferReader(buffer, initialPosition, options);
}

/**
 * Legacy alias for backward compatibility
 */
export const SafeBufferReader = BufferReader; 