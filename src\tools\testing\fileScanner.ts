/**
 * Comprehensive File Scanner for Sims 4 Mod Management
 *
 * This module provides unified utilities for scanning directories for Sims 4 mod files.
 * Consolidates all folder scanning functionality to avoid duplicates.
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { createPrintFunction } from '../../utils/console/consoleOutput.js';
import { logger } from '../../utils/logging/logger.js';

// Create a print function for direct output
const print = createPrintFunction();

export interface ScanOptions {
    /** Maximum number of files to find */
    maxFiles?: number;
    /** Maximum depth to scan */
    maxDepth?: number;
    /** Folders to skip during scanning */
    skipFolders?: string[];
    /** Progress callback function */
    progressCallback?: (count: number, path: string) => void;
    /** Randomize results */
    randomize?: boolean;
    /** Include file metadata */
    includeMetadata?: boolean;
    /** File extensions to include */
    includeExtensions?: string[];
    /** Minimum file size in bytes */
    minFileSize?: number;
    /** Maximum file size in bytes */
    maxFileSize?: number;
}

export interface ScannedFile {
    /** Full path to the file */
    path: string;
    /** File name without path */
    name: string;
    /** File extension */
    extension: string;
    /** File size in bytes */
    size?: number;
    /** Last modified timestamp */
    lastModified?: number;
    /** Relative path from scan root */
    relativePath?: string;
    /** Directory depth from scan root */
    depth?: number;
}

export interface ScanResult {
    /** Array of scanned files */
    files: ScannedFile[];
    /** Total number of files found */
    totalFiles: number;
    /** Total size of all files in bytes */
    totalSize: number;
    /** Scan duration in milliseconds */
    scanDuration: number;
    /** Number of directories scanned */
    directoriesScanned: number;
    /** Number of files skipped due to filters */
    filesSkipped: number;
    /** Scan statistics by extension */
    extensionStats: Map<string, { count: number; size: number }>;
}

/**
 * Find package files in a directory and its subdirectories with options
 * @param directory Directory to scan
 * @param options Scan options
 * @returns Array of package file paths
 */
export async function findPackageFiles(
    directory: string,
    options: {
        maxFiles?: number;
        maxDepth?: number;
        skipFolders?: string[];
        progressCallback?: (count: number, path: string) => void;
        randomize?: boolean;
    } = {}
): Promise<string[]> {
    // Default options
    const maxFiles = options.maxFiles || Number.MAX_SAFE_INTEGER;
    const maxDepth = options.maxDepth || Number.MAX_SAFE_INTEGER;
    const skipFolders = options.skipFolders || ['_Backup', 'cache', 'Cache', 'Backups'];
    const progressCallback = options.progressCallback;
    const randomize = options.randomize || false;

    // Result array
    const packageFiles: string[] = [];

    // Internal recursive function with depth tracking
    async function scanDirectory(dir: string, currentDepth: number): Promise<void> {
        // Stop if we've reached the maximum files or depth
        if (packageFiles.length >= maxFiles || currentDepth > maxDepth) {
            return;
        }

        try {
            // Read directory contents
            const entries = await fs.readdir(dir, { withFileTypes: true });

            // Process files first (for faster results)
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (packageFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isFile() && entry.name.toLowerCase().endsWith('.package')) {
                    // Add package file
                    packageFiles.push(fullPath);

                    // Call progress callback if provided
                    if (progressCallback) {
                        progressCallback(packageFiles.length, fullPath);
                    }
                }
            }

            // Then process directories
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (packageFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isDirectory()) {
                    // Skip specified folders
                    if (skipFolders.some(folder => entry.name.toLowerCase() === folder.toLowerCase())) {
                        continue;
                    }

                    // Skip folders that start with underscore (common for backup folders)
                    if (entry.name.startsWith('_')) {
                        continue;
                    }

                    // Recursively scan subdirectory
                    await scanDirectory(fullPath, currentDepth + 1);
                }
            }
        } catch (error: any) {
            print(`Error scanning directory ${dir}: ${error.message || error}`);
        }
    }

    // Start scanning from the root directory
    await scanDirectory(directory, 0);

    // Randomize results if requested
    if (randomize && packageFiles.length > 0) {
        // Fisher-Yates shuffle
        for (let i = packageFiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [packageFiles[i], packageFiles[j]] = [packageFiles[j], packageFiles[i]];
        }
    }

    return packageFiles;
}

/**
 * Find TS4Script files in a directory and its subdirectories with options
 * @param directory Directory to scan
 * @param options Scan options
 * @returns Array of TS4Script file paths
 */
export async function findTS4ScriptFiles(
    directory: string,
    options: {
        maxFiles?: number;
        maxDepth?: number;
        skipFolders?: string[];
        progressCallback?: (count: number, path: string) => void;
        randomize?: boolean;
    } = {}
): Promise<string[]> {
    // Default options
    const maxFiles = options.maxFiles || Number.MAX_SAFE_INTEGER;
    const maxDepth = options.maxDepth || Number.MAX_SAFE_INTEGER;
    const skipFolders = options.skipFolders || ['_Backup', 'cache', 'Cache', 'Backups'];
    const progressCallback = options.progressCallback;
    const randomize = options.randomize || false;

    // Result array
    const ts4ScriptFiles: string[] = [];

    // Internal recursive function with depth tracking
    async function scanDirectory(dir: string, currentDepth: number): Promise<void> {
        // Stop if we've reached the maximum files or depth
        if (ts4ScriptFiles.length >= maxFiles || currentDepth > maxDepth) {
            return;
        }

        try {
            // Read directory contents
            const entries = await fs.readdir(dir, { withFileTypes: true });

            // Process files first (for faster results)
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (ts4ScriptFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isFile() && entry.name.toLowerCase().endsWith('.ts4script')) {
                    // Add TS4Script file
                    ts4ScriptFiles.push(fullPath);

                    // Call progress callback if provided
                    if (progressCallback) {
                        progressCallback(ts4ScriptFiles.length, fullPath);
                    }
                }
            }

            // Then process directories
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (ts4ScriptFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isDirectory()) {
                    // Skip specified folders
                    if (skipFolders.some(folder => entry.name.toLowerCase() === folder.toLowerCase())) {
                        continue;
                    }

                    // Skip folders that start with underscore (common for backup folders)
                    if (entry.name.startsWith('_')) {
                        continue;
                    }

                    // Recursively scan subdirectory
                    await scanDirectory(fullPath, currentDepth + 1);
                }
            }
        } catch (error: any) {
            print(`Error scanning directory ${dir} for TS4Script files: ${error.message || error}`);
        }
    }

    // Start scanning from the root directory
    await scanDirectory(directory, 0);

    // Randomize results if requested
    if (randomize && ts4ScriptFiles.length > 0) {
        // Fisher-Yates shuffle
        for (let i = ts4ScriptFiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [ts4ScriptFiles[i], ts4ScriptFiles[j]] = [ts4ScriptFiles[j], ts4ScriptFiles[i]];
        }
    }

    return ts4ScriptFiles;
}

/**
 * Comprehensive folder scanner that consolidates all scanning functionality
 * @param directory Directory to scan
 * @param options Scan options
 * @returns Detailed scan result with metadata
 */
export async function scanModsFolder(directory: string, options: ScanOptions = {}): Promise<ScanResult> {
    const startTime = Date.now();
    const {
        maxFiles = Number.MAX_SAFE_INTEGER,
        maxDepth = Number.MAX_SAFE_INTEGER,
        skipFolders = ['_Backup', 'cache', 'Cache', 'Backups', '__MACOSX', '.DS_Store', 'Thumbs.db', '.git', 'node_modules'],
        progressCallback,
        randomize = false,
        includeMetadata = true,
        includeExtensions = ['.package', '.ts4script'],
        minFileSize = 100,
        maxFileSize = 500 * 1024 * 1024 // 500MB
    } = options;

    const files: ScannedFile[] = [];
    const extensionStats = new Map<string, { count: number; size: number }>();
    let directoriesScanned = 0;
    let filesSkipped = 0;

    logger.info(`Starting comprehensive folder scan: ${directory}`);

    try {
        // Verify directory exists
        const dirStat = await fs.stat(directory);
        if (!dirStat.isDirectory()) {
            throw new Error(`Path is not a directory: ${directory}`);
        }

        // Recursive scan with metadata collection
        await scanDirectoryRecursive(
            directory,
            directory,
            0,
            files,
            extensionStats,
            { directoriesScanned: 0, filesSkipped: 0 },
            {
                maxFiles,
                maxDepth,
                skipFolders,
                progressCallback,
                includeMetadata,
                includeExtensions,
                minFileSize,
                maxFileSize
            }
        );

    } catch (error: any) {
        logger.error(`Error scanning folder ${directory}: ${error.message}`);
        throw error;
    }

    // Randomize if requested
    if (randomize && files.length > 0) {
        for (let i = files.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [files[i], files[j]] = [files[j], files[i]];
        }
    }

    const scanDuration = Date.now() - startTime;
    const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);

    const result: ScanResult = {
        files,
        totalFiles: files.length,
        totalSize,
        scanDuration,
        directoriesScanned,
        filesSkipped,
        extensionStats
    };

    logger.info(`Folder scan completed: ${files.length} files found in ${scanDuration}ms`);
    logScanStatistics(result);

    return result;
}

/**
 * Recursive directory scanning with comprehensive metadata collection
 */
async function scanDirectoryRecursive(
    currentPath: string,
    rootPath: string,
    depth: number,
    files: ScannedFile[],
    extensionStats: Map<string, { count: number; size: number }>,
    counters: { directoriesScanned: number; filesSkipped: number },
    options: {
        maxFiles: number;
        maxDepth: number;
        skipFolders: string[];
        progressCallback?: (count: number, path: string) => void;
        includeMetadata: boolean;
        includeExtensions: string[];
        minFileSize: number;
        maxFileSize: number;
    }
): Promise<void> {
    // Check limits
    if (files.length >= options.maxFiles || depth > options.maxDepth) {
        return;
    }

    try {
        const entries = await fs.readdir(currentPath, { withFileTypes: true });
        counters.directoriesScanned++;

        // Process files first
        for (const entry of entries) {
            if (files.length >= options.maxFiles) break;

            const fullPath = path.join(currentPath, entry.name);

            if (entry.isFile()) {
                await processFile(
                    fullPath,
                    rootPath,
                    depth,
                    files,
                    extensionStats,
                    counters,
                    options
                );
            }
        }

        // Then process directories
        for (const entry of entries) {
            if (files.length >= options.maxFiles) break;

            const fullPath = path.join(currentPath, entry.name);

            if (entry.isDirectory()) {
                // Skip excluded folders
                if (shouldSkipFolder(entry.name, options.skipFolders)) {
                    counters.filesSkipped++;
                    continue;
                }

                // Recursively scan subdirectory
                await scanDirectoryRecursive(
                    fullPath,
                    rootPath,
                    depth + 1,
                    files,
                    extensionStats,
                    counters,
                    options
                );
            }
        }
    } catch (error: any) {
        logger.warn(`Error scanning directory ${currentPath}: ${error.message}`);
    }
}

/**
 * Process a single file with metadata collection
 */
async function processFile(
    filePath: string,
    rootPath: string,
    depth: number,
    files: ScannedFile[],
    extensionStats: Map<string, { count: number; size: number }>,
    counters: { directoriesScanned: number; filesSkipped: number },
    options: {
        progressCallback?: (count: number, path: string) => void;
        includeMetadata: boolean;
        includeExtensions: string[];
        minFileSize: number;
        maxFileSize: number;
    }
): Promise<void> {
    try {
        const extension = path.extname(filePath).toLowerCase();
        const name = path.basename(filePath);

        // Check extension filter
        if (!options.includeExtensions.includes(extension)) {
            counters.filesSkipped++;
            return;
        }

        // Get file metadata if requested
        let size = 0;
        let lastModified = 0;

        if (options.includeMetadata) {
            const stat = await fs.stat(filePath);
            size = stat.size;
            lastModified = stat.mtime.getTime();

            // Check size limits
            if (size < options.minFileSize || size > options.maxFileSize) {
                counters.filesSkipped++;
                return;
            }
        }

        // Create scanned file entry
        const scannedFile: ScannedFile = {
            path: filePath,
            name,
            extension,
            ...(options.includeMetadata && {
                size,
                lastModified,
                relativePath: path.relative(rootPath, filePath),
                depth
            })
        };

        files.push(scannedFile);

        // Update extension statistics
        const stats = extensionStats.get(extension) || { count: 0, size: 0 };
        stats.count++;
        stats.size += size;
        extensionStats.set(extension, stats);

        // Progress callback
        if (options.progressCallback) {
            options.progressCallback(files.length, filePath);
        }

    } catch (error: any) {
        logger.warn(`Error processing file ${filePath}: ${error.message}`);
        counters.filesSkipped++;
    }
}

/**
 * Check if a folder should be skipped
 */
function shouldSkipFolder(folderName: string, skipFolders: string[]): boolean {
    // Skip hidden folders
    if (folderName.startsWith('.')) return true;

    // Skip folders that start with underscore
    if (folderName.startsWith('_')) return true;

    // Check skip list
    return skipFolders.some(pattern => {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');
            return regex.test(folderName);
        } else {
            return folderName.toLowerCase().includes(pattern.toLowerCase());
        }
    });
}

/**
 * Log scan statistics
 */
function logScanStatistics(result: ScanResult): void {
    logger.info(`Scan Statistics:`);
    logger.info(`  Total files: ${result.totalFiles}`);
    logger.info(`  Total size: ${formatBytes(result.totalSize)}`);
    logger.info(`  Directories scanned: ${result.directoriesScanned}`);
    logger.info(`  Files skipped: ${result.filesSkipped}`);
    logger.info(`  Scan duration: ${result.scanDuration}ms`);

    if (result.extensionStats.size > 0) {
        logger.info(`  Extension breakdown:`);
        for (const [ext, stats] of result.extensionStats) {
            logger.info(`    ${ext}: ${stats.count} files (${formatBytes(stats.size)})`);
        }
    }
}

/**
 * Format bytes to human-readable string
 */
function formatBytes(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
}
