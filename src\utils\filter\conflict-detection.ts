// Import from canonical sources
import { ResourceKey } from '../../types/resource/interfaces.js';
import { ResourcePackageInfo } from '../../types/resource/conflicts.js';
import { ConflictResult, ConflictType, ConflictSeverity, ConflictInfo } from '../../types/conflict/index.js';


export interface ConflictDetectionOptions {
  similarityThreshold: number;
  strictMode: boolean;
}

// Function returns ConflictInfo[]
export function detectConflicts(
  resources: ResourceKey[],
  options: ConflictDetectionOptions = { similarityThreshold: 0.8, strictMode: false }
): ConflictInfo[] { // Use local ConflictInfo
  const conflicts: ConflictInfo[] = []; // Use local ConflictInfo

  const resourcesByType = resources.reduce((acc, resource) => {
    const type = resource.type;
    if (!acc[type]) acc[type] = [];
    acc[type].push(resource);
    return acc;
  }, {} as Record<number, ResourceKey[]>);

  Object.entries(resourcesByType).forEach(([type, typeResources]) => {
    const nameMap = new Map<string, ResourceKey[]>();
    typeResources.forEach(resource => {
      const name = resource.name;
      if (name !== undefined && name !== null) {
        if (!nameMap.has(name)) nameMap.set(name, []);
        nameMap.get(name)?.push(resource);
      }
    });

    nameMap.forEach((resourcesForKey, name) => {
      if (resourcesForKey.length > 1) {
        const conflictInfo: ConflictInfo = { // Use local ConflictInfo
            id: `${type}-${name}`,
            type: 'RESOURCE' as any, // Cast string literal to any
            severity: 'MEDIUM' as any, // Cast string literal to any
            description: `Multiple resources found with name "${name}" of type ${type}`,
            affectedResources: resourcesForKey,
            timestamp: Date.now(),
            recommendations: [
                'Consider renaming one of the resources to avoid conflicts.',
                'Check if these are different versions of the same resource.'
            ]
        };
        conflicts.push(conflictInfo);
      }
    });
  });
  return conflicts;
}

export function analyzeConflicts(
  resources: ResourceKey[],
  options: ConflictDetectionOptions = { similarityThreshold: 0.8, strictMode: false }
): {
  conflicts: ConflictInfo[]; // Use local ConflictInfo
  metrics: { totalResources: number; conflictCount: number; affectedResources: number; };
} {
  const conflictsList: ConflictInfo[] = detectConflicts(resources, options); // Use local ConflictInfo
  const affectedResources = new Set<ResourceKey>();
  conflictsList.forEach((conflict: ConflictInfo) => { // Use local ConflictInfo
    conflict.affectedResources?.forEach((resource: ResourceKey) => {
      affectedResources.add(resource);
    });
  });
  return {
    conflicts: conflictsList,
    metrics: { totalResources: resources.length, conflictCount: conflictsList.length, affectedResources: affectedResources.size }
  };
}
