/**
 * Multi-Level Filtering Conflict Detector
 * 
 * This module provides a conflict detector that uses a multi-level filtering approach
 * to efficiently detect conflicts between resources. It reduces the computational complexity
 * from O(n²) to a more manageable level by filtering resources at each level.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../types/database.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/index.js';
import { ConflictDetectorBase } from '../ConflictDetectorBase.js';
import { DatabaseService } from '../../databaseService.js';
import { ContentAddressableStorage } from '../../storage/ContentAddressableStorage.js';
import { v4 as uuidv4 } from 'uuid';
import { createHash } from 'crypto';

// Create a logger for this module
const logger = new Logger('MultiLevelConflictDetector');

/**
 * Multi-level conflict detector options
 */
export interface MultiLevelConflictDetectorOptions {
    similarityThreshold?: number;
    maxContentSize?: number;
    excludeTypes?: number[];
    includeTypes?: number[];
    enableSignatureFiltering?: boolean;
    enableMetadataFiltering?: boolean;
    enableDeepComparison?: boolean;
}

/**
 * Potential conflict between resources
 */
interface PotentialConflict {
    resource1: ResourceInfo;
    resource2: ResourceInfo;
    similarityScore?: number;
    reason: string;
}

/**
 * Resource group by type
 */
type ResourceTypeGroup = Map<number, ResourceInfo[]>;

/**
 * Resource group by metadata
 */
type ResourceMetadataGroup = Map<string, ResourceInfo[]>;

/**
 * Multi-Level Conflict Detector class
 */
export class MultiLevelConflictDetector extends ConflictDetectorBase {
    private databaseService: DatabaseService;
    private contentStorage: ContentAddressableStorage;
    private options: MultiLevelConflictDetectorOptions;
    
    /**
     * Create a new multi-level conflict detector
     * @param databaseService Database service
     * @param options Conflict detector options
     * @param logger Logger instance
     */
    constructor(
        databaseService: DatabaseService,
        options: MultiLevelConflictDetectorOptions = {},
        logger?: Logger
    ) {
        super(logger || new Logger('MultiLevelConflictDetector'));
        
        this.databaseService = databaseService;
        this.contentStorage = ContentAddressableStorage.getInstance();
        
        // Set default options
        this.options = {
            similarityThreshold: options.similarityThreshold || 0.8,
            maxContentSize: options.maxContentSize || 1048576, // 1 MB
            excludeTypes: options.excludeTypes || [],
            includeTypes: options.includeTypes || [],
            enableSignatureFiltering: options.enableSignatureFiltering !== false,
            enableMetadataFiltering: options.enableMetadataFiltering !== false,
            enableDeepComparison: options.enableDeepComparison !== false
        };
        
        this.logger.info('Multi-level conflict detector created');
    }
    
    /**
     * Initialize the conflict detector
     * @returns Promise resolving when initialization is complete
     */
    public async initialize(): Promise<void> {
        try {
            this.logger.info('Initializing multi-level conflict detector');
            
            // Initialize content storage
            await this.contentStorage.initialize();
            
            this.logger.info('Multi-level conflict detector initialized');
        } catch (error: any) {
            this.logger.error(`Error initializing multi-level conflict detector: ${error.message || error}`);
            throw error;
        }
    }
    
    /**
     * Detect conflicts between resources
     * @param resources List of resources to check for conflicts
     * @returns Promise resolving to a list of detected conflicts
     */
    public async detectConflicts(resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        try {
            this.logger.info(`Detecting conflicts between ${resources.length} resources using multi-level filtering`);
            
            // Level 1: Filter by TGI
            const tgiGroups = await this.filterByTGI(resources);
            this.logger.info(`TGI filtering: ${tgiGroups.size} type groups`);
            
            // Level 2: Filter by metadata
            const metadataGroups = await this.filterByMetadata(tgiGroups);
            
            // Level 3: Filter by signature
            const potentialConflicts = await this.filterBySignature(metadataGroups);
            this.logger.info(`Signature filtering: ${potentialConflicts.length} potential conflicts`);
            
            // Level 4: Deep content comparison
            const conflicts = await this.compareContent(potentialConflicts);
            this.logger.info(`Deep comparison: ${conflicts.length} confirmed conflicts`);
            
            return conflicts;
        } catch (error: any) {
            this.logger.error(`Error detecting conflicts: ${error.message || error}`);
            return [];
        }
    }
    
    /**
     * Filter resources by Type, Group, and Instance (TGI)
     * @param resources List of resources to filter
     * @returns Map of resource type to list of resources
     * @private
     */
    private async filterByTGI(resources: ResourceInfo[]): Promise<ResourceTypeGroup> {
        const typeGroups = new Map<number, ResourceInfo[]>();
        
        // Group resources by type
        for (const resource of resources) {
            // Skip excluded types
            if (this.options.excludeTypes?.length && this.options.excludeTypes.includes(resource.key.type)) {
                continue;
            }
            
            // Only include specified types if includeTypes is not empty
            if (this.options.includeTypes?.length && !this.options.includeTypes.includes(resource.key.type)) {
                continue;
            }
            
            // Add resource to type group
            if (!typeGroups.has(resource.key.type)) {
                typeGroups.set(resource.key.type, []);
            }
            
            typeGroups.get(resource.key.type)?.push(resource);
        }
        
        return typeGroups;
    }
    
    /**
     * Filter resources by metadata (size, name, path patterns)
     * @param typeGroups Map of resource type to list of resources
     * @returns Map of resource type to list of resource groups
     * @private
     */
    private async filterByMetadata(typeGroups: ResourceTypeGroup): Promise<Map<number, ResourceInfo[][]>> {
        const result = new Map<number, ResourceInfo[][]>();
        
        // Skip metadata filtering if disabled
        if (!this.options.enableMetadataFiltering) {
            // Just put each type group as a single group
            for (const [type, resources] of typeGroups.entries()) {
                result.set(type, [resources]);
            }
            return result;
        }
        
        // Process each type group
        for (const [type, resources] of typeGroups.entries()) {
            // Skip if only one resource
            if (resources.length <= 1) {
                continue;
            }
            
            // Group resources by size range
            const sizeGroups = new Map<string, ResourceInfo[]>();
            for (const resource of resources) {
                const size = resource.metadata.size || 0;
                // Group by size range (within 10%)
                const sizeRange = Math.floor(size / (size * 0.1));
                const key = `size_${sizeRange}`;
                
                if (!sizeGroups.has(key)) {
                    sizeGroups.set(key, []);
                }
                
                sizeGroups.get(key)?.push(resource);
            }
            
            // Filter out groups with only one resource
            const filteredGroups = Array.from(sizeGroups.values())
                .filter(group => group.length > 1);
            
            if (filteredGroups.length > 0) {
                result.set(type, filteredGroups);
            }
        }
        
        return result;
    }
    
    /**
     * Filter resources by signature (SimHash or MinHash)
     * @param metadataGroups Map of resource type to list of resource groups
     * @returns List of potential conflicts
     * @private
     */
    private async filterBySignature(metadataGroups: Map<number, ResourceInfo[][]>): Promise<PotentialConflict[]> {
        const potentialConflicts: PotentialConflict[] = [];
        
        // Skip signature filtering if disabled
        if (!this.options.enableSignatureFiltering) {
            // Just compare all resources in each group
            for (const [type, groups] of metadataGroups.entries()) {
                for (const group of groups) {
                    for (let i = 0; i < group.length; i++) {
                        for (let j = i + 1; j < group.length; j++) {
                            potentialConflicts.push({
                                resource1: group[i],
                                resource2: group[j],
                                reason: 'Same type and metadata'
                            });
                        }
                    }
                }
            }
            return potentialConflicts;
        }
        
        // Process each type and group
        for (const [type, groups] of metadataGroups.entries()) {
            for (const group of groups) {
                // Skip if only one resource
                if (group.length <= 1) {
                    continue;
                }
                
                // Get or calculate signatures for each resource
                const resourceSignatures = new Map<ResourceInfo, string>();
                for (const resource of group) {
                    const signature = await this.getResourceSignature(resource);
                    if (signature) {
                        resourceSignatures.set(resource, signature);
                    }
                }
                
                // Group resources by signature bucket
                const signatureBuckets = new Map<string, ResourceInfo[]>();
                for (const [resource, signature] of resourceSignatures.entries()) {
                    // Use first 8 characters of signature as bucket key
                    const bucketKey = signature.substring(0, 8);
                    
                    if (!signatureBuckets.has(bucketKey)) {
                        signatureBuckets.set(bucketKey, []);
                    }
                    
                    signatureBuckets.get(bucketKey)?.push(resource);
                }
                
                // Find potential conflicts within each bucket
                for (const [bucket, bucketResources] of signatureBuckets.entries()) {
                    // Skip if only one resource in bucket
                    if (bucketResources.length <= 1) {
                        continue;
                    }
                    
                    // Compare each pair of resources in the bucket
                    for (let i = 0; i < bucketResources.length; i++) {
                        for (let j = i + 1; j < bucketResources.length; j++) {
                            const resource1 = bucketResources[i];
                            const resource2 = bucketResources[j];
                            
                            // Calculate similarity between signatures
                            const signature1 = resourceSignatures.get(resource1);
                            const signature2 = resourceSignatures.get(resource2);
                            
                            if (signature1 && signature2) {
                                const similarityScore = this.calculateSignatureSimilarity(signature1, signature2);
                                
                                // Add as potential conflict if similarity is above threshold
                                if (similarityScore >= this.options.similarityThreshold!) {
                                    potentialConflicts.push({
                                        resource1,
                                        resource2,
                                        similarityScore,
                                        reason: `Signature similarity: ${similarityScore.toFixed(2)}`
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return potentialConflicts;
    }
    
    /**
     * Compare content of potential conflicts
     * @param potentialConflicts List of potential conflicts
     * @returns List of confirmed conflicts
     * @private
     */
    private async compareContent(potentialConflicts: PotentialConflict[]): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];
        
        // Skip deep comparison if disabled
        if (!this.options.enableDeepComparison) {
            // Convert all potential conflicts to confirmed conflicts
            for (const conflict of potentialConflicts) {
                conflicts.push(this.createConflictInfo(
                    conflict.resource1,
                    conflict.resource2,
                    ConflictType.CONTENT,
                    ConflictSeverity.MEDIUM,
                    `Potential conflict detected: ${conflict.reason}`,
                    conflict.similarityScore || 0.5
                ));
            }
            return conflicts;
        }
        
        // Process each potential conflict
        for (const conflict of potentialConflicts) {
            try {
                // Get content for both resources
                const content1 = await this.getResourceContent(conflict.resource1);
                const content2 = await this.getResourceContent(conflict.resource2);
                
                if (!content1 || !content2) {
                    continue;
                }
                
                // Compare content
                const comparisonResult = await this.compareResourceContent(
                    conflict.resource1,
                    content1,
                    conflict.resource2,
                    content2
                );
                
                if (comparisonResult.isConflict) {
                    conflicts.push(this.createConflictInfo(
                        conflict.resource1,
                        conflict.resource2,
                        ConflictType.CONTENT,
                        comparisonResult.severity,
                        comparisonResult.description,
                        comparisonResult.confidence
                    ));
                }
            } catch (error: any) {
                this.logger.error(`Error comparing content: ${error.message || error}`);
            }
        }
        
        return conflicts;
    }
    
    /**
     * Get or calculate resource signature
     * @param resource Resource to get signature for
     * @returns Promise resolving to the signature, or null if not available
     * @private
     */
    private async getResourceSignature(resource: ResourceInfo): Promise<string | null> {
        try {
            // Check if signature is already in database
            const signature = await this.databaseService.get(`
                SELECT signatureValue
                FROM ResourceSignatures
                WHERE resourceId = ? AND signatureType = 'simhash'
            `, [resource.metadata.resourceId]);
            
            if (signature?.signatureValue) {
                return signature.signatureValue;
            }
            
            // Calculate signature
            const content = await this.getResourceContent(resource);
            if (!content) {
                return null;
            }
            
            // Calculate SimHash
            const simhash = this.calculateSimHash(content.toString());
            
            // Store signature in database
            await this.databaseService.run(`
                INSERT OR REPLACE INTO ResourceSignatures (
                    resourceId, signatureType, signatureValue, timestamp
                ) VALUES (?, ?, ?, ?)
            `, [
                resource.metadata.resourceId,
                'simhash',
                simhash,
                Date.now()
            ]);
            
            return simhash;
        } catch (error: any) {
            this.logger.error(`Error getting resource signature: ${error.message || error}`);
            return null;
        }
    }
    
    /**
     * Get resource content
     * @param resource Resource to get content for
     * @returns Promise resolving to the content buffer, or null if not available
     * @private
     */
    private async getResourceContent(resource: ResourceInfo): Promise<Buffer | null> {
        try {
            // Check if content path is available
            if (resource.metadata.contentPath) {
                return await this.contentStorage.getContent(resource.metadata.contentPath);
            }
            
            // Get content from database
            const content = await this.databaseService.get(`
                SELECT content
                FROM ResourceContents
                WHERE resourceId = ?
            `, [resource.metadata.resourceId]);
            
            if (content?.content) {
                return Buffer.from(content.content);
            }
            
            return null;
        } catch (error: any) {
            this.logger.error(`Error getting resource content: ${error.message || error}`);
            return null;
        }
    }
    
    /**
     * Calculate SimHash for content
     * @param content Content to hash
     * @returns SimHash as hex string
     * @private
     */
    private calculateSimHash(content: string): string {
        // Simple implementation of SimHash
        // In a real implementation, this would be more sophisticated
        return createHash('sha256').update(content).digest('hex');
    }
    
    /**
     * Calculate similarity between two signatures
     * @param signature1 First signature
     * @param signature2 Second signature
     * @returns Similarity score (0-1)
     * @private
     */
    private calculateSignatureSimilarity(signature1: string, signature2: string): number {
        // Simple implementation of signature similarity
        // In a real implementation, this would be more sophisticated
        let matchingChars = 0;
        const length = Math.min(signature1.length, signature2.length);
        
        for (let i = 0; i < length; i++) {
            if (signature1[i] === signature2[i]) {
                matchingChars++;
            }
        }
        
        return matchingChars / length;
    }
    
    /**
     * Compare resource content
     * @param resource1 First resource
     * @param content1 First resource content
     * @param resource2 Second resource
     * @param content2 Second resource content
     * @returns Comparison result
     * @private
     */
    private async compareResourceContent(
        resource1: ResourceInfo,
        content1: Buffer,
        resource2: ResourceInfo,
        content2: Buffer
    ): Promise<{
        isConflict: boolean;
        severity: ConflictSeverity;
        description: string;
        confidence: number;
    }> {
        // Simple content comparison
        // In a real implementation, this would be more sophisticated
        
        // Check if content is identical
        if (content1.equals(content2)) {
            return {
                isConflict: true,
                severity: ConflictSeverity.LOW,
                description: 'Identical content in different resources',
                confidence: 1.0
            };
        }
        
        // Check if content is similar
        const similarity = this.calculateContentSimilarity(content1, content2);
        
        if (similarity >= this.options.similarityThreshold!) {
            return {
                isConflict: true,
                severity: ConflictSeverity.MEDIUM,
                description: `Similar content (${(similarity * 100).toFixed(1)}% similarity)`,
                confidence: similarity
            };
        }
        
        return {
            isConflict: false,
            severity: ConflictSeverity.LOW,
            description: 'No conflict detected',
            confidence: 0.0
        };
    }
    
    /**
     * Calculate similarity between two content buffers
     * @param content1 First content buffer
     * @param content2 Second content buffer
     * @returns Similarity score (0-1)
     * @private
     */
    private calculateContentSimilarity(content1: Buffer, content2: Buffer): number {
        // Simple implementation of content similarity
        // In a real implementation, this would be more sophisticated
        
        // If sizes are very different, similarity is low
        const sizeRatio = Math.min(content1.length, content2.length) / Math.max(content1.length, content2.length);
        
        // If size ratio is too low, return it as similarity
        if (sizeRatio < 0.5) {
            return sizeRatio;
        }
        
        // Sample the content to compare
        const sampleSize = Math.min(1024, content1.length, content2.length);
        let matchingBytes = 0;
        
        for (let i = 0; i < sampleSize; i++) {
            if (content1[i] === content2[i]) {
                matchingBytes++;
            }
        }
        
        return (matchingBytes / sampleSize) * sizeRatio;
    }
    
    /**
     * Create a conflict info object
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param type Conflict type
     * @param severity Conflict severity
     * @param description Conflict description
     * @param confidence Confidence score (0-1)
     * @returns Conflict info object
     * @private
     */
    private createConflictInfo(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        type: ConflictType,
        severity: ConflictSeverity,
        description: string,
        confidence: number
    ): ConflictInfo {
        return {
            id: uuidv4(),
            type,
            severity,
            description,
            affectedResources: [resource1.key, resource2.key],
            timestamp: Date.now(),
            recommendations: [
                'Review both resources to determine which one to keep',
                'Consider merging the resources if they contain complementary changes'
            ],
            metadata: {
                resource1: {
                    name: resource1.metadata.name,
                    path: resource1.metadata.path,
                    size: resource1.metadata.size
                },
                resource2: {
                    name: resource2.metadata.name,
                    path: resource2.metadata.path,
                    size: resource2.metadata.size
                }
            },
            confidence
        };
    }
}
