/**
 * Enhanced Dependency Pipeline Adapter
 *
 * This adapter connects the EnhancedStreamPipeline with the DependencyChainAnalyzer
 * to enable streaming analysis of dependencies during resource extraction.
 */

import { Readable, Transform } from 'stream';
import { Logger } from '../../../utils/logging/logger.js';
import { EnhancedStreamPipeline, EnhancedStreamPipelineOptions } from './compatibility/enhancedStreamPipelineCompat.js';
import { DependencyChainAnalyzer } from '../semantic/dependencyChainAnalyzer.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { 
    DependencyResourceKey, 
    DependencyInfo, 
    MetadataInfo, 
    DependencyEventData, 
    DependencyAnalysisOptions 
} from '../../../types/resource/dependency.js';
import { DatabaseService } from '../../databaseService.js';
import { injectable } from '../../di/decorators.js';

/**
 * Enhanced dependency pipeline options
 */
export interface EnhancedDependencyPipelineOptions extends EnhancedStreamPipelineOptions {
  /**
   * Maximum dependency chain depth to analyze
   */
  maxDependencyDepth?: number;

  /**
   * Whether to include gameplay analysis
   */
  includeGameplayAnalysis?: boolean;

  /**
   * Whether to include visualization metadata
   */
  includeVisualizationMetadata?: boolean;

  /**
   * Whether to analyze cross-package dependencies
   */
  analyzeCrossPackageDependencies?: boolean;

  /**
   * Package ID for context
   */
  packageId?: number;
}

/**
 * Enhanced Dependency Pipeline Adapter
 * 
 * This adapter connects the EnhancedStreamPipeline with the DependencyChainAnalyzer
 * to enable streaming analysis of dependencies during resource extraction.
 */
@injectable()
export class EnhancedDependencyPipelineAdapter {
  private logger: Logger;
  private streamPipeline: EnhancedStreamPipeline;
  private dependencyAnalyzer: DependencyChainAnalyzer;
  private databaseService: DatabaseService;
  private processingResourceIds: Set<number> = new Set();

  /**
   * Create a new enhanced dependency pipeline adapter
   * @param databaseService Database service
   * @param dependencyAnalyzer Dependency analyzer (optional, will create if not provided)
   * @param streamPipeline Stream pipeline (optional, will create if not provided)
   * @param logger Logger instance
   */
  constructor(
    databaseService: DatabaseService,
    dependencyAnalyzer?: DependencyChainAnalyzer,
    streamPipeline?: EnhancedStreamPipeline,
    logger?: Logger
  ) {
    this.logger = logger || new Logger('EnhancedDependencyPipelineAdapter');
    this.databaseService = databaseService;
    this.dependencyAnalyzer = dependencyAnalyzer || new DependencyChainAnalyzer(databaseService, this.logger);
    this.streamPipeline = streamPipeline || new EnhancedStreamPipeline();

    this.logger.debug('Created enhanced dependency pipeline adapter');
  }

  /**
   * Initialize the adapter
   */
  public async initialize(): Promise<void> {
    await this.dependencyAnalyzer.initialize();
  }

  /**
   * Create a dependency-aware pipeline for a resource
   * @param resourceId Resource ID
   * @param resourceType Resource type
   * @param source Source stream
   * @param options Pipeline options
   * @returns Enhanced pipeline with dependency analysis
   */
  public async createPipeline(
    resourceId: number,
    resourceType: number,
    source: Readable,
    options: EnhancedDependencyPipelineOptions = {}
  ): Promise<Readable> {
    // Set default options
    const pipelineOptions: EnhancedDependencyPipelineOptions = {
      maxDependencyDepth: 3,
      includeGameplayAnalysis: true,
      includeVisualizationMetadata: true,
      analyzeCrossPackageDependencies: false,
      ...options
    };

    // Check if already processing this resource
    if (this.processingResourceIds.has(resourceId)) {
      this.logger.warn(`Already processing resource ${resourceId}, skipping dependency analysis`);
      return source;
    }

    // Mark as processing
    this.processingResourceIds.add(resourceId);

    try {
      // Create the base pipeline
      const pipeline = await this.streamPipeline.createPipeline(resourceType, source, pipelineOptions);

      // Create the dependency transformer
      const dependencyTransformer = this.createDependencyTransformer(resourceId, resourceType, pipelineOptions);

      // Connect pipeline to dependency transformer
      const enhancedPipeline = pipeline.pipe(dependencyTransformer);

      // Schedule dependency analysis after pipeline completion
      enhancedPipeline.on('end', async () => {
        try {
          // Remove from processing set
          this.processingResourceIds.delete(resourceId);

          // Perform async dependency analysis
          this.performAsyncDependencyAnalysis(resourceId, pipelineOptions);
        } catch (error) {
          this.logger.error(`Error in dependency analysis for resource ${resourceId}:`, error);
        }
      });

      // Handle errors
      enhancedPipeline.on('error', (error) => {
        this.logger.error(`Error in dependency pipeline for resource ${resourceId}:`, error);
        this.processingResourceIds.delete(resourceId);
      });

      return enhancedPipeline;
    } catch (error) {
      this.logger.error(`Failed to create dependency pipeline for resource ${resourceId}:`, error);
      this.processingResourceIds.delete(resourceId);
      throw error;
    }
  }

  /**
   * Create a transform that monitors for dependencies
   * @param resourceId Resource ID
   * @param resourceType Resource type
   * @param options Pipeline options
   * @returns Dependency transformer
   */
  private createDependencyTransformer(
    resourceId: number,
    resourceType: number,
    options: EnhancedDependencyPipelineOptions
  ): Transform {
    // Get resource info for easier reference in transformer
    const sourceResourcePromise = this.databaseService.resources.getResourceById(resourceId);

    // Create transform to monitor for dependencies
    const transformer = new Transform({
      objectMode: true,
      transform: async (chunk, encoding, callback) => {
        try {
          // First, pass through the chunk unchanged
          callback(null, chunk);

          // Check if chunk contains dependency information
          if (chunk && typeof chunk === 'object') {
            // Check for TGI references in the chunk
            const references = this.extractReferences(chunk);

            if (references && references.length > 0) {
              try {
                // Get source resource
                const sourceResource = await sourceResourcePromise;

                if (!sourceResource) {
                  return; // Skip if source resource not found
                }

                // Create source key
                const sourceKey: ResourceKey = {
                  type: resourceType,
                  group: sourceResource.group,
                  instance: sourceResource.instance
                };

                // Process each reference
                for (const ref of references) {
                  // Create dependency record
                  const dependencyInfo: DependencyInfo = {
                    resourceId,
                    targetType: ref.type,
                    targetGroup: ref.group,
                    targetInstance: ref.instance,
                    referenceType: ref.referenceType || 'reference',
                    context: ref.context || 'pipeline_extraction',
                    confidence: ref.confidence || 80,
                    metadata: ref.metadata,
                    timestamp: Date.now()
                  };

                  await this.databaseService.dependencies.saveDependency(dependencyInfo);

                  // Emit dependency event
                  this.streamPipeline.emit('dependency', {
                    resourceId,
                    sourceKey,
                    targetKey: ref,
                    dependencyType: ref.referenceType || 'reference',
                    confidence: ref.confidence || 80,
                    metadata: ref.metadata
                  } as DependencyEventData);
                }
              } catch (error) {
                this.logger.error(`Error processing references for resource ${resourceId}:`, error);
              }
            }
          }
        } catch (error) {
          // Log but don't fail the pipeline
          this.logger.error(`Error in dependency transformer for resource ${resourceId}:`, error);
          callback(null, chunk); // Still pass through the chunk
        }
      }
    });

    // Add default methods required by IStreamTransformer interface
    (transformer as any).getName = () => 'DependencyTransformer';
    (transformer as any).getStats = () => ({
      transformer: 'DependencyTransformer',
      resourceId,
      resourceType
    });

    return transformer;
  }

  /**
   * Extract references from a chunk
   * @param chunk Chunk to extract references from
   * @returns Array of dependency resource keys
   */
  private extractReferences(chunk: any): DependencyResourceKey[] {
    const references: DependencyResourceKey[] = [];

    // Look for explicit references array
    if (Array.isArray(chunk.references)) {
      return chunk.references.filter((ref: any) => 
        ref && typeof ref === 'object' && 
        ref.type !== undefined && 
        ref.instance !== undefined
      );
    }

    // Check for simdata references
    if (chunk.schema && chunk.instance) {
      // SimData references are often in instance.references
      if (Array.isArray(chunk.instance.references)) {
        chunk.instance.references.forEach((ref: any) => {
          if (ref && typeof ref === 'object' && ref.TGI) {
            references.push({
              type: ref.TGI.t || 0,
              group: ref.TGI.g || 0n,
              instance: ref.TGI.i || 0n,
              referenceType: 'simdata_reference',
              confidence: 90
            });
          }
        });
      }
    }

    // Check for model references
    if (chunk.model_data) {
      // Check for embedded references
      if (Array.isArray(chunk.model_data.references)) {
        chunk.model_data.references.forEach((ref: any) => {
          if (ref && typeof ref === 'object') {
            references.push({
              type: ref.type || 0,
              group: BigInt(ref.group || 0),
              instance: BigInt(ref.instance || 0),
              referenceType: 'model_reference',
              confidence: 85
            });
          }
        });
      }
    }

    // Check for animation references
    if (chunk.animation_data) {
      // Check for embedded references
      if (Array.isArray(chunk.animation_data.references)) {
        chunk.animation_data.references.forEach((ref: any) => {
          if (ref && typeof ref === 'object') {
            references.push({
              type: ref.type || 0,
              group: BigInt(ref.group || 0),
              instance: BigInt(ref.instance || 0),
              referenceType: 'animation_reference',
              confidence: 85
            });
          }
        });
      }
    }

    return references;
  }

  /**
   * Perform asynchronous dependency analysis after pipeline completes
   * @param resourceId Resource ID to analyze
   * @param options Pipeline options
   */
  private async performAsyncDependencyAnalysis(
    resourceId: number,
    options: EnhancedDependencyPipelineOptions
  ): Promise<void> {
    try {
      // Skip if resource is being reprocessed
      if (this.processingResourceIds.has(resourceId)) {
        return;
      }

      // Analyze dependency chain
      const dependencyChain = await this.dependencyAnalyzer.analyzeDependencyChain(
        resourceId,
        options.maxDependencyDepth,
        'forward'
      );

      // Check if we should analyze cross-package dependencies
      if (options.analyzeCrossPackageDependencies && options.packageId) {
        // Get resource
        const resource = await this.databaseService.resources.getResourceById(resourceId);
        if (!resource) return;

        // If resource is from a different package than the context package
        if (resource.packageId !== options.packageId) {
          // Analyze dependencies between these packages
          await this.dependencyAnalyzer.analyzePackageDependencies([resource.packageId, options.packageId]);
        }
      }

      // Save dependency analysis results to database
      await this.saveDependencyAnalysisResults(resourceId, dependencyChain);

      this.logger.debug(`Completed dependency analysis for resource ${resourceId}`);
    } catch (error) {
      this.logger.error(`Error in async dependency analysis for resource ${resourceId}:`, error);
    }
  }

  /**
   * Save dependency analysis results to database
   * @param resourceId Resource ID
   * @param dependencyChain Dependency chain
   */
  private async saveDependencyAnalysisResults(
    resourceId: number,
    dependencyChain: any
  ): Promise<void> {
    try {
      // Save basic metadata about the dependency chain
      const metadataInfo: MetadataInfo = {
        resourceId,
        key: 'dependencyChainSummary',
        value: JSON.stringify({
          totalNodes: dependencyChain.totalNodes,
          maxDepth: dependencyChain.maxDepth,
          impactScore: dependencyChain.impactScore,
          timestamp: Date.now()
        }),
        metadataType: 'json',
        timestamp: Date.now()
      };

      await this.databaseService.metadata.saveMetadata(metadataInfo);

      // Save impacted gameplay systems
      if (dependencyChain.impactedGameplaySystems && 
          dependencyChain.impactedGameplaySystems.length > 0) {
        const gameplayMetadata: MetadataInfo = {
          resourceId,
          key: 'impactedGameplaySystems',
          value: JSON.stringify(dependencyChain.impactedGameplaySystems),
          metadataType: 'json',
          timestamp: Date.now()
        };

        await this.databaseService.metadata.saveMetadata(gameplayMetadata);
      }

      // Save visualization data
      if (dependencyChain.visualizationMetadata) {
        // Export for visualization
        const visualizationData = this.dependencyAnalyzer.exportForVisualization(dependencyChain);

        const visualizationMetadata: MetadataInfo = {
          resourceId,
          key: 'dependencyVisualization',
          value: JSON.stringify(visualizationData),
          metadataType: 'json',
          timestamp: Date.now()
        };

        await this.databaseService.metadata.saveMetadata(visualizationMetadata);
      }
    } catch (error) {
      this.logger.error(`Error saving dependency analysis results for resource ${resourceId}:`, error);
    }
  }

  /**
   * Dispose of resources
   */
  public async dispose(): Promise<void> {
    try {
      await this.streamPipeline.destroy();
      await this.dependencyAnalyzer.dispose();
      this.processingResourceIds.clear();
      this.logger.info('Enhanced Dependency Pipeline Adapter disposed successfully');
    } catch (error) {
      this.logger.error('Error disposing Enhanced Dependency Pipeline Adapter:', error);
      throw error;
    }
  }
}