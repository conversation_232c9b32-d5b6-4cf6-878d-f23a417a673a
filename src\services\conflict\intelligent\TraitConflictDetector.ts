/**
 * Trait Conflict Detector - Phase 2: Intelligent Conflict Detection
 * 
 * This detector implements the game's trait conflict logic, using patterns
 * discovered from analyzing the official Sims 4 trait system.
 * 
 * Based on the game's conflicting_traits system and trait interaction logic.
 */

import { OfficialResourceType } from '../../../types/resource/OfficialResourceTypes.js';
import { ConflictSeverity, GameplayImpact, ConflictAnalysisResult } from './GameplayImpactAnalyzer.js';

/**
 * Trait Conflict Types based on game logic
 */
export enum TraitConflictType {
  CONFLICTING_TRAITS = 'CONFLICTING_TRAITS',     // Traits that explicitly conflict
  PERSONALITY_CLASH = 'PERSONALITY_CLASH',       // Opposing personality traits
  BEHAVIOR_OVERRIDE = 'BEHAVIOR_OVERRIDE',       // Traits that override behaviors
  MOOD_CONFLICT = 'MOOD_CONFLICT',               // Traits affecting same moods
  SKILL_CONFLICT = 'SKILL_CONFLICT',             // Traits affecting same skills
  INTERACTION_CONFLICT = 'INTERACTION_CONFLICT'  // Traits modifying same interactions
}

/**
 * Trait Metadata Interface
 */
export interface TraitMetadata {
  traitId: string;
  traitName?: string;
  conflictingTraits?: string[];
  personalityType?: string;
  moodEffects?: string[];
  skillEffects?: string[];
  interactionModifications?: string[];
  isBaseTrait?: boolean;
  isRewardTrait?: boolean;
  isLifestyleTrait?: boolean;
}

/**
 * Trait Conflict Context
 */
export interface TraitConflictContext {
  primaryTrait: TraitMetadata;
  conflictingTraits: TraitMetadata[];
  packageNames: string[];
  resourceIds: string[];
}

/**
 * Trait Conflict Detector
 * 
 * Implements intelligent trait conflict detection using the game's
 * trait system logic and conflict patterns.
 */
export class TraitConflictDetector {
  
  // Known conflicting trait pairs from game analysis
  private readonly KNOWN_CONFLICTING_TRAITS = new Map<string, string[]>([
    // Personality conflicts
    ['trait_Mean', ['trait_Good', 'trait_Cheerful']],
    ['trait_Evil', ['trait_Good', 'trait_Cheerful']],
    ['trait_Hates_Children', ['trait_Family_Oriented', 'trait_Loves_Children']],
    ['trait_Neat', ['trait_Slob']],
    ['trait_Active', ['trait_Lazy']],
    ['trait_Outgoing', ['trait_Loner']],
    ['trait_Ambitious', ['trait_Lazy']],
    
    // Lifestyle conflicts
    ['trait_Vegetarian', ['trait_Loves_Meat']],
    ['trait_Lactose_Intolerant', ['trait_Loves_Dairy']],
    ['trait_Cat_Lover', ['trait_Dog_Lover']], // Not always conflicting but can be
    
    // Career conflicts
    ['trait_Hates_Art', ['trait_Creative', 'trait_Art_Lover']],
    ['trait_Technophobe', ['trait_Geek', 'trait_Computer_Whiz']],
    
    // Supernatural conflicts (from packs)
    ['trait_Vampire', ['trait_Human_Only']],
    ['trait_Spellcaster', ['trait_Magic_Resistant']],
    ['trait_Werewolf', ['trait_Vampire']] // Cross-supernatural conflicts
  ]);
  
  // Personality categories that typically conflict
  private readonly PERSONALITY_CATEGORIES = new Map<string, string[]>([
    ['social', ['trait_Outgoing', 'trait_Loner']],
    ['cleanliness', ['trait_Neat', 'trait_Slob']],
    ['activity', ['trait_Active', 'trait_Lazy']],
    ['morality', ['trait_Good', 'trait_Mean', 'trait_Evil']],
    ['family', ['trait_Family_Oriented', 'trait_Hates_Children']],
    ['creativity', ['trait_Creative', 'trait_Hates_Art']],
    ['technology', ['trait_Geek', 'trait_Technophobe']]
  ]);
  
  /**
   * Detect trait conflicts using game logic
   */
  public detectTraitConflicts(context: TraitConflictContext): ConflictAnalysisResult {
    const conflictType = this.identifyConflictType(context);
    const severity = this.calculateConflictSeverity(conflictType, context);
    const impact = this.determineGameplayImpact(conflictType, severity);
    
    const description = this.generateConflictDescription(conflictType, context);
    const recommendation = this.generateRecommendation(conflictType, severity, context);
    const affectedSystems = this.identifyAffectedSystems(conflictType);
    
    const isRealConflict = severity !== ConflictSeverity.HARMLESS;
    const confidence = this.calculateConfidence(conflictType, context);
    
    return {
      severity,
      impact,
      description,
      recommendation,
      affectedSystems,
      isRealConflict,
      confidence
    };
  }
  
  /**
   * Identify the type of trait conflict
   */
  private identifyConflictType(context: TraitConflictContext): TraitConflictType {
    const primaryTrait = context.primaryTrait;
    const conflictingTraits = context.conflictingTraits;
    
    // Check for explicit conflicting traits
    if (this.hasExplicitConflicts(primaryTrait, conflictingTraits)) {
      return TraitConflictType.CONFLICTING_TRAITS;
    }
    
    // Check for personality clashes
    if (this.hasPersonalityClash(primaryTrait, conflictingTraits)) {
      return TraitConflictType.PERSONALITY_CLASH;
    }
    
    // Check for mood conflicts
    if (this.hasMoodConflicts(primaryTrait, conflictingTraits)) {
      return TraitConflictType.MOOD_CONFLICT;
    }
    
    // Check for skill conflicts
    if (this.hasSkillConflicts(primaryTrait, conflictingTraits)) {
      return TraitConflictType.SKILL_CONFLICT;
    }
    
    // Check for interaction conflicts
    if (this.hasInteractionConflicts(primaryTrait, conflictingTraits)) {
      return TraitConflictType.INTERACTION_CONFLICT;
    }
    
    // Default to behavior override if multiple mods modify same trait
    return TraitConflictType.BEHAVIOR_OVERRIDE;
  }
  
  /**
   * Check for explicit trait conflicts from game data
   */
  private hasExplicitConflicts(primaryTrait: TraitMetadata, conflictingTraits: TraitMetadata[]): boolean {
    const knownConflicts = this.KNOWN_CONFLICTING_TRAITS.get(primaryTrait.traitId) || [];
    
    return conflictingTraits.some(trait => 
      knownConflicts.includes(trait.traitId) ||
      (trait.conflictingTraits && trait.conflictingTraits.includes(primaryTrait.traitId))
    );
  }
  
  /**
   * Check for personality category conflicts
   */
  private hasPersonalityClash(primaryTrait: TraitMetadata, conflictingTraits: TraitMetadata[]): boolean {
    for (const [category, traits] of this.PERSONALITY_CATEGORIES) {
      if (traits.includes(primaryTrait.traitId)) {
        // Check if any conflicting trait is in the same personality category
        const hasConflictInCategory = conflictingTraits.some(trait => 
          traits.includes(trait.traitId) && trait.traitId !== primaryTrait.traitId
        );
        if (hasConflictInCategory) {
          return true;
        }
      }
    }
    return false;
  }
  
  /**
   * Check for mood-related conflicts
   */
  private hasMoodConflicts(primaryTrait: TraitMetadata, conflictingTraits: TraitMetadata[]): boolean {
    if (!primaryTrait.moodEffects || primaryTrait.moodEffects.length === 0) {
      return false;
    }
    
    return conflictingTraits.some(trait => {
      if (!trait.moodEffects) return false;
      
      // Check for opposing mood effects
      return primaryTrait.moodEffects!.some(mood => {
        const oppositeMood = this.getOppositeMood(mood);
        return trait.moodEffects!.includes(oppositeMood);
      });
    });
  }
  
  /**
   * Check for skill-related conflicts
   */
  private hasSkillConflicts(primaryTrait: TraitMetadata, conflictingTraits: TraitMetadata[]): boolean {
    if (!primaryTrait.skillEffects || primaryTrait.skillEffects.length === 0) {
      return false;
    }
    
    return conflictingTraits.some(trait => {
      if (!trait.skillEffects) return false;
      
      // Check for conflicting skill modifications
      return primaryTrait.skillEffects!.some(skill => 
        trait.skillEffects!.includes(skill)
      );
    });
  }
  
  /**
   * Check for interaction modification conflicts
   */
  private hasInteractionConflicts(primaryTrait: TraitMetadata, conflictingTraits: TraitMetadata[]): boolean {
    if (!primaryTrait.interactionModifications || primaryTrait.interactionModifications.length === 0) {
      return false;
    }
    
    return conflictingTraits.some(trait => {
      if (!trait.interactionModifications) return false;
      
      // Check for same interaction modifications
      return primaryTrait.interactionModifications!.some(interaction => 
        trait.interactionModifications!.includes(interaction)
      );
    });
  }
  
  /**
   * Calculate conflict severity based on type and context
   */
  private calculateConflictSeverity(conflictType: TraitConflictType, context: TraitConflictContext): ConflictSeverity {
    const conflictCount = context.conflictingTraits.length;
    
    switch (conflictType) {
      case TraitConflictType.CONFLICTING_TRAITS:
        return ConflictSeverity.CRITICAL; // Game explicitly marks these as conflicting
        
      case TraitConflictType.PERSONALITY_CLASH:
        return conflictCount > 2 ? ConflictSeverity.HIGH : ConflictSeverity.MEDIUM;
        
      case TraitConflictType.MOOD_CONFLICT:
        return ConflictSeverity.HIGH; // Mood conflicts can cause significant issues
        
      case TraitConflictType.SKILL_CONFLICT:
      case TraitConflictType.INTERACTION_CONFLICT:
        return ConflictSeverity.MEDIUM;
        
      case TraitConflictType.BEHAVIOR_OVERRIDE:
        return conflictCount > 3 ? ConflictSeverity.HIGH : ConflictSeverity.MEDIUM;
        
      default:
        return ConflictSeverity.MEDIUM;
    }
  }
  
  /**
   * Determine gameplay impact from conflict type
   */
  private determineGameplayImpact(conflictType: TraitConflictType, severity: ConflictSeverity): GameplayImpact {
    switch (conflictType) {
      case TraitConflictType.CONFLICTING_TRAITS:
        return GameplayImpact.FUNCTIONALITY_LOSS; // Traits may not work as expected
        
      case TraitConflictType.PERSONALITY_CLASH:
      case TraitConflictType.MOOD_CONFLICT:
        return GameplayImpact.BEHAVIOR_CHANGE; // Sim behavior becomes unpredictable
        
      case TraitConflictType.SKILL_CONFLICT:
      case TraitConflictType.INTERACTION_CONFLICT:
      case TraitConflictType.BEHAVIOR_OVERRIDE:
        return severity === ConflictSeverity.HIGH 
          ? GameplayImpact.FUNCTIONALITY_LOSS 
          : GameplayImpact.BEHAVIOR_CHANGE;
        
      default:
        return GameplayImpact.BEHAVIOR_CHANGE;
    }
  }
  
  /**
   * Generate human-readable conflict description
   */
  private generateConflictDescription(conflictType: TraitConflictType, context: TraitConflictContext): string {
    const traitName = context.primaryTrait.traitName || context.primaryTrait.traitId;
    const conflictCount = context.conflictingTraits.length;
    
    switch (conflictType) {
      case TraitConflictType.CONFLICTING_TRAITS:
        return `The trait "${traitName}" has explicit conflicts with ${conflictCount} other trait(s). The game is designed to prevent these traits from coexisting.`;
        
      case TraitConflictType.PERSONALITY_CLASH:
        return `The trait "${traitName}" represents a personality type that conflicts with ${conflictCount} other personality trait(s). This may cause inconsistent Sim behavior.`;
        
      case TraitConflictType.MOOD_CONFLICT:
        return `The trait "${traitName}" affects moods in ways that conflict with ${conflictCount} other trait(s). This may cause mood system instability.`;
        
      case TraitConflictType.SKILL_CONFLICT:
        return `The trait "${traitName}" modifies skills that are also affected by ${conflictCount} other trait(s). This may cause unexpected skill progression.`;
        
      case TraitConflictType.INTERACTION_CONFLICT:
        return `The trait "${traitName}" modifies interactions that are also changed by ${conflictCount} other trait(s). This may cause interaction system issues.`;
        
      case TraitConflictType.BEHAVIOR_OVERRIDE:
        return `Multiple mods (${conflictCount + 1}) are modifying the trait "${traitName}". Only one modification will take effect, potentially breaking the others.`;
        
      default:
        return `The trait "${traitName}" has conflicts with ${conflictCount} other trait modification(s).`;
    }
  }
  
  /**
   * Generate recommendation for resolving conflict
   */
  private generateRecommendation(
    conflictType: TraitConflictType, 
    severity: ConflictSeverity, 
    context: TraitConflictContext
  ): string {
    switch (severity) {
      case ConflictSeverity.CRITICAL:
        return 'CRITICAL: Remove conflicting trait mods immediately. The game is designed to prevent these traits from coexisting.';
        
      case ConflictSeverity.HIGH:
        return 'RECOMMENDED: Choose one trait mod to keep and remove the others. Look for compatibility patches if available.';
        
      case ConflictSeverity.MEDIUM:
        return 'CONSIDER: Test gameplay carefully. Remove mods if Sim behavior becomes inconsistent or unpredictable.';
        
      default:
        return 'MONITOR: Watch for unusual Sim behavior and remove mods if issues occur.';
    }
  }
  
  /**
   * Identify affected game systems
   */
  private identifyAffectedSystems(conflictType: TraitConflictType): string[] {
    const baseSystems = ['Trait System', 'Personality System'];
    
    switch (conflictType) {
      case TraitConflictType.CONFLICTING_TRAITS:
      case TraitConflictType.PERSONALITY_CLASH:
        return [...baseSystems, 'Behavior System', 'Autonomy System'];
        
      case TraitConflictType.MOOD_CONFLICT:
        return [...baseSystems, 'Mood System', 'Emotion System'];
        
      case TraitConflictType.SKILL_CONFLICT:
        return [...baseSystems, 'Skill System', 'Progression System'];
        
      case TraitConflictType.INTERACTION_CONFLICT:
        return [...baseSystems, 'Interaction System', 'Social System'];
        
      case TraitConflictType.BEHAVIOR_OVERRIDE:
        return [...baseSystems, 'Behavior System'];
        
      default:
        return baseSystems;
    }
  }
  
  /**
   * Calculate confidence in conflict detection
   */
  private calculateConfidence(conflictType: TraitConflictType, context: TraitConflictContext): number {
    let confidence = 0.8; // Base confidence for trait conflicts
    
    switch (conflictType) {
      case TraitConflictType.CONFLICTING_TRAITS:
        confidence = 0.95; // Very high confidence for explicit conflicts
        break;
        
      case TraitConflictType.PERSONALITY_CLASH:
        confidence = 0.85; // High confidence for personality conflicts
        break;
        
      case TraitConflictType.MOOD_CONFLICT:
      case TraitConflictType.SKILL_CONFLICT:
        confidence = 0.75; // Good confidence for system conflicts
        break;
        
      case TraitConflictType.INTERACTION_CONFLICT:
      case TraitConflictType.BEHAVIOR_OVERRIDE:
        confidence = 0.7; // Moderate confidence
        break;
    }
    
    // Adjust confidence based on available metadata
    if (!context.primaryTrait.traitName) {
      confidence -= 0.1; // Lower confidence without trait name
    }
    
    if (context.conflictingTraits.some(trait => !trait.traitName)) {
      confidence -= 0.05; // Slightly lower confidence for incomplete data
    }
    
    return Math.min(1.0, Math.max(0.1, confidence));
  }
  
  /**
   * Get opposite mood for conflict detection
   */
  private getOppositeMood(mood: string): string {
    const moodOpposites: Record<string, string> = {
      'Happy': 'Sad',
      'Sad': 'Happy',
      'Angry': 'Calm',
      'Calm': 'Angry',
      'Confident': 'Embarrassed',
      'Embarrassed': 'Confident',
      'Energized': 'Tired',
      'Tired': 'Energized',
      'Focused': 'Distracted',
      'Distracted': 'Focused',
      'Inspired': 'Uninspired',
      'Uninspired': 'Inspired',
      'Playful': 'Serious',
      'Serious': 'Playful'
    };
    
    return moodOpposites[mood] || '';
  }
}
