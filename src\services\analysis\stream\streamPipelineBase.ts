/**
 * Stream Pipeline Base
 * 
 * This module provides a base class for all stream pipelines.
 * It implements the common functionality for managing the flow of data,
 * handling errors, and reporting statistics.
 */

import { Readable, Transform, pipeline } from 'stream';
import { promisify } from 'util';
import { EventEmitter } from 'events';
import { Logger } from '../../../utils/logging/logger.js';
import { IStreamTransformer, StreamTransformerStats } from './baseStreamTransformer.js';
import EnhancedMemoryManager from '../../../utils/memory/enhancedMemoryManager.js';

// Promisify pipeline for async/await usage
const pipelineAsync = promisify(pipeline);

// Create a logger for this module
const logger = new Logger('StreamPipelineBase');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

/**
 * Pipeline statistics
 */
export interface PipelineStats {
    resourceType: number;
    startTime: number;
    endTime: number;
    duration?: number;
    bytesProcessed: number;
    transformerStats: Record<string, StreamTransformerStats>;
    memoryUsage?: {
        heapUsed: number;
        heapTotal: number;
        external: number;
        arrayBuffers: number;
    };
    error?: string;
}

/**
 * Stream pipeline options
 */
export interface StreamPipelineOptions {
    // Memory management options
    maxMemoryUsage?: number;
    
    // Error handling options
    errorHandling?: 'strict' | 'lenient' | 'skip';
    
    // Monitoring options
    emitProgress?: boolean;
    emitStats?: boolean;
    
    // Transformer options
    transformerOptions?: Record<string, any>;
}

/**
 * Stream pipeline interface
 */
export interface IStreamPipeline extends EventEmitter {
    /**
     * Create a pipeline for a specific resource type
     * @param resourceType Resource type
     * @param source Source stream
     * @param options Pipeline options
     */
    createPipeline(
        resourceType: number, 
        source: Readable, 
        options?: StreamPipelineOptions
    ): Promise<Readable>;
    
    /**
     * Add a custom transformer to the pipeline
     * @param transformer Transformer to add
     * @param position Position to add the transformer at
     */
    addTransformer(
        transformer: IStreamTransformer, 
        position?: number
    ): void;
    
    /**
     * Get pipeline statistics
     */
    getStats(): PipelineStats;
    
    /**
     * Destroy the pipeline and clean up resources
     */
    destroy(): Promise<void>;
}

/**
 * Stream pipeline base implementation
 */
export abstract class StreamPipelineBase extends EventEmitter implements IStreamPipeline {
    protected transformers: IStreamTransformer[] = [];
    protected stats: PipelineStats = {
        resourceType: 0,
        startTime: 0,
        endTime: 0,
        bytesProcessed: 0,
        transformerStats: {}
    };
    protected source?: Readable;
    protected destination?: Readable;
    protected pipelinePromise?: Promise<void>;
    protected options: StreamPipelineOptions = {
        errorHandling: 'strict',
        emitProgress: true,
        emitStats: true
    };
    protected destroyed = false;
    
    /**
     * Create a new stream pipeline
     * @param options Pipeline options
     */
    constructor(options: StreamPipelineOptions = {}) {
        super();
        this.options = { ...this.options, ...options };
        
        // Set max listeners to avoid memory leak warnings
        this.setMaxListeners(50);
        
        logger.debug('Created stream pipeline base');
    }
    
    /**
     * Create a pipeline for a specific resource type
     * @param resourceType Resource type
     * @param source Source stream
     * @param options Pipeline options
     */
    public abstract createPipeline(
        resourceType: number, 
        source: Readable, 
        options?: StreamPipelineOptions
    ): Promise<Readable>;
    
    /**
     * Add a custom transformer to the pipeline
     * @param transformer Transformer to add
     * @param position Position to add the transformer at
     */
    public addTransformer(
        transformer: IStreamTransformer, 
        position?: number
    ): void {
        if (position !== undefined && position >= 0 && position <= this.transformers.length) {
            this.transformers.splice(position, 0, transformer);
        } else {
            this.transformers.push(transformer);
        }
        
        logger.debug(`Added transformer ${transformer.getName()} to pipeline`);
    }
    
    /**
     * Get pipeline statistics
     */
    public getStats(): PipelineStats {
        // Update transformer stats
        for (const transformer of this.transformers) {
            this.stats.transformerStats[transformer.getName()] = transformer.getStats();
        }
        
        // Update memory usage
        this.stats.memoryUsage = {
            heapUsed: process.memoryUsage().heapUsed,
            heapTotal: process.memoryUsage().heapTotal,
            external: process.memoryUsage().external,
            arrayBuffers: process.memoryUsage().arrayBuffers || 0
        };
        
        return this.stats;
    }
    
    /**
     * Destroy the pipeline and clean up resources
     */
    public async destroy(): Promise<void> {
        if (this.destroyed) {
            return;
        }
        
        this.destroyed = true;
        
        // Wait for pipeline to complete if it's running
        if (this.pipelinePromise) {
            try {
                await this.pipelinePromise;
            } catch (error: any) {
                logger.error(`Error waiting for pipeline to complete: ${error.message}`);
            }
        }
        
        // Destroy all transformers
        for (const transformer of this.transformers) {
            try {
                await transformer.cleanup();
                transformer.destroy();
            } catch (error: any) {
                logger.error(`Error destroying transformer ${transformer.getName()}: ${error.message}`);
            }
        }
        
        // Destroy source and destination if they exist
        if (this.source && !this.source.destroyed) {
            this.source.destroy();
        }
        
        if (this.destination && !this.destination.destroyed) {
            this.destination.destroy();
        }
        
        // Clear transformers
        this.transformers = [];
        
        // Remove all listeners
        this.removeAllListeners();
        
        logger.debug('Destroyed stream pipeline');
    }
    
    /**
     * Set up event listeners for transformers
     */
    protected setupTransformerEvents(): void {
        for (const transformer of this.transformers) {
            // Listen for progress events
            if (this.options.emitProgress) {
                transformer.on('progress', (progress) => {
                    this.emit('progress', {
                        transformer: transformer.getName(),
                        progress
                    });
                });
            }
            
            // Listen for stats events
            if (this.options.emitStats) {
                transformer.on('stats', (stats) => {
                    this.stats.transformerStats[transformer.getName()] = stats;
                    this.emit('stats', this.getStats());
                });
            }
            
            // Listen for error events
            transformer.on('error', (error) => {
                logger.error(`Error in transformer ${transformer.getName()}: ${error.message}`);
                this.emit('error', error);
            });
        }
    }
}
