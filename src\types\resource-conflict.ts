﻿// Corrected import
import { ResourceKey } from './resource/interfaces.js';

export type ResourceConflictType = 'DUPLICATE' | 'VERSION_MISMATCH' | 'DEPENDENCY' | 'COMPATIBILITY';

export type ResourceConflictSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export interface ResourceConflict {
  type: ResourceConflictType;
  severity: ResourceConflictSeverity;
  description: string;
  affectedResources: ResourceKey[];
  resolution?: {
    action: string;
    description: string;
  };
}

export interface ResourceConflictAnalysis {
  conflicts: ResourceConflict[];
  metrics: {
    totalConflicts: number;
    severityDistribution: Record<ResourceConflictSeverity, number>;
    affectedResourceCount: number;
  };
  recommendations: string[];
}

export interface ConflictResolutionStrategy {
  type: ResourceConflictType;
  resolve: (conflict: ResourceConflict) => Promise<boolean>;
  validate: (conflict: ResourceConflict) => boolean;
}

export interface ConflictResult {
  hasConflict: boolean;
  conflicts: ResourceConflict[];
  metadata: {
    duration: number;
    resourceCount: number;
    metrics: {
      totalConflicts: number;
      agentCount: number;
      averageConfidence: number;
    };
  };
}
