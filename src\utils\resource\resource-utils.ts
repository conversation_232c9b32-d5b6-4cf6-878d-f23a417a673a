﻿import { BinaryResourceType, BinaryResourceTypeValue } from "../types/resource/core.js"; // Import both type and value

/**
 * Gets the distribution of resource types in a package
 */
export function getResourceTypeDistribution(resources: any[]): Record<string, number> {
  const distribution: { [key: string]: number } = {};
  resources.forEach(resource => {
    const type = resource.type;
    if (type in BinaryResourceTypeValue) { // Use value for 'in' check
      distribution[type] = (distribution[type] || 0) + 1;
    }
  });
  return distribution;
} 
