# AGENT.md - Guidelines for Archistack Repo

## Build & Test Commands
- Build: `npm run build` (server and client) or `npm run build:server`
- Run tests: `npm test` or `npx tsx --no-warnings src/tools/test-real-world-refactored.ts`
- Lint: `npm run lint`
- Format: `npm run format`

## Code Style Guidelines
- Use TypeScript with explicit return types
- Follow ESLint config and Prettier rules (100 char line length, 2 space indent, single quotes)
- Use comprehensive error handling with typed catch blocks (error: any)
- Imports should be organized by scope (internal before external)
- Prefer memory-efficient solutions with streaming approaches
- Use comprehensive documentation with JSDoc comments
- Follow existing patterns for dependency injection and error handling
- Proactively manage memory with EnhancedMemoryManager
- Implement proper resource cleanup with dispose methods
- Prefer fixing original files rather than creating new ones
- Prefer integration tests with real-world scenarios