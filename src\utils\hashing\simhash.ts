/**
 * SimHash implementation for content fingerprinting
 * 
 * This module provides functions for generating and comparing SimHash fingerprints,
 * which are useful for identifying similar content.
 */

import { createHash } from 'crypto';

/**
 * Generate a SimHash fingerprint for a string
 * @param content Content to fingerprint
 * @param numFeatures Number of features to extract (default: 64)
 * @returns SimHash fingerprint as a BigInt
 */
export function simhash(content: string | Buffer, numFeatures: number = 64): bigint {
    // Convert Buffer to string if needed
    const contentStr = Buffer.isBuffer(content) ? content.toString('utf8') : content;
    
    // Extract features (shingles) from the content
    const shingles = extractShingles(contentStr);
    
    // Initialize feature vector
    const featureVector = new Array(numFeatures).fill(0);
    
    // Process each shingle
    for (const shingle of shingles) {
        // Hash the shingle
        const hash = hashShingle(shingle);
        
        // Update feature vector
        for (let i = 0; i < numFeatures; i++) {
            // Check if bit i is set in the hash
            if ((hash >> BigInt(i)) & BigInt(1)) {
                featureVector[i]++;
            } else {
                featureVector[i]--;
            }
        }
    }
    
    // Generate fingerprint from feature vector
    let fingerprint = BigInt(0);
    for (let i = 0; i < numFeatures; i++) {
        // Set bit i if the feature is positive
        if (featureVector[i] > 0) {
            fingerprint |= (BigInt(1) << BigInt(i));
        }
    }
    
    return fingerprint;
}

/**
 * Calculate the Hamming distance between two SimHash fingerprints
 * @param hash1 First SimHash fingerprint
 * @param hash2 Second SimHash fingerprint
 * @returns Hamming distance (number of differing bits)
 */
export function hammingDistance(hash1: bigint, hash2: bigint): number {
    // XOR the hashes to get bits that differ
    const xor = hash1 ^ hash2;
    
    // Count the number of set bits in the XOR result
    let distance = 0;
    let value = xor;
    
    while (value > 0) {
        // Increment count if the least significant bit is set
        if (value & BigInt(1)) {
            distance++;
        }
        
        // Shift right to check the next bit
        value = value >> BigInt(1);
    }
    
    return distance;
}

/**
 * Extract shingles (n-grams) from a string
 * @param content Content to extract shingles from
 * @param shingleSize Size of each shingle (default: 3)
 * @returns Array of shingles
 */
function extractShingles(content: string, shingleSize: number = 3): string[] {
    const shingles: string[] = [];
    
    // Normalize content
    const normalizedContent = content
        .toLowerCase()
        .replace(/\s+/g, ' ')
        .trim();
    
    // Extract shingles
    for (let i = 0; i <= normalizedContent.length - shingleSize; i++) {
        shingles.push(normalizedContent.substring(i, i + shingleSize));
    }
    
    return shingles;
}

/**
 * Hash a shingle to a 64-bit value
 * @param shingle Shingle to hash
 * @returns 64-bit hash as a BigInt
 */
function hashShingle(shingle: string): bigint {
    // Use SHA-256 for hashing
    const hash = createHash('sha256').update(shingle).digest();
    
    // Convert first 8 bytes to a BigInt
    return BigInt('0x' + hash.subarray(0, 8).toString('hex'));
}

/**
 * Check if two SimHash fingerprints are similar
 * @param hash1 First SimHash fingerprint
 * @param hash2 Second SimHash fingerprint
 * @param threshold Similarity threshold (default: 10)
 * @returns True if the fingerprints are similar, false otherwise
 */
export function areSimilar(hash1: bigint, hash2: bigint, threshold: number = 10): boolean {
    return hammingDistance(hash1, hash2) <= threshold;
}

/**
 * Calculate similarity percentage between two SimHash fingerprints
 * @param hash1 First SimHash fingerprint
 * @param hash2 Second SimHash fingerprint
 * @param numBits Number of bits in the fingerprints (default: 64)
 * @returns Similarity percentage (0-100)
 */
export function similarityPercentage(hash1: bigint, hash2: bigint, numBits: number = 64): number {
    const distance = hammingDistance(hash1, hash2);
    return 100 * (1 - distance / numBits);
}
