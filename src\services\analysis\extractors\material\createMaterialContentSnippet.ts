/**
 * Creates a content snippet from material header information
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { MaterialHeaderInfo } from './materialTypes.js';
import { MATERIAL_RESOURCE_TYPES } from './materialConstants.js';
import { handleMaterialExtractionError } from './error/index.js';

const logger = new Logger('MaterialContentSnippet');

/**
 * Creates a content snippet from material header information
 * @param header The material header information
 * @param resourceType The resource type
 * @param dependencyCount The number of dependencies
 * @returns A human-readable content snippet
 */
export function createMaterialContentSnippet(
    header: MaterialHeaderInfo,
    resourceType: number,
    dependencyCount: number = 0
): string {
    try {
        // Create content snippet based on the material format
        let contentSnippet = `[Material: ${header.format}, Ver=${header.version}`;

        // Add resource type name for clarity
        let resourceTypeName = "Unknown";
        if (resourceType === MATERIAL_RESOURCE_TYPES.MATERIAL_DEFINITION) {
            resourceTypeName = "MaterialDefinition";
        } else if (resourceType === MATERIAL_RESOURCE_TYPES.MATERIAL_DEFINITION_ALT) {
            resourceTypeName = "MaterialDefinitionAlt";
        } else if (resourceType === MATERIAL_RESOURCE_TYPES.MATERIAL_VARIANT) {
            resourceTypeName = "MaterialVariant";
        } else if (resourceType === MATERIAL_RESOURCE_TYPES.TEXTURE_DEFINITION) {
            resourceTypeName = "TextureDefinition";
        } else if (resourceType === MATERIAL_RESOURCE_TYPES.SHADER_DEFINITION) {
            resourceTypeName = "ShaderDefinition";
        }
        contentSnippet += `, Type=${resourceTypeName}`;

        // Add format-specific information
        if (header.format === 'MTST') {
            contentSnippet += `, Materials=${header.materialCount}`;
            if (header.materialNames && header.materialNames.length > 0) {
                const nameSample = header.materialNames.slice(0, 2).join(', ');
                contentSnippet += `, Names=[${nameSample}${header.materialNames.length > 2 ? '...' : ''}]`;
            }
        } else if (header.format === 'MTRL' || header.format === 'TXMT') {
            if (header.shaderType) contentSnippet += `, Shader=${header.shaderType}`;
            if (header.textureCount) contentSnippet += `, Textures=${header.textureCount}`;
            if (header.parameterCount) contentSnippet += `, Params=${header.parameterCount}`;
        } else if (header.format === 'MTDT') {
            if (header.parameterCount) contentSnippet += `, Params=${header.parameterCount}`;
            if (header.parameters && header.parameters.length > 0) {
                const paramSample = header.parameters.slice(0, 2).map(p => p.name).join(', ');
                contentSnippet += `, ParamNames=[${paramSample}${header.parameters.length > 2 ? '...' : ''}]`;
            }
        } else if (header.format === 'MTDF') {
            if (header.shaderType) contentSnippet += `, Shader=${header.shaderType}`;
            if (header.textureCount) contentSnippet += `, Textures=${header.textureCount}`;
            if (header.materialCount) contentSnippet += `, Materials=${header.materialCount}`;
        } else if (header.format === 'MTVR') {
            if (header.flags) contentSnippet += `, Flags=0x${header.flags.toString(16)}`;
        } else if (header.format === 'TXDF') {
            if (header.textureCount) contentSnippet += `, Textures=${header.textureCount}`;
        } else if (header.format === 'SHDF') {
            if (header.parameterCount) contentSnippet += `, Params=${header.parameterCount}`;
        }

        // Add flags in hex format if not already included
        if (header.flags && !contentSnippet.includes('Flags=')) {
            contentSnippet += `, Flags=0x${header.flags.toString(16)}`;
        }

        // Add dependency count if available
        if (dependencyCount > 0) {
            contentSnippet += `, Dependencies=${dependencyCount}`;
        }

        contentSnippet += `]`;
        return contentSnippet;
    } catch (error) {
        return handleMaterialExtractionError(
            error,
            {
                operation: 'createMaterialContentSnippet',
                additionalInfo: { 
                    format: header?.format || 'Unknown',
                    resourceType
                }
            },
            `[Material: Error creating content snippet]`
        );
    }
}
