/**
 * Image extractor index module
 * 
 * This module exports all image extractor functionality for use in the package analyzer.
 */

// Export main extractor
export { extractImageMetadata } from './imageExtractor.js';

// Export types
export * from './types.js';

// Export format detection and parsing
export {
    detectImageFormat,
    isValidImage,
    getMimeType,
    parseDDS,
    parsePNG,
    parseJPEG,
    parseRLE2
} from './formats/index.js';

// Export utilities
export { generateImageContentSnippet, generateErrorContentSnippet } from './utils/contentSnippetGenerator.js';
export { convertToResourceMetadata, convertToDatabaseMetadata } from './utils/metadataConverter.js';
export { BufferReader } from './utils/bufferReader.js';

// Export error handling
export {
    createImageErrorContext,
    handleImageError,
    withImageExtractionErrorHandling
} from './error/imageExtractorErrorHandler.js';

// Export database utilities
export {
    saveImageMetadata,
    saveImageDependencies
} from './database/databaseUtils.js';
