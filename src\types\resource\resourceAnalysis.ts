﻿// Corrected imports
import { BinaryResourceType } from './core.js';
import { ResourceCategory } from './enums.js';
import { ResourceKey, ResourceMetadata } from './interfaces.js';
// Removed incorrect import: import { ResourceMetadata } from './metadata.js';
import { ResourceConflict } from '../conflict/ConflictTypes.js'; // Assuming ResourceConflict is defined here

export enum AnalysisTier {
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  DETAILED = 'DETAILED',
  COMPREHENSIVE = 'COMPREHENSIVE'
}

export interface ResourceAnalysisMetadata {
  key: ResourceKey;
  type: BinaryResourceType;
  name?: string;
  path?: string;
  size?: number;
  hash?: string;
  timestamp?: number;
  version?: string;
  author?: string;
  description?: string;
  dependencies?: string[];
  conflicts?: string[];
  gameVersion?: string;
  packRequirements?: string[];
  customData?: {
    [key: string]: unknown;
  };
}

export interface ResourceAnalysisInfo {
  key: ResourceKey;
  type: BinaryResourceType;
  metadata: ResourceMetadata;
  conflicts: ResourceConflict[];
  timestamp: number;
  metrics: {
    size: number;
    complexity: number;
    dependencies: number;
    conflicts: number;
    performance?: number;
  };
  recommendations: string[];
}

export interface ResourceAnalysisOptions {
  tier: AnalysisTier;
  includeMetadata?: boolean;
  includeStats?: boolean;
  includeConflicts?: boolean;
  includeDependencies?: boolean;
  includeOverrides?: boolean;
  includeCustomData?: boolean;
  customDataFields?: string[];
  maxDepth?: number;
  timeout?: number;
  validateContent?: boolean;
  validateMetadata?: boolean;
  validateCustomData?: boolean;
  maxContentSize?: number;
  maxMetadataSize?: number;
  maxCustomDataSize?: number;
}

export interface ResourceAnalysisStats {
  totalSize: number;
  compressedSize: number;
  compressionRatio: number;
  resourceCount: number;
  conflictCount: number;
  dependencyCount: number;
  overrideCount: number;
  analysisTime: number;
  memoryUsage: number;
  cpuUsage: number;
  ioOperations: number;
  totalResources: number;
  averageSize: number;
  maxSize: number;
  minSize: number;
  typeDistribution: {
    [key: string]: number;
  };
  categoryDistribution: {
    [key: string]: number;
  };
  errors: Error[];
  warnings: string[];
  startTime: Date;
  endTime: Date;
  duration: number;
}

export interface ResourceAnalysisResult {
  metadata: ResourceMetadata;
  stats?: ResourceAnalysisStats;
  conflicts?: string[];
  dependencies?: string[];
  overrides?: string[];
  customData?: {
    [key: string]: any;
  };
}

export interface ResourceAnalysisReport {
  results: ResourceAnalysisResult[];
  options: ResourceAnalysisOptions;
  timestamp: number;
  duration: number;
  totalResources: number;
  totalConflicts: number;
  totalDependencies: number;
  totalOverrides: number;
  resourceTypeBreakdown: {
    [key in BinaryResourceType]?: number; // Removed TuningResourceType
  };
  stats: ResourceAnalysisStats;
  errors?: Error[];
  warnings?: string[];
}

export interface ResourceAnalysisCollection {
  entries: ResourceAnalysisInfo[];
  map: Map<string, ResourceAnalysisInfo>;
  add(analysis: ResourceAnalysisInfo): void;
  get(key: string): ResourceAnalysisInfo | undefined;
  has(key: string): boolean;
  remove(key: string): boolean;
  clear(): void;
  size(): number;
}
