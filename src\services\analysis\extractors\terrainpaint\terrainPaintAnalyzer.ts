/**
 * Terrain Paint Analyzer
 *
 * This analyzer identifies and extracts metadata from terrain paint mods.
 * Unlike Light and Slot resources, terrain paints don't have a dedicated binary resource type.
 * Instead, they are implemented using a combination of standard resource types:
 * - 0x220557DA: String Table (STBL) resources for names and descriptions
 * - 0x3C2A8647: Object Thumbnails for terrain paint swatches
 * - 0x00B2D882: Image resources (likely DDS textures for the terrain paint)
 * - 0xEBCBB16C and 0x01D0E75D: Additional resources related to terrain paint
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { ResourceKey } from '../../../../types/resource/interfaces.js';
import { ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { StringTableExtractor } from './extractors/stringTableExtractor.js';
import { ThumbnailExtractor } from './extractors/thumbnailExtractor.js';
import { TextureExtractor } from './extractors/textureExtractor.js';
import { AdditionalResourceExtractor } from './extractors/additionalResourceExtractor.js';

const log = new Logger('TerrainPaintAnalyzer');

// Resource types commonly found in terrain paint mods
export const TERRAIN_PAINT_RESOURCE_TYPES = {
    StringTable: 0x220557DA,
    Thumbnail: 0x3C2A8647,
    Texture: 0x00B2D882,
    Unknown1: 0xEBCBB16C,
    Unknown2: 0x01D0E75D
};

/**
 * Analyzes a package to determine if it contains terrain paint resources
 * and extracts metadata from those resources.
 */
export class TerrainPaintAnalyzer {
    private stringTableExtractor: StringTableExtractor;
    private thumbnailExtractor: ThumbnailExtractor;
    private textureExtractor: TextureExtractor;
    private additionalResourceExtractor: AdditionalResourceExtractor;
    private databaseService?: DatabaseService;

    constructor(databaseService?: DatabaseService) {
        this.databaseService = databaseService;
        this.stringTableExtractor = new StringTableExtractor();
        this.thumbnailExtractor = new ThumbnailExtractor();
        this.textureExtractor = new TextureExtractor();
        this.additionalResourceExtractor = new AdditionalResourceExtractor();
    }

    /**
     * Determines if a package is likely a terrain paint mod based on its resources
     * @param resources Array of resources in the package
     * @returns True if the package is likely a terrain paint mod
     */
    public isTerrainPaintMod(resources: { key: ResourceKey, buffer: Buffer }[]): boolean {
        console.log(`Checking if package with ${resources.length} resources is a terrain paint mod`);

        // Count the number of resources of each type
        const typeCounts: Record<number, number> = {};

        for (const resource of resources) {
            const type = resource.key.type;
            typeCounts[type] = (typeCounts[type] || 0) + 1;
        }

        console.log('Resource type counts:', JSON.stringify(typeCounts));

        // Check if the package contains the expected resource types for a terrain paint mod
        const hasStringTable = typeCounts[TERRAIN_PAINT_RESOURCE_TYPES.StringTable] > 0;
        const hasThumbnail = typeCounts[TERRAIN_PAINT_RESOURCE_TYPES.Thumbnail] > 0;
        const hasTexture = typeCounts[TERRAIN_PAINT_RESOURCE_TYPES.Texture] > 0;

        console.log(`Has String Table: ${hasStringTable}, Has Thumbnail: ${hasThumbnail}, Has Texture: ${hasTexture}`);
        console.log(`String Table type: ${TERRAIN_PAINT_RESOURCE_TYPES.StringTable.toString(16)}`);
        console.log(`Thumbnail type: ${TERRAIN_PAINT_RESOURCE_TYPES.Thumbnail.toString(16)}`);
        console.log(`Texture type: ${TERRAIN_PAINT_RESOURCE_TYPES.Texture.toString(16)}`);

        // Check if the package name contains terrain-related keywords
        const packageNameIndicatesTerrainPaint = this.packageNameIndicatesTerrainPaint(resources);
        console.log(`Package name indicates terrain paint: ${packageNameIndicatesTerrainPaint}`);

        // A package is likely a terrain paint mod if it has at least 2 of the expected resource types
        // and its name indicates it's a terrain paint mod
        const likelyTerrainPaintMod =
            (hasStringTable && hasThumbnail) ||
            (hasStringTable && hasTexture) ||
            (hasThumbnail && hasTexture) ||
            packageNameIndicatesTerrainPaint;

        if (likelyTerrainPaintMod) {
            console.log(`Package is likely a terrain paint mod. Resource type counts: ${JSON.stringify(typeCounts)}`);
            log.info(`Package is likely a terrain paint mod. Resource type counts: ${JSON.stringify(typeCounts)}`);
        } else {
            console.log(`Package is NOT likely a terrain paint mod.`);
        }

        return likelyTerrainPaintMod;
    }

    /**
     * Checks if the package name indicates it's a terrain paint mod
     * @param resources Array of resources in the package
     * @returns True if the package name indicates it's a terrain paint mod
     */
    private packageNameIndicatesTerrainPaint(resources: { key: ResourceKey, buffer: Buffer }[]): boolean {
        // Since we don't have access to the package name directly,
        // we'll use a heuristic based on the resource types and instance IDs

        // Check if the package contains the expected resource types for a terrain paint mod
        const hasExpectedResourceTypes = resources.some(resource =>
            Object.values(TERRAIN_PAINT_RESOURCE_TYPES).includes(resource.key.type)
        );

        // Check if any resource instance contains terrain-related keywords
        const terrainKeywords = ['terrain', 'paint', 'ground', 'dirt', 'grass', 'sand', 'soil', 'rock', 'stone', 'gravel', 'mud'];

        // Convert instance IDs to strings and check if they contain terrain-related keywords
        const instancesContainTerrainKeywords = resources.some(resource => {
            const instanceHex = resource.key.instance.toString(16);
            return terrainKeywords.some(keyword => instanceHex.includes(keyword.toLowerCase()));
        });

        // A package is likely a terrain paint mod if it has the expected resource types
        // or if any of its resource instances contain terrain-related keywords
        const result = hasExpectedResourceTypes || instancesContainTerrainKeywords;

        console.log(`Package likely contains terrain paint resources: ${result}`);
        return result;
    }

    /**
     * Analyzes resources in a package to extract terrain paint metadata
     * @param resources Array of resources in the package
     * @param packageId ID of the package in the database
     * @returns Composite metadata for the terrain paint
     */
    public async analyzeResources(
        resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[],
        packageId: number
    ): Promise<TerrainPaintMetadata> {
        log.info(`Analyzing ${resources.length} resources for terrain paint metadata`);

        // Group resources by type
        const resourcesByType: Record<number, { key: ResourceKey, buffer: Buffer, resourceId: number }[]> = {};

        for (const resource of resources) {
            const type = resource.key.type;
            if (!resourcesByType[type]) {
                resourcesByType[type] = [];
            }
            resourcesByType[type].push(resource);
        }

        // Extract metadata from each resource type
        const stringTableMetadata = await this.extractStringTableMetadata(resourcesByType[TERRAIN_PAINT_RESOURCE_TYPES.StringTable] || []);
        const thumbnailMetadata = await this.extractThumbnailMetadata(resourcesByType[TERRAIN_PAINT_RESOURCE_TYPES.Thumbnail] || []);
        const textureMetadata = await this.extractTextureMetadata(resourcesByType[TERRAIN_PAINT_RESOURCE_TYPES.Texture] || []);
        const additionalMetadata = await this.extractAdditionalMetadata(
            resourcesByType[TERRAIN_PAINT_RESOURCE_TYPES.Unknown1] || [],
            resourcesByType[TERRAIN_PAINT_RESOURCE_TYPES.Unknown2] || []
        );

        // Combine metadata from all resource types
        const compositeMetadata: TerrainPaintMetadata = {
            name: stringTableMetadata.name || this.inferNameFromResources(resources),
            description: stringTableMetadata.description || '',
            thumbnailCount: thumbnailMetadata.count || 0,
            textureCount: textureMetadata.count || 0,
            textureFormat: textureMetadata.format || '',
            textureDimensions: textureMetadata.dimensions || '',
            terrainType: this.inferTerrainType(resources, stringTableMetadata),
            contentSnippet: this.generateContentSnippet(stringTableMetadata, thumbnailMetadata, textureMetadata),
            extractorUsed: 'terrainpaint-composite'
        };

        // Save the composite metadata to the database
        await this.saveCompositeMetadata(compositeMetadata, packageId);

        return compositeMetadata;
    }

    /**
     * Extracts metadata from string table resources
     * @param resources Array of string table resources
     * @returns Metadata extracted from string tables
     */
    private async extractStringTableMetadata(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): Promise<{
        name?: string;
        description?: string;
        locale?: string;
        entries?: number;
    }> {
        if (resources.length === 0) {
            return {};
        }

        log.info(`Extracting metadata from ${resources.length} string table resources`);

        // Use the string table extractor to extract metadata
        const metadata = await this.stringTableExtractor.extract(resources);

        return metadata;
    }

    /**
     * Extracts metadata from thumbnail resources
     * @param resources Array of thumbnail resources
     * @returns Metadata extracted from thumbnails
     */
    private async extractThumbnailMetadata(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): Promise<{
        count?: number;
        dimensions?: string;
        format?: string;
    }> {
        if (resources.length === 0) {
            return {};
        }

        log.info(`Extracting metadata from ${resources.length} thumbnail resources`);

        // Use the thumbnail extractor to extract metadata
        const metadata = await this.thumbnailExtractor.extract(resources);

        return metadata;
    }

    /**
     * Extracts metadata from texture resources
     * @param resources Array of texture resources
     * @returns Metadata extracted from textures
     */
    private async extractTextureMetadata(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): Promise<{
        count?: number;
        format?: string;
        dimensions?: string;
    }> {
        if (resources.length === 0) {
            return {};
        }

        log.info(`Extracting metadata from ${resources.length} texture resources`);

        // Use the texture extractor to extract metadata
        const metadata = await this.textureExtractor.extract(resources);

        return metadata;
    }

    /**
     * Extracts metadata from additional resources
     * @param resources1 Array of resources of type Unknown1
     * @param resources2 Array of resources of type Unknown2
     * @returns Metadata extracted from additional resources
     */
    private async extractAdditionalMetadata(
        resources1: { key: ResourceKey, buffer: Buffer, resourceId: number }[],
        resources2: { key: ResourceKey, buffer: Buffer, resourceId: number }[]
    ): Promise<{
        properties?: Record<string, any>;
    }> {
        const allResources = [...resources1, ...resources2];

        if (allResources.length === 0) {
            return {};
        }

        log.info(`Extracting metadata from ${allResources.length} additional resources`);

        // Use the additional resource extractor to extract metadata
        const metadata = await this.additionalResourceExtractor.extract(allResources);

        return metadata;
    }

    /**
     * Infers a name for the terrain paint from the resources
     * @param resources Array of resources in the package
     * @returns Inferred name for the terrain paint
     */
    private inferNameFromResources(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): string {
        // Since we don't have access to the package name directly,
        // we'll try to infer a name from the resource instance IDs

        // Check if any resource instance contains terrain-related keywords
        const terrainKeywords = [
            { keyword: 'dirt', name: 'Dirt Terrain' },
            { keyword: 'grass', name: 'Grass Terrain' },
            { keyword: 'sand', name: 'Sand Terrain' },
            { keyword: 'soil', name: 'Soil Terrain' },
            { keyword: 'rock', name: 'Rock Terrain' },
            { keyword: 'stone', name: 'Stone Terrain' },
            { keyword: 'gravel', name: 'Gravel Terrain' },
            { keyword: 'mud', name: 'Mud Terrain' },
            { keyword: 'snow', name: 'Snow Terrain' },
            { keyword: 'cement', name: 'Cement Terrain' },
            { keyword: 'concrete', name: 'Concrete Terrain' },
            { keyword: 'brick', name: 'Brick Terrain' },
            { keyword: 'wood', name: 'Wood Terrain' },
            { keyword: 'carpet', name: 'Carpet Terrain' },
            { keyword: 'tile', name: 'Tile Terrain' },
            { keyword: 'marble', name: 'Marble Terrain' },
            { keyword: 'metal', name: 'Metal Terrain' },
            { keyword: 'linoleum', name: 'Linoleum Terrain' },
            { keyword: 'leaves', name: 'Leaves Terrain' },
            { keyword: 'water', name: 'Water Terrain' },
            { keyword: 'puddle', name: 'Puddle Terrain' },
            { keyword: 'terrain', name: 'Custom Terrain' },
            { keyword: 'paint', name: 'Custom Terrain Paint' }
        ];

        // Check each resource instance for terrain-related keywords
        for (const resource of resources) {
            const instanceHex = resource.key.instance.toString(16);

            for (const { keyword, name } of terrainKeywords) {
                if (instanceHex.includes(keyword.toLowerCase())) {
                    return name;
                }
            }
        }

        return 'Unknown Terrain Paint';
    }

    /**
     * Infers the terrain type from the resources and string table metadata
     * @param resources Array of resources in the package
     * @param stringTableMetadata Metadata extracted from string tables
     * @returns Inferred terrain type
     */
    private inferTerrainType(
        resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[],
        stringTableMetadata: { name?: string; description?: string; }
    ): string {
        // Try to infer the terrain type from the string table metadata
        if (stringTableMetadata.name) {
            const name = stringTableMetadata.name.toLowerCase();

            // Check for common terrain types
            if (name.includes('dirt')) return 'DIRT';
            if (name.includes('grass')) return 'GRASS';
            if (name.includes('sand')) return 'SAND';
            if (name.includes('stone') || name.includes('rock')) return 'STONE';
            if (name.includes('gravel')) return 'GRAVEL';
            if (name.includes('mud')) return 'DIRT';
            if (name.includes('snow')) return 'SNOW';
            if (name.includes('cement') || name.includes('concrete')) return 'CEMENT';
            if (name.includes('brick')) return 'BRICK';
            if (name.includes('wood')) return 'WOOD_DECK';
            if (name.includes('carpet')) return 'CARPET';
            if (name.includes('tile')) return 'STONE';
            if (name.includes('marble')) return 'MARBLE';
            if (name.includes('metal')) return 'METAL';
            if (name.includes('linoleum')) return 'LINOLEUM';
            if (name.includes('leaves')) return 'LEAVES';
            if (name.includes('water') || name.includes('puddle')) return 'PUDDLE';
        }

        // If no terrain type found, try to infer from the package name
        const packageName = resources[0]?.key?.name?.toLowerCase() || '';

        if (packageName.includes('dirt')) return 'DIRT';
        if (packageName.includes('grass')) return 'GRASS';
        if (packageName.includes('sand')) return 'SAND';
        if (packageName.includes('stone') || packageName.includes('rock')) return 'STONE';
        if (packageName.includes('gravel')) return 'GRAVEL';
        if (packageName.includes('mud')) return 'DIRT';
        if (packageName.includes('snow')) return 'SNOW';
        if (packageName.includes('cement') || packageName.includes('concrete')) return 'CEMENT';
        if (packageName.includes('brick')) return 'BRICK';
        if (packageName.includes('wood')) return 'WOOD_DECK';
        if (packageName.includes('carpet')) return 'CARPET';
        if (packageName.includes('tile')) return 'STONE';
        if (packageName.includes('marble')) return 'MARBLE';
        if (packageName.includes('metal')) return 'METAL';
        if (packageName.includes('linoleum')) return 'LINOLEUM';
        if (packageName.includes('leaves')) return 'LEAVES';
        if (packageName.includes('water') || packageName.includes('puddle')) return 'PUDDLE';

        return 'UNKNOWN';
    }

    /**
     * Generates a content snippet for the terrain paint
     * @param stringTableMetadata Metadata extracted from string tables
     * @param thumbnailMetadata Metadata extracted from thumbnails
     * @param textureMetadata Metadata extracted from textures
     * @returns Content snippet for the terrain paint
     */
    private generateContentSnippet(
        stringTableMetadata: { name?: string; description?: string; },
        thumbnailMetadata: { count?: number; dimensions?: string; },
        textureMetadata: { count?: number; format?: string; dimensions?: string; }
    ): string {
        const name = stringTableMetadata.name || 'Unknown Terrain Paint';
        let snippet = `Terrain Paint: ${name}`;

        if (textureMetadata.count && textureMetadata.count > 0) {
            snippet += `, ${textureMetadata.count} texture(s)`;

            if (textureMetadata.dimensions) {
                snippet += ` (${textureMetadata.dimensions})`;
            }

            if (textureMetadata.format) {
                snippet += `, ${textureMetadata.format}`;
            }
        }

        if (thumbnailMetadata.count && thumbnailMetadata.count > 0) {
            snippet += `, ${thumbnailMetadata.count} thumbnail(s)`;
        }

        return snippet;
    }

    /**
     * Saves the composite metadata to the database if available
     * @param metadata Composite metadata for the terrain paint
     * @param packageId ID of the package in the database
     */
    private async saveCompositeMetadata(metadata: TerrainPaintMetadata, packageId: number): Promise<void> {
        if (!this.databaseService) {
            log.info('No database service available, skipping metadata save');
            return;
        }

        try {
            // Save the composite metadata to the database
            await this.databaseService.parsedContent.saveParsedContent({
                packageId,
                contentType: 'terrain_paint_metadata',
                content: JSON.stringify(metadata)
            });

            log.info(`Saved composite terrain paint metadata for package ${packageId}`);
        } catch (error) {
            log.error(`Error saving composite terrain paint metadata: ${error}`);
        }
    }
}

/**
 * Metadata for a terrain paint
 */
export interface TerrainPaintMetadata extends Partial<ResourceMetadata> {
    name: string;
    description: string;
    thumbnailCount: number;
    textureCount: number;
    textureFormat: string;
    textureDimensions: string;
    terrainType: string;
    contentSnippet: string;
    extractorUsed: string;
}
