/**
 * Entry point for Material extraction
 * This file re-exports the Material extractor for backward compatibility
 */

import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../types/resource/interfaces.js';
import { DatabaseService } from '../../databaseService.js';
import { extractMaterialMetadata as extractMaterialMetadataImpl } from './material/extractMaterialMetadata.js';
export { MATERIAL_RESOURCE_TYPES } from './material/materialConstants.js';

/**
 * Extracts metadata specifically from Material resources.
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object for Material resources.
 */
export async function extractMaterialMetadata(
    key: AppResourceKey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    // Forward to the implementation
    return extractMaterialMetadataImpl(key, buffer, resourceId, databaseService);
}
