import { Logger } from '../../utils/logging/logger.js';
import { PydanticAIResult } from './types.js';

/**
 * Mock implementation of PydanticAIService for testing
 */
export class PydanticAIService {
    private logger: Logger;
    private serverUrl: string;

    constructor(logger: Logger) {
        // Use the provided logger or create a new one
        this.logger = logger || new Logger('PydanticAIService');

        const host = process.env.PYDANTIC_AI_HOST || '127.0.0.1';
        const port = process.env.PYDANTIC_AI_PORT || 8001;
        this.serverUrl = `http://${host}:${port}/analyze_conflict`;
    }

    public async analyzeConflict(pairKey: string, args: any): Promise<PydanticAIResult | null> {
        this.logger.info(`Calling PydanticAI FastAPI server at ${this.serverUrl} for ${pairKey}`);

        try {
            // Mock response for testing
            const result: PydanticAIResult = {
                hasConflict: false,
                severity: "LOW",
                details: "No conflict detected (mock response)",
                recommendations: ["This is a mock response for testing"]
            };

            this.logger.info(`Successfully received and parsed response from PydanticAI server for ${pairKey}`);
            return result;

        } catch (e: any) {
            this.logger.error(`Error calling PydanticAI server for ${pairKey}: ${e.message || e}`);
            return null;
        }
    }
}
