/**
 * Audio formats module for the Sims 4 mod management tool
 *
 * This module provides utilities for detecting and parsing different audio formats,
 * including standard formats (WAV, MP3, OGG) and Sims 4-specific formats (SNR, SNS).
 */

import { Logger } from '../../../../utils/logging/logger.js';

const logger = new Logger('AudioFormats');

/**
 * Sound resource header information based on common audio formats
 */
export interface SoundHeaderInfo {
    format?: string;       // Format identifier (e.g., "RIFF", "OGG", "MP3", "SNR", "SNS")
    sampleRate?: number;   // Sample rate in Hz
    channels?: number;     // Number of audio channels
    bitDepth?: number;     // Bit depth (8, 16, 24, 32)
    duration?: number;     // Duration in seconds (if available)
    dataSize?: number;     // Size of audio data
    loopStart?: number;    // Loop start point (samples)
    loopEnd?: number;      // Loop end point (samples)
    audioType?: string;    // Type of audio (music, voice, effect, ambience, ui, etc.)
    codec?: string;        // Audio codec (XAS, EALayer3, etc.)
    isCompressed?: boolean; // Whether the audio is compressed
    version?: number;      // Format version
    flags?: number;        // Format flags
    headerSize?: number;   // Size of the header

    // XML Sound Effect specific fields
    soundName?: string;    // Name of the sound effect from XML
    soundRefCount?: number; // Number of sound references in the XML
    animationName?: string; // Name of the animation this sound effect is for
    soundRefs?: string[];  // List of sound references in the XML

    // EA Audio Container specific fields
    soundClipCount?: number; // Number of audio clips in the container
    clipOffsets?: number[]; // Offsets to individual clips in the container
    clipSizes?: number[];  // Sizes of individual clips in the container

    // Additional Sims 4 specific fields
    channelFlags?: number; // Channel flags for audio effects
    bankName?: string;     // Name of the sound bank
    bankType?: string;     // Type of sound bank
    isLooping?: boolean;   // Whether the audio is designed to loop
    category?: string;     // Audio category (e.g., "Music", "SFX", "Voice")

    // Metadata fields
    resourceName?: string; // Name of the resource (if available)
    instanceName?: string; // Name derived from instance ID
    groupName?: string;    // Name derived from group ID
}

/**
 * Audio resource types in Sims 4
 */
export const AUDIO_RESOURCE_TYPES = {
    SOUND: 0x2026960B,
    SOUND_EFFECT: 0x0B8BFB57,
    SOUND_EFFECT_ALT: 0x02D5DF13,
    SOUND_EFFECT_ALT2: 0x539399691,
    SOUND_TRACK: 0x193723223,
    SOUND_DATA: 0x2BC4DEEC,
    SOUND_BANK: 0x3F08FB5F,
    HEADERLESS_SOUND: 0x01EEF63A
};

/**
 * Audio type categories in Sims 4
 */
export enum AudioCategory {
    MUSIC = 'music',
    VOICE = 'voice',
    EFFECT = 'effect',
    AMBIENCE = 'ambience',
    UI = 'ui',
    INSTRUMENT = 'instrument',
    SOUND = 'sound',
    SFX = 'sfx',
    DIALOGUE = 'dialogue',
    FOOTSTEP = 'footstep',
    ANIMATION = 'animation',
    OBJECT = 'object',
    ENVIRONMENT = 'environment',
    NOTIFICATION = 'notification',
    STINGER = 'stinger',
    UNKNOWN = 'unknown'
}

/**
 * Audio codec types in Sims 4
 */
export enum AudioCodec {
    XAS = 'XAS',
    EALAYER3 = 'EALayer3',
    EAXMA = 'EAXMA',
    EASTREAM = 'EAStream',
    EAOPUS = 'EAOpus',
    EAMP3 = 'EAMP3',
    EAAC = 'EAAC',
    PCM = 'PCM',
    MP3 = 'MP3',
    OGG = 'OGG',
    UNKNOWN = 'Unknown'
}

/**
 * Audio bank types in Sims 4
 */
export enum AudioBankType {
    SOUND_BANK = 'Sound Bank',
    MUSIC_BANK = 'Music Bank',
    VOICE_BANK = 'Voice Bank',
    EFFECT_BANK = 'Effect Bank',
    UNKNOWN = 'Unknown'
}

/**
 * Detects the audio format from a buffer
 * @param buffer The audio buffer
 * @returns The detected format or undefined if not recognized
 */
export function detectAudioFormat(buffer: Buffer): string | undefined {
    if (buffer.length < 4) return undefined;

    const signature = buffer.toString('ascii', 0, 4);

    // Check for standard audio formats
    if (signature === 'RIFF') {
        return 'WAV';
    } else if (buffer[0] === 0x4F && buffer[1] === 0x67 && buffer[2] === 0x67 && buffer[3] === 0x53) {
        return 'OGG';
    } else if ((buffer[0] === 0xFF && (buffer[1] & 0xE0) === 0xE0) ||
               (buffer[0] === 0x49 && buffer[1] === 0x44 && buffer[2] === 0x33)) { // ID3 tag
        return 'MP3';
    }

    // Check for Sims 4 specific formats
    else if (signature === 'SNR\0' || signature === 'SNR ') {
        return 'SNR';
    } else if (signature === 'SNS\0' || signature === 'SNS ') {
        return 'SNS';
    } else if (signature === 'SCH\0' || signature === 'SCH ') {
        return 'SCH'; // Sound Channel format
    } else if (signature === 'SBK\0' || signature === 'SBK ') {
        return 'SBK'; // Sound Bank format
    } else if (signature === 'EAL3') {
        return 'EALayer3';
    }

    // Check for XML format (for animation sound effects)
    else if (buffer.length >= 5 && buffer.toString('ascii', 0, 5) === '<?xml') {
        return 'XML';
    }

    // Check for EA Headerless Audio format
    else if (buffer.length > 8) {
        const formatMarker = buffer.readUInt32LE(4);
        if (formatMarker === 0x0000002F || (formatMarker & 0xFF00FFFF) === 0x0000002F) {
            return 'EA Headerless Audio';
        }

        // Check for EA Audio Container format
        // Look for common EA audio signatures in the first 64 bytes
        if (buffer.length > 64) {
            const headerBytes = buffer.slice(0, 64);
            const headerHex = headerBytes.toString('hex').toUpperCase();

            if (headerHex.includes('45414C33')) { // 'EAL3' in hex
                return 'EA Audio (EALayer3)';
            } else if (headerHex.includes('45414F50')) { // 'EAOP' in hex
                return 'EA Audio (EAOpus)';
            } else if (headerHex.includes('45414D50')) { // 'EAMP' in hex
                return 'EA Audio (EAMP3)';
            } else if (headerHex.includes('********')) { // 'EAAC' in hex
                return 'EA Audio (EAAC)';
            } else if (headerHex.includes('********')) { // 'EAST' in hex
                return 'EA Audio (EAStream)';
            } else if (headerHex.includes('********')) { // 'EAXS' in hex
                return 'EA Audio (XAS)';
            }
        }

        // Check for Sound Bank format
        // Sound banks often have a specific structure with multiple clips
        if (buffer.length > 1024) {
            let clipCount = 0;
            let pos = 0;

            // Scan for potential clip markers in the first 10KB
            while (pos < Math.min(buffer.length, 10240)) {
                if (pos + 8 < buffer.length) {
                    const marker = buffer.readUInt32LE(pos + 4);
                    if ((marker & 0xFF00FFFF) === 0x0000002F) {
                        clipCount++;

                        // If we find multiple clips, it's likely a sound bank
                        if (clipCount >= 3) {
                            return 'EA Sound Bank';
                        }
                    }
                }
                pos += 4;
            }
        }
    }

    return undefined;
}

/**
 * Parses a WAV header from a buffer
 * @param buffer The audio buffer
 * @returns The parsed header information
 */
export function parseWavHeader(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'WAV',
        codec: AudioCodec.PCM
    };

    try {
        if (buffer.length < 36) return header;

        header.dataSize = buffer.readUInt32LE(4);

        // Parse WAV format chunk
        const audioFormat = buffer.readUInt16LE(20); // 1 for PCM
        header.channels = buffer.readUInt16LE(22);
        header.sampleRate = buffer.readUInt32LE(24);
        const byteRate = buffer.readUInt32LE(28);
        const blockAlign = buffer.readUInt16LE(32);
        header.bitDepth = buffer.readUInt16LE(34);

        // Find the 'data' chunk
        let dataOffset = 12; // Start after RIFF header
        while (dataOffset < buffer.length - 8) {
            const chunkSignature = buffer.toString('ascii', dataOffset, dataOffset + 4);
            const chunkSize = buffer.readUInt32LE(dataOffset + 4);
            if (chunkSignature === 'data') {
                const dataChunkSize = chunkSize;
                if (header.sampleRate && header.channels && header.bitDepth) {
                    const bytesPerSecond = header.sampleRate * header.channels * (header.bitDepth / 8);
                    if (bytesPerSecond > 0) {
                        header.duration = dataChunkSize / bytesPerSecond;
                    }
                }
                break; // Found data chunk
            }
            dataOffset += 8 + chunkSize; // Move to next chunk
        }

        // Check for loop points in 'smpl' chunk
        dataOffset = 12; // Reset to start after RIFF header
        while (dataOffset < buffer.length - 8) {
            const chunkSignature = buffer.toString('ascii', dataOffset, dataOffset + 4);
            const chunkSize = buffer.readUInt32LE(dataOffset + 4);
            if (chunkSignature === 'smpl' && dataOffset + 36 < buffer.length) {
                // smpl chunk contains loop information
                const numLoops = buffer.readUInt32LE(dataOffset + 28);
                if (numLoops > 0 && dataOffset + 52 < buffer.length) {
                    // Loop start and end points are at offsets 44 and 48 in the first loop
                    header.loopStart = buffer.readUInt32LE(dataOffset + 44);
                    header.loopEnd = buffer.readUInt32LE(dataOffset + 48);
                }
                break;
            }
            dataOffset += 8 + chunkSize; // Move to next chunk
        }
    } catch (error: any) {
        logger.error(`Error parsing WAV header: ${error.message || error}`);
    }

    return header;
}

/**
 * Parses an MP3 header from a buffer
 * @param buffer The audio buffer
 * @returns The parsed header information
 */
export function parseMp3Header(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'MP3',
        codec: AudioCodec.MP3,
        isCompressed: true
    };

    try {
        if (buffer.length < 4) return header;

        const byte2 = buffer[2];
        const byte3 = buffer[3];

        // Sample rate index (bits 2-3 of byte 2)
        const sampleRateIndex = (byte2 >> 2) & 0x03;
        const sampleRates = [44100, 48000, 32000, 0]; // For MPEG 1, Layer III
        header.sampleRate = sampleRates[sampleRateIndex];

        // Channel mode (bits 6-7 of byte 3)
        const channelMode = (byte3 >> 6) & 0x03;
        // 00: Stereo, 01: Joint stereo, 10: Dual channel, 11: Mono
        header.channels = (channelMode === 3) ? 1 : 2;

        // Bit rate index (bits 4-7 of byte 2)
        const bitrateIndex = (byte2 >> 4) & 0x0F;
        const bitrates = [0, 32, 40, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320, 0]; // kbps
        const bitrate = bitrates[bitrateIndex] * 1000; // Convert to bps

        // Calculate approximate duration if we have bitrate
        if (bitrate > 0) {
            header.duration = (buffer.length * 8) / bitrate;
        }
    } catch (error: any) {
        logger.error(`Error parsing MP3 header: ${error.message || error}`);
    }

    return header;
}

/**
 * Parses an OGG header from a buffer
 * @param buffer The audio buffer
 * @returns The parsed header information
 */
export function parseOggHeader(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'OGG',
        codec: AudioCodec.OGG,
        isCompressed: true
    };

    try {
        if (buffer.length < 30) return header;

        // Attempt to extract sample rate and channels from the first page header
        const pageSegments = buffer[26];
        let segmentTableOffset = 27;
        let headerSize = 0;
        for(let i = 0; i < pageSegments; i++) {
            if (segmentTableOffset + i >= buffer.length) break;
            headerSize += buffer[segmentTableOffset + i];
        }
        const identificationHeaderOffset = segmentTableOffset + pageSegments;

        // Identification header starts with 0x01 + "vorbis" or 0x01 + "opus"
        if (identificationHeaderOffset + 7 <= buffer.length) {
            const headerType = buffer[identificationHeaderOffset];
            const codec = buffer.toString('ascii', identificationHeaderOffset + 1, identificationHeaderOffset + 7);

            if (headerType === 0x01 && codec === 'vorbis') {
                // Vorbis identification header
                if (identificationHeaderOffset + 16 + 4 <= buffer.length) {
                    header.channels = buffer[identificationHeaderOffset + 12];
                    header.sampleRate = buffer.readUInt32LE(identificationHeaderOffset + 16);
                }
            } else if (headerType === 0x01 && codec === 'Opus  ') { // Note the spaces for 'Opus'
                // Opus identification header
                if (identificationHeaderOffset + 9 <= buffer.length) {
                    header.channels = buffer[identificationHeaderOffset + 9];
                    header.sampleRate = 48000; // Opus always uses 48kHz internally
                }
            }
        }
    } catch (error: any) {
        logger.error(`Error parsing OGG header: ${error.message || error}`);
    }

    return header;
}

/**
 * Parses a Sims 4 SNR (Sound Resource) header from a buffer
 * @param buffer The audio buffer
 * @returns The parsed header information
 */
export function parseSnrHeader(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'SNR',
        audioType: AudioCategory.EFFECT
    };

    try {
        if (buffer.length < 40) return header;

        header.version = buffer.readUInt32LE(4);
        header.headerSize = buffer.readUInt32LE(8);
        header.dataSize = buffer.readUInt32LE(12);
        header.sampleRate = buffer.readUInt32LE(16);
        header.channels = buffer.readUInt32LE(20);
        header.bitDepth = buffer.readUInt32LE(24);
        header.flags = buffer.readUInt32LE(28);
        header.loopStart = buffer.readUInt32LE(32);
        header.loopEnd = buffer.readUInt32LE(36);

        // Determine if the audio is compressed
        header.isCompressed = (header.flags! & 0x1) !== 0;

        // Determine codec based on flags
        if (header.isCompressed) {
            // Check for EALayer3 signature in the data section
            if (buffer.length >= header.headerSize! + 4) {
                const codecSignature = buffer.toString('ascii', header.headerSize!, header.headerSize! + 4);
                if (codecSignature === 'EAL3') {
                    header.codec = AudioCodec.EALAYER3;
                } else {
                    header.codec = AudioCodec.XAS; // Default for compressed Sims 4 audio
                }
            } else {
                header.codec = AudioCodec.XAS; // Default for compressed Sims 4 audio
            }
        } else {
            header.codec = AudioCodec.PCM; // Uncompressed audio
        }

        // Calculate duration if we have sample rate
        if (header.sampleRate && header.dataSize) {
            if (header.isCompressed) {
                // For compressed audio, this is just an approximation
                // EALayer3 is similar to MP3, so we can use a similar calculation
                // Assuming ~128kbps for compressed audio
                const bitrate = 128000; // bits per second
                header.duration = (header.dataSize * 8) / bitrate;
            } else {
                // For uncompressed audio, we can calculate exactly
                const bytesPerSample = header.bitDepth ? (header.bitDepth / 8) : 2;
                const channels = header.channels || 1;
                header.duration = header.dataSize / (bytesPerSample * channels * header.sampleRate);
            }
        }
    } catch (error: any) {
        logger.error(`Error parsing SNR header: ${error.message || error}`);
    }

    return header;
}

/**
 * Parses a Sims 4 SNS (Sound Stream) header from a buffer
 * @param buffer The audio buffer
 * @returns The parsed header information
 */
export function parseSnsHeader(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'SNS',
        audioType: AudioCategory.MUSIC
    };

    try {
        if (buffer.length < 40) return header;

        header.version = buffer.readUInt32LE(4);
        header.headerSize = buffer.readUInt32LE(8);
        header.dataSize = buffer.readUInt32LE(12);
        header.sampleRate = buffer.readUInt32LE(16);
        header.channels = buffer.readUInt32LE(20);
        header.bitDepth = buffer.readUInt32LE(24);
        header.flags = buffer.readUInt32LE(28);
        header.loopStart = buffer.readUInt32LE(32);
        header.loopEnd = buffer.readUInt32LE(36);

        // Determine if the audio is compressed
        header.isCompressed = (header.flags! & 0x1) !== 0;

        // Determine codec based on flags
        if (header.isCompressed) {
            // Check for EALayer3 signature in the data section
            if (buffer.length >= header.headerSize! + 4) {
                const codecSignature = buffer.toString('ascii', header.headerSize!, header.headerSize! + 4);
                if (codecSignature === 'EAL3') {
                    header.codec = AudioCodec.EALAYER3;
                } else {
                    header.codec = AudioCodec.XAS; // Default for compressed Sims 4 audio
                }
            } else {
                header.codec = AudioCodec.XAS; // Default for compressed Sims 4 audio
            }
        } else {
            header.codec = AudioCodec.PCM; // Uncompressed audio
        }

        // Calculate duration if we have sample rate
        if (header.sampleRate && header.dataSize) {
            if (header.isCompressed) {
                // For compressed audio, this is just an approximation
                // EALayer3 is similar to MP3, so we can use a similar calculation
                // Assuming ~128kbps for compressed audio
                const bitrate = 128000; // bits per second
                header.duration = (header.dataSize * 8) / bitrate;
            } else {
                // For uncompressed audio, we can calculate exactly
                const bytesPerSample = header.bitDepth ? (header.bitDepth / 8) : 2;
                const channels = header.channels || 1;
                header.duration = header.dataSize / (bytesPerSample * channels * header.sampleRate);
            }
        }
    } catch (error: any) {
        logger.error(`Error parsing SNS header: ${error.message || error}`);
    }

    return header;
}

/**
 * Parses an EALayer3 header from a buffer
 * @param buffer The audio buffer
 * @returns The parsed header information
 */
export function parseEaLayer3Header(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'EALayer3',
        codec: AudioCodec.EALAYER3,
        isCompressed: true
    };

    try {
        if (buffer.length < 12) return header;

        // EALayer3 header structure (simplified):
        // 0-3: 'EAL3' signature
        // 4-7: Header size (uint32)
        // 8-11: Sample count (uint32)
        // 12-15: Sample rate (uint32)
        // 16-19: Channels (uint32)
        // 20-23: Bit depth (uint32)
        // 24-27: Flags (uint32)

        if (buffer.length >= 28) {
            const headerSize = buffer.readUInt32LE(4);
            const sampleCount = buffer.readUInt32LE(8);
            header.sampleRate = buffer.readUInt32LE(12);
            header.channels = buffer.readUInt32LE(16);
            header.bitDepth = buffer.readUInt32LE(20);
            const flags = buffer.readUInt32LE(24);

            // Calculate duration from sample count and sample rate
            if (header.sampleRate && sampleCount) {
                header.duration = sampleCount / header.sampleRate;
            }
        }
    } catch (error: any) {
        logger.error(`Error parsing EALayer3 header: ${error.message || error}`);
    }

    return header;
}

/**
 * Parses an EA Headerless Audio format from a buffer
 * @param buffer The audio buffer
 * @returns The parsed header information
 */
export function parseEaHeaderlessAudio(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'EA Headerless Audio',
        codec: AudioCodec.EALAYER3,
        isCompressed: true,
        audioType: AudioCategory.SOUND
    };

    try {
        if (buffer.length < 16) return header;

        // EA Headerless Audio format appears to have a header structure like:
        // 0-1: Size marker or flags (uint16)
        // 2-3: Unknown (usually 0x0000)
        // 4-7: Format marker (usually 0x0000002F)
        // 8-15: Various format-specific data

        const sizeMarker = buffer.readUInt16LE(0);
        const formatMarker = buffer.readUInt32LE(4);

        // Check for known format markers
        if (formatMarker === 0x0000002F) {
            header.format = 'EA Audio Container';
            header.headerSize = 16; // Standard header size
        } else if ((formatMarker & 0x00FFFFFF) === 0x00000001) {
            // This appears to be a different type of EA audio format
            header.format = 'EA Audio Format';
            header.headerSize = 32; // Larger header size
            header.version = (formatMarker >> 24) & 0xFF;
        }

        // Try to determine if this is a container with multiple audio clips
        if (buffer.length > 1024) {
            // This is likely a container with multiple audio clips
            header.format = 'EA Audio Container';

            // Scan for audio clip markers and collect offsets
            const clipOffsets: number[] = [];
            const clipSizes: number[] = [];
            let pos = 0;

            // First pass: find all potential clip markers
            while (pos < Math.min(buffer.length, 100000)) { // Scan first 100KB only
                if (pos + 8 < buffer.length) {
                    const marker = buffer.readUInt32LE(pos + 4);
                    if ((marker & 0xFF00FFFF) === 0x0000002F) { // Pattern: XX 00 00 2F
                        clipOffsets.push(pos);

                        // Try to determine clip size by looking for the next marker
                        let nextPos = pos + 16; // Skip header
                        while (nextPos < Math.min(buffer.length, pos + 1000000)) { // Look ahead up to 1MB
                            if (nextPos + 8 < buffer.length) {
                                const nextMarker = buffer.readUInt32LE(nextPos + 4);
                                if ((nextMarker & 0xFF00FFFF) === 0x0000002F) {
                                    // Found next clip
                                    clipSizes.push(nextPos - pos);
                                    break;
                                }
                            }
                            nextPos += 4;
                        }

                        // If we didn't find a next marker, this is the last clip
                        if (clipSizes.length < clipOffsets.length) {
                            clipSizes.push(buffer.length - pos);
                        }

                        // Skip ahead to after this clip
                        if (clipSizes.length > 0) {
                            pos += clipSizes[clipSizes.length - 1] - 4;
                        } else {
                            pos += 16; // Skip header
                        }
                    }
                }
                pos += 4;
            }

            if (clipOffsets.length > 0) {
                header.soundClipCount = clipOffsets.length;
                header.clipOffsets = clipOffsets;
                header.clipSizes = clipSizes;

                // Try to determine audio type based on clip count and sizes
                if (clipOffsets.length > 20) {
                    // Many small clips is likely a sound bank
                    header.format = 'EA Sound Bank';
                    header.bankType = AudioBankType.SOUND_BANK;
                } else if (clipOffsets.length > 5) {
                    // Several medium clips could be voice lines
                    header.audioType = AudioCategory.VOICE;
                    header.format = 'EA Voice Container';
                } else if (clipOffsets.length === 1 && buffer.length > 1000000) {
                    // Single large clip is likely music
                    header.audioType = AudioCategory.MUSIC;
                    header.format = 'EA Music Container';
                }
            }
        }

        // Try to extract codec information
        // Check for codec identifiers in the first 64 bytes
        const headerBytes = buffer.slice(0, Math.min(buffer.length, 64));
        const headerHex = headerBytes.toString('hex').toUpperCase();

        if (headerHex.includes('45414C33')) { // 'EAL3' in hex
            header.codec = AudioCodec.EALAYER3;
        } else if (headerHex.includes('45414F50')) { // 'EAOP' in hex
            header.codec = AudioCodec.EAOPUS;
        } else if (headerHex.includes('45414D50')) { // 'EAMP' in hex
            header.codec = AudioCodec.EAMP3;
        } else if (headerHex.includes('********')) { // 'EAAC' in hex
            header.codec = AudioCodec.EAAC;
        } else if (headerHex.includes('********')) { // 'EAST' in hex
            header.codec = AudioCodec.EASTREAM;
        } else if (headerHex.includes('********')) { // 'EAXS' in hex
            header.codec = AudioCodec.XAS;
        }

        // Try to extract some metadata from the header
        if (buffer.length >= 32) {
            // These are guesses based on observed patterns
            const possibleSampleRate = buffer.readUInt32LE(16);
            if (possibleSampleRate >= 8000 && possibleSampleRate <= 48000) {
                header.sampleRate = possibleSampleRate;
            }

            const possibleChannels = buffer.readUInt16LE(20);
            if (possibleChannels >= 1 && possibleChannels <= 8) {
                header.channels = possibleChannels;
            }

            // Check for loop flags
            const possibleFlags = buffer.readUInt32LE(28);
            if (possibleFlags !== 0) {
                header.flags = possibleFlags;

                // Check if bit 0 is set (often indicates looping)
                if ((possibleFlags & 0x1) !== 0) {
                    header.isLooping = true;
                }

                // Check for loop points
                if (buffer.length >= 40) {
                    const possibleLoopStart = buffer.readUInt32LE(32);
                    const possibleLoopEnd = buffer.readUInt32LE(36);

                    if (possibleLoopStart < possibleLoopEnd && possibleLoopEnd > 0) {
                        header.loopStart = possibleLoopStart;
                        header.loopEnd = possibleLoopEnd;
                    }
                }
            }
        }

        // Calculate approximate duration based on compressed audio size
        // This is a very rough estimate
        if (buffer.length > (header.headerSize || 0)) {
            const dataSize = buffer.length - (header.headerSize || 0);
            header.dataSize = dataSize;

            // Estimate bitrate based on codec
            let bitrate = 128000; // Default: 128kbps

            if (header.codec === AudioCodec.EALAYER3) {
                bitrate = 128000; // ~128kbps for EALayer3
            } else if (header.codec === AudioCodec.EAOPUS) {
                bitrate = 96000; // ~96kbps for EAOpus
            } else if (header.codec === AudioCodec.XAS) {
                bitrate = 64000; // ~64kbps for XAS
            }

            // Calculate duration
            header.duration = (dataSize * 8) / bitrate;

            // Adjust for multi-clip containers
            if (header.soundClipCount && header.soundClipCount > 1) {
                // For multi-clip containers, the duration is less meaningful
                // but we can provide an average clip duration
                header.duration = header.duration / header.soundClipCount;
            }
        }

    } catch (error: any) {
        logger.error(`Error parsing EA Headerless Audio: ${error.message || error}`);
    }

    return header;
}

/**
 * Parses an XML-based animation sound effect from a buffer
 * @param buffer The buffer containing XML data
 * @returns The parsed header information
 */
export function parseXmlSoundEffect(buffer: Buffer): SoundHeaderInfo {
    const header: SoundHeaderInfo = {
        format: 'XML Sound Effect',
        audioType: AudioCategory.ANIMATION
    };

    try {
        if (buffer.length < 16) return header;

        // Check if this is an XML file
        const signature = buffer.toString('ascii', 0, 5);
        if (signature !== '<?xml') {
            return header;
        }

        // This is an XML file, try to extract some information
        const xmlContent = buffer.toString('utf8', 0, Math.min(buffer.length, 2000));

        // Extract ASM name (animation sound mapping)
        const asmMatch = xmlContent.match(/<ASM name="([^"]+)"/);
        if (asmMatch && asmMatch[1]) {
            header.soundName = asmMatch[1];
        }

        // Extract animation name
        const animMatch = xmlContent.match(/animation name="([^"]+)"/);
        if (animMatch && animMatch[1]) {
            header.animationName = animMatch[1];
        }

        // Extract sound references
        const soundRefs: string[] = [];
        const soundRefRegex = /sound_ref="([^"]+)"/g;
        let match;
        while ((match = soundRefRegex.exec(xmlContent)) !== null) {
            soundRefs.push(match[1]);
        }

        if (soundRefs.length > 0) {
            header.soundRefs = soundRefs;
            header.soundRefCount = soundRefs.length;
        }

        // Determine audio type based on content
        if (header.soundName) {
            if (header.soundName.toLowerCase().includes('voice') ||
                header.soundName.toLowerCase().includes('talk') ||
                header.soundName.toLowerCase().includes('speech') ||
                header.soundName.toLowerCase().includes('dialog')) {
                header.audioType = AudioCategory.VOICE;
            } else if (header.soundName.toLowerCase().includes('music') ||
                       header.soundName.toLowerCase().includes('song')) {
                header.audioType = AudioCategory.MUSIC;
            } else if (header.soundName.toLowerCase().includes('ambient') ||
                       header.soundName.toLowerCase().includes('env')) {
                header.audioType = AudioCategory.AMBIENCE;
            } else if (header.soundName.toLowerCase().includes('ui') ||
                       header.soundName.toLowerCase().includes('interface')) {
                header.audioType = AudioCategory.UI;
            } else if (header.soundName.toLowerCase().includes('footstep') ||
                       header.soundName.toLowerCase().includes('foot_')) {
                header.audioType = AudioCategory.FOOTSTEP;
            } else if (header.soundName.toLowerCase().includes('sfx') ||
                       header.soundName.toLowerCase().includes('effect')) {
                header.audioType = AudioCategory.SFX;
            }
        }

        // Check for specific animation types
        if (header.animationName) {
            if (header.animationName.toLowerCase().includes('footstep') ||
                header.animationName.toLowerCase().includes('walk') ||
                header.animationName.toLowerCase().includes('run')) {
                header.audioType = AudioCategory.FOOTSTEP;
            } else if (header.animationName.toLowerCase().includes('voice') ||
                       header.animationName.toLowerCase().includes('talk') ||
                       header.animationName.toLowerCase().includes('chat')) {
                header.audioType = AudioCategory.VOICE;
            }
        }

        // Check sound references for clues about audio type
        if (header.soundRefs) {
            for (const ref of header.soundRefs) {
                const refLower = ref.toLowerCase();
                if (refLower.includes('voice') || refLower.includes('talk')) {
                    header.audioType = AudioCategory.VOICE;
                    break;
                } else if (refLower.includes('footstep')) {
                    header.audioType = AudioCategory.FOOTSTEP;
                    break;
                } else if (refLower.includes('music')) {
                    header.audioType = AudioCategory.MUSIC;
                    break;
                }
            }
        }

    } catch (error: any) {
        logger.error(`Error parsing XML Sound Effect: ${error.message || error}`);
    }

    return header;
}

/**
 * Determines the audio type based on resource type and instance ID
 * @param typeId The resource type ID
 * @param instanceId The resource instance ID
 * @returns The audio type
 */
export function determineAudioType(typeId: number, instanceId: bigint): string {
    const instanceHex = instanceId.toString(16).toLowerCase();
    const instanceStr = Buffer.from(instanceHex, 'hex').toString('utf8').toLowerCase();

    // Check resource type first - this is the most reliable indicator
    if (typeId === AUDIO_RESOURCE_TYPES.SOUND_TRACK) {
        return AudioCategory.MUSIC;
    } else if (typeId === AUDIO_RESOURCE_TYPES.SOUND_EFFECT) {
        return AudioCategory.SFX;
    } else if (typeId === AUDIO_RESOURCE_TYPES.SOUND_EFFECT_ALT) {
        return AudioCategory.ANIMATION; // XML-based animation sound effects
    } else if (typeId === AUDIO_RESOURCE_TYPES.SOUND_EFFECT_ALT2) {
        return AudioCategory.EFFECT;
    } else if (typeId === AUDIO_RESOURCE_TYPES.SOUND_BANK) {
        return AudioCategory.SOUND; // Sound banks contain multiple sounds
    } else if (typeId === AUDIO_RESOURCE_TYPES.HEADERLESS_SOUND) {
        // For headerless sounds, we need to rely more on the instance ID
        // but default to SOUND if we can't determine more specifically
        if (instanceHex.length > 0) {
            // Continue to the instance ID checks below
        } else {
            return AudioCategory.SOUND;
        }
    }

    // Check instance ID for clues - both hex representation and string representation
    // Voice/Dialogue related
    if (instanceHex.includes('voice') || instanceHex.includes('vocal') ||
        instanceHex.includes('speech') || instanceHex.includes('talk') ||
        instanceHex.includes('dialog') || instanceHex.includes('chat') ||
        instanceStr.includes('voice') || instanceStr.includes('talk')) {
        return AudioCategory.VOICE;
    }
    // Music related
    else if (instanceHex.includes('music') || instanceHex.includes('song') ||
             instanceHex.includes('track') || instanceHex.includes('theme') ||
             instanceHex.includes('tune') || instanceHex.includes('jingle') ||
             instanceStr.includes('music') || instanceStr.includes('song')) {
        return AudioCategory.MUSIC;
    }
    // Sound effects
    else if (instanceHex.includes('sfx') || instanceHex.includes('effect') ||
             instanceStr.includes('sfx') || instanceStr.includes('effect')) {
        return AudioCategory.SFX;
    }
    // Ambient sounds
    else if (instanceHex.includes('ambient') || instanceHex.includes('env') ||
             instanceHex.includes('background') || instanceHex.includes('atmos') ||
             instanceStr.includes('ambient') || instanceStr.includes('environment')) {
        return AudioCategory.AMBIENCE;
    }
    // UI sounds
    else if (instanceHex.includes('ui') || instanceHex.includes('interface') ||
             instanceHex.includes('menu') || instanceHex.includes('button') ||
             instanceStr.includes('ui') || instanceStr.includes('interface')) {
        return AudioCategory.UI;
    }
    // Instrument sounds
    else if (instanceHex.includes('instrument') || instanceHex.includes('guitar') ||
             instanceHex.includes('piano') || instanceHex.includes('drum') ||
             instanceHex.includes('bass') || instanceHex.includes('violin') ||
             instanceStr.includes('instrument') || instanceStr.includes('guitar')) {
        return AudioCategory.INSTRUMENT;
    }
    // Footstep sounds
    else if (instanceHex.includes('footstep') || instanceHex.includes('foot_') ||
             instanceHex.includes('step') || instanceHex.includes('walk') ||
             instanceStr.includes('footstep') || instanceStr.includes('step')) {
        return AudioCategory.FOOTSTEP;
    }
    // Notification sounds
    else if (instanceHex.includes('notif') || instanceHex.includes('alert') ||
             instanceHex.includes('warn') || instanceHex.includes('ping') ||
             instanceStr.includes('notification') || instanceStr.includes('alert')) {
        return AudioCategory.NOTIFICATION;
    }
    // Object sounds
    else if (instanceHex.includes('object') || instanceHex.includes('item') ||
             instanceHex.includes('prop') || instanceHex.includes('furniture') ||
             instanceStr.includes('object') || instanceStr.includes('item')) {
        return AudioCategory.OBJECT;
    }
    // Stinger sounds (short musical phrases for transitions)
    else if (instanceHex.includes('stinger') || instanceHex.includes('transition') ||
             instanceHex.includes('sting') || instanceHex.includes('fanfare') ||
             instanceStr.includes('stinger') || instanceStr.includes('transition')) {
        return AudioCategory.STINGER;
    }

    // Default based on resource type if we couldn't determine from instance ID
    if (typeId === AUDIO_RESOURCE_TYPES.SOUND) {
        return AudioCategory.SOUND;
    }

    return AudioCategory.UNKNOWN;
}

/**
 * Creates a content snippet from sound header information
 * @param header The sound header information
 * @param buffer The audio buffer
 * @returns A content snippet string
 */
export function createContentSnippet(header: SoundHeaderInfo, buffer: Buffer): string {
    const soundType = header.audioType ?
        header.audioType.charAt(0).toUpperCase() + header.audioType.slice(1) :
        'Sound';

    // Handle XML Sound Effect format
    if (header.format === 'XML Sound Effect') {
        let snippet = `[Animation Sound Effect`;

        if (header.soundName) {
            snippet += `: ${header.soundName}`;
        }

        if (header.animationName) {
            snippet += ` for ${header.animationName}`;
        }

        if (header.soundRefCount) {
            snippet += `, ${header.soundRefCount} sound references`;
        }

        // Add a few sound references if available
        if (header.soundRefs && header.soundRefs.length > 0) {
            const maxRefs = Math.min(3, header.soundRefs.length);
            snippet += ` (${header.soundRefs.slice(0, maxRefs).join(', ')}${header.soundRefs.length > maxRefs ? '...' : ''})`;
        }

        snippet += `, Size=${buffer.length} bytes]`;
        return snippet;
    }

    // Handle EA Audio Container formats
    if (header.format && (
        header.format.includes('EA Audio Container') ||
        header.format.includes('EA Headerless Audio') ||
        header.format.includes('EA Sound Bank') ||
        header.format.includes('EA Voice Container') ||
        header.format.includes('EA Music Container')
    )) {
        let snippet = `[${soundType}`;

        if (header.format) {
            snippet += `: ${header.format}`;
        }

        if (header.soundClipCount) {
            snippet += `, ${header.soundClipCount} clips`;
        }

        if (header.bankName) {
            snippet += `, Bank: ${header.bankName}`;
        }

        if (header.bankType) {
            snippet += `, Type: ${header.bankType}`;
        }

        snippet += `, Size=${buffer.length} bytes`;

        if (header.sampleRate) snippet += `, Rate=${header.sampleRate}Hz`;
        if (header.channels) snippet += `, Channels=${header.channels}`;
        if (header.codec) snippet += `, Codec=${header.codec}`;

        if (header.isLooping) {
            snippet += `, Looping`;

            if (header.loopStart !== undefined && header.loopEnd !== undefined) {
                snippet += ` (${header.loopStart}-${header.loopEnd})`;
            }
        }

        if (header.duration && header.soundClipCount && header.soundClipCount > 1) {
            snippet += `, Avg. Duration=${header.duration.toFixed(2)}s`;
        } else if (header.duration) {
            snippet += `, Duration=${header.duration.toFixed(2)}s`;
        }

        snippet += ']';
        return snippet;
    }

    // Handle standard audio formats
    let snippet = `[${soundType}: Format=${header.format || 'Unknown'}, Size=${buffer.length} bytes`;

    if (header.sampleRate) snippet += `, Rate=${header.sampleRate}Hz`;
    if (header.channels) snippet += `, Channels=${header.channels}`;
    if (header.bitDepth) snippet += `, ${header.bitDepth}-bit`;
    if (header.duration) snippet += `, Duration=${header.duration.toFixed(2)}s`;
    if (header.codec) snippet += `, Codec=${header.codec}`;
    if (header.isCompressed) snippet += `, Compressed`;

    if (header.isLooping) {
        snippet += `, Looping`;
    } else if (header.loopStart !== undefined && header.loopEnd !== undefined &&
        (header.loopStart > 0 || header.loopEnd > 0)) {
        snippet += `, Loop: ${header.loopStart}-${header.loopEnd}`;
    }

    if (header.version) {
        snippet += `, Version=${header.version}`;
    }

    if (header.category) {
        snippet += `, Category=${header.category}`;
    }

    snippet += ']';

    return snippet;
}
