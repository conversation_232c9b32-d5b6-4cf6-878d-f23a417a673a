/**
 * Extracts metadata from Material resources
 */

import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService, DependencyInfo } from '../../../databaseService.js';
import {
    MTST_SIGNATURE,
    MTRL_SIGNATURE,
    MTDT_SIGNATURE,
    TXMT_SIGNATURE,
    MATERIAL_RESOURCE_TYPES,
    MaterialHeaderInfo,
    parseMtst,
    parseMtrl,
    parseMtdt,
    parseTxmt,
    detectUnknownFormat,
    createMaterialContentSnippet,
    withAsyncMaterialExtractionErrorHandling
} from './index.js';

const logger = new Logger('ExtractMaterialMetadata');

/**
 * Extracts metadata specifically from Material resources.
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object for Material resources.
 */
export const extractMaterialMetadata = withAsyncMaterialExtractionErrorHandling(
    async function extractMaterialMetadataImpl(
        key: AppResourceKey,
        buffer: Buffer,
        resourceId: number,
        databaseService: DatabaseService
    ): Promise<Partial<ResourceMetadata>> {
        const extractedMetadata: Partial<ResourceMetadata> = {};
        let contentSnippet: string | undefined = undefined;
        const tgiDependencies: DependencyInfo[] = [];

        // Validate inputs
        if (!key) {
            logger.error('Invalid resource key: key is undefined');
            return { contentSnippet: '[Material Invalid Key]' };
        }

        if (!buffer) {
            logger.error('Invalid buffer: buffer is undefined');
            return { contentSnippet: '[Material Invalid Buffer]' };
        }

        if (!resourceId && resourceId !== 0) {
            logger.warn('Invalid resourceId: using default value 1');
            resourceId = 1;
        }

        if (!databaseService) {
            logger.warn('Invalid databaseService: using mock implementation');
            databaseService = {
                saveParsedContent: async () => {},
                saveDependencies: async () => {},
                resources: {
                    saveResource: async () => 1
                }
            } as any;
        }

        // Material resources are binary, so we need to identify the format and parse the header
        if (buffer.length < 16) {
            logger.warn(`Material buffer too small for ${key.instance.toString(16)}`);
            return { contentSnippet: '[Material Buffer Too Small]' };
        }

        // Initialize header with default format
        let header: MaterialHeaderInfo = {
            format: 'Unknown',
            version: 0,
            materialCount: 0,
            flags: 0
        };

        // Check for known material format signatures
        if (buffer.length >= 4) {
            const signature = buffer.slice(0, 4).toString();
            let parseResult: { header: MaterialHeaderInfo, dependencies: DependencyInfo[] };

            if (signature === MTST_SIGNATURE) {
                // MTST format (Material Set)
                parseResult = parseMtst(buffer, resourceId);
                header = parseResult.header;
                tgiDependencies.push(...parseResult.dependencies);
                logger.debug(`Successfully parsed MTST resource ${resourceId}`);
            } else if (signature === MTRL_SIGNATURE) {
                // MTRL format (Material)
                parseResult = parseMtrl(buffer, resourceId);
                header = parseResult.header;
                tgiDependencies.push(...parseResult.dependencies);
                logger.debug(`Successfully parsed MTRL resource ${resourceId}`);
            } else if (signature === MTDT_SIGNATURE) {
                // MTDT format (Material Data)
                parseResult = parseMtdt(buffer, resourceId);
                header = parseResult.header;
                tgiDependencies.push(...parseResult.dependencies);
                logger.debug(`Successfully parsed MTDT resource ${resourceId}`);
            } else if (signature === TXMT_SIGNATURE) {
                // TXMT format (Texture Material)
                parseResult = parseTxmt(buffer, resourceId);
                header = parseResult.header;
                tgiDependencies.push(...parseResult.dependencies);
                logger.debug(`Successfully parsed TXMT resource ${resourceId}`);
            } else {
                // Unknown format - try to identify based on resource type and structure
                logger.debug(`Attempting to detect material format for resource ${resourceId} with type 0x${key.type?.toString(16) || 'unknown'}`);
                parseResult = detectUnknownFormat(buffer, resourceId, key.type || 0);
                header = parseResult.header;
                tgiDependencies.push(...parseResult.dependencies);
                logger.debug(`Detected material format: ${header.format}, found ${tgiDependencies.length} dependencies`);
            }
        }

        // Store extracted metadata
        extractedMetadata.materialFormat = header.format;
        extractedMetadata.materialVersion = header.version;
        extractedMetadata.materialFlags = header.flags;

        if (header.materialCount !== undefined) {
            extractedMetadata.materialCount = header.materialCount;
        }

        if (header.shaderType !== undefined) {
            extractedMetadata.materialShaderType = header.shaderType;
        }

        if (header.textureCount !== undefined) {
            extractedMetadata.materialTextureCount = header.textureCount;
        }

        if (header.parameterCount !== undefined) {
            extractedMetadata.materialParameterCount = header.parameterCount;
        }

        if (header.materialNames !== undefined && header.materialNames.length > 0) {
            extractedMetadata.materialNames = JSON.stringify(header.materialNames);
        }

        if (header.textureReferences !== undefined && header.textureReferences.length > 0) {
            extractedMetadata.materialTextureReferences = JSON.stringify(header.textureReferences);
        }

        if (header.parameters !== undefined && header.parameters.length > 0) {
            extractedMetadata.materialParameters = JSON.stringify(header.parameters);
        }

        // Create content snippet based on the material format
        contentSnippet = createMaterialContentSnippet(header, key.type || 0, tgiDependencies.length);

        // Save material data for deeper analysis
        try {
            if (typeof databaseService.saveParsedContent === 'function') {
                await databaseService.saveParsedContent({
                    resourceId: resourceId,
                    contentType: 'material_metadata',
                    content: JSON.stringify(header)
                });
                logger.debug(`Saved material metadata for resource ${resourceId}`);
            }
        } catch (dbError: any) {
            logger.error(`Failed to save material metadata for resource ${resourceId} to DB: ${dbError.message || dbError}`);
        }

        // Save any found dependencies
        if (tgiDependencies.length > 0) {
            try {
                if (typeof databaseService.saveDependencies === 'function') {
                    await databaseService.saveDependencies(tgiDependencies);
                    logger.debug(`Saved ${tgiDependencies.length} dependencies for material resource ${resourceId}`);
                }
            } catch (depError: any) {
                logger.error(`Failed to save dependencies for material resource ${resourceId}: ${depError.message || depError}`);
            }
        }

        // Return extracted metadata
        return {
            contentSnippet: contentSnippet,
            ...extractedMetadata,
        };
    },
    null, // We'll provide the key in each call
    0,    // We'll provide the resourceId in each call
    'MaterialExtractor'
);
