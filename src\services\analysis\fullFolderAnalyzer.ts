/**
 * Full Folder Analyzer - Comprehensive analysis of entire mod collections
 * Provides batch processing and conflict detection for large mod folders
 */

import { PackageAnalyzer } from './packageAnalyzer.js';
import { EnhancedConflictOrchestrator } from '../conflict/EnhancedConflictOrchestrator.js';
import { DatabaseService } from '../databaseService.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ScanR<PERSON>ult, ScannedFile } from '../../utils/scanning/folderScanner.js';
import { MemoryManager } from '../../utils/memory/memoryManager.js';
import { logger } from '../../utils/logger.js';
import { EventEmitter } from 'events';

export interface FullFolderAnalysisOptions {
    /** Batch size for processing packages (default: 50) */
    batchSize?: number;
    /** Maximum concurrent package analyses (default: 2) */
    maxConcurrentAnalyses?: number;
    /** Enable incremental analysis (skip unchanged files) */
    enableIncrementalAnalysis?: boolean;
    /** Memory limit in bytes (default: 2GB) */
    memoryLimit?: number;
    /** Include TS4Script analysis */
    includeTS4Scripts?: boolean;
    /** Enable conflict detection */
    enableConflictDetection?: boolean;
    /** Progress reporting interval in packages (default: 10) */
    progressReportInterval?: number;
    /** Use in-memory database for faster processing */
    useInMemoryDatabase?: boolean;
    /** Clear database before analysis */
    clearDatabaseBeforeAnalysis?: boolean;
}

export interface AnalysisProgress {
    /** Current phase of analysis */
    phase: 'scanning' | 'analyzing' | 'conflicts' | 'reporting' | 'complete';
    /** Current batch number */
    currentBatch: number;
    /** Total number of batches */
    totalBatches: number;
    /** Packages processed in current batch */
    packagesInBatch: number;
    /** Total packages processed */
    packagesProcessed: number;
    /** Total packages to process */
    totalPackages: number;
    /** Current package being processed */
    currentPackage?: string;
    /** Estimated time remaining in milliseconds */
    estimatedTimeRemaining?: number;
    /** Processing speed (packages per second) */
    processingSpeed?: number;
    /** Memory usage percentage */
    memoryUsage?: number;
}

export interface FullFolderAnalysisResult {
    /** Scan result from folder scanning */
    scanResult: ScanResult;
    /** Number of packages successfully analyzed */
    packagesAnalyzed: number;
    /** Number of packages that failed analysis */
    packagesFailed: number;
    /** Total analysis duration in milliseconds */
    analysisDuration: number;
    /** Number of conflicts detected */
    conflictsDetected: number;
    /** Analysis errors encountered */
    errors: string[];
    /** Performance metrics */
    performanceMetrics: {
        averagePackageProcessingTime: number;
        peakMemoryUsage: number;
        totalDatabaseOperations: number;
        conflictDetectionTime: number;
    };
    /** Detailed results by batch */
    batchResults: BatchResult[];
}

export interface BatchResult {
    batchNumber: number;
    packagesInBatch: number;
    packagesProcessed: number;
    packagesFailed: number;
    processingTime: number;
    memoryUsageAtStart: number;
    memoryUsageAtEnd: number;
    errors: string[];
}

/**
 * Full Folder Analyzer - Orchestrates comprehensive analysis of mod collections
 */
export class FullFolderAnalyzer extends EventEmitter {
    private packageAnalyzer: PackageAnalyzer;
    private conflictOrchestrator: EnhancedConflictOrchestrator;
    private databaseService: DatabaseService;
    private memoryManager: MemoryManager;
    private folderScanner: FolderScanner;
    private options: Required<FullFolderAnalysisOptions>;

    constructor(
        packageAnalyzer: PackageAnalyzer,
        conflictOrchestrator: EnhancedConflictOrchestrator,
        databaseService: DatabaseService,
        memoryManager: MemoryManager,
        options: FullFolderAnalysisOptions = {}
    ) {
        super();

        this.packageAnalyzer = packageAnalyzer;
        this.conflictOrchestrator = conflictOrchestrator;
        this.databaseService = databaseService;
        this.memoryManager = memoryManager;
        this.folderScanner = new FolderScanner();

        this.options = {
            batchSize: options.batchSize ?? 50,
            maxConcurrentAnalyses: options.maxConcurrentAnalyses ?? 2,
            enableIncrementalAnalysis: options.enableIncrementalAnalysis ?? true,
            memoryLimit: options.memoryLimit ?? 2 * 1024 * 1024 * 1024, // 2GB
            includeTS4Scripts: options.includeTS4Scripts ?? false,
            enableConflictDetection: options.enableConflictDetection ?? true,
            progressReportInterval: options.progressReportInterval ?? 10,
            useInMemoryDatabase: options.useInMemoryDatabase ?? false,
            clearDatabaseBeforeAnalysis: options.clearDatabaseBeforeAnalysis ?? false
        };
    }

    /**
     * Analyze an entire mods folder
     * @param folderPath Path to the mods folder
     * @returns Analysis result
     */
    async analyzeFolder(folderPath: string): Promise<FullFolderAnalysisResult> {
        const startTime = Date.now();
        logger.info(`Starting full folder analysis: ${folderPath}`);

        try {
            // Initialize database if needed
            if (this.options.clearDatabaseBeforeAnalysis) {
                await this.databaseService.clearDatabase();
            }

            // Phase 1: Scan folder
            this.emitProgress({ phase: 'scanning', currentBatch: 0, totalBatches: 0, packagesInBatch: 0, packagesProcessed: 0, totalPackages: 0 });
            const scanResult = await this.folderScanner.scanFolder(folderPath);
            
            if (scanResult.files.length === 0) {
                throw new Error(`No mod files found in folder: ${folderPath}`);
            }

            // Filter for package files
            const packageFiles = scanResult.files.filter(file => file.extension === '.package');
            const totalPackages = packageFiles.length;
            const totalBatches = Math.ceil(totalPackages / this.options.batchSize);

            logger.info(`Found ${totalPackages} package files to analyze in ${totalBatches} batches`);

            // Phase 2: Analyze packages in batches
            this.emitProgress({ 
                phase: 'analyzing', 
                currentBatch: 0, 
                totalBatches, 
                packagesInBatch: 0, 
                packagesProcessed: 0, 
                totalPackages 
            });

            const batchResults: BatchResult[] = [];
            let packagesAnalyzed = 0;
            let packagesFailed = 0;
            const errors: string[] = [];

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const batchStartTime = Date.now();
                const batchStart = batchIndex * this.options.batchSize;
                const batchEnd = Math.min(batchStart + this.options.batchSize, totalPackages);
                const batchFiles = packageFiles.slice(batchStart, batchEnd);

                logger.info(`Processing batch ${batchIndex + 1}/${totalBatches} (${batchFiles.length} packages)`);

                const batchResult = await this.processBatch(
                    batchFiles,
                    batchIndex + 1,
                    totalBatches,
                    packagesAnalyzed
                );

                batchResults.push(batchResult);
                packagesAnalyzed += batchResult.packagesProcessed;
                packagesFailed += batchResult.packagesFailed;
                errors.push(...batchResult.errors);

                // Memory management
                await this.memoryManager.performCleanup();
                
                // Check memory pressure
                const memoryUsage = process.memoryUsage();
                if (memoryUsage.heapUsed > this.options.memoryLimit * 0.8) {
                    logger.warn(`High memory usage detected: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`);
                    await this.memoryManager.performAggressiveCleanup();
                }
            }

            // Phase 3: Conflict detection
            let conflictsDetected = 0;
            let conflictDetectionTime = 0;

            if (this.options.enableConflictDetection && packagesAnalyzed > 1) {
                this.emitProgress({ 
                    phase: 'conflicts', 
                    currentBatch: totalBatches, 
                    totalBatches, 
                    packagesInBatch: 0, 
                    packagesProcessed: packagesAnalyzed, 
                    totalPackages 
                });

                const conflictStartTime = Date.now();
                logger.info('Starting comprehensive conflict detection...');

                try {
                    const conflictResult = await this.conflictOrchestrator.detectConflicts();
                    conflictsDetected = conflictResult.conflicts.length;
                    conflictDetectionTime = Date.now() - conflictStartTime;
                    
                    logger.info(`Conflict detection completed: ${conflictsDetected} conflicts found in ${conflictDetectionTime}ms`);
                } catch (error: any) {
                    logger.error(`Conflict detection failed: ${error.message}`);
                    errors.push(`Conflict detection error: ${error.message}`);
                }
            }

            // Phase 4: Complete
            const analysisDuration = Date.now() - startTime;
            this.emitProgress({ 
                phase: 'complete', 
                currentBatch: totalBatches, 
                totalBatches, 
                packagesInBatch: 0, 
                packagesProcessed: packagesAnalyzed, 
                totalPackages 
            });

            const result: FullFolderAnalysisResult = {
                scanResult,
                packagesAnalyzed,
                packagesFailed,
                analysisDuration,
                conflictsDetected,
                errors,
                performanceMetrics: {
                    averagePackageProcessingTime: packagesAnalyzed > 0 ? analysisDuration / packagesAnalyzed : 0,
                    peakMemoryUsage: Math.max(...batchResults.map(b => Math.max(b.memoryUsageAtStart, b.memoryUsageAtEnd))),
                    totalDatabaseOperations: packagesAnalyzed, // Simplified metric
                    conflictDetectionTime
                },
                batchResults
            };

            logger.info(`Full folder analysis completed: ${packagesAnalyzed}/${totalPackages} packages analyzed, ${conflictsDetected} conflicts detected`);
            return result;

        } catch (error: any) {
            logger.error(`Full folder analysis failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process a batch of packages
     */
    private async processBatch(
        batchFiles: ScannedFile[],
        batchNumber: number,
        totalBatches: number,
        packagesProcessedSoFar: number
    ): Promise<BatchResult> {
        const batchStartTime = Date.now();
        const memoryUsageAtStart = process.memoryUsage().heapUsed;
        
        let packagesProcessed = 0;
        let packagesFailed = 0;
        const errors: string[] = [];

        for (let i = 0; i < batchFiles.length; i++) {
            const file = batchFiles[i];
            
            try {
                // Update progress
                if ((packagesProcessedSoFar + i) % this.options.progressReportInterval === 0) {
                    this.emitProgress({
                        phase: 'analyzing',
                        currentBatch: batchNumber,
                        totalBatches,
                        packagesInBatch: i + 1,
                        packagesProcessed: packagesProcessedSoFar + i,
                        totalPackages: packagesProcessedSoFar + batchFiles.length,
                        currentPackage: file.name
                    });
                }

                // Check if file should be skipped (incremental analysis)
                if (this.options.enableIncrementalAnalysis) {
                    const existingPackage = await this.databaseService.getPackageByPath(file.path);
                    if (existingPackage && existingPackage.lastModified >= file.lastModified) {
                        logger.debug(`Skipping unchanged package: ${file.name}`);
                        packagesProcessed++;
                        continue;
                    }
                }

                // Analyze package
                logger.debug(`Analyzing package: ${file.name}`);
                await this.packageAnalyzer.analyzePackage(file.path, {
                    initializeIfNeeded: true,
                    cleanupBuffers: true,
                    batchSize: 20,
                    maxConcurrentResources: 1
                });

                packagesProcessed++;

            } catch (error: any) {
                logger.error(`Failed to analyze package ${file.name}: ${error.message}`);
                errors.push(`${file.name}: ${error.message}`);
                packagesFailed++;
            }
        }

        const processingTime = Date.now() - batchStartTime;
        const memoryUsageAtEnd = process.memoryUsage().heapUsed;

        return {
            batchNumber,
            packagesInBatch: batchFiles.length,
            packagesProcessed,
            packagesFailed,
            processingTime,
            memoryUsageAtStart,
            memoryUsageAtEnd,
            errors
        };
    }

    /**
     * Emit progress update
     */
    private emitProgress(progress: AnalysisProgress): void {
        // Calculate additional metrics
        if (progress.packagesProcessed > 0 && progress.phase === 'analyzing') {
            const elapsed = Date.now() - (this as any).startTime || Date.now();
            progress.processingSpeed = progress.packagesProcessed / (elapsed / 1000);
            
            if (progress.processingSpeed > 0) {
                const remaining = progress.totalPackages - progress.packagesProcessed;
                progress.estimatedTimeRemaining = (remaining / progress.processingSpeed) * 1000;
            }
        }

        progress.memoryUsage = (process.memoryUsage().heapUsed / this.options.memoryLimit) * 100;

        this.emit('progress', progress);
        
        // Log progress
        if (progress.phase === 'analyzing' && progress.currentPackage) {
            logger.info(`[${progress.currentBatch}/${progress.totalBatches}] Processing: ${progress.currentPackage} (${progress.packagesProcessed}/${progress.totalPackages})`);
        }
    }
}
