/**
 * Package Analyzer
 *
 * Main package analyzer implementation with memory-efficient processing,
 * streaming capabilities, and consolidated resource handling.
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { Resource as ResourceInterface } from '../../types/resource/interfaces.js';
import { SemanticAnalysisService } from './semanticAnalysisService.js';
import { ResourceProcessor } from './package/resourceProcessor.js';
import { PackageLoader } from './package/packageLoader.js';
import { ResourceMetadataExtractor } from './package/resourceMetadataExtractor.js';
import { PackageIndexEntry } from './stream/index.js';
import { ConsolidatedResourceProvider } from './stream/consolidatedResourceProvider.js';
import { ConsolidatedStreamPipeline } from './stream/consolidatedStreamPipeline.js';
import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { EnhancedBufferPool } from '../../utils/memory/enhancedBufferPool.js';
import { formatBytes, formatDuration } from '../../utils/formatting/formatUtils.js';
import { ResourceTracker } from '../../utils/memory/resourceTracker.js';
import { AdaptiveProcessingManager, WorkloadType } from './adaptive/AdaptiveProcessingManager.js';
import { promises as fs } from 'fs';
import * as path from 'path';

import { EnhancedDependencyPipelineAdapter } from './stream/enhancedDependencyPipelineAdapter.js';
import { EnhancedDependencyChainAnalyzer } from './semantic/enhancedDependencyChainAnalyzer.js';
import { GameplaySystemAnalyzer } from './gameplay/GameplaySystemAnalyzer.js';
import { Readable } from 'stream';
import { ProcessingParameters } from './adaptive/AdaptiveProcessingManager.js';
import { StreamingPackageReader } from './stream/compatibility/streamingPackageReaderCompat.js';

// Create a logger for this module
const logger = new Logger('PackageAnalyzer');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get buffer pool instance
const bufferPool = EnhancedBufferPool.getInstance();

// Get resource tracker instance
const resourceTracker = ResourceTracker.getInstance();

// Get adaptive processing manager instance
const adaptiveProcessingManager = AdaptiveProcessingManager.getInstance();

/**
 * Package analyzer options
 */
export interface PackageAnalyzerOptions {
    initializeIfNeeded?: boolean;
    cleanupBuffers?: boolean;
    batchSize?: number;
    maxConcurrentResources?: number;
    directBufferThreshold?: number;
    chunkedProcessingThreshold?: number;
    chunkSize?: number;
    bufferPoolSize?: number;
    maxBufferSize?: number;
    analyzeDependencies?: boolean;
    maxDependencyDepth?: number;
    analyzeCrossPackageDependencies?: boolean;
    includeGameplayAnalysis?: boolean;
    includeVisualizationMetadata?: boolean;
    trackPerformance?: boolean;
    logResourceApproach?: boolean;
    enableHardwareAwareness?: boolean;
    workloadIntensity?: 'low' | 'medium' | 'high';
}

/**
 * Package analysis result
 */
export interface PackageAnalysisResult {
    id: number;
    name: string;
    path: string;
    hash: string;
    size: number;
    lastModified: number;
    resources: ResourceInterface[];

    // Enhanced analysis results
    enhancedAnalysis?: {
        // Dependency analysis
        dependencies: {
            totalDependencies: number;
            crossPackageDependencies: number;
            impactedGameplaySystems: string[];
            highestImpactScore: number;
        };

        // Performance metrics
        performance: {
            totalTime: number;
            readTime: number;
            processingTime: number;
            dependencyAnalysisTime: number;
            streamingUtilization: number;
            memoryEfficiency: number;
        };
    };
}

/**
 * Package analyzer class
 */
export class PackageAnalyzer {
    private databaseService: DatabaseService;
    private semanticAnalysisService: SemanticAnalysisService;
    private resourceProcessor: ResourceProcessor;
    private packageLoader: PackageLoader;
    private resourceMetadataExtractor: ResourceMetadataExtractor;
    private isInitialized: boolean = false;
    private logger: Logger;

    private performanceMetrics: {
        readTime: number;
        processingTime: number;
        dependencyAnalysisTime: number;
        totalTime: number;
        resourceCount: number;
        totalResourceSize: number;
        dependencyCount: number;
        crossPackageDependencyCount: number;
        adaptiveAdjustments: number;
        streamingUtilization: number;
        memoryEfficiency: number;
        smallestResourceSize: number;
        largestResourceSize: number;
    } = {
        readTime: 0,
        processingTime: 0,
        dependencyAnalysisTime: 0,
        totalTime: 0,
        resourceCount: 0,
        totalResourceSize: 0,
        dependencyCount: 0,
        crossPackageDependencyCount: 0,
        adaptiveAdjustments: 0,
        streamingUtilization: 0,
        memoryEfficiency: 0,
        smallestResourceSize: Number.MAX_SAFE_INTEGER,
        largestResourceSize: 0,
    };

    private resourceProvider: ConsolidatedResourceProvider;
    private streamPipeline: ConsolidatedStreamPipeline;
    private dependencyPipelineAdapter: EnhancedDependencyPipelineAdapter;
    private dependencyChainAnalyzer: EnhancedDependencyChainAnalyzer;
    private gameplaySystemAnalyzer: GameplaySystemAnalyzer;
    private isDisposed: boolean = false;

    private async streamToBuffer(stream: Readable): Promise<Buffer> {
        return new Promise((resolve, reject) => {
            const chunks: Buffer[] = [];
            stream.on('data', (chunk) => chunks.push(chunk));
            stream.on('error', reject);
            stream.on('end', () => resolve(Buffer.concat(chunks)));
        });
    }

    private resetPerformanceMetrics(): void {
        this.performanceMetrics = {
            readTime: 0,
            processingTime: 0,
            dependencyAnalysisTime: 0,
            totalTime: 0,
            resourceCount: 0,
            totalResourceSize: 0,
            dependencyCount: 0,
            crossPackageDependencyCount: 0,
            adaptiveAdjustments: 0,
            streamingUtilization: 0,
            memoryEfficiency: 0,
            smallestResourceSize: Number.MAX_SAFE_INTEGER,
            largestResourceSize: 0,
        };
    }

    private calculateStreamingUtilization(): number {
        return 0; // Placeholder
    }

    private calculateMemoryEfficiency(): number {
        return 0; // Placeholder
    }

    private async getHighestImpactScore(packageId: number): Promise<number> {
        try {
            const summaries = await this.databaseService.executeQuery(
                `SELECT m.value FROM metadata m
                 JOIN resources r ON m.resourceId = r.id
                 WHERE r.packageId = ? AND m.key = 'dependencyChainSummary'
                 ORDER BY JSON_EXTRACT(m.value, '$.impactScore') DESC
                 LIMIT 1`,
                [packageId]
            );

            if (summaries && summaries.length > 0 && summaries[0].value) {
                try {
                    const summary = JSON.parse(summaries[0].value);
                    return summary.impactScore || 0;
                } catch (parseError: any) {
                    this.logger.error(`Error parsing dependency chain summary: ${parseError.message}`);
                }
            }

            return 0;
        } catch (error: any) {
            this.logger.error(`Error getting highest impact score: ${error.message}`);
            return 0;
        }
    }

    private logPerformanceMetrics(): void {
        this.logger.info(`\n===== PACKAGE ANALYZER PERFORMANCE REPORT =====`);
        this.logger.info(`Total resources processed: ${this.performanceMetrics.resourceCount}`);
        this.logger.info(`Total analysis time: ${formatDuration(this.performanceMetrics.totalTime)}`);

        this.logger.info(`\nTime breakdown:`);
        this.logger.info(`- Read time: ${formatDuration(this.performanceMetrics.readTime)} (${this.getPercentage(this.performanceMetrics.readTime, this.performanceMetrics.totalTime)}%)`);
        this.logger.info(`- Processing time: ${formatDuration(this.performanceMetrics.processingTime)} (${this.getPercentage(this.performanceMetrics.processingTime, this.performanceMetrics.totalTime)}%)`);
        this.logger.info(`- Dependency analysis time: ${formatDuration(this.performanceMetrics.dependencyAnalysisTime)} (${this.getPercentage(this.performanceMetrics.dependencyAnalysisTime, this.performanceMetrics.totalTime)}%)`);

        this.logger.info(`\nResource metrics:`);
        this.logger.info(`- Total resource size: ${formatBytes(this.performanceMetrics.totalResourceSize)}`);
        const averageResourceSize = this.performanceMetrics.resourceCount > 0 ? this.performanceMetrics.totalResourceSize / this.performanceMetrics.resourceCount : 0;
        this.logger.info(`- Average resource size: ${formatBytes(averageResourceSize)}`);

        this.logger.info(`\nEfficiency metrics:`);
        this.logger.info(`- Streaming utilization: ${this.performanceMetrics.streamingUtilization.toFixed(1)}%`);
        this.logger.info(`- Memory efficiency: ${this.performanceMetrics.memoryEfficiency.toFixed(1)}%`);
        this.logger.info(`- Adaptive adjustments: ${this.performanceMetrics.adaptiveAdjustments}`);
        this.logger.info(`===========================================================\n`);
    }

    private getPercentage(value: number, total: number): string {
        if (total === 0) {
            return '0';
        }
        return (value / total * 100).toFixed(1);
    }

    /**
     * Create a new package analyzer
     * @param databaseService Database service
     * @param semanticAnalysisService Semantic analysis service
     * @param resourceProcessor Resource processor
     * @param packageLoader Package loader
     * @param resourceMetadataExtractor Resource metadata extractor
     */
    constructor(
        databaseService: DatabaseService,
        semanticAnalysisService: SemanticAnalysisService,
        resourceProcessor: ResourceProcessor,
        packageLoader: PackageLoader,
        resourceMetadataExtractor: ResourceMetadataExtractor,
        dependencyChainAnalyzer: EnhancedDependencyChainAnalyzer,
        gameplaySystemAnalyzer: GameplaySystemAnalyzer,
        resourceProvider: ConsolidatedResourceProvider,
        streamPipeline: ConsolidatedStreamPipeline,
        dependencyPipelineAdapter: EnhancedDependencyPipelineAdapter
    ) {
        this.databaseService = databaseService;
        this.semanticAnalysisService = semanticAnalysisService;
        this.resourceProcessor = resourceProcessor;
        this.packageLoader = packageLoader;
        this.resourceMetadataExtractor = resourceMetadataExtractor;
        this.dependencyChainAnalyzer = dependencyChainAnalyzer;
        this.gameplaySystemAnalyzer = gameplaySystemAnalyzer;
        this.resourceProvider = resourceProvider;
        this.streamPipeline = streamPipeline;
        this.dependencyPipelineAdapter = dependencyPipelineAdapter;
        this.logger = logger;
    }

    /**
     * Initialize the analyzer
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        // Initialize dependencies that have initialize methods
            await this.databaseService.initialize();

        // Only call initialize on services that have the method
        await this.dependencyChainAnalyzer.initialize();
        await this.gameplaySystemAnalyzer.initialize();
        await this.dependencyPipelineAdapter.initialize();

        // Initialize adaptive processing manager
        await adaptiveProcessingManager.initialize();

        this.isInitialized = true;
        this.logger.info('Package analyzer initialized');
    }

    /**
     * Analyze a package file using consolidated components
     * @param filePath Path to the package file
     * @param options Optional analysis options
     * @returns Analysis result
     */
    public async analyzePackage(
        filePath: string,
        options: PackageAnalyzerOptions = {}
    ): Promise<PackageAnalysisResult> {
        // Set default options
        const {
            initializeIfNeeded = true,
            cleanupBuffers = true,
            batchSize = 20,
            maxConcurrentResources = 1,

            // Resource reading options
            directBufferThreshold = 5 * 1024 * 1024, // 5MB default
            chunkedProcessingThreshold = 50 * 1024 * 1024, // 50MB default
            chunkSize = 64 * 1024, // 64KB default chunk size
            bufferPoolSize = 5, // Default buffer pool size
            maxBufferSize = 1024 * 1024, // 1MB default max buffer size

            // Dependency analysis options
            analyzeDependencies = true,
            maxDependencyDepth = 3,
            analyzeCrossPackageDependencies = true,
            includeGameplayAnalysis = true,
            includeVisualizationMetadata = true,

            // Performance tracking options
            trackPerformance = true,
            logResourceApproach = true,

            // Hardware adaptation options
            enableHardwareAwareness = true,
            workloadIntensity = 'medium'
        } = options;

        // Start performance tracking
        const analysisStartTime = Date.now();

        // Reset performance metrics
        if (trackPerformance) {
            this.resetPerformanceMetrics();
        }

        // Check if analyzer is initialized
        if (!this.isInitialized) {
            if (initializeIfNeeded) {
                await this.initialize();
            } else {
                throw new Error('PackageAnalyzer is not initialized. Call initialize() first or set initializeIfNeeded to true.');
            }
        }

        // Check if analyzer is disposed
        if (this.isDisposed) {
            throw new Error('PackageAnalyzer has been disposed and cannot be used.');
        }

        this.logger.info(`Analyzing package: ${filePath}`);

        // Track resources that need cleanup
        const resources: ResourceInterface[] = [];
        let processedResourceCount = 0;
        const buffersToCleanup: Buffer[] = [];
        let packageId: number = 0; // Initialize to 0 to avoid "used before assigned" error
        let packageInfo: any;
        let memoryUsage = { start: process.memoryUsage().heapUsed };
        let errors: string[] = [];

        // Set workload type for adaptive processing
        const workloadType = workloadIntensity === 'low' ? WorkloadType.IO_BOUND :
                           workloadIntensity === 'high' ? WorkloadType.CPU_BOUND : WorkloadType.BALANCED;

        try {
            // Load the package file and save initial info
            const loadedPackage = await this.packageLoader.loadPackage(filePath);
            packageId = loadedPackage.packageId;
            packageInfo = loadedPackage.packageInfo;

            // Get current memory pressure to adjust thresholds
            const initialMemoryPressure = memoryManager.getMemoryPressure();

            // Get hardware-aware parameters if enabled
            let adjustedDirectBufferThreshold = directBufferThreshold;
            let adjustedChunkedProcessingThreshold = chunkedProcessingThreshold;
            let adjustedBatchSize = batchSize;

            if (enableHardwareAwareness) {
                // Get hardware-aware parameters
                const adaptiveParams = adaptiveProcessingManager.getProcessingParameters(workloadType); // Corrected method name

                // Apply adaptive parameters
                adjustedDirectBufferThreshold = adaptiveParams.directBufferThreshold || directBufferThreshold;
                adjustedChunkedProcessingThreshold = adaptiveParams.chunkedProcessingThreshold || chunkedProcessingThreshold;
                adjustedBatchSize = adaptiveParams.batchSize || batchSize;

                this.logger.info(`Using hardware-aware parameters: directBuffer=${formatBytes(adjustedDirectBufferThreshold)}, ` +
                                `chunkedProcessing=${formatBytes(adjustedChunkedProcessingThreshold)}, ` +
                                `batchSize=${adjustedBatchSize}`);
            } else {
                // Adjust thresholds based on initial memory pressure
                if (initialMemoryPressure > 0.8) {
                    // High memory pressure - use more conservative thresholds
                    adjustedDirectBufferThreshold = Math.floor(directBufferThreshold * 0.5);
                    adjustedChunkedProcessingThreshold = Math.floor(chunkedProcessingThreshold * 0.5);
                    adjustedBatchSize = Math.floor(batchSize * 0.5);
                    this.logger.warn(`High memory pressure (${(initialMemoryPressure * 100).toFixed(1)}%), reducing thresholds`);
                } else if (initialMemoryPressure < 0.3) {
                    // Low memory pressure - can use more aggressive thresholds
                    adjustedDirectBufferThreshold = Math.floor(directBufferThreshold * 1.5);
                    adjustedChunkedProcessingThreshold = Math.floor(chunkedProcessingThreshold * 1.5);
                    adjustedBatchSize = Math.floor(batchSize * 1.5);
                    this.logger.info(`Low memory pressure (${(initialMemoryPressure * 100).toFixed(1)}%), increasing thresholds`);
                }
            }

            // Initialize the consolidated resource provider with hybrid approach settings
            // The provider is already a class property, so configure it instead of creating a new one
            // This requires adding a configure method to ConsolidatedResourceProvider or passing options in constructor
            // Assuming options are passed in constructor for now, as done in the original ConsolidatedPackageAnalyzer

            // Get resource entries from the consolidated resource provider
            // ConsolidatedResourceProvider does not have getResourceEntries or an index property like the old reader.
            // It provides streams for individual entries.
            // We need to get the index entries first, likely by reading the package header/index directly here
            // or adding a method to ConsolidatedResourceProvider to get the index.
            // Based on the original ConsolidatedPackageAnalyzer structure, it doesn't get entries this way.
            // It seems the original ConsolidatedPackageAnalyzer was incomplete.
            // Let's revert to the structure of packageAnalyzer-streaming but use the consolidated components.

            // Re-implementing the core loop based on packageAnalyzer-streaming, using consolidated components

            // Get file stats for size information
            const fileStats = await fs.stat(filePath);
            const fileSize = fileStats.size;
            this.logger.debug(`Package file size: ${fileSize} bytes`);

            // Need to get package index entries. ConsolidatedResourceProvider doesn't provide this.
            // This logic needs to be added to ConsolidatedPackageAnalyzer or ConsolidatedResourceProvider.
            // For now, let's assume we can get entries somehow. This is a gap in the existing consolidated structure.
            // Let's temporarily use the old StreamingPackageReader (compat layer) just to get the entries,
            // but this is not ideal for consolidation. A better approach is needed for index reading.

            // TEMPORARY WORKAROUND: Use StreamingPackageReader compat layer to get entries
            const tempPackageReaderForIndex = new StreamingPackageReader(filePath);
            await tempPackageReaderForIndex.initialize();
            const entries = tempPackageReaderForIndex.index.entries;
            await tempPackageReaderForIndex.close(); // Clean up temporary reader
            // END TEMPORARY WORKAROUND

            this.logger.debug(`Found ${entries.length} valid entries in package: ${packageInfo.name}`);

            // Process entries in batches to limit memory usage
            const totalEntries = entries.length;
            let currentBatchSize = adjustedBatchSize;
            let processedEntries = 0;
            let batchIndex = 0;
            const totalBatches = Math.ceil(totalEntries / currentBatchSize);

            this.logger.info(`Processing ${totalEntries} resources with initial batch size of ${currentBatchSize}`);

            // Process entries in batches
            while (processedEntries < totalEntries) {
                // Check memory pressure before processing batch
                const memoryPressure = memoryManager.getMemoryPressure();

                // Adjust batch size based on memory pressure
                const memoryUsage = process.memoryUsage();
                const isConstrainedHeap = memoryUsage.heapTotal < 500 * 1024 * 1024; // 500MB
                const extremePressureThreshold = isConstrainedHeap ? 0.99 : 0.9; // 99% for constrained, 90% for normal

                let batchSizeAdjusted = false;
                if (memoryPressure > extremePressureThreshold) {
                    // Extreme memory pressure, reduce to minimum
                    const oldBatchSize = currentBatchSize;
                    currentBatchSize = 1;
                    batchSizeAdjusted = true;
                    this.logger.warn(`EXTREME memory pressure (${(memoryPressure * 100).toFixed(1)}%), reducing batch size to absolute minimum: ${currentBatchSize}`);

                    if (trackPerformance) {
                        this.performanceMetrics.adaptiveAdjustments++;
                    }
                } else if (memoryPressure > (isConstrainedHeap ? 0.97 : 0.8)) {
                    // Very high memory pressure, reduce batch size significantly
                    const oldBatchSize = currentBatchSize;
                    currentBatchSize = Math.max(2, Math.floor(batchSize * 0.3));
                    batchSizeAdjusted = currentBatchSize !== oldBatchSize;
                    if (batchSizeAdjusted) {
                        this.logger.warn(`Very high memory pressure (${(memoryPressure * 100).toFixed(1)}%), reducing batch size to ${currentBatchSize}`);

                        if (trackPerformance) {
                            this.performanceMetrics.adaptiveAdjustments++;
                        }
                    }
                } else if (memoryPressure > (isConstrainedHeap ? 0.95 : 0.7)) {
                    // High memory pressure, reduce batch size moderately
                    const oldBatchSize = currentBatchSize;
                    currentBatchSize = Math.max(5, Math.floor(batchSize * 0.5));
                    batchSizeAdjusted = currentBatchSize !== oldBatchSize;
                    if (batchSizeAdjusted) {
                        this.logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%), reducing batch size to ${currentBatchSize}`);

                        if (trackPerformance) {
                            this.performanceMetrics.adaptiveAdjustments++;
                        }
                    }
                } else if (memoryPressure < 0.5 && currentBatchSize < batchSize) {
                    // Low memory pressure, increase batch size (but don't exceed original)
                    const oldBatchSize = currentBatchSize;
                    currentBatchSize = Math.min(batchSize, Math.floor(currentBatchSize * 1.5));
                    batchSizeAdjusted = currentBatchSize !== oldBatchSize;
                    if (batchSizeAdjusted) {
                        this.logger.info(`Low memory pressure (${(memoryPressure * 100).toFixed(1)}%), increasing batch size to ${currentBatchSize}`);

                        if (trackPerformance) {
                            this.performanceMetrics.adaptiveAdjustments++;
                        }
                    }
                }

                // Calculate batch range
                const batchStart = processedEntries;
                const batchEnd = Math.min(batchStart + currentBatchSize, totalEntries);
                const batchEntries = entries.slice(batchStart, batchEnd);

                this.logger.debug(`Processing batch ${batchIndex + 1} (entries ${batchStart + 1}-${batchEnd}, size: ${batchEntries.length})`);

                // Process each entry in the current batch
                for (let resourceIndex = 0; resourceIndex < batchEntries.length; resourceIndex++) {
                    const entry = batchEntries[resourceIndex];
                    try {
                        // Determine resource size for metrics
                        const resourceSize = entry.compressed ? entry.memSize : entry.fileSize;

                        // Update resource size metrics
                        if (trackPerformance) {
                            this.performanceMetrics.totalResourceSize += resourceSize;
                        }

                        // The consolidated resource provider handles the hybrid approach internally.
                        // Logging of specific approach is simplified.
                        if (logResourceApproach) {
                             this.logger.debug(`Processing resource ${entry.type.toString(16)}:${entry.group}:${entry.instanceHi}:${entry.instanceLo} (${formatBytes(resourceSize)}) using consolidated resource provider.`);
                        }

                        // Measure read time if tracking performance
                        const readStartTime = trackPerformance ? Date.now() : 0;

                        // Read the resource using the consolidated resource provider
                        const resourceStream = await this.resourceProvider.createResourceStream(filePath, entry); // Use class property
                        const resourceBuffer = await this.streamToBuffer(resourceStream);

                        // Update performance metrics for read time
                        if (trackPerformance && readStartTime > 0) {
                            const readTime = Date.now() - readStartTime;
                            this.performanceMetrics.readTime += readTime;
                            // Granular approach-specific metrics are not applicable here
                        }

                        // Measure processing time if tracking performance
                        const processingStartTime = trackPerformance ? Date.now() : 0;

                        // Create a minimal object that mimics S4TK ResourceEntry for processEntry
                        const minimalResourceEntryForProcessor = {
                            key: {
                                type: entry.type,
                                group: entry.group,
                                instance: BigInt(entry.instanceHi) << BigInt(32) | BigInt(entry.instanceLo) // Use BigInt() constructor for compatibility
                            },
                            offset: entry.offset,
                            size: entry.fileSize,
                            compressed: entry.compressed,
                            value: {
                                buffer: resourceBuffer,
                                toBuffer: () => resourceBuffer,
                                compressed: entry.compressed
                            }
                        };

                        // Process the resource entry
                        const processedResult = await this.resourceProcessor.processEntry(
                            minimalResourceEntryForProcessor,
                            packageId,
                            filePath
                        );

                        // Update processing time metrics
                        if (trackPerformance && processingStartTime > 0) {
                            const processingTime = Date.now() - processingStartTime;
                            this.performanceMetrics.processingTime += processingTime;
                        }

                        if (processedResult) {
                            // Resource was processed successfully
                            const resourceId = processedResult.resourceId;
                            const resourceInfo = processedResult.resourceInfo;

                            // If dependency analysis is enabled, use the dependency pipeline adapter
                            if (analyzeDependencies) {
                                try {
                                    // Create a readable stream from the buffer
                                    const resourceStreamForDependency = new Readable();
                                    resourceStreamForDependency.push(processedResult.resourceBuffer);
                                    resourceStreamForDependency.push(null); // End the stream

                                    // Measure dependency analysis time if tracking performance
                                    const dependencyStartTime = trackPerformance ? Date.now() : 0;

                                    // Process through dependency pipeline
                                    await this.dependencyPipelineAdapter.createPipeline( // Use class property
                                        resourceId,
                                        resourceInfo.key.type,
                                        resourceStreamForDependency,
                                        {
                                            maxDependencyDepth,
                                            includeGameplayAnalysis,
                                            includeVisualizationMetadata,
                                            analyzeCrossPackageDependencies,
                                            packageId: packageId
                                        }
                                    );

                                    // Update dependency analysis time metrics
                                    if (trackPerformance && dependencyStartTime > 0) {
                                        const dependencyTime = Date.now() - dependencyStartTime;
                                        this.performanceMetrics.dependencyAnalysisTime += dependencyTime;
                                    }
                                } catch (dependencyError) {
                                    this.logger.error(`Error in dependency analysis for resource ${resourceId}:`, dependencyError);
                                    // Continue despite dependency analysis errors
                                }
                            }

                            // Add to cleanup list if requested
                            if (cleanupBuffers) {
                                // Buffer release is handled by the consolidated provider internally
                                // No explicit releaseBuffer call needed here
                                buffersToCleanup.push(processedResult.resourceBuffer); // Still track for potential manual cleanup if needed
                            }

                            // Extract metadata based on resource type
                            const metadata = await this.resourceMetadataExtractor.extractMetadata(
                                processedResult.resourceInfo.key,
                                processedResult.resourceBuffer,
                                processedResult.resourceId
                            );

                            // Create the final Resource object
                            const resource: ResourceInterface = {
                                key: processedResult.resourceInfo.key,
                                metadata: {
                                    ...processedResult.resourceInfo.metadata,
                                    ...metadata,
                                    extractorUsed: metadata.extractorUsed || processedResult.resourceInfo.metadata.extractorUsed || 'generic'
                                },
                                timestamp: Date.now()
                            };

                            resources.push(resource);
                            this.logger.debug(`Analyzed resource ${resource.key.type.toString(16)}:${resource.key.group.toString(16)}:${resource.key.instance.toString(16)} (${resource.metadata.resourceType})`);
                            processedResourceCount++;

                            // Update resource count in performance metrics
                            if (trackPerformance) {
                                this.performanceMetrics.resourceCount++;
                            }
                        }
                    } catch (processingError: any) {
                        const keyStr = `${entry.type.toString(16)}:${entry.group.toString(16)}:${entry.instanceHi.toString(16)}:${entry.instanceLo.toString(16)}`;
                        const resourceSize = entry.compressed ? entry.memSize : entry.fileSize;

                        // Enhanced error handling with comprehensive context
                        this.logger.error(`Error processing resource ${keyStr}:`);
                        this.logger.error(`  Package: ${packageInfo.name} (${filePath})`);
                        this.logger.error(`  Resource size: ${formatBytes(resourceSize)}`);
                        this.logger.error(`  Compressed: ${entry.compressed}`);
                        this.logger.error(`  Offset: ${entry.offset}`);
                        this.logger.error(`  Batch: ${batchIndex + 1}/${totalBatches}`);
                        this.logger.error(`  Resource in batch: ${resourceIndex + 1}/${batchEntries.length}`);
                        this.logger.error(`  Error: ${processingError.message || processingError}`);
                        this.logger.error(`  Error type: ${processingError.constructor.name}`);

                        // Log memory state at time of error
                        const memoryUsage = process.memoryUsage();
                        this.logger.error(`  Memory at error: Heap ${formatBytes(memoryUsage.heapUsed)}/${formatBytes(memoryUsage.heapTotal)} (${(memoryUsage.heapUsed / memoryUsage.heapTotal * 100).toFixed(1)}%)`);

                        if (processingError.stack) {
                            this.logger.debug(`Stack trace: ${processingError.stack}`);
                        }

                        // Track error for analysis
                        errors.push(`Resource ${keyStr}: ${processingError.message || processingError}`);

                        // Continue processing other resources in the batch

                        // Try to determine the cause of the error
                        let errorType = 'unknown';
                        if (processingError.message && processingError.message.includes('corrupt')) {
                            errorType = 'corrupted';
                        } else if (processingError.message && processingError.message.includes('memory')) {
                            errorType = 'memory';
                        } else if (processingError.message && processingError.message.includes('timeout')) {
                            errorType = 'timeout';
                        }

                        // Log additional information based on error type
                        if (errorType === 'corrupted') {
                            this.logger.warn(`Resource ${keyStr} appears to be corrupted. Skipping.`);
                        } else if (errorType === 'memory') {
                            this.logger.warn(`Memory issue while processing resource ${keyStr}. Consider reducing batch size or thresholds.`);

                            // Force garbage collection if available
                            if (global.gc) {
                                this.logger.debug('Forcing garbage collection after memory error');
                                global.gc();
                                // Add a delay to allow GC to complete
                                await new Promise(resolve => setTimeout(resolve, 500));
                            }
                        } else if (errorType === 'timeout') {
                            this.logger.warn(`Timeout while processing resource ${keyStr}. Resource may be too complex.`);
                        }

                        // Continue processing other resources despite this error
                    }
                }

                // Update counters for next batch
                processedEntries += batchEntries.length;
                batchIndex++;

                // Clean up batch resources to free memory
                if (cleanupBuffers && buffersToCleanup.length > 0) {
                    this.logger.debug(`Cleaning up ${buffersToCleanup.length} buffers after batch ${batchIndex}`);

                    // Clear references to allow garbage collection
                    for (let i = 0; i < buffersToCleanup.length; i++) {
                        // Zero out buffer contents to help memory release
                        const buffer = buffersToCleanup[i];
                        if (buffer && buffer.length > 0) {
                            // Only zero out the first few bytes to save time
                            const bytesToClear = Math.min(buffer.length, 1024);
                            for (let j = 0; j < bytesToClear; j++) {
                                buffer[j] = 0;
                            }
                        }
                        buffersToCleanup[i] = null as any;
                    }
                    buffersToCleanup.length = 0; // Clear array

                    // Force garbage collection
                    if (global.gc) {
                        try {
                            global.gc();
                            this.logger.debug('Garbage collection forced after batch processing');

                            // Add a delay to allow GC to complete
                            await new Promise(resolve => setTimeout(resolve, 200));
                        } catch (gcError) {
                            this.logger.debug('Failed to run garbage collection', gcError);
                        }
                    }
                }

                // Add a small delay to allow event loop to process other tasks
                await new Promise(resolve => setTimeout(resolve, 10));
            }

            // Close the consolidated resource provider
            await this.resourceProvider.close(); // Use class property

            // Destroy the consolidated stream pipeline
            await this.streamPipeline.destroy(); // Use class property

            this.logger.info(`Analyzed ${processedResourceCount} resources in package ${packageInfo.name}`);

            // Generate performance report if tracking was enabled
            if (trackPerformance) {
                this.performanceMetrics.totalTime = Date.now() - analysisStartTime;

                // Calculate streaming utilization (percentage of resources using streaming vs direct buffer)
                // This calculation needs to be updated as we no longer track direct/chunked/streaming counts explicitly here.
                // It might need to be derived from ConsolidatedResourceProvider stats if available, or simplified.
                // For now, use a placeholder or remove if not derivable.
                this.performanceMetrics.streamingUtilization = this.calculateStreamingUtilization(); // This method needs to be updated or removed

                // Calculate memory efficiency
                // This calculation needs to be updated as we no longer track peak memory usage within this method.
                // It might need to be derived from EnhancedMemoryManager or ResourceTracker.
                this.performanceMetrics.memoryEfficiency = this.calculateMemoryEfficiency(); // This method needs to be updated or removed

                // Log performance metrics
                this.logPerformanceMetrics();
            }

            // Get dependency analysis summary
            let dependencyAnalysisResult = {
                totalDependencies: 0,
                crossPackageDependencies: 0,
                impactedGameplaySystems: [], // This will be populated by dependency analysis
                highestImpactScore: 0
            };

            if (analyzeDependencies) {
                try {
                    // Get dependency metrics from database
                    // Use placeholder values since the methods don't exist
                    const dependencyCount = 0; // Placeholder - method doesn't exist
                    const crossPackageDependencyCount = 0; // Placeholder - method doesn't exist

                    // Get highest impact score
                    const highestImpactScore = await this.getHighestImpactScore(packageId);

                    dependencyAnalysisResult = {
                        totalDependencies: dependencyCount,
                        crossPackageDependencies: crossPackageDependencyCount,
                        impactedGameplaySystems: [], // Populated by dependency pipeline adapter
                        highestImpactScore
                    };
                } catch (error) {
                    this.logger.error(`Error getting dependency analysis summary: ${error}`);
                }
            }

            // Get gameplay systems impacted from the dependency analysis result
            // The dependency pipeline adapter should ideally return this information.
            // Assuming the dependencyAnalysisResult object is populated correctly by the adapter.
            // If not, this needs adjustment based on the actual output of the dependency pipeline.
             if (analyzeDependencies && this.dependencyChainAnalyzer) {
                 // Assuming dependencyChainAnalyzer has a method to get the impacted systems after analysis
                 // This needs to be confirmed based on EnhancedDependencyChainAnalyzer's API
                 // For now, let's assume the dependencyAnalysisResult from the try block above is correct
                 // and includes impactedGameplaySystems if includeGameplayAnalysis was true.
                 // If not, we might need to call gameplaySystemAnalyzer.analyzePackage(packageId) here
                 // and merge its results.
             }


            return {
                id: packageId, // Use id instead of packageId to match interface
                name: packageInfo.name,
                path: packageInfo.path,
                hash: '', // TODO: Calculate hash
                size: fileSize,
                lastModified: Date.now(),
                resources: resources, // Return the actual resources array that was populated during analysis
                enhancedAnalysis: { // Add enhancedAnalysis property
                     dependencies: dependencyAnalysisResult,
                     performance: {
                         totalTime: this.performanceMetrics.totalTime,
                         readTime: this.performanceMetrics.readTime,
                         processingTime: this.performanceMetrics.processingTime,
                         dependencyAnalysisTime: this.performanceMetrics.dependencyAnalysisTime,
                         streamingUtilization: this.performanceMetrics.streamingUtilization,
                         memoryEfficiency: this.performanceMetrics.memoryEfficiency
                     }
                 }
            };
        } catch (error: any) {
            this.logger.error(`Error analyzing package: ${error.message}`);
            // Collect errors in the result object
            errors.push(`Error analyzing package: ${error.message}`);

            // Initialize packageId if not set
            const safePackageId = packageId || 0;

            // Return partial result with errors
            return {
                 id: safePackageId, // Use id instead of packageId
                 name: packageInfo?.name || path.basename(filePath),
                 path: packageInfo?.path || filePath,
                 hash: '',
                 size: packageInfo?.size || 0,
                 lastModified: Date.now(),
                 resources: [],
                 enhancedAnalysis: undefined // No enhanced analysis on error
             };
        } finally {
            // Clean up resources
            try {
                // Close the consolidated resource provider
                if (this.resourceProvider) { // Check if provider was created
                    await this.resourceProvider.close();
                }

                // Destroy the consolidated stream pipeline
                if (this.streamPipeline) { // Check if pipeline was created
                    await this.streamPipeline.destroy();
                }

                // Clean up buffers if requested
                if (cleanupBuffers && bufferPool) { // Check if bufferPool exists and cleanup is requested
                    bufferPool.cleanup();
                }
            } catch (cleanupError: any) {
                this.logger.error(`Error cleaning up resources: ${cleanupError.message}`);
            }
        }
    }

    /**
     * Dispose of the package analyzer and clean up resources
     */
    public async dispose(): Promise<void> {
        if (this.isDisposed) {
            return;
        }

        try {
            this.logger.info('Disposing package analyzer...');

            // Dispose of dependency chain analyzer
            if (this.dependencyChainAnalyzer) {
                await this.dependencyChainAnalyzer.dispose();
            }

            // Dispose of gameplay system analyzer
            if (this.gameplaySystemAnalyzer) {
                await this.gameplaySystemAnalyzer.dispose();
            }

            // Dispose of dependency pipeline adapter
            if (this.dependencyPipelineAdapter) {
                await this.dependencyPipelineAdapter.dispose();
            }

            // Close resource provider
            if (this.resourceProvider) {
                await this.resourceProvider.close();
            }

            // Destroy stream pipeline
            if (this.streamPipeline) {
                await this.streamPipeline.destroy();
            }

            // Mark as disposed
            this.isDisposed = true;
            this.isInitialized = false;

            this.logger.info('Package analyzer disposed successfully');
        } catch (error: any) {
            this.logger.error(`Error disposing package analyzer: ${error.message}`);
            throw error;
        }
    }
}
