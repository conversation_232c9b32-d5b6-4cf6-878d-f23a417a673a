/**
 * Utility class for string similarity calculations
 */
export class StringSimilarityUtil {
  /**
   * Calculates the similarity between two strings using Levenshtein distance
   * @param str1 First string
   * @param str2 Second string
   * @returns Similarity score between 0 and 1
   */
  public static calculateLevenshteinSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
      return 1.0;
    }

    const costs: number[] = [];
    for (let i = 0; i <= shorter.length; i++) {
      let lastValue = i;
      for (let j = 0; j <= longer.length; j++) {
        if (i === 0) {
          costs[j] = j;
        } else {
          if (j > 0) {
            let newValue = costs[j - 1];
            if (shorter[i - 1] !== longer[j - 1]) {
              newValue = Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
            }
            costs[j - 1] = lastValue;
            lastValue = newValue;
          }
        }
      }
      if (i > 0) {
        costs[longer.length] = lastValue;
      }
    }

    return (longer.length - costs[shorter.length]) / longer.length;
  }

  /**
   * Calculates text similarity using a combination of length ratio and content comparison
   * @param text1 First text
   * @param text2 Second text
   * @returns Similarity score between 0 and 1
   */
  public static calculateTextSimilarity(text1: string, text2: string): number {
    const len1 = text1.length;
    const len2 = text2.length;

    if (len1 === 0 && len2 === 0) return 1;
    if (len1 === 0 || len2 === 0) return 0;

    // Combine length ratio with Levenshtein similarity for more accurate comparison
    const lenRatio = Math.min(len1, len2) / Math.max(len1, len2);
    const levenshteinSim = this.calculateLevenshteinSimilarity(text1, text2);

    return (lenRatio + levenshteinSim) / 2;
  }
}
