/**
 * Progress Tracker
 *
 * This module provides a progress tracker for monitoring and reporting progress on long-running operations.
 * It supports multiple stages, detailed statistics, and event-based progress reporting.
 */

import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';

// Create a logger for this module
const logger = new Logger('ProgressTracker');

/**
 * Progress stage information
 */
export interface ProgressStage {
    id: string;
    name: string;
    weight: number;
    total: number;
    completed: number;
    status: 'pending' | 'active' | 'completed' | 'failed';
    startTime?: number;
    endTime?: number;
    error?: Error;
}

/**
 * Progress update event
 */
export interface ProgressUpdate {
    operationId: string;
    operationName: string;
    overallProgress: number; // 0-100
    currentStage?: ProgressStage;
    stages: ProgressStage[];
    startTime: number;
    currentTime: number;
    estimatedEndTime?: number;
    status: 'pending' | 'active' | 'completed' | 'failed';
    error?: Error;
}

/**
 * Progress tracker options
 */
export interface ProgressTrackerOptions {
    updateIntervalMs?: number;
    logUpdates?: boolean;
    estimateTimeRemaining?: boolean;
}

/**
 * Progress tracker class for monitoring and reporting progress
 */
export class ProgressTracker extends EventEmitter {
    private operationId: string;
    private operationName: string;
    private stages: ProgressStage[] = [];
    private startTime: number = 0;
    private status: 'pending' | 'active' | 'completed' | 'failed' = 'pending';
    private updateIntervalMs: number;
    private updateIntervalId?: NodeJS.Timeout;
    private logUpdates: boolean;
    private estimateTimeRemaining: boolean;
    private error?: Error;

    /**
     * Create a new progress tracker
     * @param operationId Operation ID
     * @param operationName Operation name
     * @param options Progress tracker options
     */
    constructor(operationId: string, operationName: string, options: ProgressTrackerOptions = {}) {
        super();
        this.setMaxListeners(100); // Set high max listeners
        this.operationId = operationId;
        this.operationName = operationName;
        this.updateIntervalMs = options.updateIntervalMs || 1000;
        this.logUpdates = options.logUpdates || false;
        this.estimateTimeRemaining = options.estimateTimeRemaining || true;

        logger.info(`Progress tracker created for operation ${operationName} (${operationId})`);
    }

    /**
     * Add a stage to the progress tracker
     * @param id Stage ID
     * @param name Stage name
     * @param total Total number of items in the stage
     * @param weight Stage weight for overall progress calculation
     * @returns The progress tracker instance (for chaining)
     */
    public addStage(id: string, name: string, total: number, weight: number = 1): ProgressTracker {
        if (this.status !== 'pending') {
            logger.warn(`Cannot add stage ${id} to operation ${this.operationId} because it is already ${this.status}`);
            return this;
        }

        if (this.stages.some(stage => stage.id === id)) {
            logger.warn(`Stage ${id} already exists in operation ${this.operationId}`);
            return this;
        }

        this.stages.push({
            id,
            name,
            weight,
            total,
            completed: 0,
            status: 'pending'
        });

        logger.debug(`Added stage ${name} (${id}) to operation ${this.operationId}`);
        return this;
    }

    /**
     * Start the progress tracker
     * @returns The progress tracker instance (for chaining)
     */
    public start(): ProgressTracker {
        if (this.status !== 'pending') {
            logger.warn(`Cannot start operation ${this.operationId} because it is already ${this.status}`);
            return this;
        }

        this.startTime = Date.now();
        this.status = 'active';

        // Start the first stage if there are any
        if (this.stages.length > 0) {
            const firstStage = this.stages[0];
            firstStage.status = 'active';
            firstStage.startTime = Date.now();
        }

        // Start the update interval
        this.updateIntervalId = setInterval(() => {
            this.emitUpdate();
        }, this.updateIntervalMs);

        // Emit the initial update
        this.emitUpdate();

        logger.info(`Started operation ${this.operationName} (${this.operationId})`);
        return this;
    }

    /**
     * Update progress for a stage
     * @param stageId Stage ID
     * @param completed Number of completed items
     * @param total Optional new total (if the total has changed)
     * @returns The progress tracker instance (for chaining)
     */
    public updateStage(stageId: string, completed: number, total?: number): ProgressTracker {
        if (this.status !== 'active') {
            logger.warn(`Cannot update stage ${stageId} in operation ${this.operationId} because it is ${this.status}`);
            return this;
        }

        const stage = this.stages.find(s => s.id === stageId);
        if (!stage) {
            logger.warn(`Stage ${stageId} not found in operation ${this.operationId}`);
            return this;
        }

        // Update the stage
        stage.completed = completed;
        if (total !== undefined) {
            stage.total = total;
        }

        // Check if the stage is completed
        if (stage.completed >= stage.total) {
            stage.completed = stage.total;
            stage.status = 'completed';
            stage.endTime = Date.now();

            // Start the next stage if there is one
            const currentIndex = this.stages.findIndex(s => s.id === stageId);
            if (currentIndex < this.stages.length - 1) {
                const nextStage = this.stages[currentIndex + 1];
                nextStage.status = 'active';
                nextStage.startTime = Date.now();
            }
        }

        // Emit an update
        this.emitUpdate();

        return this;
    }

    /**
     * Complete a stage
     * @param stageId Stage ID
     * @returns The progress tracker instance (for chaining)
     */
    public completeStage(stageId: string): ProgressTracker {
        if (this.status !== 'active') {
            logger.warn(`Cannot complete stage ${stageId} in operation ${this.operationId} because it is ${this.status}`);
            return this;
        }

        const stage = this.stages.find(s => s.id === stageId);
        if (!stage) {
            logger.warn(`Stage ${stageId} not found in operation ${this.operationId}`);
            return this;
        }

        // Complete the stage
        stage.completed = stage.total;
        stage.status = 'completed';
        stage.endTime = Date.now();

        // Start the next stage if there is one
        const currentIndex = this.stages.findIndex(s => s.id === stageId);
        if (currentIndex < this.stages.length - 1) {
            const nextStage = this.stages[currentIndex + 1];
            nextStage.status = 'active';
            nextStage.startTime = Date.now();
        }

        // Emit an update
        this.emitUpdate();

        return this;
    }

    /**
     * Fail a stage
     * @param stageId Stage ID
     * @param error Error that caused the failure
     * @returns The progress tracker instance (for chaining)
     */
    public failStage(stageId: string, error: Error): ProgressTracker {
        if (this.status !== 'active') {
            logger.warn(`Cannot fail stage ${stageId} in operation ${this.operationId} because it is ${this.status}`);
            return this;
        }

        const stage = this.stages.find(s => s.id === stageId);
        if (!stage) {
            logger.warn(`Stage ${stageId} not found in operation ${this.operationId}`);
            return this;
        }

        // Fail the stage
        stage.status = 'failed';
        stage.endTime = Date.now();
        stage.error = error;

        // Fail the operation
        this.fail(error);

        return this;
    }

    /**
     * Complete the operation
     * @returns The progress tracker instance (for chaining)
     */
    public complete(): ProgressTracker {
        if (this.status !== 'active') {
            logger.warn(`Cannot complete operation ${this.operationId} because it is ${this.status}`);
            return this;
        }

        // Complete all remaining stages
        for (const stage of this.stages) {
            if (stage.status !== 'completed' && stage.status !== 'failed') {
                stage.completed = stage.total;
                stage.status = 'completed';
                stage.endTime = Date.now();
            }
        }

        // Complete the operation
        this.status = 'completed';

        // Stop the update interval
        if (this.updateIntervalId) {
            clearInterval(this.updateIntervalId);
            this.updateIntervalId = undefined;
        }

        // Emit the final update
        this.emitUpdate();

        logger.info(`Completed operation ${this.operationName} (${this.operationId})`);
        return this;
    }

    /**
     * Check if a stage exists
     * @param stageId Stage ID
     * @returns True if the stage exists, false otherwise
     */
    public hasStage(stageId: string): boolean {
        return this.stages.some(s => s.id === stageId);
    }

    /**
     * Fail the operation
     * @param error Error that caused the failure
     * @returns The progress tracker instance (for chaining)
     */
    public fail(error: Error): ProgressTracker {
        if (this.status !== 'active' && this.status !== 'pending') {
            logger.warn(`Cannot fail operation ${this.operationId} because it is ${this.status}`);
            return this;
        }

        // Fail the operation
        this.status = 'failed';
        this.error = error;

        // Stop the update interval
        if (this.updateIntervalId) {
            clearInterval(this.updateIntervalId);
            this.updateIntervalId = undefined;
        }

        // Emit the final update
        this.emitUpdate();

        logger.error(`Failed operation ${this.operationName} (${this.operationId}):`, error);
        return this;
    }

    /**
     * Emit a progress update event
     * @private
     */
    private emitUpdate(): void {
        const currentTime = Date.now();
        const currentStage = this.stages.find(s => s.status === 'active');

        // Calculate overall progress
        const totalWeight = this.stages.reduce((sum, stage) => sum + stage.weight, 0);
        const weightedProgress = this.stages.reduce((sum, stage) => {
            const stageProgress = stage.total > 0 ? (stage.completed / stage.total) : 0;
            return sum + (stageProgress * stage.weight);
        }, 0);

        const overallProgress = totalWeight > 0 ? (weightedProgress / totalWeight) * 100 : 0;

        // Estimate time remaining if requested
        let estimatedEndTime: number | undefined;
        if (this.estimateTimeRemaining && overallProgress > 0) {
            const elapsedTime = currentTime - this.startTime;
            const estimatedTotalTime = elapsedTime / (overallProgress / 100);
            estimatedEndTime = this.startTime + estimatedTotalTime;
        }

        // Create the progress update
        const update: ProgressUpdate = {
            operationId: this.operationId,
            operationName: this.operationName,
            overallProgress,
            currentStage,
            stages: [...this.stages],
            startTime: this.startTime,
            currentTime,
            estimatedEndTime,
            status: this.status,
            error: this.error
        };

        // Emit the update event
        this.emit('update', update);

        // Log the update if requested
        if (this.logUpdates) {
            const stageInfo = currentStage ? ` (Stage: ${currentStage.name}, ${currentStage.completed}/${currentStage.total})` : '';
            logger.info(`Progress: ${overallProgress.toFixed(1)}%${stageInfo}`);
        }
    }
}

// Export the ProgressTracker class
export default ProgressTracker;
