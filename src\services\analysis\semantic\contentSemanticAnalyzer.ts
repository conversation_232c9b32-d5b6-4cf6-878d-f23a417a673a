/**
 * Content Semantic Analyzer
 * 
 * This module provides advanced semantic analysis of resource content
 * to extract meaningful information and patterns.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { injectable, singleton } from '../../di/decorators.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';

/**
 * Semantic entity type
 */
export enum SemanticEntityType {
    OBJECT = 'OBJECT',
    INTERACTION = 'INTERACTION',
    TRAIT = 'TRAIT',
    BUFF = 'BUFF',
    CAREER = 'CAREER',
    SKILL = 'SKILL',
    ASPIRATION = 'ASPIRATION',
    EVENT = 'EVENT',
    UI_ELEMENT = 'UI_ELEMENT',
    ANIMATION = 'ANIMATION',
    VISUAL_EFFECT = 'VISUAL_EFFECT',
    SOUND_EFFECT = 'SOUND_EFFECT',
    SCRIPT_FUNCTION = 'SCRIPT_FUNCTION',
    SCRIPT_CLASS = 'SCRIPT_CLASS',
    SCRIPT_MODULE = 'SCRIPT_MODULE',
    UNKNOWN = 'UNKNOWN'
}

/**
 * Semantic entity
 */
export interface SemanticEntity {
    /**
     * Entity type
     */
    type: SemanticEntityType;
    
    /**
     * Entity name
     */
    name: string;
    
    /**
     * Entity description
     */
    description?: string;
    
    /**
     * Entity attributes
     */
    attributes: Record<string, any>;
    
    /**
     * Source location in the content
     */
    sourceLocation?: {
        /**
         * Start position
         */
        start: number;
        
        /**
         * End position
         */
        end: number;
        
        /**
         * Context snippet
         */
        context: string;
    };
    
    /**
     * Confidence score (0-100)
     */
    confidence: number;
}

/**
 * Content semantic analysis result
 */
export interface ContentSemanticAnalysisResult {
    /**
     * Extracted entities
     */
    entities: SemanticEntity[];
    
    /**
     * Key phrases
     */
    keyPhrases: string[];
    
    /**
     * Main topics
     */
    mainTopics: string[];
    
    /**
     * Content summary
     */
    summary: string;
    
    /**
     * Content complexity score (0-100)
     */
    complexityScore: number;
    
    /**
     * Content type
     */
    contentType: string;
    
    /**
     * Analysis timestamp
     */
    timestamp: number;
}

/**
 * Content semantic analyzer
 */
@singleton()
export class ContentSemanticAnalyzer {
    private logger: Logger;
    
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(logger?: Logger) {
        this.logger = logger || new Logger('ContentSemanticAnalyzer');
    }
    
    /**
     * Analyze resource content semantically
     * @param resourceKey Resource key
     * @param content Resource content
     * @param metadata Resource metadata
     * @returns Content semantic analysis result
     */
    public analyzeContent(
        resourceKey: ResourceKey,
        content: string,
        metadata: Record<string, any>
    ): ContentSemanticAnalysisResult {
        try {
            // Initialize result
            const result: ContentSemanticAnalysisResult = {
                entities: [],
                keyPhrases: [],
                mainTopics: [],
                summary: '',
                complexityScore: 0,
                contentType: this.determineContentType(resourceKey, content),
                timestamp: Date.now()
            };
            
            // Skip analysis for empty content
            if (!content || content.trim().length === 0) {
                return result;
            }
            
            // Analyze based on resource type
            switch (resourceKey.type) {
                case ResourceTypes.RESOURCE_TYPE_TUNING:
                    this.analyzeTuningXml(content, result);
                    break;
                    
                case ResourceTypes.RESOURCE_TYPE_SCRIPT:
                    this.analyzePythonScript(content, result);
                    break;
                    
                case ResourceTypes.RESOURCE_TYPE_STRINGTABLE:
                    this.analyzeStringTable(content, result);
                    break;
                    
                default:
                    // Generic analysis for other types
                    this.analyzeGenericContent(content, result);
                    break;
            }
            
            // Calculate complexity score
            result.complexityScore = this.calculateComplexityScore(result);
            
            // Generate summary
            result.summary = this.generateSummary(result);
            
            return result;
        } catch (error) {
            this.logger.error(`Error analyzing content for resource ${resourceKey.type}:${resourceKey.instance}:`, error);
            
            // Return empty result on error
            return {
                entities: [],
                keyPhrases: [],
                mainTopics: [],
                summary: 'Error analyzing content',
                complexityScore: 0,
                contentType: 'unknown',
                timestamp: Date.now()
            };
        }
    }
    
    /**
     * Determine content type based on resource key and content
     * @param resourceKey Resource key
     * @param content Resource content
     * @returns Content type string
     */
    private determineContentType(resourceKey: ResourceKey, content: string): string {
        // Check resource type first
        switch (resourceKey.type) {
            case ResourceTypes.RESOURCE_TYPE_TUNING:
                return 'xml';
                
            case ResourceTypes.RESOURCE_TYPE_SCRIPT:
                return 'python';
                
            case ResourceTypes.RESOURCE_TYPE_STRINGTABLE:
                return 'stringtable';
                
            case ResourceTypes.RESOURCE_TYPE_DDS_IMAGE:
            case ResourceTypes.RESOURCE_TYPE_PNG_IMAGE:
            case ResourceTypes.RESOURCE_TYPE_RLE2_IMAGE:
                return 'image';
                
            case ResourceTypes.RESOURCE_TYPE_SOUNDEFFECT:
                return 'audio';
                
            case ResourceTypes.RESOURCE_TYPE_MODEL:
                return 'model';
        }
        
        // If content is available, try to determine from content
        if (content) {
            if (content.trim().startsWith('<')) {
                return 'xml';
            }
            
            if (content.includes('import ') && content.includes('def ')) {
                return 'python';
            }
            
            if (content.includes('class ') && content.includes('function ')) {
                return 'script';
            }
        }
        
        return 'unknown';
    }
    
    /**
     * Analyze tuning XML content
     * @param content XML content
     * @param result Analysis result to update
     */
    private analyzeTuningXml(content: string, result: ContentSemanticAnalysisResult): void {
        try {
            // Extract tuning type
            const tuningTypeMatch = content.match(/<I c="([^"]+)"/);
            if (tuningTypeMatch && tuningTypeMatch[1]) {
                const tuningType = tuningTypeMatch[1];
                result.mainTopics.push(tuningType);
                
                // Map tuning type to semantic entity type
                let entityType = SemanticEntityType.UNKNOWN;
                if (tuningType.includes('Trait')) entityType = SemanticEntityType.TRAIT;
                else if (tuningType.includes('Buff')) entityType = SemanticEntityType.BUFF;
                else if (tuningType.includes('Career')) entityType = SemanticEntityType.CAREER;
                else if (tuningType.includes('Skill')) entityType = SemanticEntityType.SKILL;
                else if (tuningType.includes('Aspiration')) entityType = SemanticEntityType.ASPIRATION;
                else if (tuningType.includes('Interaction')) entityType = SemanticEntityType.INTERACTION;
                else if (tuningType.includes('Object')) entityType = SemanticEntityType.OBJECT;
                
                // Extract name
                const nameMatch = content.match(/n="([^"]+)"/);
                const name = nameMatch ? nameMatch[1] : 'Unknown';
                
                // Add entity
                result.entities.push({
                    type: entityType,
                    name: name,
                    attributes: { tuningType },
                    confidence: 90
                });
            }
            
            // Extract display name
            const displayNameMatch = content.match(/<T n="display_name">([^<]+)<\/T>/);
            if (displayNameMatch && displayNameMatch[1]) {
                result.keyPhrases.push(displayNameMatch[1]);
            }
            
            // Extract description
            const descriptionMatch = content.match(/<T n="display_description">([^<]+)<\/T>/);
            if (descriptionMatch && descriptionMatch[1]) {
                result.keyPhrases.push(descriptionMatch[1]);
            }
            
            // Extract other important elements
            const importantElements = [
                'ages', 'gender', 'categories', 'tags', 'buffs', 'traits',
                'loot_list', 'basic_extras', 'super_affordance'
            ];
            
            for (const element of importantElements) {
                if (content.includes(`n="${element}"`)) {
                    result.keyPhrases.push(element);
                }
            }
        } catch (error) {
            this.logger.error('Error analyzing tuning XML:', error);
        }
    }
    
    /**
     * Analyze Python script content
     * @param content Python script content
     * @param result Analysis result to update
     */
    private analyzePythonScript(content: string, result: ContentSemanticAnalysisResult): void {
        try {
            // Extract imports
            const importMatches = content.matchAll(/import\s+([^\n]+)/g);
            for (const match of importMatches) {
                if (match[1]) {
                    result.keyPhrases.push(`import ${match[1].trim()}`);
                }
            }
            
            // Extract classes
            const classMatches = content.matchAll(/class\s+(\w+)(?:\(([^)]+)\))?:/g);
            for (const match of classMatches) {
                if (match[1]) {
                    const className = match[1];
                    const parentClass = match[2] || '';
                    
                    result.entities.push({
                        type: SemanticEntityType.SCRIPT_CLASS,
                        name: className,
                        attributes: { parentClass },
                        confidence: 90
                    });
                    
                    result.mainTopics.push(className);
                }
            }
            
            // Extract functions
            const functionMatches = content.matchAll(/def\s+(\w+)\s*\(([^)]*)\):/g);
            for (const match of functionMatches) {
                if (match[1]) {
                    const functionName = match[1];
                    const parameters = match[2] || '';
                    
                    result.entities.push({
                        type: SemanticEntityType.SCRIPT_FUNCTION,
                        name: functionName,
                        attributes: { parameters },
                        confidence: 85
                    });
                }
            }
            
            // Check for command decorators
            if (content.includes('@sims4.commands.Command')) {
                result.keyPhrases.push('command');
                result.mainTopics.push('command');
            }
            
            // Check for injections
            if (content.includes('inject') || content.includes('patch')) {
                result.keyPhrases.push('injection');
                result.mainTopics.push('injection');
            }
        } catch (error) {
            this.logger.error('Error analyzing Python script:', error);
        }
    }
    
    /**
     * Analyze string table content
     * @param content String table content
     * @param result Analysis result to update
     */
    private analyzeStringTable(content: string, result: ContentSemanticAnalysisResult): void {
        try {
            // Extract string entries
            const stringMatches = content.matchAll(/<String\s+[^>]*>([^<]+)<\/String>/g);
            for (const match of stringMatches) {
                if (match[1]) {
                    result.keyPhrases.push(match[1].trim());
                }
            }
            
            // Extract key attributes
            const keyMatches = content.matchAll(/key="([^"]+)"/g);
            for (const match of keyMatches) {
                if (match[1]) {
                    result.entities.push({
                        type: SemanticEntityType.UI_ELEMENT,
                        name: match[1],
                        attributes: {},
                        confidence: 80
                    });
                }
            }
        } catch (error) {
            this.logger.error('Error analyzing string table:', error);
        }
    }
    
    /**
     * Analyze generic content
     * @param content Generic content
     * @param result Analysis result to update
     */
    private analyzeGenericContent(content: string, result: ContentSemanticAnalysisResult): void {
        try {
            // Extract common patterns
            const commonPatterns = [
                { pattern: /trait/gi, topic: 'trait' },
                { pattern: /skill/gi, topic: 'skill' },
                { pattern: /career/gi, topic: 'career' },
                { pattern: /buff/gi, topic: 'buff' },
                { pattern: /aspiration/gi, topic: 'aspiration' },
                { pattern: /interaction/gi, topic: 'interaction' },
                { pattern: /object/gi, topic: 'object' },
                { pattern: /animation/gi, topic: 'animation' },
                { pattern: /effect/gi, topic: 'effect' },
                { pattern: /sound/gi, topic: 'sound' },
                { pattern: /texture/gi, topic: 'texture' },
                { pattern: /model/gi, topic: 'model' }
            ];
            
            for (const { pattern, topic } of commonPatterns) {
                if (pattern.test(content)) {
                    result.keyPhrases.push(topic);
                    
                    // Add to main topics if it appears frequently
                    const matches = content.match(pattern);
                    if (matches && matches.length > 2) {
                        result.mainTopics.push(topic);
                    }
                }
            }
        } catch (error) {
            this.logger.error('Error analyzing generic content:', error);
        }
    }
    
    /**
     * Calculate complexity score
     * @param result Analysis result
     * @returns Complexity score (0-100)
     */
    private calculateComplexityScore(result: ContentSemanticAnalysisResult): number {
        // Base score
        let score = 0;
        
        // Add points for entities
        score += Math.min(50, result.entities.length * 5);
        
        // Add points for key phrases
        score += Math.min(20, result.keyPhrases.length * 2);
        
        // Add points for main topics
        score += Math.min(20, result.mainTopics.length * 5);
        
        // Cap at 100
        return Math.min(100, score);
    }
    
    /**
     * Generate summary from analysis result
     * @param result Analysis result
     * @returns Summary string
     */
    private generateSummary(result: ContentSemanticAnalysisResult): string {
        // Create summary based on entities and topics
        const entityTypes = [...new Set(result.entities.map(e => e.type))];
        const mainTopics = result.mainTopics.slice(0, 3);
        
        if (entityTypes.length === 0 && mainTopics.length === 0) {
            return 'Unknown content';
        }
        
        let summary = '';
        
        if (entityTypes.length > 0) {
            summary += `Contains ${entityTypes.join(', ')}`;
        }
        
        if (mainTopics.length > 0) {
            if (summary) summary += ' related to ';
            else summary += 'Related to ';
            
            summary += mainTopics.join(', ');
        }
        
        return summary;
    }
}
