<template>
  <v-app>
    <v-app-bar app color="primary" dark>
      <v-toolbar-title>{{ title }}</v-toolbar-title>
      <!-- Add toolbar actions here if needed -->
    </v-app-bar>

    <v-main>
      <v-container fluid class="pa-4"> <!-- Add padding -->
        <slot name="toolbar"></slot> <!-- Slot for potential toolbar components -->
        <slot></slot> <!-- Default slot for main content -->
      </v-container>
    </v-main>

    <v-footer app class="pa-2"> <!-- Add padding -->
      <span>Status: {{ currentStatus }}</span>
    </v-footer>
    <!-- PrimeVue components removed -->
    <!-- <Toast /> -->
    <!-- <Dialog /> -->
  </v-app> <!-- Closing v-app instead of div -->
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
// Removed PrimeVue imports
// import Toast from 'primevue/toast';
// import Dialog from 'primevue/dialog';
import { useAnalysisStore } from '../store/analysis'; // Removed .js extension

// Define props if needed, e.g., for the title
const props = defineProps({
  title: {
    type: String,
    default: 'Sims 4 Package Analyzer'
  }
});

// Setup logic for the layout component if needed
const analysisStore = useAnalysisStore();
const currentStatus = computed(() => analysisStore.status);

</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* Ensure layout takes full viewport height */
  background-color: #f4f4f4; /* Light background for contrast */
}

.app-header {
  background-color: #607D8B; /* Example header color */
  color: white;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-main-content {
  flex-grow: 1; /* Allow main content to take available space */
  padding: 1.5rem;
  max-width: 1200px; /* Limit max width for better readability */
  margin: 0 auto; /* Center content */
  width: 100%;
}

.app-footer {
  background-color: #455A64; /* Example footer color */
  color: #cfd8dc;
  padding: 0.5rem 1rem;
  text-align: center;
  font-size: 0.85rem;
  margin-top: auto; /* Push footer to the bottom */
}

/* Add more specific styles as needed */
</style>
