/**
 * Cross-Resource Analyzer
 *
 * This service analyzes relationships between different resources within a package.
 * It identifies connections between Tuning XML, SimData, Object Definitions, and Scripts,
 * and extracts semantic meaning from these relationships.
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import * as ResourceTypes from '../../constants/resourceTypes.js';

// Import DependencyInfo from database types
import { DependencyInfo } from '../../types/database.js';

export interface ResourceRelationship {
    sourceResourceId: number;
    targetResourceId: number;
    sourceType: number;
    targetType: number;
    sourceInstance: bigint;
    targetInstance: bigint;
    relationshipType: string;
    confidence: number;
    metadata?: Record<string, any>;
    timestamp: number;
}

export interface RelationshipAnalysisResult {
    relationships: ResourceRelationship[];
    summary: {
        totalRelationships: number;
        byType: Record<string, number>;
        byConfidence: Record<string, number>;
    };
}

export class CrossResourceAnalyzer {
    private logger: Logger;
    private databaseService: DatabaseService;

    constructor(databaseService: DatabaseService, logger?: Logger) {
        this.logger = logger || new Logger('CrossResourceAnalyzer');
        this.databaseService = databaseService;
    }

    /**
     * Initialize the analyzer
     */
    public async initialize(): Promise<void> {
        try {
            // Check if the relationships table exists
            const tableExists = await this.databaseService.checkTableExists('relationships');

            if (!tableExists) {
                // Create the relationships table
                await this.databaseService.executeQuery(`
                    CREATE TABLE relationships (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sourceResourceId INTEGER NOT NULL,
                        targetResourceId INTEGER NOT NULL,
                        sourceType INTEGER NOT NULL,
                        targetType INTEGER NOT NULL,
                        sourceInstance TEXT NOT NULL,
                        targetInstance TEXT NOT NULL,
                        relationshipType TEXT NOT NULL,
                        confidence INTEGER NOT NULL,
                        metadata TEXT,
                        timestamp INTEGER NOT NULL,
                        FOREIGN KEY (sourceResourceId) REFERENCES resources(id),
                        FOREIGN KEY (targetResourceId) REFERENCES resources(id)
                    )
                `);

                // Create indexes
                await this.databaseService.executeQuery(`
                    CREATE INDEX idx_relationships_source ON relationships(sourceResourceId)
                `);

                await this.databaseService.executeQuery(`
                    CREATE INDEX idx_relationships_target ON relationships(targetResourceId)
                `);

                await this.databaseService.executeQuery(`
                    CREATE INDEX idx_relationships_type ON relationships(relationshipType)
                `);

                this.logger.info('Relationships table created');
            }
        } catch (error: any) {
            this.logger.error(`Failed to initialize CrossResourceAnalyzer: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Analyze relationships between resources in a package
     * @param packageId The package ID
     * @param resources Optional array of resources (if already loaded)
     * @returns Analysis results
     */
    public async analyzePackageRelationships(
        packageId: number,
        resources?: any[]
    ): Promise<RelationshipAnalysisResult> {
        await this.initialize();

        this.logger.info(`Analyzing cross-resource relationships for package ${packageId}`);

        // Check if packageId is valid
        if (!packageId) {
            this.logger.warn(`Invalid packageId: ${packageId}`);
            return {
                relationships: [],
                summary: {
                    totalRelationships: 0,
                    byType: {},
                    byConfidence: {}
                }
            };
        }

        // Get resources if not provided
        let packageResources = resources;
        if (!packageResources) {
            packageResources = await this.databaseService.resources.getResourcesByPackageId(packageId);
        }

        if (!packageResources || packageResources.length === 0) {
            this.logger.warn(`No resources found for package ${packageId}`);
            return {
                relationships: [],
                summary: {
                    totalRelationships: 0,
                    byType: {},
                    byConfidence: {}
                }
            };
        }

        this.logger.info(`Found ${packageResources.length} resources in package ${packageId}`);

        // Get all dependencies for the package
        const dependencies = await this.databaseService.dependencies.getDependenciesForPackage(packageId);

        // Analyze relationships
        const relationships = await this.analyzeRelationships(packageResources, dependencies || []);

        // Create summary
        const summary = this.createRelationshipSummary(relationships);

        // Save relationships to database
        await this.saveRelationships(relationships);

        return {
            relationships,
            summary
        };
    }

    /**
     * Analyze relationships between resources in a package (alias for analyzePackageRelationships)
     * @param packageId The package ID
     * @returns Analysis results
     */
    public async analyzePackage(packageId: number): Promise<RelationshipAnalysisResult> {
        return this.analyzePackageRelationships(packageId);
    }

    /**
     * Analyze relationships between resources
     * @param resources The resources to analyze
     * @param dependencies The dependencies between resources
     * @returns Analyzed relationships
     */
    private async analyzeRelationships(
        resources: any[],
        dependencies: DependencyInfo[]
    ): Promise<ResourceRelationship[]> {
        const relationships: ResourceRelationship[] = [];

        // Create maps for quick resource lookup
        const resourceByIdMap = new Map<number, any>();
        const resourceByTypeInstanceMap = new Map<string, any>();

        resources.forEach(resource => {
            if (resource && resource.id) {
                resourceByIdMap.set(resource.id, resource);
            }

            if (resource && resource.type !== undefined && resource.instance) {
                // Create a type-instance key for lookup
                const typeInstanceKey = `${resource.type}:${resource.instance}`;
                resourceByTypeInstanceMap.set(typeInstanceKey, resource);
            }
        });

        // Process dependencies to identify relationships
        for (const dependency of dependencies) {
            // Skip invalid dependencies
            if (!dependency.resourceId || !dependency.targetInstance) {
                continue;
            }

            // Find the source resource by ID
            const sourceResource = resourceByIdMap.get(dependency.resourceId);

            if (!sourceResource) {
                continue;
            }

            // Find target resource by type and instance
            const targetTypeInstanceKey = dependency.targetType && dependency.targetInstance ?
                `${dependency.targetType}:${dependency.targetInstance}` : null;
            const targetResource = targetTypeInstanceKey ?
                resourceByTypeInstanceMap.get(targetTypeInstanceKey) : null;

            if (!targetResource) {
                // Target resource not found in this package, might be an external reference
                continue;
            }

            // Determine relationship type and confidence
            const { relationshipType, confidence, metadata } = this.determineRelationship(
                sourceResource,
                targetResource,
                dependency
            );

            // Create relationship
            const relationship: ResourceRelationship = {
                sourceResourceId: sourceResource.id,
                targetResourceId: targetResource.id,
                sourceType: sourceResource.type,
                targetType: targetResource.type,
                sourceInstance: BigInt(sourceResource.instance),
                targetInstance: BigInt(targetResource.instance),
                relationshipType,
                confidence,
                metadata,
                timestamp: Date.now()
            };

            relationships.push(relationship);
        }

        // Look for additional relationships based on naming patterns and other heuristics
        const additionalRelationships = await this.findAdditionalRelationships(resources);
        relationships.push(...additionalRelationships);

        return relationships;
    }

    /**
     * Determine the type and confidence of a relationship
     * @param sourceResource The source resource
     * @param targetResource The target resource
     * @param dependency The dependency information
     * @returns Relationship type, confidence, and metadata
     */
    private determineRelationship(
        sourceResource: any,
        targetResource: any,
        dependency: DependencyInfo
    ): { relationshipType: string; confidence: number; metadata?: Record<string, any> } {
        const sourceType = sourceResource.type;
        const targetType = targetResource.type;
        const metadata: Record<string, any> = {};

        // Default values
        let relationshipType = 'Unknown';
        let confidence = 50;

        // Tuning XML to SimData relationship
        if (sourceType === ResourceTypes.RESOURCE_TYPE_TUNING && targetType === ResourceTypes.RESOURCE_TYPE_SIMDATA) {
            relationshipType = 'TuningToSimData';
            confidence = 90;
            metadata.description = 'Tuning XML references SimData';
        }
        // SimData to Tuning XML relationship
        else if (sourceType === ResourceTypes.RESOURCE_TYPE_SIMDATA && targetType === ResourceTypes.RESOURCE_TYPE_TUNING) {
            relationshipType = 'SimDataToTuning';
            confidence = 90;
            metadata.description = 'SimData references Tuning XML';
        }
        // Object Definition to Tuning XML relationship
        else if (sourceType === ResourceTypes.RESOURCE_TYPE_OBJECT_DEFINITION && targetType === ResourceTypes.RESOURCE_TYPE_TUNING) {
            relationshipType = 'ObjectToTuning';
            confidence = 85;
            metadata.description = 'Object Definition references Tuning XML';
        }
        // Script to Resource relationship
        else if (sourceType === ResourceTypes.RESOURCE_TYPE_SCRIPT) {
            relationshipType = 'ScriptToResource';
            confidence = 80;
            metadata.description = 'Script references Resource';
            metadata.targetTypeName = this.getResourceTypeName(targetType);
        }
        // CAS Part to Resource relationship
        else if (sourceType === ResourceTypes.RESOURCE_TYPE_CASPART) {
            relationshipType = 'CASPartToResource';
            confidence = 80;
            metadata.description = 'CAS Part references Resource';
            metadata.targetTypeName = this.getResourceTypeName(targetType);
        }
        // Use the reference type from the dependency if available
        else if (dependency.referenceType) {
            relationshipType = dependency.referenceType;
            confidence = 70;
            metadata.description = `${this.getResourceTypeName(sourceType)} references ${this.getResourceTypeName(targetType)}`;
        }

        return { relationshipType, confidence, metadata };
    }

    /**
     * Find additional relationships based on naming patterns and other heuristics
     * @param resources The resources to analyze
     * @returns Additional relationships
     */
    private async findAdditionalRelationships(resources: any[]): Promise<ResourceRelationship[]> {
        const relationships: ResourceRelationship[] = [];

        // Group resources by type for easier processing
        const resourcesByType = new Map<number, any[]>();
        resources.forEach(resource => {
            const type = resource.type;
            if (!resourcesByType.has(type)) {
                resourcesByType.set(type, []);
            }
            resourcesByType.get(type)?.push(resource);
        });

        // Find Tuning XML and SimData pairs based on instance ID
        const tuningResources = resourcesByType.get(ResourceTypes.RESOURCE_TYPE_TUNING) || [];
        const simDataResources = resourcesByType.get(ResourceTypes.RESOURCE_TYPE_SIMDATA) || [];

        for (const tuningResource of tuningResources) {
            const tuningInstance = BigInt(tuningResource.instance);

            // Look for SimData with the same instance ID
            const matchingSimData = simDataResources.find(
                simData => BigInt(simData.instance) === tuningInstance
            );

            if (matchingSimData) {
                // Create relationship
                const relationship: ResourceRelationship = {
                    sourceResourceId: tuningResource.id,
                    targetResourceId: matchingSimData.id,
                    sourceType: tuningResource.type,
                    targetType: matchingSimData.type,
                    sourceInstance: tuningInstance,
                    targetInstance: BigInt(matchingSimData.instance),
                    relationshipType: 'TuningSimDataPair',
                    confidence: 95,
                    metadata: {
                        description: 'Tuning XML and SimData with matching instance IDs',
                        tuningName: tuningResource.name || 'Unknown'
                    },
                    timestamp: Date.now()
                };

                relationships.push(relationship);
            }
        }

        return relationships;
    }

    /**
     * Create a summary of relationships
     * @param relationships The relationships to summarize
     * @returns Summary information
     */
    private createRelationshipSummary(relationships: ResourceRelationship[]): {
        totalRelationships: number;
        byType: Record<string, number>;
        byConfidence: Record<string, number>;
    } {
        const byType: Record<string, number> = {};
        const byConfidence: Record<string, number> = {};

        // Count relationships by type and confidence
        relationships.forEach(relationship => {
            // Count by type
            byType[relationship.relationshipType] = (byType[relationship.relationshipType] || 0) + 1;

            // Count by confidence range
            const confidenceRange = this.getConfidenceRange(relationship.confidence);
            byConfidence[confidenceRange] = (byConfidence[confidenceRange] || 0) + 1;
        });

        return {
            totalRelationships: relationships.length,
            byType,
            byConfidence
        };
    }

    /**
     * Get the confidence range for a confidence value
     * @param confidence The confidence value
     * @returns The confidence range
     */
    private getConfidenceRange(confidence: number): string {
        if (confidence >= 90) return 'Very High (90-100)';
        if (confidence >= 75) return 'High (75-89)';
        if (confidence >= 50) return 'Medium (50-74)';
        if (confidence >= 25) return 'Low (25-49)';
        return 'Very Low (0-24)';
    }

    /**
     * Get a human-readable name for a resource type
     * @param type The resource type
     * @returns The resource type name
     */
    private getResourceTypeName(type: number): string {
        switch (type) {
            case ResourceTypes.RESOURCE_TYPE_TUNING: return 'Tuning XML';
            case ResourceTypes.RESOURCE_TYPE_SIMDATA: return 'SimData';
            case ResourceTypes.RESOURCE_TYPE_OBJECT_DEFINITION: return 'Object Definition';
            case ResourceTypes.RESOURCE_TYPE_CASPART: return 'CAS Part';
            case ResourceTypes.RESOURCE_TYPE_SCRIPT: return 'Script';
            case ResourceTypes.RESOURCE_TYPE_DDS_IMAGE: return 'DDS Image';
            case ResourceTypes.RESOURCE_TYPE_PNG_IMAGE: return 'PNG Image';
            case ResourceTypes.RESOURCE_TYPE_SOUND: return 'Sound';
            case ResourceTypes.RESOURCE_TYPE_ANIMATION: return 'Animation';
            case ResourceTypes.RESOURCE_TYPE_MODEL: return 'Model';
            default: return `Type 0x${type.toString(16)}`;
        }
    }

    /**
     * Save relationships to the database
     * @param relationships The relationships to save
     */
    private async saveRelationships(relationships: ResourceRelationship[]): Promise<void> {
        if (relationships.length === 0) {
            return;
        }

        try {
            // Make sure the relationships table exists
            await this.initialize();

            // Save relationships
            for (const relationship of relationships) {
                await this.databaseService.executeQuery(`
                    INSERT INTO relationships (
                        sourceResourceId, targetResourceId, sourceType, targetType,
                        sourceInstance, targetInstance, relationshipType, confidence,
                        metadata, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    relationship.sourceResourceId,
                    relationship.targetResourceId,
                    relationship.sourceType,
                    relationship.targetType,
                    relationship.sourceInstance.toString(),
                    relationship.targetInstance.toString(),
                    relationship.relationshipType,
                    relationship.confidence,
                    relationship.metadata ? JSON.stringify(relationship.metadata) : null,
                    relationship.timestamp
                ]);
            }

            this.logger.info(`Saved ${relationships.length} relationships to database`);
        } catch (error: any) {
            this.logger.error(`Failed to save relationships to database: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Dispose of resources used by the analyzer
     */
    public async dispose(): Promise<void> {
        try {
            this.logger.info('Disposing CrossResourceAnalyzer resources');
            this.logger.info('CrossResourceAnalyzer resources disposed successfully');
        } catch (error: any) {
            this.logger.error(`Error disposing CrossResourceAnalyzer resources: ${error.message || error}`);
            throw error;
        }
    }
}
