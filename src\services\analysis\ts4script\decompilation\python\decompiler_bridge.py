#!/usr/bin/env python
"""
Python Decompiler Bridge for TS4Script Analysis

This script provides a bridge to various Python decompilers to decompile
Python bytecode from TS4Script files. It supports multiple decompilers
and falls back to alternatives if one fails.

Supported decompilers:
- decompyle3: For Python 3.7-3.8
- unpyc37: For Python 3.7-3.10
- pycdc: For all Python versions (C++ based)

Usage:
    python decompiler_bridge.py <bytecode_file> [--version <python_version>] [--decompiler <name>]

Arguments:
    bytecode_file       Path to the Python bytecode file to decompile
    --version           Python version of the bytecode (e.g., 3.7, 3.8)
    --decompiler        Preferred decompiler to use (decompyle3, unpyc37, pycdc)
"""

import os
import sys
import argparse
import subprocess
import tempfile
import json
import importlib.util
from typing import Dict, List, Any, Optional, Tuple


class DecompilerResult:
    """Result of a decompilation attempt"""
    
    def __init__(self, success: bool, source_code: Optional[str] = None, error: Optional[str] = None):
        self.success = success
        self.source_code = source_code
        self.error = error
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'success': self.success,
            'source_code': self.source_code if self.success else None,
            'error': self.error if not self.success else None
        }


class Decompiler:
    """Base class for decompilers"""
    
    def __init__(self, name: str):
        self.name = name
        self.available = self._check_availability()
    
    def _check_availability(self) -> bool:
        """Check if the decompiler is available"""
        raise NotImplementedError("Subclasses must implement this method")
    
    def decompile(self, bytecode_path: str, python_version: str) -> DecompilerResult:
        """Decompile bytecode"""
        raise NotImplementedError("Subclasses must implement this method")


class Decompyle3Decompiler(Decompiler):
    """Decompiler using decompyle3"""
    
    def __init__(self):
        super().__init__("decompyle3")
    
    def _check_availability(self) -> bool:
        """Check if decompyle3 is available"""
        try:
            import decompyle3
            return True
        except ImportError:
            return False
    
    def decompile(self, bytecode_path: str, python_version: str) -> DecompilerResult:
        """Decompile bytecode using decompyle3"""
        if not self.available:
            return DecompilerResult(False, error="decompyle3 is not installed")
        
        try:
            # Import decompyle3 dynamically
            import decompyle3.main
            
            # Create a temporary file for output
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.py', delete=False) as temp_file:
                temp_path = temp_file.name
            
            # Decompile the bytecode
            try:
                decompyle3.main.decompile_file(bytecode_path, temp_path)
                
                # Read the decompiled source
                with open(temp_path, 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Clean up
                os.unlink(temp_path)
                
                return DecompilerResult(True, source_code=source_code)
            except Exception as e:
                # Clean up
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                
                return DecompilerResult(False, error=f"decompyle3 error: {str(e)}")
        except Exception as e:
            return DecompilerResult(False, error=f"decompyle3 error: {str(e)}")


class Unpyc37Decompiler(Decompiler):
    """Decompiler using unpyc37"""
    
    def __init__(self):
        super().__init__("unpyc37")
    
    def _check_availability(self) -> bool:
        """Check if unpyc37 is available"""
        try:
            import unpyc.unpyc3
            return True
        except ImportError:
            return False
    
    def decompile(self, bytecode_path: str, python_version: str) -> DecompilerResult:
        """Decompile bytecode using unpyc37"""
        if not self.available:
            return DecompilerResult(False, error="unpyc37 is not installed")
        
        try:
            # Import unpyc37 dynamically
            import unpyc.unpyc3
            
            # Read the bytecode file
            with open(bytecode_path, 'rb') as f:
                bytecode = f.read()
            
            # Decompile the bytecode
            try:
                source_code = unpyc.unpyc3.decompile(bytecode)
                return DecompilerResult(True, source_code=source_code)
            except Exception as e:
                return DecompilerResult(False, error=f"unpyc37 error: {str(e)}")
        except Exception as e:
            return DecompilerResult(False, error=f"unpyc37 error: {str(e)}")


class PycdcDecompiler(Decompiler):
    """Decompiler using pycdc"""
    
    def __init__(self):
        super().__init__("pycdc")
        self.pycdc_path = self._find_pycdc()
    
    def _check_availability(self) -> bool:
        """Check if pycdc is available"""
        return self.pycdc_path is not None
    
    def _find_pycdc(self) -> Optional[str]:
        """Find the pycdc executable"""
        # Check if pycdc is in PATH
        try:
            result = subprocess.run(['pycdc', '--version'], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE, 
                                   check=False)
            if result.returncode == 0:
                return 'pycdc'
        except FileNotFoundError:
            pass
        
        # Check common installation locations
        common_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'bin', 'pycdc'),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pycdc'),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'bin', 'pycdc'),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pycdc'),
            os.path.join(os.path.expanduser('~'), 'bin', 'pycdc'),
            os.path.join(os.path.expanduser('~'), '.local', 'bin', 'pycdc')
        ]
        
        for path in common_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                return path
        
        return None
    
    def decompile(self, bytecode_path: str, python_version: str) -> DecompilerResult:
        """Decompile bytecode using pycdc"""
        if not self.available:
            return DecompilerResult(False, error="pycdc is not installed or not found")
        
        try:
            # Run pycdc
            result = subprocess.run([self.pycdc_path, bytecode_path], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE, 
                                   check=False)
            
            if result.returncode == 0:
                source_code = result.stdout.decode('utf-8', errors='replace')
                return DecompilerResult(True, source_code=source_code)
            else:
                error = result.stderr.decode('utf-8', errors='replace')
                return DecompilerResult(False, error=f"pycdc error: {error}")
        except Exception as e:
            return DecompilerResult(False, error=f"pycdc error: {str(e)}")


class DecompilerBridge:
    """Bridge to multiple decompilers"""
    
    def __init__(self):
        self.decompilers = [
            Decompyle3Decompiler(),
            Unpyc37Decompiler(),
            PycdcDecompiler()
        ]
        
        # Filter out unavailable decompilers
        self.available_decompilers = [d for d in self.decompilers if d.available]
    
    def get_available_decompilers(self) -> List[str]:
        """Get names of available decompilers"""
        return [d.name for d in self.available_decompilers]
    
    def decompile(self, bytecode_path: str, python_version: str, preferred_decompiler: Optional[str] = None) -> DecompilerResult:
        """Decompile bytecode using available decompilers"""
        if not self.available_decompilers:
            return DecompilerResult(False, error="No decompilers available")
        
        # Try preferred decompiler first if specified
        if preferred_decompiler:
            for decompiler in self.available_decompilers:
                if decompiler.name.lower() == preferred_decompiler.lower():
                    result = decompiler.decompile(bytecode_path, python_version)
                    if result.success:
                        return result
        
        # Try all available decompilers
        errors = []
        for decompiler in self.available_decompilers:
            result = decompiler.decompile(bytecode_path, python_version)
            if result.success:
                return result
            errors.append(f"{decompiler.name}: {result.error}")
        
        # All decompilers failed
        return DecompilerResult(False, error=f"All decompilers failed: {'; '.join(errors)}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Python Decompiler Bridge')
    parser.add_argument('bytecode_file', help='Path to the Python bytecode file to decompile')
    parser.add_argument('--version', default='3.7', help='Python version of the bytecode (e.g., 3.7, 3.8)')
    parser.add_argument('--decompiler', help='Preferred decompiler to use (decompyle3, unpyc37, pycdc)')
    parser.add_argument('--json', action='store_true', help='Output in JSON format')
    
    args = parser.parse_args()
    
    bridge = DecompilerBridge()
    available_decompilers = bridge.get_available_decompilers()
    
    if not available_decompilers:
        print("Error: No decompilers available. Please install at least one of: decompyle3, unpyc37, pycdc", file=sys.stderr)
        sys.exit(1)
    
    result = bridge.decompile(args.bytecode_file, args.version, args.decompiler)
    
    if args.json:
        print(json.dumps(result.to_dict(), indent=2))
    else:
        if result.success:
            print(result.source_code)
        else:
            print(f"Error: {result.error}", file=sys.stderr)
            sys.exit(1)


if __name__ == '__main__':
    main()
