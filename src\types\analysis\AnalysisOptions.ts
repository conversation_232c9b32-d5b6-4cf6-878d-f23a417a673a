export interface AnalysisOptions {
  checkConflicts?: boolean;
  checkDependencies?: boolean;
  checkVersions?: boolean;
  maxResourceSize?: number;
  ignoreTypes?: string[];
  ignorePaths?: string[];
  validateResources?: boolean;
  validateTypes?: boolean;
  validateSizes?: boolean;
}

export interface ConflictDetectionOptions {
  checkVersions?: boolean;
  checkDependencies?: boolean;
  checkOverrides?: boolean;
  checkScripts?: boolean;
  checkTuning?: boolean;
  checkAssets?: boolean;
  checkData?: boolean;
  ignoreTypes?: string[];
  ignorePaths?: string[];
}

export interface ResourceAnalysisOptions {
  validateTypes?: boolean;
  validateSizes?: boolean;
  maxResourceSize?: number;
  ignoreTypes?: string[];
  ignorePaths?: string[];
}