/**
 * Decompiler service for Python bytecode in TS4Script files
 *
 * This service provides functionality to decompile Python bytecode to source code
 * using various decompilers with fallback mechanisms.
 *
 * Supported decompilers:
 * - decompyle3: For Python 3.7-3.8 (primary)
 * - unpyc37: Alternative for Python 3.7
 * - pycdc: C++ based decompiler (fallback)
 */

import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { Logger } from '../../../common/logger.js';
import { BytecodeParseResult } from '../bytecode/types.js';

/**
 * Result of decompilation attempt
 */
export interface DecompilationResult {
    success: boolean;
    sourceCode?: string;
    error?: string;
    decompilerUsed?: string;
    confidence: number; // 0-1 confidence score
    pythonVersion?: string;
}

/**
 * Options for decompilation
 */
export interface DecompilationOptions {
    tempDir?: string;
    timeout?: number; // milliseconds
    preferredDecompiler?: 'decompyle3' | 'unpyc37' | 'pycdc' | 'auto';
    cacheResults?: boolean;
    pythonVersion?: string; // Python version (e.g., '3.7', '3.8')
}

/**
 * Service for decompiling Python bytecode
 */
export class DecompilerService {
    private logger: Logger;
    private tempDir: string;
    private timeout: number;
    private cacheDir: string;
    private decompilersAvailable: {
        decompyle3: boolean;
        unpyc37: boolean;
        pycdc: boolean;
    };

    /**
     * Create a new DecompilerService
     * @param logger Logger instance
     */
    constructor(logger: Logger) {
        this.logger = logger;
        this.tempDir = os.tmpdir();
        this.timeout = 10000; // 10 seconds default timeout
        this.cacheDir = path.join(os.tmpdir(), 'ts4script_decompile_cache');

        // Create cache directory if it doesn't exist
        if (!fs.existsSync(this.cacheDir)) {
            try {
                fs.mkdirSync(this.cacheDir, { recursive: true });
            } catch (error) {
                this.logger.warn(`[DecompilerService] Failed to create cache directory: ${error}`);
            }
        }

        // Check which decompilers are available
        this.decompilersAvailable = {
            decompyle3: this.checkDecompilerAvailability('decompyle3'),
            unpyc37: this.checkDecompilerAvailability('unpyc37'),
            pycdc: this.checkDecompilerAvailability('pycdc'),
        };

        this.logger.info(`[DecompilerService] Initialized with available decompilers: ${
            Object.entries(this.decompilersAvailable)
                .filter(([_, available]) => available)
                .map(([name]) => name)
                .join(', ')
        }`);
    }

    /**
     * Check if a decompiler is available in the system
     * @param decompilerName Name of the decompiler executable
     * @returns true if available, false otherwise
     */
    private checkDecompilerAvailability(decompilerName: string): boolean {
        try {
            const result = spawn(decompilerName, ['--version'], { shell: true });
            return result.pid !== undefined;
        } catch (error) {
            return false;
        }
    }

    /**
     * Decompile Python bytecode using available decompilers
     * @param bytecode Python bytecode buffer
     * @param moduleName Name of the module (for caching)
     * @param options Decompilation options
     * @returns Decompilation result
     */
    public async decompileWithFallback(
        bytecode: Buffer,
        moduleName: string,
        options?: DecompilationOptions
    ): Promise<DecompilationResult> {
        const opts = this.mergeOptions(options);

        // Check cache first if enabled
        if (opts.cacheResults) {
            const cachedResult = this.checkCache(bytecode, moduleName);
            if (cachedResult) {
                this.logger.debug(`[DecompilerService] Using cached decompilation for ${moduleName}`);
                return cachedResult;
            }
        }

        // Try with bytecode parse result if available
        if (bytecode instanceof Object && 'codeObject' in bytecode) {
            const parseResult = bytecode as unknown as BytecodeParseResult;
            if (parseResult.codeObject) {
                // Use the bytecode information to help with decompilation
                this.logger.debug(`[DecompilerService] Using bytecode parse result for ${moduleName}`);
                return this.decompileFromCodeObject(parseResult, moduleName, opts);
            }
        }

        // Try decompilers in sequence based on preference
        const decompilers = this.getDecompilerSequence(opts.preferredDecompiler);

        for (const decompiler of decompilers) {
            if (!this.decompilersAvailable[decompiler]) {
                continue;
            }

            try {
                const result = await this.runDecompiler(decompiler, bytecode, moduleName, opts);

                if (result.success && result.sourceCode) {
                    // Cache successful result if enabled
                    if (opts.cacheResults) {
                        this.cacheResult(bytecode, moduleName, result);
                    }

                    return result;
                }
            } catch (error) {
                this.logger.warn(`[DecompilerService] Error using ${decompiler}: ${error}`);
            }
        }

        // If all decompilers fail, try AST extraction as last resort
        return this.extractASTFromBytecode(bytecode, moduleName);
    }

    /**
     * Get the sequence of decompilers to try based on preference
     * @param preferredDecompiler Preferred decompiler
     * @returns Array of decompiler names to try in sequence
     */
    private getDecompilerSequence(preferredDecompiler?: string): Array<keyof typeof this.decompilersAvailable> {
        if (preferredDecompiler === 'decompyle3' && this.decompilersAvailable.decompyle3) {
            return ['decompyle3', 'unpyc37', 'pycdc'];
        } else if (preferredDecompiler === 'unpyc37' && this.decompilersAvailable.unpyc37) {
            return ['unpyc37', 'decompyle3', 'pycdc'];
        } else if (preferredDecompiler === 'pycdc' && this.decompilersAvailable.pycdc) {
            return ['pycdc', 'decompyle3', 'unpyc37'];
        }

        // Default sequence (auto)
        return ['decompyle3', 'unpyc37', 'pycdc'];
    }

    /**
     * Run a specific decompiler on the bytecode
     * @param decompiler Decompiler to use
     * @param bytecode Python bytecode buffer
     * @param moduleName Name of the module
     * @param options Decompilation options
     * @returns Decompilation result
     */
    private async runDecompiler(
        decompiler: string,
        bytecode: Buffer,
        moduleName: string,
        options: DecompilationOptions
    ): Promise<DecompilationResult> {
        // Use Python bridge for decompilation if available
        if (decompiler === 'decompyle3' || decompiler === 'unpyc37') {
            try {
                const bridgeResult = await this.runPythonDecompilerBridge(bytecode, moduleName, {
                    ...options,
                    preferredDecompiler: decompiler
                });

                if (bridgeResult.success) {
                    return bridgeResult;
                }

                // If bridge fails, fall back to direct command
                this.logger.warn(`[DecompilerService] Python bridge failed for ${decompiler}, falling back to direct command`);
            } catch (error) {
                this.logger.warn(`[DecompilerService] Python bridge error: ${error}`);
            }
        }

        // Fall back to direct command execution
        const tempFilePath = path.join(options.tempDir, `${moduleName}_${Date.now()}.pyc`);

        try {
            // Write bytecode to temporary file
            fs.writeFileSync(tempFilePath, bytecode);

            // Prepare command based on decompiler
            let command: string;
            let args: string[];

            switch (decompiler) {
                case 'decompyle3':
                    command = 'decompyle3';
                    args = ['-o', '-', tempFilePath];
                    break;
                case 'unpyc37':
                    command = 'unpyc37';
                    args = [tempFilePath];
                    break;
                case 'pycdc':
                    command = 'pycdc';
                    args = [tempFilePath];
                    break;
                default:
                    throw new Error(`Unknown decompiler: ${decompiler}`);
            }

            // Run decompiler process
            return new Promise<DecompilationResult>((resolve) => {
                const process = spawn(command, args, { shell: true });

                let stdout = '';
                let stderr = '';
                let timeout: NodeJS.Timeout | null = null;

                // Set timeout
                if (options.timeout) {
                    timeout = setTimeout(() => {
                        process.kill();
                        resolve({
                            success: false,
                            error: `Decompilation timed out after ${options.timeout}ms`,
                            confidence: 0,
                            decompilerUsed: decompiler
                        });
                    }, options.timeout);
                }

                // Collect stdout
                process.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                // Collect stderr
                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                // Handle process completion
                process.on('close', (code) => {
                    if (timeout) {
                        clearTimeout(timeout);
                    }

                    // Clean up temporary file
                    try {
                        fs.unlinkSync(tempFilePath);
                    } catch (error) {
                        this.logger.warn(`[DecompilerService] Failed to delete temporary file: ${error}`);
                    }

                    if (code === 0 && stdout.trim()) {
                        // Successful decompilation
                        resolve({
                            success: true,
                            sourceCode: stdout,
                            confidence: this.calculateConfidence(stdout, decompiler),
                            decompilerUsed: decompiler
                        });
                    } else {
                        // Failed decompilation
                        resolve({
                            success: false,
                            error: stderr || `Decompiler exited with code ${code}`,
                            confidence: 0,
                            decompilerUsed: decompiler
                        });
                    }
                });

                // Handle process error
                process.on('error', (error) => {
                    if (timeout) {
                        clearTimeout(timeout);
                    }

                    // Clean up temporary file
                    try {
                        fs.unlinkSync(tempFilePath);
                    } catch (fileError) {
                        this.logger.warn(`[DecompilerService] Failed to delete temporary file: ${fileError}`);
                    }

                    resolve({
                        success: false,
                        error: error.message,
                        confidence: 0,
                        decompilerUsed: decompiler
                    });
                });
            });
        } catch (error) {
            // Clean up temporary file
            try {
                if (fs.existsSync(tempFilePath)) {
                    fs.unlinkSync(tempFilePath);
                }
            } catch (fileError) {
                this.logger.warn(`[DecompilerService] Failed to delete temporary file: ${fileError}`);
            }

            return {
                success: false,
                error: error.message,
                confidence: 0,
                decompilerUsed: decompiler
            };
        }
    }

    /**
     * Run Python decompiler bridge
     * @param bytecode Python bytecode buffer
     * @param moduleName Name of the module
     * @param options Decompilation options
     * @returns Decompilation result
     */
    private async runPythonDecompilerBridge(
        bytecode: Buffer,
        moduleName: string,
        options: DecompilationOptions
    ): Promise<DecompilationResult> {
        const tempDir = options.tempDir || os.tmpdir();
        const bytecodeFile = path.join(tempDir, `${moduleName}_${Date.now()}.pyc`);
        const scriptPath = path.join(__dirname, 'python', 'decompiler_bridge.py');

        try {
            // Write bytecode to temporary file
            fs.writeFileSync(bytecodeFile, bytecode);

            // Prepare command arguments
            const args = [
                scriptPath,
                bytecodeFile,
                '--json'
            ];

            // Add Python version if available
            if (options.pythonVersion) {
                args.push('--version', options.pythonVersion);
            }

            // Add preferred decompiler if specified
            if (options.preferredDecompiler && options.preferredDecompiler !== 'auto') {
                args.push('--decompiler', options.preferredDecompiler);
            }

            // Run decompiler bridge
            return new Promise<DecompilationResult>((resolve) => {
                const process = spawn('python', args, { shell: true });

                let stdout = '';
                let stderr = '';
                let timeout: NodeJS.Timeout | null = null;

                // Set timeout
                if (options.timeout) {
                    timeout = setTimeout(() => {
                        process.kill();
                        resolve({
                            success: false,
                            error: `Decompilation timed out after ${options.timeout}ms`,
                            confidence: 0,
                            decompilerUsed: 'python-bridge'
                        });
                    }, options.timeout);
                }

                // Collect stdout
                process.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                // Collect stderr
                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                // Handle process completion
                process.on('close', (code) => {
                    if (timeout) {
                        clearTimeout(timeout);
                    }

                    // Clean up temporary file
                    try {
                        fs.unlinkSync(bytecodeFile);
                    } catch (error) {
                        this.logger.warn(`[DecompilerService] Failed to delete temporary file: ${error}`);
                    }

                    if (code === 0 && stdout.trim()) {
                        try {
                            // Parse JSON output
                            const result = JSON.parse(stdout);

                            if (result.success && result.source_code) {
                                resolve({
                                    success: true,
                                    sourceCode: result.source_code,
                                    confidence: 0.9, // High confidence for Python bridge
                                    decompilerUsed: options.preferredDecompiler || 'python-bridge',
                                    pythonVersion: options.pythonVersion
                                });
                            } else {
                                resolve({
                                    success: false,
                                    error: result.error || 'Unknown error in Python bridge',
                                    confidence: 0,
                                    decompilerUsed: 'python-bridge'
                                });
                            }
                        } catch (error) {
                            resolve({
                                success: false,
                                error: `Failed to parse Python bridge output: ${error.message}`,
                                confidence: 0,
                                decompilerUsed: 'python-bridge'
                            });
                        }
                    } else {
                        resolve({
                            success: false,
                            error: stderr || `Python bridge exited with code ${code}`,
                            confidence: 0,
                            decompilerUsed: 'python-bridge'
                        });
                    }
                });

                // Handle process error
                process.on('error', (error) => {
                    if (timeout) {
                        clearTimeout(timeout);
                    }

                    // Clean up temporary file
                    try {
                        fs.unlinkSync(bytecodeFile);
                    } catch (fileError) {
                        this.logger.warn(`[DecompilerService] Failed to delete temporary file: ${fileError}`);
                    }

                    resolve({
                        success: false,
                        error: `Python bridge error: ${error.message}`,
                        confidence: 0,
                        decompilerUsed: 'python-bridge'
                    });
                });
            });
        } catch (error) {
            // Clean up temporary file
            try {
                if (fs.existsSync(bytecodeFile)) {
                    fs.unlinkSync(bytecodeFile);
                }
            } catch (fileError) {
                this.logger.warn(`[DecompilerService] Failed to delete temporary file: ${fileError}`);
            }

            return {
                success: false,
                error: `Failed to run Python bridge: ${error.message}`,
                confidence: 0,
                decompilerUsed: 'python-bridge'
            };
        }
    }

    /**
     * Calculate confidence score for decompilation result
     * @param sourceCode Decompiled source code
     * @param decompiler Decompiler used
     * @returns Confidence score (0-1)
     */
    private calculateConfidence(sourceCode: string, decompiler: string): number {
        // Check if source code is empty or very short
        if (!sourceCode || sourceCode.length < 10) {
            return 0;
        }

        // Check for common error indicators
        const errorIndicators = [
            'Traceback (most recent call last)',
            'SyntaxError',
            'Error decompiling',
            'Decompilation error',
            'Unknown opcode',
            'Unsupported opcode',
            '# WARNING:',
            '# WARN:',
            '# ERROR:'
        ];

        for (const indicator of errorIndicators) {
            if (sourceCode.includes(indicator)) {
                return 0.2; // Low confidence if error indicators are present
            }
        }

        // Check for common success indicators
        const successIndicators = [
            'def ',
            'class ',
            'import ',
            'from ',
            'return ',
            'if ',
            'for ',
            'while '
        ];

        let successCount = 0;
        for (const indicator of successIndicators) {
            if (sourceCode.includes(indicator)) {
                successCount++;
            }
        }

        // Base confidence on success indicators and decompiler reliability
        let confidence = Math.min(0.5 + (successCount / successIndicators.length) * 0.5, 1);

        // Adjust based on decompiler reliability
        switch (decompiler) {
            case 'decompyle3':
                confidence *= 0.95; // Most reliable for Python 3.7-3.8
                break;
            case 'unpyc37':
                confidence *= 0.9; // Good for Python 3.7
                break;
            case 'pycdc':
                confidence *= 0.85; // Less reliable but more versatile
                break;
            default:
                confidence *= 0.8;
                break;
        }

        return confidence;
    }

    /**
     * Merge default options with provided options
     * @param options User-provided options
     * @returns Merged options
     */
    private mergeOptions(options?: DecompilationOptions): DecompilationOptions {
        return {
            tempDir: options?.tempDir || this.tempDir,
            timeout: options?.timeout || this.timeout,
            preferredDecompiler: options?.preferredDecompiler || 'auto',
            cacheResults: options?.cacheResults !== undefined ? options.cacheResults : true
        };
    }

    /**
     * Check cache for decompilation result
     * @param bytecode Python bytecode buffer
     * @param moduleName Name of the module
     * @returns Cached decompilation result or null if not found
     */
    private checkCache(bytecode: Buffer, moduleName: string): DecompilationResult | null {
        try {
            // Generate cache key based on bytecode hash
            const hash = require('crypto').createHash('md5').update(bytecode).digest('hex');
            const cacheFile = path.join(this.cacheDir, `${moduleName}_${hash}.json`);

            if (fs.existsSync(cacheFile)) {
                const cacheData = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
                return cacheData as DecompilationResult;
            }
        } catch (error) {
            this.logger.warn(`[DecompilerService] Cache check failed: ${error}`);
        }

        return null;
    }

    /**
     * Cache decompilation result
     * @param bytecode Python bytecode buffer
     * @param moduleName Name of the module
     * @param result Decompilation result
     */
    private cacheResult(bytecode: Buffer, moduleName: string, result: DecompilationResult): void {
        try {
            // Generate cache key based on bytecode hash
            const hash = require('crypto').createHash('md5').update(bytecode).digest('hex');
            const cacheFile = path.join(this.cacheDir, `${moduleName}_${hash}.json`);

            fs.writeFileSync(cacheFile, JSON.stringify(result), 'utf8');
        } catch (error) {
            this.logger.warn(`[DecompilerService] Cache write failed: ${error}`);
        }
    }

    /**
     * Decompile from code object (when bytecode parsing was successful)
     * @param parseResult Bytecode parse result
     * @param moduleName Name of the module
     * @param options Decompilation options
     * @returns Decompilation result
     */
    private decompileFromCodeObject(
        parseResult: BytecodeParseResult,
        moduleName: string,
        options: DecompilationOptions
    ): Promise<DecompilationResult> {
        // If we have a code object, we can try to reconstruct Python source
        // This is a simplified approach - in a real implementation, we would
        // use the code object structure to generate Python source code

        if (!parseResult.codeObject) {
            return Promise.resolve({
                success: false,
                error: 'No code object available',
                confidence: 0
            });
        }

        // For now, we'll still use external decompilers but with enhanced confidence
        return this.runDecompiler('decompyle3', Buffer.from(parseResult.bytecode), moduleName, options)
            .then(result => {
                if (result.success) {
                    // Increase confidence since we have a valid code object
                    result.confidence = Math.min(result.confidence + 0.2, 1);
                }
                return result;
            });
    }

    /**
     * Extract AST from bytecode as a last resort
     * @param bytecode Python bytecode buffer
     * @param moduleName Name of the module
     * @returns Decompilation result with minimal information
     */
    private extractASTFromBytecode(bytecode: Buffer, moduleName: string): DecompilationResult {
        try {
            // Extract string literals and other basic information
            const stringLiterals: string[] = [];
            let offset = 0;

            // Skip header (12 bytes)
            offset += 12;

            // Scan for string literals (look for string length markers followed by UTF-8 text)
            while (offset < bytecode.length - 4) {
                // Look for potential string length markers (1-byte type + 3-byte length)
                const possibleType = bytecode[offset];
                if (possibleType === 0x73 || possibleType === 0x74) { // 's' or 't' in ASCII
                    const length = bytecode.readUInt32LE(offset + 1) & 0xFFFFFF; // 3-byte length

                    if (length > 0 && length < 1000 && offset + 4 + length <= bytecode.length) {
                        try {
                            const text = bytecode.slice(offset + 4, offset + 4 + length).toString('utf8');

                            // Check if it's a valid string (only printable ASCII or common Unicode)
                            if (/^[\x20-\x7E\u00A0-\u00FF\u2000-\u206F]+$/.test(text)) {
                                stringLiterals.push(text);
                            }
                        } catch (e) {
                            // Ignore decoding errors
                        }
                    }
                }

                offset++;
            }

            // Generate a minimal "decompiled" source with extracted information
            const sourceLines: string[] = [
                `# Partial decompilation of ${moduleName}`,
                '# Only string literals could be extracted',
                '',
                '"""',
                'This is a minimal reconstruction based on string extraction.',
                'The original code structure could not be determined.',
                '"""',
                '',
                '# String literals found in the bytecode:',
                ...stringLiterals.map(s => `# "${s}"`),
                '',
                'def main():',
                '    """Entry point (reconstructed)"""',
                '    pass',
                '',
                'if __name__ == "__main__":',
                '    main()',
                ''
            ];

            return {
                success: true,
                sourceCode: sourceLines.join('\n'),
                confidence: 0.1, // Very low confidence
                decompilerUsed: 'string-extraction'
            };
        } catch (error) {
            return {
                success: false,
                error: `Failed to extract AST from bytecode: ${error.message}`,
                confidence: 0
            };
        }
    }
}
