#!/usr/bin/env python
"""
AST Parser for Sims 4 Python Scripts

This script parses Python source code into an Abstract Syntax Tree (AST)
and extracts useful information from it, including:
- Classes and their methods
- Functions and their parameters
- Imports
- Tuning references
- EA-specific patterns

Usage:
    python ast_parser.py <source_file> [--json] [--ea-patterns] [--docstrings]

Arguments:
    source_file     Path to the Python source file or '-' to read from stdin
    --json          Output in JSON format (default)
    --ea-patterns   Analyze EA-specific patterns
    --docstrings    Extract docstrings
"""

import ast
import json
import sys
import re
import argparse
from typing import Dict, List, Any, Optional, Set, Tuple


class EAPatternVisitor(ast.NodeVisitor):
    """Visitor that identifies EA-specific patterns in the code"""
    
    def __init__(self):
        self.commands = []
        self.injections = []
        self.event_handlers = []
        self.tuning_references = []
        self.ea_classes = set()
    
    def visit_ClassDef(self, node):
        """Visit class definitions to identify EA-specific classes"""
        # Check for EA base classes
        for base in node.bases:
            if isinstance(base, ast.Name) and base.id in [
                'Component', 'Service', 'Interaction', 'SuperInteraction',
                'SocialSuperInteraction', 'MixerInteraction', 'ImmediateSuperInteraction',
                'Situation', 'SituationState', 'SituationJob', 'Zone', 'Venue',
                'Buff', 'BuffData', 'Trait', 'TraitData', 'Lot', 'LotTuning',
                'ObjectStateValue', 'ObjectState', 'Commodity', 'Statistic',
                'CommodityData', 'StatisticData', 'Aspiration', 'AspirationTrack',
                'Career', 'CareerTrack', 'CareerLevel', 'Skill', 'SkillData'
            ]:
                self.ea_classes.add(node.name)
        
        # Visit all child nodes
        self.generic_visit(node)
    
    def visit_Call(self, node):
        """Visit function calls to identify EA-specific patterns"""
        # Check for command registration
        if isinstance(node.func, ast.Attribute) and node.func.attr == 'Command':
            if len(node.args) >= 1 and isinstance(node.args[0], ast.Constant):
                command_name = node.args[0].value
                self.commands.append({
                    'name': command_name,
                    'line': node.lineno
                })
        
        # Check for injection registration
        if isinstance(node.func, ast.Attribute) and node.func.attr in ['inject', 'injected', 'inject_to']:
            if len(node.args) >= 2:
                target = None
                if isinstance(node.args[0], ast.Name):
                    target = node.args[0].id
                elif isinstance(node.args[0], ast.Attribute):
                    target = f"{self._get_attribute_path(node.args[0])}"
                
                if target:
                    self.injections.append({
                        'target': target,
                        'line': node.lineno
                    })
        
        # Check for event handlers
        if isinstance(node.func, ast.Attribute) and node.func.attr in ['register_event_handler', 'on_event']:
            if len(node.args) >= 1:
                event_type = None
                if isinstance(node.args[0], ast.Name):
                    event_type = node.args[0].id
                elif isinstance(node.args[0], ast.Attribute):
                    event_type = f"{self._get_attribute_path(node.args[0])}"
                
                if event_type:
                    self.event_handlers.append({
                        'event_type': event_type,
                        'line': node.lineno
                    })
        
        # Check for tuning references
        if isinstance(node.func, ast.Name) and node.func.id in ['TunableReference', 'TunableList', 'TunableTuple']:
            for keyword in node.keywords:
                if keyword.arg == 'manager' and isinstance(keyword.value, ast.Name):
                    manager_type = keyword.value.id
                    self.tuning_references.append({
                        'type': manager_type,
                        'line': node.lineno
                    })
        
        # Visit all child nodes
        self.generic_visit(node)
    
    def _get_attribute_path(self, node):
        """Get the full path of an attribute (e.g., sims.sim.Sim)"""
        if isinstance(node.value, ast.Attribute):
            return f"{self._get_attribute_path(node.value)}.{node.attr}"
        elif isinstance(node.value, ast.Name):
            return f"{node.value.id}.{node.attr}"
        return node.attr


class Sims4AstParser:
    """Parser for Sims 4 Python scripts"""
    
    def __init__(self, analyze_ea_patterns=True, extract_docstrings=True):
        self.analyze_ea_patterns = analyze_ea_patterns
        self.extract_docstrings = extract_docstrings
    
    def parse(self, source_code: str) -> Dict[str, Any]:
        """Parse Python source code and extract information"""
        try:
            tree = ast.parse(source_code)
            
            # Basic information
            classes = self._extract_classes(tree)
            functions = self._extract_functions(tree)
            imports = self._extract_imports(tree)
            globals_vars = self._extract_globals(tree)
            constants = self._extract_constants(tree)
            
            # EA-specific patterns
            tuning_references = []
            if self.analyze_ea_patterns:
                ea_visitor = EAPatternVisitor()
                ea_visitor.visit(tree)
                
                # Update classes with EA information
                for cls in classes:
                    if cls['name'] in ea_visitor.ea_classes:
                        cls['isEAClass'] = True
                
                # Update functions with EA information
                for func in functions:
                    # Check if function is a command
                    for cmd in ea_visitor.commands:
                        if func['startLine'] <= cmd['line'] <= func['endLine']:
                            func['isCommand'] = True
                    
                    # Check if function is an injection
                    for inj in ea_visitor.injections:
                        if func['startLine'] <= inj['line'] <= func['endLine']:
                            func['isInjection'] = True
                            func['injectionTarget'] = inj.get('target')
                    
                    # Check if function is an event handler
                    for handler in ea_visitor.event_handlers:
                        if func['startLine'] <= handler['line'] <= func['endLine']:
                            func['isEventHandler'] = True
                            func['eventType'] = handler.get('event_type')
                
                tuning_references = ea_visitor.tuning_references
            
            return {
                'success': True,
                'classes': classes,
                'functions': functions,
                'imports': imports,
                'tuningReferences': tuning_references,
                'globalVariables': globals_vars,
                'constants': constants
            }
        
        except SyntaxError as e:
            return {
                'success': False,
                'error': f"Syntax error: {str(e)}",
                'classes': [],
                'functions': [],
                'imports': [],
                'tuningReferences': [],
                'globalVariables': [],
                'constants': {}
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Error parsing AST: {str(e)}",
                'classes': [],
                'functions': [],
                'imports': [],
                'tuningReferences': [],
                'globalVariables': [],
                'constants': {}
            }
    
    def _extract_classes(self, tree: ast.Module) -> List[Dict[str, Any]]:
        """Extract class information from the AST"""
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                parent_classes = []
                for base in node.bases:
                    if isinstance(base, ast.Name):
                        parent_classes.append(base.id)
                    elif isinstance(base, ast.Attribute):
                        parent_classes.append(self._get_attribute_name(base))
                
                methods = []
                properties = []
                
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        methods.append(self._extract_function_info(item, is_method=True))
                    elif isinstance(item, ast.Assign):
                        for target in item.targets:
                            if isinstance(target, ast.Name):
                                properties.append(target.id)
                
                decorators = [self._get_decorator_name(d) for d in node.decorator_list]
                
                docstring = None
                if self.extract_docstrings and node.body and isinstance(node.body[0], ast.Expr) and isinstance(node.body[0].value, ast.Constant):
                    docstring = node.body[0].value.value
                
                classes.append({
                    'name': node.name,
                    'parentClasses': parent_classes,
                    'methods': methods,
                    'properties': properties,
                    'decorators': decorators,
                    'docstring': docstring,
                    'startLine': node.lineno,
                    'endLine': node.end_lineno if hasattr(node, 'end_lineno') else node.lineno,
                    'isEAClass': False  # Will be updated later if EA patterns are analyzed
                })
        
        return classes
    
    def _extract_functions(self, tree: ast.Module) -> List[Dict[str, Any]]:
        """Extract function information from the AST"""
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and not self._is_class_method(node):
                functions.append(self._extract_function_info(node))
        
        return functions
    
    def _extract_function_info(self, node: ast.FunctionDef, is_method: bool = False) -> Dict[str, Any]:
        """Extract information about a function or method"""
        parameters = []
        for arg in node.args.args:
            parameters.append(arg.arg)
        
        decorators = [self._get_decorator_name(d) for d in node.decorator_list]
        
        is_static_method = '@staticmethod' in decorators or 'staticmethod' in decorators
        is_class_method = '@classmethod' in decorators or 'classmethod' in decorators
        is_property = '@property' in decorators or 'property' in decorators
        
        docstring = None
        if self.extract_docstrings and node.body and isinstance(node.body[0], ast.Expr) and isinstance(node.body[0].value, ast.Constant):
            docstring = node.body[0].value.value
        
        # Extract called functions and accessed attributes
        called_functions = []
        accessed_attributes = []
        for subnode in ast.walk(node):
            if isinstance(subnode, ast.Call) and isinstance(subnode.func, ast.Name):
                called_functions.append(subnode.func.id)
            elif isinstance(subnode, ast.Call) and isinstance(subnode.func, ast.Attribute):
                called_functions.append(self._get_attribute_name(subnode.func))
            elif isinstance(subnode, ast.Attribute) and isinstance(subnode.value, ast.Name):
                accessed_attributes.append(f"{subnode.value.id}.{subnode.attr}")
        
        return {
            'name': node.name,
            'parameters': parameters,
            'decorators': decorators,
            'isMethod': is_method,
            'isStaticMethod': is_static_method,
            'isClassMethod': is_class_method,
            'isProperty': is_property,
            'isCommand': False,  # Will be updated later if EA patterns are analyzed
            'isInjection': False,  # Will be updated later if EA patterns are analyzed
            'isEventHandler': False,  # Will be updated later if EA patterns are analyzed
            'docstring': docstring,
            'startLine': node.lineno,
            'endLine': node.end_lineno if hasattr(node, 'end_lineno') else node.lineno,
            'calledFunctions': list(set(called_functions)),
            'accessedAttributes': list(set(accessed_attributes))
        }
    
    def _extract_imports(self, tree: ast.Module) -> List[Dict[str, Any]]:
        """Extract import information from the AST"""
        imports = []
        
        for node in tree.body:
            if isinstance(node, ast.Import):
                for name in node.names:
                    imports.append({
                        'module': name.name,
                        'name': name.name,
                        'alias': name.asname,
                        'isFromImport': False,
                        'line': node.lineno
                    })
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for name in node.names:
                    imports.append({
                        'module': module,
                        'name': name.name,
                        'alias': name.asname,
                        'isFromImport': True,
                        'line': node.lineno
                    })
        
        return imports
    
    def _extract_globals(self, tree: ast.Module) -> List[str]:
        """Extract global variables from the AST"""
        globals_vars = []
        
        for node in tree.body:
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        globals_vars.append(target.id)
        
        return globals_vars
    
    def _extract_constants(self, tree: ast.Module) -> Dict[str, Any]:
        """Extract constants from the AST"""
        constants = {}
        
        for node in tree.body:
            if isinstance(node, ast.Assign) and all(isinstance(target, ast.Name) for target in node.targets):
                # Check if the name is all uppercase (conventional for constants)
                for target in node.targets:
                    if target.id.isupper():
                        if isinstance(node.value, ast.Constant):
                            constants[target.id] = node.value.value
                        elif isinstance(node.value, ast.List):
                            constants[target.id] = '[list]'
                        elif isinstance(node.value, ast.Dict):
                            constants[target.id] = '{dict}'
                        elif isinstance(node.value, ast.Tuple):
                            constants[target.id] = '(tuple)'
                        else:
                            constants[target.id] = '[complex value]'
        
        return constants
    
    def _is_class_method(self, node: ast.FunctionDef) -> bool:
        """Check if a function definition is inside a class"""
        for parent in ast.walk(ast.Module(body=[])):
            for child in ast.iter_child_nodes(parent):
                if child == node and isinstance(parent, ast.ClassDef):
                    return True
        return False
    
    def _get_decorator_name(self, node: ast.expr) -> str:
        """Get the name of a decorator"""
        if isinstance(node, ast.Name):
            return f"@{node.id}"
        elif isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
            return f"@{node.func.id}"
        elif isinstance(node, ast.Attribute):
            return f"@{self._get_attribute_name(node)}"
        return "@unknown"
    
    def _get_attribute_name(self, node: ast.Attribute) -> str:
        """Get the full name of an attribute (e.g., sims.sim.Sim)"""
        if isinstance(node.value, ast.Attribute):
            return f"{self._get_attribute_name(node.value)}.{node.attr}"
        elif isinstance(node.value, ast.Name):
            return f"{node.value.id}.{node.attr}"
        return node.attr


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Parse Python source code into AST')
    parser.add_argument('source_file', help='Path to the Python source file or "-" to read from stdin')
    parser.add_argument('--json', action='store_true', help='Output in JSON format (default)')
    parser.add_argument('--ea-patterns', action='store_true', help='Analyze EA-specific patterns')
    parser.add_argument('--docstrings', action='store_true', help='Extract docstrings')
    
    args = parser.parse_args()
    
    # Read source code
    if args.source_file == '-':
        source_code = sys.stdin.read()
    else:
        with open(args.source_file, 'r', encoding='utf-8') as f:
            source_code = f.read()
    
    # Parse the source code
    parser = Sims4AstParser(
        analyze_ea_patterns=args.ea_patterns,
        extract_docstrings=args.docstrings
    )
    result = parser.parse(source_code)
    
    # Output the result
    print(json.dumps(result, indent=2))


if __name__ == '__main__':
    main()
