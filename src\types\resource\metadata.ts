﻿// Corrected imports
import { BinaryResourceType } from './core.js';
import { ResourceMetadata } from './interfaces.js'; // Import from interfaces.js
import { ResourceCategory } from './enums.js'; // Import from enums.js

/**
 * Resource metadata collection interface
 */
// Note: This collection likely needs to be updated to use the ResourceMetadata from interfaces.js
// if it's intended to store those objects. For now, just removing the conflicting interface.
export interface ResourceMetadataCollection {
  entries: ResourceMetadata[];
  map: Map<string, ResourceMetadata>;
  add(metadata: ResourceMetadata): void;
  get(key: string): ResourceMetadata | undefined;
  has(key: string): boolean;
  remove(key: string): boolean;
  clear(): void;
  size(): number;
}

/**
 * Consolidated Resource Type Metadata Interface
 * Combines properties from both legacy and official resource type metadata
 */
export interface ResourceTypeMetadata {
  // Core identification (legacy properties)
  type: BinaryResourceType;
  name: string;
  description: string;
  category: ResourceCategory;

  // Official game properties (from InstanceTuningDefinition)
  /** Resource type name (alternative to name) */
  typeName?: string;
  /** Plural form of type name */
  typeNamePlural?: string;
  /** File extension for this resource type */
  fileExtension?: string;
  /** Numeric resource type ID (alternative to type) */
  resourceType?: number;
  /** Manager name for this resource type */
  managerName?: string;
  /** Manager type classification */
  managerType?: string;
  /** Whether to use GUID for references */
  useGuidForRef?: boolean;
  /** Whether this is base game only */
  baseGameOnly?: boolean;
  /** Whether this resource type requires references */
  requireReference?: boolean;

  // Legacy application properties
  version?: string;
  author?: string;
  dependencies?: string[];
  conflicts?: string[];
  customData?: {
    [key: string]: unknown;
  };

  // Additional properties needed by the application
  mimeTypes?: string[];
  isCompressible?: boolean;
  isEncryptable?: boolean;
  isModifiable?: boolean;
  hasCustomData?: boolean;
  customDataSchema?: {
    [key: string]: {
      type: string;
      description: string;
      required: boolean;
    };
  };
}
