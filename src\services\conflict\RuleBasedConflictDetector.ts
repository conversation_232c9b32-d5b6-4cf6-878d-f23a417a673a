import { ConflictInfo, ConflictSeverity, ConflictType } from '../../types/conflict/index.js';
import { ResourceInfo } from '../../types/database.js';
import { ResourceKey } from '../../types/resource/interfaces.js';
import { compareResourceKeys } from '../../utils/resource/comparers.js';
import { formatTgi } from '../../utils/resource/formatters.js';
import { Logger } from '../../utils/logging/logger.js';
import { resourceTypeRegistry } from '../../utils/resource/resourceTypeRegistry.js';
import { DependencyInfo } from '../databaseService.js';

const logger = new Logger('RuleBasedConflictDetector');

/**
 * A map of dependencies for resources
 */
export type DependencyMap = Map<number, DependencyInfo[]>;

/**
 * Rule-based conflict detector that uses predefined rules to detect conflicts
 * between resources without requiring LLM assistance.
 */
export class RuleBasedConflictDetector {
    /**
     * Detect conflicts between resources based on TGI (Type-Group-Instance) matching
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    detectTgiConflicts(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        if (compareResourceKeys(resource1.key, resource2.key)) {
            const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.key.type);
            const severity = this.determineSeverity(resource1.key.type);

            return {
                id: `tgi-${resource1.key.type}-${resource1.key.group}-${resource1.key.instance}`,
                type: ConflictType.RESOURCE,
                severity: severity,
                description: `Resources with identical TGI (${formatTgi(resource1.key)})`,
                affectedResources: [resource1.key, resource2.key],
                timestamp: Date.now(),
                recommendations: this.getRecommendations(resource1.key.type)
            };
        }
        return null;
    }

    /**
     * Detect conflicts between resources based on their dependencies
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @param dependencies Map of resource IDs to their dependencies
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    detectDependencyConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo,
        dependencies: DependencyMap
    ): ConflictInfo | null {
        // Skip if either resource doesn't have an ID or metadata
        if (!resource1.id || !resource2.id || !resource1.metadata || !resource2.metadata) {
            return null;
        }

        // Get dependencies for both resources
        const deps1 = dependencies.get(resource1.id) || [];
        const deps2 = dependencies.get(resource2.id) || [];

        // Skip if either resource has no dependencies
        if (deps1.length === 0 || deps2.length === 0) {
            return null;
        }

        // Check for common dependencies
        const commonDeps: DependencyInfo[] = [];

        for (const dep1 of deps1) {
            for (const dep2 of deps2) {
                if (dep1.targetType === dep2.targetType &&
                    dep1.targetGroup === dep2.targetGroup &&
                    dep1.targetInstance === dep2.targetInstance) {
                    commonDeps.push(dep1);
                    break;
                }
            }
        }

        // If there are common dependencies, create a conflict
        if (commonDeps.length > 0) {
            return {
                id: `dep-${resource1.id}-${resource2.id}`,
                type: ConflictType.DEPENDENCY,
                severity: ConflictSeverity.MEDIUM,
                description: `Resources share ${commonDeps.length} common dependencies`,
                affectedResources: [resource1.key, resource2.key],
                timestamp: Date.now(),
                recommendations: [
                    'Check if both mods modify the same game functionality',
                    'Load mods in the correct order to ensure proper overrides',
                    'Consider using only one of the conflicting mods'
                ]
            };
        }

        return null;
    }

    /**
     * Detect conflicts between resources based on their content
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    detectContentConflicts(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Skip if either resource doesn't have metadata
        if (!resource1.metadata || !resource2.metadata) {
            return null;
        }

        // Check for specific content conflicts based on resource type
        switch (resource1.metadata.resourceType) {
            case 'TUNING':
            case 'XML':
                return this.detectTuningConflicts(resource1, resource2);
            case 'SIMDATA':
                return this.detectSimDataConflicts(resource1, resource2);
            case 'SCRIPT':
            case 'SCRIPT_MODULE':
                return this.detectScriptConflicts(resource1, resource2);
            case 'CASPART':
                return this.detectCasPartConflicts(resource1, resource2);
            default:
                return null;
        }
    }

    /**
     * Detect conflicts between tuning resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectTuningConflicts(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // This is a simplified implementation
        // A real implementation would parse and compare the tuning XML

        // For now, just check if the resources have the same name but different content
        if (resource1.metadata?.name &&
            resource2.metadata?.name &&
            resource1.metadata.name === resource2.metadata.name &&
            resource1.metadata.hash !== resource2.metadata.hash) {

            return {
                id: `tuning-${resource1.id}-${resource2.id}`,
                type: ConflictType.TUNING,
                severity: ConflictSeverity.HIGH,
                description: `Tuning resources with same name (${resource1.metadata.name}) but different content`,
                affectedResources: [resource1.key, resource2.key],
                timestamp: Date.now(),
                recommendations: [
                    'Use Sims 4 Studio or XML Compare to view differences',
                    'Load conflicting mods last if one should override the other',
                    'Merge changes or choose one version'
                ]
            };
        }

        return null;
    }

    /**
     * Detect conflicts between script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectScriptConflicts(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // This is a simplified implementation
        // A real implementation would analyze the script content

        // For now, just check if the resources have the same name but different content
        if (resource1.metadata?.name &&
            resource2.metadata?.name &&
            resource1.metadata.name === resource2.metadata.name &&
            resource1.metadata.hash !== resource2.metadata.hash) {

            return {
                id: `script-${resource1.id}-${resource2.id}`,
                type: ConflictType.SCRIPT,
                severity: ConflictSeverity.CRITICAL,
                description: `Script resources with same name (${resource1.metadata.name}) but different content`,
                affectedResources: [resource1.key, resource2.key],
                timestamp: Date.now(),
                recommendations: [
                    'Script conflicts can cause game crashes',
                    'Only use one of the conflicting script mods',
                    'Check if updated versions are available'
                ]
            };
        }

        return null;
    }

    /**
     * Detect conflicts between SimData resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectSimDataConflicts(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Check for schema name conflicts
        if (resource1.metadata?.simDataSchemaName &&
            resource2.metadata?.simDataSchemaName &&
            resource1.metadata.simDataSchemaName === resource2.metadata.simDataSchemaName &&
            resource1.metadata.hash !== resource2.metadata.hash) {

            return {
                id: `simdata-schema-${resource1.id}-${resource2.id}`,
                type: ConflictType.TUNING,
                severity: ConflictSeverity.HIGH,
                description: `SimData resources with same schema (${resource1.metadata.simDataSchemaName}) but different content`,
                affectedResources: [resource1.key, resource2.key],
                timestamp: Date.now(),
                recommendations: [
                    'Check if both mods modify the same game functionality',
                    'Load mods in the correct order to ensure proper overrides',
                    'Consider using only one of the conflicting mods',
                    'Use a conflict resolution patch if available'
                ]
            };
        }

        // Check for critical gameplay column conflicts
        if (resource1.metadata?.simDataCriticalGameplayColumns &&
            resource2.metadata?.simDataCriticalGameplayColumns) {

            const criticalColumns1 = JSON.parse(resource1.metadata.simDataCriticalGameplayColumns);
            const criticalColumns2 = JSON.parse(resource2.metadata.simDataCriticalGameplayColumns);

            // Check if both resources have critical gameplay columns
            if (criticalColumns1.length > 0 && criticalColumns2.length > 0) {
                // Find common critical columns
                const commonColumns = criticalColumns1.filter(col => criticalColumns2.includes(col));

                if (commonColumns.length > 0) {
                    return {
                        id: `simdata-critical-${resource1.id}-${resource2.id}`,
                        type: ConflictType.TUNING,
                        severity: ConflictSeverity.HIGH,
                        description: `SimData resources modify the same critical gameplay columns: ${commonColumns.join(', ')}`,
                        affectedResources: [resource1.key, resource2.key],
                        timestamp: Date.now(),
                        recommendations: [
                            'Check if both mods modify the same game functionality',
                            'Load mods in the correct order to ensure proper overrides',
                            'Consider using only one of the conflicting mods',
                            'Use a conflict resolution patch if available'
                        ]
                    };
                }
            }
        }

        // Check for instance conflicts
        if (resource1.metadata?.simDataInstanceCount &&
            resource2.metadata?.simDataInstanceCount &&
            resource1.metadata.simDataInstanceCount > 0 &&
            resource2.metadata.simDataInstanceCount > 0) {

            // If both resources have instances and the same schema name, they might conflict
            if (resource1.metadata.simDataSchemaName &&
                resource2.metadata.simDataSchemaName &&
                resource1.metadata.simDataSchemaName === resource2.metadata.simDataSchemaName) {

                return {
                    id: `simdata-instance-${resource1.id}-${resource2.id}`,
                    type: ConflictType.TUNING,
                    severity: ConflictSeverity.MEDIUM,
                    description: `SimData resources with same schema (${resource1.metadata.simDataSchemaName}) and instances might conflict`,
                    affectedResources: [resource1.key, resource2.key],
                    timestamp: Date.now(),
                    recommendations: [
                        'Check if both mods modify the same game functionality',
                        'Load mods in the correct order to ensure proper overrides',
                        'Consider using only one of the conflicting mods',
                        'Use a conflict resolution patch if available'
                    ]
                };
            }
        }

        return null;
    }

    /**
     * Detect conflicts between CAS part resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    private detectCasPartConflicts(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Check for CAS parts with same body type and age/gender flags
        if (resource1.metadata?.casBodyTypeRaw &&
            resource2.metadata?.casBodyTypeRaw &&
            resource1.metadata.casBodyTypeRaw === resource2.metadata.casBodyTypeRaw &&
            resource1.metadata.casAgeGenderFlagsRaw === resource2.metadata.casAgeGenderFlagsRaw) {

            return {
                id: `caspart-${resource1.id}-${resource2.id}`,
                type: ConflictType.CASPART,
                severity: ConflictSeverity.MEDIUM,
                description: `CAS parts with same body type and age/gender flags`,
                affectedResources: [resource1.key, resource2.key],
                timestamp: Date.now(),
                recommendations: [
                    'These CAS parts may replace each other in-game',
                    'Check if they are intended to be alternatives',
                    'Load preferred mod last for it to take precedence'
                ]
            };
        }

        return null;
    }

    /**
     * Determine the severity of a conflict based on resource type
     * @param resourceType The numeric resource type ID
     * @returns The appropriate conflict severity
     */
    private determineSeverity(resourceType: number): ConflictSeverity {
        // Get resource type info
        const typeInfo = resourceTypeRegistry.getInfo(resourceType);

        // Determine severity based on resource category
        switch (typeInfo.category) {
            case 'SCRIPT':
                return ConflictSeverity.CRITICAL;
            case 'TUNING':
                return ConflictSeverity.HIGH;
            case 'OBJECT':
            case 'ANIMATION':
                return ConflictSeverity.MEDIUM;
            case 'IMAGE':
            case 'SOUND':
                return ConflictSeverity.LOW;
            default:
                return ConflictSeverity.MEDIUM;
        }
    }

    /**
     * Get recommendations for resolving conflicts based on resource type
     * @param resourceType The numeric resource type ID
     * @returns Array of recommendation strings
     */
    private getRecommendations(resourceType: number): string[] {
        // Get resource type info
        const typeInfo = resourceTypeRegistry.getInfo(resourceType);

        // Determine recommendations based on resource category
        switch (typeInfo.category) {
            case 'SCRIPT':
                return [
                    'Script conflicts can cause game crashes',
                    'Only use one of the conflicting script mods',
                    'Check if updated versions are available'
                ];
            case 'TUNING':
                return [
                    'Use Sims 4 Studio or XML Compare to view differences',
                    'Load conflicting mods last if one should override the other',
                    'Merge changes or choose one version'
                ];
            case 'OBJECT':
                return [
                    'These objects may replace each other in-game',
                    'Check if they are intended to be alternatives',
                    'Load preferred mod last for it to take precedence'
                ];
            case 'ANIMATION':
                return [
                    'Animation conflicts may cause visual glitches',
                    'Only use one of the conflicting animation mods',
                    'Check if the animations are intended to be alternatives'
                ];
            case 'IMAGE':
                return [
                    'Image conflicts may cause visual glitches',
                    'Load preferred mod last for it to take precedence'
                ];
            case 'SOUND':
                return [
                    'Sound conflicts may cause audio issues',
                    'Load preferred mod last for it to take precedence'
                ];
            default:
                return [
                    'Check if these resources are intended to override each other',
                    'Load mods in the correct order to ensure proper overrides'
                ];
        }
    }
}
