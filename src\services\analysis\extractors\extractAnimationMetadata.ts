import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../types/resource/interfaces.js';
import { ResourceCategory } from '../../../types/resource/enums.js';
import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService, DependencyInfo } from '../../databaseService.js';

const logger = new Logger('ExtractAnimationMetadata');

// Animation file format constants
const ANIM_SIGNATURE = 'ANIM'; // Standard animation
const CLIP_SIGNATURE = 'CLIP'; // Animation clip
const CLAF_SIGNATURE = 'CLAF'; // Clip animation file (contains multiple clips)
const ASMB_SIGNATURE = 'ASMB'; // Animation state machine binary
const ASMP_SIGNATURE = 'ASMP'; // Animation state machine parameters
const BNAM_SIGNATURE = 'BNAM'; // Bone name mapping
const BNRY_SIGNATURE = 'BNRY'; // Binary animation data
const SKMG_SIGNATURE = 'SKMG'; // Skeleton mapping

// Animation resource type IDs
export const ANIMATION_RESOURCE_TYPES = {
    ANIMATION: 0x6B20C4F3,           // Standard animation
    ANIMATION_MAP: 0xAC16FBEC,       // Animation map
    ANIMATION_STATE_MACHINE: 0x8EAF13DE, // Animation state machine
    BONE_DELTA: 0x55867754,          // Bone delta
    ANIMATION_ALT: 0x2C70ADF2,       // Alternative animation format
    ANIMATION_STATE_MACHINE_ALT: 0x1797309683, // Alternative animation state machine
    RIG: 0x033260E3,                 // Rig/skeleton
    SKELETON: 0xD382BF57             // Skeleton
};

/**
 * Animation resource structure information based on Sims 4 file format
 * This is a simplified representation for metadata extraction
 */
interface AnimationHeaderInfo {
    format: string;         // Format identifier (e.g., "ANIM", "CLIP", "CLAF", "ASMB", "Unknown")
    version: number;        // Format version
    frameCount: number;     // Number of frames
    duration: number;       // Duration in seconds
    flags: number;          // Format-specific flags
    boneCount?: number;     // Number of bones/joints
    trackCount?: number;    // Number of animation tracks
    fps?: number;           // Frames per second
    looping?: boolean;      // Whether the animation loops
    priority?: number;      // Animation priority
    blendInTime?: number;   // Blend-in time in seconds
    blendOutTime?: number;  // Blend-out time in seconds
    clipNames?: string[];   // Names of animation clips (for CLAF)

    // Animation State Machine specific fields
    stateCount?: number;    // Number of states in the state machine
    transitionCount?: number; // Number of transitions between states
    parameterCount?: number; // Number of parameters
    stateNames?: string[];  // Names of states
    parameterNames?: string[]; // Names of parameters

    // Rig/Skeleton specific fields
    jointCount?: number;    // Number of joints in the skeleton
    jointNames?: string[];  // Names of joints

    // Animation Map specific fields
    mapEntryCount?: number; // Number of entries in the animation map
    mapEntries?: { name: string, type: number, group: number, instance: bigint }[]; // Animation map entries
}

/**
 * Extracts metadata specifically from Animation resources.
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object for Animation resources.
 */
export async function extractAnimationMetadata(
    key: AppResourceKey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};
    let contentSnippet: string | undefined = undefined;
    const tgiDependencies: DependencyInfo[] = [];

    try {
        // Animation resources are binary, so we need to identify the format and parse the header
        if (buffer.length < 16) {
            logger.warn(`Animation buffer too small for ${key.instance.toString(16)}`);
            return { contentSnippet: '[Animation Buffer Too Small]' };
        }

        // Initialize header with default format
        const header: AnimationHeaderInfo = {
            format: 'Unknown',
            version: 0,
            frameCount: 0,
            duration: 0,
            flags: 0
        };

        // Check for known animation format signatures
        if (buffer.length >= 4) {
            const signature = buffer.slice(0, 4).toString();

            if (signature === ANIM_SIGNATURE) {
                // ANIM format (standard animation)
                header.format = 'ANIM';

                if (buffer.length >= 24) {
                    header.version = buffer.readUInt32LE(4);
                    header.frameCount = buffer.readUInt32LE(8);
                    header.duration = buffer.readFloatLE(12);
                    header.flags = buffer.readUInt32LE(16);

                    // Calculate FPS from frame count and duration
                    if (header.duration > 0) {
                        header.fps = header.frameCount / header.duration;
                    }

                    // Check looping flag
                    header.looping = (header.flags & 0x1) !== 0;

                    // Extract additional metadata if buffer is large enough
                    if (buffer.length >= 32) {
                        header.boneCount = buffer.readUInt32LE(20);
                        header.trackCount = buffer.readUInt32LE(24);
                        header.priority = buffer.readUInt32LE(28);
                    }

                    // Extract blend times if available
                    if (buffer.length >= 40) {
                        header.blendInTime = buffer.readFloatLE(32);
                        header.blendOutTime = buffer.readFloatLE(36);
                    }
                }
            } else if (signature === CLIP_SIGNATURE) {
                // CLIP format (animation clip)
                header.format = 'CLIP';

                if (buffer.length >= 24) {
                    header.version = buffer.readUInt32LE(4);
                    header.frameCount = buffer.readUInt32LE(8);
                    header.duration = buffer.readFloatLE(12);
                    header.flags = buffer.readUInt32LE(16);

                    // Calculate FPS from frame count and duration
                    if (header.duration > 0) {
                        header.fps = header.frameCount / header.duration;
                    }

                    // Check looping flag
                    header.looping = (header.flags & 0x1) !== 0;

                    // Extract additional metadata
                    if (buffer.length >= 28) {
                        header.boneCount = buffer.readUInt32LE(20);
                        header.priority = buffer.readUInt32LE(24);
                    }
                }
            } else if (signature === CLAF_SIGNATURE) {
                // CLAF format (clip animation file - contains multiple clips)
                header.format = 'CLAF';

                if (buffer.length >= 16) {
                    header.version = buffer.readUInt32LE(4);
                    const clipCount = buffer.readUInt32LE(8);
                    header.flags = buffer.readUInt32LE(12);

                    // Extract clip names if available
                    header.clipNames = [];
                    let offset = 16;

                    for (let i = 0; i < clipCount && offset < buffer.length; i++) {
                        // Read string length
                        if (offset + 4 <= buffer.length) {
                            const nameLength = buffer.readUInt32LE(offset);
                            offset += 4;

                            // Read string if there's enough data
                            if (offset + nameLength <= buffer.length) {
                                const clipName = buffer.slice(offset, offset + nameLength).toString('utf8');
                                header.clipNames.push(clipName);
                                offset += nameLength;

                                // Look for clip TGI reference
                                if (offset + 16 <= buffer.length) {
                                    const clipType = buffer.readUInt32LE(offset);
                                    const clipGroup = buffer.readUInt32LE(offset + 4);
                                    const clipInstance1 = buffer.readUInt32LE(offset + 8);
                                    const clipInstance2 = buffer.readUInt32LE(offset + 12);

                                    // Construct bigint instance from two 32-bit parts
                                    const clipInstance = BigInt(clipInstance1) | (BigInt(clipInstance2) << 32n);

                                    // Add as dependency
                                    tgiDependencies.push({
                                        resourceId: resourceId,
                                        targetType: clipType,
                                        targetGroup: BigInt(clipGroup),
                                        targetInstance: clipInstance,
                                        referenceType: 'AnimationClip',
                                        timestamp: Date.now()
                                    });

                                    offset += 16;
                                }
                            }
                        }
                    }

                    // Set frame count and duration based on clip count
                    header.frameCount = clipCount;
                }
            } else if (signature === ASMB_SIGNATURE) {
                // ASMB format (Animation State Machine Binary)
                header.format = 'ASMB';

                if (buffer.length >= 20) {
                    header.version = buffer.readUInt32LE(4);
                    header.stateCount = buffer.readUInt32LE(8);
                    header.transitionCount = buffer.readUInt32LE(12);
                    header.parameterCount = buffer.readUInt32LE(16);

                    // Extract state names and parameter names if available
                    header.stateNames = [];
                    header.parameterNames = [];
                    let offset = 20;

                    // Extract state names
                    for (let i = 0; i < header.stateCount && offset + 4 <= buffer.length; i++) {
                        const nameLength = buffer.readUInt32LE(offset);
                        offset += 4;

                        if (offset + nameLength <= buffer.length) {
                            const stateName = buffer.slice(offset, offset + nameLength).toString('utf8');
                            header.stateNames.push(stateName);
                            offset += nameLength;
                        }
                    }

                    // Extract parameter names
                    for (let i = 0; i < header.parameterCount && offset + 4 <= buffer.length; i++) {
                        const nameLength = buffer.readUInt32LE(offset);
                        offset += 4;

                        if (offset + nameLength <= buffer.length) {
                            const paramName = buffer.slice(offset, offset + nameLength).toString('utf8');
                            header.parameterNames.push(paramName);
                            offset += nameLength;
                        }
                    }

                    // Look for animation references in the state machine
                    // Each state can reference one or more animations
                    for (let i = 0; i < header.stateCount && offset + 16 <= buffer.length; i++) {
                        // Skip state data to get to the animation reference
                        // This is a simplified approach - real parsing would be more complex
                        const animType = buffer.readUInt32LE(offset);
                        const animGroup = buffer.readUInt32LE(offset + 4);
                        const animInstance1 = buffer.readUInt32LE(offset + 8);
                        const animInstance2 = buffer.readUInt32LE(offset + 12);

                        // Only add as dependency if it looks like a valid TGI
                        if (animType !== 0) {
                            // Construct bigint instance from two 32-bit parts
                            const animInstance = BigInt(animInstance1) | (BigInt(animInstance2) << 32n);

                            // Add as dependency
                            tgiDependencies.push({
                                resourceId: resourceId,
                                targetType: animType,
                                targetGroup: BigInt(animGroup),
                                targetInstance: animInstance,
                                referenceType: 'AnimationState',
                                timestamp: Date.now()
                            });
                        }

                        offset += 16;
                    }
                }
            } else if (signature === ASMP_SIGNATURE) {
                // ASMP format (Animation State Machine Parameters)
                header.format = 'ASMP';

                if (buffer.length >= 12) {
                    header.version = buffer.readUInt32LE(4);
                    header.parameterCount = buffer.readUInt32LE(8);

                    // Extract parameter names if available
                    header.parameterNames = [];
                    let offset = 12;

                    for (let i = 0; i < header.parameterCount && offset + 4 <= buffer.length; i++) {
                        const nameLength = buffer.readUInt32LE(offset);
                        offset += 4;

                        if (offset + nameLength <= buffer.length) {
                            const paramName = buffer.slice(offset, offset + nameLength).toString('utf8');
                            header.parameterNames.push(paramName);
                            offset += nameLength;
                        }
                    }
                }
            } else if (signature === BNAM_SIGNATURE || signature === SKMG_SIGNATURE) {
                // BNAM format (Bone Name Mapping) or SKMG (Skeleton Mapping)
                header.format = signature;

                if (buffer.length >= 12) {
                    header.version = buffer.readUInt32LE(4);
                    header.jointCount = buffer.readUInt32LE(8);

                    // Extract joint names if available
                    header.jointNames = [];
                    let offset = 12;

                    for (let i = 0; i < header.jointCount && offset + 4 <= buffer.length; i++) {
                        const nameLength = buffer.readUInt32LE(offset);
                        offset += 4;

                        if (offset + nameLength <= buffer.length) {
                            const jointName = buffer.slice(offset, offset + nameLength).toString('utf8');
                            header.jointNames.push(jointName);
                            offset += nameLength;
                        }
                    }
                }
            } else {
                // Unknown format - try generic parsing
                header.version = buffer.readUInt32LE(0);

                if (buffer.length >= 16) {
                    header.frameCount = buffer.readUInt32LE(4);
                    header.duration = buffer.readFloatLE(8);
                    header.flags = buffer.readUInt32LE(12);

                    // Calculate FPS from frame count and duration
                    if (header.duration > 0) {
                        header.fps = header.frameCount / header.duration;
                    }
                }

                // Extract additional metadata if buffer is large enough
                if (buffer.length >= 24) {
                    header.boneCount = buffer.readUInt32LE(16);
                    header.trackCount = buffer.readUInt32LE(20);
                }
            }
        }

        // Store extracted metadata
        extractedMetadata.animationFormat = header.format;
        extractedMetadata.animationVersion = header.version;

        // Store standard animation fields if they exist
        if (header.frameCount !== undefined) extractedMetadata.animationFrameCount = header.frameCount;
        if (header.duration !== undefined) extractedMetadata.animationDuration = header.duration;
        if (header.flags !== undefined) extractedMetadata.animationFlags = header.flags;
        if (header.boneCount !== undefined) extractedMetadata.animationBoneCount = header.boneCount;
        if (header.trackCount !== undefined) extractedMetadata.animationTrackCount = header.trackCount;
        if (header.fps !== undefined) extractedMetadata.animationFps = header.fps;
        if (header.looping !== undefined) extractedMetadata.animationLooping = header.looping;
        if (header.priority !== undefined) extractedMetadata.animationPriority = header.priority;
        if (header.blendInTime !== undefined) extractedMetadata.animationBlendInTime = header.blendInTime;
        if (header.blendOutTime !== undefined) extractedMetadata.animationBlendOutTime = header.blendOutTime;

        // Store clip names if they exist
        if (header.clipNames !== undefined && header.clipNames.length > 0) {
            extractedMetadata.animationClipNames = JSON.stringify(header.clipNames);
        }

        // Store animation state machine fields if they exist
        if (header.stateCount !== undefined) extractedMetadata.animationStateCount = header.stateCount;
        if (header.transitionCount !== undefined) extractedMetadata.animationTransitionCount = header.transitionCount;
        if (header.parameterCount !== undefined) extractedMetadata.animationParameterCount = header.parameterCount;

        // Store state names if they exist
        if (header.stateNames !== undefined && header.stateNames.length > 0) {
            extractedMetadata.animationStateNames = JSON.stringify(header.stateNames);
        }

        // Store parameter names if they exist
        if (header.parameterNames !== undefined && header.parameterNames.length > 0) {
            extractedMetadata.animationParameterNames = JSON.stringify(header.parameterNames);
        }

        // Store skeleton/rig fields if they exist
        if (header.jointCount !== undefined) extractedMetadata.animationJointCount = header.jointCount;

        // Store joint names if they exist
        if (header.jointNames !== undefined && header.jointNames.length > 0) {
            extractedMetadata.animationJointNames = JSON.stringify(header.jointNames);
        }

        // Store animation map fields if they exist
        if (header.mapEntryCount !== undefined) extractedMetadata.animationMapEntryCount = header.mapEntryCount;

        // Store map entries if they exist
        if (header.mapEntries !== undefined && header.mapEntries.length > 0) {
            extractedMetadata.animationMapEntries = JSON.stringify(header.mapEntries);
        }

        // Create content snippet based on the animation format
        contentSnippet = `[Animation: ${header.format}, Ver=${header.version}`;

        if (header.format === 'CLAF') {
            contentSnippet += `, Clips=${header.frameCount}`;
            if (header.clipNames && header.clipNames.length > 0) {
                const clipSample = header.clipNames.slice(0, 3).join(', ');
                contentSnippet += `, Names=[${clipSample}${header.clipNames.length > 3 ? '...' : ''}]`;
            }
        } else if (header.format === 'ANIM' || header.format === 'CLIP') {
            contentSnippet += `, Frames=${header.frameCount}`;
            if (header.duration > 0) contentSnippet += `, Duration=${header.duration.toFixed(2)}s`;
            if (header.fps) contentSnippet += `, FPS=${header.fps.toFixed(1)}`;
            if (header.looping) contentSnippet += `, Looping`;
        } else if (header.format === 'ASMB') {
            contentSnippet += `, States=${header.stateCount}, Transitions=${header.transitionCount}, Params=${header.parameterCount}`;
            if (header.stateNames && header.stateNames.length > 0) {
                const stateSample = header.stateNames.slice(0, 2).join(', ');
                contentSnippet += `, StateNames=[${stateSample}${header.stateNames.length > 2 ? '...' : ''}]`;
            }
        } else if (header.format === 'ASMP') {
            contentSnippet += `, Parameters=${header.parameterCount}`;
            if (header.parameterNames && header.parameterNames.length > 0) {
                const paramSample = header.parameterNames.slice(0, 2).join(', ');
                contentSnippet += `, ParamNames=[${paramSample}${header.parameterNames.length > 2 ? '...' : ''}]`;
            }
        } else if (header.format === 'BNAM' || header.format === 'SKMG') {
            contentSnippet += `, Joints=${header.jointCount}`;
            if (header.jointNames && header.jointNames.length > 0) {
                const jointSample = header.jointNames.slice(0, 2).join(', ');
                contentSnippet += `, JointNames=[${jointSample}${header.jointNames.length > 2 ? '...' : ''}]`;
            }
        }

        contentSnippet += `]`;

        // Look for skeleton dependencies in the animation data
        // Common resource types for skeletons and rigs
        const skeletonTypes = [
            0x033260E3, // Skeleton
            0xD382BF57, // Rig
            0x6B20C4F3  // Bone Delta
        ];

        // Scan for potential skeleton references
        for (let i = 32; i < buffer.length - 16; i += 4) {
            const potentialType = buffer.readUInt32LE(i);

            if (skeletonTypes.includes(potentialType)) {
                const potentialGroup = buffer.readUInt32LE(i + 4);
                const potentialInstance1 = buffer.readUInt32LE(i + 8);
                const potentialInstance2 = buffer.readUInt32LE(i + 12);

                // Construct potential bigint instance from two 32-bit parts
                const potentialInstance = BigInt(potentialInstance1) | (BigInt(potentialInstance2) << 32n);

                // Add as potential dependency
                tgiDependencies.push({
                    resourceId: resourceId,
                    targetType: potentialType,
                    targetGroup: BigInt(potentialGroup),
                    targetInstance: potentialInstance,
                    referenceType: 'Skeleton',
                    timestamp: Date.now()
                });
            }
        }

        // Save animation data for deeper analysis
        try {
            if (databaseService.parsedContent && typeof databaseService.parsedContent.saveParsedContent === 'function') {
                await databaseService.parsedContent.saveParsedContent({
                    resourceId: resourceId,
                    contentType: 'animation_metadata',
                    content: JSON.stringify(header)
                });
                logger.debug(`Saved animation metadata for resource ${resourceId}`);
            } else {
                logger.warn(`Cannot save animation metadata: parsedContent repository not available`);
            }
        } catch (dbError: any) {
            logger.error(`Failed to save animation metadata for resource ${resourceId} to DB: ${dbError.message || dbError}`);
        }

        // Save any found dependencies
        if (tgiDependencies.length > 0) {
            try {
                if (databaseService.dependencies && typeof databaseService.dependencies.saveDependencies === 'function') {
                    await databaseService.dependencies.saveDependencies(tgiDependencies);
                    logger.debug(`Saved ${tgiDependencies.length} dependencies for animation resource ${resourceId}`);
                } else {
                    logger.warn(`Cannot save dependencies: dependencies repository not available`);
                }
            } catch (depError: any) {
                logger.error(`Failed to save dependencies for animation resource ${resourceId}: ${depError.message || depError}`);
            }
        }

    } catch (error: any) {
        logger.error(`Error extracting animation metadata for ${key.instance.toString(16)}: ${error.message || error}`);
        contentSnippet = '[Animation Parse Error]';
    }

    // Return extracted metadata
    return {
        contentSnippet: contentSnippet,
        ...extractedMetadata,
    };
}
