/**
 * Generic Task Handler
 * 
 * This module provides a handler for generic tasks that can be executed in worker threads.
 * It serves as a fallback for tasks that don't have a specific handler.
 */

import { Task } from '../workerPool.js';

/**
 * Handle a generic task
 * @param task The task to handle
 * @returns The result of the task
 */
export async function handleGenericTask(task: Task): Promise<any> {
    // For generic tasks, we expect the data to contain a serialized function
    // and arguments to pass to that function
    const { fn, args } = task.data;

    if (typeof fn !== 'string') {
        throw new Error('Generic task requires a serialized function (fn) in task data');
    }

    try {
        // Deserialize the function
        const deserializedFn = new Function(`return ${fn}`)();
        
        if (typeof deserializedFn !== 'function') {
            throw new Error('Failed to deserialize function for generic task');
        }

        // Execute the function with the provided arguments
        return await deserializedFn(...(args || []));
    } catch (error: any) {
        throw new Error(`Error executing generic task: ${error.message}`);
    }
}
