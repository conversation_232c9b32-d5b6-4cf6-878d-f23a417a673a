import { Logger } from '../logging/logger';

const logger = new Logger('TgiParser');

export interface TgiComponents {
    type: number;
    group: number;
    instance: bigint;
}

/**
 * Parses a TGI string (e.g., "Type:Group:Instance" in hex or decimal) into its components.
 * Handles optional '0x' prefixes. Instance ID is returned as bigint.
 * Returns null if parsing fails.
 */
export function parseTgi(tgiString: string | null | undefined): TgiComponents | null {
    if (!tgiString || typeof tgiString !== 'string') {
        return null;
    }

    const parts = tgiString.split(':');
    if (parts.length !== 3) {
        // logger.warn(`Invalid TGI format: "${tgiString}". Expected 3 parts separated by ':'.`);
        return null;
    }

    try {
        const typeStr = parts[0].startsWith('0x') ? parts[0] : `0x${parts[0]}`;
        const groupStr = parts[1].startsWith('0x') ? parts[1] : `0x${parts[1]}`;
        const instanceStr = parts[2].startsWith('0x') ? parts[2] : `0x${parts[2]}`;

        const type = parseInt(typeStr, 16);
        const group = parseInt(groupStr, 16);
        const instance = BigInt(instanceStr); // Use BigInt for instance

        if (isNaN(type) || isNaN(group)) {
             logger.warn(`Failed to parse TGI components as numbers: "${tgiString}"`);
             return null;
        }

        return { type, group, instance };
    } catch (error: any) {
        logger.warn(`Error parsing TGI string "${tgiString}": ${error.message || error}`);
        return null;
    }
}