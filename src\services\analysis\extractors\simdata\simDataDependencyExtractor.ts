import { Logger } from '../../../../utils/logging/logger.js';
import { DependencyInfo } from '../../../../types/database.js'; // Import DependencyInfo
import { TgiComponents, parseTgi } from '../../../../utils/parsing/tgiParser.js'; // Import TgiComponents and parseTgi
import { ParsedSimData } from './simDataParser.js'; // Import ParsedSimData

const log = new Logger('SimDataDependencyExtractor');

// Helper function to create a dependency
function createDependency(resourceId: number, type: number, group: number | bigint, instance: bigint, referenceType: string): DependencyInfo {
    return {
        resourceId: resourceId, // Set resourceId here
        targetType: type,
        targetGroup: typeof group === 'number' ? BigInt(group) : group,
        targetInstance: instance,
        referenceType: referenceType, // Use provided referenceType
        timestamp: Date.now()
    };
}

// Helper function to refine relationship type based on TGI type
function getRelationshipTypeFromTgi(type: number, defaultType: string): string {
     switch (type) {
         case 0x0166038C: return 'SimData_TO_Tuning';
         case 0x220557DA: return 'SimData_TO_StringTable';
         case 0x545AC67A: return 'SimData_TO_SimData';
         case 0x6017E896: return 'SimData_TO_Model';
         case 0x00B2D882: return 'SimData_TO_Image';
         case 0x319E4F1D: return 'SimData_TO_Image'; // PNG
         case 0x034AEECB: return 'SimData_TO_CASPart';
         case 0xEA5118B0: return 'SimData_TO_Script';
         case 0x6B20C4F3: return 'SimData_TO_Animation';
         case 0xAC16FBEC: return 'SimData_TO_AnimationMap';
         case 0x2026960B: return 'SimData_TO_Sound';
         case 0x0B8BFB57: return 'SimData_TO_SoundEffect';
         case 0x47454F4D: return 'SimData_TO_Geometry';
         case 0x4D545354: return 'SimData_TO_MaterialSet';
         case 0x4A415A5A: return 'SimData_TO_JazzFile';
         case 0x4C41594F: return 'SimData_TO_Layout';
         case 0x534C4F54: return 'SimData_TO_Slot';
         case 0x56505859: return 'SimData_TO_VisualProxy';
         case 0x57424E4B: return 'SimData_TO_WaveBank';
         default: return defaultType; // Fallback to the type derived from column name or default
     }
}


/**
 * Extracts TGI references from SimData resource.
 * @param simData The parsed SimData resource.
 * @param resourceId The ID of the SimData resource in the database.
 * @returns Array of DependencyInfo objects.
 */
export function extractSimDataDependencies(simData: ParsedSimData, resourceId: number): DependencyInfo[] { // Export and add resourceId parameter
    const dependencies: DependencyInfo[] = [];

    // Check if we have schema information to help with type detection
    const resourceKeyColumns: Set<string> = new Set();

    // First identify all ResourceKey columns from schema if available
    if (simData.schema && simData.schema.columns) {
        for (const column of simData.schema.columns) {
            // Type 20 is ResourceKey in SimData
            if (column.type === 20) {
                resourceKeyColumns.add(column.name);
            }
        }
    }

    // Process all instances
    for (const instance of simData.instances) {
        // Check each value for potential TGI references
        for (const [columnName, value] of Object.entries(instance.values)) {
            // Skip null values
            if (value === null || value === undefined) continue;

            // Determine potential relationship type based on column name
            let potentialReferenceType = 'SimData'; // Default

            // Check if this is a ResourceKey column based on schema or naming patterns
            const isResourceKeyColumn =
                resourceKeyColumns.has(columnName) ||
                columnName.endsWith('_key') ||
                columnName.endsWith('Key') ||
                columnName.endsWith('_reference') ||
                columnName.endsWith('Reference') ||
                columnName.endsWith('_resource') ||
                columnName.endsWith('Resource') ||
                columnName.includes('Ref') ||
                columnName.includes('TGI') ||
                columnName.includes('Resource');

            if (isResourceKeyColumn) {
                 // Categorize relationship by resource type hint in column name
                 if (columnName.includes('STBL') || columnName.includes('StringTable')) {
                     potentialReferenceType = 'SimData_TO_StringTable';
                 } else if (columnName.includes('Tuning') || columnName.includes('XML')) {
                     potentialReferenceType = 'SimData_TO_Tuning';
                 } else if (columnName.includes('SimData')) {
                     potentialReferenceType = 'SimData_TO_SimData';
                 } else if (columnName.includes('Model')) {
                     potentialReferenceType = 'SimData_TO_Model';
                 } else if (columnName.includes('Image') || columnName.includes('DDS')) {
                     potentialReferenceType = 'SimData_TO_Image';
                 } else {
                     potentialReferenceType = 'SimData_TO_UnknownType';
                 }


                // Try to parse as TGI
                try {
                    // Handle different value formats
                    if (typeof value === 'string') {
                        // Try to parse string format (like "T-G-I")
                        const tgi = parseTgi(value);
                        if (tgi) {
                            // Refine relationship type based on parsed type if possible
                            const refinedReferenceType = getRelationshipTypeFromTgi(tgi.type, potentialReferenceType);
                            dependencies.push(createDependency(resourceId, tgi.type, tgi.group, tgi.instance, refinedReferenceType));
                        }
                    } else if (typeof value === 'object' && value !== null) {
                        // Handle object format with type, group, instance properties
                        if ('type' in value && 'group' in value && 'instance' in value) {
                             const tgi: TgiComponents = {
                                 type: Number(value.type),
                                 group: Number(value.group),
                                 instance: BigInt(String(value.instance))
                             };
                             const refinedReferenceType = getRelationshipTypeFromTgi(tgi.type, potentialReferenceType);
                             dependencies.push(createDependency(resourceId, tgi.type, tgi.group, tgi.instance, refinedReferenceType));
                        }
                    } else if (typeof value === 'number' && value > 0) {
                        // Could be an instance ID reference (common in Sims 4)
                        // Try to find the type and group from column name patterns
                        let type: number | undefined;
                        let group: number | undefined;

                        // Check for type hints in column name
                        if (columnName.includes('STBL') || columnName.includes('StringTable')) {
                            type = 0x220557DA; // String Table type
                            group = 0x80000000; // Common group for string tables
                        } else if (columnName.includes('Tuning') || columnName.includes('XML')) {
                            type = 0x0166038C; // Tuning/XML type
                            group = 0; // Default group
                        } else if (columnName.includes('SimData')) {
                            type = 0x545AC67A; // SimData type
                            group = 0; // Default group
                        }

                        // If we have type and group, add as a reference
                        if (type !== undefined && group !== undefined) {
                             const refinedReferenceType = getRelationshipTypeFromTgi(type, potentialReferenceType);
                             dependencies.push(createDependency(resourceId, type, group, BigInt(value), refinedReferenceType));
                        }
                    }
                } catch (e) {
                    // Ignore parsing errors
                    log.debug(`Error parsing TGI reference in column ${columnName}: ${e}`);
                }
            }

            // Also check for string values that might contain TGI references
            if (typeof value === 'string' && value.length > 10) {
                // Look for patterns like "0x00000000!0x00000000!0x0000000000000000"
                const tgiPattern = /0x([0-9A-Fa-f]+)!0x([0-9A-Fa-f]+)!0x([0-9A-Fa-f]+)/g;
                let match;

                while ((match = tgiPattern.exec(value)) !== null) {
                    try {
                        const type = parseInt(match[1], 16);
                        const group = parseInt(match[2], 16);
                        const instance = BigInt(`0x${match[3]}`);

                        const refinedReferenceType = getRelationshipTypeFromTgi(type, potentialReferenceType);
                        dependencies.push(createDependency(resourceId, type, group, instance, refinedReferenceType));
                    } catch (e) {
                        // Ignore parsing errors
                    }
                }

                // Also look for hash patterns that might be instance IDs
                const hashPattern = /0x([0-9A-Fa-f]{8,16})/g;
                while ((match = hashPattern.exec(value)) !== null) {
                    try {
                        // This is just an instance ID, so we don't know type/group
                        // We'll use a special marker type to indicate this
                        const instance = BigInt(`0x${match[1]}`);

                        // Only add if it looks like a valid instance ID (not too small)
                        if (instance > 0x100000n) {
                             const refinedReferenceType = getRelationshipTypeFromTgi(0, potentialReferenceType); // Use type 0 for unknown
                             dependencies.push(createDependency(resourceId, 0, 0, instance, refinedReferenceType));
                        }
                    } catch (e) {
                        // Ignore parsing errors
                    }
                }
            }
        }
    }

    // Remove duplicates
    const uniqueRefs: DependencyInfo[] = [];
    const seen = new Set<string>();

    for (const dep of dependencies) {
        const key = `${dep.resourceId}-${dep.targetType}-${dep.targetGroup}-${dep.targetInstance}-${dep.referenceType}`;
        if (!seen.has(key)) {
            seen.add(key);
            uniqueRefs.push(dep);
        }
    }

    return uniqueRefs;
}