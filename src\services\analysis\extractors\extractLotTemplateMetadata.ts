import { <PERSON><PERSON><PERSON> } from "../../../types/resource/interfaces.js";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { LotTemplateMetadata } from "../../../types/resource/analysis.js";
import { logger } from "../../../utils/logging/logger.js";

/**
 * Extracts metadata from a LOT_TEMPLATE resource (0x0A5BCFDB).
 * 
 * Lot templates define the layout and properties of lots that can be placed in worlds.
 * They contain information about the lot size, building count, object count, and other properties.
 *
 * @param key The resource key of the LOT_TEMPLATE resource.
 * @param buffer The buffer containing the LOT_TEMPLATE resource data.
 * @returns The extracted LotTemplateMetadata.
 */
export function extractLotTemplateMetadata(key: ResourceKey, buffer: Buffer): LotTemplateMetadata | null {
  try {
    if (buffer.length < 0x40) {
      logger.warn(`LOT_TEMPLATE resource ${key.instance.toString(16)} is too small to contain a valid header.`);
      return null;
    }

    // Check for magic number/signature if applicable
    // Many Sims 4 resources start with a magic number
    const signature = buffer.slice(0, 4).toString('utf8');
    logger.debug(`LOT_TEMPLATE signature: ${signature}`);

    // Extract basic template information
    const templateId = buffer.readUInt32LE(0x04);
    const templateType = buffer.readUInt32LE(0x08);
    const templateFlags = buffer.readUInt32LE(0x0C);
    
    // Extract lot size
    const sizeX = buffer.readUInt16LE(0x10);
    const sizeZ = buffer.readUInt16LE(0x12);
    
    // Extract buildable area if available
    let buildableAreaX = 0;
    let buildableAreaZ = 0;
    
    if (buffer.length >= 0x18) {
      buildableAreaX = buffer.readUInt16LE(0x14);
      buildableAreaZ = buffer.readUInt16LE(0x16);
    }
    
    // Extract additional metadata if available
    let buildingCount = 0;
    let objectCount = 0;
    let roomCount = 0;
    let hasBasement = false;
    let hasPool = false;
    let valueCategory = 0;
    let estimatedValue = 0;
    
    if (buffer.length >= 0x30) {
      buildingCount = buffer.readUInt16LE(0x18);
      objectCount = buffer.readUInt16LE(0x1A);
      roomCount = buffer.readUInt16LE(0x1C);
      
      // Extract flags for features
      const featureFlags = buffer.readUInt32LE(0x20);
      hasBasement = (featureFlags & 0x01) !== 0;
      hasPool = (featureFlags & 0x02) !== 0;
      
      valueCategory = buffer.readUInt16LE(0x24);
      estimatedValue = buffer.readUInt32LE(0x28);
    }
    
    // Look for template name and description
    // These are typically stored as Pascal strings (length-prefixed)
    let templateName: string | undefined = undefined;
    let templateDescription: string | undefined = undefined;
    
    // Try to find name at common offset
    if (buffer.length >= 0x40) {
      const nameLength = buffer.readUInt16LE(0x30);
      if (nameLength > 0 && nameLength < 256 && buffer.length >= 0x32 + nameLength) {
        templateName = buffer.slice(0x32, 0x32 + nameLength).toString('utf8');
      }
      
      // Try to find description after name
      if (templateName && buffer.length >= 0x32 + nameLength + 2) {
        const descOffset = 0x32 + nameLength;
        const descLength = buffer.readUInt16LE(descOffset);
        if (descLength > 0 && descLength < 1024 && buffer.length >= descOffset + 2 + descLength) {
          templateDescription = buffer.slice(descOffset + 2, descOffset + 2 + descLength).toString('utf8');
        }
      }
    }

    // Create and return the metadata object
    const metadata: LotTemplateMetadata = {
      templateId,
      templateType,
      templateFlags,
      lotSize: { x: sizeX, z: sizeZ },
      buildableArea: { x: buildableAreaX, z: buildableAreaZ },
      buildingCount,
      objectCount,
      roomCount,
      hasBasement,
      hasPool,
      valueCategory,
      estimatedValue
    };
    
    // Add optional fields if available
    if (templateName) {
      metadata.templateName = templateName;
    }
    
    if (templateDescription) {
      metadata.templateDescription = templateDescription;
    }

    logger.info(`Extracted metadata for LOT_TEMPLATE resource ${key.instance.toString(16)}`);
    return metadata;
  } catch (error) {
    logger.error(`Error extracting LOT_TEMPLATE metadata: ${error}`);
    return null;
  }
}

/**
 * Creates a user-friendly content snippet from the extracted metadata.
 * 
 * @param metadata The extracted LotTemplateMetadata.
 * @returns A string containing a user-friendly representation of the metadata.
 */
export function createLotTemplateContentSnippet(metadata: LotTemplateMetadata): string {
  let snippet = `Lot Template`;
  
  if (metadata.templateName) {
    snippet += `: "${metadata.templateName}"`;
  } else {
    snippet += ` #${metadata.templateId}`;
  }
  
  snippet += ` (${metadata.lotSize.x}x${metadata.lotSize.z})`;
  
  if (metadata.buildingCount > 0) {
    snippet += `, ${metadata.buildingCount} building(s)`;
  }
  
  if (metadata.roomCount > 0) {
    snippet += `, ${metadata.roomCount} room(s)`;
  }
  
  if (metadata.objectCount > 0) {
    snippet += `, ${metadata.objectCount} object(s)`;
  }
  
  if (metadata.hasBasement) {
    snippet += `, has basement`;
  }
  
  if (metadata.hasPool) {
    snippet += `, has pool`;
  }
  
  if (metadata.estimatedValue > 0) {
    snippet += `, value: §${metadata.estimatedValue.toLocaleString()}`;
  }
  
  return snippet;
}
