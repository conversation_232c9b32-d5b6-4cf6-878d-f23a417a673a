# S4TK Package: @s4tk/images

Based on documentation found in `docs/technical/images/`.

## Overview

This package provides models and utilities for processing Sims 4 image resources, primarily focusing on DDS (DirectDraw Surface) and its shuffled variant DST used by the game. It leverages the [Jimp](https://github.com/oliver-moran/jimp) library for handling conversions to/from other formats like PNG.

**Note:** The documentation explicitly states this package is highly experimental, especially regarding format conversions. Using PNG for import/export is recommended for best results.

## Installation

```sh
npm i @s4tk/images
```

## API

### Main Class: `DdsImage`

Represents a DDS/DST image resource.

**Properties:**

*   `header: DdsHeader` (readonly): An object containing parsed DDS header information (dimensions, format, flags, mipmap count, etc.).
*   `buffer: Buffer` (readonly): The raw buffer containing the complete DDS file data (signature, header, pixel data).
*   `isShuffled: boolean` (getter): Returns `true` if the image uses DST compression (shuffled), `false` otherwise (DXT).

**Static Creation Methods:**

*   `static from(buffer: Buffer): DdsImage`: Parses a buffer containing a full DDS/DST file.
*   `static fromAsync(buffer: Buffer): Promise<DdsImage>`: Asynchronous version of `from`.
*   `static fromBitmapAsync(bitmap: Bitmap, options?: DdsConversionOptions): Promise<DdsImage>`: Creates a DDS/DST from raw pixel data (requires `Bitmap` interface, likely via Jimp).
*   `static fromImageAsync(buffer: Buffer, options?: DdsConversionOptions): Promise<DdsImage>`: Creates a DDS/DST from another image format buffer (e.g., PNG). May resize image.
*   `static fromJimpAsync(image: JimpType, options?: DdsConversionOptions): Promise<DdsImage>`: Creates a DDS/DST directly from a Jimp object.

**Instance Methods:**

*   `clone(): DdsImage`: Creates a deep copy.
*   `toBitmap(): Bitmap`: Decodes DXT/DST pixel data into a raw RGBA `Bitmap` object.
*   `toJimp(): JimpType`: Converts the DDS/DST image into a Jimp object, which can then be used for manipulation or export (e.g., `jimp.getBufferAsync('image/png')`).
*   `toShuffled(clone?: boolean): DdsImage`: Returns a copy guaranteed to be DST (shuffled). Throws if the source isn't DXT1/DXT5.
*   `toUnshuffled(clone?: boolean): DdsImage`: Returns a copy guaranteed to be DXT (unshuffled). Throws if the source isn't DST1/DST5.

### Supporting Enums

Located in `lib/enums.d.ts`:

*   `FourCC`: Identifiers for compression formats (DXT1, DXT3, DXT5, DST1, DST3, DST5).
*   `PixelFormatFlags`: Flags for pixel format types (RGB, RGBA, Luminance, FourCC).
*   `RleVersion`: Identifiers for RLE image versions (RLE2, RLES).
*   `HeaderFlags`: Flags found in DDS headers (Texture, Mipmap, Volume, Pitch, LinearSize).

### Supporting Types

Located in `lib/types.d.ts`:

*   `Bitmap`: Interface `{ data: Buffer; width: number; height: number; }`.
*   `DdsConversionOptions`: Options for converting *to* DDS:
    *   `maxMipMaps?: number` (default 15): Max number of mipmaps to generate.
    *   `shuffle?: boolean` (default false): Whether to use DST (true) or DXT (false) compression.