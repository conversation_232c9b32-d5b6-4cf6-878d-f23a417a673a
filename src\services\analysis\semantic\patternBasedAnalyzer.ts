/**
 * Pattern-Based Analyzer
 * 
 * This module provides pattern-based analysis of resources to identify
 * common patterns and structures in Sims 4 mods.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { injectable, singleton } from '../../di/decorators.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';
import { ResourcePurposeType } from './interfaces/resourcePurpose.js';

/**
 * Pattern type
 */
export enum PatternType {
    OVERRIDE = 'OVERRIDE',
    EXTENSION = 'EXTENSION',
    NEW_CONTENT = 'NEW_CONTENT',
    UTILITY = 'UTILITY',
    REPLACEMENT = 'REPLACEMENT',
    INJECTION = 'INJECTION',
    UNKNOWN = 'UNKNOWN'
}

/**
 * Pattern match
 */
export interface PatternMatch {
    /**
     * Pattern type
     */
    patternType: PatternType;
    
    /**
     * Pattern name
     */
    patternName: string;
    
    /**
     * Pattern description
     */
    description: string;
    
    /**
     * Confidence score (0-100)
     */
    confidence: number;
    
    /**
     * Related purpose type
     */
    purposeType: ResourcePurposeType;
    
    /**
     * Evidence for the pattern match
     */
    evidence: string[];
}

/**
 * Pattern definition
 */
interface PatternDefinition {
    /**
     * Pattern type
     */
    type: PatternType;
    
    /**
     * Pattern name
     */
    name: string;
    
    /**
     * Pattern description
     */
    description: string;
    
    /**
     * Related purpose type
     */
    purposeType: ResourcePurposeType;
    
    /**
     * Resource types this pattern applies to
     */
    applicableResourceTypes: number[];
    
    /**
     * Detector function
     */
    detector: (resourceKey: ResourceKey, metadata: Record<string, any>, content?: string) => PatternMatch | null;
}

/**
 * Pattern-based analyzer
 */
@singleton()
export class PatternBasedAnalyzer {
    private logger: Logger;
    private patterns: PatternDefinition[] = [];
    
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(logger?: Logger) {
        this.logger = logger || new Logger('PatternBasedAnalyzer');
        this.initializePatterns();
    }
    
    /**
     * Initialize pattern definitions
     */
    private initializePatterns(): void {
        // Tuning override pattern
        this.patterns.push({
            type: PatternType.OVERRIDE,
            name: 'Tuning Override',
            description: 'Overrides an existing tuning resource',
            purposeType: ResourcePurposeType.MODIFIES_CONTENT,
            applicableResourceTypes: [ResourceTypes.RESOURCE_TYPE_TUNING],
            detector: (resourceKey, metadata, content) => {
                const evidence: string[] = [];
                
                // Check if it's marked as an override
                if (metadata.isOverride) {
                    evidence.push('Resource is marked as an override');
                }
                
                // Check for override indicators in content
                if (content && content.includes('instance_reference')) {
                    evidence.push('Contains instance_reference attribute');
                }
                
                // Check for common override patterns in XML
                if (content && (content.includes('_override') || content.includes('_patch'))) {
                    evidence.push('Contains _override or _patch in name');
                }
                
                if (evidence.length > 0) {
                    return {
                        patternType: PatternType.OVERRIDE,
                        patternName: 'Tuning Override',
                        description: 'Overrides an existing tuning resource',
                        confidence: evidence.length * 25,
                        purposeType: ResourcePurposeType.MODIFIES_CONTENT,
                        evidence
                    };
                }
                
                return null;
            }
        });
        
        // Script injection pattern
        this.patterns.push({
            type: PatternType.INJECTION,
            name: 'Script Injection',
            description: 'Injects code into existing game functions',
            purposeType: ResourcePurposeType.MODIFIES_CONTENT,
            applicableResourceTypes: [ResourceTypes.RESOURCE_TYPE_SCRIPT],
            detector: (resourceKey, metadata, content) => {
                const evidence: string[] = [];
                
                // Check if it's marked as having injections
                if (metadata.hasInjections) {
                    evidence.push('Resource is marked as having injections');
                }
                
                // Check for injection indicators in content
                if (content) {
                    if (content.includes('inject') || content.includes('patch')) {
                        evidence.push('Contains inject or patch keywords');
                    }
                    
                    if (content.includes('@injector') || content.includes('@wrapper')) {
                        evidence.push('Contains injection decorators');
                    }
                    
                    if (content.includes('original') && content.includes('return original')) {
                        evidence.push('Contains original function call pattern');
                    }
                }
                
                if (evidence.length > 0) {
                    return {
                        patternType: PatternType.INJECTION,
                        patternName: 'Script Injection',
                        description: 'Injects code into existing game functions',
                        confidence: evidence.length * 25,
                        purposeType: ResourcePurposeType.MODIFIES_CONTENT,
                        evidence
                    };
                }
                
                return null;
            }
        });
        
        // New CAS item pattern
        this.patterns.push({
            type: PatternType.NEW_CONTENT,
            name: 'New CAS Item',
            description: 'Adds a new Create-A-Sim item',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            applicableResourceTypes: [ResourceTypes.RESOURCE_TYPE_CASPART],
            detector: (resourceKey, metadata, content) => {
                const evidence: string[] = [];
                
                // CAS parts are almost always new content
                evidence.push('Resource is a CAS part');
                
                // Check for new content indicators
                if (metadata.isCustomContent) {
                    evidence.push('Resource is marked as custom content');
                }
                
                if (metadata.category && metadata.category.includes('CAS')) {
                    evidence.push(`Resource category is ${metadata.category}`);
                }
                
                return {
                    patternType: PatternType.NEW_CONTENT,
                    patternName: 'New CAS Item',
                    description: 'Adds a new Create-A-Sim item',
                    confidence: 90,
                    purposeType: ResourcePurposeType.ADDS_CONTENT,
                    evidence
                };
            }
        });
        
        // Texture replacement pattern
        this.patterns.push({
            type: PatternType.REPLACEMENT,
            name: 'Texture Replacement',
            description: 'Replaces an existing texture',
            purposeType: ResourcePurposeType.REPLACES_CONTENT,
            applicableResourceTypes: [
                ResourceTypes.RESOURCE_TYPE_DDS_IMAGE,
                ResourceTypes.RESOURCE_TYPE_PNG_IMAGE,
                ResourceTypes.RESOURCE_TYPE_RLE2_IMAGE
            ],
            detector: (resourceKey, metadata, content) => {
                const evidence: string[] = [];
                
                // Images are often replacements
                evidence.push('Resource is an image');
                
                // Check for replacement indicators
                if (metadata.isReplacement) {
                    evidence.push('Resource is marked as a replacement');
                }
                
                if (metadata.description && (
                    metadata.description.includes('replace') ||
                    metadata.description.includes('recolor') ||
                    metadata.description.includes('retexture')
                )) {
                    evidence.push(`Description contains replacement keywords: ${metadata.description}`);
                }
                
                return {
                    patternType: PatternType.REPLACEMENT,
                    patternName: 'Texture Replacement',
                    description: 'Replaces an existing texture',
                    confidence: 80,
                    purposeType: ResourcePurposeType.REPLACES_CONTENT,
                    evidence
                };
            }
        });
        
        // Utility script pattern
        this.patterns.push({
            type: PatternType.UTILITY,
            name: 'Utility Script',
            description: 'Provides utility functions or services',
            purposeType: ResourcePurposeType.UTILITY,
            applicableResourceTypes: [ResourceTypes.RESOURCE_TYPE_SCRIPT],
            detector: (resourceKey, metadata, content) => {
                const evidence: string[] = [];
                
                // Check for utility indicators
                if (metadata.modType === 'utility') {
                    evidence.push('Resource is marked as a utility mod');
                }
                
                if (content) {
                    if (content.includes('class') && (
                        content.includes('Service') ||
                        content.includes('Manager') ||
                        content.includes('Helper') ||
                        content.includes('Util')
                    )) {
                        evidence.push('Contains utility class patterns');
                    }
                    
                    if (content.includes('@sims4.commands.Command')) {
                        evidence.push('Contains command decorators');
                    }
                }
                
                if (evidence.length > 0) {
                    return {
                        patternType: PatternType.UTILITY,
                        patternName: 'Utility Script',
                        description: 'Provides utility functions or services',
                        confidence: evidence.length * 30,
                        purposeType: ResourcePurposeType.UTILITY,
                        evidence
                    };
                }
                
                return null;
            }
        });
        
        // New object pattern
        this.patterns.push({
            type: PatternType.NEW_CONTENT,
            name: 'New Object',
            description: 'Adds a new object to the game',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            applicableResourceTypes: [ResourceTypes.RESOURCE_TYPE_OBJECTDEFINITION],
            detector: (resourceKey, metadata, content) => {
                const evidence: string[] = [];
                
                // Object definitions are almost always new content
                evidence.push('Resource is an object definition');
                
                // Check for new content indicators
                if (metadata.isCustomContent) {
                    evidence.push('Resource is marked as custom content');
                }
                
                return {
                    patternType: PatternType.NEW_CONTENT,
                    patternName: 'New Object',
                    description: 'Adds a new object to the game',
                    confidence: 90,
                    purposeType: ResourcePurposeType.ADDS_CONTENT,
                    evidence
                };
            }
        });
    }
    
    /**
     * Analyze a resource for patterns
     * @param resourceKey Resource key
     * @param metadata Resource metadata
     * @param content Resource content
     * @returns Array of pattern matches
     */
    public analyzePatterns(
        resourceKey: ResourceKey,
        metadata: Record<string, any>,
        content?: string
    ): PatternMatch[] {
        try {
            const matches: PatternMatch[] = [];
            
            // Apply applicable patterns
            for (const pattern of this.patterns) {
                // Skip if resource type doesn't match
                if (!pattern.applicableResourceTypes.includes(resourceKey.type)) {
                    continue;
                }
                
                // Apply detector
                const match = pattern.detector(resourceKey, metadata, content);
                if (match) {
                    matches.push(match);
                }
            }
            
            // Sort by confidence (descending)
            return matches.sort((a, b) => b.confidence - a.confidence);
        } catch (error) {
            this.logger.error(`Error analyzing patterns for resource ${resourceKey.type}:${resourceKey.instance}:`, error);
            return [];
        }
    }
    
    /**
     * Get the best pattern match
     * @param resourceKey Resource key
     * @param metadata Resource metadata
     * @param content Resource content
     * @returns Best pattern match or null if none found
     */
    public getBestPatternMatch(
        resourceKey: ResourceKey,
        metadata: Record<string, any>,
        content?: string
    ): PatternMatch | null {
        const matches = this.analyzePatterns(resourceKey, metadata, content);
        return matches.length > 0 ? matches[0] : null;
    }
}
