/**
 * Unit tests for DependencyGraphBuilder
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DependencyGraphBuilder } from '../../../../services/analysis/semantic/dependencyGraph/dependencyGraphBuilder.js';
import { DatabaseService } from '../../../../services/databaseService.js';

// Mock the database service
const mockDatabaseService = {
  resources: {
    getAllResources: vi.fn(),
    getResourcesByPackageId: vi.fn(),
    getResourceById: vi.fn(),
    getResourcesByTGI: vi.fn()
  },
  dependencies: {
    getAllDependencies: vi.fn(),
    getDependenciesByPackageId: vi.fn(),
    getDependencies: vi.fn()
  }
} as unknown as DatabaseService;

describe('DependencyGraphBuilder', () => {
  let dependencyGraphBuilder: DependencyGraphBuilder;
  
  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();
    
    // Create a new instance of DependencyGraphBuilder
    dependencyGraphBuilder = new DependencyGraphBuilder(mockDatabaseService);
  });
  
  afterEach(() => {
    // Clear the dependency graph
    dependencyGraphBuilder.clear();
  });
  
  it('should initialize correctly', () => {
    expect(dependencyGraphBuilder).toBeDefined();
  });
  
  it('should build a graph for all resources', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphBuilder.buildGraph();
    
    // Check that the graph was built
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').has('2')).toBe(true);
    expect(dependencyGraphBuilder.getDependents('2').has('1')).toBe(true);
  });
  
  it('should build a graph for a specific package', async () => {
    // Mock database responses
    mockDatabaseService.resources.getResourcesByPackageId.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getDependenciesByPackageId.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphBuilder.buildGraphForPackage(1);
    
    // Check that the graph was built
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').has('2')).toBe(true);
    expect(dependencyGraphBuilder.getDependents('2').has('1')).toBe(true);
  });
  
  it('should build a graph for a specific resource', async () => {
    // Mock database responses
    mockDatabaseService.resources.getResourceById.mockResolvedValue(
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' }
    );
    
    mockDatabaseService.dependencies.getDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph
    await dependencyGraphBuilder.buildGraphForResource(1);
    
    // Check that the graph was built
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').has('2')).toBe(true);
    expect(dependencyGraphBuilder.getDependents('2').has('1')).toBe(true);
  });
  
  it('should handle errors when building the graph', async () => {
    // Mock database responses to throw an error
    mockDatabaseService.resources.getAllResources.mockRejectedValue(new Error('Database error'));
    
    // Build the graph
    await expect(dependencyGraphBuilder.buildGraph()).rejects.toThrow('Database error');
    
    // Check that the graph was not built
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(false);
  });
  
  it('should handle missing resources', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    // Mock getResourcesByTGI to return an empty array (resource not found)
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([]);
    
    // Build the graph
    await dependencyGraphBuilder.buildGraph();
    
    // Check that the graph was built but without the missing resource
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').size).toBe(0);
  });
  
  it('should handle weak dependencies', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Optional' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockResolvedValue([
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Build the graph without weak dependencies
    await dependencyGraphBuilder.buildGraph({ includeWeakDependencies: false });
    
    // Check that the graph was built but without the weak dependency
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').size).toBe(0);
    
    // Build the graph with weak dependencies
    await dependencyGraphBuilder.buildGraph({ includeWeakDependencies: true });
    
    // Check that the graph was built with the weak dependency
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').has('2')).toBe(true);
  });
  
  it('should process dependencies in batches', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' }
    ]);
    
    // Create a large number of dependencies
    const dependencies = Array.from({ length: 100 }, (_, i) => ({
      resourceId: 1,
      targetType: 0x545AC67A,
      targetGroup: 0n,
      targetInstance: BigInt(i + 1),
      referenceType: 'Required'
    }));
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue(dependencies);
    
    // Mock getResourcesByTGI to return a resource for each dependency
    mockDatabaseService.resources.getResourcesByTGI.mockImplementation((type, group, instance) => {
      const instanceNum = parseInt(instance);
      return Promise.resolve([
        { id: instanceNum + 1, type, group, instance, resourceType: 'SIMDATA' }
      ]);
    });
    
    // Build the graph with a small batch size
    await dependencyGraphBuilder.buildGraph({ batchSize: 10 });
    
    // Check that the graph was built with all dependencies
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').size).toBe(100);
  });
  
  it('should recursively add dependencies up to the specified depth', async () => {
    // Mock database responses
    mockDatabaseService.resources.getResourceById.mockResolvedValue(
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' }
    );
    
    // First level dependency
    mockDatabaseService.dependencies.getDependencies.mockImplementation((resourceId) => {
      if (resourceId === 1) {
        return Promise.resolve([
          { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
        ]);
      } else if (resourceId === 2) {
        return Promise.resolve([
          { resourceId: 2, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 2n, referenceType: 'Required' }
        ]);
      } else {
        return Promise.resolve([]);
      }
    });
    
    // Mock getResourcesByTGI to return resources
    mockDatabaseService.resources.getResourcesByTGI.mockImplementation((type, group, instance) => {
      if (type === 0x545AC67A && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 2, type, group, instance, resourceType: 'SIMDATA' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '2') {
        return Promise.resolve([{ id: 3, type, group, instance, resourceType: 'TUNING' }]);
      } else {
        return Promise.resolve([]);
      }
    });
    
    // Build the graph with a depth of 2
    await dependencyGraphBuilder.buildGraphForResource(1, { maxDepth: 2 });
    
    // Check that the graph was built with dependencies up to depth 2
    expect(dependencyGraphBuilder.isGraphBuilt()).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('1').has('2')).toBe(true);
    expect(dependencyGraphBuilder.getDependencies('2').has('3')).toBe(true);
  });
});
