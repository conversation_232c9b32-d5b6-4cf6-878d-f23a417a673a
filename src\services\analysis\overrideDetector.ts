import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { PackageAnalysisResult } from '../../types/analysis/PackageAnalysisResult.js';
import { OverrideInfo } from '../../types/database.js';

const logger = new Logger('OverrideDetector');

/**
 * Detects and saves override information to the database based on processed resources
 * across multiple package analysis results.
 * @param databaseService The DatabaseService instance.
 * @param analysisResults The array of PackageAnalysisResult objects.
 */
export async function detectAndSaveOverrides(databaseService: DatabaseService, analysisResults: PackageAnalysisResult[]): Promise<void> {
    logger.info('Performing deferred override checks...');
    // Keep track of all processed resources across packages for efficient lookup
    const allProcessedResources: Map<string, { resourceId: number; packageId: number }[]> = new Map();

    // First pass: Populate the map with all resources from all packages
    analysisResults.forEach(result => {
        const packageId = databaseService.packages.getPackageIdByPath(result.metadata.path);
        if (packageId === -1) {
            logger.warn(`Could not find packageId for ${result.metadata.path} during override check.`);
            return; // Skip if package wasn't saved properly
        }
        result.metadata.processedResources?.forEach((procRes: { resourceId: number; tgiString: string }) => {
            const existing = allProcessedResources.get(procRes.tgiString) || [];
            existing.push({ resourceId: procRes.resourceId, packageId: packageId });
            allProcessedResources.set(procRes.tgiString, existing);
        });
    });

    // Second pass: Check for overrides
    analysisResults.forEach(result => {
        const currentPackageId = databaseService.packages.getPackageIdByPath(result.metadata.path);
        if (currentPackageId === -1) return; // Skip if package wasn't saved
        result.metadata.processedResources?.forEach((procRes: { resourceId: number; tgiString: string }) => {
            const potentialOverrides = allProcessedResources.get(procRes.tgiString);
            if (potentialOverrides && potentialOverrides.length > 1) {
                // Find the resource from a *different* package (if any)
                // Simple approach: find the first one not matching current packageId
                // More robust: find the one with the highest resourceId from a different package?
                const overridden = potentialOverrides.find((p: { resourceId: number; packageId: number }) => p.packageId !== currentPackageId);

                if (overridden) {
                    // Check if this specific override pair was already logged/saved to avoid duplicates if logic runs multiple times
                    // (Could use a Set to track saved override pairs if needed)
                    logger.info(`Override detected: Resource ${procRes.resourceId} (Package ${currentPackageId}) overrides Resource ${overridden.resourceId} (Package ${overridden.packageId}) with TGI ${procRes.tgiString}`);
                    const overrideInfo: OverrideInfo = {
                        packageId: currentPackageId,
                        overridingResourceId: procRes.resourceId,
                        overriddenTGI: procRes.tgiString,
                        overriddenResourceId: overridden.resourceId
                    };
                    databaseService.overrides.saveOverride(overrideInfo);
                }
            }
        });
    });
    logger.info('Deferred override checks complete.');
}