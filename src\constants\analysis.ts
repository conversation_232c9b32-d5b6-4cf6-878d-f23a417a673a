﻿import { BinaryResourceType, BinaryResourceTypeValue } from '../types/resource/core.js'; // Import both type and value

export enum AnalysisStatus {
  IDLE = 'IDLE',
  ANALYZING = 'ANALYZING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR'
}

export enum ConflictType {
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  VERSION_CONFLICT = 'VERSION_CONFLICT',
  DEPENDENCY_CONFLICT = 'DEPENDENCY_CONFLICT',
  METADATA_CONFLICT = 'METADATA_CONFLICT'
}

export interface AnalysisConfig {
  maxFileSize: number;
  allowedTypes: BinaryResourceType[];
  validateResources: boolean;
  checkConflicts: boolean;
  generateRecommendations: boolean;
  timeout: number;
}

export const DEFAULT_ANALYSIS_CONFIG: AnalysisConfig = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedTypes: [
    0x0166038C, // TUNING (not in S4TK enum)
    0x0C772E27, // SCRIPT (not in S4TK enum)
    BinaryResourceTypeValue.SimData,
    BinaryResourceTypeValue.DdsImage, // Using DDS as primary image type
    BinaryResourceTypeValue.ObjectDefinition,
    0 // UNKNOWN
  ],
  validateResources: true,
  checkConflicts: true,
  generateRecommendations: true,
  timeout: 30000 // 30 seconds
};

export const RESOURCE_ANALYSIS_CONSTANTS = {
  MAX_RESOURCE_SIZE: 100 * 1024 * 1024, // 100MB
  MIN_RESOURCE_SIZE: 1, // 1 byte
  VALID_RESOURCE_TYPES: Object.values(BinaryResourceTypeValue), // Use value for Object.values
  CAS_PART_FLAGS: [
    0x00000001, // CAS_PART_FLAG_HAIR
    0x00000002, // CAS_PART_FLAG_FACE
    0x00000004, // CAS_PART_FLAG_BODY
    0x00000008, // CAS_PART_FLAG_SKIN
    0x00000010, // CAS_PART_FLAG_EYES
    0x00000020, // CAS_PART_FLAG_MOUTH
    0x00000040, // CAS_PART_FLAG_NOSE
    0x00000080, // CAS_PART_FLAG_EARS
    0x00000100, // CAS_PART_FLAG_HEAD
    0x00000200, // CAS_PART_FLAG_FEET
    0x00000400, // CAS_PART_FLAG_HANDS
    0x00000800, // CAS_PART_FLAG_LEGS
    0x00001000, // CAS_PART_FLAG_ARMS
    0x00002000, // CAS_PART_FLAG_TAIL
    0x00004000, // CAS_PART_FLAG_WINGS
    0x00008000, // CAS_PART_FLAG_HORNS
    0x00010000, // CAS_PART_FLAG_ACCESSORY
    0x00020000, // CAS_PART_FLAG_OUTFIT
    0x00040000, // CAS_PART_FLAG_SHOES
    0x00080000, // CAS_PART_FLAG_GLASSES
    0x00100000, // CAS_PART_FLAG_HAT
    0x00200000, // CAS_PART_FLAG_JEWELRY
    0x00400000, // CAS_PART_FLAG_NAILS
    0x00800000, // CAS_PART_FLAG_MAKEUP
    0x01000000, // CAS_PART_FLAG_TATTOO
    0x02000000, // CAS_PART_FLAG_BODY_HAIR
    0x04000000, // CAS_PART_FLAG_FUR
    0x08000000, // CAS_PART_FLAG_SCALES
    0x10000000, // CAS_PART_FLAG_FEATHERS
    0x20000000, // CAS_PART_FLAG_SHELL
    0x40000000, // CAS_PART_FLAG_LEAVES
    0x80000000  // CAS_PART_FLAG_PET
  ]
};

