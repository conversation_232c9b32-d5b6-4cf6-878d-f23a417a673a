import winston from 'winston';
import electronLog from 'electron-log';
import path from 'path';
import fs from 'fs';
import os from 'os';

// Determine appropriate log directory based on platform
// For Electron apps, we should use the app's user data directory
let LOG_DIR = 'logs';

// In an Electron environment, use electron-log's path
if (process.env.ELECTRON_RENDERER || process.env.ELECTRON_BROWSER || process.env.ELECTRON_RUN_AS_NODE) {
  LOG_DIR = electronLog.transports.file.getFile().path;
  // Extract just the directory
  LOG_DIR = path.dirname(LOG_DIR);
} else {
  // For non-Electron environments, use a platform-appropriate location
  if (process.env.APPDATA) {
    // Windows
    LOG_DIR = path.join(process.env.APPDATA, 'sims4-mod-manager', 'logs');
  } else if (process.platform === 'darwin') {
    // macOS
    LOG_DIR = path.join(os.homedir(), 'Library', 'Logs', 'sims4-mod-manager');
  } else {
    // Linux and others
    LOG_DIR = path.join(os.homedir(), '.sims4-mod-manager', 'logs');
  }
}

// Ensure log directory exists
try {
  fs.mkdirSync(LOG_DIR, { recursive: true });
} catch (error) {
  console.error(`Failed to create log directory: ${LOG_DIR}`, error);
  // Fall back to temp directory if we can't create our preferred location
  LOG_DIR = path.join(os.tmpdir(), 'sims4-mod-manager-logs');
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

const MAX_SIZE = 5 * 1024 * 1024; // 5MB in bytes
const MAX_FILES = 10; // Keep more log files for better history

// Custom JSON replacer function to handle BigInt values
const jsonReplacer = (key: string, value: any) => {
  // Convert BigInt to string with 'n' suffix to indicate it's a BigInt
  if (typeof value === 'bigint') {
    return value.toString() + 'n';
  }
  return value;
};

// Custom timestamp format that doesn't use fecha library
const customTimestamp = winston.format((info) => {
  const date = new Date();
  const pad = (num: number) => (num < 10 ? '0' + num : num);

  // Format: YYYY-MM-DD HH:mm:ss.SSS
  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());
  const milliseconds = date.getMilliseconds().toString().padStart(3, '0');

  info.timestamp = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  return info;
});

// Custom format for better readability in log files
const customFormat = winston.format.printf(({ level, message, timestamp, service, ...rest }) => {
  const meta = Object.keys(rest).length ? JSON.stringify(rest, jsonReplacer) : '';
  return `${timestamp} [${service}] ${level}: ${message} ${meta}`;
});

// Base logger configuration
const baseLoggerConfig: winston.LoggerOptions = {
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    customTimestamp(),
    winston.format.errors({ stack: true }),
    customFormat
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(LOG_DIR, 'error.log'),
      level: 'error',
      maxsize: MAX_SIZE,
      maxFiles: MAX_FILES,
      tailable: true,
    }),
    new winston.transports.File({
      filename: path.join(LOG_DIR, 'combined.log'),
      maxsize: MAX_SIZE,
      maxFiles: MAX_FILES,
      tailable: true,
    }),
  ],
  // Add exception handling
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(LOG_DIR, 'exceptions.log'),
      maxsize: MAX_SIZE,
      maxFiles: 5,
    })
  ],
  // Don't exit on handled exceptions
  exitOnError: false
};

// Custom console timestamp format that doesn't use fecha library
const consoleTimestamp = winston.format((info) => {
  const date = new Date();
  const pad = (num: number) => (num < 10 ? '0' + num : num);

  // Format: HH:mm:ss
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  info.timestamp = `${hours}:${minutes}:${seconds}`;
  return info;
});

// Development environment configuration
if (process.env.NODE_ENV !== 'production') {
  const consoleFormat = winston.format.combine(
    winston.format.colorize(),
    consoleTimestamp(),
    winston.format.printf(
      ({ level, message, timestamp, service, ...rest }) => {
        const meta = Object.keys(rest).length ? JSON.stringify(rest, jsonReplacer) : '';
        return `${timestamp} ${level} [${service}]: ${message} ${meta}`;
      }
    )
  );

  const consoleTransport = new winston.transports.Console({
    format: consoleFormat,
    handleExceptions: true,
  });

  if (Array.isArray(baseLoggerConfig.transports)) {
    baseLoggerConfig.transports.push(consoleTransport);
  } else {
    baseLoggerConfig.transports = [consoleTransport];
  }
}

// Configure electron-log
electronLog.transports.file.level = process.env.NODE_ENV === 'production' ? 'info' : 'debug';
electronLog.transports.file.maxSize = MAX_SIZE;
electronLog.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] [{processType}] {text}';

// Add custom formatter for electron-log to handle BigInt values
const originalTransform = electronLog.transports.file.transform;
electronLog.transports.file.transform = (msg) => {
  // Handle BigInt values in the data property
  if (msg.data && Array.isArray(msg.data)) {
    msg.data = msg.data.map(item => {
      if (typeof item === 'bigint') {
        return item.toString() + 'n';
      }
      if (typeof item === 'object' && item !== null) {
        try {
          // Convert to string and back to handle BigInt in nested objects
          const jsonStr = JSON.stringify(item, jsonReplacer);
          return JSON.parse(jsonStr);
        } catch (e) {
          // If JSON conversion fails, return the original item
          return item;
        }
      }
      return item;
    });
  }
  return originalTransform ? originalTransform(msg) : msg;
};

// Create logger interface
export interface LoggerInterface extends winston.Logger {
  electron?: typeof electronLog;
}

// Create and configure logger
export function createLogger(name: string): LoggerInterface {
  const logger = winston.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: name },
  }) as LoggerInterface;

  // Add electron logger in electron environment
  if (process.env.ELECTRON_RENDERER || process.env.ELECTRON_BROWSER) {
    logger.electron = electronLog;

    // Configure electron-log for this context
    electronLog.scope(name);
  }

  return logger;
}

// Export a function to get the log directory
export function getLogDirectory(): string {
  return LOG_DIR;
}

export default createLogger;
