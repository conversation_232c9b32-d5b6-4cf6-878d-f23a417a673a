/**
 * <PERSON><PERSON> for mod-specific SimData versions (12345, 21324, etc.)
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { ParsedSimData, VersionHandlerFunction } from '../types.js';
import { parseGenericVersion } from '../../parsers/genericVersionParser.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('ModVersionHandler');

/**
 * Creates a handler function for a mod-specific SimData version
 * @param version SimData version
 * @returns Handler function for the specified version
 */
export function createModVersionHandler(version: number): VersionHandlerFunction {
    return (buffer: Buffer): ParsedSimData | undefined => {
        try {
            logger.info(`Handling mod-specific SimData version ${version}`);
            return parseGenericVersion(buffer);
        } catch (error) {
            return handleVersionError(
                error,
                createVersionErrorContext(version, 'modVersionHandler', { bufferLength: buffer.length }),
                undefined
            );
        }
    };
}

/**
 * Get all mod version handlers (12345, 21324, etc.)
 * @returns Map of version numbers to handler functions
 */
export function getModVersionHandlers(): Map<number, VersionHandlerFunction> {
    const handlers = new Map<number, VersionHandlerFunction>();
    
    // Common mod versions we've seen in the wild
    const modVersions = [12345, 21324, 32768, 42069, 50000, 54321, 60000];
    
    for (const version of modVersions) {
        handlers.set(version, createModVersionHandler(version));
    }
    
    return handlers;
}
