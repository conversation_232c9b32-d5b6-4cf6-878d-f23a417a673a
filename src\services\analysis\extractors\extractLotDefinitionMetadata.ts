import { <PERSON><PERSON><PERSON> } from "../../../types/resource.js";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { LotDefinitionMetadata } from "../../../types/resource/analysis.js";
import { logger } from "../../../utils/logging/logger.js";

/**
 * Extracts metadata from a LOT_DEFINITION resource (0x12952634).
 *
 * @param key The resource key of the LOT_DEFINITION resource.
 * @param buffer The buffer containing the LOT_DEFINITION resource data.
 * @returns The extracted LotDefinitionMetadata.
 */
export function extractLotDefinitionMetadata(key: <PERSON><PERSON><PERSON>, buffer: Buffer): LotDefinitionMetadata | null {
  if (buffer.length < 0x90) {
    logger.warn(`LOT_DEFINITION resource ${key.instance.toString(16)} is too small to contain a valid header.`);
    return null;
  }

  // Assumed offsets based on docs/lot-res.md field order and types
  const lotId = buffer.readUInt32LE(0x00);
  const worldPositionX = buffer.readFloatLE(0x04);
  const worldPositionY = buffer.readFloatLE(0x08);
  const worldPositionZ = buffer.readFloatLE(0x0C);
  const rotation = buffer.readFloatLE(0x10);
  const sizeX = buffer.readUInt16LE(0x14);
  const sizeZ = buffer.readUInt16LE(0x16);
  const flags = buffer.readUInt32LE(0x18);
  const templateHash = buffer.readBigUInt64LE(0x1C);


  const metadata: LotDefinitionMetadata = {
    lotId: lotId,
    worldPosition: { x: worldPositionX, y: worldPositionY, z: worldPositionZ },
    rotation: rotation,
    size: { x: sizeX, z: sizeZ },
    flags: flags,
    templateHash: templateHash,
  };

  logger.info(`Extracted metadata for LOT_DEFINITION resource ${key.instance.toString(16)}`);
  return metadata;
}