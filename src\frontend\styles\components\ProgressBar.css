.progress-container {
  width: 100%;
  max-width: 600px;
  margin: 2rem auto;
  padding: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  transition: width 0.3s ease;
}

.progress-bar::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  background-color: #007bff;
  transform-origin: left;
  transition: transform 0.3s ease;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
}

.progress-percentage {
  font-weight: 500;
  color: #495057;
}

.progress-status {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Error state */
.progress-bar.error {
  background-color: #dc3545;
}

.progress-status.error {
  color: #dc3545;
}

/* Success state */
.progress-bar.success {
  background-color: #28a745;
}

.progress-status.success {
  color: #28a745;
} 