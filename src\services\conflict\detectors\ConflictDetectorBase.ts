import { Logger } from '../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../types/database.js';
import { ConflictInfo } from '../../../types/conflict/index.js';
import { DatabaseService } from '../../databaseService.js';

/**
 * Base interface for conflict detection options
 */
export interface ConflictDetectionOptionsBase {
    /**
     * Whether to enable this detector
     * Default: true
     */
    enabled?: boolean;

    /**
     * Resource types to exclude from conflict detection
     */
    excludeTypes?: number[];

    /**
     * Resource types to include in conflict detection (if specified, only these types will be checked)
     */
    includeTypes?: number[];
}

/**
 * Base class for all conflict detectors
 */
export abstract class ConflictDetectorBase<T extends ConflictDetectionOptionsBase = ConflictDetectionOptionsBase> {
    protected logger: Logger;
    protected databaseService: DatabaseService;
    protected options: T;

    /**
     * Create a new conflict detector
     * @param databaseService Database service instance
     * @param options Options for conflict detection
     * @param logger Optional logger instance
     */
    constructor(
        databaseService: DatabaseService,
        options: T,
        logger?: Logger
    ) {
        this.databaseService = databaseService;
        this.options = options;
        this.logger = logger || new Logger(this.constructor.name);
    }

    /**
     * Detect conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    abstract detectConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null;

    /**
     * Validate a resource to ensure it has all required properties
     * @param resource Resource to validate
     * @returns True if the resource is valid, false otherwise
     */
    protected validateResource(resource: ResourceInfo): boolean {
        // Check if resource is defined
        if (!resource) {
            this.logger.warn(`Resource is undefined or null`);
            return false;
        }

        // Check if resource has required properties
        if (resource.type === undefined || resource.group === undefined || resource.instance === undefined) {
            this.logger.warn(`Resource missing required properties: type=${resource.type}, group=${resource.group}, instance=${resource.instance}`);
            return false;
        }

        // Check if resource has key property
        if (!resource.key) {
            // Create key property if missing
            resource.key = {
                type: resource.type,
                group: resource.group,
                instance: resource.instance
            };
            this.logger.debug(`Created key property for resource`);
        }

        // Check if resource has metadata property
        if (!resource.metadata) {
            // Create metadata property if missing
            resource.metadata = {
                name: resource.resourceType || 'Unknown Resource',
                path: resource.packagePath || '',
                hash: resource.hash || '',
                size: resource.size || 0,
                timestamp: Date.now()
            };
            this.logger.debug(`Created metadata property for resource`);
        }

        return true;
    }

    /**
     * Check if a resource should be skipped based on its type
     * @param resource The resource to check
     * @returns True if the resource should be skipped, false otherwise
     */
    protected shouldSkipResource(resource: ResourceInfo): boolean {
        // Skip if detector is disabled
        if (this.options.enabled === false) {
            return true;
        }

        // Skip if resource type is in excludeTypes
        if (this.options.excludeTypes && this.options.excludeTypes.includes(resource.type)) {
            return true;
        }

        // Skip if includeTypes is specified and resource type is not in it
        if (this.options.includeTypes && this.options.includeTypes.length > 0) {
            return !this.options.includeTypes.includes(resource.type);
        }

        return false;
    }

    /**
     * Detect conflicts between multiple resources
     * @param resources Array of resources to check for conflicts
     * @returns Array of detected conflicts
     */
    detectConflicts(resources: ResourceInfo[]): ConflictInfo[] {
        const conflicts: ConflictInfo[] = [];

        // Validate input
        if (!resources || !Array.isArray(resources)) {
            this.logger.error(`Invalid resources parameter: ${resources}`);
            return conflicts;
        }

        // Skip if detector is disabled
        if (this.options.enabled === false) {
            this.logger.debug(`Detector ${this.constructor.name} is disabled, skipping conflict detection`);
            return conflicts;
        }

        // Filter out invalid resources
        const validResources = resources.filter(resource => this.validateResource(resource));

        if (validResources.length === 0) {
            this.logger.warn(`No valid resources to check for conflicts`);
            return conflicts;
        }

        if (validResources.length < resources.length) {
            this.logger.info(`Filtered out ${resources.length - validResources.length} invalid resources, proceeding with ${validResources.length} valid resources`);
        }

        // Compare each resource with every other resource
        for (let i = 0; i < validResources.length; i++) {
            const resource1 = validResources[i];

            for (let j = i + 1; j < validResources.length; j++) {
                const resource2 = validResources[j];

                // Detect conflict between these two resources
                const conflict = this.detectConflict(resource1, resource2);

                // Add conflict to the list if detected
                if (conflict) {
                    conflicts.push(conflict);
                }
            }
        }

        this.logger.debug(`Detected ${conflicts.length} conflicts among ${validResources.length} resources`);
        return conflicts;
    }
}
