/**
 * Generic handler for unknown SimData versions
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { ParsedSimData, VersionHandlerFunction } from '../types.js';
import { parseGenericVersion } from '../../parsers/genericVersionParser.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('GenericVersionHandler');

/**
 * Generic handler for unknown SimData versions
 * @param buffer SimData buffer
 * @returns Parsed SimData or undefined if parsing fails
 */
export function handleGenericVersion(buffer: Buffer): ParsedSimData | undefined {
    try {
        // Try to read version from buffer
        if (buffer.length < 2) {
            logger.error('Buffer too small to contain version');
            return undefined;
        }
        
        const version = buffer.readUInt16LE(0);
        logger.info(`Handling unknown SimData version ${version} with generic handler`);
        
        return parseGenericVersion(buffer);
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(undefined, 'handleGenericVersion', { bufferLength: buffer.length }),
            undefined
        );
    }
}

/**
 * Creates a handler function for an unknown SimData version
 * @param version SimData version
 * @returns Handler function for the specified version
 */
export function createGenericVersionHandler(version: number): VersionHandlerFunction {
    return (buffer: Buffer): ParsedSimData | undefined => {
        try {
            logger.info(`Handling unknown SimData version ${version} with generic handler`);
            return parseGenericVersion(buffer);
        } catch (error) {
            return handleVersionError(
                error,
                createVersionErrorContext(version, 'genericVersionHandler', { bufferLength: buffer.length }),
                undefined
            );
        }
    };
}
