import express from 'express';
import helmet from 'helmet'; // Import helmet
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import multer from 'multer';
import path from 'path';
// import { fileURLToPath } from 'url'; // Reverted ES Module style
// import { dirname } from 'path'; // Reverted ES Module style
// path is already imported above
import { ModConflictOrchestrator } from './services/ml/ModConflictOrchestrator.js';
import { PackageAnalysisService } from './services/analysis/packageAnalysisService.js';
import { Logger } from './utils/logging/logger.js';
import { promises as fs } from 'fs';
import { createHash } from 'crypto';
import { handleAnalyzePackage, handleComparePackages } from './api/handlers.js'; // Import handlers
import { DatabaseService } from './services/databaseService.js'; // Import DatabaseService
import { configureEventEmitter } from './utils/eventEmitterConfig.js'; // Import EventEmitter configuration
import EnhancedMemoryManager from './utils/memory/enhancedMemoryManager.js'; // Import Enhanced Memory Manager
import resourcePool from './utils/resource/resourcePoolManager.js'; // Import Resource Pool Manager
import { initializeS4TKPlugins } from './utils/s4tk/s4tkPluginManager.js'; // Import S4TK Plugin Manager
import { applyMonkeyPatches } from './utils/monkeyPatch.js'; // Import Monkey Patch utilities

// Apply monkey patches to fix known issues
applyMonkeyPatches();

// Configure EventEmitter to avoid memory leaks
configureEventEmitter();

// Initialize Enhanced Memory Manager
EnhancedMemoryManager.getInstance().initialize();

// Initialize logger
const logger = new Logger('Server');

// Log resource pool configuration
logger.info(`Resource pool initialized with max concurrent operations: ${resourcePool.getStats().peakConcurrent}`);

// Initialize S4TK plugins
initializeS4TKPlugins().then(initialized => {
    logger.info(`S4TK plugins initialized: ${initialized}`);
});

// CommonJS style __dirname
// const __filename = fileURLToPath(import.meta.url); // Reverted ES Module style
// const __dirname = dirname(__filename); // Reverted ES Module style

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});
const upload = multer({ dest: 'uploads/' });

// Initialize services
const dbService = DatabaseService.getInstance(); // Initialize DatabaseService
const conflictOrchestrator = ModConflictOrchestrator.getInstance(logger);
const packageAnalysisService = PackageAnalysisService.getInstance(logger, dbService); // Pass dbService

// Initialize services
(async () => {
  try {
    await Promise.all([
      dbService.initialize(), // Initialize DatabaseService
      conflictOrchestrator.initialize(),
      packageAnalysisService.initialize()
    ]);
    logger.info('Services initialized successfully');
  } catch (error) {
    logger.error(`Error initializing services: ${error}`);
  }
})();

// Middleware
app.use(cors());
app.use(express.json());
// Use helmet with CSP configuration
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        ...helmet.contentSecurityPolicy.getDefaultDirectives(),
        'font-src': ["'self'", 'https://fonts.gstatic.com'], // Allow fonts from self and Google Fonts
        'style-src': ["'self'", "'unsafe-inline'", 'https://cdnjs.cloudflare.com'], // Allow inline styles and Font Awesome
        'script-src': ["'self'", "'unsafe-eval'"], // Allow self and unsafe-eval (needed by some dev tools/libs, review for production)
        'connect-src': ["'self'", 'ws://localhost:8081', 'http://localhost:8501'], // Allow connections to self, WS dev server, and backend API
        'img-src': ["'self'", 'data:'], // Allow images from self and data URIs
      },
    },
  })
);


// Serve static files from public directory
app.use(express.static(path.join(__dirname, '../public')));

// Routes
app.post('/api/analyze', upload.single('file'), (req, res) => handleAnalyzePackage(req, res, packageAnalysisService)); // Use handler function

// Add endpoint for comparing two packages
app.post('/api/compare', upload.array('files', 2), (req, res) => handleComparePackages(req, res, packageAnalysisService, conflictOrchestrator)); // Use handler function

// Socket.IO events
io.on('connection', (socket) => {
  logger.info('Client connected');

  // Send progress updates
  socket.on('requestProgress', () => {
    socket.emit('progress', {
      current: 0,
      total: 100,
      message: 'Ready for analysis',
      status: 'idle'
    });
  });

  socket.on('disconnect', () => {
    logger.info('Client disconnected');
  });

  socket.on('error', (error) => {
    logger.error(`Socket error: ${error}`);
  });
});

// // Catch-all route - Handled by webpack-dev-server during development
// app.get('*', (req, res) => {
//   // Resolve path relative to the compiled server file in dist
//   res.sendFile(path.resolve(__dirname, 'index.html'));
// });

// Start server
const PORT = process.env.PORT || 8501;
httpServer.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
});
