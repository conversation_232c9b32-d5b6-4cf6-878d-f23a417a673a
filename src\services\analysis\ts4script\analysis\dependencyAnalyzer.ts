/**
 * TS4Script Dependency Analyzer
 * 
 * This module provides functionality for analyzing dependencies between Sims 4 scripts and other resources.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { BytecodeInstruction, CodeObject } from '../bytecode/types.js';
import { TS4ScriptResourceReference, TS4ScriptTuningReference } from '../types.js';
import { DatabaseService } from '../../../databaseService.js';

/**
 * TS4Script dependency analyzer
 */
@singleton()
export class DependencyAnalyzer {
    /**
     * Constructor
     * @param logger Logger instance
     * @param databaseService Database service
     */
    constructor(
        private logger: Logger = new Logger('DependencyAnalyzer'),
        private databaseService?: DatabaseService
    ) {}

    /**
     * Set database service
     * @param databaseService Database service
     */
    public setDatabaseService(databaseService: DatabaseService): void {
        this.databaseService = databaseService;
    }

    /**
     * Analyze resource references in code object
     * @param codeObject Code object
     * @param moduleName Module name
     * @param instructions Bytecode instructions
     * @returns Resource references
     */
    public analyzeResourceReferences(codeObject: CodeObject, moduleName: string, instructions: BytecodeInstruction[]): TS4ScriptResourceReference[] {
        try {
            this.logger.debug(`Analyzing resource references in module: ${moduleName}`);

            const resourceReferences: TS4ScriptResourceReference[] = [];
            
            // Find tuning references in the bytecode
            const tuningReferences = this.findTuningReferences(instructions);
            
            // Process each tuning reference
            for (const tuningReference of tuningReferences) {
                // Find resource ID for the tuning reference
                const resourceId = this.findResourceId(tuningReference);
                
                if (resourceId) {
                    // Create resource reference object
                    const resourceReference: TS4ScriptResourceReference = {
                        resourceId,
                        referenceType: tuningReference.resourceType,
                        accessType: tuningReference.accessType
                    };
                    
                    resourceReferences.push(resourceReference);
                }
            }
            
            return resourceReferences;
        } catch (error) {
            this.logger.error(`Error analyzing resource references in module ${moduleName}:`, error);
            return [];
        }
    }

    /**
     * Find tuning references in bytecode
     * @param instructions Bytecode instructions
     * @returns Tuning references
     */
    private findTuningReferences(instructions: BytecodeInstruction[]): TS4ScriptTuningReference[] {
        const tuningReferences: TS4ScriptTuningReference[] = [];
        
        // Look for patterns that indicate tuning references
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            // Look for LOAD_ATTR with 'Types'
            if (instruction.opcodeName === 'LOAD_ATTR' && instruction.argValue === 'Types') {
                // Next instruction should be LOAD_ATTR with the resource type
                const resourceTypeInstruction = instructions[i + 1];
                if (resourceTypeInstruction && resourceTypeInstruction.opcodeName === 'LOAD_ATTR') {
                    const resourceType = resourceTypeInstruction.argValue as string;
                    
                    // Look for different access patterns
                    
                    // Pattern 1: get_instance_manager(Types.X)
                    if (i > 0 && instructions[i - 1].opcodeName === 'LOAD_NAME' && 
                        instructions[i - 1].argValue === 'get_instance_manager') {
                        tuningReferences.push({
                            resourceType,
                            accessType: 'get_instance_manager',
                            lineNumber: instruction.lineNo
                        });
                    }
                    
                    // Pattern 2: TunableInstanceParam(Types.X)
                    if (i > 0 && instructions[i - 1].opcodeName === 'LOAD_NAME' && 
                        instructions[i - 1].argValue === 'TunableInstanceParam') {
                        tuningReferences.push({
                            resourceType,
                            accessType: 'tunable_instance_param',
                            lineNumber: instruction.lineNo
                        });
                    }
                    
                    // Pattern 3: ResourceKey(x, Types.X, y)
                    if (i > 0 && instructions[i - 1].opcodeName === 'LOAD_NAME' && 
                        instructions[i - 1].argValue === 'ResourceKey') {
                        // Look for the instance ID
                        const instanceIdInstruction = instructions[i + 3];
                        if (instanceIdInstruction && instanceIdInstruction.opcodeName === 'LOAD_NAME') {
                            const instanceId = instanceIdInstruction.argValue as string;
                            
                            tuningReferences.push({
                                resourceType,
                                instanceId,
                                accessType: 'resource_key',
                                lineNumber: instruction.lineNo
                            });
                        }
                    }
                }
            }
        }
        
        return tuningReferences;
    }

    /**
     * Find resource ID for tuning reference
     * @param tuningReference Tuning reference
     * @returns Resource ID
     */
    private findResourceId(tuningReference: TS4ScriptTuningReference): number | undefined {
        try {
            if (!this.databaseService) {
                return undefined;
            }
            
            // Query the database for resources matching the tuning reference
            const query = `
                SELECT id FROM Resources
                WHERE resourceType = ?
            `;
            
            const params = [tuningReference.resourceType];
            
            // Add instance ID to query if available
            if (tuningReference.instanceId) {
                query + ` AND instance = ?`;
                params.push(tuningReference.instanceId);
            }
            
            const results = this.databaseService.executeQuery(query, params);
            
            if (results && results.length > 0) {
                return results[0].id;
            }
            
            return undefined;
        } catch (error) {
            this.logger.error(`Error finding resource ID for tuning reference:`, error);
            return undefined;
        }
    }

    /**
     * Save resource references to database
     * @param moduleId Module ID
     * @param resourceReferences Resource references
     */
    public async saveResourceReferences(moduleId: number, resourceReferences: TS4ScriptResourceReference[]): Promise<void> {
        try {
            if (!this.databaseService) {
                return;
            }
            
            this.logger.debug(`Saving ${resourceReferences.length} resource references for module ${moduleId}`);
            
            // Save each resource reference
            for (const resourceReference of resourceReferences) {
                // Check if resource reference already exists
                const existingReference = await this.databaseService.executeQuery(`
                    SELECT id FROM ResourceReferences
                    WHERE sourceId = ? AND targetId = ? AND referenceType = ?
                `, [moduleId, resourceReference.resourceId, resourceReference.referenceType]);
                
                if (existingReference && existingReference.length > 0) {
                    continue;
                }
                
                // Insert new resource reference
                await this.databaseService.executeQuery(`
                    INSERT INTO ResourceReferences (sourceId, targetId, referenceType, accessType)
                    VALUES (?, ?, ?, ?)
                `, [
                    moduleId,
                    resourceReference.resourceId,
                    resourceReference.referenceType,
                    resourceReference.accessType
                ]);
            }
        } catch (error) {
            this.logger.error(`Error saving resource references for module ${moduleId}:`, error);
        }
    }

    /**
     * Find cross-resource relationships
     * @param packageId Package ID
     * @returns Cross-resource relationships
     */
    public async findCrossResourceRelationships(packageId: number): Promise<any[]> {
        try {
            if (!this.databaseService) {
                return [];
            }
            
            this.logger.debug(`Finding cross-resource relationships for package ${packageId}`);
            
            // Query the database for cross-resource relationships
            const relationships = await this.databaseService.executeQuery(`
                SELECT 
                    r1.id as sourceId,
                    r1.type as sourceType,
                    r1.resourceType as sourceResourceType,
                    r2.id as targetId,
                    r2.type as targetType,
                    r2.resourceType as targetResourceType,
                    rr.referenceType,
                    rr.accessType
                FROM ResourceReferences rr
                JOIN Resources r1 ON rr.sourceId = r1.id
                JOIN Resources r2 ON rr.targetId = r2.id
                WHERE r1.packageId = ? OR r2.packageId = ?
            `, [packageId, packageId]);
            
            return relationships || [];
        } catch (error) {
            this.logger.error(`Error finding cross-resource relationships for package ${packageId}:`, error);
            return [];
        }
    }

    /**
     * Analyze script-package relationships
     * @param packageId Package ID
     * @returns Script-package relationships
     */
    public async analyzeScriptPackageRelationships(packageId: number): Promise<any> {
        try {
            if (!this.databaseService) {
                return {};
            }
            
            this.logger.debug(`Analyzing script-package relationships for package ${packageId}`);
            
            // Find all scripts in the package
            const scripts = await this.databaseService.executeQuery(`
                SELECT id, resourceType
                FROM Resources
                WHERE packageId = ? AND resourceType = 'TS4Script'
            `, [packageId]);
            
            if (!scripts || scripts.length === 0) {
                return {};
            }
            
            // Find all resources referenced by the scripts
            const scriptIds = scripts.map((script: any) => script.id);
            const referencedResources = await this.databaseService.executeQuery(`
                SELECT 
                    r.id,
                    r.type,
                    r.resourceType,
                    rr.referenceType,
                    rr.accessType
                FROM ResourceReferences rr
                JOIN Resources r ON rr.targetId = r.id
                WHERE rr.sourceId IN (${scriptIds.join(',')})
            `);
            
            // Group referenced resources by type
            const resourcesByType: Record<string, any[]> = {};
            
            for (const resource of referencedResources) {
                const resourceType = resource.resourceType;
                
                if (!resourcesByType[resourceType]) {
                    resourcesByType[resourceType] = [];
                }
                
                resourcesByType[resourceType].push(resource);
            }
            
            return {
                scriptCount: scripts.length,
                referencedResourceCount: referencedResources.length,
                resourcesByType
            };
        } catch (error) {
            this.logger.error(`Error analyzing script-package relationships for package ${packageId}:`, error);
            return {};
        }
    }
}
