/**
 * Schema Version Registry
 *
 * Maintains a registry of known schemas for each game version
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { SchemaVersion, SchemaVersionDetectionResult, SchemaChangeType } from './interfaces/schemaVersion.js';
import { KNOWN_SIMDATA_SCHEMAS } from './constants/schemaVersions.js';
import { VersionDatabase } from './database/versionDatabase.js';
import { GameVersionDetector } from './gameVersionDetector.js';

/**
 * Registry for schema versions
 */
export class SchemaVersionRegistry {
    private logger: Logger;
    private versionDatabase: VersionDatabase;
    private gameVersionDetector: GameVersionDetector;
    private schemas: Map<string, Map<string, SchemaVersion>> = new Map(); // Map<schemaName, Map<gameVersion, SchemaVersion>>
    private initialized: boolean = false;

    /**
     * Constructor
     * @param databaseService The database service
     * @param gameVersionDetector Optional game version detector
     * @param logger The logger instance
     */
    constructor(
        private databaseService: DatabaseService,
        gameVersionDetector?: GameVersionDetector,
        logger?: Logger
    ) {
        this.logger = logger || new Logger('SchemaVersionRegistry');
        this.versionDatabase = new VersionDatabase(databaseService, this.logger);
        this.gameVersionDetector = gameVersionDetector || new GameVersionDetector(databaseService, this.logger);
    }

    /**
     * Initialize the registry
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Initialize the version database
            await this.versionDatabase.initialize();

            // Initialize the game version detector
            await this.gameVersionDetector.initialize();

            // Load all known schemas into memory
            for (const [schemaName, versions] of Object.entries(KNOWN_SIMDATA_SCHEMAS)) {
                if (!versions) continue;

                const schemaVersions = new Map<string, SchemaVersion>();
                this.schemas.set(schemaName, schemaVersions);

                for (const schema of versions) {
                    schemaVersions.set(schema.gameVersion, schema);

                    // Save to database
                    await this.versionDatabase.saveSchemaVersion(schema);
                }
            }

            this.initialized = true;
            this.logger.info(`Initialized SchemaVersionRegistry with schemas for ${this.schemas.size} schema types`);
        } catch (error) {
            this.logger.error('Error initializing SchemaVersionRegistry:', error);
            throw error;
        }
    }

    /**
     * Detect the schema version for a SimData resource
     * @param resourceId The resource ID in the database
     * @param schemaName The schema name
     * @param schemaData The schema data (serialized)
     * @param gameVersion Optional known game version
     * @returns Schema version detection result
     */
    public async detectSchemaVersion(
        resourceId: number,
        schemaName: string,
        schemaData: string,
        gameVersion?: string
    ): Promise<SchemaVersionDetectionResult> {
        await this.initialize();

        try {
            // Calculate schema hash
            const schemaHash = this.calculateSchemaHash(schemaData);

            // Track version matches and their confidence scores
            const versionMatches: Map<string, number> = new Map();
            let matchExplanations: string[] = [];

            // Get schema versions for this schema name
            const schemaVersions = this.schemas.get(schemaName);

            if (!schemaVersions || schemaVersions.size === 0) {
                // No known versions for this schema
                return {
                    schemaVersion: 'unknown',
                    gameVersion: gameVersion || 'unknown',
                    confidence: 0,
                    explanation: `No known versions for schema ${schemaName}`,
                    compatibility: {
                        backwardCompatible: false,
                        forwardCompatible: false,
                        notes: 'Unknown schema compatibility'
                    },
                    timestamp: Date.now()
                };
            }

            // If game version is provided, prioritize that version
            if (gameVersion && schemaVersions.has(gameVersion)) {
                const schema = schemaVersions.get(gameVersion)!;

                // Check if hash matches exactly
                if (schema.schemaHash === schemaHash) {
                    versionMatches.set(gameVersion, 100);
                    matchExplanations.push(`Exact hash match for game version ${gameVersion}`);
                } else {
                    // Compare schema structure
                    const similarity = this.compareSchemas(schemaData, schema.schemaData);
                    versionMatches.set(gameVersion, similarity);
                    matchExplanations.push(`Schema similarity for game version ${gameVersion}: ${similarity}%`);
                }
            }

            // Check all known versions
            for (const [version, schema] of schemaVersions.entries()) {
                // Skip if we already checked this version
                if (gameVersion === version) continue;

                // Check if hash matches exactly
                if (schema.schemaHash === schemaHash) {
                    versionMatches.set(version, 100);
                    matchExplanations.push(`Exact hash match for game version ${version}`);
                } else {
                    // Compare schema structure
                    const similarity = this.compareSchemas(schemaData, schema.schemaData);

                    // Only consider significant similarities
                    if (similarity >= 70) {
                        versionMatches.set(version, similarity);
                        matchExplanations.push(`Schema similarity for game version ${version}: ${similarity}%`);
                    }
                }
            }

            // Convert matches to sorted array
            const sortedMatches = Array.from(versionMatches.entries())
                .sort((a, b) => b[1] - a[1]);

            // Default detection result
            let detectionResult: SchemaVersionDetectionResult = {
                schemaVersion: 'unknown',
                gameVersion: gameVersion || 'unknown',
                confidence: 0,
                explanation: `No matching schema version found for ${schemaName}`,
                compatibility: {
                    backwardCompatible: false,
                    forwardCompatible: false,
                    notes: 'Unknown schema compatibility'
                },
                timestamp: Date.now()
            };

            // If we have matches, update the detection result
            if (sortedMatches.length > 0) {
                const [detectedVersion, score] = sortedMatches[0];
                const schema = schemaVersions.get(detectedVersion);

                if (schema) {
                    // Determine compatibility
                    const compatibility = this.determineCompatibility(schemaName, detectedVersion, schemaData);

                    detectionResult = {
                        schemaVersion: `${schemaName}_${detectedVersion}`,
                        gameVersion: detectedVersion,
                        confidence: score,
                        explanation: matchExplanations.join('. '),
                        compatibility,
                        timestamp: Date.now()
                    };
                }
            }

            // Save to database
            await this.versionDatabase.saveResourceSchemaVersion(detectionResult, resourceId);

            return detectionResult;
        } catch (error) {
            this.logger.error(`Error detecting schema version for resource ${resourceId}, schema ${schemaName}:`, error);

            // Return default result on error
            return {
                schemaVersion: 'unknown',
                gameVersion: gameVersion || 'unknown',
                confidence: 0,
                explanation: 'Error detecting schema version',
                compatibility: {
                    backwardCompatible: false,
                    forwardCompatible: false,
                    notes: 'Error determining compatibility'
                },
                timestamp: Date.now()
            };
        }
    }

    /**
     * Calculate a hash for a schema
     * @param schemaData The schema data (serialized)
     * @returns Hash string
     */
    private calculateSchemaHash(schemaData: string): string {
        try {
            // Simple hash calculation for now
            // In a real implementation, this would use a proper hashing algorithm
            let hash = 0;
            for (let i = 0; i < schemaData.length; i++) {
                hash = ((hash << 5) - hash) + schemaData.charCodeAt(i);
                hash |= 0; // Convert to 32bit integer
            }
            return hash.toString(16);
        } catch (error) {
            this.logger.error('Error calculating schema hash:', error);
            return 'error';
        }
    }

    /**
     * Compare two schemas and return similarity percentage
     * @param schemaData1 The first schema data (serialized)
     * @param schemaData2 The second schema data (serialized)
     * @returns Similarity percentage (0-100)
     */
    private compareSchemas(schemaData1: string, schemaData2: string): number {
        try {
            // Parse schemas
            const schema1 = JSON.parse(schemaData1);
            const schema2 = JSON.parse(schemaData2);

            // Check if schemas have columns
            if (!schema1.columns || !schema2.columns) {
                return 0;
            }

            // Count matching columns
            const columns1 = schema1.columns;
            const columns2 = schema2.columns;

            let matchingColumns = 0;
            let totalColumns = Math.max(columns1.length, columns2.length);

            // Create maps of column name to column
            const columnMap1 = new Map(columns1.map((col: any) => [col.name, col]));
            const columnMap2 = new Map(columns2.map((col: any) => [col.name, col]));

            // Check each column in schema1
            for (const [name, col1] of columnMap1.entries()) {
                const col2 = columnMap2.get(name);

                if (col2) {
                    // Column exists in both schemas
                    if (col1.type === col2.type) {
                        // Exact match
                        matchingColumns += 1;
                    } else {
                        // Same name but different type
                        matchingColumns += 0.5;
                    }
                }
            }

            // Calculate similarity percentage
            return Math.round((matchingColumns / totalColumns) * 100);
        } catch (error) {
            this.logger.error('Error comparing schemas:', error);
            return 0;
        }
    }

    /**
     * Determine compatibility of a schema with other versions
     * @param schemaName The schema name
     * @param detectedVersion The detected game version
     * @param schemaData The schema data (serialized)
     * @returns Compatibility information
     */
    private determineCompatibility(
        schemaName: string,
        detectedVersion: string,
        schemaData: string
    ): { backwardCompatible: boolean; forwardCompatible: boolean; notes: string } {
        try {
            const schemaVersions = this.schemas.get(schemaName);

            if (!schemaVersions || schemaVersions.size === 0) {
                return {
                    backwardCompatible: false,
                    forwardCompatible: false,
                    notes: 'No known versions for comparison'
                };
            }

            // Get all game versions
            const gameVersions = Array.from(schemaVersions.keys())
                .sort((a, b) => this.compareVersions(a, b));

            // Find index of detected version
            const versionIndex = gameVersions.indexOf(detectedVersion);

            if (versionIndex === -1) {
                return {
                    backwardCompatible: false,
                    forwardCompatible: false,
                    notes: `Detected version ${detectedVersion} not found in known versions`
                };
            }

            // Check backward compatibility
            let backwardCompatible = true;
            let backwardNotes = '';

            if (versionIndex > 0) {
                // There are earlier versions
                const earlierVersions = gameVersions.slice(0, versionIndex);

                for (const version of earlierVersions) {
                    const schema = schemaVersions.get(version);
                    if (!schema) continue;

                    const similarity = this.compareSchemas(schemaData, schema.schemaData);

                    if (similarity < 80) {
                        backwardCompatible = false;
                        backwardNotes = `Significant changes from version ${version} (${similarity}% similar)`;
                        break;
                    }
                }
            }

            // Check forward compatibility
            let forwardCompatible = true;
            let forwardNotes = '';

            if (versionIndex < gameVersions.length - 1) {
                // There are later versions
                const laterVersions = gameVersions.slice(versionIndex + 1);

                for (const version of laterVersions) {
                    const schema = schemaVersions.get(version);
                    if (!schema) continue;

                    const similarity = this.compareSchemas(schemaData, schema.schemaData);

                    if (similarity < 80) {
                        forwardCompatible = false;
                        forwardNotes = `Significant changes in version ${version} (${similarity}% similar)`;
                        break;
                    }
                }
            }

            // Combine notes
            const notes = [
                backwardNotes,
                forwardNotes,
                backwardCompatible && forwardCompatible ? 'Schema is compatible across versions' : '',
                !backwardCompatible && !forwardCompatible ? 'Schema has significant version-specific changes' : ''
            ].filter(Boolean).join('. ');

            return {
                backwardCompatible,
                forwardCompatible,
                notes
            };
        } catch (error) {
            this.logger.error('Error determining schema compatibility:', error);
            return {
                backwardCompatible: false,
                forwardCompatible: false,
                notes: 'Error determining compatibility'
            };
        }
    }

    /**
     * Compare two version strings
     * @param a First version string
     * @param b Second version string
     * @returns -1 if a < b, 0 if a = b, 1 if a > b
     */
    private compareVersions(a: string, b: string): number {
        const partsA = a.split('.').map(Number);
        const partsB = b.split('.').map(Number);

        for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
            const partA = i < partsA.length ? partsA[i] : 0;
            const partB = i < partsB.length ? partsB[i] : 0;

            if (partA < partB) return -1;
            if (partA > partB) return 1;
        }

        return 0;
    }

    /**
     * Get a schema version by name and game version
     * @param schemaName The schema name
     * @param gameVersion The game version
     * @returns The schema version or undefined if not found
     */
    public async getSchemaVersion(schemaName: string, gameVersion: string): Promise<SchemaVersion | undefined> {
        await this.initialize();

        const schemaVersions = this.schemas.get(schemaName);
        if (!schemaVersions) return undefined;

        return schemaVersions.get(gameVersion);
    }

    /**
     * Get all schema versions for a schema name
     * @param schemaName The schema name
     * @returns Array of schema versions
     */
    public async getSchemaVersions(schemaName: string): Promise<SchemaVersion[]> {
        await this.initialize();

        const schemaVersions = this.schemas.get(schemaName);
        if (!schemaVersions) return [];

        return Array.from(schemaVersions.values());
    }

    /**
     * Register a custom schema version
     * @param schema The schema version to register
     */
    public async registerSchemaVersion(schema: SchemaVersion): Promise<void> {
        await this.initialize();

        try {
            // Get or create schema versions map
            let schemaVersions = this.schemas.get(schema.schemaName);

            if (!schemaVersions) {
                schemaVersions = new Map<string, SchemaVersion>();
                this.schemas.set(schema.schemaName, schemaVersions);
            }

            // Add to memory cache
            schemaVersions.set(schema.gameVersion, schema);

            // Save to database
            await this.versionDatabase.saveSchemaVersion(schema);

            this.logger.info(`Registered custom schema version: ${schema.schemaName} for game version ${schema.gameVersion}`);
        } catch (error) {
            this.logger.error(`Error registering custom schema version ${schema.schemaName} for game version ${schema.gameVersion}:`, error);
            throw error;
        }
    }

    /**
     * Get the detected schema version for a resource
     * @param resourceId The resource ID
     * @returns The schema version detection result or undefined if not found
     */
    public async getDetectedSchemaVersion(resourceId: number): Promise<SchemaVersionDetectionResult | undefined> {
        await this.initialize();
        return this.versionDatabase.getResourceSchemaVersion(resourceId);
    }

    /**
     * Dispose of resources used by the registry
     */
    public async dispose(): Promise<void> {
        try {
            this.logger.info('Disposing SchemaVersionRegistry resources');

            // Clear schemas map
            this.schemas.clear();

            this.initialized = false;
            this.logger.info('SchemaVersionRegistry resources disposed successfully');
        } catch (error) {
            this.logger.error('Error disposing SchemaVersionRegistry resources:', error);
            throw error;
        }
    }
}
