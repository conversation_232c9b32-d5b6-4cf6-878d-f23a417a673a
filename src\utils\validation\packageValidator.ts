﻿import { promises as fs } from 'fs';
import { XMLParser } from 'fast-xml-parser';
import { Logger } from '../logging/logger.js';
import { BinaryResourceType, BinaryResourceTypeValue } from '../../types/resource/core.js'; // Import both type and value

const logger = new Logger('PackageValidator');

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export async function validatePackage(filePath: string): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  try {
    // Check file exists
    await fs.access(filePath);

    // Check file size
    const stats = await fs.stat(filePath);
    if (stats.size === 0) {
      result.errors.push('Package file is empty');
      result.isValid = false;
      return result;
    }

    // Read and parse file content
    const content = await fs.readFile(filePath, 'utf-8');
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@_',
      parseAttributeValue: true,
      parseTagValue: true
    });

    const parsed = parser.parse(content);

    // Validate package structure
    if (!parsed.Package) {
      result.errors.push('Invalid package structure: missing Package root element');
      result.isValid = false;
      return result;
    }

    // Validate metadata
    if (!parsed.Package.MetaData) {
      result.warnings.push('Missing metadata section');
    } else {
      const meta = parsed.Package.MetaData;
      if (!meta.Version) {
        result.warnings.push('Missing version information');
      }
      if (!meta.Author) {
        result.warnings.push('Missing author information');
      }
    }

    // Validate resources
    if (!parsed.Package.Resources) {
      result.errors.push('No resources found in package');
      result.isValid = false;
    } else {
      const resources = Array.isArray(parsed.Package.Resources) 
        ? parsed.Package.Resources 
        : [parsed.Package.Resources];

      for (const resource of resources) {
        // Validate resource type
        if (!resource.Type) {
          result.errors.push('Resource missing type information');
          result.isValid = false;
        } else {
          const type = parseInt(resource.Type);
          if (!Object.values(BinaryResourceTypeValue).includes(type)) { // Use value for Object.values
            result.warnings.push(`Unknown resource type: ${type}`);
          }
        }

        // Validate resource name
        if (!resource.Name) {
          result.errors.push('Resource missing name information');
          result.isValid = false;
        }

        // Validate resource ID
        if (!resource.Id) {
          result.warnings.push(`Resource ${resource.Name} missing ID`);
        }

        // Validate resource dependencies
        if (resource.Dependencies) {
          const deps = Array.isArray(resource.Dependencies) 
            ? resource.Dependencies 
            : [resource.Dependencies];

          for (const dep of deps) {
            if (!dep.Id) {
              result.warnings.push(`Dependency missing ID for resource ${resource.Name}`);
            }
          }
        }
      }
    }

    // Validate tuning data if present
    if (parsed.Package.Tuning) {
      const tuning = parsed.Package.Tuning;
      if (!tuning.Class) {
        result.warnings.push('Tuning data missing class information');
      }
      if (!tuning.Instance) {
        result.warnings.push('Tuning data missing instance information');
      }
    }

    // Validate script data if present
    if (parsed.Package.Script) {
      const script = parsed.Package.Script;
      if (!script.Name) {
        result.warnings.push('Script missing name information');
      }
      if (!script.Content) {
        result.warnings.push('Script missing content');
      }
    }

  } catch (error) {
    logger.error(`Error validating package ${filePath}: ${error}`);
    result.errors.push(`Validation error: ${error}`);
    result.isValid = false;
  }

  return result;
} 
