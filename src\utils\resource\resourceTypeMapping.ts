import { ResourceCategory } from '../../types/resource/enums.js';
import { Logger } from '../logging/logger.js';

const logger = new Logger('ResourceTypeMapping');

/**
 * Information about a resource type, including its name, category, and which extractor to use
 */
export interface ResourceTypeInfo {
    name: string;
    category: ResourceCategory;
    extractor: string;
    description?: string;
    isCommon?: boolean;
}

/**
 * Comprehensive mapping of resource type IDs to their metadata
 * This is used to determine how to process each resource type
 */
export const ResourceTypeMapping: Record<number, ResourceTypeInfo> = {
    // Tuning Resources
    0x220557DA: { name: 'TUNING', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },
    0x545AC67A: { name: 'XML', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },
    0x6017E896: { name: 'SIMDATA', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },
    0x0C772E27: { name: 'STRING_TABLE', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },
    0x0166038C: { name: 'COMBINED_TUNING', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },
    0x7F4AD89D: { name: 'ZONE_MODIFIER_TUNING', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },
    0x3C1D8799: { name: 'XML_UNKNOWN', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },
    0x1659456824: { name: 'COMBINED_TUNING_ALT', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: true },

    // Script Resources
    0x319E4F1D: { name: 'SCRIPT', category: ResourceCategory.SCRIPT, extractor: 'extractScriptMetadata', isCommon: true },
    0xEA5118B0: { name: 'SCRIPT_MODULE', category: ResourceCategory.SCRIPT, extractor: 'extractScriptMetadata', isCommon: true },
    0x3931183280: { name: 'SCRIPT_MODULE_ALT', category: ResourceCategory.SCRIPT, extractor: 'extractScriptMetadata', isCommon: true },
    0x209137191: { name: 'SCRIPT_ALT', category: ResourceCategory.SCRIPT, extractor: 'extractScriptMetadata', isCommon: true },

    // Object Resources
    0x00B2D882: { name: 'OBJECT_DEFINITION', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: true },
    0xD55E5E9D: { name: 'OBJECT_CATALOG', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: true },
    0x832458525: { name: 'OBJECT_DEFINITION_ALT', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: true },
    0x3571055591: { name: 'OBJECT_CATALOG_ALT', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: true },
    0x4253767882: { name: 'OBJECT_CATALOG_SET', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: true },
    0x4223212257: { name: 'BUILD_PART', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: true },
    0x3548561239: { name: 'FOOTPRINT', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: true },
    0x339BC5BD: { name: 'UNKNOWN_OBJECT_TYPE', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: false },

    // CAS Resources
    0x034AEECB: { name: 'CASPART', category: ResourceCategory.OBJECT, extractor: 'extractCasPartMetadata', isCommon: true },
    0x55242443: { name: 'CASPART_ALT', category: ResourceCategory.OBJECT, extractor: 'extractCasPartMetadata', isCommon: true },
    0x55959718: { name: 'CAS_PRESET', category: ResourceCategory.OBJECT, extractor: 'extractCasPartMetadata', isCommon: true },

    // Animation Resources
    0x2C70ADF2: { name: 'ANIMATION', category: ResourceCategory.ANIMATION, extractor: 'extractAnimationMetadata', isCommon: true },
    0x8EAF13DE: { name: 'ANIMATION_STATE_MACHINE', category: ResourceCategory.ANIMATION, extractor: 'extractAnimationMetadata', isCommon: true },
    0x1797309683: { name: 'ANIMATION_STATE_MACHINE_ALT', category: ResourceCategory.ANIMATION, extractor: 'extractAnimationMetadata', isCommon: true },
    0x55867754: { name: 'BONE_DELTA', category: ResourceCategory.ANIMATION, extractor: 'extractAnimationMetadata', isCommon: false },
    0x2393838558: { name: 'ANIMATION_ALT', category: ResourceCategory.ANIMATION, extractor: 'extractAnimationMetadata', isCommon: false },

    // Sound Resources
    0x2026960B: { name: 'SOUND_EFFECT', category: ResourceCategory.SOUND, extractor: 'extractAudioMetadata', isCommon: true },
    0x539399691: { name: 'SOUND_EFFECT_ALT', category: ResourceCategory.SOUND, extractor: 'extractAudioMetadata', isCommon: true },
    0x193723223: { name: 'SOUND_TRACK', category: ResourceCategory.SOUND, extractor: 'extractAudioMetadata', isCommon: true },

    // Image Resources
    0x2E75C764: { name: 'DDS_IMAGE', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: true },
    0x796721156: { name: 'PNG_IMAGE', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: true },
    0x373075429: { name: 'RLE2_IMAGE', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: true },
    0x1492744074: { name: 'CAS_PART_THUMBNAIL', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: true },
    0x3C2A8647: { name: 'BUILD_BUY_THUMBNAIL', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: true },
    0x11720834: { name: 'IMAGE_GENERIC', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: true },
    0xBA856C78: { name: 'SPECULAR_CUBEMAP', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: false },
    0xB8BF1A63: { name: 'SPECULAR_CUBEMAP_ALT', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: false },
    0x3453CF95: { name: 'RLE2_IMAGE_ALT', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: false },
    0x3099531875: { name: 'IMAGE_UNKNOWN', category: ResourceCategory.IMAGE, extractor: 'extractImageMetadata', isCommon: false },

    // Model Resources
    0x01661233: { name: 'MODEL', category: ResourceCategory.OBJECT, extractor: 'extractModelMetadata', isCommon: true },
    0x01D10F34: { name: 'MODEL_LOD', category: ResourceCategory.OBJECT, extractor: 'extractModelMetadata', isCommon: true },
    0x23466547: { name: 'MODEL_ALT', category: ResourceCategory.OBJECT, extractor: 'extractModelMetadata', isCommon: true },
    0x30478132: { name: 'MODEL_LOD_ALT', category: ResourceCategory.OBJECT, extractor: 'extractModelMetadata', isCommon: true },

    // World/Lot Resources
    0x15A1849: { name: 'REGION_MAP', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: false },
    0xAC16FBEC: { name: 'REGION_MAP_ALT', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: false },
    0x1808779248: { name: 'REGION', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: false },
    0x2887187436: { name: 'REGION_DESCRIPTION', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: false },
    0x3321263678: { name: 'REGION_MAP_MAIN', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: false },

    // Miscellaneous Resources
    0xD3044521: { name: 'SLOT', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: false },
    0x3B4C61D: { name: 'LIGHT', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: false },
    0x81CA1A10: { name: 'TUNING_B', category: ResourceCategory.TUNING, extractor: 'extractTuningMetadata', isCommon: false },
    0x3235601127: { name: 'TRAY_ITEM', category: ResourceCategory.OBJECT, extractor: 'extractObjectMetadata', isCommon: false },
    0x6BF15BBE: { name: 'UNKNOWN_RESOURCE', category: ResourceCategory.UNKNOWN, extractor: 'extractTuningMetadata', isCommon: false },

    // Fallback
    0: { name: 'UNKNOWN', category: ResourceCategory.UNKNOWN, extractor: 'extractTuningMetadata', isCommon: false }
};

/**
 * Get the appropriate extractor function name for a given resource type
 * @param resourceType The numeric resource type ID
 * @returns The name of the extractor function to use
 */
export function getExtractorForResourceType(resourceType: number): string {
    const typeInfo = ResourceTypeMapping[resourceType];
    if (typeInfo) {
        return typeInfo.extractor;
    }

    // Default to tuning extractor for unknown types
    logger.warn(`No extractor defined for resource type 0x${resourceType.toString(16)}. Using default tuning extractor.`);
    return 'extractTuningMetadata';
}

/**
 * Get the resource type info for a given resource type
 * @param resourceType The numeric resource type ID
 * @returns The ResourceTypeInfo object or undefined if not found
 */
export function getResourceTypeInfo(resourceType: number): ResourceTypeInfo | undefined {
    return ResourceTypeMapping[resourceType];
}

/**
 * Initialize the resource type mapping system
 */
export function initializeResourceTypeMapping(): void {
    logger.info(`ResourceTypeMapping initialized with ${Object.keys(ResourceTypeMapping).length} entries.`);

    // Log the most common resource types for debugging
    const commonTypes = Object.entries(ResourceTypeMapping)
        .filter(([_, info]) => info.isCommon)
        .map(([id, info]) => `0x${Number(id).toString(16)}: ${info.name} (${info.category})`);

    logger.debug(`Common resource types: ${commonTypes.join(', ')}`);
}
