/**
 * Script Analyzer for TS4Script files
 * 
 * This service combines the decompiler and AST parser to provide comprehensive analysis
 * of Python scripts in TS4Script files.
 * 
 * Features:
 * - Decompile Python bytecode to source code
 * - Parse source code into AST
 * - Extract classes, functions, imports, and other structures
 * - Identify EA-specific patterns in the code
 * - Map relationships between different code elements
 */

import { Logger } from '../../../common/logger.js';
import { BytecodeParseResult } from '../bytecode/types.js';
import { DecompilerService, DecompilationResult, DecompilationOptions } from './decompilerService.js';
import { AstParser, AstParseResult, AstParseOptions } from './astParser.js';

/**
 * Script analysis result
 */
export interface ScriptAnalysisResult {
    success: boolean;
    sourceCode?: string;
    ast?: AstParseResult;
    error?: string;
    confidence: number; // 0-1 confidence score
    pythonVersion?: string;
    decompilerUsed?: string;
}

/**
 * Options for script analysis
 */
export interface ScriptAnalysisOptions {
    decompilationOptions?: DecompilationOptions;
    astParseOptions?: AstParseOptions;
    skipDecompilation?: boolean;
    skipAstParsing?: boolean;
}

/**
 * Service for analyzing Python scripts in TS4Script files
 */
export class ScriptAnalyzer {
    private logger: Logger;
    private decompilerService: DecompilerService;
    private astParser: AstParser;

    /**
     * Create a new ScriptAnalyzer
     * @param logger Logger instance
     */
    constructor(logger: Logger) {
        this.logger = logger;
        this.decompilerService = new DecompilerService(logger);
        this.astParser = new AstParser(logger);
    }

    /**
     * Analyze Python bytecode
     * @param bytecode Python bytecode buffer
     * @param moduleName Name of the module
     * @param options Analysis options
     * @returns Analysis result
     */
    public async analyzeScript(
        bytecode: Buffer | BytecodeParseResult,
        moduleName: string,
        options?: ScriptAnalysisOptions
    ): Promise<ScriptAnalysisResult> {
        const opts = this.mergeOptions(options);
        
        try {
            // Step 1: Decompile bytecode to source code
            let decompilationResult: DecompilationResult | null = null;
            
            if (!opts.skipDecompilation) {
                this.logger.debug(`[ScriptAnalyzer] Decompiling ${moduleName}`);
                decompilationResult = await this.decompilerService.decompileWithFallback(
                    bytecode instanceof Buffer ? bytecode : Buffer.from(bytecode.bytecode),
                    moduleName,
                    opts.decompilationOptions
                );
                
                if (!decompilationResult.success || !decompilationResult.sourceCode) {
                    return {
                        success: false,
                        error: decompilationResult.error || 'Decompilation failed',
                        confidence: 0
                    };
                }
            } else if (bytecode instanceof Object && 'sourceCode' in bytecode) {
                // Use provided source code if available
                decompilationResult = {
                    success: true,
                    sourceCode: (bytecode as any).sourceCode,
                    confidence: 1
                };
            } else {
                return {
                    success: false,
                    error: 'Decompilation skipped but no source code provided',
                    confidence: 0
                };
            }
            
            // Step 2: Parse source code into AST
            if (opts.skipAstParsing) {
                return {
                    success: true,
                    sourceCode: decompilationResult.sourceCode,
                    confidence: decompilationResult.confidence,
                    pythonVersion: decompilationResult.pythonVersion,
                    decompilerUsed: decompilationResult.decompilerUsed
                };
            }
            
            this.logger.debug(`[ScriptAnalyzer] Parsing AST for ${moduleName}`);
            const astResult = await this.astParser.parseSourceCode(
                decompilationResult.sourceCode,
                moduleName,
                opts.astParseOptions
            );
            
            return {
                success: astResult.success,
                sourceCode: decompilationResult.sourceCode,
                ast: astResult,
                error: astResult.success ? undefined : astResult.error,
                confidence: astResult.success ? decompilationResult.confidence : decompilationResult.confidence * 0.5,
                pythonVersion: decompilationResult.pythonVersion,
                decompilerUsed: decompilationResult.decompilerUsed
            };
        } catch (error) {
            this.logger.error(`[ScriptAnalyzer] Error analyzing script: ${error}`);
            return {
                success: false,
                error: `Error analyzing script: ${error.message}`,
                confidence: 0
            };
        }
    }

    /**
     * Merge default options with provided options
     * @param options User-provided options
     * @returns Merged options
     */
    private mergeOptions(options?: ScriptAnalysisOptions): ScriptAnalysisOptions {
        return {
            decompilationOptions: options?.decompilationOptions || {},
            astParseOptions: options?.astParseOptions || {},
            skipDecompilation: options?.skipDecompilation || false,
            skipAstParsing: options?.skipAstParsing || false
        };
    }
}
