﻿﻿// Corrected imports
import { BinaryResourceType } from './core.js';
import { ResourceKey, ResourceMetadata } from './interfaces.js';
import { ResourceTypeMetadata } from './metadata.js'; // Corrected import name
import { ResourceEntry } from './entry.js';
import { ExtendedResource } from './extended.js';

/**
 * Resource resolver interface
 */
export interface ResourceResolver {
  resolveType(key: ResourceKey): BinaryResourceType;
  resolveMetadata(key: ResourceKey): ResourceMetadata | undefined;
  resolveKey(type: BinaryResourceType, group: bigint, instance: bigint): ResourceKey;
  resolveTypeFromName(name: string): BinaryResourceType | undefined;
  resolveTypeFromPath(path: string): BinaryResourceType | undefined;
  resolveTypeFromBuffer(buffer: Buffer): BinaryResourceType | undefined;
  resolve(key: ResourceKey): Promise<ResourceEntry | null>;
  resolveExtended(key: ResourceKey): Promise<ExtendedResource | null>;
  resolveAll(): Promise<ResourceEntry[]>;
  resolveAllMetadata(): Promise<ResourceMetadata[]>;
  resolveAllExtended(): Promise<ExtendedResource[]>;
  resolveByType(type: number): Promise<ResourceEntry[]>;
  resolveByGroup(group: bigint): Promise<ResourceEntry[]>;
  resolveByInstance(instance: bigint): Promise<ResourceEntry[]>;
  resolveByPath(path: string): Promise<ResourceEntry | null>;
  resolveByHash(hash: string): Promise<ResourceEntry | null>;
  resolveByTimestamp(timestamp: number): Promise<ResourceEntry[]>;
  resolveByVersion(version: string): Promise<ResourceEntry[]>;
  resolveBySource(source: string): Promise<ResourceEntry[]>;
  resolveConflicts(key: ResourceKey): Promise<ExtendedResource[]>;
  resolveDependencies(key: ResourceKey): Promise<ExtendedResource[]>;
  resolveDependents(key: ResourceKey): Promise<ExtendedResource[]>;
  resolveModified(): Promise<ResourceEntry[]>;
  resolveCompressed(): Promise<ResourceEntry[]>;
  resolveEncrypted(): Promise<ResourceEntry[]>;
  resolveCustom(key: string, value: unknown): Promise<ResourceEntry[]>;
}

/**
 * Resource type resolver interface
 */
export interface ResourceTypeResolver {
  resolveType(key: ResourceKey): BinaryResourceType;
  resolveTypeFromName(name: string): BinaryResourceType | undefined;
  resolveTypeFromPath(path: string): BinaryResourceType | undefined;
  resolveTypeFromBuffer(buffer: Buffer): BinaryResourceType | undefined;
  resolveTypeMetadata(type: BinaryResourceType): ResourceMetadata; // Use ResourceMetadata from interfaces.js
  resolveTypeContent(data: Buffer, type: BinaryResourceType): unknown;
  resolveTypeConflicts(type: BinaryResourceType): string[];
  resolveTypeDependencies(type: BinaryResourceType): string[];
  resolveTypeOverrides(type: BinaryResourceType): string[];
}

/**
 * Resource metadata resolver interface
 */
export interface ResourceMetadataResolver {
  resolveMetadata(key: ResourceKey): ResourceMetadata | undefined;
  resolveMetadataFromName(name: string): ResourceMetadata | undefined;
  resolveMetadataFromPath(path: string): ResourceMetadata | undefined;
  resolveMetadataFromBuffer(buffer: Buffer): ResourceMetadata | undefined;
}

export interface ResourceResolverOptions {
  cacheEnabled?: boolean;
  cacheSize?: number;
  cacheTimeout?: number;
  validateData?: boolean;
  validateMetadata?: boolean;
  validateCustomData?: boolean;
  maxDataSize?: number;
  maxMetadataSize?: number;
  maxCustomDataSize?: number;
  timeout?: number;
}

export interface ResourceResolverStats {
  totalResolved: number;
  cacheHits: number;
  cacheMisses: number;
  cacheSize: number;
  cacheUsage: number;
  averageResolutionTime: number;
  errors: number;
  warnings: number;
}

export interface ResourceResolverResult {
  type: BinaryResourceType; // Removed TuningResourceType
  metadata: ResourceMetadata;
  typeMetadata: ResourceTypeMetadata; // This should now be correctly resolved
  customData?: any;
  conflicts?: string[];
  dependencies?: string[];
  overrides?: string[];
  stats?: ResourceResolverStats;
  errors?: Error[];
  warnings?: string[];
}

export interface ResourceResolverCache {
  get(key: string): ResourceResolverResult | undefined;
  set(key: string, value: ResourceResolverResult): void;
  has(key: string): boolean;
  delete(key: string): void;
  clear(): void;
  size: number;
  stats: ResourceResolverStats;
}
