# New Player First-Time Setup Workflow
# This scenario simulates a new Sims 4 player setting up mod management for the first time

metadata:
  name: "New Player First-Time Setup"
  description: "Simulates a new player's first experience with mod management - scanning existing mods, organizing them, and resolving conflicts"
  version: "1.0.0"
  author: "Sims4ModManagement Team"
  tags: ["new-player", "onboarding", "basic-workflow"]
  difficulty: "beginner"
  estimatedDuration: 15 # minutes

prerequisites:
  minModCount: 5
  maxModCount: 100
  requiredFeatures:
    - "mod_scanning"
    - "basic_categorization"
    - "conflict_detection"
    - "guided_resolution"
  hardwareRequirements:
    minMemoryMB: 1024
    minStorageGB: 2
    recommendedCPUCores: 2

actions:
  - id: "scan_existing_mods"
    type: "scan_mod_collection"
    name: "Scan Existing Mods"
    description: "Discover and catalog existing mods in the Sims 4 mods folder"
    parameters:
      scanPath: "${SIMS4_MODS_PATH}"
      includeSubfolders: true
      scanDepth: 3
      progressFeedback: true
    timeout: 300 # 5 minutes
    retryPolicy:
      maxRetries: 2
      backoffStrategy: "linear"
      retryableErrors: ["FILESYSTEM_ERROR", "PERMISSION_DENIED"]
    validation:
      successCriteria:
        - "mods_discovered > 0"
        - "scan_completed_successfully"
        - "no_critical_errors"
      performanceThresholds:
        maxScanTime: 180 # 3 minutes for up to 100 mods
        maxMemoryUsage: 512 # MB
      stateChecks:
        - "database_populated"
        - "mod_registry_updated"
    guiSimulation:
      interfaceType: "wizard"
      userInteractions:
        - type: "click"
          target: "scan_button"
          expectedResponse: "progress_dialog_shown"
          timing:
            delay: 1000
            duration: 500
        - type: "confirm"
          target: "scan_complete_dialog"
          expectedResponse: "results_displayed"
          timing:
            delay: 2000
            duration: 300
      visualFeedback:
        - "progress_bar"
        - "file_count_display"
        - "completion_notification"

  - id: "auto_categorize_mods"
    type: "categorize_mods"
    name: "Automatic Mod Categorization"
    description: "Automatically categorize discovered mods based on content analysis"
    parameters:
      useAIClassification: true
      confidenceThreshold: 0.7
      allowManualOverride: true
      createNewCategories: false
    timeout: 120 # 2 minutes
    retryPolicy:
      maxRetries: 1
      backoffStrategy: "exponential"
      retryableErrors: ["ANALYSIS_TIMEOUT", "CLASSIFICATION_ERROR"]
    validation:
      successCriteria:
        - "categorization_rate > 80%" # At least 80% of mods categorized
        - "no_duplicate_categories"
        - "valid_category_assignments"
      performanceThresholds:
        maxCategorizationTime: 90 # 1.5 minutes
        maxMemoryIncrease: 256 # MB
      stateChecks:
        - "categories_created"
        - "mod_assignments_saved"
    guiSimulation:
      interfaceType: "form"
      userInteractions:
        - type: "select"
          target: "categorization_method"
          value: "automatic"
          expectedResponse: "options_updated"
          timing:
            delay: 500
            duration: 200
        - type: "click"
          target: "start_categorization"
          expectedResponse: "categorization_started"
          timing:
            delay: 1000
            duration: 300
      visualFeedback:
        - "category_tree_view"
        - "mod_assignment_preview"
        - "confidence_indicators"

  - id: "detect_conflicts"
    type: "conflict_detection"
    name: "Conflict Detection"
    description: "Scan for potential conflicts between installed mods"
    parameters:
      detectionMethods: ["tgi", "content", "script"]
      thoroughnessLevel: "standard"
      includeMinorConflicts: false
      generateReport: true
    timeout: 180 # 3 minutes
    retryPolicy:
      maxRetries: 2
      backoffStrategy: "linear"
      retryableErrors: ["ANALYSIS_ERROR", "MEMORY_PRESSURE"]
    validation:
      successCriteria:
        - "conflict_scan_completed"
        - "results_generated"
        - "no_false_positives_detected"
      performanceThresholds:
        maxDetectionTime: 150 # 2.5 minutes
        maxMemoryUsage: 1024 # MB
      stateChecks:
        - "conflicts_database_updated"
        - "conflict_report_generated"
    guiSimulation:
      interfaceType: "wizard"
      userInteractions:
        - type: "click"
          target: "detect_conflicts_button"
          expectedResponse: "detection_started"
          timing:
            delay: 500
            duration: 300
        - type: "select"
          target: "detection_thoroughness"
          value: "standard"
          expectedResponse: "settings_applied"
          timing:
            delay: 1000
            duration: 200
      visualFeedback:
        - "detection_progress"
        - "conflict_count_display"
        - "severity_indicators"

  - id: "guided_conflict_resolution"
    type: "conflict_resolution"
    name: "Guided Conflict Resolution"
    description: "Provide guided assistance for resolving detected conflicts"
    parameters:
      resolutionMode: "guided"
      autoResolveSimple: true
      requireConfirmation: true
      explainRecommendations: true
    timeout: 600 # 10 minutes (user interaction time)
    retryPolicy:
      maxRetries: 0 # No retries for user interaction
      backoffStrategy: "linear"
      retryableErrors: []
    validation:
      successCriteria:
        - "conflicts_addressed"
        - "user_choices_recorded"
        - "resolution_applied_successfully"
      performanceThresholds:
        maxResolutionTime: 300 # 5 minutes for automated parts
        maxMemoryUsage: 512 # MB
      stateChecks:
        - "conflict_resolutions_saved"
        - "mod_states_updated"
    guiSimulation:
      interfaceType: "wizard"
      userInteractions:
        - type: "click"
          target: "start_resolution_wizard"
          expectedResponse: "wizard_opened"
          timing:
            delay: 1000
            duration: 500
        - type: "select"
          target: "resolution_option"
          value: "recommended"
          expectedResponse: "option_selected"
          timing:
            delay: 2000
            duration: 300
        - type: "confirm"
          target: "apply_resolution"
          expectedResponse: "resolution_applied"
          timing:
            delay: 1000
            duration: 500
      visualFeedback:
        - "conflict_details_panel"
        - "resolution_options_list"
        - "impact_preview"
        - "progress_tracker"

  - id: "validate_setup"
    type: "validation"
    name: "Setup Validation"
    description: "Validate that the initial setup was completed successfully"
    parameters:
      checkModIntegrity: true
      validateCategories: true
      verifyConflictResolution: true
      generateSummaryReport: true
    timeout: 60 # 1 minute
    retryPolicy:
      maxRetries: 1
      backoffStrategy: "linear"
      retryableErrors: ["VALIDATION_ERROR"]
    validation:
      successCriteria:
        - "all_mods_accessible"
        - "categories_valid"
        - "no_unresolved_conflicts"
        - "system_stable"
      performanceThresholds:
        maxValidationTime: 45 # 45 seconds
        maxMemoryUsage: 256 # MB
      stateChecks:
        - "database_consistent"
        - "file_system_organized"
        - "user_preferences_saved"
    guiSimulation:
      interfaceType: "form"
      userInteractions:
        - type: "click"
          target: "run_validation"
          expectedResponse: "validation_started"
          timing:
            delay: 500
            duration: 200
      visualFeedback:
        - "validation_checklist"
        - "success_indicators"
        - "summary_report"

validation:
  required:
    functionalChecks:
      - "mods_scanned_successfully"
      - "categorization_completed"
      - "conflicts_detected_and_resolved"
      - "system_state_consistent"
    performanceChecks:
      - "total_time_under_15_minutes"
      - "memory_usage_under_2GB"
      - "no_system_crashes"
    stateChecks:
      - "database_integrity_maintained"
      - "file_system_unchanged"
      - "user_preferences_preserved"
  optional:
    qualityChecks:
      - "categorization_accuracy_above_85%"
      - "conflict_detection_precision_above_90%"
      - "user_satisfaction_indicators"
    usabilityChecks:
      - "clear_progress_feedback"
      - "intuitive_interface_flow"
      - "helpful_error_messages"
  thresholds:
    maxExecutionTime: 900 # 15 minutes
    maxMemoryUsage: 2048 # 2GB
    minSuccessRate: 95 # 95% success rate

benchmarks:
  performance:
    executionTime:
      min: 300 # 5 minutes
      max: 900 # 15 minutes
      target: 600 # 10 minutes
    memoryUsage:
      min: 512 # 512MB
      max: 2048 # 2GB
      target: 1024 # 1GB
    cpuUsage:
      min: 10 # 10%
      max: 80 # 80%
      target: 40 # 40%
  scalability:
    modCounts: [10, 25, 50, 100]
    expectedLinearScaling: true
    maxDegradation: 20 # 20% performance degradation acceptable
  reliability:
    successRate: 95 # 95% success rate required
    errorRecovery: true
    dataIntegrity: true

aiMetadata:
  learningObjectives:
    - "Understand new player onboarding flow"
    - "Identify friction points in initial setup"
    - "Validate automatic categorization accuracy"
    - "Assess conflict resolution guidance effectiveness"
  successIndicators:
    - "High completion rate (>95%)"
    - "Low user confusion indicators"
    - "Accurate automatic categorization"
    - "Effective conflict resolution"
  failurePatterns:
    - "Scan timeout or failure"
    - "Poor categorization accuracy"
    - "Unresolved conflicts"
    - "User abandonment during process"
