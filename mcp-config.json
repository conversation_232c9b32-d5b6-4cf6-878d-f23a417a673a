{"servers": {"conflict-detection": {"command": "npx", "args": ["tsx", "mcp-servers/conflict-detection/server.ts"]}, "resource-analysis": {"command": "npx", "args": ["tsx", "mcp-servers/resource-analysis/server.ts"]}, "mod-organization": {"command": "npx", "args": ["tsx", "mcp-servers/mod-organization/server.ts"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE", "MODEL": "claude-3-7-sonnet-20250219", "PERPLEXITY_MODEL": "sonar-pro", "MAX_TOKENS": "64000", "TEMPERATURE": "0.2", "DEFAULT_SUBTASKS": "5", "DEFAULT_PRIORITY": "medium"}}}}