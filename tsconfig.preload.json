{
  "compilerOptions": {
    "target": "ES2020", // Target modern Node.js versions used by Electron
    "module": "CommonJS", // Preload script needs to be CommonJS for Electron
    "outDir": "./dist/main-process/frontend/electron", // Output alongside main.js
    "rootDir": "./src/frontend/electron", // Define root for structure (optional)
    "strict": true, // Enable strict type-checking
    "esModuleInterop": true, // Allows default imports from CommonJS modules
    "skipLibCheck": true, // Speeds up compilation
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "Node", // Use Node.js module resolution
    "sourceMap": true, // Generate source maps for debugging
    "declaration": false // No need for declaration files for preload
  },
  "include": [
    "src/frontend/electron/preload.ts" // Only compile this file
  ],
  "exclude": [
    "node_modules",
    "dist" // Exclude build output directories
  ]
}
