<template>
  <AppLayout>
    <!-- Content that goes into the default slot of AppLayout -->
    <!-- Removed placeholder message -->
    <!-- Integrate FileUploadComponent -->
    <FileUploadComponent />
    <!-- Integrate ProgressBarComponent -->
    <ProgressBarComponent />
    <!-- Integrate ResultsPanelComponent -->
    <ResultsPanelComponent />
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import AppLayout from './components/AppLayout.vue'; // Import the layout component
import FileUploadComponent from './components/FileUploadComponent.vue';
import ProgressBarComponent from './components/ProgressBarComponent.vue'; // Uncommented
import ResultsPanelComponent from './components/ResultsPanelComponent.vue'; // Uncommented
import { useAnalysisStore } from './store/analysis';

const message = ref('Welcome to the Vue 3 App!'); // Keep or remove as needed
const analysisStore = useAnalysisStore(); // Get store instance

onMounted(() => {
  console.log('App.vue mounted. Waiting briefly before setting up IPC listeners...');

  // Wait a short period (e.g., 100ms) before checking for the API
  setTimeout(() => {
    console.log('App.vue: Attempting to register listeners after delay...');
    // Check if the exposed electronAPI.on function exists
  if (window.electronAPI?.on) {
    console.log('window.electronAPI.on found. Registering listeners.');

    // Listener for progress updates
    window.electronAPI.on('analysis-progress', (data: { progress: number; status: string }) => {
      console.log('App.vue received analysis-progress:', data);
      if (data && typeof data.progress === 'number' && typeof data.status === 'string') {
        analysisStore.updateProgress(data.progress, data.status);
      } else {
         console.error('App.vue received invalid analysis-progress data:', data);
      }
    });

    // Listener for analysis completion
    window.electronAPI.on('analysis-complete', (data: any) => {
       console.log('App.vue received analysis-complete:', data);
       if (data && Array.isArray(data.individualResults)) { // Basic check
         analysisStore.setResults(data); // Pass the whole structure
       } else {
          console.error('App.vue received invalid analysis-complete data:', data);
          analysisStore.setError('Received invalid result data from backend.');
       }
    });

    // Listener for analysis errors
    window.electronAPI.on('analysis-error', (error: any) => {
      console.log('App.vue received analysis-error:', error);
      let errorMessage = String(error || 'An unknown error occurred during analysis.');
      // Attempt to extract message if it's an object-like error
      if (error && typeof error === 'object' && 'message' in error) {
          errorMessage = String(error.message);
      }
      analysisStore.setError(errorMessage);
    });

    console.log('App.vue IPC listeners registered successfully.');

  } else {
      console.error('App.vue: window.electronAPI.on is not available after delay.');
     // Optionally, set an error state in the store here
     analysisStore.setError('Failed to initialize communication channel with the backend.');
    }
  }, 100); // Wait 100 milliseconds
});

// Define methods or reactive state for file upload, progress, results etc. later
// This logic will likely move into Pinia stores and specific components
</script>

<style>
/* Global styles can remain here or be moved to a separate CSS file imported in index.ts */
body {
  margin: 0; /* Remove default body margin */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  background-color: #f8f9fa; /* Light background for the whole page */
}

/* Scoped styles specific to App.vue could go here if needed */
</style>
