/**
 * Gameplay System Analyzer
 *
 * Analyzes which gameplay systems (needs, emotions, traits, etc.) are impacted by mods.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { injectable } from '../../di/decorators.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';

/**
 * Gameplay system information
 */
export interface GameplaySystem {
    /**
     * System name
     */
    name: string;
    
    /**
     * System description
     */
    description: string;
    
    /**
     * Impact score (0-100)
     */
    impactScore: number;
    
    /**
     * Resources that impact this system
     */
    resources: {
        /**
         * Resource ID
         */
        resourceId: number;
        
        /**
         * Resource type
         */
        resourceType: number;
        
        /**
         * Impact description
         */
        impactDescription: string;
        
        /**
         * Impact strength (0-100)
         */
        impactStrength: number;
    }[];
}

/**
 * Package gameplay analysis result
 */
export interface PackageGameplayAnalysis {
    /**
     * Package ID
     */
    packageId: number;
    
    /**
     * Package name
     */
    packageName: string;
    
    /**
     * Impacted gameplay systems
     */
    systems: GameplaySystem[];
    
    /**
     * Overall impact score (0-100)
     */
    overallImpactScore: number;
    
    /**
     * Primary gameplay category
     */
    primaryCategory: string;
    
    /**
     * Analysis timestamp
     */
    timestamp: number;
}

/**
 * Gameplay system analyzer
 */
@injectable()
export class GameplaySystemAnalyzer {
    private logger: Logger;
    private databaseService: DatabaseService;
    private knownGameplaySystems: Map<string, {
        name: string;
        description: string;
        keywords: string[];
        resourceTypes: number[];
        importance: number;
    }>;
    private isInitialized: boolean = false;
    
    /**
     * Constructor
     * @param databaseService Database service
     * @param logger Logger instance
     */
    constructor(
        databaseService: DatabaseService,
        logger?: Logger
    ) {
        this.logger = logger || new Logger('GameplaySystemAnalyzer');
        this.databaseService = databaseService;
        
        // Initialize known gameplay systems
        this.knownGameplaySystems = new Map();
        this.initializeKnownSystems();
    }
    
    /**
     * Initialize the analyzer
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }
        
        try {
            // Check if the gameplay_systems table exists
            const tableExists = await this.databaseService.checkTableExists('gameplay_systems');
            
            if (!tableExists) {
                // Create the gameplay_systems table
                await this.databaseService.executeQuery(`
                    CREATE TABLE gameplay_systems (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        packageId INTEGER NOT NULL,
                        resourceId INTEGER NOT NULL,
                        systemName TEXT NOT NULL,
                        impactScore INTEGER NOT NULL,
                        impactDescription TEXT,
                        timestamp INTEGER NOT NULL,
                        FOREIGN KEY (packageId) REFERENCES packages(id),
                        FOREIGN KEY (resourceId) REFERENCES resources(id)
                    )
                `);
                
                // Create indexes
                await this.databaseService.executeQuery(`
                    CREATE INDEX idx_gameplay_systems_package ON gameplay_systems(packageId)
                `);
                
                await this.databaseService.executeQuery(`
                    CREATE INDEX idx_gameplay_systems_resource ON gameplay_systems(resourceId)
                `);
                
                await this.databaseService.executeQuery(`
                    CREATE INDEX idx_gameplay_systems_system ON gameplay_systems(systemName)
                `);
                
                this.logger.info('Gameplay systems table created');
            }
            
            this.isInitialized = true;
            this.logger.info('Gameplay system analyzer initialized');
        } catch (error) {
            this.logger.error('Error initializing gameplay system analyzer:', error);
            throw error;
        }
    }
    
    /**
     * Analyze gameplay systems impacted by a package
     * @param packageId Package ID
     * @returns Gameplay analysis result
     */
    public async analyzePackage(packageId: number): Promise<PackageGameplayAnalysis> {
        await this.initialize();
        
        try {
            // Get package information
            const packageInfo = await this.databaseService.executeQuery(
                'SELECT * FROM packages WHERE id = ?',
                [packageId]
            );
            
            if (!packageInfo || packageInfo.length === 0) {
                throw new Error(`Package ${packageId} not found`);
            }
            
            const packageName = packageInfo[0].name || `Package ${packageId}`;
            
            // Get resources for this package
            const resources = await this.databaseService.resources.getResourcesByPackageId(packageId);
            
            if (!resources || resources.length === 0) {
                return {
                    packageId,
                    packageName,
                    systems: [],
                    overallImpactScore: 0,
                    primaryCategory: 'unknown',
                    timestamp: Date.now()
                };
            }
            
            // Analyze each resource
            const systemImpacts = new Map<string, {
                system: string;
                description: string;
                impactScore: number;
                resources: Array<{
                    resourceId: number;
                    resourceType: number;
                    impactDescription: string;
                    impactStrength: number;
                }>;
                importance: number;
            }>();
            
            for (const resource of resources) {
                try {
                    // Check if this resource has already been analyzed
                    const existingAnalysis = await this.databaseService.executeQuery(
                        'SELECT * FROM gameplay_systems WHERE resourceId = ?',
                        [resource.id]
                    );
                    
                    if (existingAnalysis && existingAnalysis.length > 0) {
                        // Use existing analysis
                        for (const analysis of existingAnalysis) {
                            const systemName = analysis.systemName;
                            const systemInfo = this.knownGameplaySystems.get(systemName);
                            
                            if (!systemInfo) continue;
                            
                            if (!systemImpacts.has(systemName)) {
                                systemImpacts.set(systemName, {
                                    system: systemName,
                                    description: systemInfo.description,
                                    impactScore: 0,
                                    resources: [],
                                    importance: systemInfo.importance
                                });
                            }
                            
                            const impact = systemImpacts.get(systemName)!;
                            impact.impactScore = Math.max(impact.impactScore, analysis.impactScore);
                            impact.resources.push({
                                resourceId: resource.id,
                                resourceType: resource.resourceType,
                                impactDescription: analysis.impactDescription || '',
                                impactStrength: analysis.impactScore
                            });
                        }
                        
                        continue; // Skip new analysis for this resource
                    }
                    
                    // Perform new analysis for this resource
                    const resourceType = resource.resourceType;
                    
                    // Get resource content for analysis
                    let content = '';
                    
                    try {
                        // Get parsed content if available
                        const parsedContent = await this.databaseService.parsedContent.getContentByResourceId(resource.id);
                        if (parsedContent && parsedContent.content) {
                            if (typeof parsedContent.content === 'string') {
                                content = parsedContent.content;
                            } else {
                                content = JSON.stringify(parsedContent.content);
                            }
                        }
                    } catch (contentError) {
                        this.logger.debug(`Error getting content for resource ${resource.id}:`, contentError);
                        // Continue without content - we can still use resource type for basic analysis
                    }
                    
                    // For each known gameplay system, check if this resource impacts it
                    for (const [systemName, systemInfo] of this.knownGameplaySystems.entries()) {
                        // Check if this resource type can impact this system
                        if (systemInfo.resourceTypes.length > 0 && 
                            !systemInfo.resourceTypes.includes(resourceType)) {
                            continue; // Skip if resource type doesn't match
                        }
                        
                        let impactScore = 0;
                        let impactDescription = '';
                        
                        // If we have content, check for keywords
                        if (content && systemInfo.keywords.length > 0) {
                            const matchedKeywords = systemInfo.keywords.filter(keyword => 
                                content.toLowerCase().includes(keyword.toLowerCase())
                            );
                            
                            if (matchedKeywords.length > 0) {
                                // Calculate impact score based on number of keyword matches
                                impactScore = Math.min(100, matchedKeywords.length * 20);
                                impactDescription = `Contains keywords: ${matchedKeywords.join(', ')}`;
                            }
                        }
                        
                        // If resource type is directly related to system, assign base score
                        if (systemInfo.resourceTypes.includes(resourceType)) {
                            impactScore = Math.max(impactScore, 40); // At least 40 if resource type matches
                            
                            if (!impactDescription) {
                                impactDescription = `Resource type ${this.getResourceTypeName(resourceType)} relates to ${systemName}`;
                            }
                        }
                        
                        // If we found an impact, add it to the results
                        if (impactScore > 0) {
                            // Save to database
                            await this.databaseService.executeQuery(
                                `INSERT INTO gameplay_systems 
                                (packageId, resourceId, systemName, impactScore, impactDescription, timestamp)
                                VALUES (?, ?, ?, ?, ?, ?)`,
                                [packageId, resource.id, systemName, impactScore, impactDescription, Date.now()]
                            );
                            
                            // Add to our analysis results
                            if (!systemImpacts.has(systemName)) {
                                systemImpacts.set(systemName, {
                                    system: systemName,
                                    description: systemInfo.description,
                                    impactScore: 0,
                                    resources: [],
                                    importance: systemInfo.importance
                                });
                            }
                            
                            const impact = systemImpacts.get(systemName)!;
                            impact.impactScore = Math.max(impact.impactScore, impactScore);
                            impact.resources.push({
                                resourceId: resource.id,
                                resourceType: resourceType,
                                impactDescription,
                                impactStrength: impactScore
                            });
                        }
                    }
                } catch (resourceError) {
                    this.logger.error(`Error analyzing resource ${resource.id} for gameplay systems:`, resourceError);
                    // Continue with next resource despite errors
                }
            }
            
            // Build the final result
            const systems: GameplaySystem[] = [];
            let totalScore = 0;
            let maxScore = 0;
            let primaryCategory = 'unknown';
            let maxImportance = 0;
            
            for (const impact of systemImpacts.values()) {
                systems.push({
                    name: impact.system,
                    description: impact.description,
                    impactScore: impact.impactScore,
                    resources: impact.resources
                });
                
                totalScore += impact.impactScore;
                
                // Track highest scoring system as primary category
                if (impact.impactScore > maxScore || 
                   (impact.impactScore === maxScore && impact.importance > maxImportance)) {
                    maxScore = impact.impactScore;
                    maxImportance = impact.importance;
                    primaryCategory = impact.system;
                }
            }
            
            // Sort systems by impact score (highest first)
            systems.sort((a, b) => b.impactScore - a.impactScore);
            
            // Calculate overall impact score (0-100)
            const overallImpactScore = systems.length > 0 ? 
                Math.min(100, Math.round(totalScore / systems.length)) : 0;
            
            return {
                packageId,
                packageName,
                systems,
                overallImpactScore,
                primaryCategory,
                timestamp: Date.now()
            };
        } catch (error) {
            this.logger.error(`Error analyzing gameplay systems for package ${packageId}:`, error);
            throw error;
        }
    }
    
    /**
     * Get resource type name
     * @param resourceType Resource type
     * @returns Resource type name
     * @private
     */
    private getResourceTypeName(resourceType: number): string {
        switch (resourceType) {
            case ResourceTypes.RESOURCE_TYPE_TUNING: return 'Tuning';
            case ResourceTypes.RESOURCE_TYPE_SIMDATA: return 'SimData';
            case ResourceTypes.RESOURCE_TYPE_OBJECT_DEFINITION: return 'Object Definition';
            case ResourceTypes.RESOURCE_TYPE_CASPART: return 'CAS Part';
            case ResourceTypes.RESOURCE_TYPE_SCRIPT: return 'Script';
            case ResourceTypes.RESOURCE_TYPE_DDS_IMAGE: return 'DDS Image';
            case ResourceTypes.RESOURCE_TYPE_PNG_IMAGE: return 'PNG Image';
            case ResourceTypes.RESOURCE_TYPE_SOUND: return 'Sound';
            case ResourceTypes.RESOURCE_TYPE_ANIMATION: return 'Animation';
            case ResourceTypes.RESOURCE_TYPE_MODEL: return 'Model';
            default: return `Type 0x${resourceType.toString(16)}`;
        }
    }
    
    /**
     * Initialize known gameplay systems
     * @private
     */
    private initializeKnownSystems(): void {
        this.addGameplaySystem(
            'needs',
            'Sim needs and motives',
            ['motive', 'need', 'hunger', 'energy', 'bladder', 'hygiene', 'fun', 'social', 'commodity'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            10
        );
        
        this.addGameplaySystem(
            'emotions',
            'Sim emotions and mood',
            ['emotion', 'mood', 'happy', 'sad', 'angry', 'tense', 'uncomfortable', 'buff', 'moodlet'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            9
        );
        
        this.addGameplaySystem(
            'traits',
            'Sim traits and personalities',
            ['trait', 'personality', 'character'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            8
        );
        
        this.addGameplaySystem(
            'aspirations',
            'Sim goals and aspirations',
            ['aspiration', 'goal', 'satisfaction', 'whim', 'wish', 'objective'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            7
        );
        
        this.addGameplaySystem(
            'careers',
            'Sim jobs and careers',
            ['career', 'job', 'work', 'profession', 'promotion', 'business'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            8
        );
        
        this.addGameplaySystem(
            'relationships',
            'Sim relationships and social interactions',
            ['relationship', 'romance', 'friendship', 'social', 'family', 'sentiment'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            9
        );
        
        this.addGameplaySystem(
            'skills',
            'Sim skills and abilities',
            ['skill', 'ability', 'talent', 'expertise', 'level', 'progress'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            7
        );
        
        this.addGameplaySystem(
            'rewards',
            'Rewards and achievements',
            ['reward', 'achievement', 'trophy', 'prize', 'unlock', 'complete'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            6
        );
        
        this.addGameplaySystem(
            'interactions',
            'Sim interactions and actions',
            ['interaction', 'action', 'mixer', 'super_interaction', 'social_mixer'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            10
        );
        
        this.addGameplaySystem(
            'ui',
            'User interface elements',
            ['ui', 'interface', 'dialog', 'panel', 'menu', 'hud', 'window'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SCRIPT],
            5
        );
        
        this.addGameplaySystem(
            'cas',
            'Create-A-Sim functionality',
            ['cas', 'create_a_sim', 'appearance', 'outfit', 'clothing', 'makeup'],
            [ResourceTypes.RESOURCE_TYPE_CASPART, ResourceTypes.RESOURCE_TYPE_TUNING],
            7
        );
        
        this.addGameplaySystem(
            'build',
            'Build mode functionality',
            ['build', 'buy', 'furniture', 'construction', 'object', 'decor'],
            [ResourceTypes.RESOURCE_TYPE_OBJECT_DEFINITION, ResourceTypes.RESOURCE_TYPE_TUNING],
            7
        );
        
        this.addGameplaySystem(
            'world',
            'World and environment',
            ['world', 'lot', 'terrain', 'environment', 'weather', 'season'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SIMDATA],
            6
        );
        
        this.addGameplaySystem(
            'gameplay',
            'Core gameplay mechanics',
            ['gameplay', 'mechanic', 'system', 'rule', 'algorithm'],
            [ResourceTypes.RESOURCE_TYPE_SCRIPT, ResourceTypes.RESOURCE_TYPE_TUNING],
            9
        );
        
        this.addGameplaySystem(
            'autonomy',
            'Sim autonomous behavior',
            ['autonomy', 'autonomous', 'behavior', 'decision', 'priority', 'situation'],
            [ResourceTypes.RESOURCE_TYPE_TUNING, ResourceTypes.RESOURCE_TYPE_SCRIPT],
            8
        );
    }
    
    /**
     * Add a gameplay system to the known systems
     * @param name System name
     * @param description System description
     * @param keywords Keywords to look for
     * @param resourceTypes Resource types that can impact this system
     * @param importance System importance (1-10)
     * @private
     */
    private addGameplaySystem(
        name: string,
        description: string,
        keywords: string[],
        resourceTypes: number[],
        importance: number
    ): void {
        this.knownGameplaySystems.set(name, {
            name,
            description,
            keywords,
            resourceTypes,
            importance: Math.max(1, Math.min(10, importance))
        });
    }
    
    /**
     * Get gameplay systems impacted by a resource
     * @param resourceId Resource ID
     * @returns Impacted systems with scores
     */
    public async getResourceImpactedSystems(resourceId: number): Promise<{
        systemName: string;
        impactScore: number;
        impactDescription: string;
    }[]> {
        await this.initialize();
        
        try {
            const impacts = await this.databaseService.executeQuery(
                'SELECT systemName, impactScore, impactDescription FROM gameplay_systems WHERE resourceId = ?',
                [resourceId]
            );
            
            return impacts || [];
        } catch (error) {
            this.logger.error(`Error getting impacted systems for resource ${resourceId}:`, error);
            return [];
        }
    }
    
    /**
     * Get resources that impact a gameplay system
     * @param systemName System name
     * @param limit Maximum number of resources to return
     * @returns Resources that impact the system
     */
    public async getSystemImpactingResources(
        systemName: string,
        limit: number = 100
    ): Promise<{
        resourceId: number;
        resourceType: number;
        impactScore: number;
        impactDescription: string;
    }[]> {
        await this.initialize();
        
        try {
            const query = `
                SELECT gs.resourceId, r.resourceType, gs.impactScore, gs.impactDescription
                FROM gameplay_systems gs
                JOIN resources r ON gs.resourceId = r.id
                WHERE gs.systemName = ?
                ORDER BY gs.impactScore DESC
                LIMIT ?
            `;
            
            const resources = await this.databaseService.executeQuery(query, [systemName, limit]);
            return resources || [];
        } catch (error) {
            this.logger.error(`Error getting resources that impact system ${systemName}:`, error);
            return [];
        }
    }
    
    /**
     * Dispose of resources used by the analyzer
     */
    public async dispose(): Promise<void> {
        try {
            this.knownGameplaySystems.clear();
            this.isInitialized = false;
            this.logger.info('Gameplay system analyzer disposed successfully');
        } catch (error) {
            this.logger.error('Error disposing gameplay system analyzer:', error);
            throw error;
        }
    }
}