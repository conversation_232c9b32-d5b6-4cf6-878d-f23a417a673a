/**
 * Python Bytecode Header Parser
 *
 * This module provides functionality for parsing Python bytecode headers.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { BytecodeHeader } from './types.js';
import {
    getPythonVersionFromMagic,
    EA_MAGIC_NUMBERS,
    createDefaultHeader,
    formatTimestamp
} from './utils.js';
import { SafeBufferReader } from './safeBufferReader.js';

// Create a logger for this module
const logger = new Logger('BytecodeHeaderParser');

/**
 * Parses a Python bytecode header from a buffer
 * @param buffer Buffer containing Python bytecode
 * @returns Parsed bytecode header
 */
export function parseHeader(buffer: Buffer): BytecodeHeader {
    try {
        // Check if buffer is large enough
        if (buffer.length < 8) {
            return createDefaultHeader('Buffer too small for bytecode header');
        }

        // Create a safe buffer reader
        const reader = new SafeBufferReader(buffer);

        // Read magic number (4 bytes)
        const magic = reader.readUInt32LE() ?? 0;

        // Get Python version from magic number
        const pythonVersion = getPythonVersionFromMagic(magic);

        // Read timestamp (4 bytes)
        const timestamp = reader.readUInt32LE() ?? 0;

        // Determine if this is a standard Python format
        const isStandardFormat = pythonVersion !== null && !(magic in EA_MAGIC_NUMBERS);

        // Determine if this is an EA-modified format
        const isEAFormat = magic in EA_MAGIC_NUMBERS;

        // Create format name
        let formatName = 'Unknown';
        if (isStandardFormat && pythonVersion) {
            formatName = `Python ${pythonVersion.toString()}`;
        } else if (isEAFormat) {
            formatName = EA_MAGIC_NUMBERS[magic];
        }

        // Create header object
        const header: BytecodeHeader = {
            magic,
            pythonVersion,
            timestamp,
            isStandardFormat,
            isEAFormat,
            formatName
        };

        // For Python 3.7+, read source size and hash if available
        if (buffer.length >= 16 && pythonVersion && pythonVersion.major === 3 && pythonVersion.minor >= 7) {
            const sourceSize = reader.readUInt32LE();
            if (sourceSize !== null) {
                header.sourceSize = sourceSize;
            }

            // Read source hash (4 bytes) and convert to hex string
            const sourceHash = reader.readUInt32LE();
            if (sourceHash !== null) {
                header.sourceHash = sourceHash.toString(16).padStart(8, '0');
            }
        }

        logger.debug(`Parsed bytecode header: magic=0x${magic.toString(16)}, version=${formatName}, timestamp=${formatTimestamp(timestamp)}`);

        return header;
    } catch (error: any) {
        logger.error(`Error parsing bytecode header: ${error.message || error}`);
        return createDefaultHeader(error.message || String(error));
    }
}

/**
 * Detects the Python bytecode format from a buffer
 * @param buffer Buffer containing Python bytecode
 * @returns Format name or 'Unknown' if not recognized
 */
export function detectBytecodeFormat(buffer: Buffer): string {
    try {
        const header = parseHeader(buffer);
        return header.formatName;
    } catch (error) {
        logger.error(`Error detecting bytecode format: ${error}`);
        return 'Unknown';
    }
}

/**
 * Checks if a buffer contains a valid Python bytecode header
 * @param buffer Buffer to check
 * @returns True if the buffer contains a valid Python bytecode header
 */
export function isValidBytecodeHeader(buffer: Buffer): boolean {
    try {
        const header = parseHeader(buffer);
        return header.isStandardFormat || header.isEAFormat;
    } catch (error) {
        logger.error(`Error checking bytecode header: ${error}`);
        return false;
    }
}

/**
 * Gets the Python version from a bytecode header
 * @param buffer Buffer containing Python bytecode
 * @returns Python version string or 'Unknown' if not recognized
 */
export function getPythonVersion(buffer: Buffer): string {
    try {
        const header = parseHeader(buffer);
        if (header.pythonVersion) {
            return header.pythonVersion.toString();
        } else if (header.isEAFormat) {
            return header.formatName;
        } else {
            return 'Unknown';
        }
    } catch (error) {
        logger.error(`Error getting Python version: ${error}`);
        return 'Unknown';
    }
}
