import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';

interface PackageInfo {
    id?: number; // Optional for new packages
    name: string;
    path: string;
    hash: string;
    size: number;
    lastModified: number; // Timestamp
}

export class PackageRepository {
    private db: Database.Database;
    private logger: Logger;

    constructor(db: Database.Database, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    savePackage(packageInfo: PackageInfo): number {
        try {
            // First, check if a package with this path already exists
            const existingPackage = this.db.prepare('SELECT id FROM Packages WHERE path = ?').get(packageInfo.path) as { id: number } | undefined;

            if (existingPackage) {
                // If it exists, update the existing row and return its ID
                this.logger.debug(`[PackageRepository] Package with path ${packageInfo.path} already exists (ID: ${existingPackage.id}). Updating.`);
                const updateStmt = this.db.prepare(`
                    UPDATE Packages SET name = ?, hash = ?, size = ?, lastModified = ?
                    WHERE id = ?
                `);
                updateStmt.run(
                    packageInfo.name,
                    packageInfo.hash,
                    packageInfo.size,
                    packageInfo.lastModified,
                    existingPackage.id
                );
                return existingPackage.id;
            } else {
                // If it doesn't exist, insert a new row and return the new ID
                this.logger.debug(`[PackageRepository] Package with path ${packageInfo.path} not found. Inserting new package.`);
                const insertStmt = this.db.prepare(`
                    INSERT INTO Packages (name, path, hash, size, lastModified)
                    VALUES (?, ?, ?, ?, ?)
                    RETURNING id;
                `);
                const result = insertStmt.get(
                    packageInfo.name,
                    packageInfo.path,
                    packageInfo.hash,
                    packageInfo.size,
                    packageInfo.lastModified
                ) as { id: number };
                this.logger.debug(`[PackageRepository] Inserted new package. Returned ID: ${result.id}`);
                return result.id;
            }
        } catch (error) {
            this.logger.error(`Error saving package ${packageInfo.path}:`, error);
            throw error; // Re-throw to allow calling service to handle
        }
    }

    public getPackageIdByPath(filePath: string): number {
        const stmt = this.db.prepare('SELECT id FROM Packages WHERE path = ?');
        try {
            const result = stmt.get(filePath) as { id: number } | undefined;
            return result ? result.id : -1; // Return -1 if not found
        } catch (error) {
            this.logger.error(`Error getting package ID for path ${filePath}:`, error);
            return -1; // Return -1 on error
        }
    }

    public getPackageByHash(hash: string): PackageInfo | undefined {
        const stmt = this.db.prepare('SELECT id, name, path, hash, size, lastModified FROM Packages WHERE hash = ?');
        try {
            const result = stmt.get(hash) as PackageInfo | undefined;
            return result;
        } catch (error) {
            this.logger.error(`Error getting package by hash ${hash}:`, error);
            return undefined;
        }
    }

    public getPackageById(id: number): PackageInfo | undefined {
        const stmt = this.db.prepare('SELECT id, name, path, hash, size, lastModified FROM Packages WHERE id = ?');
        try {
            const result = stmt.get(id) as PackageInfo | undefined;
            return result;
        } catch (error) {
            this.logger.error(`Error getting package by ID ${id}:`, error);
            return undefined;
        }
    }
}