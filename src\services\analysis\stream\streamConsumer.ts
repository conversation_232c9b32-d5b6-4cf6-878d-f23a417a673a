/**
 * Stream Consumer Interface
 * 
 * This module defines the interface for stream consumers that process
 * the output of the streaming pipeline.
 */

import { Readable } from 'stream';

/**
 * Stream consumer interface
 */
export interface StreamConsumer {
    /**
     * Consume a stream and process its data
     * @param stream Stream to consume
     */
    consume(stream: Readable): Promise<void>;
    
    /**
     * Get consumption progress
     * @returns Progress value (0-1)
     */
    getProgress(): number;
    
    /**
     * Get consumption results
     * @returns Results of consumption
     */
    getResults(): any;
    
    /**
     * Clean up resources
     */
    cleanup(): Promise<void>;
}
