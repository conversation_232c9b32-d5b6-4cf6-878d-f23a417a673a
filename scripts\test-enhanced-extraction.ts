import { Logger } from '../src/utils/logging/logger.js';
import { DatabaseService } from '../src/services/databaseService.js';
import { PackageAnalysisService } from '../src/services/analysis/packageAnalysisService.js';
import { ModConflictOrchestrator } from '../src/services/ml/ModConflictOrchestrator.js';
import * as path from 'path';
import * as fs from 'fs';

// Configure logger for this script
const scriptLogger = new Logger('TestEnhancedExtractionScript');
console.log('Starting enhanced extraction test script...');

// Base path for convenience
const modsPath = "C:\\Users\\<USER>\\OneDrive\\Documents\\Electronic Arts\\The Sims 4\\Mods\\";

// Just test one file to keep it simple
const testFilePath = modsPath + "mc_cmd_center.package"; // MC Command Center package

// Main function to run the test
async function runTest() {
    try {
        // Initialize services
        const databaseService = new DatabaseService();
        await databaseService.initialize();

        // Create a logger for the package analysis service
        const packageLogger = new Logger('PackageAnalysisService');

        // Create the package analysis service with both logger and database service
        const packageAnalysisService = PackageAnalysisService.getInstance(packageLogger, databaseService);

        // Process the test file
        console.log(`\n==================================================`);
        console.log(`Starting analysis for: ${path.basename(testFilePath)}`);
        console.log(`Full Path: ${testFilePath}`);
        console.log(`==================================================`);

        const startTime = Date.now();

        // Analyze the package file
        const result = await packageAnalysisService.analyzePackage(testFilePath);

        const endTime = Date.now();
        console.log(`Analysis completed in ${endTime - startTime}ms.`);

        // Log basic package info
        console.log(`Package ID: ${result.packageId}`);
        console.log(`Package Name: ${result.packageInfo?.name}`);
        console.log(`Resource Count: ${result.resources?.length || 0}`);

        // Log resource types found
        const resourceTypes = new Map<string, number>();
        result.resources?.forEach(resource => {
            const typeName = resource.metadata.resourceType || 'Unknown';
            resourceTypes.set(typeName, (resourceTypes.get(typeName) || 0) + 1);
        });

        console.log('--- Resource Types Found ---');
        for (const [typeName, count] of resourceTypes.entries()) {
            console.log(`${typeName}: ${count}`);
        }

        // Log extractors used
        const extractorsUsed = new Map<string, number>();
        result.resources?.forEach(resource => {
            const extractor = resource.metadata.extractorUsed || 'none';
            extractorsUsed.set(extractor, (extractorsUsed.get(extractor) || 0) + 1);
        });

        console.log('--- Extractors Used ---');
        for (const [extractor, count] of extractorsUsed.entries()) {
            console.log(`${extractor}: ${count}`);
        }

        // Check for specific resource types
        const footprints = result.resources?.filter(r => r.metadata.resourceType === 'FOOTPRINT');
        if (footprints && footprints.length > 0) {
            console.log(`\n--- Found ${footprints.length} Footprint Resources ---`);
            for (const footprint of footprints) {
                console.log(`Footprint: ${footprint.key.instance.toString(16)}`);
                console.log(`  Version: ${footprint.metadata.footprintVersion}`);
                console.log(`  Dimensions: ${footprint.metadata.footprintWidth}x${footprint.metadata.footprintHeight}`);
                console.log(`  Slot Count: ${footprint.metadata.footprintSlotCount}`);
                console.log(`  Content: ${footprint.metadata.contentSnippet}`);
            }
        }

        const buildParts = result.resources?.filter(r => r.metadata.resourceType === 'BUILD_PART');
        if (buildParts && buildParts.length > 0) {
            console.log(`\n--- Found ${buildParts.length} Build Part Resources ---`);
            for (const buildPart of buildParts) {
                console.log(`Build Part: ${buildPart.key.instance.toString(16)}`);
                console.log(`  Type: ${buildPart.metadata.buildPartType}`);
                console.log(`  Version: ${buildPart.metadata.buildPartVersion}`);
                console.log(`  Material Count: ${buildPart.metadata.buildPartMaterialCount}`);
                console.log(`  Price: ${buildPart.metadata.buildPartPrice}`);
                console.log(`  Content: ${buildPart.metadata.contentSnippet}`);
            }
        }

        // Check for script resources
        const scripts = result.resources?.filter(r => r.metadata.scriptType);
        if (scripts && scripts.length > 0) {
            console.log(`\n--- Found ${scripts.length} Script Resources ---`);
            for (const script of scripts) {
                console.log(`Script: ${script.key.instance.toString(16)}`);
                console.log(`  Type: ${script.metadata.scriptType}`);
                console.log(`  Mod Type: ${script.metadata.modType}`);
                console.log(`  Complexity: ${script.metadata.modComplexity}`);
                console.log(`  Conflict Risk: ${script.metadata.potentialConflictRisk}`);

                if (script.metadata.scriptType === 'TS4Script') {
                    console.log(`  Module Count: ${script.metadata.scriptModuleCount}`);
                    console.log(`  Main Module: ${script.metadata.scriptMainModule}`);
                    console.log(`  Class Count: ${script.metadata.scriptClassCount}`);
                    console.log(`  Function Count: ${script.metadata.scriptFunctionCount}`);

                    if (script.metadata.scriptVersion) {
                        console.log(`  Version: ${script.metadata.scriptVersion}`);
                    }

                    if (script.metadata.scriptAuthor) {
                        console.log(`  Author: ${script.metadata.scriptAuthor}`);
                    }

                    if (script.metadata.hasInjections) {
                        console.log(`  Has Injections: Yes`);
                    }

                    if (script.metadata.hasGameHooks) {
                        console.log(`  Has Game Hooks: Yes`);
                    }
                }
            }
        }

        // Close the database
        await databaseService.close();

        console.log('\nTest completed successfully!');
    } catch (error) {
        console.error(`Test failed: ${error}`);
    }
}

// Run the test
runTest().catch(error => {
    console.error(`Unhandled error: ${error}`);
    process.exit(1);
});
