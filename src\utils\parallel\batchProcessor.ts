/**
 * Batch Processor
 * 
 * This module provides a batch processor for processing large collections of items
 * with adaptive batch sizing based on performance metrics and memory pressure.
 */

import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import TaskDispatcher, { BatchOptions } from './taskDispatcher.js';
import { TaskPriority, TaskType } from './workerPool.js';
import EnhancedMemoryManager from '../memory/enhancedMemoryManager.js';

// Create a logger for this module
const logger = new Logger('BatchProcessor');

/**
 * Batch processor options
 */
export interface BatchProcessorOptions {
    initialBatchSize?: number;
    minBatchSize?: number;
    maxBatchSize?: number;
    concurrency?: number;
    priority?: TaskPriority;
    timeout?: number;
    retries?: number;
    memoryThreshold?: number;
    adaptiveBatchSizing?: boolean;
    progressInterval?: number;
}

/**
 * Batch processing statistics
 */
export interface BatchProcessingStats {
    totalItems: number;
    processedItems: number;
    successfulItems: number;
    failedItems: number;
    totalBatches: number;
    completedBatches: number;
    failedBatches: number;
    currentBatchSize: number;
    averageBatchDuration: number;
    startTime: number;
    endTime?: number;
    elapsedTime: number;
    estimatedTimeRemaining?: number;
    memoryUsage: {
        heapUsed: number;
        heapTotal: number;
        rss: number;
        memoryPressure: number;
    };
}

/**
 * Batch processor class
 */
export class BatchProcessor extends EventEmitter {
    private taskDispatcher: TaskDispatcher;
    private memoryManager: EnhancedMemoryManager;
    private options: Required<BatchProcessorOptions>;
    private stats: BatchProcessingStats;
    private progressIntervalId?: NodeJS.Timeout;
    private isProcessing: boolean = false;
    
    /**
     * Create a new batch processor
     * @param options Batch processor options
     */
    constructor(options: BatchProcessorOptions = {}) {
        super();
        this.setMaxListeners(100); // Set high max listeners
        
        // Set default options
        this.options = {
            initialBatchSize: options.initialBatchSize || 10,
            minBatchSize: options.minBatchSize || 1,
            maxBatchSize: options.maxBatchSize || 100,
            concurrency: options.concurrency || 4,
            priority: options.priority || TaskPriority.NORMAL,
            timeout: options.timeout || 60000, // 1 minute
            retries: options.retries || 3,
            memoryThreshold: options.memoryThreshold || 0.8, // 80% memory usage
            adaptiveBatchSizing: options.adaptiveBatchSizing !== false,
            progressInterval: options.progressInterval || 1000 // 1 second
        };
        
        // Initialize dependencies
        this.taskDispatcher = TaskDispatcher.getInstance();
        this.memoryManager = EnhancedMemoryManager.getInstance();
        
        // Initialize stats
        this.stats = {
            totalItems: 0,
            processedItems: 0,
            successfulItems: 0,
            failedItems: 0,
            totalBatches: 0,
            completedBatches: 0,
            failedBatches: 0,
            currentBatchSize: this.options.initialBatchSize,
            averageBatchDuration: 0,
            startTime: 0,
            elapsedTime: 0,
            memoryUsage: {
                heapUsed: 0,
                heapTotal: 0,
                rss: 0,
                memoryPressure: 0
            }
        };
        
        logger.info('Batch processor created');
    }
    
    /**
     * Process a collection of items in batches
     * @param items Collection of items to process
     * @param processor Function that processes a batch of items
     * @param options Batch processing options
     * @returns Promise that resolves with the results
     */
    public async processBatches<T, R>(
        items: T[],
        processor: (batch: T[]) => Promise<R[]>,
        options?: Partial<BatchProcessorOptions>
    ): Promise<R[]> {
        if (this.isProcessing) {
            throw new Error('Batch processor is already processing a batch');
        }
        
        this.isProcessing = true;
        
        try {
            // Merge options with defaults
            const opts: Required<BatchProcessorOptions> = {
                ...this.options,
                ...options
            };
            
            // Reset stats
            this.resetStats();
            this.stats.totalItems = items.length;
            this.stats.startTime = Date.now();
            this.stats.currentBatchSize = opts.initialBatchSize;
            
            // Start progress reporting
            this.startProgressReporting(opts.progressInterval);
            
            // Create batch options for task dispatcher
            const batchOptions: BatchOptions = {
                initialBatchSize: opts.initialBatchSize,
                minBatchSize: opts.minBatchSize,
                maxBatchSize: opts.maxBatchSize,
                concurrency: opts.concurrency,
                priority: opts.priority,
                timeout: opts.timeout,
                retries: opts.retries,
                onProgress: (completed, total) => {
                    this.updateProgress(completed, total);
                }
            };
            
            // Process batches using task dispatcher
            const results = await this.taskDispatcher.dispatchBatch<T, R>(
                items,
                processor,
                batchOptions
            );
            
            // Update stats
            this.stats.endTime = Date.now();
            this.stats.elapsedTime = this.stats.endTime - this.stats.startTime;
            this.stats.processedItems = items.length;
            this.stats.successfulItems = results.length;
            
            // Stop progress reporting
            this.stopProgressReporting();
            
            // Emit completion event
            this.emit('completed', this.stats);
            
            return results;
        } catch (error) {
            // Update stats
            this.stats.endTime = Date.now();
            this.stats.elapsedTime = this.stats.endTime - this.stats.startTime;
            
            // Stop progress reporting
            this.stopProgressReporting();
            
            // Emit error event
            this.emit('error', error, this.stats);
            
            // Rethrow error
            throw error;
        } finally {
            this.isProcessing = false;
        }
    }
    
    /**
     * Reset batch processing statistics
     * @private
     */
    private resetStats(): void {
        this.stats = {
            totalItems: 0,
            processedItems: 0,
            successfulItems: 0,
            failedItems: 0,
            totalBatches: 0,
            completedBatches: 0,
            failedBatches: 0,
            currentBatchSize: this.options.initialBatchSize,
            averageBatchDuration: 0,
            startTime: 0,
            elapsedTime: 0,
            memoryUsage: {
                heapUsed: 0,
                heapTotal: 0,
                rss: 0,
                memoryPressure: 0
            }
        };
    }
    
    /**
     * Start progress reporting
     * @param interval Interval in milliseconds
     * @private
     */
    private startProgressReporting(interval: number): void {
        // Stop existing interval if any
        this.stopProgressReporting();
        
        // Start new interval
        this.progressIntervalId = setInterval(() => {
            this.reportProgress();
        }, interval);
    }
    
    /**
     * Stop progress reporting
     * @private
     */
    private stopProgressReporting(): void {
        if (this.progressIntervalId) {
            clearInterval(this.progressIntervalId);
            this.progressIntervalId = undefined;
        }
    }
    
    /**
     * Update progress
     * @param completed Number of completed items
     * @param total Total number of items
     * @private
     */
    private updateProgress(completed: number, total: number): void {
        this.stats.processedItems = completed;
        this.stats.elapsedTime = Date.now() - this.stats.startTime;
        
        // Calculate estimated time remaining
        if (completed > 0) {
            const itemsRemaining = total - completed;
            const timePerItem = this.stats.elapsedTime / completed;
            this.stats.estimatedTimeRemaining = itemsRemaining * timePerItem;
        }
        
        // Update memory usage
        const memUsage = process.memoryUsage();
        this.stats.memoryUsage = {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            rss: memUsage.rss,
            memoryPressure: memUsage.heapUsed / memUsage.heapTotal
        };
    }
    
    /**
     * Report progress
     * @private
     */
    private reportProgress(): void {
        // Emit progress event
        this.emit('progress', this.stats);
    }
    
    /**
     * Adjust batch size based on performance metrics and memory pressure
     * @param processingTime Processing time of the last batch in milliseconds
     * @param currentBatchSize Current batch size
     * @returns New batch size
     * @private
     */
    private adjustBatchSize(processingTime: number, currentBatchSize: number): number {
        // Skip adjustment if adaptive batch sizing is disabled
        if (!this.options.adaptiveBatchSizing) {
            return currentBatchSize;
        }
        
        // Get memory pressure
        const memoryPressure = this.getMemoryPressure();
        
        // High memory pressure: reduce batch size
        if (memoryPressure > this.options.memoryThreshold) {
            return Math.max(this.options.minBatchSize, Math.floor(currentBatchSize * 0.5));
        }
        
        // Processing time too long: reduce batch size
        if (processingTime > this.options.timeout * 0.8) {
            return Math.max(this.options.minBatchSize, Math.floor(currentBatchSize * 0.7));
        }
        
        // Processing time too short and low memory pressure: increase batch size
        if (processingTime < this.options.timeout * 0.2 && memoryPressure < this.options.memoryThreshold * 0.5) {
            return Math.min(this.options.maxBatchSize, Math.floor(currentBatchSize * 1.5));
        }
        
        // Keep current batch size
        return currentBatchSize;
    }
    
    /**
     * Get memory pressure (0-1)
     * @returns Memory pressure
     * @private
     */
    private getMemoryPressure(): number {
        const memUsage = process.memoryUsage();
        return memUsage.heapUsed / memUsage.heapTotal;
    }
    
    /**
     * Get current batch processing statistics
     * @returns Current batch processing statistics
     */
    public getStats(): BatchProcessingStats {
        // Update elapsed time
        if (this.isProcessing && !this.stats.endTime) {
            this.stats.elapsedTime = Date.now() - this.stats.startTime;
        }
        
        return { ...this.stats };
    }
    
    /**
     * Cancel batch processing
     */
    public cancel(): void {
        if (!this.isProcessing) {
            return;
        }
        
        logger.info('Cancelling batch processing');
        
        // Stop progress reporting
        this.stopProgressReporting();
        
        // Cancel all tasks
        this.taskDispatcher.cancelAll();
        
        // Update stats
        this.stats.endTime = Date.now();
        this.stats.elapsedTime = this.stats.endTime - this.stats.startTime;
        
        // Emit cancelled event
        this.emit('cancelled', this.stats);
        
        this.isProcessing = false;
    }
}

// Export the BatchProcessor class
export default BatchProcessor;
