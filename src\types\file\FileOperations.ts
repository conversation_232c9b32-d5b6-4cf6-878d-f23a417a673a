/**
 * Result of a file operation
 */
export interface FileOperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Options for file operations
 */
export interface FileOperationOptions {
  createDirectories?: boolean;
  overwrite?: boolean;
  encoding?: string;
  recursive?: boolean;
}

/**
 * File operation types
 */
export enum FileOperationType {
  READ = 'READ',
  WRITE = 'WRITE',
  DELETE = 'DELETE',
  COPY = 'COPY',
  MOVE = 'MOVE',
  RENAME = 'RENAME',
  CREATE = 'CREATE',
  EXISTS = 'EXISTS',
  LIST = 'LIST',
} 
