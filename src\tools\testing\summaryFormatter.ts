/**
 * Summary Formatter
 * 
 * Formats comprehensive test summaries for human-readable display
 * with clear sections, bullet points, and status indicators.
 */

import { ComprehensiveTestSummary, ExecutionPhase, MemorySnapshot } from './comprehensiveTestSummary.js';

/**
 * Format bytes to human-readable string
 */
function formatBytes(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format duration to human-readable string
 */
function formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * Get status indicator
 */
function getStatusIndicator(status: string): string {
    switch (status) {
        case 'success':
        case 'completed':
            return '✅';
        case 'failed':
            return '❌';
        case 'partial':
            return '⚠️';
        case 'running':
            return '🔄';
        default:
            return '❓';
    }
}

/**
 * Format percentage with color coding
 */
function formatPercentage(value: number, goodThreshold: number = 80, excellentThreshold: number = 95): string {
    const percentage = `${value.toFixed(1)}%`;
    if (value >= excellentThreshold) return `${percentage} 🟢`;
    if (value >= goodThreshold) return `${percentage} 🟡`;
    return `${percentage} 🔴`;
}

/**
 * Format execution overview section
 */
function formatExecutionOverview(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const overview = summary.executionOverview;
    
    lines.push('📊 **EXECUTION OVERVIEW**');
    lines.push('═'.repeat(50));
    lines.push(`${getStatusIndicator(overview.overallStatus)} Overall Status: ${overview.overallStatus.toUpperCase()}`);
    lines.push(`⏱️  Total Duration: ${formatDuration(overview.totalDuration)}`);
    lines.push(`🧠 Memory Progression: ${overview.memoryProgression.length} snapshots`);
    
    if (overview.memoryProgression.length > 0) {
        const initial = overview.memoryProgression[0];
        const peak = overview.memoryProgression.reduce((max, snap) => snap.heapUsed > max.heapUsed ? snap : max);
        const final = overview.memoryProgression[overview.memoryProgression.length - 1];
        
        lines.push(`   Initial: ${formatBytes(initial.heapUsed)} (${initial.pressure.toFixed(1)}%)`);
        lines.push(`   Peak: ${formatBytes(peak.heapUsed)} (${peak.pressure.toFixed(1)}%)`);
        lines.push(`   Final: ${formatBytes(final.heapUsed)} (${final.pressure.toFixed(1)}%)`);
    }
    
    lines.push('');
    lines.push('**Phase Breakdown:**');
    overview.phaseBreakdown.forEach((phase, index) => {
        const status = getStatusIndicator(phase.status);
        const duration = phase.duration ? formatDuration(phase.duration) : 'N/A';
        lines.push(`  ${index + 1}. ${status} ${phase.name} - ${duration} (${phase.operations} ops, ${phase.errors.length} errors)`);
    });
    
    return lines;
}

/**
 * Format package analysis section
 */
function formatPackageAnalysis(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const analysis = summary.packageAnalysis;
    
    lines.push('📦 **PACKAGE ANALYSIS SUMMARY**');
    lines.push('═'.repeat(50));
    lines.push(`📁 Files Processed: ${analysis.filesProcessed.length}`);
    lines.push(`🔧 Total Resources: ${analysis.totalResources.toLocaleString()}`);
    lines.push(`💾 Total Size: ${formatBytes(analysis.totalSize)}`);
    lines.push(`📈 Average Size: ${formatBytes(analysis.averageSize)}`);
    
    if (analysis.largestFile.name) {
        lines.push(`🏆 Largest File: ${analysis.largestFile.name} (${formatBytes(analysis.largestFile.size)})`);
    }
    
    lines.push('');
    lines.push('**Processing Speed:**');
    lines.push(`  Files/sec: ${analysis.processingSpeed.filesPerSecond.toFixed(2)}`);
    lines.push(`  Resources/sec: ${analysis.processingSpeed.resourcesPerSecond.toFixed(2)}`);
    lines.push(`  Throughput: ${formatBytes(analysis.processingSpeed.bytesPerSecond)}/sec`);
    
    lines.push('');
    lines.push('**Resource Type Distribution:**');
    const sortedTypes = Object.entries(analysis.resourcesByType)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10); // Top 10 types
    
    sortedTypes.forEach(([type, count]) => {
        const percentage = ((count / analysis.totalResources) * 100).toFixed(1);
        lines.push(`  ${type}: ${count.toLocaleString()} (${percentage}%)`);
    });
    
    return lines;
}

/**
 * Format metadata extraction section
 */
function formatMetadataExtraction(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const metadata = summary.metadataExtraction;
    
    lines.push('🔍 **METADATA EXTRACTION RESULTS**');
    lines.push('═'.repeat(50));
    lines.push(`📊 Resource Types: ${Object.keys(metadata.resourceTypeDistribution).length}`);
    lines.push(`🧬 SimData Schemas: ${metadata.simDataSchemas.length}`);
    lines.push(`🏷️  Enhanced Categories: ${metadata.enhancedMetadataCategories.length}`);
    lines.push(`🔗 Cross-Resource Relationships: ${metadata.crossResourceRelationships}`);
    lines.push(`🎯 Extraction Accuracy: ${formatPercentage(metadata.extractionAccuracy)}`);
    
    if (metadata.simDataSchemas.length > 0) {
        lines.push('');
        lines.push('**SimData Schemas Found:**');
        metadata.simDataSchemas.slice(0, 10).forEach(schema => {
            lines.push(`  • ${schema}`);
        });
        if (metadata.simDataSchemas.length > 10) {
            lines.push(`  ... and ${metadata.simDataSchemas.length - 10} more`);
        }
    }
    
    return lines;
}

/**
 * Format conflict detection section
 */
function formatConflictDetection(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const conflicts = summary.conflictDetection;
    
    lines.push('⚔️  **CONFLICT DETECTION REPORT**');
    lines.push('═'.repeat(50));
    lines.push(`🚨 Total Conflicts: ${conflicts.totalConflicts.toLocaleString()}`);
    lines.push(`🎯 Detection Accuracy: ${formatPercentage(conflicts.detectionAccuracy)}`);
    
    lines.push('');
    lines.push('**Severity Breakdown:**');
    Object.entries(conflicts.severityBreakdown).forEach(([severity, count]) => {
        const percentage = conflicts.totalConflicts > 0 
            ? ((count / conflicts.totalConflicts) * 100).toFixed(1)
            : '0.0';
        lines.push(`  ${severity}: ${count.toLocaleString()} (${percentage}%)`);
    });
    
    lines.push('');
    lines.push('**Conflict Types:**');
    Object.entries(conflicts.conflictTypes).forEach(([type, count]) => {
        const percentage = conflicts.totalConflicts > 0 
            ? ((count / conflicts.totalConflicts) * 100).toFixed(1)
            : '0.0';
        lines.push(`  ${type}: ${count.toLocaleString()} (${percentage}%)`);
    });
    
    if (conflicts.problematicMods.length > 0) {
        lines.push('');
        lines.push('**Most Problematic Mods:**');
        conflicts.problematicMods.slice(0, 5).forEach((mod, index) => {
            lines.push(`  ${index + 1}. ${mod.name}: ${mod.conflicts} conflicts`);
        });
    }
    
    if (conflicts.resolutionRecommendations.length > 0) {
        lines.push('');
        lines.push('**Resolution Recommendations:**');
        conflicts.resolutionRecommendations.forEach(rec => {
            lines.push(`  • ${rec}`);
        });
    }
    
    return lines;
}

/**
 * Format database operations section
 */
function formatDatabaseOperations(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const db = summary.databaseOperations;
    
    lines.push('🗄️  **DATABASE OPERATIONS LOG**');
    lines.push('═'.repeat(50));
    lines.push(`📝 Total Transactions: ${db.totalTransactions.toLocaleString()}`);
    lines.push(`⚡ Average Query Time: ${db.queryPerformance.averageQueryTime.toFixed(2)}ms`);
    lines.push(`🐌 Slowest Query: ${db.queryPerformance.slowestQuery.time.toFixed(2)}ms`);
    lines.push(`🚀 Fastest Query: ${db.queryPerformance.fastestQuery.time.toFixed(2)}ms`);
    
    lines.push('');
    lines.push('**Records by Table:**');
    Object.entries(db.recordsByTable).forEach(([table, count]) => {
        lines.push(`  ${table}: ${count.toLocaleString()}`);
    });
    
    lines.push('');
    lines.push('**Integrity Checks:**');
    const totalChecks = db.integrityChecks.passed + db.integrityChecks.failed;
    const passRate = totalChecks > 0 ? (db.integrityChecks.passed / totalChecks) * 100 : 100;
    lines.push(`  ${getStatusIndicator(passRate >= 95 ? 'success' : 'failed')} Pass Rate: ${formatPercentage(passRate)}`);
    lines.push(`  Passed: ${db.integrityChecks.passed}`);
    lines.push(`  Failed: ${db.integrityChecks.failed}`);
    
    if (db.integrityChecks.issues.length > 0) {
        lines.push('  Issues:');
        db.integrityChecks.issues.forEach(issue => {
            lines.push(`    • ${issue}`);
        });
    }
    
    return lines;
}

/**
 * Format performance analysis section
 */
function formatPerformanceAnalysis(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const perf = summary.performanceAnalysis;
    
    lines.push('⚡ **PERFORMANCE ANALYSIS**');
    lines.push('═'.repeat(50));
    
    lines.push('**Memory Efficiency:**');
    lines.push(`  Peak Usage: ${formatBytes(perf.memoryEfficiency.peakUsage)}`);
    lines.push(`  Average Usage: ${formatBytes(perf.memoryEfficiency.averageUsage)}`);
    lines.push(`  Leaks Detected: ${perf.memoryEfficiency.leaksDetected}`);
    lines.push(`  Cleanup Effectiveness: ${formatPercentage(perf.memoryEfficiency.cleanupEffectiveness)}`);
    
    lines.push('');
    lines.push('**Resource Cleanup:**');
    const cleanupRate = perf.resourceCleanupVerification.cleanupRate;
    lines.push(`  ${getStatusIndicator(cleanupRate >= 95 ? 'success' : 'failed')} Cleanup Rate: ${formatPercentage(cleanupRate)}`);
    lines.push(`  Resources Tracked: ${perf.resourceCleanupVerification.resourcesTracked}`);
    lines.push(`  Resources Released: ${perf.resourceCleanupVerification.resourcesReleased}`);
    
    if (perf.processingBottlenecks.length > 0) {
        lines.push('');
        lines.push('**Processing Bottlenecks:**');
        perf.processingBottlenecks.forEach(bottleneck => {
            lines.push(`  🚧 ${bottleneck}`);
        });
    }
    
    lines.push('');
    lines.push('**Scalability Projections:**');
    lines.push(`  Estimated Capacity: ${perf.scalabilityProjections.estimatedCapacity.toLocaleString()} mods`);
    if (Object.keys(perf.scalabilityProjections.recommendedLimits).length > 0) {
        lines.push('  Recommended Limits:');
        Object.entries(perf.scalabilityProjections.recommendedLimits).forEach(([metric, limit]) => {
            lines.push(`    ${metric}: ${limit}`);
        });
    }
    
    return lines;
}

/**
 * Format quality assurance section
 */
function formatQualityAssurance(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const qa = summary.qualityAssurance;
    
    lines.push('🛡️  **QUALITY ASSURANCE VALIDATION**');
    lines.push('═'.repeat(50));
    
    lines.push('**Data Integrity:**');
    const integrityRate = qa.dataIntegrityChecks.passed + qa.dataIntegrityChecks.failed > 0
        ? (qa.dataIntegrityChecks.passed / (qa.dataIntegrityChecks.passed + qa.dataIntegrityChecks.failed)) * 100
        : 100;
    lines.push(`  ${getStatusIndicator(integrityRate >= 95 ? 'success' : 'failed')} Integrity Rate: ${formatPercentage(integrityRate)}`);
    
    lines.push('');
    lines.push('**Real Data Verification:**');
    lines.push(`  ${getStatusIndicator(!qa.realDataVerification.mockDataDetected ? 'success' : 'failed')} Mock Data Detected: ${qa.realDataVerification.mockDataDetected ? 'Yes' : 'No'}`);
    lines.push(`  ${getStatusIndicator(!qa.realDataVerification.syntheticDataDetected ? 'success' : 'failed')} Synthetic Data Detected: ${qa.realDataVerification.syntheticDataDetected ? 'Yes' : 'No'}`);
    lines.push(`  ${getStatusIndicator(qa.realDataVerification.realDataPercentage >= 95 ? 'success' : 'failed')} Real Data Percentage: ${formatPercentage(qa.realDataVerification.realDataPercentage)}`);
    
    lines.push('');
    lines.push('**Error Handling:**');
    const recoveryRate = qa.errorHandlingEffectiveness.recoveryRate;
    lines.push(`  ${getStatusIndicator(recoveryRate >= 80 ? 'success' : 'failed')} Recovery Rate: ${formatPercentage(recoveryRate)}`);
    lines.push(`  Errors Handled: ${qa.errorHandlingEffectiveness.errorsHandled}`);
    lines.push(`  Unhandled Errors: ${qa.errorHandlingEffectiveness.unhandledErrors}`);
    
    lines.push('');
    lines.push('**System Stability:**');
    const stabilityScore = qa.systemStability.stabilityScore;
    lines.push(`  ${getStatusIndicator(stabilityScore >= 90 ? 'success' : 'failed')} Stability Score: ${formatPercentage(stabilityScore)}`);
    lines.push(`  Crashes: ${qa.systemStability.crashCount}`);
    lines.push(`  Memory Leaks: ${qa.systemStability.memoryLeaks}`);
    lines.push(`  Resource Leaks: ${qa.systemStability.resourceLeaks}`);
    
    return lines;
}

/**
 * Format recommendations section
 */
function formatRecommendations(summary: ComprehensiveTestSummary): string[] {
    const lines: string[] = [];
    const rec = summary.recommendations;
    
    lines.push('💡 **RECOMMENDATIONS**');
    lines.push('═'.repeat(50));
    
    if (rec.immediate.length > 0) {
        lines.push('**🚨 Immediate Actions Required:**');
        rec.immediate.forEach(item => {
            lines.push(`  • ${item}`);
        });
        lines.push('');
    }
    
    if (rec.shortTerm.length > 0) {
        lines.push('**📅 Short-term Improvements:**');
        rec.shortTerm.forEach(item => {
            lines.push(`  • ${item}`);
        });
        lines.push('');
    }
    
    if (rec.longTerm.length > 0) {
        lines.push('**🎯 Long-term Strategic Goals:**');
        rec.longTerm.forEach(item => {
            lines.push(`  • ${item}`);
        });
    }
    
    return lines;
}

/**
 * Format complete comprehensive test summary
 */
export function formatComprehensiveTestSummary(summary: ComprehensiveTestSummary): string {
    const sections: string[][] = [
        formatExecutionOverview(summary),
        [''], // Empty line separator
        formatPackageAnalysis(summary),
        [''],
        formatMetadataExtraction(summary),
        [''],
        formatConflictDetection(summary),
        [''],
        formatDatabaseOperations(summary),
        [''],
        formatPerformanceAnalysis(summary),
        [''],
        formatQualityAssurance(summary),
        [''],
        formatRecommendations(summary)
    ];
    
    const allLines = sections.flat();
    
    // Add header and footer
    const header = [
        '🎯 **COMPREHENSIVE TEST EXECUTION SUMMARY**',
        '═'.repeat(80),
        `Generated: ${new Date().toISOString()}`,
        `Test Version: ${summary.structuredData.version}`,
        ''
    ];
    
    const footer = [
        '',
        '═'.repeat(80),
        '✅ **SUMMARY COMPLETE** - All operations documented and verified',
        `📊 Total sections: ${sections.filter(s => s.length > 1).length}`,
        `🕒 Report generated in: ${Date.now() - summary.structuredData.timestamp}ms`
    ];
    
    return [...header, ...allLines, ...footer].join('\n');
}

/**
 * Export summary as structured JSON for AI analysis
 */
export function exportStructuredSummary(summary: ComprehensiveTestSummary): string {
    return JSON.stringify(summary, null, 2);
}

/**
 * Export summary as YAML for configuration management
 */
export function exportYamlSummary(summary: ComprehensiveTestSummary): string {
    // Simple YAML export (would use a proper YAML library in production)
    const yamlLines: string[] = [];
    
    yamlLines.push('# Comprehensive Test Summary');
    yamlLines.push(`timestamp: ${summary.structuredData.timestamp}`);
    yamlLines.push(`version: "${summary.structuredData.version}"`);
    yamlLines.push(`overall_status: "${summary.executionOverview.overallStatus}"`);
    yamlLines.push(`total_duration: ${summary.executionOverview.totalDuration}`);
    yamlLines.push(`packages_processed: ${summary.packageAnalysis.filesProcessed.length}`);
    yamlLines.push(`total_resources: ${summary.packageAnalysis.totalResources}`);
    yamlLines.push(`conflicts_detected: ${summary.conflictDetection.totalConflicts}`);
    yamlLines.push(`real_data_percentage: ${summary.qualityAssurance.realDataVerification.realDataPercentage}`);
    
    return yamlLines.join('\n');
}
