import { tuningTypeMap } from '../constants/tuningTypeMap.js';

export interface TuningSemanticInfo {
    tuningType: string;
    tuningCategory: string;
    tuningDescription: string;
    tuningTags: string[];
}

/**
 * Extracts semantic information from tuning XML
 * @param rootElement The root XML element
 * @param tuningName The name of the tuning
 * @returns Semantic information about the tuning
 */
export function extractTuningSemanticInfo(rootElement: any, tuningName: string): TuningSemanticInfo {
    let tuningType = 'Unknown';
    let tuningCategory = 'Unknown';
    let tuningDescription = '';
    const tuningTags: string[] = [];

    // Try to determine tuning type from the root element name
    if (rootElement.name) {
        const rootName = rootElement.name.toLowerCase();
        for (const [key, value] of Object.entries(tuningTypeMap)) {
            if (rootName.includes(key)) {
                tuningType = value;
                break;
            }
        }
    }

    // Try to determine category from tuning name or type
    if (tuningName) {
        const lowerName = tuningName.toLowerCase();
        if (lowerName.includes('trait')) tuningCategory = 'Traits';
        else if (lowerName.includes('buff')) tuningCategory = 'Buffs';
        else if (lowerName.includes('interaction')) tuningCategory = 'Interactions';
        else if (lowerName.includes('object')) tuningCategory = 'Objects';
        else if (lowerName.includes('aspiration')) tuningCategory = 'Aspirations';
        else if (lowerName.includes('career')) tuningCategory = 'Careers';
        else if (lowerName.includes('recipe')) tuningCategory = 'Recipes';
        else if (lowerName.includes('lot')) tuningCategory = 'Lots';
        else if (lowerName.includes('skill')) tuningCategory = 'Skills';
        else if (lowerName.includes('relationship')) tuningCategory = 'Relationships';
        else if (lowerName.includes('situation')) tuningCategory = 'Situations';
        else if (lowerName.includes('event')) tuningCategory = 'Events';
        else if (lowerName.includes('mood')) tuningCategory = 'Moods';
        else if (lowerName.includes('commodity')) tuningCategory = 'Commodities';
        else if (lowerName.includes('statistic')) tuningCategory = 'Statistics';
        else if (lowerName.includes('venue')) tuningCategory = 'Venues';
        else if (lowerName.includes('zone')) tuningCategory = 'Zones';
        else if (lowerName.includes('service')) tuningCategory = 'Services';
        else if (lowerName.includes('reward')) tuningCategory = 'Rewards';
        else if (lowerName.includes('tutorial')) tuningCategory = 'Tutorials';
        else if (lowerName.includes('dialog')) tuningCategory = 'UI';
        else if (lowerName.includes('animation')) tuningCategory = 'Animations';
        else if (lowerName.includes('posture')) tuningCategory = 'Postures';
        else if (lowerName.includes('social')) tuningCategory = 'Social';
        else if (lowerName.includes('broadcaster')) tuningCategory = 'Broadcasters';
        else if (lowerName.includes('loot')) tuningCategory = 'Loot';
        else if (lowerName.includes('test')) tuningCategory = 'Tests';
        else if (lowerName.includes('snippet')) tuningCategory = 'Snippets';
    }

    // Extract description if available
    if (rootElement.description) {
        tuningDescription = rootElement.description;
    }

    // Extract tags from the tuning
    const extractTags = (obj: any, tags: string[] = []) => {
        if (obj && typeof obj === 'object') {
            // Check for tag-like properties
            if (obj.tag && typeof obj.tag === 'string') {
                tags.push(obj.tag);
            }
            if (obj.tags && Array.isArray(obj.tags)) {
                tags.push(...obj.tags.filter((t: any) => typeof t === 'string'));
            }
            if (obj.category && typeof obj.category === 'string') {
                tags.push(obj.category);
            }
            if (obj.type && typeof obj.type === 'string') {
                tags.push(obj.type);
            }

            // Recursively process child objects
            for (const key in obj) {
                if (typeof obj[key] === 'object') {
                    extractTags(obj[key], tags);
                }
            }
        }
        return tags;
    };

    tuningTags.push(...extractTags(rootElement));

    return {
        tuningType,
        tuningCategory,
        tuningDescription,
        tuningTags: [...new Set(tuningTags)] // Remove duplicates
    };
} 