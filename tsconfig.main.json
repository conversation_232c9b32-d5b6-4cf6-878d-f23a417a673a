{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "outDir": "./dist/main-process",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true, // Keep skipping lib check
    "moduleResolution": "Node",
    "sourceMap": true,
    "declaration": false,
    "isolatedModules": false, // Keep this override
    "allowJs": true, // Allow importing JS files
    "checkJs": false // Don't type-check JS files
  },
  "include": [
    "src/frontend/electron/main.ts",
    // Restore includes needed by main.ts
    "src/services/**/*.ts",
    "src/utils/**/*.ts",
    "src/types/**/*.ts",
    "src/constants/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    // Restore necessary excludes
    "src/frontend/electron/**/*.vue",
    "src/frontend/electron/index.ts",
    "src/frontend/electron/App.vue",
    "src/frontend/electron/components",
    "src/frontend/electron/composables",
    "src/frontend/electron/store",
    "src/frontend/streamlit",
    "**/*.test.ts",
    "**/*.spec.ts",
    "src/frontend/electron/preload.ts" // Keep preload excluded here
  ]
}
