import { Request, Response } from 'express';
import { PackageAnalysisService } from '../services/analysis/packageAnalysisService.js';
import { ModConflictOrchestrator } from '../services/ml/ModConflictOrchestrator.js';
import { Logger } from '../utils/logging/logger.js';
import { promises as fs } from 'fs';
import { createHash } from 'crypto';

const logger = new Logger('ApiHandlers');

/**
 * Handles the package analysis request.
 * @param req The Express request object.
 * @param res The Express response object.
 * @param packageAnalysisService The PackageAnalysisService instance.
 */
export async function handleAnalyzePackage(req: Request, res: Response, packageAnalysisService: PackageAnalysisService) {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Please upload a package file' });
    }

    const file = req.file;
    logger.info(`Analyzing file: ${file.originalname}`);

    const result = await packageAnalysisService.analyzePackage(file.path);

    await fs.unlink(file.path);

    res.json({
      conflicts: result.conflicts || [],
      recommendations: [],
      metrics: {
        resourceCount: result.resources?.length || 0,
        totalSize: result.totalSize || 0,
        analysisTime: result.analysisTime || 0
      }
    });
  } catch (error) {
    logger.error(`Error analyzing packages: ${error}`);
    res.status(500).json({ error: 'Failed to analyze packages' });
  }
}

/**
 * Handles the package comparison request.
 * @param req The Express request object.
 * @param res The Express response object.
 * @param packageAnalysisService The PackageAnalysisService instance.
 * @param conflictOrchestrator The ModConflictOrchestrator instance.
 */
export async function handleComparePackages(req: Request, res: Response, packageAnalysisService: PackageAnalysisService, conflictOrchestrator: ModConflictOrchestrator) {
  try {
    if (!req.files || !Array.isArray(req.files) || req.files.length < 2) {
      return res.status(400).json({ error: 'Please upload exactly two package files' });
    }

    const files = req.files as Express.Multer.File[];
    logger.info(`Comparing files: ${files[0].originalname} and ${files[1].originalname}`);

    const results = await Promise.all([
      packageAnalysisService.analyzePackage(files[0].path),
      packageAnalysisService.analyzePackage(files[1].path)
    ]);

    const packageInfos = results.map((result, index) => ({
      name: files[index].originalname,
      filePath: files[index].path,
      size: files[index].size,
      hash: createHash('sha256').update(files[index].originalname).digest('hex'),
      timestamp: result.metadata?.timestamp || Date.now(),
      version: result.metadata?.version || 'N/A',
      resources: result.resources || []
    }));

    const conflictResult = await conflictOrchestrator.analyzeConflictsBetweenPackages(
      packageInfos[0],
      packageInfos[1]
    );

    await Promise.all(files.map(file => fs.unlink(file.path)));

    const conflicts = conflictResult || [];
    const recommendations = conflicts.flatMap((c: any) => c.recommendations || []);

    res.json({
      conflicts: conflicts,
      recommendations: recommendations,
      metrics: {
        resourceCount: results.reduce((acc, r) => acc + (r.resources?.length || 0), 0),
        conflictsFound: conflicts.length || 0,
        analysisTime: Date.now()
      }
    });
  } catch (error) {
    logger.error(`Error comparing packages: ${error}`);
    res.status(500).json({ error: 'Failed to compare packages' });
  }
}