import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey as AppResource<PERSON>ey } from '../../../../../types/resource/interfaces.js';
import { ResourceExtractionError } from '../../../../../utils/error/index.js';
import { TuningExtractionContext, TuningParseResult } from '../types.js';

/**
 * Creates a tuning extraction context
 * @param key The resource key
 * @param resourceId The resource ID
 * @param operation The operation being performed
 * @param additionalInfo Additional information
 * @returns The tuning extraction context
 */
export function createTuningExtractionContext(
    key: AppResourceKey,
    resourceId: number,
    operation: string,
    additionalInfo?: Record<string, any>
): TuningExtractionContext {
    return {
        key,
        resourceId,
        operation,
        additionalInfo
    };
}

/**
 * Handles a tuning extraction error
 * @param error The error
 * @param context The tuning extraction context
 * @param log The logger instance
 * @returns The tuning parse result with error information
 */
export function handleTuningExtractionError(
    error: any,
    context: TuningExtractionContext,
    log: Logger
): TuningParseResult {
    try {
        // Extract error message
        const errorMessage = error?.message || String(error);
        
        // Log the error with context
        log.error(`Error in ${context.operation} for resource ${context.resourceId} (instance: ${context.key.instance.toString(16)}): ${errorMessage}`, {
            resourceId: context.resourceId,
            instance: context.key.instance.toString(16),
            type: context.key.type.toString(16),
            operation: context.operation,
            error: errorMessage,
            ...context.additionalInfo
        });
        
        // Return a result with error information
        return {
            tuningResource: null,
            parsedWithS4TK: false,
            contentSnippet: `[Tuning Parse Error: ${context.operation}]`,
            error: errorMessage
        };
    } catch (handlerError: any) {
        // If error handling itself fails, log and return default result
        log.error(`Error handling failed: ${handlerError?.message || handlerError}`);
        
        // Return a minimal result
        return {
            tuningResource: null,
            parsedWithS4TK: false,
            contentSnippet: '[Tuning Parse Error]',
            error: 'Error handling failed'
        };
    }
}

/**
 * Higher-order function that wraps a tuning extraction function with error handling
 * @param fn Function to wrap
 * @returns Wrapped function with error handling
 */
export function withTuningExtractionErrorHandling<T extends any[], R>(
    fn: (...args: T) => Promise<R> | R,
    operationName: string,
    log: Logger
): (...args: T) => Promise<R> {
    return async function(...args: T): Promise<R> {
        try {
            return await fn(...args);
        } catch (error: any) {
            // Extract context information from arguments if possible
            const key = args[0] as AppResourceKey;
            const resourceId = typeof args[2] === 'number' ? args[2] : 0;
            
            // Create error context
            const context = createTuningExtractionContext(
                key,
                resourceId,
                operationName
            );
            
            // Handle the error
            const result = handleTuningExtractionError(error, context, log);
            
            // Return the result as R (this is a type assertion)
            return result as unknown as R;
        }
    };
}

/**
 * Creates a ResourceExtractionError from a tuning extraction error
 * @param error The error
 * @param context The tuning extraction context
 * @returns The ResourceExtractionError
 */
export function createResourceExtractionError(
    error: any,
    context: TuningExtractionContext
): ResourceExtractionError {
    const errorMessage = error?.message || String(error);
    
    return new ResourceExtractionError(
        errorMessage,
        {
            resourceId: context.resourceId,
            resourceType: context.key.type,
            resourceGroup: context.key.group,
            resourceInstance: context.key.instance,
            operation: context.operation,
            ...context.additionalInfo
        }
    );
}
