import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';
import { DependencyInfo } from '../../types/database.js';

export class DependencyRepository {
    private db: Database.Database;
    private logger: Logger;

    constructor(db: Database.Database, logger: Logger) {
        this.db = db;
        this.logger = logger;
    }

    saveDependencies(resourceIdOrDependencies: number | DependencyInfo[], dependencies?: DependencyInfo[]): void {
        let depsToSave: DependencyInfo[] = [];

        if (typeof resourceIdOrDependencies === 'number' && Array.isArray(dependencies)) {
            const resourceId = resourceIdOrDependencies;
            depsToSave = dependencies.map(dep => ({
                ...dep,
                resourceId: dep.resourceId || resourceId,
                sourceResourceId: dep.sourceResourceId || resourceId
            }));
        } else if (Array.isArray(resourceIdOrDependencies)) {
            depsToSave = resourceIdOrDependencies;
        } else {
            this.logger.error(`[DependencyRepository] Invalid arguments to saveDependencies`);
            return;
        }

        if (!depsToSave || depsToSave.length === 0) {
            return; // Nothing to save
        }

        this.updateDependenciesTableSchema();

        const insertStmt = this.db.prepare(`
            INSERT OR IGNORE INTO Dependencies (sourceResourceId, targetType, targetGroup, targetInstance, referenceType, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        const insertMany = this.db.transaction((deps) => {
            for (const dep of deps) {
                try {
                    const sourceId = dep.sourceResourceId || dep.resourceId;

                    insertStmt.run(
                        sourceId,
                        dep.targetType,
                        dep.targetGroup.toString(),
                        dep.targetInstance.toString(),
                        dep.referenceType || 'Unknown',
                        dep.timestamp || Date.now()
                    );
                } catch (error) {
                    const sourceId = dep.sourceResourceId || dep.resourceId;
                    this.logger.error(`[DependencyRepository] Error saving dependency for source ${sourceId} -> TGI(${dep.targetType}, ${dep.targetGroup}, ${dep.targetInstance}):`, error);
                }
            }
        });

        try {
            insertMany(depsToSave);
            this.logger.debug(`[DependencyRepository] Saved/Ignored ${depsToSave.length} dependencies`);
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error executing transaction for saving dependencies:`, error);
            throw error;
        }
    }

    private updateDependenciesTableSchema(): void {
        try {
            const hasReferenceType = this.db.prepare(`PRAGMA table_info(Dependencies)`).all()
                .some((col: any) => col.name === 'referenceType');

            const hasTimestamp = this.db.prepare(`PRAGMA table_info(Dependencies)`).all()
                .some((col: any) => col.name === 'timestamp');

            if (!hasReferenceType) {
                this.db.prepare(`ALTER TABLE Dependencies ADD COLUMN referenceType TEXT DEFAULT 'Unknown'`).run();
                this.logger.info(`[DependencyRepository] Added referenceType column to Dependencies table`);
            }

            if (!hasTimestamp) {
                this.db.prepare(`ALTER TABLE Dependencies ADD COLUMN timestamp INTEGER DEFAULT ${Date.now()}`).run();
                this.logger.info(`[DependencyRepository] Added timestamp column to Dependencies table`);
            }
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error updating Dependencies table schema:`, error);
        }
    }

    public saveDependency(dependency: DependencyInfo): number {
        this.updateDependenciesTableSchema();

        const insertStmt = this.db.prepare(`
            INSERT OR IGNORE INTO Dependencies (sourceResourceId, targetType, targetGroup, targetInstance, referenceType, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING id;
        `);

        try {
            const sourceId = dependency.sourceResourceId || dependency.resourceId;

            const result = insertStmt.get(
                sourceId,
                dependency.targetType,
                dependency.targetGroup.toString(),
                dependency.targetInstance.toString(),
                dependency.referenceType || 'Unknown',
                dependency.timestamp || Date.now()
            ) as { id: number } | undefined;

            return result?.id || -1;
        } catch (error) {
            const sourceId = dependency.sourceResourceId || dependency.resourceId;
            this.logger.error(`[DependencyRepository] Error saving dependency for source ${sourceId} -> TGI(${dependency.targetType}, ${dependency.targetGroup}, ${dependency.targetInstance}):`, error);
            return -1;
        }
    }

    public getDependencies(resourceId: number): DependencyInfo[] {
        this.logger.debug(`[DependencyRepository] Querying for dependencies for resourceId: ${resourceId}`);

        let hasReferenceType = false;
        let hasTimestamp = false;

        try {
            const columns = this.db.prepare(`PRAGMA table_info(Dependencies)`).all() as any[];
            hasReferenceType = columns.some(col => col.name === 'referenceType');
            hasTimestamp = columns.some(col => col.name === 'timestamp');
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error checking Dependencies table schema:`, error);
        }

        let query = `
            SELECT targetType, targetGroup, targetInstance`;

        if (hasReferenceType) {
            query += `, referenceType`;
        }

        if (hasTimestamp) {
            query += `, timestamp`;
        }

        query += ` FROM Dependencies WHERE sourceResourceId = ?`;

        const stmt = this.db.prepare(query);

        try {
            const rows = stmt.all(resourceId) as any[];
            this.logger.debug(`[DependencyRepository] Found ${rows.length} dependencies for resourceId ${resourceId}`);

            return rows.map(row => {
                const dependency: DependencyInfo = {
                    resourceId,
                    targetType: row.targetType,
                    targetGroup: BigInt(row.targetGroup),
                    targetInstance: BigInt(row.targetInstance)
                };

                if (hasReferenceType && row.referenceType) {
                    dependency.referenceType = row.referenceType;
                }

                if (hasTimestamp && row.timestamp) {
                    dependency.timestamp = row.timestamp;
                }

                return dependency;
            });
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error getting dependencies for resource ${resourceId}:`, error);
            return [];
        }
    }

    public getDependenciesForPackage(packageId: number): DependencyInfo[] {
        this.logger.debug(`[DependencyRepository] Querying for dependencies for packageId: ${packageId}`);

        try {
            const resourcesStmt = this.db.prepare(`
                SELECT id FROM Resources WHERE packageId = ?
            `);

            const resources = resourcesStmt.all(packageId) as { id: number }[];

            if (resources.length === 0) {
                this.logger.debug(`[DependencyRepository] No resources found for packageId ${packageId}`);
                return [];
            }

            const dependencies: DependencyInfo[] = [];

            for (const resource of resources) {
                const resourceDependencies = this.getDependencies(resource.id);
                dependencies.push(...resourceDependencies);
            }

            this.logger.debug(`[DependencyRepository] Found ${dependencies.length} dependencies for packageId ${packageId}`);
            return dependencies;
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error getting dependencies for package ${packageId}:`, error);
            return [];
        }
    }

    /**
     * Alias for getDependenciesForPackage for compatibility
     * @param packageId Package ID
     * @returns Array of dependencies
     */
    public getDependenciesByPackageId(packageId: number): DependencyInfo[] {
        return this.getDependenciesForPackage(packageId);
    }

    /**
     * Get dependencies by source resource ID
     * @param sourceResourceId Source resource ID
     * @returns Array of dependencies
     */
    public getDependenciesBySourceId(sourceResourceId: number): DependencyInfo[] {
        this.logger.debug(`[DependencyRepository] Querying for dependencies by sourceResourceId: ${sourceResourceId}`);

        try {
            const stmt = this.db.prepare(`
                SELECT id, sourceResourceId, targetType, targetGroup, targetInstance, referenceType, timestamp
                FROM Dependencies
                WHERE sourceResourceId = ?
            `);

            const rows = stmt.all(sourceResourceId) as any[];
            this.logger.debug(`[DependencyRepository] Found ${rows.length} dependencies for sourceResourceId ${sourceResourceId}`);

            return rows.map(row => ({
                id: row.id,
                sourceResourceId: row.sourceResourceId,
                targetType: row.targetType,
                targetGroup: BigInt(row.targetGroup),
                targetInstance: BigInt(row.targetInstance),
                referenceType: row.referenceType || 'Unknown',
                timestamp: row.timestamp || Date.now()
            }));
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error getting dependencies by sourceResourceId ${sourceResourceId}:`, error);
            return [];
        }
    }

    /**
     * Get all dependencies from the database
     * @param limit Optional limit on the number of dependencies to return
     * @param offset Optional offset for pagination
     * @returns Array of all dependencies
     */
    public getAllDependencies(limit?: number, offset?: number): DependencyInfo[] {
        this.logger.debug(`[DependencyRepository] Querying for all dependencies`);

        try {
            let query = `
                SELECT id, sourceResourceId, targetType, targetGroup, targetInstance, referenceType, timestamp
                FROM Dependencies
            `;

            const params: any[] = [];

            if (limit !== undefined) {
                query += ` LIMIT ?`;
                params.push(limit);

                if (offset !== undefined) {
                    query += ` OFFSET ?`;
                    params.push(offset);
                }
            }

            const stmt = this.db.prepare(query);
            const rows = stmt.all(...params) as any[];

            this.logger.debug(`[DependencyRepository] Found ${rows.length} total dependencies`);

            return rows.map(row => ({
                id: row.id,
                resourceId: row.sourceResourceId, // Map sourceResourceId to resourceId for compatibility
                sourceResourceId: row.sourceResourceId,
                targetType: row.targetType,
                targetGroup: BigInt(row.targetGroup),
                targetInstance: BigInt(row.targetInstance),
                referenceType: row.referenceType || 'Unknown',
                timestamp: row.timestamp || Date.now()
            }));
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error getting all dependencies:`, error);
            return [];
        }
    }

    /**
     * Get dependencies by target TGI
     * @param targetType Target type
     * @param targetGroup Target group
     * @param targetInstance Target instance
     * @returns Array of dependencies
     */
    public getDependenciesByTargetTGI(
        targetType: number,
        targetGroup: bigint | string,
        targetInstance: bigint | string
    ): DependencyInfo[] {
        const targetGroupStr = targetGroup.toString();
        const targetInstanceStr = targetInstance.toString();

        this.logger.debug(`[DependencyRepository] Querying for dependencies by targetTGI: (${targetType}, ${targetGroupStr}, ${targetInstanceStr})`);

        try {
            const stmt = this.db.prepare(`
                SELECT id, sourceResourceId, targetType, targetGroup, targetInstance, referenceType, timestamp
                FROM Dependencies
                WHERE targetType = ? AND targetGroup = ? AND targetInstance = ?
            `);

            const rows = stmt.all(targetType, targetGroupStr, targetInstanceStr) as any[];
            this.logger.debug(`[DependencyRepository] Found ${rows.length} dependencies for targetTGI (${targetType}, ${targetGroupStr}, ${targetInstanceStr})`);

            return rows.map(row => ({
                id: row.id,
                sourceResourceId: row.sourceResourceId,
                targetType: row.targetType,
                targetGroup: BigInt(row.targetGroup),
                targetInstance: BigInt(row.targetInstance),
                referenceType: row.referenceType || 'Unknown',
                timestamp: row.timestamp || Date.now()
            }));
        } catch (error) {
            this.logger.error(`[DependencyRepository] Error getting dependencies by targetTGI (${targetType}, ${targetGroupStr}, ${targetInstanceStr}):`, error);
            return [];
        }
    }
}