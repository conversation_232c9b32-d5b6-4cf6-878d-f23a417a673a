import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { getResourceSemantics, getRelatedResourceTypes, ResourceSemanticInfo } from '../../data/resourceSemantics.js';

const logger = new Logger('SemanticAnalysisService');

/**
 * Service for analyzing the semantic meaning and relationships of resources
 */
export class SemanticAnalysisService {
    private databaseService: DatabaseService;

    constructor(databaseService: DatabaseService) {
        this.databaseService = databaseService;
    }

    /**
     * Analyze a resource and add semantic information to the database
     * @param resourceId The ID of the resource to analyze
     */
    public async analyzeResourceSemantics(resourceId: number): Promise<void> {
        try {
            // Get the resource from the database
            const resource = await this.databaseService.resources.getResourceById(resourceId);
            if (!resource) {
                logger.warn(`Resource with ID ${resourceId} not found in database`);
                return;
            }

            // Get semantic information for this resource type
            const semantics = getResourceSemantics(resource.type);
            if (!semantics) {
                logger.debug(`No semantic information available for resource type 0x${resource.type.toString(16)}`);
                
                // Save minimal semantic information
                this.databaseService.metadata.saveMetadata({
                    resourceId,
                    key: 'semanticCategory',
                    value: 'UNKNOWN'
                });

                this.databaseService.metadata.saveMetadata({
                    resourceId,
                    key: 'semanticName',
                    value: `TYPE_0x${resource.type.toString(16).toUpperCase()}`
                });

                return;
            }

            // Save semantic information to the database
            this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'semanticName',
                value: semantics.name
            });

            this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'semanticDescription',
                value: semantics.description
            });

            this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'semanticGameplayImpact',
                value: semantics.gameplayImpact
            });

            this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'semanticSystems',
                value: JSON.stringify(semantics.systems)
            });

            this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'semanticCategory',
                value: semantics.category
            });

            this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'semanticIsOfficial',
                value: semantics.isOfficial
            });

            // Find potential related resources based on semantic relationships
            await this.findSemanticRelationships(resourceId, semantics);

            logger.debug(`Added semantic information for resource ${resourceId} (${semantics.name})`);
        } catch (error) {
            logger.error(`Error analyzing semantics for resource ${resourceId}: ${error}`);
        }
    }

    /**
     * Find and record semantic relationships between resources
     * @param resourceId The ID of the resource to analyze relationships for
     * @param semantics The semantic information for the resource
     */
    private async findSemanticRelationships(resourceId: number, semantics: ResourceSemanticInfo): Promise<void> {
        try {
            // Get related resource types from semantic information
            const relatedTypes = semantics.relatedTypes || [];
            if (relatedTypes.length === 0) {
                return;
            }

            // Get the package ID for this resource
            const resource = await this.databaseService.resources.getResourceById(resourceId);
            if (!resource) {
                return;
            }

            // Find resources of related types in the same package
            for (const relatedType of relatedTypes) {
                const relatedResources = await this.databaseService.resources.findResourcesByTypeInPackage(
                    relatedType,
                    resource.packageId
                );

                // Record relationships
                for (const relatedResource of relatedResources) {
                    // Get semantic info for the related resource
                    const relatedSemantics = getResourceSemantics(relatedType);
                    const relationshipType = relatedSemantics ?
                        `${semantics.name}_TO_${relatedSemantics.name}` :
                        `${semantics.name}_TO_TYPE_0x${relatedType.toString(16).toUpperCase()}`;

                    // Save the relationship as a dependency
                    this.databaseService.dependencies.saveDependency({
                        resourceId,
                        targetType: relatedResource.type,
                        targetGroup: relatedResource.group,
                        targetInstance: relatedResource.instance,
                        referenceType: `SEMANTIC_${relationshipType}`,
                        timestamp: Date.now()
                    });

                    logger.debug(`Added semantic relationship from ${resourceId} to ${relatedResource.id} (${relationshipType})`);
                }
            }
        } catch (error) {
            logger.error(`Error finding semantic relationships for resource ${resourceId}: ${error}`);
        }
    }

    /**
     * Analyze all resources in a package and add semantic information
     * @param packageId The ID of the package to analyze
     */
    public async analyzePackageSemantics(packageId: number): Promise<void> {
        try {
            // Get all resources in the package
            const resources = await this.databaseService.resources.getResourcesByPackageId(packageId);

            logger.info(`Analyzing semantics for ${resources.length} resources in package ${packageId}`);

            // Analyze each resource
            for (const resource of resources) {
                await this.analyzeResourceSemantics(resource.id);
            }

            logger.info(`Completed semantic analysis for package ${packageId}`);
        } catch (error) {
            logger.error(`Error analyzing semantics for package ${packageId}: ${error}`);
        }
    }

    /**
     * Get a summary of resource types and their semantic categories in a package
     * @param packageId The ID of the package to summarize
     * @returns A summary object with counts by category and type
     */
    public async getPackageSemanticSummary(packageId: number): Promise<any> {
        try {
            // Get all resources in the package
            const resources = await this.databaseService.resources.getResourcesByPackageId(packageId);

            // Initialize summary objects
            const categoryCounts: Record<string, number> = {};
            const typeCounts: Record<string, number> = {};

            // Count resources by type and category
            for (const resource of resources) {
                // Get semantic information
                const semantics = getResourceSemantics(resource.type);

                // Count by type
                const typeName = semantics ? semantics.name : `TYPE_0x${resource.type.toString(16).toUpperCase()}`;
                typeCounts[typeName] = (typeCounts[typeName] || 0) + 1;

                // Count by category
                const category = semantics ? semantics.category : 'UNKNOWN';
                categoryCounts[category] = (categoryCounts[category] || 0) + 1;
            }

            return {
                packageId,
                resourceCount: resources.length,
                categoryCounts,
                typeCounts
            };
        } catch (error) {
            logger.error(`Error getting semantic summary for package ${packageId}: ${error}`);
            return {
                packageId,
                error: `Failed to generate summary: ${error}`
            };
        }
    }
}
