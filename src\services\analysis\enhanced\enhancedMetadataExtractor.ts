/**
 * Enhanced Metadata Extractor - Phase 1 Implementation
 *
 * This module provides enhanced metadata extraction capabilities based on
 * deep analysis of Sims 4 game structure and internal mechanics.
 *
 * Key improvements:
 * - Gameplay system impact analysis
 * - Trait conflict detection enhancement
 * - Performance impact scoring
 * - Cross-resource relationship mapping
 */

import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../types/resource/interfaces.js';
import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';

const logger = new Logger('EnhancedMetadataExtractor');

/**
 * Enhanced metadata interface with gameplay-aware fields
 */
export interface EnhancedResourceMetadata extends ResourceMetadata {
    // Gameplay Impact Analysis
    gameplaySystemsAffected: string[];
    modificationSeverity: 'cosmetic' | 'functional' | 'structural' | 'core';
    performanceImpact: 'negligible' | 'low' | 'medium' | 'high' | 'extreme';

    // Compatibility Analysis
    gameVersionCompatibility: string[];
    packDependencies: string[];
    modFrameworkRequirements: string[];

    // Quality Metrics
    codeQualityScore: number;
    documentationCompleteness: number;
    testingEvidence: boolean;

    // User Experience
    userFacingChanges: string[];
    configurationOptions: ConfigOption[];
    installationComplexity: 'simple' | 'moderate' | 'complex';

    // Conflict Analysis
    conflictPotential: number; // 0-100 score
    knownConflictPatterns: string[];
    compatibilityNotes: string[];
}

/**
 * Configuration option interface
 */
export interface ConfigOption {
    name: string;
    type: 'boolean' | 'number' | 'string' | 'enum';
    defaultValue: any;
    description: string;
    impact: 'low' | 'medium' | 'high';
}

/**
 * Gameplay system registry based on Sims 4 internal structure
 */
export class GameplaySystemRegistry {
    private static readonly GAMEPLAY_SYSTEMS = {
        // Core Systems
        TRAITS: {
            name: 'Traits',
            conflictSeverity: 'high',
            keywords: ['trait', 'personality', 'buff_replacement', 'conflicting_traits']
        },
        SKILLS: {
            name: 'Skills',
            conflictSeverity: 'medium',
            keywords: ['skill', 'level', 'experience', 'skill_loot']
        },
        RELATIONSHIPS: {
            name: 'Relationships',
            conflictSeverity: 'high',
            keywords: ['relationship', 'romance', 'friendship', 'family']
        },
        CAREERS: {
            name: 'Careers',
            conflictSeverity: 'medium',
            keywords: ['career', 'job', 'promotion', 'work']
        },
        ASPIRATIONS: {
            name: 'Aspirations',
            conflictSeverity: 'medium',
            keywords: ['aspiration', 'goal', 'milestone', 'reward']
        },
        BUFFS: {
            name: 'Buffs & Moods',
            conflictSeverity: 'high',
            keywords: ['buff', 'mood', 'emotion', 'moodlet']
        },
        NEEDS: {
            name: 'Needs & Motives',
            conflictSeverity: 'high',
            keywords: ['need', 'motive', 'hunger', 'energy', 'fun', 'social']
        },
        OBJECTS: {
            name: 'Objects & Interactions',
            conflictSeverity: 'medium',
            keywords: ['object', 'interaction', 'affordance', 'catalog']
        },
        CAS: {
            name: 'Create-a-Sim',
            conflictSeverity: 'low',
            keywords: ['cas', 'appearance', 'clothing', 'hair', 'makeup']
        },
        BUILD_BUY: {
            name: 'Build/Buy Mode',
            conflictSeverity: 'low',
            keywords: ['build', 'buy', 'catalog', 'room', 'lot']
        },
        OCCULT: {
            name: 'Occult Systems',
            conflictSeverity: 'high',
            keywords: ['vampire', 'alien', 'ghost', 'spellcaster', 'werewolf']
        },
        SEASONS: {
            name: 'Seasons & Weather',
            conflictSeverity: 'medium',
            keywords: ['season', 'weather', 'holiday', 'calendar']
        },
        PETS: {
            name: 'Pets & Animals',
            conflictSeverity: 'medium',
            keywords: ['pet', 'dog', 'cat', 'animal', 'breed']
        },
        UNIVERSITY: {
            name: 'University',
            conflictSeverity: 'medium',
            keywords: ['university', 'degree', 'course', 'homework']
        },
        FAME: {
            name: 'Fame & Celebrity',
            conflictSeverity: 'medium',
            keywords: ['fame', 'celebrity', 'reputation', 'media']
        }
    };

    /**
     * Analyze which gameplay systems are affected by a resource
     */
    public static analyzeGameplayImpact(
        resourceType: string,
        content: string,
        metadata: any
    ): string[] {
        const affectedSystems: string[] = [];
        const contentLower = content.toLowerCase();

        for (const [systemKey, system] of Object.entries(this.GAMEPLAY_SYSTEMS)) {
            for (const keyword of system.keywords) {
                if (contentLower.includes(keyword)) {
                    affectedSystems.push(system.name);
                    break;
                }
            }
        }

        // Resource type specific analysis
        switch (resourceType) {
            case 'TRAIT':
                affectedSystems.push('Traits');
                if (contentLower.includes('buff')) affectedSystems.push('Buffs & Moods');
                if (contentLower.includes('skill')) affectedSystems.push('Skills');
                break;
            case 'TUNING_XML':
                // Analyze XML structure for specific systems
                if (contentLower.includes('<trait')) affectedSystems.push('Traits');
                if (contentLower.includes('<skill')) affectedSystems.push('Skills');
                if (contentLower.includes('<career')) affectedSystems.push('Careers');
                break;
            case 'SIMDATA':
                // SimData often affects core systems
                affectedSystems.push('Core Game Data');
                break;
        }

        return [...new Set(affectedSystems)]; // Remove duplicates
    }

    /**
     * Calculate conflict potential based on affected systems
     */
    public static calculateConflictPotential(affectedSystems: string[]): number {
        let conflictScore = 0;

        for (const systemName of affectedSystems) {
            const system = Object.values(this.GAMEPLAY_SYSTEMS)
                .find(s => s.name === systemName);

            if (system) {
                switch (system.conflictSeverity) {
                    case 'high': conflictScore += 30; break;
                    case 'medium': conflictScore += 20; break;
                    case 'low': conflictScore += 10; break;
                }
            }
        }

        return Math.min(conflictScore, 100); // Cap at 100
    }
}

/**
 * Performance impact analyzer
 */
export class PerformanceImpactAnalyzer {
    /**
     * Analyze performance impact of a resource
     */
    public static analyzePerformanceImpact(
        resourceType: string,
        size: number,
        content: string,
        metadata: any
    ): 'negligible' | 'low' | 'medium' | 'high' | 'extreme' {
        let impactScore = 0;

        // Size-based impact
        if (size > 10 * 1024 * 1024) impactScore += 40; // 10MB+
        else if (size > 5 * 1024 * 1024) impactScore += 30; // 5MB+
        else if (size > 1 * 1024 * 1024) impactScore += 20; // 1MB+
        else if (size > 100 * 1024) impactScore += 10; // 100KB+

        // Content-based impact
        const contentLower = content.toLowerCase();

        // Script complexity indicators
        if (contentLower.includes('while') || contentLower.includes('for')) impactScore += 10;
        if (contentLower.includes('threading') || contentLower.includes('async')) impactScore += 15;
        if (contentLower.includes('database') || contentLower.includes('sql')) impactScore += 10;

        // Animation and graphics
        if (resourceType.includes('ANIMATION') || resourceType.includes('VFX')) impactScore += 15;
        if (resourceType.includes('IMAGE') && size > 1024 * 1024) impactScore += 10;

        // Audio impact
        if (resourceType.includes('AUDIO') && size > 5 * 1024 * 1024) impactScore += 20;

        // Resource type specific impact
        switch (resourceType) {
            case 'SCRIPT':
            case 'PYTHON_SCRIPT':
                impactScore += 20; // Scripts always have some performance impact
                break;
            case 'SIMDATA':
                impactScore += 10; // SimData affects core game performance
                break;
            case 'TUNING_XML':
                impactScore += 5; // XML parsing has minimal impact
                break;
        }

        // Convert score to category
        if (impactScore >= 80) return 'extreme';
        if (impactScore >= 60) return 'high';
        if (impactScore >= 40) return 'medium';
        if (impactScore >= 20) return 'low';
        return 'negligible';
    }
}

/**
 * Enhanced metadata extractor class
 */
export class EnhancedMetadataExtractor {
    private databaseService: DatabaseService;

    constructor(databaseService: DatabaseService) {
        this.databaseService = databaseService;
    }

    /**
     * Extract enhanced metadata from a resource
     */
    public async extractEnhancedMetadata(
        key: AppResourceKey,
        buffer: Buffer,
        resourceId: number,
        resourceType: string,
        existingMetadata: Partial<ResourceMetadata> = {}
    ): Promise<EnhancedResourceMetadata> {
        try {
            const content = buffer.toString('utf8', 0, Math.min(buffer.length, 10000)); // First 10KB for analysis

            // Analyze gameplay impact
            const gameplaySystemsAffected = GameplaySystemRegistry.analyzeGameplayImpact(
                resourceType,
                content,
                existingMetadata
            );

            // Calculate performance impact
            const performanceImpact = PerformanceImpactAnalyzer.analyzePerformanceImpact(
                resourceType,
                buffer.length,
                content,
                existingMetadata
            );

            // Calculate conflict potential
            const conflictPotential = GameplaySystemRegistry.calculateConflictPotential(
                gameplaySystemsAffected
            );

            // Determine modification severity
            const modificationSeverity = this.determineModificationSeverity(
                resourceType,
                gameplaySystemsAffected,
                content
            );

            // Build enhanced metadata
            const enhancedMetadata: EnhancedResourceMetadata = {
                ...existingMetadata,
                name: existingMetadata.name || `Resource_${key.instance.toString(16)}`,
                path: existingMetadata.path || '',
                size: buffer.length,
                hash: existingMetadata.hash || '',
                timestamp: Date.now(),

                // Enhanced fields
                gameplaySystemsAffected,
                modificationSeverity,
                performanceImpact,
                gameVersionCompatibility: this.detectGameVersionCompatibility(content),
                packDependencies: this.detectPackDependencies(content),
                modFrameworkRequirements: this.detectModFrameworks(content),
                codeQualityScore: this.calculateCodeQuality(content, resourceType),
                documentationCompleteness: this.assessDocumentation(content),
                testingEvidence: this.detectTestingEvidence(content),
                userFacingChanges: this.identifyUserFacingChanges(content, resourceType),
                configurationOptions: this.extractConfigurationOptions(content),
                installationComplexity: this.assessInstallationComplexity(resourceType, content),
                conflictPotential,
                knownConflictPatterns: this.identifyConflictPatterns(content, resourceType),
                compatibilityNotes: this.generateCompatibilityNotes(gameplaySystemsAffected)
            };

            // Save enhanced metadata to database
            await this.saveEnhancedMetadata(resourceId, enhancedMetadata);

            return enhancedMetadata;

        } catch (error) {
            logger.error(`Error extracting enhanced metadata for resource ${resourceId}:`, error);

            // Return basic enhanced metadata on error
            return {
                ...existingMetadata,
                name: existingMetadata.name || `Resource_${key.instance.toString(16)}`,
                path: existingMetadata.path || '',
                size: buffer.length,
                hash: existingMetadata.hash || '',
                timestamp: Date.now(),
                gameplaySystemsAffected: [],
                modificationSeverity: 'functional',
                performanceImpact: 'low',
                gameVersionCompatibility: [],
                packDependencies: [],
                modFrameworkRequirements: [],
                codeQualityScore: 50,
                documentationCompleteness: 0,
                testingEvidence: false,
                userFacingChanges: [],
                configurationOptions: [],
                installationComplexity: 'moderate',
                conflictPotential: 50,
                knownConflictPatterns: [],
                compatibilityNotes: []
            } as EnhancedResourceMetadata;
        }
    }

    /**
     * Determine modification severity based on content analysis
     */
    private determineModificationSeverity(
        resourceType: string,
        gameplaySystemsAffected: string[],
        content: string
    ): 'cosmetic' | 'functional' | 'structural' | 'core' {
        // Core modifications
        if (gameplaySystemsAffected.includes('Traits') ||
            gameplaySystemsAffected.includes('Needs & Motives') ||
            content.includes('core_') ||
            content.includes('base_game')) {
            return 'core';
        }

        // Structural modifications
        if (gameplaySystemsAffected.length > 3 ||
            content.includes('override') ||
            content.includes('replace')) {
            return 'structural';
        }

        // Functional modifications
        if (gameplaySystemsAffected.length > 0 ||
            resourceType.includes('SCRIPT') ||
            resourceType.includes('TUNING')) {
            return 'functional';
        }

        // Cosmetic modifications
        return 'cosmetic';
    }

    /**
     * Detect game version compatibility from content
     */
    private detectGameVersionCompatibility(content: string): string[] {
        const versions: string[] = [];
        const contentLower = content.toLowerCase();

        // Common version patterns
        if (contentLower.includes('base_game') || contentLower.includes('1.0')) versions.push('Base Game');
        if (contentLower.includes('get_to_work') || contentLower.includes('gtw')) versions.push('Get to Work');
        if (contentLower.includes('get_together') || contentLower.includes('gt')) versions.push('Get Together');
        if (contentLower.includes('city_living') || contentLower.includes('cl')) versions.push('City Living');
        if (contentLower.includes('cats_dogs') || contentLower.includes('cd')) versions.push('Cats & Dogs');
        if (contentLower.includes('seasons')) versions.push('Seasons');
        if (contentLower.includes('get_famous') || contentLower.includes('gf')) versions.push('Get Famous');
        if (contentLower.includes('island_living') || contentLower.includes('il')) versions.push('Island Living');
        if (contentLower.includes('discover_university') || contentLower.includes('du')) versions.push('Discover University');
        if (contentLower.includes('eco_lifestyle') || contentLower.includes('el')) versions.push('Eco Lifestyle');
        if (contentLower.includes('snowy_escape') || contentLower.includes('se')) versions.push('Snowy Escape');
        if (contentLower.includes('cottage_living') || contentLower.includes('cl2')) versions.push('Cottage Living');
        if (contentLower.includes('high_school') || contentLower.includes('hs')) versions.push('High School Years');
        if (contentLower.includes('growing_together') || contentLower.includes('gt2')) versions.push('Growing Together');
        if (contentLower.includes('horse_ranch') || contentLower.includes('hr')) versions.push('Horse Ranch');

        return versions.length > 0 ? versions : ['Base Game'];
    }

    /**
     * Detect pack dependencies
     */
    private detectPackDependencies(content: string): string[] {
        const dependencies: string[] = [];
        const contentLower = content.toLowerCase();

        // Look for pack-specific content
        if (contentLower.includes('vampire') || contentLower.includes('plasma')) dependencies.push('Vampires');
        if (contentLower.includes('alien') || contentLower.includes('sixam')) dependencies.push('Get to Work');
        if (contentLower.includes('club') || contentLower.includes('windenburg')) dependencies.push('Get Together');
        if (contentLower.includes('apartment') || contentLower.includes('san_myshuno')) dependencies.push('City Living');
        if (contentLower.includes('vet') || contentLower.includes('brindleton')) dependencies.push('Cats & Dogs');
        if (contentLower.includes('celebrity') || contentLower.includes('del_sol')) dependencies.push('Get Famous');
        if (contentLower.includes('mermaid') || contentLower.includes('sulani')) dependencies.push('Island Living');
        if (contentLower.includes('university') || contentLower.includes('britechester')) dependencies.push('Discover University');

        return dependencies;
    }

    /**
     * Detect mod framework requirements
     */
    private detectModFrameworks(content: string): string[] {
        const frameworks: string[] = [];
        const contentLower = content.toLowerCase();

        if (contentLower.includes('mccc') || contentLower.includes('mc_command_center')) frameworks.push('MC Command Center');
        if (contentLower.includes('basemental') || contentLower.includes('bmg')) frameworks.push('Basemental Mods');
        if (contentLower.includes('wicked') || contentLower.includes('turbodriver')) frameworks.push('WickedWhims');
        if (contentLower.includes('slice_of_life') || contentLower.includes('sol')) frameworks.push('Slice of Life');
        if (contentLower.includes('ui_cheats')) frameworks.push('UI Cheats Extension');

        return frameworks;
    }

    /**
     * Calculate code quality score
     */
    private calculateCodeQuality(content: string, resourceType: string): number {
        let score = 50; // Base score

        // Positive indicators
        if (content.includes('# ') || content.includes('//') || content.includes('<!--')) score += 10; // Comments
        if (content.includes('def ') || content.includes('class ')) score += 10; // Structured code
        if (content.includes('try:') || content.includes('except:')) score += 10; // Error handling
        if (content.includes('logger') || content.includes('log.')) score += 5; // Logging

        // Negative indicators
        if (content.includes('TODO') || content.includes('FIXME')) score -= 5; // Unfinished work
        if (content.includes('hack') || content.includes('workaround')) score -= 10; // Poor practices
        if (content.length < 100) score -= 20; // Very short files might be incomplete

        return Math.max(0, Math.min(100, score));
    }

    /**
     * Assess documentation completeness
     */
    private assessDocumentation(content: string): number {
        let score = 0;
        const lines = content.split('\n');
        const commentLines = lines.filter(line =>
            line.trim().startsWith('#') ||
            line.trim().startsWith('//') ||
            line.includes('<!--')
        ).length;

        const commentRatio = commentLines / Math.max(lines.length, 1);
        score = Math.round(commentRatio * 100);

        return Math.min(100, score);
    }

    /**
     * Detect testing evidence
     */
    private detectTestingEvidence(content: string): boolean {
        const contentLower = content.toLowerCase();
        return contentLower.includes('test') ||
               contentLower.includes('assert') ||
               contentLower.includes('unittest') ||
               contentLower.includes('pytest');
    }

    /**
     * Identify user-facing changes
     */
    private identifyUserFacingChanges(content: string, resourceType: string): string[] {
        const changes: string[] = [];
        const contentLower = content.toLowerCase();

        if (contentLower.includes('ui') || contentLower.includes('interface')) changes.push('User Interface');
        if (contentLower.includes('menu') || contentLower.includes('dialog')) changes.push('Menus & Dialogs');
        if (contentLower.includes('notification') || contentLower.includes('message')) changes.push('Notifications');
        if (contentLower.includes('animation') || contentLower.includes('visual')) changes.push('Visual Effects');
        if (contentLower.includes('sound') || contentLower.includes('audio')) changes.push('Audio');
        if (contentLower.includes('interaction') || contentLower.includes('action')) changes.push('Interactions');

        return changes;
    }

    /**
     * Extract configuration options
     */
    private extractConfigurationOptions(content: string): ConfigOption[] {
        const options: ConfigOption[] = [];
        // This would be implemented to parse configuration from content
        // For now, return empty array
        return options;
    }

    /**
     * Assess installation complexity
     */
    private assessInstallationComplexity(resourceType: string, content: string): 'simple' | 'moderate' | 'complex' {
        const contentLower = content.toLowerCase();

        if (contentLower.includes('install') && contentLower.includes('script')) return 'complex';
        if (contentLower.includes('dependency') || contentLower.includes('require')) return 'moderate';
        if (resourceType.includes('SCRIPT')) return 'moderate';

        return 'simple';
    }

    /**
     * Identify known conflict patterns
     */
    private identifyConflictPatterns(content: string, resourceType: string): string[] {
        const patterns: string[] = [];
        const contentLower = content.toLowerCase();

        if (contentLower.includes('override') || contentLower.includes('replace')) {
            patterns.push('Override Pattern');
        }
        if (contentLower.includes('conflicting_traits')) {
            patterns.push('Trait Conflict');
        }
        if (contentLower.includes('buff_replacement')) {
            patterns.push('Buff Replacement');
        }

        return patterns;
    }

    /**
     * Generate compatibility notes
     */
    private generateCompatibilityNotes(gameplaySystemsAffected: string[]): string[] {
        const notes: string[] = [];

        if (gameplaySystemsAffected.includes('Traits')) {
            notes.push('May conflict with other trait mods');
        }
        if (gameplaySystemsAffected.includes('Buffs & Moods')) {
            notes.push('Check for mood system conflicts');
        }
        if (gameplaySystemsAffected.includes('Skills')) {
            notes.push('Verify skill progression compatibility');
        }

        return notes;
    }

    /**
     * Save enhanced metadata to database
     */
    private async saveEnhancedMetadata(resourceId: number, metadata: EnhancedResourceMetadata): Promise<void> {
        try {
            // Save gameplay systems affected
            for (const system of metadata.gameplaySystemsAffected) {
                await this.databaseService.metadata.saveMetadata({
                    resourceId,
                    key: 'gameplaySystem',
                    value: system
                });
            }

            // Save other enhanced metadata fields
            await this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'modificationSeverity',
                value: metadata.modificationSeverity
            });

            await this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'performanceImpact',
                value: metadata.performanceImpact
            });

            await this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'conflictPotential',
                value: metadata.conflictPotential.toString()
            });

            await this.databaseService.metadata.saveMetadata({
                resourceId,
                key: 'codeQualityScore',
                value: metadata.codeQualityScore.toString()
            });

        } catch (error) {
            logger.error(`Error saving enhanced metadata for resource ${resourceId}:`, error);
        }
    }
}
