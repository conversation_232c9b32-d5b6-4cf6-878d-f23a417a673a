/**
 * Electron Integration Utilities
 * 
 * This module provides utilities for integrating with Electron,
 * including environment detection, IPC communication, and resource management.
 */

import { Logger } from '../logging/logger.js';
import { getLogDirectory } from '../logging/loggerConfig.js';
import { EventEmitter } from 'events';
import { createTrackedEmitter } from '../eventEmitterConfig.js';
import path from 'path';
import os from 'os';
import fs from 'fs';

// Create a logger for this module
const logger = new Logger('ElectronIntegration');

// Create an event emitter for Electron events
const electronEvents = createTrackedEmitter('ElectronEvents');

/**
 * Check if the application is running in an Electron environment
 * @returns True if running in Electron, false otherwise
 */
export function isElectronEnvironment(): boolean {
    return !!(
        process.versions.electron ||
        process.env.ELECTRON_RUN_AS_NODE ||
        process.env.ELECTRON_RENDERER ||
        process.env.ELECTRON_BROWSER
    );
}

/**
 * Get the Electron process type
 * @returns The process type ('main', 'renderer', 'worker', or 'unknown')
 */
export function getElectronProcessType(): 'main' | 'renderer' | 'worker' | 'unknown' {
    if (!isElectronEnvironment()) {
        return 'unknown';
    }

    if (process.type === 'renderer') {
        return 'renderer';
    }

    if (process.type === 'worker') {
        return 'worker';
    }

    // If process.type is undefined but we're in Electron, it's the main process
    return 'main';
}

/**
 * Get the user data directory for the application
 * This is where application data should be stored
 * 
 * @param appName Optional application name (default: 'sims4-mod-manager')
 * @returns The path to the user data directory
 */
export function getUserDataDirectory(appName: string = 'sims4-mod-manager'): string {
    // In Electron, use the app.getPath('userData') API
    if (isElectronEnvironment() && global.electron?.app?.getPath) {
        return global.electron.app.getPath('userData');
    }

    // For non-Electron environments, use platform-specific paths
    let userDataDir: string;

    if (process.env.APPDATA) {
        // Windows
        userDataDir = path.join(process.env.APPDATA, appName);
    } else if (process.platform === 'darwin') {
        // macOS
        userDataDir = path.join(os.homedir(), 'Library', 'Application Support', appName);
    } else {
        // Linux and others
        userDataDir = path.join(os.homedir(), '.config', appName);
    }

    // Ensure the directory exists
    try {
        fs.mkdirSync(userDataDir, { recursive: true });
    } catch (error) {
        logger.error(`Failed to create user data directory: ${userDataDir}`, error);
        // Fall back to temp directory
        userDataDir = path.join(os.tmpdir(), appName);
        fs.mkdirSync(userDataDir, { recursive: true });
    }

    return userDataDir;
}

/**
 * Get the logs directory for the application
 * @returns The path to the logs directory
 */
export function getLogsDirectory(): string {
    return getLogDirectory();
}

/**
 * Subscribe to an Electron event
 * @param event The event name
 * @param listener The event listener
 * @returns A function to unsubscribe from the event
 */
export function subscribeToElectronEvent(
    event: string,
    listener: (...args: any[]) => void
): () => void {
    electronEvents.on(event, listener);
    return () => electronEvents.off(event, listener);
}

/**
 * Emit an Electron event
 * @param event The event name
 * @param args The event arguments
 */
export function emitElectronEvent(event: string, ...args: any[]): void {
    electronEvents.emit(event, ...args);
}

/**
 * Check for memory leaks in Electron event listeners
 * This should be called during application shutdown
 */
export function checkForElectronEventLeaks(): void {
    (electronEvents as any).checkForLeaks();
}

/**
 * Initialize Electron integration
 * This should be called when the application starts
 */
export function initializeElectronIntegration(): void {
    logger.info('Initializing Electron integration');
    
    // Register cleanup handler
    if (isElectronEnvironment()) {
        const processType = getElectronProcessType();
        logger.info(`Running in Electron ${processType} process`);
        
        // Register exit handler
        process.on('exit', () => {
            logger.info('Process exiting, checking for event leaks');
            checkForElectronEventLeaks();
        });
    } else {
        logger.info('Not running in Electron environment');
    }
}

/**
 * Clean up Electron integration resources
 * This should be called when the application exits
 */
export function cleanupElectronIntegration(): void {
    logger.info('Cleaning up Electron integration resources');
    
    // Check for event leaks
    checkForElectronEventLeaks();
    
    // Remove all event listeners
    electronEvents.removeAllListeners();
}

// Export the event emitter for direct access if needed
export { electronEvents };
