/**
 * Dependency Graph Analyzer
 * 
 * This class extends the DependencyGraphBuilder with analysis capabilities.
 * It provides methods for analyzing the dependency graph, such as finding
 * circular dependencies, calculating metrics, and generating dependency
 * chains and trees.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { EnhancedMemoryManager } from '../../../../utils/memory/enhancedMemoryManager.js';
import { DependencyGraphBuilder } from './dependencyGraphBuilder.js';
import { 
  DependencyNode, 
  DependencyMetrics
} from './types.js';
import { injectable, singleton } from '../../../di/decorators.js';

/**
 * DependencyGraphAnalyzer class
 * 
 * This class extends the DependencyGraphBuilder with analysis capabilities.
 * It provides methods for analyzing the dependency graph, such as finding
 * circular dependencies, calculating metrics, and generating dependency
 * chains and trees.
 */
@injectable()
@singleton()
export class DependencyGraphAnalyzer extends DependencyGraphBuilder {
  /**
   * Constructor
   * @param databaseService Database service
   * @param memoryManager Memory manager
   */
  constructor(
    databaseService: DatabaseService,
    memoryManager?: EnhancedMemoryManager
  ) {
    super(databaseService, memoryManager);
  }
  
  /**
   * Get the dependency chain for a resource
   * @param resourceId Resource ID
   * @returns Array of resource IDs in the dependency chain
   */
  public getDependencyChain(resourceId: string): string[] {
    const logger = new Logger('DependencyGraphAnalyzer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return [];
    }
    
    // Check cache first
    const cachedChain = this.getDependencyChainFromCache(resourceId);
    if (cachedChain) {
      return [...cachedChain];
    }
    
    const chain: string[] = [];
    const visited: Set<string> = new Set();
    
    this.buildDependencyChain(resourceId, chain, visited);
    
    // Cache the result
    this.cacheDependencyChain(resourceId, [...chain]);
    
    return chain;
  }
  
  /**
   * Build the dependency chain for a resource
   * @param resourceId Resource ID
   * @param chain Chain to build
   * @param visited Set of visited resource IDs
   */
  private buildDependencyChain(resourceId: string, chain: string[], visited: Set<string>): void {
    if (visited.has(resourceId)) {
      return;
    }
    
    visited.add(resourceId);
    chain.push(resourceId);
    
    const dependencies = this.getDependencies(resourceId);
    
    for (const dependencyId of dependencies) {
      this.buildDependencyChain(dependencyId, chain, visited);
    }
  }
  
  /**
   * Get the dependency tree for a resource
   * @param resourceId Resource ID
   * @returns Dependency tree
   */
  public getDependencyTree(resourceId: string): DependencyNode {
    const logger = new Logger('DependencyGraphAnalyzer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return { resourceId, dependencies: [] };
    }
    
    // Check cache first
    const cachedTree = this.getDependencyTreeFromCache(resourceId);
    if (cachedTree) {
      return this.cloneDependencyNode(cachedTree);
    }
    
    const visited: Set<string> = new Set();
    const tree = this.buildDependencyTree(resourceId, visited);
    
    // Cache the result
    this.cacheDependencyTree(resourceId, this.cloneDependencyNode(tree));
    
    return tree;
  }
  
  /**
   * Build the dependency tree for a resource
   * @param resourceId Resource ID
   * @param visited Set of visited resource IDs
   * @returns Dependency tree
   */
  private buildDependencyTree(resourceId: string, visited: Set<string>): DependencyNode {
    const node: DependencyNode = {
      resourceId,
      metadata: this.getResourceMetadata(resourceId),
      dependencies: []
    };
    
    if (visited.has(resourceId)) {
      return node;
    }
    
    visited.add(resourceId);
    
    const dependencies = this.getDependencies(resourceId);
    
    for (const dependencyId of dependencies) {
      node.dependencies.push(this.buildDependencyTree(dependencyId, visited));
    }
    
    return node;
  }
  
  /**
   * Find circular dependencies in the graph
   * @returns Array of circular dependency chains
   */
  public findCircularDependencies(): string[][] {
    const logger = new Logger('DependencyGraphAnalyzer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return [];
    }
    
    const circularDependencies: string[][] = [];
    const visited: Set<string> = new Set();
    const recursionStack: Set<string> = new Set();
    
    // Check each node for circular dependencies
    for (const resourceId of this.getResourceIds()) {
      if (!visited.has(resourceId)) {
        this.findCircularDependenciesDFS(
          resourceId,
          visited,
          recursionStack,
          [],
          circularDependencies
        );
      }
    }
    
    return circularDependencies;
  }
  
  /**
   * Find circular dependencies using depth-first search
   * @param resourceId Current resource ID
   * @param visited Set of visited resource IDs
   * @param recursionStack Set of resource IDs in the current recursion stack
   * @param currentPath Current path
   * @param circularDependencies Array of circular dependency chains
   */
  private findCircularDependenciesDFS(
    resourceId: string,
    visited: Set<string>,
    recursionStack: Set<string>,
    currentPath: string[],
    circularDependencies: string[][]
  ): void {
    visited.add(resourceId);
    recursionStack.add(resourceId);
    currentPath.push(resourceId);
    
    const dependencies = this.getDependencies(resourceId);
    
    for (const dependencyId of dependencies) {
      if (!visited.has(dependencyId)) {
        this.findCircularDependenciesDFS(
          dependencyId,
          visited,
          recursionStack,
          [...currentPath],
          circularDependencies
        );
      } else if (recursionStack.has(dependencyId)) {
        // Found a circular dependency
        const cycle = [...currentPath];
        const startIndex = cycle.indexOf(dependencyId);
        if (startIndex !== -1) {
          circularDependencies.push(cycle.slice(startIndex));
        }
      }
    }
    
    recursionStack.delete(resourceId);
  }
  
  /**
   * Find strongly connected components in the graph using Tarjan's algorithm
   * @returns Array of strongly connected components
   */
  public findStronglyConnectedComponents(): string[][] {
    const logger = new Logger('DependencyGraphAnalyzer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return [];
    }
    
    const components: string[][] = [];
    const visited: Map<string, boolean> = new Map();
    const indices: Map<string, number> = new Map();
    const lowLinks: Map<string, number> = new Map();
    const stack: string[] = [];
    const onStack: Map<string, boolean> = new Map();
    let index = 0;
    
    // Initialize visited map
    for (const resourceId of this.getResourceIds()) {
      visited.set(resourceId, false);
    }
    
    // Run Tarjan's algorithm for each unvisited node
    for (const resourceId of this.getResourceIds()) {
      if (!visited.get(resourceId)) {
        this.tarjanSCC(
          resourceId,
          index,
          indices,
          lowLinks,
          visited,
          stack,
          onStack,
          components
        );
      }
    }
    
    return components;
  }
  
  /**
   * Tarjan's algorithm for finding strongly connected components
   * @param resourceId Current resource ID
   * @param index Current index
   * @param indices Map of indices
   * @param lowLinks Map of low links
   * @param visited Map of visited nodes
   * @param stack Stack of nodes
   * @param onStack Map of nodes on the stack
   * @param components Array of strongly connected components
   */
  private tarjanSCC(
    resourceId: string,
    index: number,
    indices: Map<string, number>,
    lowLinks: Map<string, number>,
    visited: Map<string, boolean>,
    stack: string[],
    onStack: Map<string, boolean>,
    components: string[][]
  ): void {
    visited.set(resourceId, true);
    indices.set(resourceId, index);
    lowLinks.set(resourceId, index);
    index++;
    stack.push(resourceId);
    onStack.set(resourceId, true);
    
    const dependencies = this.getDependencies(resourceId);
    
    for (const dependencyId of dependencies) {
      if (!visited.get(dependencyId)) {
        this.tarjanSCC(
          dependencyId,
          index,
          indices,
          lowLinks,
          visited,
          stack,
          onStack,
          components
        );
        lowLinks.set(
          resourceId,
          Math.min(lowLinks.get(resourceId)!, lowLinks.get(dependencyId)!)
        );
      } else if (onStack.get(dependencyId)) {
        lowLinks.set(
          resourceId,
          Math.min(lowLinks.get(resourceId)!, indices.get(dependencyId)!)
        );
      }
    }
    
    // If resourceId is a root node, pop the stack and generate a strongly connected component
    if (lowLinks.get(resourceId) === indices.get(resourceId)) {
      const component: string[] = [];
      let w: string;
      
      do {
        w = stack.pop()!;
        onStack.set(w, false);
        component.push(w);
      } while (w !== resourceId);
      
      // Only add components with more than one node (circular dependencies)
      if (component.length > 1) {
        components.push(component);
      }
    }
  }
  
  /**
   * Calculate dependency metrics
   * @returns Dependency metrics
   */
  public calculateDependencyMetrics(): DependencyMetrics {
    const logger = new Logger('DependencyGraphAnalyzer');
    
    if (!this.isGraphBuilt()) {
      logger.warn('Dependency graph has not been built yet');
      return {
        totalResources: 0,
        totalDependencies: 0,
        averageDependenciesPerResource: 0,
        maxDependenciesForResource: 0,
        resourceWithMostDependencies: '',
        maxDependentsForResource: 0,
        resourceWithMostDependents: '',
        circularDependencies: 0,
        stronglyConnectedComponents: 0
      };
    }
    
    let totalResources = this.getResourceCount();
    let totalDependencies = 0;
    let maxDependenciesForResource = 0;
    let resourceWithMostDependencies = '';
    let maxDependentsForResource = 0;
    let resourceWithMostDependents = '';
    
    // Calculate total dependencies and find resource with most dependencies
    for (const resourceId of this.getResourceIds()) {
      const dependencyCount = this.getDependencies(resourceId).size;
      totalDependencies += dependencyCount;
      
      if (dependencyCount > maxDependenciesForResource) {
        maxDependenciesForResource = dependencyCount;
        resourceWithMostDependencies = resourceId;
      }
      
      const dependentCount = this.getDependents(resourceId).size;
      
      if (dependentCount > maxDependentsForResource) {
        maxDependentsForResource = dependentCount;
        resourceWithMostDependents = resourceId;
      }
    }
    
    // Calculate average dependencies per resource
    const averageDependenciesPerResource = totalResources > 0 ?
      totalDependencies / totalResources : 0;
    
    // Find circular dependencies
    const circularDependencies = this.findCircularDependencies();
    
    // Find strongly connected components
    const stronglyConnectedComponents = this.findStronglyConnectedComponents();
    
    return {
      totalResources,
      totalDependencies,
      averageDependenciesPerResource,
      maxDependenciesForResource,
      resourceWithMostDependencies,
      maxDependentsForResource,
      resourceWithMostDependents,
      circularDependencies: circularDependencies.length,
      stronglyConnectedComponents: stronglyConnectedComponents.length
    };
  }
  
  /**
   * Clone a dependency node
   * @param node Node to clone
   * @returns Cloned node
   */
  private cloneDependencyNode(node: DependencyNode): DependencyNode {
    return {
      resourceId: node.resourceId,
      metadata: node.metadata,
      dependencies: node.dependencies.map(dep => this.cloneDependencyNode(dep))
    };
  }
}
