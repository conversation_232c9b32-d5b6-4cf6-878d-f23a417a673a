/**
 * Additional Resource Extractor for Terrain Paint Analysis
 * 
 * Extracts metadata from additional resources in terrain paint mods.
 * These resources may contain additional information about terrain paints.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey } from '../../../../../types/resource/interfaces.js';

const log = new Logger('AdditionalResourceExtractor');

/**
 * Extracts metadata from additional resources
 */
export class AdditionalResourceExtractor {
    /**
     * Extracts metadata from additional resources
     * @param resources Array of additional resources
     * @returns Metadata extracted from additional resources
     */
    public async extract(resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[]): Promise<{
        properties?: Record<string, any>;
    }> {
        if (resources.length === 0) {
            return {};
        }
        
        log.info(`Extracting metadata from ${resources.length} additional resources`);
        
        // Parse each additional resource
        const parsedResources = resources.map(resource => this.parseAdditionalResource(resource.buffer, resource.key.type));
        
        // Combine properties from all resources
        const properties: Record<string, any> = {};
        
        for (const resource of parsedResources) {
            if (resource.properties) {
                Object.assign(properties, resource.properties);
            }
        }
        
        return {
            properties
        };
    }
    
    /**
     * Parses an additional resource buffer
     * @param buffer Additional resource buffer
     * @param type Resource type
     * @returns Parsed additional resource
     */
    private parseAdditionalResource(buffer: Buffer, type: number): {
        properties?: Record<string, any>;
    } {
        try {
            // Different resource types may have different formats
            switch (type) {
                case 0xEBCBB16C:
                    return this.parseType1Resource(buffer);
                case 0x01D0E75D:
                    return this.parseType2Resource(buffer);
                default:
                    return {};
            }
        } catch (error) {
            log.error(`Error parsing additional resource: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a Type 1 resource buffer (0xEBCBB16C)
     * @param buffer Type 1 resource buffer
     * @returns Parsed Type 1 resource
     */
    private parseType1Resource(buffer: Buffer): {
        properties?: Record<string, any>;
    } {
        try {
            // Type 1 resources may contain terrain paint properties
            // Since we don't know the exact format, we'll try to extract some basic information
            
            const properties: Record<string, any> = {};
            
            // Check if the buffer is large enough
            if (buffer.length < 8) {
                return { properties };
            }
            
            // Extract version (first 4 bytes)
            const version = buffer.readUInt32LE(0);
            properties.version = version;
            
            // Extract flags (next 4 bytes)
            const flags = buffer.readUInt32LE(4);
            properties.flags = flags;
            
            // Extract additional properties if available
            if (buffer.length >= 16) {
                // Extract property 1 (next 4 bytes)
                const property1 = buffer.readUInt32LE(8);
                properties.property1 = property1;
                
                // Extract property 2 (next 4 bytes)
                const property2 = buffer.readUInt32LE(12);
                properties.property2 = property2;
            }
            
            return { properties };
        } catch (error) {
            log.error(`Error parsing Type 1 resource: ${error}`);
            return {};
        }
    }
    
    /**
     * Parses a Type 2 resource buffer (0x01D0E75D)
     * @param buffer Type 2 resource buffer
     * @returns Parsed Type 2 resource
     */
    private parseType2Resource(buffer: Buffer): {
        properties?: Record<string, any>;
    } {
        try {
            // Type 2 resources may contain terrain paint properties
            // Since we don't know the exact format, we'll try to extract some basic information
            
            const properties: Record<string, any> = {};
            
            // Check if the buffer is large enough
            if (buffer.length < 8) {
                return { properties };
            }
            
            // Extract version (first 4 bytes)
            const version = buffer.readUInt32LE(0);
            properties.version = version;
            
            // Extract flags (next 4 bytes)
            const flags = buffer.readUInt32LE(4);
            properties.flags = flags;
            
            // Extract additional properties if available
            if (buffer.length >= 16) {
                // Extract property 1 (next 4 bytes)
                const property1 = buffer.readUInt32LE(8);
                properties.property1 = property1;
                
                // Extract property 2 (next 4 bytes)
                const property2 = buffer.readUInt32LE(12);
                properties.property2 = property2;
            }
            
            return { properties };
        } catch (error) {
            log.error(`Error parsing Type 2 resource: ${error}`);
            return {};
        }
    }
}
