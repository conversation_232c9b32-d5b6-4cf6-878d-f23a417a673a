/**
 * Analysis Types
 *
 * This module contains types for package analysis functionality.
 */

// Corrected imports
import { BinaryResourceType } from '../resource/core.js';
import { ResourceCategory } from '../resource/enums.js';
import { ResourceMetadata } from '../resource/interfaces.js';
import { PackageMetadata } from '../resource/Package.js'; // Import PackageMetadata from Package.js
import { ConflictResult } from '../conflict/ConflictTypes.js';
import { ResourceConflict, ConflictInfo } from '../conflict/index.js'; // Assuming index.js exports ConflictInfo

// Export local interfaces
export interface ResourceAnalysisMetadata {
  name?: string;
  description?: string;
  typeDescription?: string;
  category?: ResourceCategory;
  isScript?: boolean;
  isTuning?: boolean;
  isAsset?: boolean;
  scriptType?: BinaryResourceType;
}

export interface ResourceAnalysisInfo {
  type: BinaryResourceType; // Removed TuningResourceType as it's not defined/exported
  size: number;
  count: number;
  description?: string;
  category: ResourceCategory;
  metadata: ResourceAnalysisMetadata;
  hash?: string;
}

export interface AnalysisTypes {
  BASIC: 'BASIC';
  STANDARD: 'STANDARD';
  DETAILED: 'DETAILED';
  COMPREHENSIVE: 'COMPREHENSIVE';
}

export interface AnalysisOptions {
  type: keyof AnalysisTypes;
  includeMetadata?: boolean;
  includeStats?: boolean;
  includeConflicts?: boolean;
  includeDependencies?: boolean;
  includeOverrides?: boolean;
  includeCustomData?: boolean;
  customDataFields?: string[];
  maxDepth?: number;
  timeout?: number;
}

export interface AnalysisResults {
  metadata: PackageMetadata;
  conflicts: ConflictResult[];
  dependencies: string[];
  overrides: string[];
  stats: AnalysisStats;
  errors?: Error[];
  warnings?: string[];
}

export interface AnalysisStats {
  totalSize: number;
  compressedSize: number;
  compressionRatio: number;
  resourceCount: number;
  conflictCount: number;
  errorCount: number;
  warningCount: number;
}

export interface PackageAnalysisResult {
  metadata: PackageMetadata;
  conflicts: ResourceConflict[];
  validation: ValidationResult;
  stats: AnalysisStats;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Export enums
export enum AnalysisTier {
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  DETAILED = 'DETAILED',
  COMPREHENSIVE = 'COMPREHENSIVE'
}

export enum AnalysisStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export enum AnalysisPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Export types for analysis context and progress
export interface AnalysisContext {
  tier: AnalysisTier;
  priority: AnalysisPriority;
  options: AnalysisOptions;
  metadata?: PackageMetadata;
}

export interface AnalysisProgress {
  status: AnalysisStatus;
  current: number;
  total: number;
  message?: string;
  errors?: Error[];
  warnings?: string[];
}

// Export types for analysis tasks and queue
export interface AnalysisTask {
  id: string;
  context: AnalysisContext;
  status: AnalysisStatus;
  progress: AnalysisProgress;
  result?: AnalysisResults;
  error?: Error;
  createdAt: Date;
  updatedAt: Date;
}

export interface AnalysisQueue {
  tasks: AnalysisTask[];
  addTask(task: AnalysisTask): void;
  removeTask(taskId: string): void;
  getTask(taskId: string): AnalysisTask | undefined;
  getNextTask(): AnalysisTask | undefined;
  clear(): void;
}

// Export types for analysis events
export interface AnalysisEvent {
  type: string;
  data: any;
  timestamp: Date;
}

export interface AnalysisListener {
  (event: AnalysisEvent): void;
}

export interface AnalysisEmitter {
  on(event: string, listener: AnalysisListener): void;
  off(event: string, listener: AnalysisListener): void;
  emit(event: string, data: any): void;
}

// Export types for analysis metrics
export interface AnalysisMetrics {
  startTime: Date;
  endTime?: Date;
  duration?: number;
  resourceCount: number;
  conflictCount: number;
  errorCount: number;
  warningCount: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

// Export type for analysis service
export interface AnalysisService {
  analyze(packagePath: string, options: AnalysisOptions): Promise<AnalysisResults>;
  validate(packagePath: string, options: AnalysisOptions): Promise<ValidationResult>;
  getProgress(taskId: string): Promise<AnalysisProgress>;
  cancel(taskId: string): Promise<void>;
}
