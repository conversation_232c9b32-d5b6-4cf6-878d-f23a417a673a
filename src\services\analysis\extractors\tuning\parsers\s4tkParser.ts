import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey as AppResourceKey } from '../../../../../types/resource/interfaces.js';
import { TuningParseResult } from '../types.js';
import { createTuningExtractionContext, handleTuningExtractionError } from '../error/tuningExtractorErrorHandler.js';
import * as S4TKModels from '@s4tk/models';
import xml2js from 'xml2js';

/**
 * Attempts to parse a buffer using S4TK models
 * @param processedBuffer The preprocessed buffer
 * @param key The resource key
 * @param resourceId The resource ID
 * @param log The logger instance
 * @returns The parsed tuning resource and a flag indicating if parsing was successful
 */
export async function parseWithS4TK(
    processedBuffer: Buffer,
    key: AppResourceKey,
    resourceId: number,
    log: Logger
): Promise<TuningParseResult> {
    let tuningResource: any = null;
    let parsedWithS4TK = false;
    let contentSnippet: string | undefined = undefined;

    try {
        // Based on the debug output, we know S4TKModels has XmlResource
        // But we need to figure out how to use it correctly
        log.debug(`Attempting to parse with S4TK models`);

        // Log the available S4TK models for debugging
        log.debug(`Available S4TK models: ${Object.keys(S4TKModels).join(', ')}`);

        // Try to use the TuningResource class if available
        // @ts-ignore - Ignore TypeScript errors since we're checking dynamically
        if (S4TKModels.TuningResource) {
            try {
                // Try to create a TuningResource from the buffer
                // @ts-ignore - Ignore TypeScript errors since we're dynamically trying methods
                tuningResource = new S4TKModels.TuningResource(processedBuffer);
                log.debug(`Created TuningResource instance`);
                parsedWithS4TK = true;
            } catch (tuningError: any) {
                const context = createTuningExtractionContext(key, resourceId, 'S4TKParser', {
                    bufferLength: processedBuffer.length,
                    parserType: 'TuningResource'
                });

                log.warn(`Failed to create TuningResource: ${tuningError.message}`);
                log.debug(`Buffer content (first 100 bytes): ${processedBuffer.toString('hex', 0, 100)}`);
            }
        }

        // If TuningResource failed, try XmlResource
        if (!parsedWithS4TK && S4TKModels.XmlResource) {
            try {
                // Try to create an XmlResource from the buffer
                // @ts-ignore - Ignore TypeScript errors since we're dynamically trying methods
                tuningResource = new S4TKModels.XmlResource(processedBuffer);
                log.debug(`Created XmlResource instance`);
                parsedWithS4TK = true;
            } catch (xmlError: any) {
                const context = createTuningExtractionContext(key, resourceId, 'S4TKParser', {
                    bufferLength: processedBuffer.length,
                    parserType: 'XmlResource'
                });

                log.warn(`Failed to create XmlResource: ${xmlError.message}`);
                log.debug(`Buffer content (first 100 bytes): ${processedBuffer.toString('hex', 0, 100)}`);
            }
        }

        // Log result of parsing
        try {
            log.debug(`Result of S4TK parsing:`, {
                parsedOk: !!tuningResource,
                hasDom: !!tuningResource?.dom,
                domRootTag: tuningResource?.dom?.root?.tag,
                domRootAttributes: tuningResource?.dom?.root?.attributes ? Object.keys(tuningResource.dom.root.attributes || {}) : 'N/A'
            });
        } catch (logError: any) {
            // Handle error when accessing properties
            log.debug(`Error accessing tuningResource properties: ${logError.message || logError}`);

            // Log a simpler version
            log.debug(`Result of S4TK parsing (simplified):`, {
                parsedOk: !!tuningResource,
                hasDom: !!tuningResource?.dom
            });
        }

        if (tuningResource?.dom) {
            log.info(`Successfully parsed Tuning XML for ${key.instance.toString(16)} (Type: 0x${key.type.toString(16)}) using @s4tk/models`);
            parsedWithS4TK = true;

            // Check if the DOM structure is valid
            if (!tuningResource.dom.root) {
                log.warn(`DOM exists but has no root element, creating a minimal valid DOM structure`);

                // Create a minimal valid DOM structure
                tuningResource.dom.root = {
                    tag: 'I',
                    attributes: {
                        n: `tuning_${key.type.toString(16)}_${key.instance.toString(16)}`,
                        s: key.instance.toString(),
                        m: '0',
                        t: '0'
                    },
                    children: []
                };

                log.debug(`Created minimal valid DOM structure`);
            }

            // Create content snippet
            contentSnippet = `[Tuning Parsed: Root=${tuningResource.dom.root?.tag ?? 'N/A'}]`;

            // Log the DOM structure for debugging
            try {
                log.debug(`DOM structure: ${JSON.stringify({
                    tag: tuningResource.dom.root.tag,
                    attributes: tuningResource.dom.root.attributes,
                    childrenCount: tuningResource.dom.root.children?.length || 0
                })}`);
            } catch (domError: any) {
                log.error(`Error logging DOM structure: ${domError.message || domError}`);
                log.debug(`DOM structure (simplified): ${JSON.stringify({
                    hasRoot: !!tuningResource.dom.root
                })}`);
            }
        } else if (tuningResource) {
            // If we have a tuningResource but no dom, try to access it differently
            log.debug(`TuningResource exists but no dom property. Available properties: ${Object.keys(tuningResource).join(', ')}`);

            // Try to find XML content in other properties
            if (tuningResource.content) {
                log.debug(`Found content property in tuningResource`);
                // Try to parse the content as XML
                try {
                    const xmlString = tuningResource.content.toString('utf-8');
                    log.debug(`Content as string: ${xmlString.substring(0, 200)}`);

                    // Try to parse the XML string with xml2js
                    const xml2jsOptions = {
                        explicitArray: false,
                        ignoreAttrs: false,
                        strict: false,
                        normalizeTags: false
                    };

                    xml2js.parseString(xmlString, xml2jsOptions, (err: any, result: any) => {
                        if (err) {
                            log.error(`Failed to parse content with xml2js: ${err.message}`);
                        } else {
                            log.debug(`Successfully parsed content with xml2js: ${JSON.stringify(result)}`);

                            // Create a dom-like structure from the xml2js result
                            tuningResource.dom = {
                                root: result.I || result.root || Object.values(result)[0]
                            };
                            parsedWithS4TK = true;
                        }
                    });
                } catch (contentError: any) {
                    log.error(`Failed to process content: ${contentError.message}`);
                }
            }
        } else {
            log.warn(`S4TK parsing returned no usable object for ${key.instance.toString(16)}`);
        }

        return {
            tuningResource,
            parsedWithS4TK,
            contentSnippet
        };
    } catch (s4tkTuningError: any) {
        const context = createTuningExtractionContext(key, resourceId, 'S4TKParser', {
            bufferLength: processedBuffer.length,
            parserType: 'S4TK',
            isProblematicType: (key.type === 0x3c1d8799 || key.type === 0x220557da)
        });

        // Handle the error
        return handleTuningExtractionError(s4tkTuningError, context, log);
    }
}
