/**
 * Unit tests for DependencyGraphAnalyzer
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DependencyGraphAnalyzer } from '../../../../services/analysis/semantic/dependencyGraph/dependencyGraphAnalyzer.js';
import { DatabaseService } from '../../../../services/databaseService.js';

// Mock the database service
const mockDatabaseService = {
  resources: {
    getAllResources: vi.fn(),
    getResourcesByPackageId: vi.fn(),
    getResourceById: vi.fn(),
    getResourcesByTGI: vi.fn()
  },
  dependencies: {
    getAllDependencies: vi.fn(),
    getDependenciesByPackageId: vi.fn(),
    getDependencies: vi.fn()
  }
} as unknown as DatabaseService;

describe('DependencyGraphAnalyzer', () => {
  let dependencyGraphAnalyzer: DependencyGraphAnalyzer;
  
  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();
    
    // Create a new instance of DependencyGraphAnalyzer
    dependencyGraphAnalyzer = new DependencyGraphAnalyzer(mockDatabaseService);
  });
  
  afterEach(() => {
    // Clear the dependency graph
    dependencyGraphAnalyzer.clear();
  });
  
  it('should initialize correctly', () => {
    expect(dependencyGraphAnalyzer).toBeDefined();
  });
  
  it('should get the dependency chain for a resource', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' },
      { id: 3, type: 0x0166038C, group: '0', instance: '2', resourceType: 'TUNING' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' },
      { resourceId: 2, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 2n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockImplementation((type, group, instance) => {
      if (type === 0x545AC67A && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 2, type, group, instance, resourceType: 'SIMDATA' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '2') {
        return Promise.resolve([{ id: 3, type, group, instance, resourceType: 'TUNING' }]);
      } else {
        return Promise.resolve([]);
      }
    });
    
    // Build the graph
    await dependencyGraphAnalyzer.buildGraph();
    
    // Get the dependency chain
    const chain = dependencyGraphAnalyzer.getDependencyChain('1');
    
    // Check the chain
    expect(chain).toContain('1');
    expect(chain).toContain('2');
    expect(chain).toContain('3');
  });
  
  it('should get the dependency tree for a resource', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' },
      { id: 3, type: 0x0166038C, group: '0', instance: '2', resourceType: 'TUNING' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' },
      { resourceId: 2, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 2n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockImplementation((type, group, instance) => {
      if (type === 0x545AC67A && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 2, type, group, instance, resourceType: 'SIMDATA' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '2') {
        return Promise.resolve([{ id: 3, type, group, instance, resourceType: 'TUNING' }]);
      } else {
        return Promise.resolve([]);
      }
    });
    
    // Build the graph
    await dependencyGraphAnalyzer.buildGraph({ includeResourceMetadata: true });
    
    // Get the dependency tree
    const tree = dependencyGraphAnalyzer.getDependencyTree('1');
    
    // Check the tree
    expect(tree.resourceId).toBe('1');
    expect(tree.dependencies.length).toBe(1);
    expect(tree.dependencies[0].resourceId).toBe('2');
    expect(tree.dependencies[0].dependencies.length).toBe(1);
    expect(tree.dependencies[0].dependencies[0].resourceId).toBe('3');
  });
  
  it('should find circular dependencies', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' },
      { id: 3, type: 0x0166038C, group: '0', instance: '2', resourceType: 'TUNING' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' },
      { resourceId: 2, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 2n, referenceType: 'Required' },
      { resourceId: 3, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockImplementation((type, group, instance) => {
      if (type === 0x545AC67A && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 2, type, group, instance, resourceType: 'SIMDATA' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '2') {
        return Promise.resolve([{ id: 3, type, group, instance, resourceType: 'TUNING' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 1, type, group, instance, resourceType: 'TUNING' }]);
      } else {
        return Promise.resolve([]);
      }
    });
    
    // Build the graph
    await dependencyGraphAnalyzer.buildGraph();
    
    // Find circular dependencies
    const circularDependencies = dependencyGraphAnalyzer.findCircularDependencies();
    
    // Check that circular dependencies were found
    expect(circularDependencies.length).toBeGreaterThan(0);
    
    // Check that the circular dependency includes all three resources
    const flattenedDependencies = circularDependencies.flat();
    expect(flattenedDependencies).toContain('1');
    expect(flattenedDependencies).toContain('2');
    expect(flattenedDependencies).toContain('3');
  });
  
  it('should find strongly connected components', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' },
      { id: 3, type: 0x0166038C, group: '0', instance: '2', resourceType: 'TUNING' },
      { id: 4, type: 0x0166038C, group: '0', instance: '3', resourceType: 'TUNING' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' },
      { resourceId: 2, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 2n, referenceType: 'Required' },
      { resourceId: 3, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' },
      { resourceId: 1, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 3n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockImplementation((type, group, instance) => {
      if (type === 0x545AC67A && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 2, type, group, instance, resourceType: 'SIMDATA' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '2') {
        return Promise.resolve([{ id: 3, type, group, instance, resourceType: 'TUNING' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 1, type, group, instance, resourceType: 'TUNING' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '3') {
        return Promise.resolve([{ id: 4, type, group, instance, resourceType: 'TUNING' }]);
      } else {
        return Promise.resolve([]);
      }
    });
    
    // Build the graph
    await dependencyGraphAnalyzer.buildGraph();
    
    // Find strongly connected components
    const components = dependencyGraphAnalyzer.findStronglyConnectedComponents();
    
    // Check that strongly connected components were found
    expect(components.length).toBeGreaterThan(0);
    
    // Check that one of the components includes resources 1, 2, and 3
    const hasCircularComponent = components.some(component => {
      return component.includes('1') && component.includes('2') && component.includes('3');
    });
    
    expect(hasCircularComponent).toBe(true);
  });
  
  it('should calculate dependency metrics', async () => {
    // Mock database responses
    mockDatabaseService.resources.getAllResources.mockResolvedValue([
      { id: 1, type: 0x0166038C, group: '0', instance: '1', resourceType: 'TUNING' },
      { id: 2, type: 0x545AC67A, group: '0', instance: '1', resourceType: 'SIMDATA' },
      { id: 3, type: 0x0166038C, group: '0', instance: '2', resourceType: 'TUNING' }
    ]);
    
    mockDatabaseService.dependencies.getAllDependencies.mockResolvedValue([
      { resourceId: 1, targetType: 0x545AC67A, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' },
      { resourceId: 2, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 2n, referenceType: 'Required' },
      { resourceId: 3, targetType: 0x0166038C, targetGroup: 0n, targetInstance: 1n, referenceType: 'Required' }
    ]);
    
    mockDatabaseService.resources.getResourcesByTGI.mockImplementation((type, group, instance) => {
      if (type === 0x545AC67A && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 2, type, group, instance, resourceType: 'SIMDATA' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '2') {
        return Promise.resolve([{ id: 3, type, group, instance, resourceType: 'TUNING' }]);
      } else if (type === 0x0166038C && group === '0' && instance === '1') {
        return Promise.resolve([{ id: 1, type, group, instance, resourceType: 'TUNING' }]);
      } else {
        return Promise.resolve([]);
      }
    });
    
    // Build the graph
    await dependencyGraphAnalyzer.buildGraph();
    
    // Calculate metrics
    const metrics = dependencyGraphAnalyzer.calculateDependencyMetrics();
    
    // Check metrics
    expect(metrics.totalResources).toBe(3);
    expect(metrics.totalDependencies).toBe(3);
    expect(metrics.averageDependenciesPerResource).toBe(1);
    expect(metrics.circularDependencies).toBeGreaterThan(0);
    expect(metrics.stronglyConnectedComponents).toBeGreaterThan(0);
  });
});
