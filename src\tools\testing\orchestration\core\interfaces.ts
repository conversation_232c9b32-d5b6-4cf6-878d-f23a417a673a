/**
 * Core interfaces for the Test Orchestration System
 * 
 * These interfaces define the structure for player workflow simulation,
 * CLI-to-GUI bridging, and AI-compatible test execution.
 */

/**
 * Player persona definition - represents different types of Sims 4 players
 */
export interface PlayerPersona {
    name: string;
    description: string;
    characteristics: {
        experienceLevel: 'novice' | 'experienced' | 'power_user';
        riskTolerance: 'low' | 'medium' | 'high';
        preferredWorkflow: 'guided' | 'efficient' | 'advanced';
        errorHandling: 'stop_on_error' | 'retry_once' | 'skip_and_continue';
    };
    preferences: {
        batchSize: number;
        timeoutTolerance: number; // seconds
        detailLevel: 'minimal' | 'standard' | 'verbose';
        autoResolveConflicts: boolean;
    };
    continueOnFailure: boolean;
    expectedActions: string[]; // Actions this persona typically performs
}

/**
 * Scenario definition - declarative workflow specification
 */
export interface ScenarioDefinition {
    metadata: {
        name: string;
        description: string;
        version: string;
        author: string;
        tags: string[];
        difficulty: 'beginner' | 'intermediate' | 'advanced';
        estimatedDuration: number; // minutes
    };
    prerequisites: {
        minModCount: number;
        maxModCount: number;
        requiredFeatures: string[];
        hardwareRequirements: {
            minMemoryMB: number;
            minStorageGB: number;
            recommendedCPUCores: number;
        };
    };
    actions: ActionDefinition[];
    validation: ValidationCriteria;
    benchmarks: BenchmarkCriteria;
    aiMetadata: {
        learningObjectives: string[];
        successIndicators: string[];
        failurePatterns: string[];
    };
}

/**
 * Individual action within a workflow
 */
export interface ActionDefinition {
    id: string;
    type: string; // e.g., 'install_mod', 'organize_collection', 'detect_conflicts'
    name: string;
    description: string;
    parameters: Record<string, any>;
    timeout: number; // seconds
    retryPolicy: {
        maxRetries: number;
        backoffStrategy: 'linear' | 'exponential';
        retryableErrors: string[];
    };
    validation: {
        successCriteria: string[];
        performanceThresholds: Record<string, number>;
        stateChecks: string[];
    };
    guiSimulation?: {
        interfaceType: 'wizard' | 'form' | 'drag_drop' | 'context_menu';
        userInteractions: GuiInteraction[];
        visualFeedback: string[];
    };
}

/**
 * GUI interaction simulation for future interface testing
 */
export interface GuiInteraction {
    type: 'click' | 'drag' | 'input' | 'select' | 'confirm';
    target: string; // Element identifier
    value?: any;
    expectedResponse: string;
    timing: {
        delay: number; // milliseconds
        duration: number; // milliseconds
    };
}

/**
 * Validation criteria for workflow outcomes
 */
export interface ValidationCriteria {
    required: {
        functionalChecks: string[];
        performanceChecks: string[];
        stateChecks: string[];
    };
    optional: {
        qualityChecks: string[];
        usabilityChecks: string[];
        accessibilityChecks: string[];
    };
    thresholds: {
        maxExecutionTime: number; // seconds
        maxMemoryUsage: number; // MB
        minSuccessRate: number; // percentage
    };
}

/**
 * Performance benchmark criteria
 */
export interface BenchmarkCriteria {
    performance: {
        executionTime: { min: number; max: number; target: number };
        memoryUsage: { min: number; max: number; target: number };
        cpuUsage: { min: number; max: number; target: number };
    };
    scalability: {
        modCounts: number[];
        expectedLinearScaling: boolean;
        maxDegradation: number; // percentage
    };
    reliability: {
        successRate: number; // percentage
        errorRecovery: boolean;
        dataIntegrity: boolean;
    };
}

/**
 * Result of workflow execution
 */
export interface WorkflowResult {
    scenarioPath: string;
    persona: string;
    startTime: number;
    endTime: number;
    duration: number;
    success: boolean;
    actions: ActionResult[];
    benchmarks: BenchmarkResults;
    validation: ValidationResults;
    errors: string[];
    warnings: string[];
    aiInsights: AIInsights;
    developmentGuidance: string[];
}

/**
 * Result of individual action execution
 */
export interface ActionResult {
    actionId: string;
    actionType: string;
    startTime: number;
    endTime: number;
    duration: number;
    success: boolean;
    output: any;
    metrics: {
        memoryUsed: number;
        cpuTime: number;
        ioOperations: number;
    };
    validation: {
        criteriaChecked: string[];
        criteriaPass: string[];
        criteriaFail: string[];
    };
    errors: string[];
    warnings: string[];
    stateChanges: Record<string, any>;
}

/**
 * Benchmark execution results
 */
export interface BenchmarkResults {
    performance: {
        executionTime: number;
        memoryPeak: number;
        cpuAverage: number;
        ioThroughput: number;
    };
    scalability: {
        testedCounts: number[];
        scalingFactor: number;
        degradationPoints: number[];
    };
    reliability: {
        successRate: number;
        errorTypes: Record<string, number>;
        recoverySuccess: boolean;
    };
    comparison: {
        baseline: string;
        improvement: number; // percentage
        regression: number; // percentage
    };
}

/**
 * Validation results
 */
export interface ValidationResults {
    overallSuccess: boolean;
    functionalValidation: {
        passed: string[];
        failed: string[];
        skipped: string[];
    };
    performanceValidation: {
        withinThresholds: boolean;
        metrics: Record<string, number>;
        violations: string[];
    };
    stateValidation: {
        consistent: boolean;
        expectedState: Record<string, any>;
        actualState: Record<string, any>;
        differences: string[];
    };
}

/**
 * AI-compatible insights and recommendations
 */
export interface AIInsights {
    performanceAnalysis: {
        bottlenecks: string[];
        optimizationOpportunities: string[];
        scalabilityLimits: string[];
    };
    featureGaps: {
        missingFeatures: string[];
        partialImplementations: string[];
        priorityScore: Record<string, number>;
    };
    userExperience: {
        frictionPoints: string[];
        successPatterns: string[];
        improvementSuggestions: string[];
    };
    developmentGuidance: {
        nextSteps: string[];
        dependencies: Record<string, string[]>;
        estimatedEffort: Record<string, number>;
    };
}

/**
 * System state snapshot
 */
export interface SystemState {
    timestamp: number;
    modCollection: {
        totalMods: number;
        categorizedMods: number;
        conflictingMods: number;
        enabledMods: number;
    };
    performance: {
        memoryUsage: number;
        cpuUsage: number;
        diskUsage: number;
    };
    userPreferences: Record<string, any>;
    systemHealth: {
        databaseIntegrity: boolean;
        fileSystemConsistency: boolean;
        configurationValid: boolean;
    };
}

/**
 * Feature compatibility status
 */
export interface FeatureCompatibility {
    featureName: string;
    implemented: boolean;
    partialImplementation: boolean;
    dependencies: string[];
    estimatedImplementationEffort: number; // hours
    priority: 'low' | 'medium' | 'high' | 'critical';
    blockers: string[];
}
