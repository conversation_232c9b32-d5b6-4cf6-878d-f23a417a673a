import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey as AppResourceKey } from '../../../types/resource/interfaces.js';
import { ResourceMetadata } from '../../../types/resource/interfaces.js';

const log = new Logger('ModularPartMetadataExtractor');

/**
 * Extracts metadata from Modular Part resources.
 * Modular Part resources define parts that can be combined to create modular objects in the game,
 * such as sectional sofas, modular closets, and other furniture that can be arranged in different configurations.
 *
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object with modular part information.
 */
export async function extractModularPartMetadata(
    key: AppResourceKey,
    buffer: Buffer,
    _resourceId: number, // Unused parameter, prefixed with underscore
    _databaseService: DatabaseService // Unused parameter, prefixed with underscore
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};

    try {
        // Verify this is a Modular Part resource
        if (key.type !== 0xBA856C78) {
            log.warn(`Resource ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)} is not a Modular Part resource`);
            return {
                contentSnippet: `[Not a Modular Part resource: ${key.type.toString(16)}]`,
                extractorUsed: 'modularpart'
            };
        }

        log.info(`Extracting metadata from Modular Part resource: ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)}`);

        // Extract the size of the buffer
        extractedMetadata.modularPartSize = buffer.length;

        // Try to identify the modular part type based on the instance ID and other resources in the package
        // This is a heuristic and might not be accurate for all modular part resources
        const instanceHex = key.instance.toString(16).toLowerCase();
        let partType = 'Unknown';

        // Common modular part types based on instance patterns
        if (instanceHex.startsWith('b')) {
            partType = 'Build Mode';
        } else if (instanceHex.startsWith('c')) {
            partType = 'CAS';
        }

        // Try to determine the type based on the group ID
        if (key.group === BigInt(0x80000000)) {
            // Check if this is a CAS part by looking at the first few bytes of the buffer
            // Many CAS parts have specific signatures
            const signature = Buffer.from(buffer.subarray(0, 16)).toString('hex');

            if (signature.includes('6361')) { // 'ca' in hex
                partType = 'CAS';
            }

            // Check for specific CAS part types by examining the entire buffer as a string
            const bufferStr = buffer.toString();

            // Check for hair-related terms
            if (bufferStr.includes('hair') || bufferStr.includes('Hair')) {
                partType = 'CAS Hair';
            }
            // Check for accessory-related terms
            else if (bufferStr.includes('accessory') || bufferStr.includes('Accessory') ||
                    bufferStr.includes('earring') || bufferStr.includes('Earring') ||
                    bufferStr.includes('necklace') || bufferStr.includes('Necklace') ||
                    bufferStr.includes('bracelet') || bufferStr.includes('Bracelet') ||
                    bufferStr.includes('jewelry') || bufferStr.includes('Jewelry')) {
                partType = 'CAS Accessory';
            }
            // Check for clothing-related terms
            else if (bufferStr.includes('dress') || bufferStr.includes('Dress') ||
                    bufferStr.includes('outfit') || bufferStr.includes('Outfit') ||
                    bufferStr.includes('cloth') || bufferStr.includes('Cloth') ||
                    bufferStr.includes('maternity') || bufferStr.includes('Maternity') ||
                    bufferStr.includes('shirt') || bufferStr.includes('Shirt') ||
                    bufferStr.includes('pant') || bufferStr.includes('Pant') ||
                    bufferStr.includes('skirt') || bufferStr.includes('Skirt')) {
                partType = 'CAS Clothing';
            }
            // Check for footwear-related terms
            else if (bufferStr.includes('shoe') || bufferStr.includes('Shoe') ||
                    bufferStr.includes('boot') || bufferStr.includes('Boot') ||
                    bufferStr.includes('slipper') || bufferStr.includes('Slipper') ||
                    bufferStr.includes('sandal') || bufferStr.includes('Sandal')) {
                partType = 'CAS Footwear';
            }

            // Check the package name for additional clues
            const instanceStr = key.instance.toString(16);
            if (instanceStr.includes('hair') || instanceStr.includes('Hair')) {
                partType = 'CAS Hair';
            } else if (instanceStr.includes('earring') || instanceStr.includes('Earring') ||
                      instanceStr.includes('necklace') || instanceStr.includes('Necklace') ||
                      instanceStr.includes('bracelet') || instanceStr.includes('Bracelet')) {
                partType = 'CAS Accessory';
            } else if (instanceStr.includes('dress') || instanceStr.includes('Dress') ||
                      instanceStr.includes('cloth') || instanceStr.includes('Cloth')) {
                partType = 'CAS Clothing';
            }
        }

        // Store the part type
        extractedMetadata.modularPartType = partType;

        // Based on research from sectional_sofa.py and modular_object_component.py
        // Determine if this is a furniture piece
        const bufferStr = buffer.toString();

        // Check for furniture types
        if (bufferStr.includes('sofa') || bufferStr.includes('Sofa')) {
            extractedMetadata.modularPartCategory = 'Sectional Sofa';
        } else if (bufferStr.includes('closet') || bufferStr.includes('Closet') ||
                  bufferStr.includes('wardrobe') || bufferStr.includes('Wardrobe')) {
            extractedMetadata.modularPartCategory = 'Modular Closet';
        } else if (bufferStr.includes('shelf') || bufferStr.includes('Shelf')) {
            extractedMetadata.modularPartCategory = 'Modular Shelf';
        } else if (bufferStr.includes('counter') || bufferStr.includes('Counter') ||
                  bufferStr.includes('kitchen') || bufferStr.includes('Kitchen')) {
            extractedMetadata.modularPartCategory = 'Kitchen Counter';
        }

        // Check for CAS items based on the file name
        const fileName = key.name ? key.name.toLowerCase() : '';
        log.info(`Checking file name: "${fileName}"`);

        if (fileName.includes('hair')) {
            log.info('Detected CAS Hair from file name');
            extractedMetadata.modularPartCategory = 'CAS Hair';
        } else if (fileName.includes('earring') || fileName.includes('necklace') ||
                  fileName.includes('bracelet') || fileName.includes('jewelry') ||
                  fileName.includes('accessory')) {
            log.info('Detected CAS Accessory from file name');
            extractedMetadata.modularPartCategory = 'CAS Accessory';
        } else if (fileName.includes('dress') || fileName.includes('cloth') ||
                  fileName.includes('outfit') || fileName.includes('maternity') ||
                  fileName.includes('shirt') || fileName.includes('pant') ||
                  fileName.includes('skirt')) {
            log.info('Detected CAS Clothing from file name');
            extractedMetadata.modularPartCategory = 'CAS Clothing';
        } else if (fileName.includes('shoe') || fileName.includes('boot') ||
                  fileName.includes('slipper') || fileName.includes('sandal')) {
            log.info('Detected CAS Footwear from file name');
            extractedMetadata.modularPartCategory = 'CAS Footwear';
        }

        // If we still don't have a category, check the instance ID
        if (!extractedMetadata.modularPartCategory) {
            const instanceStr = key.instance.toString(16).toLowerCase();

            if (instanceStr.includes('hair')) {
                extractedMetadata.modularPartCategory = 'CAS Hair';
            } else if (instanceStr.includes('earring') || instanceStr.includes('necklace') ||
                      instanceStr.includes('bracelet') || instanceStr.includes('jewelry') ||
                      instanceStr.includes('accessory')) {
                extractedMetadata.modularPartCategory = 'CAS Accessory';
            } else if (instanceStr.includes('dress') || instanceStr.includes('cloth') ||
                      instanceStr.includes('outfit') || instanceStr.includes('maternity') ||
                      instanceStr.includes('shirt') || instanceStr.includes('pant') ||
                      instanceStr.includes('skirt')) {
                extractedMetadata.modularPartCategory = 'CAS Clothing';
            }
        }

        // Look for adjacency information
        // Based on part_data.py, modular parts can have adjacency relationships (LEFT, RIGHT, etc.)
        let hasAdjacencyInfo = false;
        const adjacencyTerms = ['adjacency', 'adjacent', 'left', 'right', 'identity'];
        const bufferStrLower = bufferStr.toLowerCase();

        for (const term of adjacencyTerms) {
            if (bufferStrLower.includes(term)) {
                hasAdjacencyInfo = true;
                break;
            }
        }

        extractedMetadata.modularPartHasAdjacencyInfo = hasAdjacencyInfo;

        // Look for part data information
        // Based on part_data.py, modular parts can have part data
        let hasPartData = false;
        const partDataTerms = ['part_data', 'partdata', 'part definition', 'partdefinition'];

        for (const term of partDataTerms) {
            if (bufferStrLower.includes(term)) {
                hasPartData = true;
                break;
            }
        }

        extractedMetadata.modularPartHasPartData = hasPartData;

        // Parse the buffer to extract modular part information
        // The format of Modular Part resources is complex and may vary
        // We'll extract some basic information based on common patterns

        // Look for references to other resources
        // Modular parts often reference other resources like meshes, textures, etc.
        const references: number[] = [];

        // Look for potential resource references (32-bit integers that might be resource IDs)
        for (let i = 0; i < buffer.length - 4; i += 4) {
            const value = buffer.readUInt32LE(i);
            // Resource IDs are typically large numbers but not too large
            if (value > 1000000 && value < 0xFFFFFFFF) {
                references.push(value);
            }
        }

        // Remove duplicates
        const uniqueReferences = [...new Set(references)];
        extractedMetadata.modularPartReferenceCount = uniqueReferences.length;

        // Look for slot information
        // Modular parts often define slots for attaching other parts
        let slotCount = 0;
        const slotSignatures = [
            Buffer.from([0x53, 0x4C, 0x4F, 0x54]), // "SLOT" in ASCII
            Buffer.from([0x73, 0x6C, 0x6F, 0x74]), // "slot" in ASCII
            Buffer.from([0x42, 0x6F, 0x6E, 0x65])  // "Bone" in ASCII - often used for attachment points
        ];

        // Additional slot-related terms to search for
        const slotTerms = ['slot', 'bone', 'attach', 'joint', 'socket'];

        // Search for binary signatures
        for (const signature of slotSignatures) {
            for (let i = 0; i < buffer.length - signature.length; i++) {
                let match = true;
                for (let j = 0; j < signature.length; j++) {
                    if (buffer[i + j] !== signature[j]) {
                        match = false;
                        break;
                    }
                }
                if (match) {
                    slotCount++;
                }
            }
        }

        // Search for text-based slot references
        if (buffer.length > 100) {
            const bufferStr = buffer.toString();
            for (const term of slotTerms) {
                let pos = 0;
                while ((pos = bufferStr.indexOf(term, pos)) !== -1) {
                    slotCount++;
                    pos += term.length;
                }
            }
        }

        extractedMetadata.modularPartSlotCount = slotCount;

        // Create a content snippet
        let contentSnippet = `Modular Part (${partType})`;

        // Add category if available
        if (extractedMetadata.modularPartCategory) {
            contentSnippet = `Modular Part (${extractedMetadata.modularPartCategory})`;
        }

        if (extractedMetadata.modularPartSize) {
            contentSnippet += `, ${(extractedMetadata.modularPartSize / 1024).toFixed(2)} KB`;
        }

        if (extractedMetadata.modularPartReferenceCount) {
            contentSnippet += `, ${extractedMetadata.modularPartReferenceCount} references`;
        }

        if (extractedMetadata.modularPartSlotCount) {
            contentSnippet += `, ${extractedMetadata.modularPartSlotCount} slots`;
        }

        if (extractedMetadata.modularPartHasAdjacencyInfo) {
            contentSnippet += `, has adjacency info`;
        }

        if (extractedMetadata.modularPartHasPartData) {
            contentSnippet += `, has part data`;
        }

        extractedMetadata.contentSnippet = contentSnippet;
        extractedMetadata.extractorUsed = 'modularpart';

        return extractedMetadata;
    } catch (error) {
        log.error(`Error extracting Modular Part metadata: ${error}`);
        return {
            contentSnippet: `[Error extracting Modular Part metadata: ${error}]`,
            extractorUsed: 'modularpart',
            extractionError: String(error)
        };
    }
}
