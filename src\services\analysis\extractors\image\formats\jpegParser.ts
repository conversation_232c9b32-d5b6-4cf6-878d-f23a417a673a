/**
 * JPEG image format parser
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ImageFormat, JPEGMetadata } from '../types.js';
import { <PERSON><PERSON>erReader } from '../utils/bufferReader.js';
import { createImageErrorContext, handleImageError } from '../error/imageExtractorErrorHandler.js';

// Create a logger instance
const log = new Logger('JPEGParser');

/**
 * JPEG marker types
 */
export enum JPEGMarker {
    SOI = 0xFFD8, // Start of Image
    EOI = 0xFFD9, // End of Image
    SOS = 0xFFDA, // Start of Scan
    SOF0 = 0xFFC0, // Start of Frame (Baseline DCT)
    SOF1 = 0xFFC1, // Start of Frame (Extended Sequential DCT)
    SOF2 = 0xFFC2, // Start of Frame (Progressive DCT)
    DHT = 0xFFC4, // Define <PERSON>man Table
    DQT = 0xFFDB, // Define Quantization Table
    DRI = 0xFFDD, // Define Restart Interval
    APP0 = 0xFFE0, // Application Segment 0 (JFIF)
    APP1 = 0xFFE1, // Application Segment 1 (EXIF)
    COM = 0xFFFE  // Comment
}

/**
 * Parses a JPEG image buffer
 * @param buffer JPEG image buffer
 * @param resourceId Resource ID for error context
 * @param instanceId Instance ID for error context
 * @returns Parsed JPEG metadata
 */
export function parseJPEG(buffer: Buffer, resourceId: number, instanceId: string): JPEGMetadata {
    try {
        // Create a buffer reader
        const reader = new BufferReader(buffer);
        
        // Validate JPEG signature (FF D8 FF)
        if (buffer.length < 3 ||
            buffer[0] !== 0xFF ||
            buffer[1] !== 0xD8 ||
            buffer[2] !== 0xFF) {
            throw new Error('Invalid JPEG signature');
        }
        
        // Skip SOI marker
        reader.skip(2);
        
        // Initialize dimensions
        let width: number | undefined;
        let height: number | undefined;
        
        // Parse markers until we find SOF or reach the end
        while (reader.getPosition() < buffer.length - 1) {
            // Read marker
            const markerByte = reader.readUInt8('Marker Byte');
            if (markerByte !== 0xFF) {
                log.warn(`Invalid JPEG marker byte: ${markerByte?.toString(16)}`);
                break;
            }
            
            // Read marker type
            const markerType = reader.readUInt8('Marker Type');
            if (markerType === undefined) {
                break;
            }
            
            // Full marker is 0xFF followed by marker type
            const marker = 0xFF00 | markerType;
            
            // Check if this is a SOF marker (Start of Frame)
            if (marker === JPEGMarker.SOF0 || marker === JPEGMarker.SOF1 || marker === JPEGMarker.SOF2) {
                // Read segment length
                const segmentLength = reader.readUInt16BE('Segment Length');
                if (segmentLength === undefined || segmentLength < 7) {
                    log.warn(`Invalid SOF segment length: ${segmentLength}`);
                    break;
                }
                
                // Skip precision
                reader.skip(1);
                
                // Read dimensions
                height = reader.readUInt16BE('Height');
                width = reader.readUInt16BE('Width');
                
                // We found what we needed, break out of the loop
                break;
            } else if (marker === JPEGMarker.SOS) {
                // Start of Scan, we won't find dimensions after this
                break;
            } else if (marker === JPEGMarker.EOI) {
                // End of Image
                break;
            } else {
                // Skip other markers
                const segmentLength = reader.readUInt16BE('Segment Length');
                if (segmentLength === undefined || segmentLength < 2) {
                    log.warn(`Invalid segment length: ${segmentLength}`);
                    break;
                }
                
                // Skip segment data (length includes the 2 bytes of the length field itself)
                reader.skip(segmentLength - 2);
            }
        }
        
        // Create and return metadata
        return {
            format: ImageFormat.JPEG,
            width: width,
            height: height,
            hasAlpha: false // JPEG doesn't support alpha
        };
    } catch (error) {
        // Handle error and return minimal metadata
        const context = createImageErrorContext(resourceId, instanceId, 'parseJPEG');
        const result = handleImageError(error, context);
        
        return {
            format: ImageFormat.JPEG,
            width: undefined,
            height: undefined,
            hasAlpha: false // JPEG doesn't support alpha
        };
    }
}
