/**
 * Gameplay Impact Analyzer - Phase 2: Intelligent Conflict Detection
 * 
 * This analyzer determines the real gameplay impact of resource conflicts,
 * distinguishing between critical conflicts that break gameplay and harmless
 * overlaps that don't affect the game experience.
 * 
 * Based on analysis of official Sims 4 game logic and conflict patterns.
 */

import { OfficialResourceType, ResourceCategory } from '../../../types/resource/OfficialResourceTypes.js';
import { getResourceMetadata } from '../../../types/resource/ResourceMetadataRegistry.js';

/**
 * Conflict Severity Levels based on gameplay impact
 */
export enum ConflictSeverity {
  CRITICAL = 'CRITICAL',     // Breaks core gameplay, causes crashes
  HIGH = 'HIGH',             // Significant gameplay disruption
  MEDIUM = 'MEDIUM',         // Noticeable but manageable issues
  LOW = 'LOW',               // Minor cosmetic or edge case issues
  HARMLESS = 'HARMLESS'      // No actual gameplay impact
}

/**
 * Gameplay Impact Classification
 */
export enum GameplayImpact {
  GAME_BREAKING = 'GAME_BREAKING',           // Crashes, corruption
  FUNCTIONALITY_LOSS = 'FUNCTIONALITY_LOSS', // Features stop working
  BEHAVIOR_CHANGE = 'BEHAVIOR_CHANGE',       // Unexpected behavior
  VISUAL_GLITCH = 'VISUAL_GLITCH',           // Visual issues only
  PERFORMANCE_IMPACT = 'PERFORMANCE_IMPACT', // Performance degradation
  NO_IMPACT = 'NO_IMPACT'                    // No noticeable effect
}

/**
 * Conflict Analysis Result
 */
export interface ConflictAnalysisResult {
  severity: ConflictSeverity;
  impact: GameplayImpact;
  description: string;
  recommendation: string;
  affectedSystems: string[];
  isRealConflict: boolean;
  confidence: number; // 0-1 confidence score
}

/**
 * Resource Conflict Context
 */
export interface ResourceConflictContext {
  resourceType: OfficialResourceType;
  resourceCategory: ResourceCategory;
  resourceId: string;
  conflictingResources: Array<{
    resourceType: OfficialResourceType;
    resourceId: string;
    packageName: string;
  }>;
  metadata?: any;
}

/**
 * Gameplay Impact Analyzer
 * 
 * Uses game logic patterns discovered in our research to determine
 * the real impact of resource conflicts on gameplay.
 */
export class GameplayImpactAnalyzer {
  
  /**
   * Analyze the gameplay impact of a resource conflict
   */
  public analyzeConflict(context: ResourceConflictContext): ConflictAnalysisResult {
    // Determine base severity from resource type
    const baseSeverity = this.getBaseSeverityForResourceType(context.resourceType);
    
    // Analyze specific conflict patterns
    const conflictPattern = this.identifyConflictPattern(context);
    
    // Calculate final severity and impact
    const severity = this.calculateFinalSeverity(baseSeverity, conflictPattern, context);
    const impact = this.determineGameplayImpact(severity, context);
    
    // Generate description and recommendation
    const description = this.generateConflictDescription(context, severity, impact);
    const recommendation = this.generateRecommendation(context, severity, impact);
    
    // Identify affected game systems
    const affectedSystems = this.identifyAffectedSystems(context);
    
    // Determine if this is a real conflict vs. harmless overlap
    const isRealConflict = severity !== ConflictSeverity.HARMLESS;
    
    // Calculate confidence based on our analysis certainty
    const confidence = this.calculateConfidence(context, conflictPattern);
    
    return {
      severity,
      impact,
      description,
      recommendation,
      affectedSystems,
      isRealConflict,
      confidence
    };
  }
  
  /**
   * Get base severity for a resource type based on game importance
   */
  private getBaseSeverityForResourceType(resourceType: OfficialResourceType): ConflictSeverity {
    const metadata = getResourceMetadata(resourceType);
    const category = metadata?.category;
    
    switch (category) {
      case ResourceCategory.TRAIT:
      case ResourceCategory.BUFF:
      case ResourceCategory.INTERACTION:
        return ConflictSeverity.HIGH; // Core gameplay systems
        
      case ResourceCategory.OBJECT:
      case ResourceCategory.ANIMATION:
      case ResourceCategory.GAMEPLAY:
        return ConflictSeverity.MEDIUM; // Important but not critical
        
      case ResourceCategory.IMAGE:
      case ResourceCategory.TEXTURE:
      case ResourceCategory.UI:
        return ConflictSeverity.LOW; // Visual only
        
      case ResourceCategory.TUNING:
        return this.analyzeTuningResourceSeverity(resourceType);
        
      default:
        return ConflictSeverity.MEDIUM; // Default for unknown types
    }
  }
  
  /**
   * Analyze tuning resource severity based on specific type
   */
  private analyzeTuningResourceSeverity(resourceType: OfficialResourceType): ConflictSeverity {
    switch (resourceType) {
      case OfficialResourceType.TRAIT:
      case OfficialResourceType.BUFF:
      case OfficialResourceType.INTERACTION:
      case OfficialResourceType.CAREER:
      case OfficialResourceType.ASPIRATION:
        return ConflictSeverity.HIGH;
        
      case OfficialResourceType.OBJECT:
      case OfficialResourceType.ANIMATION:
      case OfficialResourceType.SITUATION:
        return ConflictSeverity.MEDIUM;
        
      default:
        return ConflictSeverity.LOW;
    }
  }
  
  /**
   * Identify specific conflict patterns
   */
  private identifyConflictPattern(context: ResourceConflictContext): string {
    const resourceType = context.resourceType;
    const conflictCount = context.conflictingResources.length;
    
    // Multiple mods modifying the same core resource
    if (conflictCount > 2 && this.isCoreGameplayResource(resourceType)) {
      return 'MULTIPLE_CORE_MODIFICATIONS';
    }
    
    // Trait conflicts (use game's conflicting_traits logic)
    if (resourceType === OfficialResourceType.TRAIT) {
      return 'TRAIT_CONFLICT';
    }
    
    // Buff conflicts (mood and behavior conflicts)
    if (resourceType === OfficialResourceType.BUFF) {
      return 'BUFF_CONFLICT';
    }
    
    // Service modifications
    if (resourceType === OfficialResourceType.SERVICE_NPC) {
      return 'SERVICE_MODIFICATION';
    }
    
    // Language variants (usually harmless)
    if (this.isLanguageVariant(context)) {
      return 'LANGUAGE_VARIANT';
    }
    
    // Debug resources (usually harmless)
    if (this.isDebugResource(context)) {
      return 'DEBUG_RESOURCE';
    }
    
    return 'STANDARD_CONFLICT';
  }
  
  /**
   * Calculate final severity based on patterns and context
   */
  private calculateFinalSeverity(
    baseSeverity: ConflictSeverity,
    pattern: string,
    context: ResourceConflictContext
  ): ConflictSeverity {
    switch (pattern) {
      case 'MULTIPLE_CORE_MODIFICATIONS':
        return ConflictSeverity.CRITICAL; // Multiple mods changing core systems
        
      case 'TRAIT_CONFLICT':
        return this.analyzeTraitConflictSeverity(context);
        
      case 'BUFF_CONFLICT':
        return this.analyzeBuffConflictSeverity(context);
        
      case 'SERVICE_MODIFICATION':
        return ConflictSeverity.HIGH; // Service changes are significant
        
      case 'LANGUAGE_VARIANT':
      case 'DEBUG_RESOURCE':
        return ConflictSeverity.HARMLESS; // Usually safe overlaps
        
      default:
        return baseSeverity; // Use base severity for standard conflicts
    }
  }
  
  /**
   * Analyze trait conflict severity using game logic
   */
  private analyzeTraitConflictSeverity(context: ResourceConflictContext): ConflictSeverity {
    // In the game, traits have conflicting_traits lists
    // Multiple mods modifying the same trait can break the conflict system
    if (context.conflictingResources.length > 1) {
      return ConflictSeverity.HIGH;
    }
    return ConflictSeverity.MEDIUM;
  }
  
  /**
   * Analyze buff conflict severity
   */
  private analyzeBuffConflictSeverity(context: ResourceConflictContext): ConflictSeverity {
    // Buffs affect mood and behavior systems
    // Multiple modifications can cause unpredictable behavior
    if (context.conflictingResources.length > 1) {
      return ConflictSeverity.HIGH;
    }
    return ConflictSeverity.MEDIUM;
  }
  
  /**
   * Determine gameplay impact from severity
   */
  private determineGameplayImpact(severity: ConflictSeverity, context: ResourceConflictContext): GameplayImpact {
    switch (severity) {
      case ConflictSeverity.CRITICAL:
        return GameplayImpact.GAME_BREAKING;
        
      case ConflictSeverity.HIGH:
        return this.isCoreGameplayResource(context.resourceType) 
          ? GameplayImpact.FUNCTIONALITY_LOSS 
          : GameplayImpact.BEHAVIOR_CHANGE;
          
      case ConflictSeverity.MEDIUM:
        return GameplayImpact.BEHAVIOR_CHANGE;
        
      case ConflictSeverity.LOW:
        return this.isVisualResource(context.resourceType)
          ? GameplayImpact.VISUAL_GLITCH
          : GameplayImpact.PERFORMANCE_IMPACT;
          
      case ConflictSeverity.HARMLESS:
        return GameplayImpact.NO_IMPACT;
    }
  }
  
  /**
   * Check if resource is core to gameplay
   */
  private isCoreGameplayResource(resourceType: OfficialResourceType): boolean {
    const coreTypes = [
      OfficialResourceType.TRAIT,
      OfficialResourceType.BUFF,
      OfficialResourceType.INTERACTION,
      OfficialResourceType.CAREER,
      OfficialResourceType.ASPIRATION,
      OfficialResourceType.SITUATION
    ];
    return coreTypes.includes(resourceType);
  }
  
  /**
   * Check if resource is visual-only
   */
  private isVisualResource(resourceType: OfficialResourceType): boolean {
    const metadata = getResourceMetadata(resourceType);
    const visualCategories = [
      ResourceCategory.IMAGE,
      ResourceCategory.TEXTURE,
      ResourceCategory.UI,
      ResourceCategory.EFFECT
    ];
    return metadata ? visualCategories.includes(metadata.category) : false;
  }
  
  /**
   * Check if conflict is between language variants
   */
  private isLanguageVariant(context: ResourceConflictContext): boolean {
    // Language variants often have similar resource IDs with language codes
    // This is a simplified check - could be enhanced with more sophisticated detection
    const resourceId = context.resourceId.toLowerCase();
    const languageCodes = ['eng', 'fra', 'ger', 'spa', 'ita', 'jpn', 'kor', 'chs', 'cht'];
    
    return languageCodes.some(lang => resourceId.includes(lang));
  }
  
  /**
   * Check if resource is debug/development only
   */
  private isDebugResource(context: ResourceConflictContext): boolean {
    const resourceId = context.resourceId.toLowerCase();
    const debugKeywords = ['debug', 'test', 'dev', 'temp', 'placeholder'];
    
    return debugKeywords.some(keyword => resourceId.includes(keyword));
  }
  
  /**
   * Generate human-readable conflict description
   */
  private generateConflictDescription(
    context: ResourceConflictContext,
    severity: ConflictSeverity,
    impact: GameplayImpact
  ): string {
    const resourceType = context.resourceType;
    const conflictCount = context.conflictingResources.length;
    
    const baseDescription = `${conflictCount} mods are modifying the same ${resourceType} resource`;
    
    switch (severity) {
      case ConflictSeverity.CRITICAL:
        return `${baseDescription}. This will likely cause game crashes or severe instability.`;
        
      case ConflictSeverity.HIGH:
        return `${baseDescription}. This may cause significant gameplay issues or broken features.`;
        
      case ConflictSeverity.MEDIUM:
        return `${baseDescription}. This may cause unexpected behavior or minor issues.`;
        
      case ConflictSeverity.LOW:
        return `${baseDescription}. This may cause minor visual glitches or performance issues.`;
        
      case ConflictSeverity.HARMLESS:
        return `${baseDescription}. This is likely a harmless overlap with no gameplay impact.`;
    }
  }
  
  /**
   * Generate recommendation for resolving conflict
   */
  private generateRecommendation(
    context: ResourceConflictContext,
    severity: ConflictSeverity,
    impact: GameplayImpact
  ): string {
    switch (severity) {
      case ConflictSeverity.CRITICAL:
        return 'URGENT: Remove all but one of the conflicting mods to prevent game crashes.';
        
      case ConflictSeverity.HIGH:
        return 'Recommended: Choose one mod to keep and remove the others, or look for compatibility patches.';
        
      case ConflictSeverity.MEDIUM:
        return 'Consider: Test gameplay with all mods active, remove if issues occur.';
        
      case ConflictSeverity.LOW:
        return 'Optional: Monitor for issues, remove if problems are noticed.';
        
      case ConflictSeverity.HARMLESS:
        return 'No action needed: This conflict is harmless and can be safely ignored.';
    }
  }
  
  /**
   * Identify affected game systems
   */
  private identifyAffectedSystems(context: ResourceConflictContext): string[] {
    const systems: string[] = [];
    const resourceType = context.resourceType;
    
    // Map resource types to affected game systems
    switch (resourceType) {
      case OfficialResourceType.TRAIT:
        systems.push('Personality System', 'Behavior System', 'Mood System');
        break;
        
      case OfficialResourceType.BUFF:
        systems.push('Mood System', 'Behavior System', 'Status Effects');
        break;
        
      case OfficialResourceType.INTERACTION:
        systems.push('Social System', 'Action System', 'Autonomy System');
        break;
        
      case OfficialResourceType.CAREER:
        systems.push('Career System', 'Skill System', 'Progression System');
        break;
        
      case OfficialResourceType.ASPIRATION:
        systems.push('Goal System', 'Reward System', 'Progression System');
        break;
        
      case OfficialResourceType.OBJECT:
        systems.push('Object System', 'Interaction System', 'Build Mode');
        break;
        
      default:
        systems.push('Unknown System');
    }
    
    return systems;
  }
  
  /**
   * Calculate confidence in our analysis
   */
  private calculateConfidence(context: ResourceConflictContext, pattern: string): number {
    let confidence = 0.7; // Base confidence
    
    // Higher confidence for well-understood patterns
    if (['TRAIT_CONFLICT', 'BUFF_CONFLICT', 'SERVICE_MODIFICATION'].includes(pattern)) {
      confidence += 0.2;
    }
    
    // Higher confidence for core gameplay resources
    if (this.isCoreGameplayResource(context.resourceType)) {
      confidence += 0.1;
    }
    
    // Lower confidence for unknown resource types
    if (context.resourceType === OfficialResourceType.UNKNOWN) {
      confidence -= 0.3;
    }
    
    return Math.min(1.0, Math.max(0.1, confidence));
  }
}
