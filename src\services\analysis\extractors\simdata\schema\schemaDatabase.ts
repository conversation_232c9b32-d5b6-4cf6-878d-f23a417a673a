/**
 * SimData Schema Database
 *
 * This class maintains a database of known SimData schemas and provides
 * functionality for schema analysis and relationship tracking.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../../databaseService.js';
import { SimDataSchema } from '../simDataTypes.js';
import { SchemaCompatibilityInfo, SchemaInheritanceInfo, SchemaPurposeInfo } from './schemaInterfaces.js';

const log = {
    info: (message: string) => console.log(`[SchemaDatabase] INFO: ${message}`),
    error: (message: string) => console.error(`[SchemaDatabase] ERROR: ${message}`),
    warn: (message: string) => console.warn(`[SchemaDatabase] WARN: ${message}`),
    debug: (message: string) => console.debug(`[SchemaDatabase] DEBUG: ${message}`)
};

export interface SchemaStats {
    totalSchemas: number;
    uniqueSchemaNames: number;
    schemasByCategory: Record<string, number>;
    mostCommonSchemas: Array<{ name: string; count: number }>;
    recentlyDiscovered: Array<{ name: string; firstSeen: Date }>;
}

export class SchemaDatabase {
    private static instance: SchemaDatabase;
    private databaseService: DatabaseService;
    private initialized: boolean = false;
    private schemaCache: Map<string, SimDataSchema> = new Map();
    private purposeCache: Map<string, SchemaPurposeInfo> = new Map();
    private relationshipCache: Map<string, Set<string>> = new Map();

    private constructor(databaseService: DatabaseService) {
        this.databaseService = databaseService;
    }

    /**
     * Get the singleton instance of the schema database
     */
    public static getInstance(databaseService: DatabaseService): SchemaDatabase {
        if (!SchemaDatabase.instance) {
            SchemaDatabase.instance = new SchemaDatabase(databaseService);
        }
        return SchemaDatabase.instance;
    }

    /**
     * Initialize the schema database
     */
    public async initialize(): Promise<void> {
        if (this.initialized) return;

        try {
            // Create the schemas table if it doesn't exist
            await this.databaseService.run(`
                CREATE TABLE IF NOT EXISTS simdata_schemas (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    schema_id INTEGER NOT NULL,
                    hash INTEGER NOT NULL,
                    version INTEGER NOT NULL,
                    flags INTEGER NOT NULL,
                    columns TEXT NOT NULL,
                    parent TEXT,
                    first_seen TEXT NOT NULL,
                    last_seen TEXT NOT NULL,
                    count INTEGER NOT NULL DEFAULT 1,
                    UNIQUE(name, schema_id, hash)
                )
            `);

            // Create the schema purposes table if it doesn't exist
            await this.databaseService.run(`
                CREATE TABLE IF NOT EXISTS simdata_schema_purposes (
                    schema_name TEXT PRIMARY KEY,
                    purpose TEXT NOT NULL,
                    gameplay_system TEXT NOT NULL,
                    confidence INTEGER NOT NULL,
                    key_columns TEXT NOT NULL,
                    critical_columns TEXT NOT NULL,
                    subcategory TEXT,
                    description TEXT,
                    examples TEXT,
                    common_mod_types TEXT
                )
            `);

            // Create the schema relationships table if it doesn't exist
            await this.databaseService.run(`
                CREATE TABLE IF NOT EXISTS simdata_schema_relationships (
                    schema_name TEXT NOT NULL,
                    related_schema TEXT NOT NULL,
                    relationship_type TEXT NOT NULL,
                    confidence INTEGER NOT NULL,
                    description TEXT,
                    PRIMARY KEY(schema_name, related_schema, relationship_type)
                )
            `);

            // Load schema purposes into cache
            const purposeRows = await this.databaseService.all('SELECT * FROM simdata_schema_purposes');
            for (const row of purposeRows) {
                this.purposeCache.set(row.schema_name, {
                    schemaName: row.schema_name,
                    purpose: row.purpose,
                    gameplaySystem: row.gameplay_system,
                    confidence: row.confidence,
                    keyColumns: JSON.parse(row.key_columns),
                    criticalColumns: JSON.parse(row.critical_columns),
                    subcategory: row.subcategory,
                    description: row.description,
                    examples: row.examples ? JSON.parse(row.examples) : [],
                    commonModTypes: row.common_mod_types ? JSON.parse(row.common_mod_types) : []
                });
            }

            // Load schema relationships into cache
            const relationshipRows = await this.databaseService.all('SELECT * FROM simdata_schema_relationships');
            for (const row of relationshipRows) {
                if (!this.relationshipCache.has(row.schema_name)) {
                    this.relationshipCache.set(row.schema_name, new Set());
                }
                this.relationshipCache.get(row.schema_name)?.add(row.related_schema);
            }

            this.initialized = true;
            log.info(`Initialized schema database with ${this.purposeCache.size} schema purposes and ${this.relationshipCache.size} schema relationships`);
        } catch (error) {
            log.error(`Error initializing schema database: ${error}`);
        }
    }

    /**
     * Register a schema in the database
     */
    public async registerSchema(schema: SimDataSchema): Promise<void> {
        if (!this.initialized) await this.initialize();

        try {
            const now = new Date();
            const schemaKey = `${schema.name}_${schema.schemaId}_${schema.hash}`;

            // Check if schema already exists
            const existingSchema = await this.databaseService.get(
                'SELECT * FROM simdata_schemas WHERE name = ? AND schema_id = ? AND hash = ?',
                [schema.name, schema.schemaId, schema.hash]
            );

            if (existingSchema) {
                // Update existing schema
                await this.databaseService.run(
                    'UPDATE simdata_schemas SET last_seen = ?, count = count + 1 WHERE id = ?',
                    [now.toISOString(), existingSchema.id]
                );
            } else {
                // Insert new schema
                await this.databaseService.run(
                    `INSERT INTO simdata_schemas
                     (name, schema_id, hash, version, flags, columns, parent, first_seen, last_seen, count)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        schema.name,
                        schema.schemaId,
                        schema.hash,
                        schema.version,
                        schema.flags,
                        JSON.stringify(schema.columns),
                        schema.parent || null,
                        now.toISOString(),
                        now.toISOString(),
                        1
                    ]
                );

                // Log discovery of new schema
                log.info(`Discovered new schema: ${schema.name} (ID: ${schema.schemaId}, Hash: ${schema.hash})`);

                // Add to cache
                this.schemaCache.set(schemaKey, schema);

                // Analyze schema purpose if not already known
                if (!this.purposeCache.has(schema.name)) {
                    const purpose = await this.analyzeSchemaPurpose(schema);
                    if (purpose) {
                        await this.registerSchemaPurpose(purpose);
                    }
                }

                // Analyze schema relationships
                await this.analyzeSchemaRelationships(schema);
            }
        } catch (error) {
            log.error(`Error registering schema ${schema.name}: ${error}`);
        }
    }

    /**
     * Register schema purpose information
     */
    public async registerSchemaPurpose(purposeInfo: SchemaPurposeInfo): Promise<void> {
        if (!this.initialized) await this.initialize();

        try {
            // Check if purpose already exists
            const existingPurpose = await this.databaseService.get(
                'SELECT * FROM simdata_schema_purposes WHERE schema_name = ?',
                [purposeInfo.schemaName]
            );

            if (existingPurpose) {
                // Only update if confidence is higher
                if (purposeInfo.confidence > existingPurpose.confidence) {
                    await this.databaseService.run(
                        `UPDATE simdata_schema_purposes
                         SET purpose = ?, gameplay_system = ?, confidence = ?,
                             key_columns = ?, critical_columns = ?, subcategory = ?,
                             description = ?, examples = ?, common_mod_types = ?
                         WHERE schema_name = ?`,
                        [
                            purposeInfo.purpose,
                            purposeInfo.gameplaySystem,
                            purposeInfo.confidence,
                            JSON.stringify(purposeInfo.keyColumns),
                            JSON.stringify(purposeInfo.criticalColumns),
                            purposeInfo.subcategory || null,
                            purposeInfo.description || null,
                            purposeInfo.examples ? JSON.stringify(purposeInfo.examples) : null,
                            purposeInfo.commonModTypes ? JSON.stringify(purposeInfo.commonModTypes) : null,
                            purposeInfo.schemaName
                        ]
                    );

                    // Update cache
                    this.purposeCache.set(purposeInfo.schemaName, purposeInfo);
                }
            } else {
                // Insert new purpose
                await this.databaseService.run(
                    `INSERT INTO simdata_schema_purposes
                     (schema_name, purpose, gameplay_system, confidence, key_columns, critical_columns,
                      subcategory, description, examples, common_mod_types)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        purposeInfo.schemaName,
                        purposeInfo.purpose,
                        purposeInfo.gameplaySystem,
                        purposeInfo.confidence,
                        JSON.stringify(purposeInfo.keyColumns),
                        JSON.stringify(purposeInfo.criticalColumns),
                        purposeInfo.subcategory || null,
                        purposeInfo.description || null,
                        purposeInfo.examples ? JSON.stringify(purposeInfo.examples) : null,
                        purposeInfo.commonModTypes ? JSON.stringify(purposeInfo.commonModTypes) : null
                    ]
                );

                // Add to cache
                this.purposeCache.set(purposeInfo.schemaName, purposeInfo);
            }
        } catch (error) {
            log.error(`Error registering schema purpose for ${purposeInfo.schemaName}: ${error}`);
        }
    }

    /**
     * Register a relationship between two schemas
     */
    public async registerSchemaRelationship(
        schemaName: string,
        relatedSchema: string,
        relationshipType: string,
        confidence: number,
        description?: string
    ): Promise<void> {
        if (!this.initialized) await this.initialize();

        try {
            // Check if relationship already exists
            const existingRelationship = await this.databaseService.get(
                'SELECT * FROM simdata_schema_relationships WHERE schema_name = ? AND related_schema = ? AND relationship_type = ?',
                [schemaName, relatedSchema, relationshipType]
            );

            if (existingRelationship) {
                // Only update if confidence is higher
                if (confidence > existingRelationship.confidence) {
                    await this.databaseService.run(
                        'UPDATE simdata_schema_relationships SET confidence = ?, description = ? WHERE schema_name = ? AND related_schema = ? AND relationship_type = ?',
                        [confidence, description || null, schemaName, relatedSchema, relationshipType]
                    );
                }
            } else {
                // Insert new relationship
                await this.databaseService.run(
                    'INSERT INTO simdata_schema_relationships (schema_name, related_schema, relationship_type, confidence, description) VALUES (?, ?, ?, ?, ?)',
                    [schemaName, relatedSchema, relationshipType, confidence, description || null]
                );

                // Update cache
                if (!this.relationshipCache.has(schemaName)) {
                    this.relationshipCache.set(schemaName, new Set());
                }
                this.relationshipCache.get(schemaName)?.add(relatedSchema);
            }
        } catch (error) {
            log.error(`Error registering schema relationship between ${schemaName} and ${relatedSchema}: ${error}`);
        }
    }

    /**
     * Get a schema by name
     */
    public async getSchemaByName(name: string): Promise<SimDataSchema | undefined> {
        if (!this.initialized) await this.initialize();

        try {
            // Check cache first
            for (const schema of this.schemaCache.values()) {
                if (schema.name === name) {
                    return schema;
                }
            }

            // If not in cache, query database
            const row = await this.databaseService.get(
                'SELECT * FROM simdata_schemas WHERE name = ? ORDER BY count DESC LIMIT 1',
                [name]
            );

            if (row) {
                const schema: SimDataSchema = {
                    name: row.name,
                    schemaId: row.schema_id,
                    hash: row.hash,
                    version: row.version,
                    flags: row.flags,
                    columns: JSON.parse(row.columns),
                    parent: row.parent || undefined
                };

                // Add to cache
                const schemaKey = `${schema.name}_${schema.schemaId}_${schema.hash}`;
                this.schemaCache.set(schemaKey, schema);

                return schema;
            }

            return undefined;
        } catch (error) {
            log.error(`Error getting schema by name ${name}: ${error}`);
            return undefined;
        }
    }

    /**
     * Get schema purpose information
     */
    public async getSchemaPurpose(schemaName: string): Promise<SchemaPurposeInfo | undefined> {
        if (!this.initialized) await this.initialize();

        // Check cache first
        if (this.purposeCache.has(schemaName)) {
            return this.purposeCache.get(schemaName);
        }

        try {
            // If not in cache, query database
            const row = await this.databaseService.get(
                'SELECT * FROM simdata_schema_purposes WHERE schema_name = ?',
                [schemaName]
            );

            if (row) {
                const purposeInfo: SchemaPurposeInfo = {
                    schemaName: row.schema_name,
                    purpose: row.purpose,
                    gameplaySystem: row.gameplay_system,
                    confidence: row.confidence,
                    keyColumns: JSON.parse(row.key_columns),
                    criticalColumns: JSON.parse(row.critical_columns),
                    subcategory: row.subcategory,
                    description: row.description,
                    examples: row.examples ? JSON.parse(row.examples) : [],
                    commonModTypes: row.common_mod_types ? JSON.parse(row.common_mod_types) : []
                };

                // Add to cache
                this.purposeCache.set(schemaName, purposeInfo);

                return purposeInfo;
            }

            return undefined;
        } catch (error) {
            log.error(`Error getting schema purpose for ${schemaName}: ${error}`);
            return undefined;
        }
    }

    /**
     * Get related schemas
     */
    public async getRelatedSchemas(schemaName: string): Promise<string[]> {
        if (!this.initialized) await this.initialize();

        // Check cache first
        if (this.relationshipCache.has(schemaName)) {
            return Array.from(this.relationshipCache.get(schemaName) || []);
        }

        try {
            // If not in cache, query database
            const rows = await this.databaseService.all(
                'SELECT related_schema FROM simdata_schema_relationships WHERE schema_name = ?',
                [schemaName]
            );

            const relatedSchemas = rows.map(row => row.related_schema);

            // Add to cache
            this.relationshipCache.set(schemaName, new Set(relatedSchemas));

            return relatedSchemas;
        } catch (error) {
            log.error(`Error getting related schemas for ${schemaName}: ${error}`);
            return [];
        }
    }

    /**
     * Check schema compatibility
     */
    public async checkSchemaCompatibility(schema1: SimDataSchema, schema2: SimDataSchema): Promise<SchemaCompatibilityInfo> {
        // Default compatibility info
        const compatibilityInfo: SchemaCompatibilityInfo = {
            isCompatible: false,
            compatibilityScore: 0,
            incompatibleColumns: [],
            missingColumns: [],
            extraColumns: [],
            typeMismatches: [],
            criticalIssues: false,
            gameplayImpact: 'none',
            conflictDescription: ''
        };

        // If schema names don't match, they're not compatible
        if (schema1.name !== schema2.name) {
            compatibilityInfo.conflictDescription = `Schema names don't match: ${schema1.name} vs ${schema2.name}`;
            return compatibilityInfo;
        }

        // Get column maps for both schemas
        const columns1: Map<string, { type: number, flags: number }> = new Map();
        const columns2: Map<string, { type: number, flags: number }> = new Map();

        schema1.columns.forEach(col => columns1.set(col.name, { type: col.type, flags: col.flags }));
        schema2.columns.forEach(col => columns2.set(col.name, { type: col.type, flags: col.flags }));

        // Check for missing columns in schema2
        for (const colName of columns1.keys()) {
            if (!columns2.has(colName)) {
                compatibilityInfo.missingColumns.push(colName);
            }
        }

        // Check for extra columns in schema2
        for (const colName of columns2.keys()) {
            if (!columns1.has(colName)) {
                compatibilityInfo.extraColumns.push(colName);
            }
        }

        // Check for type mismatches
        for (const colName of columns1.keys()) {
            if (columns2.has(colName)) {
                const col1 = columns1.get(colName)!;
                const col2 = columns2.get(colName)!;

                if (col1.type !== col2.type) {
                    compatibilityInfo.typeMismatches.push({
                        column: colName,
                        expectedType: col1.type,
                        actualType: col2.type
                    });
                    compatibilityInfo.incompatibleColumns.push(colName);
                }
            }
        }

        // Get schema purpose to determine critical columns
        const purpose = await this.getSchemaPurpose(schema1.name);
        const criticalColumns = purpose?.criticalColumns || [];

        // Check if any critical columns are affected
        compatibilityInfo.criticalIssues = compatibilityInfo.incompatibleColumns.some(col =>
            criticalColumns.includes(col)
        ) || compatibilityInfo.missingColumns.some(col =>
            criticalColumns.includes(col)
        );

        // Calculate compatibility score
        const totalColumns = columns1.size;
        const incompatibleCount = compatibilityInfo.incompatibleColumns.length;
        const missingCount = compatibilityInfo.missingColumns.length;
        const extraCount = compatibilityInfo.extraColumns.length;

        // Weight critical issues more heavily
        const criticalMultiplier = compatibilityInfo.criticalIssues ? 2 : 1;
        const problemCount = (incompatibleCount + missingCount) * criticalMultiplier + extraCount;

        // Calculate score (0-100)
        if (totalColumns === 0) {
            compatibilityInfo.compatibilityScore = 0;
        } else {
            const rawScore = 100 - (problemCount / totalColumns) * 100;
            compatibilityInfo.compatibilityScore = Math.max(0, Math.min(100, rawScore));
        }

        // Determine if schemas are compatible
        compatibilityInfo.isCompatible = compatibilityInfo.compatibilityScore >= 80 && !compatibilityInfo.criticalIssues;

        // Determine gameplay impact
        if (compatibilityInfo.criticalIssues) {
            compatibilityInfo.gameplayImpact = 'high';
        } else if (incompatibleCount > 0 || missingCount > 0) {
            compatibilityInfo.gameplayImpact = 'medium';
        } else if (extraCount > 0) {
            compatibilityInfo.gameplayImpact = 'low';
        } else {
            compatibilityInfo.gameplayImpact = 'none';
        }

        // Generate conflict description
        let description = '';
        if (incompatibleCount > 0) {
            description += `${incompatibleCount} column(s) with type mismatches. `;
        }
        if (missingCount > 0) {
            description += `${missingCount} column(s) missing. `;
        }
        if (extraCount > 0) {
            description += `${extraCount} extra column(s). `;
        }
        if (compatibilityInfo.criticalIssues) {
            description += 'Critical columns affected. ';
        }

        compatibilityInfo.conflictDescription = description.trim();

        return compatibilityInfo;
    }

    /**
     * Analyze schema inheritance
     */
    public async analyzeSchemaInheritance(schema: SimDataSchema): Promise<SchemaInheritanceInfo | undefined> {
        if (!schema.name.includes('_')) {
            return undefined;
        }

        const parts = schema.name.split('_');
        const potentialParent = parts[0];

        // Check if parent schema exists
        const parentSchema = await this.getSchemaByName(potentialParent);
        if (!parentSchema) {
            return undefined;
        }

        // Analyze inheritance
        const parentColumns = new Set(parentSchema.columns.map(col => col.name));
        const childColumns = new Set(schema.columns.map(col => col.name));

        const inheritedColumns: string[] = [];
        const addedColumns: string[] = [];

        // Find inherited columns
        for (const colName of childColumns) {
            if (parentColumns.has(colName)) {
                inheritedColumns.push(colName);
            } else {
                addedColumns.push(colName);
            }
        }

        return {
            parent: potentialParent,
            child: schema.name,
            inheritedColumns,
            addedColumns
        };
    }

    /**
     * Analyze schema purpose
     */
    private async analyzeSchemaPurpose(schema: SimDataSchema): Promise<SchemaPurposeInfo | undefined> {
        // This is a simplified version - in a real implementation, this would use
        // more sophisticated analysis based on column names, types, and patterns

        const schemaName = schema.name;
        let purpose = '';
        let gameplaySystem = '';
        let confidence = 50; // Default medium confidence
        const keyColumns: string[] = [];
        const criticalColumns: string[] = [];

        // Analyze schema name for clues
        if (schemaName.includes('Trait')) {
            purpose = 'Defines sim traits';
            gameplaySystem = 'Sim Traits';
            confidence = 80;
        } else if (schemaName.includes('Buff')) {
            purpose = 'Defines temporary buffs';
            gameplaySystem = 'Buffs System';
            confidence = 80;
        } else if (schemaName.includes('Skill')) {
            purpose = 'Defines sim skills';
            gameplaySystem = 'Skills System';
            confidence = 80;
        } else if (schemaName.includes('Object')) {
            purpose = 'Defines game objects';
            gameplaySystem = 'Objects';
            confidence = 70;
        } else if (schemaName.includes('Aspiration')) {
            purpose = 'Defines sim aspirations';
            gameplaySystem = 'Aspirations';
            confidence = 80;
        } else if (schemaName.includes('Career')) {
            purpose = 'Defines sim careers';
            gameplaySystem = 'Careers';
            confidence = 80;
        } else if (schemaName.includes('Recipe')) {
            purpose = 'Defines cooking recipes';
            gameplaySystem = 'Cooking';
            confidence = 80;
        } else if (schemaName.includes('Interaction')) {
            purpose = 'Defines sim interactions';
            gameplaySystem = 'Interactions';
            confidence = 80;
        } else if (schemaName.includes('Lot')) {
            purpose = 'Defines lot properties';
            gameplaySystem = 'Lots';
            confidence = 80;
        } else if (schemaName.includes('Mood')) {
            purpose = 'Defines sim moods';
            gameplaySystem = 'Emotions';
            confidence = 80;
        } else {
            // Generic purpose based on name
            purpose = `Defines ${schemaName.toLowerCase()} data`;
            gameplaySystem = 'Unknown';
            confidence = 30;
        }

        // Identify key columns based on column names
        for (const column of schema.columns) {
            if (column.name.toLowerCase().includes('id') ||
                column.name.toLowerCase() === 'key' ||
                column.name.toLowerCase() === 'instance_id') {
                keyColumns.push(column.name);
            }

            // Identify critical columns based on name patterns
            if (column.name.toLowerCase().includes('tuning') ||
                column.name.toLowerCase().includes('value') ||
                column.name.toLowerCase().includes('enabled') ||
                column.name.toLowerCase().includes('active')) {
                criticalColumns.push(column.name);
            }
        }

        return {
            schemaName,
            purpose,
            gameplaySystem,
            confidence,
            keyColumns,
            criticalColumns
        };
    }

    /**
     * Analyze schema relationships
     */
    private async analyzeSchemaRelationships(schema: SimDataSchema): Promise<void> {
        // Check for inheritance relationships
        const inheritanceInfo = await this.analyzeSchemaInheritance(schema);
        if (inheritanceInfo) {
            await this.registerSchemaRelationship(
                inheritanceInfo.child,
                inheritanceInfo.parent,
                'inherits_from',
                90,
                `Inherits ${inheritanceInfo.inheritedColumns.length} columns and adds ${inheritanceInfo.addedColumns.length} new columns`
            );
        }

        // Check for reference relationships based on column names
        for (const column of schema.columns) {
            if (column.type === 20) { // ResourceKey type
                // This column likely references another resource
                await this.registerSchemaRelationship(
                    schema.name,
                    'Unknown', // We don't know what it references yet
                    'references',
                    60,
                    `Contains ResourceKey reference in column ${column.name}`
                );
            } else if (column.name.endsWith('_ref') || column.name.endsWith('_key') || column.name.endsWith('_id')) {
                // This column likely references another schema
                const referencedName = column.name.replace(/_ref$|_key$|_id$/, '');

                // Check if the referenced schema exists
                const referencedSchema = await this.getSchemaByName(referencedName);
                if (referencedSchema) {
                    await this.registerSchemaRelationship(
                        schema.name,
                        referencedSchema.name,
                        'references',
                        70,
                        `References ${referencedSchema.name} via column ${column.name}`
                    );
                }
            }
        }
    }

    /**
     * Get schema statistics
     */
    public async getSchemaStats(): Promise<SchemaStats> {
        if (!this.initialized) await this.initialize();

        try {
            // Get total schemas
            const totalSchemasResult = await this.databaseService.get('SELECT COUNT(*) as count FROM simdata_schemas');
            const totalSchemas = totalSchemasResult?.count || 0;

            // Get unique schema names
            const uniqueNamesResult = await this.databaseService.get('SELECT COUNT(DISTINCT name) as count FROM simdata_schemas');
            const uniqueSchemaNames = uniqueNamesResult?.count || 0;

            // Get schemas by category
            const schemasByCategory: Record<string, number> = {};
            const purposeRows = await this.databaseService.all('SELECT gameplay_system, COUNT(*) as count FROM simdata_schema_purposes GROUP BY gameplay_system');

            for (const row of purposeRows) {
                schemasByCategory[row.gameplay_system] = row.count;
            }

            // Get most common schemas
            const commonSchemaRows = await this.databaseService.all('SELECT name, COUNT(*) as count FROM simdata_schemas GROUP BY name ORDER BY count DESC LIMIT 10');
            const mostCommonSchemas = commonSchemaRows.map(row => ({ name: row.name, count: row.count }));

            // Get recently discovered schemas
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const recentSchemaRows = await this.databaseService.all(
                'SELECT name, first_seen FROM simdata_schemas WHERE first_seen > ? ORDER BY first_seen DESC LIMIT 10',
                [thirtyDaysAgo.toISOString()]
            );

            const recentlyDiscovered = recentSchemaRows.map(row => ({
                name: row.name,
                firstSeen: new Date(row.first_seen)
            }));

            return {
                totalSchemas,
                uniqueSchemaNames,
                schemasByCategory,
                mostCommonSchemas,
                recentlyDiscovered
            };
        } catch (error) {
            log.error(`Error getting schema statistics: ${error}`);
            return {
                totalSchemas: 0,
                uniqueSchemaNames: 0,
                schemasByCategory: {},
                mostCommonSchemas: [],
                recentlyDiscovered: []
            };
        }
    }
}
