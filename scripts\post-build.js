const fs = require('fs');
const path = require('path');

const distMainProcessPath = path.resolve(__dirname, '../dist/main-process');
const electronFrontendPath = path.join(distMainProcessPath, 'frontend', 'electron');

const preloadJsPath = path.join(electronFrontendPath, 'preload.js');
const preloadCjsPath = path.join(electronFrontendPath, 'preload.cjs');
const preloadMapPath = path.join(electronFrontendPath, 'preload.js.map');
const preloadCjsMapPath = path.join(electronFrontendPath, 'preload.cjs.map');
const packageJsonPath = path.join(distMainProcessPath, 'package.json');

console.log('Running post-build steps...');

try {
  // Rename preload.js to preload.cjs
  if (fs.existsSync(preloadJsPath)) {
    fs.renameSync(preloadJsPath, preloadCjsPath);
    console.log(`Renamed ${preloadJsPath} to ${preloadCjsPath}`);
  } else {
    console.warn(`Warning: ${preloadJsPath} not found, skipping rename.`);
  }

  // Rename preload.js.map to preload.cjs.map
  if (fs.existsSync(preloadMapPath)) {
    fs.renameSync(preloadMapPath, preloadCjsMapPath);
    console.log(`Renamed ${preloadMapPath} to ${preloadCjsMapPath}`);
  } else {
    console.warn(`Warning: ${preloadMapPath} not found, skipping rename.`);
  }

  // Create package.json in dist/main-process
  const packageJsonContent = JSON.stringify({ type: 'commonjs' }, null, 2);
  fs.writeFileSync(packageJsonPath, packageJsonContent, 'utf8');
  console.log(`Created ${packageJsonPath} with content: ${packageJsonContent}`);

  console.log('Post-build steps completed successfully.');

} catch (error) {
  console.error('Error during post-build steps:', error);
  process.exit(1); // Exit with error code
}