import { ConflictInfo, ConflictDetectionConfig, ConflictDetectionResult } from '../../types/conflict/index.js';
import { ResourceInfo } from '../../types/resource/interfaces.js';
import { Logger } from '../../utils/logging/logger.js';
import { HybridConflictDetector } from './HybridConflictDetector.js';
import { EnhancedConflictDetector } from './detectors/EnhancedConflictDetector.js';
import { DatabaseService } from '../databaseService.js';

const logger = new Logger('ConflictDetectionService');

/**
 * Service for detecting conflicts between mods
 */
export class ConflictDetectionService {
    private static instance: ConflictDetectionService;
    private detector: EnhancedConflictDetector;
    private databaseService: DatabaseService;
    private initialized: boolean = false;

    /**
     * Create a new conflict detection service
     */
    private constructor() {
        // Get database service
        this.databaseService = DatabaseService.getInstance();

        // Create enhanced conflict detector
        this.detector = new EnhancedConflictDetector(
            this.databaseService,
            {
                // Default options
                similarityThreshold: 0.8,
                maxContentSize: 1048576, // 1 MB
                enableSignatureFiltering: true,
                enableMetadataFiltering: true,
                enableDeepComparison: true,
                enableLLMDetection: false, // Disabled by default
                llmConfidenceThreshold: 0.7
            }
        );

        logger.info('ConflictDetectionService created with EnhancedConflictDetector');
    }

    /**
     * Get the singleton instance of the conflict detection service
     * @returns The conflict detection service instance
     */
    public static getInstance(): ConflictDetectionService {
        if (!ConflictDetectionService.instance) {
            ConflictDetectionService.instance = new ConflictDetectionService();
        }
        return ConflictDetectionService.instance;
    }

    /**
     * Initialize the conflict detection service
     */
    public async initialize(): Promise<void> {
        if (this.initialized) return;

        logger.info('Initializing ConflictDetectionService');

        // Initialize the database service
        await this.databaseService.initialize();

        // Initialize the conflict detector
        await this.detector.initialize();

        this.initialized = true;
        logger.info('ConflictDetectionService initialized');
    }

    /**
     * Clean up resources used by the conflict detection service
     */
    public async cleanup(): Promise<void> {
        logger.info('Cleaning up ConflictDetectionService');

        // Clean up the database service
        await this.databaseService.cleanup();

        this.initialized = false;
        logger.info('ConflictDetectionService cleaned up');
    }

    /**
     * Detect conflicts between resources
     * @param resources List of resources to check for conflicts
     * @param config Optional configuration for conflict detection
     * @returns Result of conflict detection
     */
    public async detectConflicts(
        resources: ResourceInfo[],
        config?: Partial<ConflictDetectionConfig>
    ): Promise<ConflictDetectionResult> {
        if (!this.initialized) {
            await this.initialize();
        }

        logger.info(`Detecting conflicts between ${resources.length} resources`);

        // Update detector configuration if provided
        if (config) {
            // Create a new enhanced conflict detector with updated configuration
            this.detector = new EnhancedConflictDetector(
                this.databaseService,
                {
                    // Default options (tuned to reduce false positives)
                    similarityThreshold: 0.85,
                    maxContentSize: 1048576, // 1 MB
                    enableSignatureFiltering: true,
                    enableMetadataFiltering: true,
                    enableDeepComparison: true,
                    // Use configuration options if provided
                    enableLLMDetection: config.useLlm ?? false,
                    llmConfidenceThreshold: 0.7
                }
            );

            // Initialize the new detector
            await this.detector.initialize();
        }

        // Get dependencies for all resources
        const dependencies = await this.getDependencies(resources);

        // Detect conflicts
        const result = await this.detector.detectConflicts(resources, dependencies);

        logger.info(`Detected ${result.conflicts.length} conflicts between ${result.resourcesCompared} resource pairs`);

        return result;
    }

    /**
     * Detect conflicts between two packages
     * @param package1 First package
     * @param package2 Second package
     * @param config Optional configuration for conflict detection
     * @returns List of conflicts
     */
    public async detectPackageConflicts(
        package1: { name: string; resources: ResourceInfo[] },
        package2: { name: string; resources: ResourceInfo[] },
        config?: Partial<ConflictDetectionConfig>
    ): Promise<ConflictInfo[]> {
        if (!this.initialized) {
            await this.initialize();
        }

        logger.info(`Detecting conflicts between packages ${package1.name} and ${package2.name}`);

        // Combine resources from both packages
        const allResources = [...package1.resources, ...package2.resources];

        // Add package information to resources
        package1.resources.forEach(resource => {
            if (!resource.metadata) resource.metadata = {};
            resource.metadata.packageName = package1.name;
        });

        package2.resources.forEach(resource => {
            if (!resource.metadata) resource.metadata = {};
            resource.metadata.packageName = package2.name;
        });

        // Detect conflicts
        const result = await this.detectConflicts(allResources, config);

        // Filter conflicts to only include those between the two packages
        const filteredConflicts = result.conflicts.filter(conflict => {
            // Check if the conflict involves resources from both packages
            const package1Resources = conflict.affectedResources.filter(key =>
                package1.resources.some(r =>
                    r.key.type === key.type &&
                    r.key.group === key.group &&
                    r.key.instance === key.instance
                )
            );

            const package2Resources = conflict.affectedResources.filter(key =>
                package2.resources.some(r =>
                    r.key.type === key.type &&
                    r.key.group === key.group &&
                    r.key.instance === key.instance
                )
            );

            return package1Resources.length > 0 && package2Resources.length > 0;
        });

        logger.info(`Detected ${filteredConflicts.length} conflicts between packages`);

        return filteredConflicts;
    }

    /**
     * Get dependencies for resources
     * @param resources List of resources
     * @returns Map of resource IDs to their dependencies
     */
    private async getDependencies(resources: ResourceInfo[]): Promise<Map<number, any[]>> {
        const dependencies = new Map<number, any[]>();

        // Get resource IDs
        const resourceIds = resources
            .filter(r => r.id !== undefined)
            .map(r => r.id as number);

        if (resourceIds.length === 0) {
            return dependencies;
        }

        try {
            // Get dependencies from the database
            const deps = await this.databaseService.getDependencies(resourceIds);

            // Organize dependencies by resource ID
            deps.forEach(dep => {
                if (!dependencies.has(dep.resourceId)) {
                    dependencies.set(dep.resourceId, []);
                }

                dependencies.get(dep.resourceId)?.push({
                    targetType: dep.targetType,
                    targetGroup: dep.targetGroup,
                    targetInstance: dep.targetInstance
                });
            });

            logger.debug(`Retrieved dependencies for ${dependencies.size} resources`);
        } catch (error: any) {
            logger.error(`Error getting dependencies: ${error.message || error}`);
        }

        return dependencies;
    }

    /**
     * Update the conflict detection configuration
     * @param config New configuration
     */
    public async updateConfig(config: Partial<ConflictDetectionConfig>): Promise<void> {
        // Create a new enhanced conflict detector with updated configuration
        this.detector = new EnhancedConflictDetector(
            this.databaseService,
            {
                // Default options
                similarityThreshold: 0.8,
                maxContentSize: 1048576, // 1 MB
                enableSignatureFiltering: true,
                enableMetadataFiltering: true,
                enableDeepComparison: true,
                // Use configuration options if provided
                enableLLMDetection: config.useLlm ?? false,
                llmConfidenceThreshold: 0.7
            }
        );

        // Initialize the new detector
        await this.detector.initialize();

        logger.info(`Updated conflict detection configuration: ${JSON.stringify(config)}`);
    }

    /**
     * Save detected conflicts to the database
     * @param conflicts List of conflicts to save
     */
    public async saveConflicts(conflicts: ConflictInfo[]): Promise<void> {
        if (!this.initialized) {
            await this.initialize();
        }

        logger.info(`Saving ${conflicts.length} conflicts to the database`);

        try {
            await this.databaseService.saveConflicts(conflicts);
            logger.info(`Saved ${conflicts.length} conflicts to the database`);
        } catch (error: any) {
            logger.error(`Error saving conflicts: ${error.message || error}`);
        }
    }

    /**
     * Get conflicts from the database
     * @param limit Maximum number of conflicts to return
     * @param offset Offset for pagination
     * @returns List of conflicts
     */
    public async getConflicts(limit: number = 100, offset: number = 0): Promise<ConflictInfo[]> {
        if (!this.initialized) {
            await this.initialize();
        }

        logger.info(`Getting conflicts from the database (limit: ${limit}, offset: ${offset})`);

        try {
            const conflicts = await this.databaseService.getConflicts(limit, offset);
            logger.info(`Retrieved ${conflicts.length} conflicts from the database`);
            return conflicts;
        } catch (error: any) {
            logger.error(`Error getting conflicts: ${error.message || error}`);
            return [];
        }
    }
}
