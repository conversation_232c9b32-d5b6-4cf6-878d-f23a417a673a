import { Logger } from '../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../types/database.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/index.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { DatabaseService } from '../../databaseService.js';
import { resourceTypeRegistry } from '../../../utils/resource/resourceTypeRegistry.js';
import { ConflictDetectorBase, ConflictDetectionOptionsBase } from './ConflictDetectorBase.js';

/**
 * Options for TGI conflict detection
 */
export interface TGIConflictDetectionOptions extends ConflictDetectionOptionsBase {
    /**
     * Whether to detect exact TGI matches (same Type, Group, and Instance)
     * Default: true
     */
    detectExactMatches?: boolean;

    /**
     * Whether to detect partial TGI matches (same Type and Instance, different Group)
     * Default: true
     */
    detectPartialMatches?: boolean;

    /**
     * Whether to detect group conflicts (same Type and Group, different Instance)
     * Default: false
     */
    detectGroupConflicts?: boolean;

    /**
     * Whether to detect instance conflicts (same Type and Instance, different Group)
     * Default: true
     */
    detectInstanceConflicts?: boolean;
}

/**
 * Detector for TGI (Type-Group-Instance) conflicts between resources
 */
export class TGIConflictDetector extends ConflictDetectorBase<TGIConflictDetectionOptions> {
    /**
     * Create a new TGI conflict detector
     * @param databaseService Database service instance
     * @param options Options for TGI conflict detection
     * @param logger Optional logger instance
     */
    constructor(
        databaseService: DatabaseService,
        options: TGIConflictDetectionOptions = {},
        logger?: Logger
    ) {
        super(databaseService, {
            enabled: options.enabled !== false,
            excludeTypes: options.excludeTypes || [],
            includeTypes: options.includeTypes || [],
            detectExactMatches: options.detectExactMatches !== false,
            detectPartialMatches: options.detectPartialMatches !== false,
            detectGroupConflicts: options.detectGroupConflicts || false,
            detectInstanceConflicts: options.detectInstanceConflicts !== false
        }, logger || new Logger('TGIConflictDetector'));

        this.logger.debug(`TGIConflictDetector initialized with options: ${JSON.stringify(this.options)}`);
    }

    /**
     * Detect TGI conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    detectConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo | null {
        // Skip if either resource is excluded by type
        if (this.shouldSkipResource(resource1) || this.shouldSkipResource(resource2)) {
            return null;
        }

        // Check for exact TGI match
        if (this.options.detectExactMatches && this.isExactMatch(resource1, resource2)) {
            return this.createExactMatchConflict(resource1, resource2);
        }

        // Check for partial TGI match (same Type and Instance, different Group)
        if (this.options.detectPartialMatches && this.isPartialMatch(resource1, resource2)) {
            return this.createPartialMatchConflict(resource1, resource2);
        }

        // Check for group conflict (same Type and Group, different Instance)
        if (this.options.detectGroupConflicts && this.isGroupConflict(resource1, resource2)) {
            return this.createGroupConflict(resource1, resource2);
        }

        // Check for instance conflict (same Type and Instance, different Group)
        if (this.options.detectInstanceConflicts && this.isInstanceConflict(resource1, resource2)) {
            return this.createInstanceConflict(resource1, resource2);
        }

        return null;
    }

    /**
     * Detect conflicts between two resources and return as an array
     * This method is used to match the signature expected by ConflictDetector
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    async detectConflicts(resource1: ResourceInfo, resource2: ResourceInfo): Promise<ConflictInfo[]> {
        const conflict = this.detectConflict(resource1, resource2);
        return conflict ? [conflict] : [];
    }

    // Using shouldSkipResource from the base class

    /**
     * Check if two resources have an exact TGI match (same Type, Group, and Instance)
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns True if the resources have an exact TGI match, false otherwise
     */
    private isExactMatch(resource1: ResourceInfo, resource2: ResourceInfo): boolean {
        return (
            resource1.type === resource2.type &&
            resource1.group === resource2.group &&
            resource1.instance === resource2.instance
        );
    }

    /**
     * Check if two resources have a partial TGI match (same Type and Instance, different Group)
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns True if the resources have a partial TGI match, false otherwise
     */
    private isPartialMatch(resource1: ResourceInfo, resource2: ResourceInfo): boolean {
        return (
            resource1.type === resource2.type &&
            resource1.instance === resource2.instance &&
            resource1.group !== resource2.group
        );
    }

    /**
     * Check if two resources have a group conflict (same Type and Group, different Instance)
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns True if the resources have a group conflict, false otherwise
     */
    private isGroupConflict(resource1: ResourceInfo, resource2: ResourceInfo): boolean {
        return (
            resource1.type === resource2.type &&
            resource1.group === resource2.group &&
            resource1.instance !== resource2.instance
        );
    }

    /**
     * Check if two resources have an instance conflict (same Type and Instance, different Group)
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns True if the resources have an instance conflict, false otherwise
     */
    private isInstanceConflict(resource1: ResourceInfo, resource2: ResourceInfo): boolean {
        return (
            resource1.type === resource2.type &&
            resource1.instance === resource2.instance &&
            resource1.group !== resource2.group
        );
    }

    /**
     * Create a conflict info object for an exact TGI match
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns ConflictInfo object
     */
    private createExactMatchConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.type);
        const resourceTypeName = resourceTypeInfo?.name || `0x${resource1.type.toString(16).toUpperCase()}`;
        const severity = this.determineSeverity(resource1.type);

        return {
            id: `tgi-exact-${resource1.type}-${resource1.group}-${resource1.instance}`,
            type: ConflictType.RESOURCE,
            severity,
            description: `Exact TGI conflict for ${resourceTypeName} resource`,
            affectedResources: [
                this.createResourceKey(resource1),
                this.createResourceKey(resource2)
            ],
            timestamp: Date.now(),
            recommendations: this.getRecommendations(resource1.type, 'exact'),
            confidence: 1.0
        };
    }

    /**
     * Create a conflict info object for a partial TGI match
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns ConflictInfo object
     */
    private createPartialMatchConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.type);
        const resourceTypeName = resourceTypeInfo?.name || `0x${resource1.type.toString(16).toUpperCase()}`;
        const severity = this.determineSeverity(resource1.type, 'partial');

        return {
            id: `tgi-partial-${resource1.type}-${resource1.instance}`,
            type: ConflictType.RESOURCE,
            severity,
            description: `Partial TGI conflict for ${resourceTypeName} resource (same Type and Instance, different Group)`,
            affectedResources: [
                this.createResourceKey(resource1),
                this.createResourceKey(resource2)
            ],
            timestamp: Date.now(),
            recommendations: this.getRecommendations(resource1.type, 'partial'),
            confidence: 0.9
        };
    }

    /**
     * Create a conflict info object for a group conflict
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns ConflictInfo object
     */
    private createGroupConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.type);
        const resourceTypeName = resourceTypeInfo?.name || `0x${resource1.type.toString(16).toUpperCase()}`;
        const severity = this.determineSeverity(resource1.type, 'group');

        return {
            id: `tgi-group-${resource1.type}-${resource1.group}`,
            type: ConflictType.RESOURCE,
            severity,
            description: `Group conflict for ${resourceTypeName} resource (same Type and Group, different Instance)`,
            affectedResources: [
                this.createResourceKey(resource1),
                this.createResourceKey(resource2)
            ],
            timestamp: Date.now(),
            recommendations: this.getRecommendations(resource1.type, 'group'),
            confidence: 0.7
        };
    }

    /**
     * Create a conflict info object for an instance conflict
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns ConflictInfo object
     */
    private createInstanceConflict(resource1: ResourceInfo, resource2: ResourceInfo): ConflictInfo {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resource1.type);
        const resourceTypeName = resourceTypeInfo?.name || `0x${resource1.type.toString(16).toUpperCase()}`;
        const severity = this.determineSeverity(resource1.type, 'instance');

        return {
            id: `tgi-instance-${resource1.type}-${resource1.instance}`,
            type: ConflictType.RESOURCE,
            severity,
            description: `Instance conflict for ${resourceTypeName} resource (same Type and Instance, different Group)`,
            affectedResources: [
                this.createResourceKey(resource1),
                this.createResourceKey(resource2)
            ],
            timestamp: Date.now(),
            recommendations: this.getRecommendations(resource1.type, 'instance'),
            confidence: 0.8
        };
    }

    /**
     * Create a ResourceKey object from a ResourceInfo object
     * @param resource The resource info
     * @returns ResourceKey object
     */
    private createResourceKey(resource: ResourceInfo): ResourceKey {
        return {
            type: resource.type,
            group: resource.group,
            instance: resource.instance
        };
    }

    /**
     * Determine the severity of a conflict based on the resource type
     * @param resourceType The resource type
     * @param conflictType The type of conflict (exact, partial, group, instance)
     * @returns The conflict severity
     */
    private determineSeverity(resourceType: number, conflictType: 'exact' | 'partial' | 'group' | 'instance' = 'exact'): ConflictSeverity {
        // Critical resource types (conflicts always critical)
        const criticalTypes = [
            0x220557DA, // Tuning XML
            0x545AC67A, // SimData
            0x03B33DDF, // OBJD (Object Definition)
            0x0166038C, // STBL (String Table)
            0x00B2D882, // TXTR (Texture)
            0xE882D22F  // THUM (Thumbnail)
        ];

        // High severity resource types
        const highSeverityTypes = [
            0x0C772E27, // MODL (Model)
            0x736884F1, // MLOD (Model LOD)
            0x01661233, // CASP (CAS Part)
            0x319E4F1D, // BGEO (Bone Geometry)
            0x00AE6C67, // BOND (Bone Data)
            0x02D5DF13, // GEOM (Geometry)
            0x0333406C, // SIMO (Sim Outfit)
            0x025ED6F4  // MTST (Material Set)
        ];

        // Medium severity resource types
        const mediumSeverityTypes = [
            0x0A5DC6B9, // VFX_MODIFIER
            0x0A5DC6BA, // VFX_STATE
            0xEA5118B0, // VISUAL_EFFECT
            0x1B192049, // NEW_VISUAL_EFFECT
            0x1B19204A, // VFX_MAPPING_TABLE
            0x03B4C61D, // LGHT (Light)
            0xD3044521, // SLOT (Slot)
            0x5B282D45, // TRNG (Terrain Geometry)
            0x0C1FE246, // TRNP (Terrain Paint)
            0x3C1AF1F2, // OBJC (Object Catalog)
            0xBA856C78  // MODL (Modular Part)
        ];

        // For exact matches, use the resource type to determine severity
        if (conflictType === 'exact') {
            if (criticalTypes.includes(resourceType)) {
                return ConflictSeverity.CRITICAL;
            } else if (highSeverityTypes.includes(resourceType)) {
                return ConflictSeverity.HIGH;
            } else if (mediumSeverityTypes.includes(resourceType)) {
                return ConflictSeverity.MEDIUM;
            } else {
                return ConflictSeverity.LOW;
            }
        }

        // For partial matches, reduce severity by one level
        if (conflictType === 'partial' || conflictType === 'instance') {
            if (criticalTypes.includes(resourceType)) {
                return ConflictSeverity.HIGH;
            } else if (highSeverityTypes.includes(resourceType)) {
                return ConflictSeverity.MEDIUM;
            } else {
                return ConflictSeverity.LOW;
            }
        }

        // For group conflicts, reduce severity by two levels
        if (conflictType === 'group') {
            if (criticalTypes.includes(resourceType)) {
                return ConflictSeverity.MEDIUM;
            } else {
                return ConflictSeverity.LOW;
            }
        }

        // Default to LOW severity
        return ConflictSeverity.LOW;
    }

    /**
     * Get recommendations for resolving a conflict
     * @param resourceType The resource type
     * @param conflictType The type of conflict (exact, partial, group, instance)
     * @returns Array of recommendations
     */
    private getRecommendations(resourceType: number, conflictType: 'exact' | 'partial' | 'group' | 'instance'): string[] {
        const resourceTypeInfo = resourceTypeRegistry.getInfo(resourceType);
        const resourceTypeName = resourceTypeInfo?.name || `0x${resourceType.toString(16).toUpperCase()}`;

        const baseRecommendations = [
            `Check if both mods are compatible with each other`,
            `Load mods in the correct order to ensure proper overrides`,
            `Consider using only one of the conflicting mods`
        ];

        // Add specific recommendations based on resource type and conflict type
        if (resourceType === 0x220557DA) { // Tuning XML
            return [
                `Tuning XML conflicts can cause gameplay issues`,
                `Use Sims 4 Studio or XML Compare to view differences`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider merging the changes or choosing one version`
            ];
        } else if (resourceType === 0x545AC67A) { // SimData
            return [
                `SimData conflicts can cause gameplay issues`,
                `Check if both mods modify the same game object or functionality`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider merging the changes or choosing one version`
            ];
        } else if (resourceType === 0x03B33DDF) { // OBJD (Object Definition)
            return [
                `Object Definition conflicts can cause objects to behave incorrectly`,
                `Check if both mods modify the same object`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider using only one of the conflicting mods`
            ];
        } else if (resourceType === 0x0166038C) { // STBL (String Table)
            return [
                `String Table conflicts can cause text issues in-game`,
                `Check if both mods modify the same strings`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider merging the string changes or choosing one version`
            ];
        } else if (resourceType === 0x00B2D882) { // TXTR (Texture)
            return [
                `Texture conflicts can cause visual issues`,
                `Check if both mods modify the same texture`,
                `Load conflicting mods in the correct order if one should override the other`,
                `Consider using only one of the conflicting texture mods`
            ];
        }

        // Default recommendations based on conflict type
        if (conflictType === 'exact') {
            return [
                `Exact TGI conflict for ${resourceTypeName} resource`,
                ...baseRecommendations
            ];
        } else if (conflictType === 'partial' || conflictType === 'instance') {
            return [
                `Partial TGI conflict for ${resourceTypeName} resource`,
                `This may be intentional if mods are designed to work together`,
                ...baseRecommendations
            ];
        } else if (conflictType === 'group') {
            return [
                `Group conflict for ${resourceTypeName} resource`,
                `This is usually not a problem but may indicate related resources`,
                `Check if both mods modify related aspects of the same game feature`
            ];
        }

        return baseRecommendations;
    }
}
