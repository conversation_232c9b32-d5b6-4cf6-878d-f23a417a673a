/**
 * Resource Pool Manager
 * 
 * This module provides a pool for managing resource-intensive operations like file reading,
 * ensuring that only a limited number of operations are performed concurrently to prevent
 * memory leaks and resource exhaustion.
 */

import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import { promises as fs } from 'fs';
import path from 'path';

// Create a logger for this module
const logger = new Logger('ResourcePoolManager');

// Default pool configuration
const DEFAULT_CONFIG = {
    maxConcurrent: 5, // Maximum number of concurrent operations
    timeout: 60000,   // Timeout for operations in milliseconds (1 minute)
    retries: 3,       // Number of retries for failed operations
};

// Pool statistics
interface PoolStats {
    totalOperations: number;
    completedOperations: number;
    failedOperations: number;
    currentConcurrent: number;
    peakConcurrent: number;
    waitingOperations: number;
}

/**
 * Resource Pool Manager class
 * Manages a pool of resource-intensive operations, ensuring only a limited
 * number are performed concurrently.
 */
export class ResourcePoolManager extends EventEmitter {
    private config: typeof DEFAULT_CONFIG;
    private active: Set<string> = new Set();
    private queue: Array<{
        id: string;
        task: () => Promise<any>;
        resolve: (value: any) => void;
        reject: (reason: any) => void;
        retries: number;
    }> = [];
    private stats: PoolStats = {
        totalOperations: 0,
        completedOperations: 0,
        failedOperations: 0,
        currentConcurrent: 0,
        peakConcurrent: 0,
        waitingOperations: 0,
    };
    private isProcessing: boolean = false;

    /**
     * Create a new ResourcePoolManager
     * @param config Optional configuration
     */
    constructor(config: Partial<typeof DEFAULT_CONFIG> = {}) {
        super();
        this.setMaxListeners(100); // Set high max listeners for the pool
        this.config = { ...DEFAULT_CONFIG, ...config };
        logger.info(`Resource pool manager created with max concurrent: ${this.config.maxConcurrent}`);
    }

    /**
     * Submit a task to the pool
     * @param id Unique identifier for the task
     * @param task The task function to execute
     * @returns Promise that resolves with the task result
     */
    public submit<T>(id: string, task: () => Promise<T>): Promise<T> {
        this.stats.totalOperations++;
        this.stats.waitingOperations++;

        return new Promise<T>((resolve, reject) => {
            this.queue.push({
                id,
                task,
                resolve: resolve as (value: any) => void,
                reject,
                retries: this.config.retries,
            });

            this.processQueue();
        });
    }

    /**
     * Read a file as a buffer through the pool
     * @param filePath Path to the file
     * @returns Promise resolving to the file buffer
     */
    public async readFile(filePath: string): Promise<Buffer> {
        const fileName = path.basename(filePath);
        return this.submit(`read_${fileName}_${Date.now()}`, async () => {
            try {
                return await fs.readFile(filePath);
            } catch (error) {
                logger.error(`Error reading file ${filePath}:`, error);
                throw error;
            }
        });
    }

    /**
     * Get current pool statistics
     * @returns Current pool statistics
     */
    public getStats(): PoolStats {
        return { ...this.stats };
    }

    /**
     * Process the queue of pending tasks
     * @private
     */
    private async processQueue(): Promise<void> {
        if (this.isProcessing) return;
        this.isProcessing = true;

        try {
            while (this.queue.length > 0 && this.active.size < this.config.maxConcurrent) {
                const item = this.queue.shift()!;
                this.stats.waitingOperations--;
                this.active.add(item.id);
                this.stats.currentConcurrent = this.active.size;
                this.stats.peakConcurrent = Math.max(this.stats.peakConcurrent, this.active.size);

                // Execute the task with timeout
                this.executeTask(item);
            }
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Execute a task with timeout
     * @param item The task item to execute
     * @private
     */
    private async executeTask(item: {
        id: string;
        task: () => Promise<any>;
        resolve: (value: any) => void;
        reject: (reason: any) => void;
        retries: number;
    }): Promise<void> {
        const { id, task, resolve, reject, retries } = item;

        // Create a timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Task ${id} timed out after ${this.config.timeout}ms`));
                clearTimeout(timeoutId);
            }, this.config.timeout);
        });

        try {
            // Race the task against the timeout
            const result = await Promise.race([task(), timeoutPromise]);
            this.stats.completedOperations++;
            resolve(result);
        } catch (error) {
            if (retries > 0) {
                logger.warn(`Task ${id} failed, retrying (${retries} retries left):`, error);
                this.queue.unshift({
                    id,
                    task,
                    resolve,
                    reject,
                    retries: retries - 1,
                });
                this.stats.waitingOperations++;
            } else {
                logger.error(`Task ${id} failed after all retries:`, error);
                this.stats.failedOperations++;
                reject(error);
            }
        } finally {
            this.active.delete(id);
            this.stats.currentConcurrent = this.active.size;
            this.emit('taskComplete', id);
            
            // Continue processing the queue
            setImmediate(() => this.processQueue());
        }
    }

    /**
     * Wait for all tasks to complete
     * @returns Promise that resolves when all tasks are complete
     */
    public async waitForAll(): Promise<void> {
        if (this.active.size === 0 && this.queue.length === 0) {
            return;
        }

        return new Promise<void>((resolve) => {
            const checkComplete = () => {
                if (this.active.size === 0 && this.queue.length === 0) {
                    this.removeListener('taskComplete', checkComplete);
                    resolve();
                }
            };

            this.on('taskComplete', checkComplete);
            checkComplete(); // Check immediately in case already complete
        });
    }

    /**
     * Reset the pool statistics
     */
    public resetStats(): void {
        this.stats = {
            totalOperations: 0,
            completedOperations: 0,
            failedOperations: 0,
            currentConcurrent: this.active.size,
            peakConcurrent: this.active.size,
            waitingOperations: this.queue.length,
        };
    }
}

// Create a singleton instance for global use
export const resourcePool = new ResourcePoolManager();

// Export the singleton instance as default
export default resourcePool;
