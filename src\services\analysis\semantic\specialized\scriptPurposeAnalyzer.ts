/**
 * Script Purpose Analyzer
 * 
 * Specialized analyzer for Python script resources to determine their purpose
 * and semantic meaning with high accuracy.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { ResourceKey } from '../../../../types/resource/interfaces.js';
import { ResourcePurposeType } from '../interfaces/resourcePurpose.js';
import { injectable, singleton } from '../../../di/decorators.js';
import * as ResourceTypes from '../../../../constants/resourceTypes.js';

/**
 * Script purpose analysis result
 */
export interface ScriptPurposeAnalysisResult {
    /**
     * Script type
     */
    scriptType: string;
    
    /**
     * Module name
     */
    moduleName: string;
    
    /**
     * Resource purpose type
     */
    purposeType: ResourcePurposeType;
    
    /**
     * Script purpose
     */
    scriptPurpose: string;
    
    /**
     * Confidence score (0-100)
     */
    confidence: number;
    
    /**
     * Related gameplay system
     */
    gameplaySystem?: string;
    
    /**
     * Has injections?
     */
    hasInjections: boolean;
    
    /**
     * Has commands?
     */
    hasCommands: boolean;
    
    /**
     * Key imports in the script
     */
    keyImports: string[];
    
    /**
     * Key classes in the script
     */
    keyClasses: string[];
    
    /**
     * Key functions in the script
     */
    keyFunctions: string[];
    
    /**
     * Explanation of the purpose determination
     */
    explanation: string[];
}

/**
 * Script type purpose mapping
 */
interface ScriptTypePurposeMapping {
    /**
     * Script type pattern (regex)
     */
    scriptTypePattern: RegExp;
    
    /**
     * Module name pattern (regex)
     */
    moduleNamePattern?: RegExp;
    
    /**
     * Import patterns (regex)
     */
    importPatterns: RegExp[];
    
    /**
     * Class patterns (regex)
     */
    classPatterns: RegExp[];
    
    /**
     * Function patterns (regex)
     */
    functionPatterns: RegExp[];
    
    /**
     * Script purpose
     */
    purpose: string;
    
    /**
     * Resource purpose type
     */
    purposeType: ResourcePurposeType;
    
    /**
     * Related gameplay system
     */
    gameplaySystem?: string;
    
    /**
     * Base confidence score (0-100)
     */
    baseConfidence: number;
}

/**
 * Specialized analyzer for Python script resources
 */
@singleton()
export class ScriptPurposeAnalyzer {
    private logger: Logger;
    private scriptTypePurposeMappings: ScriptTypePurposeMapping[] = [];
    
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(logger?: Logger) {
        this.logger = logger || new Logger('ScriptPurposeAnalyzer');
        this.initializeScriptTypeMappings();
    }
    
    /**
     * Initialize script type purpose mappings
     */
    private initializeScriptTypeMappings(): void {
        // Injection script
        this.scriptTypePurposeMappings.push({
            scriptTypePattern: /Injection/i,
            moduleNamePattern: /injector/i,
            importPatterns: [
                /from sims4\.utils import injector/i,
                /from sims4\.collections import inject/i
            ],
            classPatterns: [
                /class\s+([A-Za-z0-9_]+)Injector/i
            ],
            functionPatterns: [
                /@injector\.inject/i,
                /@inject_to/i,
                /def\s+inject/i
            ],
            purpose: 'script_injection',
            purposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'script_core',
            baseConfidence: 90
        });
        
        // Command script
        this.scriptTypePurposeMappings.push({
            scriptTypePattern: /Command/i,
            moduleNamePattern: /commands/i,
            importPatterns: [
                /from sims4\.commands import Command/i,
                /import sims4\.commands/i
            ],
            classPatterns: [
                /class\s+([A-Za-z0-9_]+)Command/i
            ],
            functionPatterns: [
                /@sims4\.commands\.Command/i,
                /@Command/i
            ],
            purpose: 'script_command',
            purposeType: ResourcePurposeType.UTILITY,
            gameplaySystem: 'script_utility',
            baseConfidence: 90
        });
        
        // UI script
        this.scriptTypePurposeMappings.push({
            scriptTypePattern: /UI/i,
            moduleNamePattern: /ui/i,
            importPatterns: [
                /from ui import/i,
                /import ui\./i,
                /from sims4\.ui import/i
            ],
            classPatterns: [
                /class\s+([A-Za-z0-9_]+)Dialog/i,
                /class\s+([A-Za-z0-9_]+)UI/i,
                /class\s+([A-Za-z0-9_]+)View/i
            ],
            functionPatterns: [
                /def\s+on_click/i,
                /def\s+show_dialog/i,
                /def\s+update_ui/i
            ],
            purpose: 'script_ui',
            purposeType: ResourcePurposeType.MODIFIES_CONTENT,
            gameplaySystem: 'ui',
            baseConfidence: 90
        });
        
        // Service script
        this.scriptTypePurposeMappings.push({
            scriptTypePattern: /Service/i,
            moduleNamePattern: /service/i,
            importPatterns: [
                /from sims4\.service_manager import Service/i,
                /import sims4\.service_manager/i
            ],
            classPatterns: [
                /class\s+([A-Za-z0-9_]+)Service\(Service\)/i,
                /class\s+([A-Za-z0-9_]+)Manager/i
            ],
            functionPatterns: [
                /def\s+on_zone_load/i,
                /def\s+on_zone_unload/i,
                /def\s+start/i,
                /def\s+stop/i
            ],
            purpose: 'script_service',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'script_core',
            baseConfidence: 90
        });
        
        // Interaction script
        this.scriptTypePurposeMappings.push({
            scriptTypePattern: /Interaction/i,
            moduleNamePattern: /interaction/i,
            importPatterns: [
                /from interactions\./i,
                /import interactions\./i,
                /from sims4\.resources import Types/i
            ],
            classPatterns: [
                /class\s+([A-Za-z0-9_]+)Interaction/i,
                /class\s+([A-Za-z0-9_]+)Mixer/i,
                /class\s+([A-Za-z0-9_]+)SocialMixer/i
            ],
            functionPatterns: [
                /def\s+_run_interaction_gen/i,
                /def\s+_test/i,
                /def\s+on_choice_selected/i
            ],
            purpose: 'script_interaction',
            purposeType: ResourcePurposeType.ADDS_CONTENT,
            gameplaySystem: 'interactions',
            baseConfidence: 90
        });
        
        // Utility script
        this.scriptTypePurposeMappings.push({
            scriptTypePattern: /Util/i,
            moduleNamePattern: /util/i,
            importPatterns: [
                /import sims4\./i,
                /from sims4\./i
            ],
            classPatterns: [
                /class\s+([A-Za-z0-9_]+)Helper/i,
                /class\s+([A-Za-z0-9_]+)Utils/i,
                /class\s+([A-Za-z0-9_]+)Util/i
            ],
            functionPatterns: [
                /def\s+get_/i,
                /def\s+is_/i,
                /def\s+has_/i
            ],
            purpose: 'script_utility',
            purposeType: ResourcePurposeType.UTILITY,
            gameplaySystem: 'script_utility',
            baseConfidence: 85
        });
    }
    
    /**
     * Analyze Python script resource purpose
     * @param resourceKey Resource key
     * @param scriptContent Script content
     * @param metadata Resource metadata
     * @returns Script purpose analysis result
     */
    public analyzeScriptPurpose(
        resourceKey: ResourceKey,
        scriptContent: string,
        metadata: Record<string, any>
    ): ScriptPurposeAnalysisResult {
        try {
            // Default result
            const result: ScriptPurposeAnalysisResult = {
                scriptType: 'Unknown',
                moduleName: '',
                purposeType: ResourcePurposeType.UNKNOWN,
                scriptPurpose: 'unknown',
                confidence: 0,
                hasInjections: false,
                hasCommands: false,
                keyImports: [],
                keyClasses: [],
                keyFunctions: [],
                explanation: []
            };
            
            // Check if this is a Script resource
            if (resourceKey.type !== ResourceTypes.RESOURCE_TYPE_SCRIPT) {
                result.explanation.push('Not a Script resource');
                return result;
            }
            
            // Extract script type and module name from metadata
            result.scriptType = metadata.scriptType || '';
            result.moduleName = metadata.moduleName || '';
            
            // Check for injections and commands in metadata
            result.hasInjections = metadata.hasInjections || false;
            result.hasCommands = metadata.hasCommands || false;
            
            // If we don't have script content, return with limited analysis
            if (!scriptContent) {
                result.explanation.push('No script content available for analysis');
                
                // Try to determine purpose from metadata
                if (result.hasInjections) {
                    result.scriptPurpose = 'script_injection';
                    result.purposeType = ResourcePurposeType.MODIFIES_CONTENT;
                    result.confidence = 70;
                    result.explanation.push('Determined as injection script from metadata');
                } else if (result.hasCommands) {
                    result.scriptPurpose = 'script_command';
                    result.purposeType = ResourcePurposeType.UTILITY;
                    result.confidence = 70;
                    result.explanation.push('Determined as command script from metadata');
                }
                
                return result;
            }
            
            // Extract imports, classes, and functions from content
            const imports: string[] = [];
            const classes: string[] = [];
            const functions: string[] = [];
            
            // Extract imports
            const importRegex = /^\s*(?:from\s+([^\s]+)\s+import|import\s+([^\s]+))/gm;
            let importMatch;
            while ((importMatch = importRegex.exec(scriptContent)) !== null) {
                const importName = importMatch[1] || importMatch[2];
                imports.push(importName);
            }
            
            // Extract classes
            const classRegex = /^\s*class\s+([A-Za-z0-9_]+)(?:\(([^)]+)\))?:/gm;
            let classMatch;
            while ((classMatch = classRegex.exec(scriptContent)) !== null) {
                const className = classMatch[1];
                const baseClass = classMatch[2] || '';
                classes.push(`${className}${baseClass ? `(${baseClass})` : ''}`);
            }
            
            // Extract functions
            const functionRegex = /^\s*def\s+([A-Za-z0-9_]+)\(/gm;
            let functionMatch;
            while ((functionMatch = functionRegex.exec(scriptContent)) !== null) {
                const functionName = functionMatch[1];
                functions.push(functionName);
            }
            
            // Check for injections
            if (!result.hasInjections) {
                result.hasInjections = scriptContent.includes('@injector.inject') || 
                                      scriptContent.includes('@inject_to') ||
                                      scriptContent.includes('def inject');
            }
            
            // Check for commands
            if (!result.hasCommands) {
                result.hasCommands = scriptContent.includes('@sims4.commands.Command') ||
                                    scriptContent.includes('@Command');
            }
            
            // Store key elements
            result.keyImports = imports.slice(0, 10);
            result.keyClasses = classes.slice(0, 10);
            result.keyFunctions = functions.slice(0, 10);
            
            // Try to match to a script type purpose
            for (const mapping of this.scriptTypePurposeMappings) {
                let matchScore = 0;
                const matchEvidence: string[] = [];
                
                // Check script type
                if (result.scriptType && mapping.scriptTypePattern.test(result.scriptType)) {
                    matchScore += 30;
                    matchEvidence.push(`Script type "${result.scriptType}" matches pattern for ${mapping.purpose}`);
                }
                
                // Check module name
                if (result.moduleName && mapping.moduleNamePattern && mapping.moduleNamePattern.test(result.moduleName)) {
                    matchScore += 20;
                    matchEvidence.push(`Module name "${result.moduleName}" matches pattern for ${mapping.purpose}`);
                }
                
                // Check imports
                let importMatches = 0;
                for (const importPattern of mapping.importPatterns) {
                    for (const importName of imports) {
                        if (importPattern.test(importName) || importPattern.test(scriptContent)) {
                            importMatches++;
                            break;
                        }
                    }
                }
                
                if (importMatches > 0) {
                    matchScore += importMatches * 10;
                    matchEvidence.push(`Found ${importMatches} matching imports for ${mapping.purpose}`);
                }
                
                // Check classes
                let classMatches = 0;
                for (const classPattern of mapping.classPatterns) {
                    for (const className of classes) {
                        if (classPattern.test(className) || classPattern.test(scriptContent)) {
                            classMatches++;
                            break;
                        }
                    }
                }
                
                if (classMatches > 0) {
                    matchScore += classMatches * 15;
                    matchEvidence.push(`Found ${classMatches} matching classes for ${mapping.purpose}`);
                }
                
                // Check functions
                let functionMatches = 0;
                for (const functionPattern of mapping.functionPatterns) {
                    for (const functionName of functions) {
                        if (functionPattern.test(functionName) || functionPattern.test(scriptContent)) {
                            functionMatches++;
                            break;
                        }
                    }
                }
                
                if (functionMatches > 0) {
                    matchScore += functionMatches * 10;
                    matchEvidence.push(`Found ${functionMatches} matching functions for ${mapping.purpose}`);
                }
                
                // If we have a good match, update the result
                if (matchScore >= 30) {
                    // Calculate confidence based on match score and base confidence
                    const confidence = Math.min(100, mapping.baseConfidence * (matchScore / 100 + 0.5));
                    
                    // If this is a better match than what we have, update the result
                    if (confidence > result.confidence) {
                        result.scriptPurpose = mapping.purpose;
                        result.purposeType = mapping.purposeType;
                        result.gameplaySystem = mapping.gameplaySystem;
                        result.confidence = confidence;
                        result.explanation = [...result.explanation, ...matchEvidence];
                    }
                }
            }
            
            // Special case handling
            if (result.hasInjections && result.confidence < 80) {
                result.scriptPurpose = 'script_injection';
                result.purposeType = ResourcePurposeType.MODIFIES_CONTENT;
                result.confidence = Math.max(result.confidence, 80);
                result.explanation.push('Script contains injections, likely modifying existing gameplay');
            } else if (result.hasCommands && result.confidence < 80) {
                result.scriptPurpose = 'script_command';
                result.purposeType = ResourcePurposeType.UTILITY;
                result.confidence = Math.max(result.confidence, 80);
                result.explanation.push('Script contains commands, likely a utility script');
            }
            
            return result;
        } catch (error) {
            this.logger.error(`Error analyzing Script purpose for resource ${resourceKey.type}:${resourceKey.instance}:`, error);
            
            // Return default result on error
            return {
                scriptType: 'Unknown',
                moduleName: '',
                purposeType: ResourcePurposeType.UNKNOWN,
                scriptPurpose: 'unknown',
                confidence: 0,
                hasInjections: false,
                hasCommands: false,
                keyImports: [],
                keyClasses: [],
                keyFunctions: [],
                explanation: ['Error analyzing Script purpose']
            };
        }
    }
}
