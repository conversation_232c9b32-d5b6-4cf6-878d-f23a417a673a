import { <PERSON><PERSON><PERSON> } from "../../../types/resource/interfaces.js";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { Logger } from "../../../utils/logging/logger.js";

const logger = new Logger('LotObjectListExtractor');

/**
 * Interface for Lot Object List metadata
 */
export interface LotObjectListMetadata {
  lotId?: number;
  objectCount: number;
  objectTypes?: string[];
  hasMailbox?: boolean;
  hasSpawnPoints?: boolean;
}

/**
 * Extracts metadata from a LOT_OBJECT_LIST resource (LOBJ - 0x91568FD8).
 * 
 * Lot Object List resources contain instanced décor & FX that live on the lot but outside
 * the editable footprint (mailboxes, spawn FX, etc.).
 * 
 * @param key The resource key of the LOT_OBJECT_LIST resource.
 * @param buffer The buffer containing the LOT_OBJECT_LIST resource data.
 * @returns The extracted LotObjectListMetadata.
 */
export function extractLotObjectListMetadata(key: <PERSON><PERSON><PERSON>, buffer: Buffer): LotObjectListMetadata | null {
  try {
    if (buffer.length < 16) {
      logger.warn(`LOT_OBJECT_LIST resource ${key.instance.toString(16)} is too small to contain valid data.`);
      return null;
    }

    logger.debug(`Extracting metadata from LOT_OBJECT_LIST resource ${key.instance.toString(16)}`);
    logger.debug(`Buffer size: ${buffer.length} bytes`);

    // Check for magic number/signature if applicable
    const signature = buffer.slice(0, 4).toString('utf8');
    logger.debug(`LOT_OBJECT_LIST signature: ${signature}`);

    // Based on the documentation, LOBJ resources are packed as a vector of 0xC0-byte structs
    // Each struct contains GUID, transform, fade level, and tuning reference
    
    // Calculate the number of objects based on the buffer size and struct size
    const structSize = 0xC0; // 192 bytes per object as per documentation
    const objectCount = Math.floor(buffer.length / structSize);
    
    // Create and return the metadata object with what we can determine
    const metadata: LotObjectListMetadata = {
      objectCount
    };

    // Try to extract lot ID if present (this is speculative)
    // Assuming lot ID might be stored as a 32-bit integer near the beginning
    for (let i = 4; i < 16; i++) {
      // Look for potential lot ID markers
      if (buffer[i] === 0x4C && buffer[i+1] === 0x49 && buffer[i+2] === 0x44) { // "LID" marker
        metadata.lotId = buffer.readUInt32LE(i + 3);
        break;
      }
    }
    
    // Check for common object types (this is speculative)
    // We'll scan the buffer for certain patterns that might indicate specific object types
    
    // Check for mailbox
    if (searchBuffer(buffer, "MAIL") || searchBuffer(buffer, "mailbox")) {
      metadata.hasMailbox = true;
    }
    
    // Check for spawn points
    if (searchBuffer(buffer, "SPWN") || searchBuffer(buffer, "spawn")) {
      metadata.hasSpawnPoints = true;
    }

    logger.info(`Extracted metadata for LOT_OBJECT_LIST resource ${key.instance.toString(16)}`);
    return metadata;
  } catch (error) {
    logger.error(`Error extracting LOT_OBJECT_LIST metadata: ${error}`);
    return null;
  }
}

/**
 * Creates a user-friendly content snippet from the extracted metadata.
 * 
 * @param metadata The extracted LotObjectListMetadata.
 * @returns A string containing a user-friendly representation of the metadata.
 */
export function createLotObjectListContentSnippet(metadata: LotObjectListMetadata): string {
  let snippet = `Lot Object List with ${metadata.objectCount} object(s)`;
  
  if (metadata.lotId !== undefined) {
    snippet += ` for Lot #${metadata.lotId}`;
  }
  
  if (metadata.hasMailbox) {
    snippet += `, includes mailbox`;
  }
  
  if (metadata.hasSpawnPoints) {
    snippet += `, includes spawn points`;
  }
  
  return snippet;
}

/**
 * Helper function to search for a string pattern in a buffer.
 * 
 * @param buffer The buffer to search in.
 * @param pattern The string pattern to search for.
 * @returns True if the pattern is found, false otherwise.
 */
function searchBuffer(buffer: Buffer, pattern: string): boolean {
  const patternBuffer = Buffer.from(pattern, 'utf8');
  
  // Simple search algorithm
  for (let i = 0; i <= buffer.length - patternBuffer.length; i++) {
    let found = true;
    for (let j = 0; j < patternBuffer.length; j++) {
      if (buffer[i + j] !== patternBuffer[j]) {
        found = false;
        break;
      }
    }
    if (found) {
      return true;
    }
  }
  
  return false;
}
