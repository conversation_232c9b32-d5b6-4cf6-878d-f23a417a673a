/**
 * TS4Script Repository
 * 
 * This module provides database access for TS4Script analysis results.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { DatabaseService } from '../../../databaseService.js';
import { TS4ScriptModule, TS4ScriptClass, TS4ScriptFunction, TS4ScriptImport, TS4ScriptCommand, TS4ScriptInjection, TS4ScriptEventHandler, TS4ScriptTuningReference } from '../types.js';

/**
 * TS4Script repository
 */
@singleton()
export class TS4ScriptRepository {
    /**
     * Constructor
     * @param databaseService Database service
     * @param logger Logger instance
     */
    constructor(
        private databaseService: DatabaseService,
        private logger: Logger = new Logger('TS4ScriptRepository')
    ) {}

    /**
     * Initialize repository
     */
    public async initialize(): Promise<void> {
        try {
            this.logger.info('Initializing TS4Script repository');

            // Create tables
            await this.createTables();

            this.logger.info('TS4Script repository initialized');
        } catch (error) {
            this.logger.error('Error initializing TS4Script repository:', error);
            throw error;
        }
    }

    /**
     * Create database tables
     */
    private async createTables(): Promise<void> {
        try {
            // Create TS4ScriptPackages table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptPackages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    packageId INTEGER NOT NULL,
                    fileName TEXT NOT NULL,
                    filePath TEXT NOT NULL,
                    fileSize INTEGER NOT NULL,
                    moduleCount INTEGER NOT NULL DEFAULT 0,
                    classCount INTEGER NOT NULL DEFAULT 0,
                    functionCount INTEGER NOT NULL DEFAULT 0,
                    importCount INTEGER NOT NULL DEFAULT 0,
                    hasInjections BOOLEAN NOT NULL DEFAULT 0,
                    hasEventHandlers BOOLEAN NOT NULL DEFAULT 0,
                    hasCommands BOOLEAN NOT NULL DEFAULT 0,
                    hasTuningReferences BOOLEAN NOT NULL DEFAULT 0,
                    modType TEXT,
                    modCategory TEXT,
                    modComplexity TEXT,
                    analysisTimestamp INTEGER NOT NULL,
                    FOREIGN KEY (packageId) REFERENCES Packages(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptModules table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptModules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    packageId INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    path TEXT NOT NULL,
                    size INTEGER NOT NULL,
                    isPackageInit BOOLEAN NOT NULL DEFAULT 0,
                    isSubpackageInit BOOLEAN NOT NULL DEFAULT 0,
                    moduleType TEXT,
                    contentSnippet TEXT,
                    classCount INTEGER NOT NULL DEFAULT 0,
                    functionCount INTEGER NOT NULL DEFAULT 0,
                    importCount INTEGER NOT NULL DEFAULT 0,
                    hasInjections BOOLEAN NOT NULL DEFAULT 0,
                    hasEventHandlers BOOLEAN NOT NULL DEFAULT 0,
                    hasCommands BOOLEAN NOT NULL DEFAULT 0,
                    hasTuningReferences BOOLEAN NOT NULL DEFAULT 0,
                    bytecodeVersion TEXT,
                    pythonVersion TEXT,
                    compilationTimestamp INTEGER,
                    FOREIGN KEY (packageId) REFERENCES Packages(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptClasses table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptClasses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    moduleId INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    fullName TEXT NOT NULL,
                    parentClasses TEXT,
                    isEAClass BOOLEAN NOT NULL DEFAULT 0,
                    methodCount INTEGER NOT NULL DEFAULT 0,
                    propertyCount INTEGER NOT NULL DEFAULT 0,
                    docString TEXT,
                    startLine INTEGER,
                    endLine INTEGER,
                    complexity INTEGER,
                    FOREIGN KEY (moduleId) REFERENCES TS4ScriptModules(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptFunctions table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptFunctions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    moduleId INTEGER NOT NULL,
                    classId INTEGER,
                    name TEXT NOT NULL,
                    fullName TEXT NOT NULL,
                    parameters TEXT,
                    returnAnnotation TEXT,
                    isMethod BOOLEAN NOT NULL DEFAULT 0,
                    isStaticMethod BOOLEAN NOT NULL DEFAULT 0,
                    isClassMethod BOOLEAN NOT NULL DEFAULT 0,
                    isProperty BOOLEAN NOT NULL DEFAULT 0,
                    isCommand BOOLEAN NOT NULL DEFAULT 0,
                    isEventHandler BOOLEAN NOT NULL DEFAULT 0,
                    isInjection BOOLEAN NOT NULL DEFAULT 0,
                    docString TEXT,
                    startLine INTEGER,
                    endLine INTEGER,
                    complexity INTEGER,
                    FOREIGN KEY (moduleId) REFERENCES TS4ScriptModules(id) ON DELETE CASCADE,
                    FOREIGN KEY (classId) REFERENCES TS4ScriptClasses(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptImports table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptImports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    moduleId INTEGER NOT NULL,
                    importedModule TEXT NOT NULL,
                    importedNames TEXT,
                    isFromImport BOOLEAN NOT NULL DEFAULT 0,
                    isEAModule BOOLEAN NOT NULL DEFAULT 0,
                    isRelativeImport BOOLEAN NOT NULL DEFAULT 0,
                    importLevel INTEGER NOT NULL DEFAULT 0,
                    lineNumber INTEGER,
                    FOREIGN KEY (moduleId) REFERENCES TS4ScriptModules(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptCommands table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptCommands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    functionId INTEGER NOT NULL,
                    commandName TEXT NOT NULL,
                    commandType TEXT,
                    parameters TEXT,
                    hasConnection BOOLEAN NOT NULL DEFAULT 0,
                    FOREIGN KEY (functionId) REFERENCES TS4ScriptFunctions(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptInjections table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptInjections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    functionId INTEGER NOT NULL,
                    targetObject TEXT NOT NULL,
                    targetFunction TEXT NOT NULL,
                    injectionType TEXT NOT NULL,
                    callsOriginal BOOLEAN NOT NULL DEFAULT 0,
                    FOREIGN KEY (functionId) REFERENCES TS4ScriptFunctions(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptEventHandlers table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptEventHandlers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    functionId INTEGER NOT NULL,
                    eventType TEXT NOT NULL,
                    targetObject TEXT,
                    FOREIGN KEY (functionId) REFERENCES TS4ScriptFunctions(id) ON DELETE CASCADE
                )
            `);

            // Create TS4ScriptTuningReferences table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS TS4ScriptTuningReferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    moduleId INTEGER NOT NULL,
                    functionId INTEGER,
                    resourceType TEXT NOT NULL,
                    instanceId TEXT,
                    accessType TEXT NOT NULL,
                    lineNumber INTEGER,
                    FOREIGN KEY (moduleId) REFERENCES TS4ScriptModules(id) ON DELETE CASCADE,
                    FOREIGN KEY (functionId) REFERENCES TS4ScriptFunctions(id) ON DELETE CASCADE
                )
            `);

            // Create indexes
            await this.createIndexes();
        } catch (error) {
            this.logger.error('Error creating TS4Script tables:', error);
            throw error;
        }
    }

    /**
     * Create database indexes
     */
    private async createIndexes(): Promise<void> {
        try {
            // Create indexes for TS4ScriptPackages
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_packages_packageId ON TS4ScriptPackages(packageId)
            `);

            // Create indexes for TS4ScriptModules
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_modules_packageId ON TS4ScriptModules(packageId)
            `);
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_modules_name ON TS4ScriptModules(name)
            `);

            // Create indexes for TS4ScriptClasses
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_classes_moduleId ON TS4ScriptClasses(moduleId)
            `);
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_classes_name ON TS4ScriptClasses(name)
            `);

            // Create indexes for TS4ScriptFunctions
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_functions_moduleId ON TS4ScriptFunctions(moduleId)
            `);
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_functions_classId ON TS4ScriptFunctions(classId)
            `);
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_functions_name ON TS4ScriptFunctions(name)
            `);

            // Create indexes for TS4ScriptImports
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_imports_moduleId ON TS4ScriptImports(moduleId)
            `);

            // Create indexes for TS4ScriptCommands
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_commands_functionId ON TS4ScriptCommands(functionId)
            `);

            // Create indexes for TS4ScriptInjections
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_injections_functionId ON TS4ScriptInjections(functionId)
            `);

            // Create indexes for TS4ScriptEventHandlers
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_eventhandlers_functionId ON TS4ScriptEventHandlers(functionId)
            `);

            // Create indexes for TS4ScriptTuningReferences
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_tuningrefs_moduleId ON TS4ScriptTuningReferences(moduleId)
            `);
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_ts4script_tuningrefs_functionId ON TS4ScriptTuningReferences(functionId)
            `);
        } catch (error) {
            this.logger.error('Error creating TS4Script indexes:', error);
            throw error;
        }
    }

    /**
     * Save TS4Script package
     * @param packageId Package ID
     * @param fileName File name
     * @param filePath File path
     * @param fileSize File size
     * @param moduleCount Module count
     * @param classCount Class count
     * @param functionCount Function count
     * @param importCount Import count
     * @param hasInjections Has injections
     * @param hasEventHandlers Has event handlers
     * @param hasCommands Has commands
     * @param hasTuningReferences Has tuning references
     * @param modType Mod type
     * @param modCategory Mod category
     * @param modComplexity Mod complexity
     * @returns TS4Script package ID
     */
    public async saveTS4ScriptPackage(
        packageId: number,
        fileName: string,
        filePath: string,
        fileSize: number,
        moduleCount: number,
        classCount: number,
        functionCount: number,
        importCount: number,
        hasInjections: boolean,
        hasEventHandlers: boolean,
        hasCommands: boolean,
        hasTuningReferences: boolean,
        modType?: string,
        modCategory?: string,
        modComplexity?: string
    ): Promise<number> {
        try {
            // Check if package already exists
            const existingPackage = await this.databaseService.executeQuery(`
                SELECT id FROM TS4ScriptPackages WHERE packageId = ?
            `, [packageId]);

            if (existingPackage && existingPackage.length > 0) {
                // Update existing package
                await this.databaseService.executeQuery(`
                    UPDATE TS4ScriptPackages
                    SET fileName = ?, filePath = ?, fileSize = ?, moduleCount = ?, classCount = ?,
                        functionCount = ?, importCount = ?, hasInjections = ?, hasEventHandlers = ?,
                        hasCommands = ?, hasTuningReferences = ?, modType = ?, modCategory = ?,
                        modComplexity = ?, analysisTimestamp = ?
                    WHERE id = ?
                `, [
                    fileName, filePath, fileSize, moduleCount, classCount,
                    functionCount, importCount, hasInjections ? 1 : 0, hasEventHandlers ? 1 : 0,
                    hasCommands ? 1 : 0, hasTuningReferences ? 1 : 0, modType, modCategory,
                    modComplexity, Date.now(), existingPackage[0].id
                ]);

                return existingPackage[0].id;
            } else {
                // Insert new package
                const result = await this.databaseService.executeQuery(`
                    INSERT INTO TS4ScriptPackages (
                        packageId, fileName, filePath, fileSize, moduleCount, classCount,
                        functionCount, importCount, hasInjections, hasEventHandlers,
                        hasCommands, hasTuningReferences, modType, modCategory,
                        modComplexity, analysisTimestamp
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    packageId, fileName, filePath, fileSize, moduleCount, classCount,
                    functionCount, importCount, hasInjections ? 1 : 0, hasEventHandlers ? 1 : 0,
                    hasCommands ? 1 : 0, hasTuningReferences ? 1 : 0, modType, modCategory,
                    modComplexity, Date.now()
                ]);

                return result.lastID;
            }
        } catch (error) {
            this.logger.error(`Error saving TS4Script package for packageId ${packageId}:`, error);
            throw error;
        }
    }

    /**
     * Save TS4Script module
     * @param module TS4Script module
     * @param packageId Package ID
     * @returns Module ID
     */
    public async saveTS4ScriptModule(module: TS4ScriptModule, packageId: number): Promise<number> {
        try {
            // Insert module
            const result = await this.databaseService.executeQuery(`
                INSERT INTO TS4ScriptModules (
                    packageId, name, path, size, isPackageInit, isSubpackageInit,
                    moduleType, contentSnippet, classCount, functionCount,
                    importCount, hasInjections, hasEventHandlers, hasCommands,
                    hasTuningReferences, bytecodeVersion, pythonVersion, compilationTimestamp
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                packageId,
                module.name,
                module.path,
                module.content.length,
                module.metadata.isPackageInit ? 1 : 0,
                module.metadata.isSubpackageInit ? 1 : 0,
                module.metadata.moduleType,
                module.contentSnippet,
                module.classes.length,
                module.functions.length,
                module.imports.length,
                module.metadata.hasInjections ? 1 : 0,
                module.metadata.hasEventHandlers ? 1 : 0,
                module.metadata.hasCommands ? 1 : 0,
                module.metadata.hasTuningReferences ? 1 : 0,
                module.metadata.bytecodeVersion,
                module.metadata.pythonVersion,
                module.metadata.compilationTimestamp
            ]);

            return result.lastID;
        } catch (error) {
            this.logger.error(`Error saving TS4Script module ${module.name}:`, error);
            throw error;
        }
    }
}
