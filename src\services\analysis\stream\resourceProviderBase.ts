/**
 * Resource Provider Base
 * 
 * This module provides a base class for all resource providers.
 * It implements common functionality for accessing resources from various sources,
 * managing file handles, and handling errors.
 */

import { Readable } from 'stream';
import { FileHandle } from 'fs/promises';
import { open } from 'fs/promises';
import { Logger } from '../../../utils/logging/logger.js';
import { EnhancedBufferPool } from '../../../utils/memory/enhancedBufferPool.js';
import EnhancedMemoryManager from '../../../utils/memory/enhancedMemoryManager.js';
import { PackageIndexEntry } from './index.js';

// Create a logger for this module
const logger = new Logger('ResourceProviderBase');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get buffer pool instance
const bufferPool = EnhancedBufferPool.getInstance();

/**
 * Resource stream options
 */
export interface ResourceStreamOptions {
    highWaterMark?: number;
    emitClose?: boolean;
    timeout?: number;
    autoClose?: boolean;
}

/**
 * Resource metadata
 */
export interface ResourceMetadata {
    size: number;
    compressed: boolean;
    memSize: number;
    fileSize: number;
    offset: number;
    exists: boolean;
}

/**
 * Resource provider interface
 */
export interface IResourceProvider {
    /**
     * Create a readable stream for a resource
     * @param packagePath Path to the package file
     * @param entry Resource entry
     * @param options Stream options
     */
    createResourceStream(
        packagePath: string, 
        entry: PackageIndexEntry, 
        options?: ResourceStreamOptions
    ): Promise<Readable>;
    
    /**
     * Get metadata about a resource without reading its content
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    getResourceMetadata(
        packagePath: string, 
        entry: PackageIndexEntry
    ): Promise<ResourceMetadata>;
    
    /**
     * Check if a resource exists
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    resourceExists(
        packagePath: string, 
        entry: PackageIndexEntry
    ): Promise<boolean>;
    
    /**
     * Close all open file handles
     */
    close(): Promise<void>;
}

/**
 * Resource provider base implementation
 */
export abstract class ResourceProviderBase implements IResourceProvider {
    protected fileHandleCache: Map<string, FileHandle> = new Map();
    protected defaultTimeout: number = 30000; // 30 seconds default timeout
    
    /**
     * Create a new resource provider
     */
    constructor() {
        logger.debug('Created resource provider base');
    }
    
    /**
     * Create a readable stream for a resource
     * @param packagePath Path to the package file
     * @param entry Resource entry
     * @param options Stream options
     */
    public abstract createResourceStream(
        packagePath: string, 
        entry: PackageIndexEntry, 
        options?: ResourceStreamOptions
    ): Promise<Readable>;
    
    /**
     * Get metadata about a resource without reading its content
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    public abstract getResourceMetadata(
        packagePath: string, 
        entry: PackageIndexEntry
    ): Promise<ResourceMetadata>;
    
    /**
     * Check if a resource exists
     * @param packagePath Path to the package file
     * @param entry Resource entry
     */
    public abstract resourceExists(
        packagePath: string, 
        entry: PackageIndexEntry
    ): Promise<boolean>;
    
    /**
     * Close all open file handles
     */
    public async close(): Promise<void> {
        const fileHandles = Array.from(this.fileHandleCache.entries());
        let closedCount = 0;
        
        for (const [path, handle] of fileHandles) {
            try {
                await handle.close();
                this.fileHandleCache.delete(path);
                closedCount++;
            } catch (error: any) {
                logger.error(`Error closing file handle for ${path}: ${error.message}`);
            }
        }
        
        logger.debug(`Closed ${closedCount} file handles`);
    }
    
    /**
     * Get a file handle from the cache or open a new one
     * @param filePath Path to the file
     * @returns File handle
     * @protected
     */
    protected async getFileHandle(filePath: string): Promise<FileHandle> {
        let fileHandle = this.fileHandleCache.get(filePath);
        
        if (!fileHandle) {
            try {
                fileHandle = await open(filePath, 'r');
                this.fileHandleCache.set(filePath, fileHandle);
                logger.debug(`Opened file handle for ${filePath}`);
            } catch (error: any) {
                logger.error(`Error opening file ${filePath}: ${error.message}`);
                throw error;
            }
        }
        
        return fileHandle;
    }
    
    /**
     * Format bytes to a human-readable string
     * @param bytes Number of bytes
     * @returns Formatted string
     * @protected
     */
    protected formatBytes(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
