﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿import { BinaryResourceType } from '../../types/resource/core.js'; // Keep BinaryResourceType if needed elsewhere
import { ResourceMetadata, ResourceKey } from '../../types/resource/interfaces.js';
import { PackageMetadata } from '../../types/resource/Package.js';
// import { ResourceConflict } from '../../types/conflict/ConflictTypes.js'; // Remove this
import { ConflictInfo } from '../../types/conflict/index.js'; // Import ConflictInfo

export interface PackageInfo {
  name: string;
  version: string;
  author?: string;
  description?: string;
  // resources: ResourceKey[]; // ResourceKeys are not directly derivable from ResourceMetadata[]
  resourceMetadata: ResourceMetadata[]; // Keep the original metadata
  dependencies?: ResourceKey[]; // Keep as ResourceKey[]
  conflicts?: ConflictInfo[]; // Use ConflictInfo[]
}

// Rewritten function to reflect correct types
export function extractPackageInfo(metadata: PackageMetadata): PackageInfo {
  return {
    name: metadata.name,
    version: metadata.version || '1.0.0', // Provide default version
    author: metadata.author,
    description: metadata.description,
    resourceMetadata: metadata.resources || [], // Pass metadata through
    // ResourceKeys cannot be reliably created from ResourceMetadata alone
    // Pass dependencies/conflicts directly
    dependencies: metadata.dependencies,
    conflicts: metadata.conflicts,
  };
}

export function validatePackageMetadata(metadata: PackageMetadata): boolean {
  if (!metadata.name || !metadata.version) {
    return false;
  }

  if (!Array.isArray(metadata.resources)) {
    return false;
  }

  // Validate based on ResourceMetadata properties
  return metadata.resources.every(resource =>
    resource.name && // Check required fields from ResourceMetadata
    resource.path &&
    resource.size >= 0 &&
    resource.hash &&
    resource.timestamp
    // Cannot validate type directly on ResourceMetadata
  );
}

export function getResourceTypeCount(metadata: PackageMetadata): Record<string, number> {
  const counts: Record<string, number> = {};

  metadata.resources.forEach(resource => {
    // Extract resource type from the resource metadata
    // The resource should have a type property or we can derive it from the key
    let typeName = 'UNKNOWN';

    if (resource.resourceType) {
      typeName = resource.resourceType;
    } else if ((resource as any).type !== undefined) {
      // Try to get type from resource object
      const type = (resource as any).type;
      typeName = `TYPE_0x${type.toString(16).toUpperCase()}`;
    } else if ((resource as any).key && (resource as any).key.type !== undefined) {
      // Try to get type from resource key
      const type = (resource as any).key.type;
      typeName = `TYPE_0x${type.toString(16).toUpperCase()}`;
    }

    counts[typeName] = (counts[typeName] || 0) + 1;
  });

  return counts;
}

export function getPackageSize(metadata: PackageMetadata): number {
  return metadata.resources.reduce((total, resource) =>
    total + (resource.size || 0), 0
  );
}

// Return type should match PackageMetadata definition
export function getPackageDependencies(metadata: PackageMetadata): ResourceKey[] {
  return metadata.dependencies || [];
}

// Return type should match PackageInfo definition
export function getPackageConflicts(metadata: PackageMetadata): ConflictInfo[] {
  return metadata.conflicts || []; // metadata.conflicts is now ConflictInfo[]
}
