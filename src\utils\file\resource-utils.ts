﻿﻿import { BinaryResourceType, BinaryResourceTypeValue } from '../../types/resource/core.js'; // Import both type and value
// Corrected import
import { ResourceKey } from '../../types/resource/interfaces.js';

export function getResourceTypeName(type: BinaryResourceType): string {
  return BinaryResourceTypeValue[type] || 'UNKNOWN'; // Use value for indexing
}

export function getResourceTypeFromName(name: string): BinaryResourceType {
  return BinaryResourceTypeValue[name as keyof typeof BinaryResourceTypeValue] || BinaryResourceTypeValue.UNKNOWN; // Use value
}

export function isValidResourceType(type: number): boolean {
  return type in BinaryResourceTypeValue; // Use value for 'in' check
}

export function getResourceTypeCategory(type: BinaryResourceType): string {
  const name = getResourceTypeName(type);
  if (name.includes('SCRIPT')) return 'SCRIPT';
  if (name.includes('IMAGE') || name === 'TEXTURE') return 'TEXTURE';
  if (name.includes('MODEL') || name === 'RIG' || name === 'BONE_DELTA') return 'MODEL';
  if (name.includes('CAS')) return 'CAS';
  if (name.includes('TUNING')) return 'TUNING';
  if (name.includes('ANIMATION')) return 'ANIMATION';
  if (name.includes('AUDIO') || name.includes('SOUND') || name.includes('MUSIC')) return 'AUDIO';
  if (name.includes('UI') || name.includes('FONT')) return 'UI';
  if (
    name.includes('BUILD') ||
    name.includes('FOOTPRINT') ||
    name.includes('LIGHT') ||
    name.includes('SLOT')
  ) return 'BUILD';
  if (name.includes('WORLD') || name.includes('REGION')) return 'WORLD';
  return 'OTHER';
}

export function validateResourceKey(key: ResourceKey): boolean {
  return (
    key.type !== undefined &&
    key.name !== undefined &&
    key.path !== undefined &&
    isValidResourceType(key.type)
  );
}

export function getResourceKeyString(key: ResourceKey): string {
  return `${key.type}:${key.name}:${key.path}`;
}

export function parseResourceKeyString(keyString: string): ResourceKey {
  const [type, name, path] = keyString.split(':');
  // Ensure returned object matches ResourceKey (group/instance should be number)
  return {
    type: parseInt(type, 10),
    name,
    path,
    id: name, // Using name as ID might be incorrect, instance is often used
    group: 0n, // Use bigint 0n
    instance: 0n, // Use bigint 0n
    // timestamp is not part of ResourceKey definition
  };
}

export function compareResourceKeys(a: ResourceKey, b: ResourceKey): number {
  if (a.type !== b.type) return a.type - b.type;
  // Compare group as bigint
  if (a.group !== b.group) {
      return a.group < b.group ? -1 : 1; // Basic bigint comparison
  }
  // Compare instance as bigint
  if (a.instance !== b.instance) {
      return a.instance < b.instance ? -1 : 1; // Basic bigint comparison
  }

  // Handle optional properties safely
  const nameA = a.name || '';
  const nameB = b.name || '';
  if (nameA !== nameB) return nameA.localeCompare(nameB);
  
  const pathA = a.path || '';
  const pathB = b.path || '';
  return pathA.localeCompare(pathB);
}
