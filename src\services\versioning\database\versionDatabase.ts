/**
 * Database service for version detection components
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { GameVersion, GameVersionDetectionResult } from '../interfaces/gameVersion.js';
import { SchemaVersion, SchemaVersionDetectionResult } from '../interfaces/schemaVersion.js';

/**
 * Database service for version detection
 */
export class VersionDatabase {
    private logger: Logger;
    private db: any; // SQLite database instance
    private initialized: boolean = false;

    /**
     * Constructor
     * @param databaseService The database service
     * @param logger The logger instance
     */
    constructor(private databaseService: DatabaseService, logger?: Logger) {
        this.logger = logger || new Logger('VersionDatabase');
        this.db = databaseService['db']; // Access the db property directly
    }

    /**
     * Initialize the database tables
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Create GameVersions table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS GameVersions (
                    id TEXT PRIMARY KEY,
                    versionNumber TEXT NOT NULL UNIQUE,
                    releaseDate TEXT,
                    description TEXT,
                    majorChanges TEXT, -- JSON array of changes
                    fingerprints TEXT, -- JSON array of fingerprint objects
                    isMajor INTEGER NOT NULL DEFAULT 0,
                    metadata TEXT -- JSON object with additional metadata
                );
            `);

            // Create SchemaVersions table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS SchemaVersions (
                    id TEXT PRIMARY KEY,
                    schemaName TEXT NOT NULL,
                    gameVersion TEXT NOT NULL,
                    schemaHash TEXT NOT NULL,
                    schemaData TEXT NOT NULL, -- JSON representation of schema
                    description TEXT,
                    changes TEXT, -- JSON array of change objects
                    metadata TEXT, -- JSON object with additional metadata
                    FOREIGN KEY (gameVersion) REFERENCES GameVersions(versionNumber),
                    UNIQUE(schemaName, gameVersion)
                );
            `);

            // Create ModGameVersions table to track detected game versions for mods
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS ModGameVersions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    packageId INTEGER NOT NULL,
                    version TEXT NOT NULL,
                    confidence REAL DEFAULT 0,
                    matchedFingerprints TEXT, -- JSON array of matched fingerprints
                    explanation TEXT,
                    timestamp INTEGER NOT NULL,
                    FOREIGN KEY (packageId) REFERENCES Packages(id),
                    UNIQUE(packageId, version)
                );
            `);

            // Create ResourceSchemaVersions table to track detected schema versions for resources
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS ResourceSchemaVersions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    resourceId INTEGER NOT NULL,
                    schemaVersion TEXT NOT NULL,
                    gameVersion TEXT NOT NULL,
                    confidence REAL DEFAULT 0,
                    explanation TEXT,
                    compatibility TEXT, -- JSON object with compatibility info
                    timestamp INTEGER NOT NULL,
                    FOREIGN KEY (resourceId) REFERENCES Resources(id),
                    UNIQUE(resourceId, schemaVersion)
                );
            `);

            // Create indices for performance
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_schemaversions_schemaname ON SchemaVersions(schemaName);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_schemaversions_gameversion ON SchemaVersions(gameVersion);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_modgameversions_packageid ON ModGameVersions(packageId);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_modgameversions_version ON ModGameVersions(version);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourceschemaversions_resourceid ON ResourceSchemaVersions(resourceId);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourceschemaversions_schemaversion ON ResourceSchemaVersions(schemaVersion);');

            this.initialized = true;
            this.logger.info('Version database tables initialized');
        } catch (error) {
            this.logger.error('Error initializing version database tables:', error);
            throw error;
        }
    }

    /**
     * Save a game version to the database
     * @param version The game version to save
     */
    public async saveGameVersion(version: GameVersion): Promise<void> {
        await this.initialize();

        try {
            await this.databaseService.executeQuery(`
                INSERT OR REPLACE INTO GameVersions (
                    id, versionNumber, releaseDate, description, majorChanges,
                    fingerprints, isMajor, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                version.id,
                version.versionNumber,
                version.releaseDate,
                version.description,
                JSON.stringify(version.majorChanges),
                JSON.stringify(version.fingerprints),
                version.isMajor ? 1 : 0,
                version.metadata ? JSON.stringify(version.metadata) : null
            ]);

            this.logger.debug(`Saved game version: ${version.versionNumber}`);
        } catch (error) {
            this.logger.error(`Error saving game version ${version.versionNumber}:`, error);
            throw error;
        }
    }

    /**
     * Save a schema version to the database
     * @param schema The schema version to save
     */
    public async saveSchemaVersion(schema: SchemaVersion): Promise<void> {
        await this.initialize();

        try {
            await this.databaseService.executeQuery(`
                INSERT OR REPLACE INTO SchemaVersions (
                    id, schemaName, gameVersion, schemaHash, schemaData,
                    description, changes, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                schema.id,
                schema.schemaName,
                schema.gameVersion,
                schema.schemaHash,
                schema.schemaData,
                schema.description || null,
                schema.changes ? JSON.stringify(schema.changes) : null,
                schema.metadata ? JSON.stringify(schema.metadata) : null
            ]);

            this.logger.debug(`Saved schema version: ${schema.schemaName} for game version ${schema.gameVersion}`);
        } catch (error) {
            this.logger.error(`Error saving schema version ${schema.schemaName} for game version ${schema.gameVersion}:`, error);
            throw error;
        }
    }

    /**
     * Save a game version detection result for a mod
     * @param result The game version detection result
     * @param packageId The package ID
     * @returns The ID of the saved detection result
     */
    public async saveModGameVersion(
        result: GameVersionDetectionResult,
        packageId: number
    ): Promise<number> {
        await this.initialize();

        try {
            const queryResult = await this.databaseService.executeQuery(`
                INSERT OR REPLACE INTO ModGameVersions (
                    packageId, version, confidence, matchedFingerprints,
                    explanation, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?)
                RETURNING id
            `, [
                packageId,
                result.version,
                result.confidence,
                JSON.stringify(result.matchedFingerprints),
                result.explanation,
                result.timestamp
            ]);

            const id = queryResult[0]?.id;
            this.logger.debug(`Saved game version detection result for package ${packageId} with ID ${id}`);
            return id;
        } catch (error) {
            this.logger.error(`Error saving game version detection result for package ${packageId}:`, error);
            throw error;
        }
    }

    /**
     * Save a schema version detection result for a resource
     * @param result The schema version detection result
     * @param resourceId The resource ID
     * @returns The ID of the saved detection result
     */
    public async saveResourceSchemaVersion(
        result: SchemaVersionDetectionResult,
        resourceId: number
    ): Promise<number> {
        await this.initialize();

        try {
            const queryResult = await this.databaseService.executeQuery(`
                INSERT OR REPLACE INTO ResourceSchemaVersions (
                    resourceId, schemaVersion, gameVersion, confidence,
                    explanation, compatibility, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                RETURNING id
            `, [
                resourceId,
                result.schemaVersion,
                result.gameVersion,
                result.confidence,
                result.explanation,
                JSON.stringify(result.compatibility),
                result.timestamp
            ]);

            const id = queryResult[0]?.id;
            this.logger.debug(`Saved schema version detection result for resource ${resourceId} with ID ${id}`);
            return id;
        } catch (error) {
            this.logger.error(`Error saving schema version detection result for resource ${resourceId}:`, error);
            throw error;
        }
    }

    /**
     * Get a game version by version number
     * @param versionNumber The version number
     * @returns The game version or undefined if not found
     */
    public async getGameVersion(versionNumber: string): Promise<GameVersion | undefined> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM GameVersions WHERE versionNumber = ?
            `, [versionNumber]);

            if (!result || result.length === 0) {
                return undefined;
            }

            const version = result[0];
            return {
                id: version.id,
                versionNumber: version.versionNumber,
                releaseDate: version.releaseDate,
                description: version.description,
                majorChanges: JSON.parse(version.majorChanges),
                fingerprints: JSON.parse(version.fingerprints),
                isMajor: version.isMajor === 1,
                metadata: version.metadata ? JSON.parse(version.metadata) : undefined
            };
        } catch (error) {
            this.logger.error(`Error getting game version ${versionNumber}:`, error);
            throw error;
        }
    }

    /**
     * Get a schema version by name and game version
     * @param schemaName The schema name
     * @param gameVersion The game version
     * @returns The schema version or undefined if not found
     */
    public async getSchemaVersion(schemaName: string, gameVersion: string): Promise<SchemaVersion | undefined> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM SchemaVersions WHERE schemaName = ? AND gameVersion = ?
            `, [schemaName, gameVersion]);

            if (!result || result.length === 0) {
                return undefined;
            }

            const schema = result[0];
            return {
                id: schema.id,
                schemaName: schema.schemaName,
                gameVersion: schema.gameVersion,
                schemaHash: schema.schemaHash,
                schemaData: schema.schemaData,
                description: schema.description,
                changes: schema.changes ? JSON.parse(schema.changes) : undefined,
                metadata: schema.metadata ? JSON.parse(schema.metadata) : undefined
            };
        } catch (error) {
            this.logger.error(`Error getting schema version ${schemaName} for game version ${gameVersion}:`, error);
            throw error;
        }
    }

    /**
     * Get the detected game version for a mod
     * @param packageId The package ID
     * @returns The game version detection result or undefined if not found
     */
    public async getModGameVersion(packageId: number): Promise<GameVersionDetectionResult | undefined> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM ModGameVersions WHERE packageId = ?
                ORDER BY confidence DESC, timestamp DESC LIMIT 1
            `, [packageId]);

            if (!result || result.length === 0) {
                return undefined;
            }

            const detection = result[0];
            return {
                version: detection.version,
                confidence: detection.confidence,
                matchedFingerprints: JSON.parse(detection.matchedFingerprints),
                explanation: detection.explanation,
                timestamp: detection.timestamp
            };
        } catch (error) {
            this.logger.error(`Error getting game version for package ${packageId}:`, error);
            throw error;
        }
    }

    /**
     * Get the detected schema version for a resource
     * @param resourceId The resource ID
     * @returns The schema version detection result or undefined if not found
     */
    public async getResourceSchemaVersion(resourceId: number): Promise<SchemaVersionDetectionResult | undefined> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(`
                SELECT * FROM ResourceSchemaVersions WHERE resourceId = ?
                ORDER BY confidence DESC, timestamp DESC LIMIT 1
            `, [resourceId]);

            if (!result || result.length === 0) {
                return undefined;
            }

            const detection = result[0];
            return {
                schemaVersion: detection.schemaVersion,
                gameVersion: detection.gameVersion,
                confidence: detection.confidence,
                explanation: detection.explanation,
                compatibility: JSON.parse(detection.compatibility),
                timestamp: detection.timestamp
            };
        } catch (error) {
            this.logger.error(`Error getting schema version for resource ${resourceId}:`, error);
            throw error;
        }
    }
}
