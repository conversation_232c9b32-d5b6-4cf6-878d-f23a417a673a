/**
 * Event Emitter Configuration
 *
 * This module configures the Node.js EventEmitter to properly manage event listeners
 * and prevent memory leaks in the application.
 */

import { EventEmitter } from 'events';
import * as stream from 'stream';
import * as fs from 'fs';
import * as process from 'process';
import { Logger } from './logging/logger.js';

// Create a logger for this module
const logger = new Logger('EventEmitterConfig');

// Set a reasonable default max listeners - 20 is usually sufficient for most components
// Setting it too high masks actual memory leaks
EventEmitter.defaultMaxListeners = 20;

/**
 * Configure event emitter settings for the application
 * This approach focuses on proper listener management rather than
 * just increasing limits to hide warnings
 */
export function configureEventEmitter(): void {
    logger.info('Configuring event emitter settings');

    // Set reasonable max listeners for specific components that need it
    // Instead of setting extremely high values, use more moderate increases
    // and focus on proper listener cleanup

    // For streams that process large files, a higher limit may be needed
    stream.Stream.prototype.setMaxListeners(50);

    // File streams may need much higher limits due to S4TK library
    if (fs.ReadStream.prototype) {
        fs.ReadStream.prototype.setMaxListeners(50);
    }

    if (fs.WriteStream.prototype) {
        fs.WriteStream.prototype.setMaxListeners(50);
    }

    // Set max listeners for File class
    try {
        const File = require('fs').File;
        if (File && File.prototype) {
            File.prototype.setMaxListeners(100);
            logger.info('Increased max listeners for File to 100');
        }
    } catch (error) {
        // File class might not be available in this Node.js version
        logger.debug('File class not available, setting max listeners for fs.ReadStream and fs.WriteStream instead');

        // Increase max listeners for fs.ReadStream and fs.WriteStream even more
        if (fs.ReadStream.prototype) {
            fs.ReadStream.prototype.setMaxListeners(100);
            logger.info('Increased max listeners for fs.ReadStream to 100');
        }

        if (fs.WriteStream.prototype) {
            fs.WriteStream.prototype.setMaxListeners(100);
            logger.info('Increased max listeners for fs.WriteStream to 100');
        }
    }

    // Process event listeners should be limited and well-managed
    try {
        if (process && typeof process.setMaxListeners === 'function') {
            process.setMaxListeners(25);
            logger.info('Set max listeners for process to 25');
        } else {
            logger.debug('process.setMaxListeners is not available');
        }
    } catch (error) {
        logger.debug(`Could not set max listeners for process: ${error}`);
        // Continue execution even if this fails
    }

    // Standard I/O streams
    if (process.stdout) {
        process.stdout.setMaxListeners(50);
    }

    if (process.stderr) {
        process.stderr.setMaxListeners(50);
    }

    if (process.stdin) {
        process.stdin.setMaxListeners(15);
    }

    // Console objects can have many listeners when redirecting output
    try {
        const Console = require('console').Console;
        if (Console && Console.prototype) {
            Console.prototype.setMaxListeners(50);
            logger.info('Increased max listeners for Console to 50');
        }
    } catch (error) {
        // Console class might not be available in this Node.js version
    }

    logger.info('Event emitter configuration complete');
}

/**
 * Create a tracked event emitter that logs warnings when listeners are added
 * but not removed, helping to identify potential memory leaks
 *
 * @param name A name for this emitter (for logging purposes)
 * @param maxListeners The maximum number of listeners (default: 10)
 * @returns A new EventEmitter with tracking enabled
 */
export function createTrackedEmitter(name: string, maxListeners: number = 10): EventEmitter {
    const emitter = new EventEmitter();
    emitter.setMaxListeners(maxListeners);

    // Store original methods
    const originalAddListener = emitter.addListener;
    const originalRemoveListener = emitter.removeListener;

    // Track active listeners
    const activeListeners = new Map<string, Set<(...args: any[]) => void>>();

    // Override addListener to track additions
    emitter.addListener = function(event: string, listener: (...args: any[]) => void): EventEmitter {
        if (!activeListeners.has(event)) {
            activeListeners.set(event, new Set());
        }
        activeListeners.get(event)?.add(listener);

        logger.debug(`[${name}] Added listener for event: ${event}, total: ${activeListeners.get(event)?.size}`);
        return originalAddListener.call(this, event, listener);
    };

    // Override removeListener to track removals
    emitter.removeListener = function(event: string, listener: (...args: any[]) => void): EventEmitter {
        if (activeListeners.has(event)) {
            activeListeners.get(event)?.delete(listener);
            logger.debug(`[${name}] Removed listener for event: ${event}, remaining: ${activeListeners.get(event)?.size}`);
        }
        return originalRemoveListener.call(this, event, listener);
    };

    // Add method to check for leaks
    (emitter as any).checkForLeaks = function(): void {
        for (const [event, listeners] of activeListeners.entries()) {
            if (listeners.size > 0) {
                logger.warn(`[${name}] Potential memory leak: ${listeners.size} listeners for event '${event}' were not removed`);
            }
        }
    };

    return emitter;
}

/**
 * Set max listeners for a specific emitter
 * @param emitter The event emitter
 * @param maxListeners The maximum number of listeners
 */
export function setMaxListeners(emitter: EventEmitter, maxListeners: number = 20): void {
    emitter.setMaxListeners(maxListeners);
}
