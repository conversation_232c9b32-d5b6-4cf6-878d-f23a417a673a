/**
 * @deprecated Use ResourceCategory from OfficialResourceTypes.ts instead
 * Legacy resource categories - kept for backward compatibility
 */
export enum ResourceCategory {
  BINARY = 'BINARY',
  TUNING = 'TUNING', // For XML/SimData based tuning
  SCRIPT = 'SCRIPT',
  OBJECT = 'OBJECT', // For Object Definitions, Catalogs
  IMAGE = 'IMAGE',
  SOUND = 'SOUND',
  ANIMATION = 'ANIMATION',
  ASSET = 'ASSET', // For general assets
  CASPART = 'CASPART', // For CAS parts
  MODEL = 'MODEL', // For 3D models
  TEXTURE = 'TEXTURE', // For textures
  EFFECT = 'EFFECT', // For visual effects
  UI = 'UI', // For UI elements
  WORLD = 'WORLD', // For world/lot related resources
  VISUAL = 'VISUAL', // For visual resources (materials, textures, etc.)
  GAMEPLAY = 'GAMEPLAY', // For gameplay-related resources
  LOT = 'LOT', // For lot-related resources
  UNKNOWN = 'UNKNOWN'
}

/**
 * @deprecated Use OfficialResourceType from OfficialResourceTypes.ts instead
 * Legacy resource types - based on S4TK BinaryResourceType enum
 *
 * This enum provides string names for the resource types defined in S4TK's BinaryResourceType.
 * It is used for type safety in our application when referring to resource types by name.
 *
 * IMPORTANT: This is now deprecated in favor of the comprehensive OfficialResourceType enum
 * which includes all 100+ official Sims 4 resource types extracted from game assets.
 */
export enum ResourceType {
  // Tuning resources
  STRING_TABLE = 'STRING_TABLE',
  SIMDATA = 'SIMDATA',
  NAME_MAP = 'NAME_MAP',
  COMBINED_TUNING = 'COMBINED_TUNING',

  // CAS resources
  CASPART = 'CASPART',
  CASPART_THUMBNAIL = 'CASPART_THUMBNAIL',
  CAS_PRESET = 'CAS_PRESET',

  // Object resources
  OBJECT_DEFINITION = 'OBJECT_DEFINITION',
  OBJECT_CATALOG = 'OBJECT_CATALOG',
  OBJECT_CATALOG_SET = 'OBJECT_CATALOG_SET',
  SLOT = 'SLOT',
  LIGHT = 'LIGHT',
  FOOTPRINT = 'FOOTPRINT',

  // Image resources
  DST_IMAGE = 'DST_IMAGE',
  DDS_IMAGE = 'DDS_IMAGE',
  PNG_IMAGE = 'PNG_IMAGE',
  RLE2_IMAGE = 'RLE2_IMAGE',
  RLES_IMAGE = 'RLES_IMAGE',

  // Model resources
  MODEL = 'MODEL',
  MODEL_LOD = 'MODEL_LOD',

  // Animation resources
  ANIMATION_STATE_MACHINE = 'ANIMATION_STATE_MACHINE',

  // Sim resources
  SIM_INFO = 'SIM_INFO',

  // Tray resources
  TRAY_ITEM = 'TRAY_ITEM',

  // World resources
  REGION_DESCRIPTION = 'REGION_DESCRIPTION',
  REGION_MAP = 'REGION_MAP',

  // Font resources
  OPENTYPE_FONT = 'OPENTYPE_FONT',
  TRUETYPE_FONT = 'TRUETYPE_FONT',

  // Audio resources
  SOUND = 'SOUND',
  SOUND_EFFECT = 'SOUND_EFFECT',
  SOUND_EFFECT_ALT = 'SOUND_EFFECT_ALT',
  SOUND_EFFECT_ALT2 = 'SOUND_EFFECT_ALT2',
  SOUND_TRACK = 'SOUND_TRACK',
  SOUND_DATA = 'SOUND_DATA',
  SOUND_BANK = 'SOUND_BANK',
  HEADERLESS_SOUND = 'HEADERLESS_SOUND',

  // Unknown resource
  UNKNOWN = 'UNKNOWN'
}

// Re-export the new comprehensive types for easy access
export {
  OfficialResourceType,
  ResourceCategory as OfficialResourceCategory,
  ResourceTypeMetadata
} from './OfficialResourceTypes.js';

export {
  getResourceTypeFromId,
  getResourceTypeId,
  isKnownResourceType,
  getAllResourceTypeIds,
  getAllResourceTypes
} from './ResourceTypeRegistry.js';

export {
  getResourceMetadata,
  getResourceTypesByCategory,
  requiresReference,
  isBaseGameOnly,
  getFileExtension
} from './ResourceMetadataRegistry.js';
