/**
 * Adaptive Throttler
 *
 * This module provides adaptive throttling capabilities for the application.
 * It helps prevent overloading the system by throttling operations based on
 * system load and hardware capabilities.
 *
 * Features:
 * - Rate limiting
 * - Concurrency limiting
 * - Memory-aware throttling
 * - CPU-aware throttling
 * - Disk-aware throttling
 * - Adaptive parameters
 */

import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import { AppError, ErrorCategory, ErrorCode, ErrorSeverity } from '../error/errorTypes.js';
import { EnhancedErrorHandler } from '../error/enhancedErrorHandler.js';
import EnhancedMemoryManager from '../memory/enhancedMemoryManager.js';
import { HardwareDetector, HardwareCategory, HardwareInfo, PerformanceRecommendations } from './hardwareDetector.js';

// Create a logger for this module
const logger = new Logger('AdaptiveThrottler');

// Get error handler instance
const errorHandler = EnhancedErrorHandler.getInstance();

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get hardware detector instance
const hardwareDetector = HardwareDetector.getInstance();

/**
 * Throttling mode
 */
export enum ThrottlingMode {
    STATIC = 'static',
    DYNAMIC = 'dynamic',
    HYBRID = 'hybrid'
}

/**
 * Throttling level
 */
export enum ThrottlingLevel {
    NONE = 'none',
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * Throttling parameters
 */
export interface ThrottlingParameters {
    maxConcurrency: number;
    maxRate: number;
    maxMemoryUsage: number;
    maxCpuUsage: number;
    maxDiskUsage: number;
    batchSize: number;
    chunkSize: number;
    bufferSize: number;
    timeout: number;
}

/**
 * Throttling statistics
 */
export interface ThrottlingStats {
    currentConcurrency: number;
    currentRate: number;
    currentMemoryUsage: number;
    currentCpuUsage: number;
    currentDiskUsage: number;
    throttlingLevel: ThrottlingLevel;
    throttledOperations: number;
    delayedOperations: number;
    rejectedOperations: number;
    completedOperations: number;
    averageOperationTime: number;
    peakConcurrency: number;
    peakRate: number;
    peakMemoryUsage: number;
    peakCpuUsage: number;
    peakDiskUsage: number;
}

/**
 * Throttling options
 */
export interface ThrottlingOptions {
    mode?: ThrottlingMode;
    updateInterval?: number;
    maxQueueSize?: number;
    enableMemoryThrottling?: boolean;
    enableCpuThrottling?: boolean;
    enableDiskThrottling?: boolean;
    memoryThreshold?: number;
    cpuThreshold?: number;
    diskThreshold?: number;
    staticParameters?: ThrottlingParameters;
}

/**
 * Operation to throttle
 */
export interface ThrottledOperation<T> {
    id: string;
    operation: () => Promise<T>;
    priority: number;
    timeout?: number;
    retries?: number;
    maxRetries?: number;
    category?: string;
    timestamp: number;
}

/**
 * Operation result
 */
export interface OperationResult<T> {
    id: string;
    result: T;
    error?: Error;
    throttled: boolean;
    delayed: boolean;
    rejected: boolean;
    retries: number;
    duration: number;
    timestamp: number;
}

/**
 * Adaptive throttler class
 */
export class AdaptiveThrottler extends EventEmitter {
    private static instance: AdaptiveThrottler;
    private options: ThrottlingOptions;
    private parameters: ThrottlingParameters;
    private stats: ThrottlingStats;
    private updateTimer?: NodeJS.Timeout;
    private activeOperations: Map<string, ThrottledOperation<any>> = new Map();
    private queuedOperations: ThrottledOperation<any>[] = [];
    private hardwareInfo?: HardwareInfo;
    private recommendations?: PerformanceRecommendations;

    /**
     * Create a new adaptive throttler
     * @param options Throttling options
     */
    private constructor(options: ThrottlingOptions = {}) {
        super();

        this.options = {
            mode: ThrottlingMode.HYBRID,
            updateInterval: 1000, // 1 second
            maxQueueSize: 1000,
            enableMemoryThrottling: true,
            enableCpuThrottling: true,
            enableDiskThrottling: true,
            memoryThreshold: 0.8, // 80%
            cpuThreshold: 0.8, // 80%
            diskThreshold: 0.8, // 80%
            ...options
        };

        // Initialize parameters with default values
        this.parameters = this.getDefaultParameters();

        // Initialize stats
        this.stats = {
            currentConcurrency: 0,
            currentRate: 0,
            currentMemoryUsage: 0,
            currentCpuUsage: 0,
            currentDiskUsage: 0,
            throttlingLevel: ThrottlingLevel.NONE,
            throttledOperations: 0,
            delayedOperations: 0,
            rejectedOperations: 0,
            completedOperations: 0,
            averageOperationTime: 0,
            peakConcurrency: 0,
            peakRate: 0,
            peakMemoryUsage: 0,
            peakCpuUsage: 0,
            peakDiskUsage: 0
        };

        // Start update timer
        this.startUpdateTimer();

        // Initialize hardware info
        this.initializeHardwareInfo();

        logger.info('Adaptive throttler initialized');
    }

    /**
     * Get the adaptive throttler instance
     * @param options Throttling options
     * @returns Adaptive throttler instance
     */
    public static getInstance(options?: ThrottlingOptions): AdaptiveThrottler {
        if (!AdaptiveThrottler.instance) {
            AdaptiveThrottler.instance = new AdaptiveThrottler(options);
        } else if (options) {
            // Update options if provided
            AdaptiveThrottler.instance.options = {
                ...AdaptiveThrottler.instance.options,
                ...options
            };
        }

        return AdaptiveThrottler.instance;
    }

    /**
     * Initialize hardware information
     * @private
     */
    private async initializeHardwareInfo(): Promise<void> {
        try {
            // Detect hardware
            this.hardwareInfo = await hardwareDetector.detectHardware();

            // Get recommendations
            this.recommendations = hardwareDetector.getRecommendations();

            // Update parameters based on recommendations
            this.updateParametersFromRecommendations();

            // Listen for hardware detection events
            hardwareDetector.on('hardwareDetected', (info) => {
                this.hardwareInfo = info;
                this.recommendations = hardwareDetector.getRecommendations();
                this.updateParametersFromRecommendations();
            });
        } catch (error: any) {
            logger.error(`Error initializing hardware info: ${error.message}`);
        }
    }

    /**
     * Update parameters from recommendations
     * @private
     */
    private updateParametersFromRecommendations(): void {
        if (!this.recommendations) {
            return;
        }

        // Update parameters based on mode
        switch (this.options.mode) {
            case ThrottlingMode.STATIC:
                // Use static parameters
                if (this.options.staticParameters) {
                    this.parameters = { ...this.options.staticParameters };
                }
                break;

            case ThrottlingMode.DYNAMIC:
                // Use dynamic parameters based on recommendations
                this.parameters = {
                    maxConcurrency: this.recommendations.concurrency,
                    maxRate: this.recommendations.concurrency * 10, // 10 operations per concurrency unit
                    maxMemoryUsage: this.recommendations.cacheSize,
                    maxCpuUsage: 0.8, // 80%
                    maxDiskUsage: 0.8, // 80%
                    batchSize: this.recommendations.batchSize,
                    chunkSize: this.recommendations.chunkSize,
                    bufferSize: this.recommendations.bufferSize,
                    timeout: this.recommendations.timeout
                };
                break;

            case ThrottlingMode.HYBRID:
            default:
                // Use hybrid parameters
                if (this.options.staticParameters) {
                    // Start with static parameters
                    const staticParams = { ...this.options.staticParameters };

                    // Adjust based on recommendations
                    this.parameters = {
                        maxConcurrency: Math.min(staticParams.maxConcurrency, this.recommendations.concurrency),
                        maxRate: Math.min(staticParams.maxRate, this.recommendations.concurrency * 10),
                        maxMemoryUsage: Math.min(staticParams.maxMemoryUsage, this.recommendations.cacheSize),
                        maxCpuUsage: staticParams.maxCpuUsage,
                        maxDiskUsage: staticParams.maxDiskUsage,
                        batchSize: Math.min(staticParams.batchSize, this.recommendations.batchSize),
                        chunkSize: Math.min(staticParams.chunkSize, this.recommendations.chunkSize),
                        bufferSize: Math.min(staticParams.bufferSize, this.recommendations.bufferSize),
                        timeout: Math.max(staticParams.timeout, this.recommendations.timeout)
                    };
                } else {
                    // Use dynamic parameters
                    this.parameters = {
                        maxConcurrency: this.recommendations.concurrency,
                        maxRate: this.recommendations.concurrency * 10,
                        maxMemoryUsage: this.recommendations.cacheSize,
                        maxCpuUsage: 0.8,
                        maxDiskUsage: 0.8,
                        batchSize: this.recommendations.batchSize,
                        chunkSize: this.recommendations.chunkSize,
                        bufferSize: this.recommendations.bufferSize,
                        timeout: this.recommendations.timeout
                    };
                }
                break;
        }

        logger.info(`Throttling parameters updated based on hardware category: ${this.hardwareInfo?.category}`);
    }

    /**
     * Start update timer
     * @private
     */
    private startUpdateTimer(): void {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }

        this.updateTimer = setInterval(() => {
            this.updateStats();
        }, this.options.updateInterval);

        logger.debug(`Started throttling update timer with interval ${this.options.updateInterval}ms`);
    }

    /**
     * Update throttling statistics
     * @private
     */
    private updateStats(): void {
        // Update memory usage
        if (this.options.enableMemoryThrottling) {
            this.stats.currentMemoryUsage = memoryManager.getMemoryPressure();
            this.stats.peakMemoryUsage = Math.max(this.stats.peakMemoryUsage, this.stats.currentMemoryUsage);
        }

        // Update CPU usage (simplified)
        if (this.options.enableCpuThrottling) {
            // In a real implementation, this would use a CPU usage monitor
            this.stats.currentCpuUsage = this.activeOperations.size / this.parameters.maxConcurrency;
            this.stats.peakCpuUsage = Math.max(this.stats.peakCpuUsage, this.stats.currentCpuUsage);
        }

        // Update disk usage (simplified)
        if (this.options.enableDiskThrottling) {
            // In a real implementation, this would use a disk usage monitor
            this.stats.currentDiskUsage = 0.5; // Placeholder
            this.stats.peakDiskUsage = Math.max(this.stats.peakDiskUsage, this.stats.currentDiskUsage);
        }

        // Update throttling level
        this.updateThrottlingLevel();

        // Process queue
        this.processQueue();
    }

    /**
     * Update throttling level
     * @private
     */
    private updateThrottlingLevel(): void {
        // Calculate throttling level based on resource usage
        let level = ThrottlingLevel.NONE;

        // Check memory usage
        if (this.options.enableMemoryThrottling && this.stats.currentMemoryUsage > this.options.memoryThreshold!) {
            level = ThrottlingLevel.HIGH;

            if (this.stats.currentMemoryUsage > 0.95) {
                level = ThrottlingLevel.CRITICAL;
            } else if (this.stats.currentMemoryUsage > 0.9) {
                level = ThrottlingLevel.HIGH;
            } else if (this.stats.currentMemoryUsage > 0.7) {
                level = ThrottlingLevel.MEDIUM;
            } else if (this.stats.currentMemoryUsage > 0.5) {
                level = ThrottlingLevel.LOW;
            }
        }

        // Check CPU usage
        if (this.options.enableCpuThrottling && this.stats.currentCpuUsage > this.options.cpuThreshold!) {
            (level as any) = Math.max(level as any, (ThrottlingLevel as any).HIGH as number);

            if (this.stats.currentCpuUsage > 0.95) {
                level = ThrottlingLevel.CRITICAL;
            } else if (this.stats.currentCpuUsage > 0.9) {
                level = ThrottlingLevel.HIGH;
            } else if (this.stats.currentCpuUsage > 0.7) {
                level = ThrottlingLevel.MEDIUM;
            } else if (this.stats.currentCpuUsage > 0.5) {
                level = ThrottlingLevel.LOW;
            }
        }

        // Check disk usage
        if (this.options.enableDiskThrottling && this.stats.currentDiskUsage > this.options.diskThreshold!) {
            (level as any) = Math.max(level as any, (ThrottlingLevel as any).HIGH);

            if (this.stats.currentDiskUsage > 0.95) {
                level = ThrottlingLevel.CRITICAL;
            } else if (this.stats.currentDiskUsage > 0.9) {
                level = ThrottlingLevel.HIGH;
            } else if (this.stats.currentDiskUsage > 0.7) {
                level = ThrottlingLevel.MEDIUM;
            } else if (this.stats.currentDiskUsage > 0.5) {
                level = ThrottlingLevel.LOW;
            }
        }

        // Check concurrency
        if (this.activeOperations.size >= this.parameters.maxConcurrency) {
            (level as any) = Math.max(level as any, (ThrottlingLevel as any).MEDIUM);
        } else if (this.activeOperations.size >= this.parameters.maxConcurrency * 0.8) {
            (level as any) = Math.max(level as any, (ThrottlingLevel as any).LOW);
        }

        // Update throttling level
        if (this.stats.throttlingLevel !== level) {
            const oldLevel = this.stats.throttlingLevel;
            this.stats.throttlingLevel = level;

            // Emit throttling level changed event
            this.emit('throttlingLevelChanged', {
                oldLevel,
                newLevel: level,
                memoryUsage: this.stats.currentMemoryUsage,
                cpuUsage: this.stats.currentCpuUsage,
                diskUsage: this.stats.currentDiskUsage,
                concurrency: this.activeOperations.size
            });

            logger.info(`Throttling level changed from ${oldLevel} to ${level}`);
        }
    }

    /**
     * Get default throttling parameters
     * @returns Default throttling parameters
     * @private
     */
    private getDefaultParameters(): ThrottlingParameters {
        return {
            maxConcurrency: 4,
            maxRate: 40,
            maxMemoryUsage: 50 * 1024 * 1024, // 50MB
            maxCpuUsage: 0.8, // 80%
            maxDiskUsage: 0.8, // 80%
            batchSize: 50,
            chunkSize: 256 * 1024, // 256KB
            bufferSize: 4 * 1024 * 1024, // 4MB
            timeout: 30000 // 30 seconds
        };
    }

    /**
     * Throttle an operation
     * @param operation Operation to throttle
     * @param options Operation options
     * @returns Operation result
     */
    public async throttle<T>(
        operation: () => Promise<T>,
        options: {
            id?: string;
            priority?: number;
            timeout?: number;
            maxRetries?: number;
            category?: string;
        } = {}
    ): Promise<T> {
        // Generate operation ID if not provided
        const id = options.id || `op-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        // Create throttled operation
        const throttledOperation: ThrottledOperation<T> = {
            id,
            operation,
            priority: options.priority || 0,
            timeout: options.timeout || this.parameters.timeout,
            retries: 0,
            maxRetries: options.maxRetries || 3,
            category: options.category,
            timestamp: Date.now()
        };

        // Check if we need to throttle
        const throttlingLevel = this.getThrottlingLevel();

        if (throttlingLevel === ThrottlingLevel.CRITICAL) {
            // Reject operation if throttling level is critical
            this.stats.rejectedOperations++;

            throw errorHandler.createError(
                ErrorCode.OPERATION_REJECTED,
                `Operation rejected due to critical throttling level`,
                {
                    category: ErrorCategory.SYSTEM,
                    severity: ErrorSeverity.ERROR,
                    context: {
                        component: 'AdaptiveThrottler',
                        operation: 'throttle',
                        parameters: { id, priority: throttledOperation.priority }
                    }
                }
            );
        } else if (
            throttlingLevel === ThrottlingLevel.HIGH ||
            this.activeOperations.size >= this.parameters.maxConcurrency
        ) {
            // Queue operation if throttling level is high or max concurrency reached
            if (this.queuedOperations.length >= this.options.maxQueueSize!) {
                // Reject operation if queue is full
                this.stats.rejectedOperations++;

                throw errorHandler.createError(
                    ErrorCode.OPERATION_REJECTED,
                    `Operation rejected due to full queue`,
                    {
                        category: ErrorCategory.SYSTEM,
                        severity: ErrorSeverity.ERROR,
                        context: {
                            component: 'AdaptiveThrottler',
                            operation: 'throttle',
                            parameters: { id, priority: throttledOperation.priority }
                        }
                    }
                );
            }

            // Add to queue
            this.queuedOperations.push(throttledOperation);

            // Sort queue by priority
            this.queuedOperations.sort((a, b) => b.priority - a.priority);

            // Update stats
            this.stats.delayedOperations++;

            // Emit operation queued event
            this.emit('operationQueued', {
                id: throttledOperation.id,
                priority: throttledOperation.priority,
                category: throttledOperation.category,
                queuePosition: this.queuedOperations.indexOf(throttledOperation),
                queueLength: this.queuedOperations.length
            });

            logger.debug(`Operation ${id} queued (priority: ${throttledOperation.priority}, queue length: ${this.queuedOperations.length})`);

            // Wait for operation to complete
            return new Promise<T>((resolve, reject) => {
                // Set up event listener for operation result
                const resultListener = (result: OperationResult<T>) => {
                    if (result.id === id) {
                        // Remove event listener
                        this.removeListener('operationResult', resultListener);

                        if (result.error) {
                            reject(result.error);
                        } else {
                            resolve(result.result);
                        }
                    }
                };

                // Add event listener
                this.on('operationResult', resultListener);

                // Set up timeout if specified
                if (throttledOperation.timeout) {
                    setTimeout(() => {
                        // Remove event listener
                        this.removeListener('operationResult', resultListener);

                        // Remove from queue if still queued
                        const index = this.queuedOperations.indexOf(throttledOperation);
                        if (index !== -1) {
                            this.queuedOperations.splice(index, 1);
                        }

                        // Remove from active operations if still active
                        if (this.activeOperations.has(id)) {
                            this.activeOperations.delete(id);
                        }

                        // Reject with timeout error
                        reject(errorHandler.createError(
                            ErrorCode.OPERATION_TIMEOUT,
                            `Operation timed out after ${throttledOperation.timeout}ms`,
                            {
                                category: ErrorCategory.SYSTEM,
                                severity: ErrorSeverity.ERROR,
                                context: {
                                    component: 'AdaptiveThrottler',
                                    operation: 'throttle',
                                    parameters: { id, priority: throttledOperation.priority }
                                }
                            }
                        ));
                    }, throttledOperation.timeout);
                }
            });
        } else {
            // Execute operation immediately
            return this.executeOperation(throttledOperation);
        }
    }

    /**
     * Get throttling parameters
     * @returns Throttling parameters
     */
    public getParameters(): ThrottlingParameters {
        return { ...this.parameters };
    }

    /**
     * Get throttling statistics
     * @returns Throttling statistics
     */
    public getStats(): ThrottlingStats {
        return { ...this.stats };
    }

    /**
     * Get throttling level
     * @returns Throttling level
     */
    public getThrottlingLevel(): ThrottlingLevel {
        return this.stats.throttlingLevel;
    }

    /**
     * Update throttling parameters
     * @param parameters Throttling parameters
     */
    public updateParameters(parameters: Partial<ThrottlingParameters>): void {
        this.parameters = {
            ...this.parameters,
            ...parameters
        };

        logger.debug(`Throttling parameters updated: ${JSON.stringify(parameters)}`);
    }

    /**
     * Process queued operations
     * @private
     */
    private processQueue(): void {
        // Process queued operations if there are any and we have capacity
        while (
            this.queuedOperations.length > 0 &&
            this.activeOperations.size < this.parameters.maxConcurrency &&
            this.getThrottlingLevel() !== ThrottlingLevel.CRITICAL &&
            this.getThrottlingLevel() !== ThrottlingLevel.HIGH
        ) {
            // Get next operation
            const operation = this.queuedOperations.shift();

            if (operation) {
                // Execute operation
                this.executeOperation(operation).catch(error => {
                    logger.error(`Error executing queued operation ${operation.id}: ${error.message}`);
                });
            }
        }
    }

    /**
     * Execute an operation
     * @param operation Operation to execute
     * @returns Operation result
     * @private
     */
    private async executeOperation<T>(operation: ThrottledOperation<T>): Promise<T> {
        // Add to active operations
        this.activeOperations.set(operation.id, operation);

        // Update stats
        this.stats.currentConcurrency = this.activeOperations.size;
        this.stats.peakConcurrency = Math.max(this.stats.peakConcurrency, this.stats.currentConcurrency);

        // Emit operation started event
        this.emit('operationStarted', {
            id: operation.id,
            priority: operation.priority,
            category: operation.category,
            activeConcurrency: this.activeOperations.size
        });

        logger.debug(`Operation ${operation.id} started (priority: ${operation.priority}, concurrency: ${this.activeOperations.size})`);

        const startTime = Date.now();

        try {
            // Execute operation
            const result = await operation.operation();

            // Calculate duration
            const duration = Date.now() - startTime;

            // Update stats
            this.stats.completedOperations++;
            this.stats.averageOperationTime = (this.stats.averageOperationTime * (this.stats.completedOperations - 1) + duration) / this.stats.completedOperations;

            // Remove from active operations
            this.activeOperations.delete(operation.id);

            // Update concurrency
            this.stats.currentConcurrency = this.activeOperations.size;

            // Create operation result
            const operationResult: OperationResult<T> = {
                id: operation.id,
                result,
                throttled: false,
                delayed: false,
                rejected: false,
                retries: operation.retries || 0,
                duration,
                timestamp: Date.now()
            };

            // Emit operation result event
            this.emit('operationResult', operationResult);

            logger.debug(`Operation ${operation.id} completed in ${duration}ms`);

            // Process queue
            this.processQueue();

            return result;
        } catch (error: any) {
            // Calculate duration
            const duration = Date.now() - startTime;

            // Increment retries
            operation.retries = (operation.retries || 0) + 1;

            // Check if we should retry
            if (operation.retries < operation.maxRetries!) {
                // Remove from active operations
                this.activeOperations.delete(operation.id);

                // Update concurrency
                this.stats.currentConcurrency = this.activeOperations.size;

                // Add back to queue with higher priority
                operation.priority += 1;
                this.queuedOperations.unshift(operation);

                // Sort queue by priority
                this.queuedOperations.sort((a, b) => b.priority - a.priority);

                // Update stats
                this.stats.throttledOperations++;

                logger.warn(`Operation ${operation.id} failed, retrying (${operation.retries}/${operation.maxRetries}): ${error.message}`);

                // Process queue
                this.processQueue();

                // Wait for operation to complete
                return new Promise<T>((resolve, reject) => {
                    // Set up event listener for operation result
                    const resultListener = (result: OperationResult<T>) => {
                        if (result.id === operation.id) {
                            // Remove event listener
                            this.removeListener('operationResult', resultListener);

                            if (result.error) {
                                reject(result.error);
                            } else {
                                resolve(result.result);
                            }
                        }
                    };

                    // Add event listener
                    this.on('operationResult', resultListener);
                });
            } else {
                // Remove from active operations
                this.activeOperations.delete(operation.id);

                // Update concurrency
                this.stats.currentConcurrency = this.activeOperations.size;

                // Create operation result
                const operationResult: OperationResult<T> = {
                    id: operation.id,
                    result: null as any,
                    error,
                    throttled: false,
                    delayed: false,
                    rejected: false,
                    retries: operation.retries,
                    duration,
                    timestamp: Date.now()
                };

                // Emit operation result event
                this.emit('operationResult', operationResult);

                logger.error(`Operation ${operation.id} failed after ${operation.retries} retries: ${error.message}`);

                // Process queue
                this.processQueue();

                throw error;
            }
        }
    }
}
