import path from 'path';

export interface CleanupConfig {
  retentionPeriods: {
    [key: string]: {
      days: number;
      pattern: string[];
      directory: string;
    };
  };
  excludePatterns: string[];
  archiveSettings: {
    enabled: boolean;
    directory: string;
    maxSize: number; // in MB
    compressionEnabled: boolean;
  };
}

const config: CleanupConfig = {
  retentionPeriods: {
    analysisFiles: {
      days: 7,
      pattern: ['*.txt', '*.json'],
      directory: 'analysis-output'
    },
    logFiles: {
      days: 7,
      pattern: ['*.log'],
      directory: 'logs'
    },
    backupFiles: {
      days: 30,
      pattern: ['*.bak', '*_backup.*'],
      directory: 'output/backups'
    },
    tempFiles: {
      days: 1,
      pattern: ['*.tmp', '*.temp'],
      directory: 'output'
    }
  },
  excludePatterns: [
    '**/node_modules/**',
    '**/.git/**',
    '**/dist/**',
    '**/build/**',
    '**/coverage/**',
    '**/important-analysis/**'
  ],
  archiveSettings: {
    enabled: true,
    directory: path.join('analysis-output', 'archive'),
    maxSize: 500, // 500MB
    compressionEnabled: true
  }
};

// Export both the config and a function to get config with custom base path
export default config;

export function getConfigWithBasePath(basePath: string): CleanupConfig {
  const configWithBasePath: CleanupConfig = {
    ...config,
    retentionPeriods: Object.entries(config.retentionPeriods).reduce((acc, [key, value]) => ({
      ...acc,
      [key]: {
        ...value,
        directory: path.join(basePath, value.directory)
      }
    }), {}),
    archiveSettings: {
      ...config.archiveSettings,
      directory: path.join(basePath, config.archiveSettings.directory)
    }
  };
  return configWithBasePath;
} 
