/**
 * Types for TS4Script analysis
 */

/**
 * TS4Script analysis result
 */
export interface TS4ScriptAnalysisResult {
    /**
     * Package ID
     */
    packageId: number;

    /**
     * Package name
     */
    packageName: string;

    /**
     * Package path
     */
    packagePath: string;

    /**
     * Python modules
     */
    modules: TS4ScriptModule[];

    /**
     * Total number of classes
     */
    totalClasses: number;

    /**
     * Total number of functions
     */
    totalFunctions: number;

    /**
     * Total number of imports
     */
    totalImports: number;

    /**
     * Has injections
     */
    hasInjections: boolean;

    /**
     * Has event handlers
     */
    hasEventHandlers: boolean;

    /**
     * Has commands
     */
    hasCommands: boolean;

    /**
     * Has tuning references
     */
    hasTuningReferences: boolean;

    /**
     * Mod type
     */
    modType?: string;

    /**
     * Mod category
     */
    modCategory?: string;

    /**
     * Mod complexity
     */
    modComplexity?: string;

    /**
     * Main module
     */
    mainModule?: string;

    /**
     * Entry points
     */
    entryPoints?: string[];

    /**
     * Analysis timestamp
     */
    timestamp: number;
}

/**
 * TS4Script module
 */
export interface TS4ScriptModule {
    /**
     * Module ID
     */
    id: number;

    /**
     * Module name
     */
    name: string;

    /**
     * Module path
     */
    path: string;

    /**
     * Module content
     */
    content: string;

    /**
     * Content snippet
     */
    contentSnippet: string;

    /**
     * Module classes
     */
    classes: TS4ScriptClass[];

    /**
     * Module functions
     */
    functions: TS4ScriptFunction[];

    /**
     * Module imports
     */
    imports: TS4ScriptImport[];

    /**
     * Module metadata
     */
    metadata: TS4ScriptModuleMetadata;

    /**
     * Purpose analysis
     */
    purposeAnalysis?: any;
}

/**
 * TS4Script module metadata
 */
export interface TS4ScriptModuleMetadata {
    /**
     * Module type
     */
    moduleType: string;

    /**
     * Has injections
     */
    hasInjections: boolean;

    /**
     * Has event handlers
     */
    hasEventHandlers: boolean;

    /**
     * Has commands
     */
    hasCommands: boolean;

    /**
     * Has tuning references
     */
    hasTuningReferences: boolean;

    /**
     * Is package init
     */
    isPackageInit: boolean;

    /**
     * Is subpackage init
     */
    isSubpackageInit: boolean;

    /**
     * Is entry point
     */
    isEntryPoint?: boolean;

    /**
     * Is main module
     */
    isMainModule?: boolean;

    /**
     * Bytecode version
     */
    bytecodeVersion?: string;

    /**
     * Python version
     */
    pythonVersion?: string;

    /**
     * Compilation timestamp
     */
    compilationTimestamp?: number;

    /**
     * Decompiled source code (snippet)
     */
    decompiledSource?: string;

    /**
     * Decompiler used for decompilation
     */
    decompilerUsed?: string;
}

/**
 * TS4Script class
 */
export interface TS4ScriptClass {
    /**
     * Class ID
     */
    id: number;

    /**
     * Class name
     */
    name: string;

    /**
     * Full name (including module path)
     */
    fullName: string;

    /**
     * Parent classes
     */
    parentClasses: string[];

    /**
     * Is EA class
     */
    isEAClass: boolean;

    /**
     * Methods
     */
    methods: TS4ScriptFunction[];

    /**
     * Properties
     */
    properties: TS4ScriptProperty[];

    /**
     * Documentation string
     */
    docString?: string;

    /**
     * Start line
     */
    startLine?: number;

    /**
     * End line
     */
    endLine?: number;

    /**
     * Complexity
     */
    complexity?: number;
}

/**
 * TS4Script function
 */
export interface TS4ScriptFunction {
    /**
     * Function ID
     */
    id: number;

    /**
     * Function name
     */
    name: string;

    /**
     * Full name (including module path and class name if applicable)
     */
    fullName: string;

    /**
     * Parameters
     */
    parameters: TS4ScriptParameter[];

    /**
     * Return annotation
     */
    returnAnnotation?: string;

    /**
     * Is method
     */
    isMethod: boolean;

    /**
     * Is static method
     */
    isStaticMethod: boolean;

    /**
     * Is class method
     */
    isClassMethod: boolean;

    /**
     * Is property
     */
    isProperty: boolean;

    /**
     * Is command
     */
    isCommand: boolean;

    /**
     * Is event handler
     */
    isEventHandler: boolean;

    /**
     * Is injection
     */
    isInjection: boolean;

    /**
     * Decorators
     */
    decorators: TS4ScriptDecorator[];

    /**
     * Documentation string
     */
    docString?: string;

    /**
     * Start line
     */
    startLine?: number;

    /**
     * End line
     */
    endLine?: number;

    /**
     * Complexity
     */
    complexity?: number;
}

/**
 * TS4Script parameter
 */
export interface TS4ScriptParameter {
    /**
     * Parameter name
     */
    name: string;

    /**
     * Parameter type annotation
     */
    typeAnnotation?: string;

    /**
     * Default value
     */
    defaultValue?: string;

    /**
     * Is positional only
     */
    isPositionalOnly: boolean;

    /**
     * Is keyword only
     */
    isKeywordOnly: boolean;

    /**
     * Is variadic positional
     */
    isVariadicPositional: boolean;

    /**
     * Is variadic keyword
     */
    isVariadicKeyword: boolean;
}

/**
 * TS4Script property
 */
export interface TS4ScriptProperty {
    /**
     * Property name
     */
    name: string;

    /**
     * Property type annotation
     */
    typeAnnotation?: string;

    /**
     * Documentation string
     */
    docString?: string;
}

/**
 * TS4Script decorator
 */
export interface TS4ScriptDecorator {
    /**
     * Decorator name
     */
    name: string;

    /**
     * Decorator arguments
     */
    arguments: any[];

    /**
     * Is command
     */
    isCommand: boolean;

    /**
     * Is injection
     */
    isInjection: boolean;

    /**
     * Is event handler
     */
    isEventHandler: boolean;
}

/**
 * TS4Script import
 */
export interface TS4ScriptImport {
    /**
     * Imported module
     */
    importedModule: string;

    /**
     * Imported names
     */
    importedNames: string[];

    /**
     * Is from import
     */
    isFromImport: boolean;

    /**
     * Is EA module
     */
    isEAModule: boolean;

    /**
     * Is relative import
     */
    isRelativeImport: boolean;

    /**
     * Import level
     */
    importLevel: number;

    /**
     * Line number
     */
    lineNumber?: number;
}

/**
 * TS4Script command
 */
export interface TS4ScriptCommand {
    /**
     * Command name
     */
    commandName: string;

    /**
     * Command type
     */
    commandType?: string;

    /**
     * Parameters
     */
    parameters: TS4ScriptParameter[];

    /**
     * Has connection
     */
    hasConnection: boolean;
}

/**
 * TS4Script injection
 */
export interface TS4ScriptInjection {
    /**
     * Target object
     */
    targetObject: string;

    /**
     * Target function
     */
    targetFunction: string;

    /**
     * Injection type
     */
    injectionType: string;

    /**
     * Calls original
     */
    callsOriginal: boolean;
}

/**
 * TS4Script event handler
 */
export interface TS4ScriptEventHandler {
    /**
     * Event type
     */
    eventType: string;

    /**
     * Target object
     */
    targetObject?: string;
}

/**
 * TS4Script tuning reference
 */
export interface TS4ScriptTuningReference {
    /**
     * Resource type
     */
    resourceType: string;

    /**
     * Instance ID
     */
    instanceId?: string;

    /**
     * Access type
     */
    accessType: string;

    /**
     * Line number
     */
    lineNumber?: number;
}

/**
 * TS4Script dependency
 */
export interface TS4ScriptDependency {
    /**
     * Depends on module name
     */
    dependsOnModuleName: string;

    /**
     * Dependency type
     */
    dependencyType: string;

    /**
     * Is EA dependency
     */
    isEADependency: boolean;
}

/**
 * TS4Script resource reference
 */
export interface TS4ScriptResourceReference {
    /**
     * Resource ID
     */
    resourceId: number;

    /**
     * Reference type
     */
    referenceType: string;

    /**
     * Access type
     */
    accessType: string;
}
