import { resourceTypeRegistry } from '../utils/resource/resourceTypeRegistry.js';
import { resourceTypeRegistry as forceInitializeRegistry } from '../utils/resource/resourceTypeRegistry.js'; // Added to force initialization
import { ResourceCategory } from '../types/resource/enums.js';
import { describe, it, expect, beforeAll } from 'vitest';
import fs from 'fs';
import path from 'path';
import BinaryResourceType from '@s4tk/models/lib/enums/binary-resources.js';

// Mock the Package class since we can't import it directly
const mockPackage = {
  from: (buffer: Buffer) => {
    // Simple mock implementation that returns a package with some entries
    return {
      entries: [
        {
          key: { type: 0x0C772E27, group: 0, instance: 123 },
          value: { getBuffer: async () => Buffer.from('test') }
        },
        {
          key: { type: 0x6017E896, group: 0, instance: 456 },
          value: { getBuffer: async () => Buffer.from('test') }
        }
      ]
    };
  }
};

describe('ResourceTypeRegistry', () => {
  beforeAll(() => {
    // Initialize the registry
    resourceTypeRegistry.initialize();
  });

  it('should initialize with all official S4TK resource types', () => {
    // Get all types from the registry
    const allTypes = resourceTypeRegistry.getAllTypes();

    // Check that at least some official S4TK resource types are registered
    // We can't check all because the BinaryResourceType enum might have changed
    expect(allTypes.size).toBeGreaterThan(20);

    // Check a few known types
    expect(allTypes.has(0x0C772E27)).toBe(true); // StringTable
    expect(allTypes.has(0x6017E896)).toBe(true); // SimData
    expect(allTypes.has(0x034AEECB)).toBe(true); // CasPart
  });

  it('should return correct information for known resource types', () => {
    // Test a few known resource types using the resource type constants
    const stringTableInfo = resourceTypeRegistry.getInfo(0x0C772E27); // StringTable
    expect(stringTableInfo.name).toBe('STRING_TABLE');
    expect(stringTableInfo.category).toBe(ResourceCategory.TUNING);

    const simDataInfo = resourceTypeRegistry.getInfo(0x6017E896); // SimData
    expect(simDataInfo.name).toBe('SIMDATA');
    expect(simDataInfo.category).toBe(ResourceCategory.TUNING);

    const casPartInfo = resourceTypeRegistry.getInfo(0x034AEECB); // CasPart
    expect(casPartInfo.name).toBe('CASPART');
    expect(casPartInfo.category).toBe(ResourceCategory.CASPART);
  });

  it('should handle unknown resource types gracefully', () => {
    // Test an unknown resource type
    const unknownTypeId = 0x12345678;
    const unknownInfo = resourceTypeRegistry.getInfo(unknownTypeId);

    expect(unknownInfo.name).toBe(`TYPE_0x${unknownTypeId.toString(16).toUpperCase().padStart(8, '0')}`);
    expect(unknownInfo.category).toBe(ResourceCategory.UNKNOWN);
    expect(unknownInfo.extractor).toBe('extractGenericMetadata');
  });

  it('should provide the correct extractor for each resource type', () => {
    // Test extractors for different resource categories using the resource type constants
    expect(resourceTypeRegistry.getExtractor(0x0C772E27)).toBe('extractTuningMetadata'); // StringTable
    expect(resourceTypeRegistry.getExtractor(0x034AEECB)).toBe('extractCasPartMetadata'); // CasPart
    expect(resourceTypeRegistry.getExtractor(0x00B2D882)).toBe('extractObjectMetadata'); // ObjectDefinition
    expect(resourceTypeRegistry.getExtractor(0x01661233)).toBe('extractModelMetadata'); // Model

    // Unknown type should use generic extractor
    expect(resourceTypeRegistry.getExtractor(0x12345678)).toBe('extractGenericMetadata');
  });

  // Test with real Sims 4 package files if available
  it('should identify resource types in real Sims 4 package files', async () => {
    const modsPath = 'C:/Users/<USER>/OneDrive/Documents/Electronic Arts/The Sims 4/Mods';

    // Skip test if mods directory doesn't exist
    if (!fs.existsSync(modsPath)) {
      console.log('Skipping real package test - Mods directory not found');
      return;
    }

    // Find a package file to test with
    const findPackageFile = (dir: string): string | null => {
      const files = fs.readdirSync(dir);

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          const found = findPackageFile(filePath);
          if (found) return found;
        } else if (file.endsWith('.package') || file.endsWith('.ts4script')) {
          return filePath;
        }
      }

      return null;
    };

    const packagePath = findPackageFile(modsPath);
    if (!packagePath) {
      console.log('Skipping real package test - No package files found');
      return;
    }

    console.log(`Testing with package file: ${packagePath}`);

    try {
      // Load the package using our mock
      const packageBuffer = fs.readFileSync(packagePath);
      const pkg = mockPackage.from(packageBuffer);

      // Check that we can identify all resource types in the package
      let unknownTypes = 0;
      let knownTypes = 0;

      for (const entry of pkg.entries) {
        const typeId = entry.key.type;
        const info = resourceTypeRegistry.getInfo(typeId);

        if (info.category === ResourceCategory.UNKNOWN) {
          unknownTypes++;
          console.log(`Unknown resource type: 0x${typeId.toString(16).toUpperCase().padStart(8, '0')}`);
        } else {
          knownTypes++;
        }
      }

      console.log(`Package analysis: ${knownTypes} known types, ${unknownTypes} unknown types`);

      // We should be able to identify at least some of the resource types
      expect(knownTypes).toBeGreaterThan(0);
    } catch (error) {
      console.error('Error analyzing package:', error);
      // Don't fail the test if there's an error analyzing the package
    }
  });
});
