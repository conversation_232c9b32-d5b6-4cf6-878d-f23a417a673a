﻿﻿// Corrected imports
import { BinaryResourceType } from '../../types/resource/core.js';
import { ResourceMetadata } from '../../types/resource/interfaces.js';
// Removed incorrect import: import { ResourceMetadata } from '../../types/resource/metadata.js';
import { ResourceAnalysisResult } from '../../types/resource/resourceAnalysis.js'; // Assuming this is the correct path
import { getResourceTypeDescription } from '../../constants/resource/resourceTypeMetadata.js';

export interface ResourceFilter {
  type?: BinaryResourceType; // Removed TuningResourceType
  name?: string;
  path?: string;
  minSize?: number;
  maxSize?: number;
  minTimestamp?: number;
  maxTimestamp?: number;
  isCompressed?: boolean;
  isEncrypted?: boolean;
  isModified?: boolean;
  hasConflicts?: boolean;
  hasDependencies?: boolean;
  hasOverrides?: boolean;
  hasCustomData?: boolean;
  customDataFilter?: (data: any) => boolean;
}

export function filterResources(resources: ResourceMetadata[], filter: ResourceFilter): ResourceMetadata[] {
  return resources.filter(resource => {
    // Use type assertion (as any) for properties not defined on ResourceMetadata
    // NOTE: Accessing 'type' directly on ResourceMetadata is incorrect based on its definition.
    // This logic needs revision if filtering by type is required. It might need ResourceKey[] as input.
    // if (filter.type && (resource as any).type !== filter.type) {
    //   return false;
    // }

    if (filter.name && !resource.name.includes(filter.name)) {
      return false;
    }

    if (filter.path && !resource.path.includes(filter.path)) {
      return false;
    }

    if (filter.minSize && resource.size < filter.minSize) {
      return false;
    }

    if (filter.maxSize && resource.size > filter.maxSize) {
      return false;
    }

    if (filter.minTimestamp && resource.timestamp < filter.minTimestamp) {
      return false;
    }

    if (filter.maxTimestamp && resource.timestamp > filter.maxTimestamp) {
      return false;
    }

    // Use type assertion (as any) for properties not defined on ResourceMetadata
    // These properties (isCompressed, isEncrypted, isModified, overrides) are not part of ResourceMetadata
    // This filtering logic needs to be based on available ResourceMetadata fields or use a different input type (e.g., ExtendedResource).
    // if (filter.isCompressed !== undefined && (resource as any).isCompressed !== filter.isCompressed) {
    //   return false;
    // }
    // if (filter.isEncrypted !== undefined && (resource as any).isEncrypted !== filter.isEncrypted) {
    //   return false;
    // }
    // if (filter.isModified !== undefined && (resource as any).isModified !== filter.isModified) {
    //   return false;
    // }
    // if (filter.hasOverrides !== undefined) {
    //   const hasOverrides = (resource as any).overrides && (resource as any).overrides.length > 0;
    //   if (hasOverrides !== filter.hasOverrides) {
    //     return false;
    //   }
    // }


    if (filter.hasConflicts !== undefined) {
      const hasConflicts = resource.conflicts && resource.conflicts.length > 0;
      if (hasConflicts !== filter.hasConflicts) {
        return false;
      }
    }

    if (filter.hasDependencies !== undefined) {
      const hasDependencies = resource.dependencies && resource.dependencies.length > 0;
      if (hasDependencies !== filter.hasDependencies) {
        return false;
      }
    }


    if (filter.hasCustomData !== undefined) {
      const hasCustomData = resource.customData !== undefined;
      if (hasCustomData !== filter.hasCustomData) {
        return false;
      }
    }

    if (filter.customDataFilter && resource.customData) {
      if (!filter.customDataFilter(resource.customData)) {
        return false;
      }
    }

    return true;
  });
}

export function filterAnalysisResults(results: ResourceAnalysisResult[], filter: ResourceFilter): ResourceAnalysisResult[] {
  return results.filter(result => {
    // Assuming ResourceAnalysisResult has a 'metadata' property of type ResourceMetadata
    const metadata = result.metadata;
    // Need to adjust filtering logic based on available fields in ResourceMetadata
    // The current filterResources function might not work correctly with just ResourceMetadata
    // For now, returning true to avoid breaking, but this needs review.
    // return filterResources([metadata], filter).length > 0;
    return true; // Placeholder - review filtering logic
  });
}

export function groupResourcesByType(resources: ResourceMetadata[]): Map<BinaryResourceType, ResourceMetadata[]> { // Removed TuningResourceType
  const groups = new Map<BinaryResourceType, ResourceMetadata[]>(); // Removed TuningResourceType

  for (const resource of resources) {
    // Use type assertion (as any) for properties not defined on ResourceMetadata
    // Cannot reliably get 'type' from ResourceMetadata. This function needs ResourceKey[] or similar.
    // const resourceType = (resource as any).type as BinaryResourceType;
    // if (!groups.has(resourceType)) {
    //   groups.set(resourceType, []);
    // }
    // groups.get(resourceType)!.push(resource);
  }
  console.warn("groupResourcesByType cannot function correctly with only ResourceMetadata input.");
  return groups; // Returning potentially empty map
}

export function summarizeResourceGroups(groups: Map<BinaryResourceType, ResourceMetadata[]>): string { // Removed TuningResourceType
  let summary = 'Resource Summary:\n';

  if (groups.size === 0) {
    return summary + " (No type information available from metadata to group resources)";
  }

  for (const [type, resources] of groups.entries()) {
    const description = getResourceTypeDescription(type);
    const count = resources.length;
    const totalSize = resources.reduce((sum, r) => sum + r.size, 0);
    const avgSize = count > 0 ? Math.round(totalSize / count) : 0;

    summary += `\n${description}:\n`;
    summary += `  Count: ${count}\n`;
    summary += `  Total Size: ${formatSize(totalSize)}\n`;
    summary += `  Average Size: ${formatSize(avgSize)}\n`;
  }

  return summary;
}

function formatSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${Math.round(size * 100) / 100} ${units[unitIndex]}`;
}
