/**
 * SimDataVersionRegistry
 *
 * This class maintains a registry of known SimData versions and provides
 * functionality to track and discover new versions.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';

const log = {
    info: (message: string) => console.log(`[SimDataVersionRegistry] INFO: ${message}`),
    error: (message: string) => console.error(`[SimDataVersionRegistry] ERROR: ${message}`),
    warn: (message: string) => console.warn(`[SimDataVersionRegistry] WARN: ${message}`),
    debug: (message: string) => console.debug(`[SimDataVersionRegistry] DEBUG: ${message}`)
};

export interface VersionInfo {
    version: number;
    count: number;
    firstSeen: Date;
    lastSeen: Date;
    schemaNames: Set<string>;
    modNames: Set<string>;
    isStandard: boolean;
    isSpecial: boolean;
    hasCustomHandler: boolean;
}

export class SimDataVersionRegistry {
    private static instance: SimDataVersionRegistry;
    private databaseService: DatabaseService;
    private versionMap: Map<number, VersionInfo> = new Map();
    private initialized: boolean = false;

    private constructor(databaseService: DatabaseService) {
        this.databaseService = databaseService;
    }

    /**
     * Get the singleton instance of the registry
     */
    public static getInstance(databaseService: DatabaseService): SimDataVersionRegistry {
        if (!SimDataVersionRegistry.instance) {
            SimDataVersionRegistry.instance = new SimDataVersionRegistry(databaseService);
        }
        return SimDataVersionRegistry.instance;
    }

    /**
     * Initialize the registry from the database
     */
    public async initialize(): Promise<void> {
        if (this.initialized) return;

        try {
            // Create the table if it doesn't exist
            await this.databaseService.run(`
                CREATE TABLE IF NOT EXISTS simdata_versions (
                    version INTEGER PRIMARY KEY,
                    count INTEGER NOT NULL DEFAULT 0,
                    first_seen TEXT NOT NULL,
                    last_seen TEXT NOT NULL,
                    schema_names TEXT NOT NULL,
                    mod_names TEXT NOT NULL,
                    is_standard INTEGER NOT NULL DEFAULT 0,
                    is_special INTEGER NOT NULL DEFAULT 0,
                    has_custom_handler INTEGER NOT NULL DEFAULT 0
                )
            `);

            // Load existing versions from the database
            const rows = await this.databaseService.all('SELECT * FROM simdata_versions');

            for (const row of rows) {
                this.versionMap.set(row.version, {
                    version: row.version,
                    count: row.count,
                    firstSeen: new Date(row.first_seen),
                    lastSeen: new Date(row.last_seen),
                    schemaNames: new Set(JSON.parse(row.schema_names)),
                    modNames: new Set(JSON.parse(row.mod_names)),
                    isStandard: row.is_standard === 1,
                    isSpecial: row.is_special === 1,
                    hasCustomHandler: row.has_custom_handler === 1
                });
            }

            this.initialized = true;
            log.info(`Initialized SimData version registry with ${this.versionMap.size} versions`);
        } catch (error) {
            log.error(`Error initializing SimData version registry: ${error}`);
        }
    }

    /**
     * Register a version sighting
     */
    public async registerVersion(
        version: number,
        schemaName: string = 'Unknown',
        modName: string = 'Unknown',
        isStandard: boolean = false,
        isSpecial: boolean = false,
        hasCustomHandler: boolean = false
    ): Promise<void> {
        if (!this.initialized) await this.initialize();

        const now = new Date();
        let versionInfo = this.versionMap.get(version);

        if (versionInfo) {
            // Update existing version info
            versionInfo.count++;
            versionInfo.lastSeen = now;
            versionInfo.schemaNames.add(schemaName);
            versionInfo.modNames.add(modName);

            // Update database
            await this.databaseService.run(
                `UPDATE simdata_versions
                 SET count = ?, last_seen = ?, schema_names = ?, mod_names = ?
                 WHERE version = ?`,
                [
                    versionInfo.count,
                    now.toISOString(),
                    JSON.stringify(Array.from(versionInfo.schemaNames)),
                    JSON.stringify(Array.from(versionInfo.modNames)),
                    version
                ]
            );
        } else {
            // Create new version info
            versionInfo = {
                version,
                count: 1,
                firstSeen: now,
                lastSeen: now,
                schemaNames: new Set([schemaName]),
                modNames: new Set([modName]),
                isStandard,
                isSpecial,
                hasCustomHandler
            };
            this.versionMap.set(version, versionInfo);

            // Insert into database
            await this.databaseService.run(
                `INSERT INTO simdata_versions
                 (version, count, first_seen, last_seen, schema_names, mod_names, is_standard, is_special, has_custom_handler)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    version,
                    1,
                    now.toISOString(),
                    now.toISOString(),
                    JSON.stringify([schemaName]),
                    JSON.stringify([modName]),
                    isStandard ? 1 : 0,
                    isSpecial ? 1 : 0,
                    hasCustomHandler ? 1 : 0
                ]
            );

            // Log discovery of new version
            if (!isStandard && !isSpecial) {
                log.info(`Discovered new SimData version: ${version} in schema ${schemaName} from mod ${modName}`);
            }
        }
    }

    /**
     * Get information about a specific version
     */
    public getVersionInfo(version: number): VersionInfo | undefined {
        return this.versionMap.get(version);
    }

    /**
     * Get all registered versions
     */
    public getAllVersions(): VersionInfo[] {
        return Array.from(this.versionMap.values());
    }

    /**
     * Get unknown versions (versions without custom handlers)
     */
    public getUnknownVersions(): VersionInfo[] {
        return Array.from(this.versionMap.values())
            .filter(info => !info.isStandard && !info.hasCustomHandler)
            .sort((a, b) => b.count - a.count); // Sort by frequency (most common first)
    }

    /**
     * Get statistics about version usage
     */
    public getVersionStatistics(): {
        totalVersions: number;
        standardVersions: number;
        specialVersions: number;
        unknownVersions: number;
        mostCommonVersion: number;
        mostCommonCount: number;
        recentlyDiscovered: VersionInfo[];
    } {
        const versions = Array.from(this.versionMap.values());
        const standardVersions = versions.filter(v => v.isStandard);
        const specialVersions = versions.filter(v => v.isSpecial);
        const unknownVersions = versions.filter(v => !v.isStandard && !v.hasCustomHandler);

        // Find most common version
        let mostCommonVersion = 0;
        let mostCommonCount = 0;

        for (const info of versions) {
            if (info.count > mostCommonCount) {
                mostCommonCount = info.count;
                mostCommonVersion = info.version;
            }
        }

        // Get recently discovered versions (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const recentlyDiscovered = versions
            .filter(v => v.firstSeen >= thirtyDaysAgo)
            .sort((a, b) => b.firstSeen.getTime() - a.firstSeen.getTime());

        return {
            totalVersions: versions.length,
            standardVersions: standardVersions.length,
            specialVersions: specialVersions.length,
            unknownVersions: unknownVersions.length,
            mostCommonVersion,
            mostCommonCount,
            recentlyDiscovered
        };
    }
}
