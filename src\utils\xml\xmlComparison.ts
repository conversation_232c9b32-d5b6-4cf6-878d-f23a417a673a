/**
 * XML comparison utilities using the fast-xml-parser library
 */
import { XMLParser, XMLBuilder } from 'fast-xml-parser';
import { Logger } from '../../utils/logging/logger.js';
import { calculateSimilarity, SimilarityAlgorithm } from '../string/enhancedStringSimilarity.js';

// Logger instance
const logger = new Logger('XMLComparison');

// XML parser options
const parserOptions = {
    ignoreAttributes: false,
    attributeNamePrefix: '@_',
    allowBooleanAttributes: true,
    parseAttributeValue: true,
    parseTagValue: true,
    trimValues: true,
    isArray: (name: string, jpath: string) => {
        // Elements that should always be treated as arrays
        const arrayElements = ['trait', 'slot', 'tuning', 'instance', 'element', 'value', 'item'];
        return arrayElements.includes(name);
    }
};

// XML builder options
const builderOptions = {
    ignoreAttributes: false,
    attributeNamePrefix: '@_',
    format: true,
    indentBy: '  '
};

// Create parser and builder instances
const parser = new XMLParser(parserOptions);
const builder = new XMLBuilder(builderOptions);

/**
 * Parse XML string into a JavaScript object
 * @param xmlString XML string to parse
 * @returns Parsed XML object or null if parsing fails
 */
export function parseXML(xmlString: string): any {
    try {
        return parser.parse(xmlString);
    } catch (error) {
        logger.error(`Error parsing XML: ${error.message || error}`);
        return null;
    }
}

/**
 * Convert JavaScript object to XML string
 * @param obj JavaScript object to convert
 * @returns XML string or null if conversion fails
 */
export function buildXML(obj: any): string | null {
    try {
        return builder.build(obj);
    } catch (error) {
        logger.error(`Error building XML: ${error.message || error}`);
        return null;
    }
}

/**
 * Difference between two XML elements
 */
export interface XMLDifference {
    /**
     * Path to the element that differs
     */
    path: string;

    /**
     * Value in the first XML
     */
    value1: any;

    /**
     * Value in the second XML
     */
    value2: any;

    /**
     * Type of difference
     */
    type: 'added' | 'removed' | 'changed';

    /**
     * Importance of the difference (0.0 to 1.0)
     */
    importance: number;
}

/**
 * Result of XML comparison
 */
export interface XMLComparisonResult {
    /**
     * Whether the XMLs are identical
     */
    identical: boolean;

    /**
     * Similarity score between 0.0 and 1.0
     */
    similarity: number;

    /**
     * List of differences between the XMLs
     */
    differences: XMLDifference[];

    /**
     * Whether the differences are significant
     */
    significant: boolean;

    /**
     * Critical paths that differ
     */
    criticalDifferences: XMLDifference[];
}

/**
 * Options for XML comparison
 */
export interface XMLComparisonOptions {
    /**
     * Whether to ignore element order
     * Default: true
     */
    ignoreOrder?: boolean;

    /**
     * Whether to ignore whitespace
     * Default: true
     */
    ignoreWhitespace?: boolean;

    /**
     * Whether to ignore comments
     * Default: true
     */
    ignoreComments?: boolean;

    /**
     * Whether to ignore attributes
     * Default: false
     */
    ignoreAttributes?: boolean;

    /**
     * Paths to ignore during comparison
     * Default: []
     */
    ignorePaths?: string[];

    /**
     * Critical paths that should trigger a significant difference
     * Default: []
     */
    criticalPaths?: string[];

    /**
     * Similarity threshold for considering XMLs similar
     * Default: 0.8
     */
    similarityThreshold?: number;
}

/**
 * Compare two XML strings and return the differences
 * @param xml1 First XML string
 * @param xml2 Second XML string
 * @param options Comparison options
 * @returns Comparison result
 */
export function compareXML(
    xml1: string,
    xml2: string,
    options: XMLComparisonOptions = {}
): XMLComparisonResult {
    // Set default options
    const ignoreOrder = options.ignoreOrder !== false;
    const ignoreWhitespace = options.ignoreWhitespace !== false;
    const ignoreComments = options.ignoreComments !== false;
    const ignoreAttributes = options.ignoreAttributes || false;
    const ignorePaths = options.ignorePaths || [];
    const criticalPaths = options.criticalPaths || [];
    const similarityThreshold = options.similarityThreshold || 0.8;

    // Parse XML strings
    const obj1 = parseXML(xml1);
    const obj2 = parseXML(xml2);

    // Handle parsing errors
    if (!obj1 || !obj2) {
        return {
            identical: false,
            similarity: 0,
            differences: [],
            significant: true,
            criticalDifferences: []
        };
    }

    // Find differences
    const differences: XMLDifference[] = [];
    findDifferences(obj1, obj2, '', differences, {
        ignoreOrder,
        ignoreAttributes,
        ignorePaths
    });

    // Calculate similarity
    const similarity = calculateXMLSimilarity(xml1, xml2, differences);

    // Identify critical differences
    const criticalDifferences = differences.filter(diff =>
        criticalPaths.some(path => diff.path.startsWith(path))
    );

    // Determine if differences are significant
    const significant = similarity < similarityThreshold || criticalDifferences.length > 0;

    return {
        identical: differences.length === 0,
        similarity,
        differences,
        significant,
        criticalDifferences
    };
}

/**
 * Find differences between two objects recursively
 * @param obj1 First object
 * @param obj2 Second object
 * @param path Current path
 * @param differences Array to store differences
 * @param options Comparison options
 */
function findDifferences(
    obj1: any,
    obj2: any,
    path: string,
    differences: XMLDifference[],
    options: {
        ignoreOrder: boolean;
        ignoreAttributes: boolean;
        ignorePaths: string[];
    }
): void {
    // Skip ignored paths
    if (options.ignorePaths.some(ignorePath => path.startsWith(ignorePath))) {
        return;
    }

    // Handle null or undefined values
    if (obj1 === null || obj1 === undefined) {
        if (obj2 !== null && obj2 !== undefined) {
            differences.push({
                path,
                value1: obj1,
                value2: obj2,
                type: 'added',
                importance: calculateImportance(path)
            });
        }
        return;
    }
    if (obj2 === null || obj2 === undefined) {
        differences.push({
            path,
            value1: obj1,
            value2: obj2,
            type: 'removed',
            importance: calculateImportance(path)
        });
        return;
    }

    // Handle different types
    if (typeof obj1 !== typeof obj2) {
        differences.push({
            path,
            value1: obj1,
            value2: obj2,
            type: 'changed',
            importance: calculateImportance(path)
        });
        return;
    }

    // Handle arrays
    if (Array.isArray(obj1) && Array.isArray(obj2)) {
        compareArrays(obj1, obj2, path, differences, options);
        return;
    }

    // Handle objects
    if (typeof obj1 === 'object' && typeof obj2 === 'object') {
        compareObjects(obj1, obj2, path, differences, options);
        return;
    }

    // Handle primitive values
    if (obj1 !== obj2) {
        differences.push({
            path,
            value1: obj1,
            value2: obj2,
            type: 'changed',
            importance: calculateImportance(path)
        });
    }
}

/**
 * Compare two arrays and find differences
 * @param arr1 First array
 * @param arr2 Second array
 * @param path Current path
 * @param differences Array to store differences
 * @param options Comparison options
 */
function compareArrays(
    arr1: any[],
    arr2: any[],
    path: string,
    differences: XMLDifference[],
    options: {
        ignoreOrder: boolean;
        ignoreAttributes: boolean;
        ignorePaths: string[];
    }
): void {
    if (options.ignoreOrder) {
        // Compare arrays ignoring order
        const unmatched1 = [...arr1];
        const unmatched2 = [...arr2];

        // Find matching elements
        for (let i = unmatched1.length - 1; i >= 0; i--) {
            const item1 = unmatched1[i];
            let matched = false;

            for (let j = unmatched2.length - 1; j >= 0; j--) {
                const item2 = unmatched2[j];
                const tempDiffs: XMLDifference[] = [];

                // Check if items match
                findDifferences(item1, item2, `${path}[*]`, tempDiffs, options);

                if (tempDiffs.length === 0) {
                    // Items match, remove from unmatched lists
                    unmatched1.splice(i, 1);
                    unmatched2.splice(j, 1);
                    matched = true;
                    break;
                }
            }

            if (!matched) {
                // Item in arr1 has no match in arr2
                differences.push({
                    path: `${path}[${i}]`,
                    value1: item1,
                    value2: undefined,
                    type: 'removed',
                    importance: calculateImportance(path)
                });
            }
        }

        // Add remaining unmatched items from arr2
        for (let i = 0; i < unmatched2.length; i++) {
            differences.push({
                path: `${path}[*]`,
                value1: undefined,
                value2: unmatched2[i],
                type: 'added',
                importance: calculateImportance(path)
            });
        }
    } else {
        // Compare arrays considering order
        const maxLength = Math.max(arr1.length, arr2.length);

        for (let i = 0; i < maxLength; i++) {
            if (i >= arr1.length) {
                // Element exists in arr2 but not in arr1
                differences.push({
                    path: `${path}[${i}]`,
                    value1: undefined,
                    value2: arr2[i],
                    type: 'added',
                    importance: calculateImportance(path)
                });
            } else if (i >= arr2.length) {
                // Element exists in arr1 but not in arr2
                differences.push({
                    path: `${path}[${i}]`,
                    value1: arr1[i],
                    value2: undefined,
                    type: 'removed',
                    importance: calculateImportance(path)
                });
            } else {
                // Compare elements at the same index
                findDifferences(arr1[i], arr2[i], `${path}[${i}]`, differences, options);
            }
        }
    }
}

/**
 * Compare two objects and find differences
 * @param obj1 First object
 * @param obj2 Second object
 * @param path Current path
 * @param differences Array to store differences
 * @param options Comparison options
 */
function compareObjects(
    obj1: any,
    obj2: any,
    path: string,
    differences: XMLDifference[],
    options: {
        ignoreOrder: boolean;
        ignoreAttributes: boolean;
        ignorePaths: string[];
    }
): void {
    // Get all keys from both objects
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    const allKeys = new Set([...keys1, ...keys2]);

    // Check each key
    for (const key of allKeys) {
        // Skip attributes if ignoreAttributes is true
        if (options.ignoreAttributes && key.startsWith('@_')) {
            continue;
        }

        const newPath = path ? `${path}.${key}` : key;

        if (!(key in obj1)) {
            // Key exists in obj2 but not in obj1
            differences.push({
                path: newPath,
                value1: undefined,
                value2: obj2[key],
                type: 'added',
                importance: calculateImportance(newPath)
            });
        } else if (!(key in obj2)) {
            // Key exists in obj1 but not in obj2
            differences.push({
                path: newPath,
                value1: obj1[key],
                value2: undefined,
                type: 'removed',
                importance: calculateImportance(newPath)
            });
        } else {
            // Key exists in both objects, compare values
            findDifferences(obj1[key], obj2[key], newPath, differences, options);
        }
    }
}

/**
 * Calculate the importance of a difference based on its path
 * @param path Path to the element
 * @returns Importance score between 0.0 and 1.0
 */
function calculateImportance(path: string): number {
    // Critical paths have higher importance
    const criticalPathPatterns = [
        /\.instance_id$/i,
        /\.guid$/i,
        /\.type$/i,
        /\.name$/i,
        /\.value$/i,
        /\.key$/i,
        /\.tunable$/i,
        /\.class$/i,
        /\.module$/i,
        /\.function$/i,
        /\.reference$/i
    ];

    // Check if path matches any critical pattern
    for (const pattern of criticalPathPatterns) {
        if (pattern.test(path)) {
            return 1.0;
        }
    }

    // Calculate importance based on path depth
    // Deeper paths are generally less important
    const depth = path.split(/[.\[\]]/).filter(Boolean).length;
    return Math.max(0.1, 1.0 - (depth * 0.1));
}

/**
 * Calculate similarity between two XML strings
 * @param xml1 First XML string
 * @param xml2 Second XML string
 * @param differences List of differences
 * @returns Similarity score between 0.0 and 1.0
 */
function calculateXMLSimilarity(
    xml1: string,
    xml2: string,
    differences: XMLDifference[]
): number {
    // If there are no differences, the XMLs are identical
    if (differences.length === 0) {
        return 1.0;
    }

    // Calculate weighted importance of differences
    const totalImportance = differences.reduce((sum, diff) => sum + diff.importance, 0);

    // Calculate similarity based on string comparison and differences
    const stringSimilarity = calculateSimilarity(xml1, xml2, {
        algorithm: SimilarityAlgorithm.COSINE,
        normalize: true
    });

    // Calculate similarity based on differences
    // More differences with higher importance = lower similarity
    const diffSimilarity = Math.max(0, 1.0 - (totalImportance / 10.0));

    // Combine both similarity measures
    return (stringSimilarity * 0.3) + (diffSimilarity * 0.7);
}

/**
 * Check if two XML strings have a conflict
 * @param xml1 First XML string
 * @param xml2 Second XML string
 * @param options Comparison options
 * @returns True if a conflict is detected, false otherwise
 */
export function hasXMLConflict(
    xml1: string,
    xml2: string,
    options: XMLComparisonOptions = {}
): boolean {
    const result = compareXML(xml1, xml2, options);
    return result.significant;
}

/**
 * Get a summary of differences between two XML strings
 * @param xml1 First XML string
 * @param xml2 Second XML string
 * @param options Comparison options
 * @returns Summary of differences
 */
export function getXMLDifferenceSummary(
    xml1: string,
    xml2: string,
    options: XMLComparisonOptions = {}
): string {
    const result = compareXML(xml1, xml2, options);

    if (result.identical) {
        return 'The XML documents are identical.';
    }

    let summary = `XML similarity: ${Math.round(result.similarity * 100)}%\n`;
    summary += `Total differences: ${result.differences.length}\n`;

    if (result.criticalDifferences.length > 0) {
        summary += `Critical differences: ${result.criticalDifferences.length}\n`;
        summary += 'Critical paths that differ:\n';

        for (const diff of result.criticalDifferences) {
            summary += `- ${diff.path}: ${JSON.stringify(diff.value1)} -> ${JSON.stringify(diff.value2)}\n`;
        }
    }

    return summary;
}
