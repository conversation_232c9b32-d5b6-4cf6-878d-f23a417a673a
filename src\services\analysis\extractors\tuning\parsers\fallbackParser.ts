import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey as AppResourceKey } from '../../../../../types/resource/interfaces.js';
import { TuningParseResult } from '../types.js';
import { createTuningExtractionContext, handleTuningExtractionError } from '../error/tuningExtractorErrorHandler.js';

/**
 * Creates a minimal valid tuning resource when all other parsing methods fail
 * @param key The resource key
 * @param resourceId The resource ID
 * @param log The logger instance
 * @returns A minimal valid tuning resource
 */
export function createMinimalTuningResource(
    key: AppResourceKey,
    resourceId: number,
    log: Logger
): TuningParseResult {
    log.warn(`Creating minimal valid tuning resource for ${key.instance.toString(16)} (Type: 0x${key.type.toString(16)})`);
    
    // Create a minimal valid tuning resource
    const tuningResource = {
        dom: {
            root: {
                tag: 'I',
                attributes: {
                    n: `tuning_${key.type.toString(16)}_${key.instance.toString(16)}`,
                    s: key.instance.toString(),
                    m: '0',
                    t: '0'
                },
                children: []
            }
        }
    };
    
    return {
        tuningResource,
        parsedWithS4TK: false,
        contentSnippet: `[Minimal Tuning: ${key.instance.toString(16)}]`
    };
}

/**
 * Extracts tuning information directly from the raw XML string
 * @param xmlString The raw XML string
 * @param key The resource key
 * @param resourceId The resource ID
 * @param log The logger instance
 * @returns The extracted tuning information
 */
export function extractFromRawXml(
    xmlString: string,
    key: AppResourceKey,
    resourceId: number,
    log: Logger
): TuningParseResult {
    try {
        log.info(`Attempting to extract tuning information directly from raw XML for ${key.instance.toString(16)}`);
        
        // Try to extract the tuning name using regex
        const tuningName = extractTuningNameFromRawXml(xmlString, log);
        
        // Create a minimal tuning resource
        const tuningResource = {
            dom: {
                root: {
                    tag: 'I',
                    attributes: {
                        n: tuningName || `tuning_${key.type.toString(16)}_${key.instance.toString(16)}`,
                        s: key.instance.toString(),
                        m: '0',
                        t: '0'
                    },
                    children: []
                }
            }
        };
        
        // Try to extract L and T elements
        const lElements = extractLElementsFromRawXml(xmlString, log);
        if (lElements.length > 0) {
            tuningResource.dom.root.children = lElements;
        }
        
        return {
            tuningResource,
            parsedWithS4TK: false,
            contentSnippet: `[Raw XML Extraction: ${tuningName || key.instance.toString(16)}]`
        };
    } catch (error: any) {
        const context = createTuningExtractionContext(key, resourceId, 'RawXmlExtraction', {
            xmlLength: xmlString.length
        });
        
        // Handle the error
        return handleTuningExtractionError(error, context, log);
    }
}

/**
 * Extracts the tuning name from raw XML
 * @param xmlString The raw XML string
 * @param log The logger instance
 * @returns The extracted tuning name or undefined if not found
 */
function extractTuningNameFromRawXml(xmlString: string, log: Logger): string | undefined {
    try {
        // Try to extract the n attribute using regex
        const nAttributeMatch = xmlString.match(/<I[^>]*n="([^"]*)"[^>]*>/);
        if (nAttributeMatch && nAttributeMatch[1]) {
            log.debug(`Extracted tuning name using regex: ${nAttributeMatch[1]}`);
            return nAttributeMatch[1];
        }
    } catch (error: any) {
        log.error(`Error extracting tuning name with regex: ${error.message}`);
    }
    return undefined;
}

/**
 * Extracts L elements from raw XML
 * @param xmlString The raw XML string
 * @param log The logger instance
 * @returns The extracted L elements
 */
function extractLElementsFromRawXml(xmlString: string, log: Logger): any[] {
    const lElements: any[] = [];
    
    try {
        // Try to extract L elements using regex
        const lElementMatches = xmlString.match(/<L[^>]*>(.*?)<\/L>/gs);
        if (lElementMatches && lElementMatches.length > 0) {
            log.debug(`Found ${lElementMatches.length} L elements in raw XML`);
            
            lElementMatches.forEach((lElementMatch, index) => {
                // Try to extract T elements within this L element
                const tElementMatches = lElementMatch.match(/<T[^>]*>(.*?)<\/T>/gs);
                if (tElementMatches && tElementMatches.length > 0) {
                    log.debug(`Found ${tElementMatches.length} T elements in L element ${index}`);
                    
                    const tElements: any[] = [];
                    tElementMatches.forEach(tElementMatch => {
                        const tContent = tElementMatch.replace(/<T[^>]*>(.*?)<\/T>/, '$1');
                        tElements.push({
                            tag: 'T',
                            innerValue: tContent.trim()
                        });
                    });
                    
                    lElements.push({
                        tag: 'L',
                        children: tElements
                    });
                }
            });
        }
    } catch (error: any) {
        log.error(`Error extracting L elements with regex: ${error.message}`);
    }
    
    return lElements;
}
