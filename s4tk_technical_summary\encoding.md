# S4TK Package: @s4tk/encoding

Based on documentation found in `docs/technical/encoding/`.

## Overview

This package provides utility classes (`BinaryDecoder` and `BinaryEncoder`) to simplify reading from and writing to Node.js `Buffer` objects, particularly for binary file formats like those used in Sims 4 packages. It manages an internal offset to track the current position within the buffer.

## Installation

```sh
npm i @s4tk/encoding
```

## API

### Base Class: `BinaryEncodingBase`

An abstract base class providing common functionality:

*   `constructor(buffer: Buffer)`: Initializes with a buffer.
*   `buffer: Buffer` (getter): Accesses the underlying buffer.
*   `skip(offset: number | bigint): number`: Advances the internal offset.
*   `seek(offset: number | bigint): void`: Sets the internal offset to a specific position.
*   `tell(): number`: Returns the current offset.
*   `savePos<T>(fn: () => T): T`: Executes a function `fn` while preserving and restoring the original offset.
*   `isEOF(): boolean`: Checks if the offset is at the end of the buffer.
*   `getEncoder(): BinaryEncoder`: Returns a new `BinaryEncoder` for the same buffer.
*   `getDecoder(): BinaryDecoder`: Returns a new `BinaryDecoder` for the same buffer.

### Class: `BinaryDecoder`

Extends `BinaryEncodingBase` for reading data.

*   `constructor(buffer: Buffer)`: Creates a decoder for the given buffer.
*   **Reading Methods (advance offset after read):**
    *   `charsUtf8(num: number): string`: Reads `num` bytes as a UTF-8 string.
    *   `charsBase64(num: number): string`: Reads `num` bytes as a Base64 string.
    *   `string(): string`: Reads a null-terminated UTF-8 string.
    *   `boolean(): boolean`: Reads 1 byte as boolean (non-zero is true).
    *   `byte(): number`: Alias for `uint8()`.
    *   `bytes(num: number): number[]`: Reads `num` raw bytes into an array.
    *   `slice(size: number): Buffer`: Reads `size` bytes into a new Buffer.
    *   `uint8(): number`
    *   `uint16(): number` (Little Endian)
    *   `uint32(): number` (Little Endian)
    *   `uint64(): bigint` (Little Endian)
    *   `int8(): number`
    *   `int16(): number` (Little Endian)
    *   `int32(): number` (Little Endian)
    *   `int64(): bigint` (Little Endian)
    *   `float(): number` (Little Endian)

### Class: `BinaryEncoder`

Extends `BinaryEncodingBase` for writing data.

*   `constructor(buffer: Buffer)`: Creates an encoder for the given buffer.
*   `static alloc(size: number): BinaryEncoder`: Creates a new buffer of `size` and returns an encoder for it.
*   **Writing Methods (advance offset after write):**
    *   `charsUtf8(value: string): void`: Writes string as UTF-8.
    *   `charsBase64(value: string): void`: Writes string as Base64.
    *   `boolean(value: boolean): void`: Writes boolean as 1 or 0 (UInt8).
    *   `byte(value: number): void`: Alias for `uint8()`.
    *   `bytes(values: number[] | Uint8Array): void`: Writes an array of bytes.
    *   `uint8(value: number): void`
    *   `uint16(value: number): void` (Little Endian)
    *   `uint32(value: number): void` (Little Endian)
    *   `uint64(value: number | bigint): void` (Little Endian)
    *   `int8(value: number): void`
    *   `int16(value: number): void` (Little Endian)
    *   `int32(value: number): void` (Little Endian)
    *   `int64(value: number | bigint): void` (Little Endian)
    *   `float(value: number): void` (Little Endian)