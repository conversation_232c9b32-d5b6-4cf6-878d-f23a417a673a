/**
 * SimData Schema Parser
 *
 * This module provides a comprehensive parser for SimData schemas that extracts
 * semantic meaning, identifies relationships, and detects potential conflicts.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ParsedSimData, SimDataSchema } from '../simDataParser.js';
import {
    SchemaAnalysisResult,
    SchemaCompatibilityInfo,
    SchemaRelationshipInfo,
    SchemaPurposeInfo
} from './schemaInterfaces.js';
import { SchemaAnalyzer } from './schemaAnalyzer.js';
import { SchemaCompatibilityAnalyzer } from './schemaCompatibilityAnalyzer.js';
import { SchemaRepository } from './schemaRepository.js';
import { DatabaseService } from '../../../../databaseService.js';
import { ColumnAnalyzer } from './columnAnalyzer.js';
import { schemaCatalog } from './schemaCatalog.js';
import { SchemaCache } from './schemaCache.js';
import { SchemaDatabase } from './schemaDatabase.js';

const log = new Logger('SimDataSchemaParser');

/**
 * SimData Schema Parser
 * Responsible for parsing and analyzing SimData schemas
 */
export class SimDataSchemaParser {
    private logger: Logger;
    private schemaAnalyzer: SchemaAnalyzer;
    private compatibilityAnalyzer: SchemaCompatibilityAnalyzer;
    private columnAnalyzer: ColumnAnalyzer;
    private schemaRepository: SchemaRepository | null = null;
    private schemaDatabase: SchemaDatabase | null = null;
    private schemaCache: SchemaCache;
    private databaseService?: DatabaseService;

    constructor(databaseService?: DatabaseService, logger?: Logger) {
        this.logger = logger || log;
        this.schemaAnalyzer = new SchemaAnalyzer();
        this.compatibilityAnalyzer = new SchemaCompatibilityAnalyzer();
        this.columnAnalyzer = new ColumnAnalyzer(this.logger);
        this.schemaCache = SchemaCache.getInstance();
        this.databaseService = databaseService;

        if (databaseService) {
            this.schemaRepository = new SchemaRepository(databaseService);
            this.schemaDatabase = SchemaDatabase.getInstance(databaseService);
        }
    }

    /**
     * Parse and analyze a SimData schema
     * @param simData The parsed SimData object
     * @param modId Optional mod ID for schema registration
     * @returns Detailed schema analysis
     */
    public async parseSchema(simData: ParsedSimData, modId?: number): Promise<SchemaAnalysisResult> {
        if (!simData.schema) {
            this.logger.warn('No schema found in SimData');
            return {
                schema: {
                    name: 'Unknown',
                    schemaId: 0,
                    hash: 0,
                    columns: []
                },
                columnSemantics: {},
                category: 'Unknown',
                complexity: 0
            };
        }

        // Get schema information
        const schema = simData.schema;

        // Generate cache key
        const cacheKey = `${schema.name}_${schema.schemaId}_${schema.hash}`;

        // Check cache first
        const cachedAnalysis = this.schemaCache.getAnalysis(cacheKey);
        if (cachedAnalysis) {
            this.logger.debug(`Using cached analysis for schema ${schema.name}`);
            return cachedAnalysis;
        }

        // If we have a repository and mod ID, register the schema
        if (this.schemaRepository && modId !== undefined) {
            try {
                const analysis = await this.schemaRepository.registerSchema(schema, modId);

                // Cache the result
                this.schemaCache.setAnalysis(cacheKey, analysis);

                return analysis;
            } catch (error) {
                this.logger.error(`Error registering schema: ${error}`);
                // Fall back to direct analysis
            }
        }

        // If we have a schema database, register the schema
        if (this.schemaDatabase) {
            try {
                await this.schemaDatabase.registerSchema(schema);
            } catch (error) {
                this.logger.error(`Error registering schema with database: ${error}`);
                // Continue with direct analysis
            }
        }

        // Analyze the schema directly
        const analysis = await this.analyzeSchema(schema);

        // Cache the result
        this.schemaCache.setAnalysis(cacheKey, analysis);

        return analysis;
    }

    /**
     * Analyze a SimData schema
     * @param schema The SimData schema to analyze
     * @returns Schema analysis result
     */
    public async analyzeSchema(schema: SimDataSchema): Promise<SchemaAnalysisResult> {
        try {
            // Generate cache key
            const cacheKey = `${schema.name}_${schema.schemaId}_${schema.hash}_analysis`;

            // Check cache first
            const cachedAnalysis = this.schemaCache.getAnalysis(cacheKey);
            if (cachedAnalysis) {
                this.logger.debug(`Using cached detailed analysis for schema ${schema.name}`);
                return cachedAnalysis;
            }

            // Analyze column semantics
            const columnSemantics = this.columnAnalyzer.analyzeColumns(schema.columns, schema.name);

            // Analyze schema
            const basicAnalysis = this.schemaAnalyzer.analyzeSchema(schema);

            // Get schema purpose information
            let purposeInfo: SchemaPurposeInfo;

            // Try to get purpose from database first
            if (this.schemaDatabase) {
                const dbPurpose = await this.schemaDatabase.getSchemaPurpose(schema.name);
                if (dbPurpose) {
                    purposeInfo = dbPurpose;
                } else {
                    // Fall back to catalog analysis
                    purposeInfo = schemaCatalog.analyzeSchemaPurpose(schema);

                    // Register with database
                    try {
                        await this.schemaDatabase.registerSchemaPurpose(purposeInfo);
                    } catch (error) {
                        this.logger.error(`Error registering schema purpose: ${error}`);
                    }
                }
            } else {
                // No database, use catalog
                purposeInfo = schemaCatalog.analyzeSchemaPurpose(schema);
            }

            // Get schema relationship information
            let relationshipInfo: SchemaRelationshipInfo;

            // Try to get relationships from database first
            if (this.schemaDatabase) {
                const relatedSchemas = await this.schemaDatabase.getRelatedSchemas(schema.name);
                if (relatedSchemas.length > 0) {
                    // Convert to relationship info format
                    relationshipInfo = {
                        schemaName: schema.name,
                        relatedSchemas: relatedSchemas.map(name => ({
                            name,
                            relationship: 'related',
                            confidence: 80
                        }))
                    };
                } else {
                    // Fall back to catalog analysis
                    relationshipInfo = schemaCatalog.analyzeSchemaRelationships(schema);
                }
            } else {
                // No database, use catalog
                relationshipInfo = schemaCatalog.analyzeSchemaRelationships(schema);
            }

            // Create enhanced analysis result
            const analysisResult: SchemaAnalysisResult = {
                schema,
                columnSemantics,
                category: basicAnalysis.category,
                subcategory: basicAnalysis.subcategory || purposeInfo.subcategory,
                purpose: purposeInfo.purpose,
                gameplaySystem: purposeInfo.gameplaySystem,
                complexity: basicAnalysis.complexity,
                inheritance: basicAnalysis.inheritance,
                relationships: relationshipInfo,
                compatibility: await this.analyzeSchemaCompatibility(schema)
            };

            // Cache the result
            this.schemaCache.setAnalysis(cacheKey, analysisResult);

            return analysisResult;
        } catch (error) {
            this.logger.error(`Error analyzing schema ${schema.name}: ${error}`);

            // Return a basic analysis result on error
            return this.schemaAnalyzer.analyzeSchema(schema);
        }
    }

    /**
     * Analyze schema compatibility
     * @param schema The SimData schema to analyze
     * @returns Schema compatibility information
     */
    private async analyzeSchemaCompatibility(schema: SimDataSchema): Promise<SchemaCompatibilityInfo> {
        try {
            // Generate cache key
            const cacheKey = `${schema.name}_${schema.schemaId}_${schema.hash}_compatibility`;

            // Check cache first
            const cachedCompatibility = this.schemaCache.getAnalysis(cacheKey) as unknown as SchemaCompatibilityInfo;
            if (cachedCompatibility) {
                this.logger.debug(`Using cached compatibility analysis for schema ${schema.name}`);
                return cachedCompatibility;
            }

            // Default compatibility info
            const defaultCompatibility: SchemaCompatibilityInfo = {
                isCompatible: true,
                compatibilityScore: 100,
                incompatibleColumns: [],
                missingColumns: [],
                extraColumns: [],
                typeMismatches: [],
                criticalIssues: false,
                gameplayImpact: 'none',
                conflictDescription: 'No conflicts detected'
            };

            // Try to use schema database first
            if (this.schemaDatabase) {
                const existingSchema = await this.schemaDatabase.getSchemaByName(schema.name);

                if (existingSchema && existingSchema.schemaId !== schema.schemaId) {
                    // Compare schemas
                    const compatibility = await this.schemaDatabase.checkSchemaCompatibility(schema, existingSchema);

                    // Cache the result
                    this.schemaCache.setAnalysis(cacheKey, compatibility as unknown as SchemaAnalysisResult);

                    return compatibility;
                }
            }

            // Fall back to repository if available
            if (this.schemaRepository) {
                // Get existing schemas with the same name
                const existingSchemas = await this.schemaRepository.findSchemasByName(schema.name);

                if (existingSchemas.length > 0) {
                    // Compare with the most common schema
                    const mostCommonSchema = existingSchemas[0];

                    // Analyze compatibility
                    const compatibility = this.compatibilityAnalyzer.compareSchemas(schema, mostCommonSchema);

                    // Cache the result
                    this.schemaCache.setAnalysis(cacheKey, compatibility as unknown as SchemaAnalysisResult);

                    return compatibility;
                }
            }

            // No existing schemas to compare with
            this.schemaCache.setAnalysis(cacheKey, defaultCompatibility as unknown as SchemaAnalysisResult);
            return defaultCompatibility;
        } catch (error) {
            this.logger.error(`Error analyzing schema compatibility for ${schema.name}: ${error}`);

            // Return a basic compatibility info on error
            const defaultCompatibility: SchemaCompatibilityInfo = {
                isCompatible: true,
                compatibilityScore: 100,
                incompatibleColumns: [],
                missingColumns: [],
                extraColumns: [],
                typeMismatches: [],
                criticalIssues: false,
                gameplayImpact: 'none',
                conflictDescription: 'No conflicts detected'
            };

            return defaultCompatibility;
        }
    }

    /**
     * Compare two schemas for compatibility
     * @param schema1 The first schema
     * @param schema2 The second schema
     * @returns Schema compatibility information
     */
    public compareSchemas(schema1: SimDataSchema, schema2: SimDataSchema): SchemaCompatibilityInfo {
        return this.compatibilityAnalyzer.compareSchemas(schema1, schema2);
    }

    /**
     * Find schemas that might conflict with a given schema
     * @param schema The SimData schema
     * @returns Array of potentially conflicting schemas
     */
    public async findPotentialConflicts(schema: SimDataSchema): Promise<Array<{
        schemaName: string;
        compatibility: number;
        conflicts: string[];
    }>> {
        if (!this.schemaRepository) {
            this.logger.warn('Schema repository not available');
            return [];
        }

        return await this.schemaRepository.findPotentialConflicts(schema);
    }

    /**
     * Find schemas by pattern
     * @param pattern The pattern to match
     * @returns Array of matching schema names
     */
    public async findSchemas(pattern: string): Promise<string[]> {
        if (!this.schemaRepository) {
            this.logger.warn('Schema repository not available');
            return [];
        }

        return await this.schemaRepository.findSchemas(pattern);
    }

    /**
     * Initialize the schema repository
     */
    public async initializeRepository(): Promise<void> {
        if (!this.schemaRepository) {
            this.logger.warn('Schema repository not available');
            return;
        }

        await this.schemaRepository.initialize();
    }
}
