/**
 * File Stream Utilities
 *
 * This module provides utilities for working with file streams in a memory-efficient
 * and leak-free way. It ensures proper cleanup of resources and event listeners.
 */

import fs from 'fs';
import { createHash } from 'crypto';
import { Logger } from '../logging/logger.js';
import { promisify } from 'util';
import { pipeline } from 'stream';
import { registerStream, closeStream } from '../stream/streamManager.js';

// Create a logger for this module
const logger = new Logger('FileStreamUtils');

// Promisify pipeline for easier use with async/await
const pipelineAsync = promisify(pipeline);

/**
 * Calculate a hash for a file using streaming to avoid loading the entire file into memory
 *
 * @param filePath Path to the file
 * @param algorithm Hash algorithm to use (default: 'md5')
 * @returns Promise resolving to the hash string
 */
export async function calculateFileHash(filePath: string, algorithm: string = 'md5'): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        const hash = createHash(algorithm);
        const stream = fs.createReadStream(filePath);

        // Register the stream with the stream manager
        const streamId = registerStream(stream, `hash_${algorithm}_${filePath}`);

        // Handle stream events
        stream.on('data', (data) => {
            hash.update(data);
        });

        stream.on('end', () => {
            const hashValue = hash.digest('hex');
            // Stream manager will handle cleanup
            closeStream(streamId);
            resolve(hashValue);
        });

        stream.on('error', (err) => {
            // Stream manager will handle cleanup
            closeStream(streamId);
            reject(err);
        });
    });
}

/**
 * Read a file in chunks and process each chunk
 *
 * @param filePath Path to the file
 * @param chunkProcessor Function to process each chunk
 * @param chunkSize Size of each chunk in bytes (default: 64KB)
 * @returns Promise that resolves when processing is complete
 */
export async function processFileInChunks(
    filePath: string,
    chunkProcessor: (chunk: Buffer) => void | Promise<void>,
    chunkSize: number = 64 * 1024
): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        const stream = fs.createReadStream(filePath, { highWaterMark: chunkSize });

        // Register the stream with the stream manager
        const streamId = registerStream(stream, `process_chunks_${filePath}`);

        // Handle stream events
        stream.on('data', async (chunk) => {
            try {
                // Pause the stream while processing the chunk
                stream.pause();

                // Process the chunk
                await Promise.resolve(chunkProcessor(chunk));

                // Resume the stream if it's still active
                if (stream.readable) {
                    stream.resume();
                }
            } catch (error) {
                closeStream(streamId);
                reject(error);
            }
        });

        stream.on('end', () => {
            closeStream(streamId);
            resolve();
        });

        stream.on('error', (err) => {
            closeStream(streamId);
            reject(err);
        });
    });
}

/**
 * Copy a file using streams with proper cleanup
 *
 * @param sourcePath Source file path
 * @param destinationPath Destination file path
 * @returns Promise that resolves when the copy is complete
 */
export async function copyFileWithStreams(sourcePath: string, destinationPath: string): Promise<void> {
    const readStream = fs.createReadStream(sourcePath);
    const writeStream = fs.createWriteStream(destinationPath);

    // Register streams with the stream manager
    const readStreamId = registerStream(readStream, `copy_read_${sourcePath}`);
    const writeStreamId = registerStream(writeStream, `copy_write_${destinationPath}`);

    try {
        // Use pipeline to handle errors and cleanup properly
        await pipelineAsync(readStream, writeStream);

        // Close streams through the stream manager
        closeStream(readStreamId);
        closeStream(writeStreamId);
    } catch (error) {
        // Ensure streams are closed in case of error
        closeStream(readStreamId);
        closeStream(writeStreamId);
        throw error;
    }
}

/**
 * Read a file completely into a buffer with proper stream handling
 *
 * @param filePath Path to the file
 * @returns Promise resolving to the file buffer
 */
export async function readFileToBuffer(filePath: string): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        const stream = fs.createReadStream(filePath);

        // Register the stream with the stream manager
        const streamId = registerStream(stream, `read_${filePath}`);

        // Handle stream events
        stream.on('data', (chunk) => {
            chunks.push(chunk);
        });

        stream.on('end', () => {
            const buffer = Buffer.concat(chunks);
            // Stream manager will handle cleanup
            closeStream(streamId);
            resolve(buffer);
        });

        stream.on('error', (err) => {
            // Stream manager will handle cleanup
            closeStream(streamId);
            reject(err);
        });
    });
}
