﻿// Corrected import
import { ResourceKey } from './interfaces.js';

export interface ResourceValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  timestamp: number;
  key?: ResourceKey;
  metadata?: {
    validatedFields?: string[];
    skippedValidations?: string[];
    validationDuration?: number;
  };
  details?: {
    [key: string]: unknown;
  };
}

export interface ResourceValidationOptions {
  validateContent?: boolean;
  validateMetadata?: boolean;
  validateDependencies?: boolean;
  validateConflicts?: boolean;
  validateVersion?: boolean;
  validateHash?: boolean;
  validateTimestamp?: boolean;
  validateCustomData?: boolean;
  skipValidation?: string[];
  customValidations?: {
    [key: string]: (resource: unknown) => boolean;
  };
}
