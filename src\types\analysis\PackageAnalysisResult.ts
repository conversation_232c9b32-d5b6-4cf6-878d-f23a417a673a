﻿// Corrected import
import { <PERSON><PERSON><PERSON>, ResourceInfo } from '../resource/interfaces.js'; // Added ResourceInfo
// Import the central ConflictInfo type
import { ConflictInfo } from '../conflict/index.js';
import { PackageMetadata } from '../resource/Package.js';

/**
 * Represents the result of analyzing a Sims 4 package file.
 */
export interface PackageAnalysisResult {
  /** Metadata extracted or generated for the package. */
  metadata: PackageMetadata;

  /** List of resources identified in the package, including metadata. */
  resources: ResourceInfo[]; // Changed from ResourceKey[] to ResourceInfo[]

  /** List of conflicts detected within or related to the package. */
  conflicts: ConflictInfo[]; // Use imported ConflictInfo type

  /** Timestamp of when the analysis was completed. */
  timestamp: number;

  /** Indicates if the package is considered valid based on analysis rules. */
  isValid: boolean;

  /** List of errors encountered during analysis. */
  errors: string[]; // Or a more structured error type

  /** List of warnings generated during analysis. */
  warnings: string[]; // Or a more structured warning type

  /** Time taken for the analysis in milliseconds. */
  analysisTime: number;

  /** Total number of resources found in the package. */
  resourceCount: number;

  /** Total size of the package file in bytes (optional). */
  totalSize?: number;

  /** Any custom results from specific analysis steps (optional). */
  customResults?: Record<string, unknown>;

  // Optional: Add dependencies if needed
  // dependencies?: DependencyInfo[];

  // Optional: Add recommendations if generated separately
  // recommendations?: Recommendation[];
}
