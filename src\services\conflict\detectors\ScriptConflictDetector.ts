/**
 * Script Conflict Detector
 *
 * Detects conflicts between script resources in Sims 4 mods.
 * Focuses on Python script conflicts in TS4Script files, including:
 * - Class definition conflicts
 * - Function definition conflicts
 * - Injection conflicts
 * - Event handler conflicts
 * - Command conflicts
 * - Import conflicts
 */

import { Logger } from '../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../types/resource/interfaces.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/index.js';
import { DatabaseService } from '../../databaseService.js';
import { ConflictDetectorBase, ConflictDetectionOptionsBase } from './ConflictDetectorBase.js';
import { BytecodeParser } from '../../analysis/ts4script/bytecode/bytecodeParser.js';
import { TS4ScriptAnalyzer } from '../../analysis/ts4script/ts4ScriptAnalyzer.js';
import { formatTgi } from '../../../utils/resource/formatters.js';
import { compareStringSimilarity } from '../../../utils/string/stringSimilarity.js';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';

/**
 * Options for script conflict detection
 */
export interface ScriptConflictDetectionOptions extends ConflictDetectionOptionsBase {
    /**
     * Whether to detect class definition conflicts
     */
    detectClassConflicts?: boolean;

    /**
     * Whether to detect function definition conflicts
     */
    detectFunctionConflicts?: boolean;

    /**
     * Whether to detect injection conflicts
     */
    detectInjectionConflicts?: boolean;

    /**
     * Whether to detect event handler conflicts
     */
    detectEventHandlerConflicts?: boolean;

    /**
     * Whether to detect command conflicts
     */
    detectCommandConflicts?: boolean;

    /**
     * Whether to detect import conflicts
     */
    detectImportConflicts?: boolean;

    /**
     * Similarity threshold for considering scripts to be similar
     * (0.0 to 1.0, where 1.0 means exact match)
     */
    similarityThreshold?: number;

    /**
     * Maximum size of script content to compare (in bytes)
     * Scripts larger than this will be skipped for performance reasons
     */
    maxContentSize?: number;
}

/**
 * Default options for script conflict detection
 */
const DEFAULT_OPTIONS: ScriptConflictDetectionOptions = {
    enabled: true,
    detectClassConflicts: true,
    detectFunctionConflicts: true,
    detectInjectionConflicts: true,
    detectEventHandlerConflicts: true,
    detectCommandConflicts: true,
    detectImportConflicts: false, // Disabled by default as imports alone don't usually cause conflicts
    similarityThreshold: 0.8,
    maxContentSize: 1048576, // 1 MB
    excludeTypes: [],
    includeTypes: []
};

/**
 * Script conflict detector for detecting conflicts between script resources
 */
export class ScriptConflictDetector extends ConflictDetectorBase<ScriptConflictDetectionOptions> {
    private bytecodeParser: BytecodeParser;
    private ts4ScriptAnalyzer: TS4ScriptAnalyzer | null = null;

    /**
     * Create a new script conflict detector
     * @param databaseService Database service for retrieving resource information
     * @param options Options for script conflict detection
     * @param logger Logger for logging messages
     */
    constructor(
        databaseService: DatabaseService,
        options: ScriptConflictDetectionOptions = {},
        logger?: Logger
    ) {
        // Create options object with defaults
        const mergedOptions: ScriptConflictDetectionOptions = {
            enabled: options.enabled !== false,
            excludeTypes: options.excludeTypes || [],
            includeTypes: options.includeTypes || [],
            detectClassConflicts: options.detectClassConflicts !== false,
            detectFunctionConflicts: options.detectFunctionConflicts !== false,
            detectInjectionConflicts: options.detectInjectionConflicts !== false,
            detectEventHandlerConflicts: options.detectEventHandlerConflicts !== false,
            detectCommandConflicts: options.detectCommandConflicts !== false,
            detectImportConflicts: options.detectImportConflicts === true,
            similarityThreshold: options.similarityThreshold || 0.8,
            maxContentSize: options.maxContentSize || 1048576,
            ...options
        };

        // Pass merged options to parent constructor
        super(databaseService, mergedOptions, logger);

        this.bytecodeParser = new BytecodeParser(this.logger);
        this.logger.debug(`ScriptConflictDetector initialized with options: ${JSON.stringify(this.options)}`);
    }

    /**
     * Initialize the script conflict detector
     */
    public async initialize(): Promise<void> {
        try {
            // Initialize the TS4ScriptAnalyzer if needed
            if (!this.ts4ScriptAnalyzer) {
                this.ts4ScriptAnalyzer = new TS4ScriptAnalyzer(this.databaseService, undefined, undefined, this.logger);
                await this.ts4ScriptAnalyzer.initialize();
                this.logger.debug('TS4ScriptAnalyzer initialized');
            }
        } catch (error: any) {
            this.logger.error(`Error initializing ScriptConflictDetector: ${error.message || error}`);
            throw error;
        }
    }

    /**
     * Detect conflicts between two resources (single conflict method)
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns ConflictInfo if a conflict is detected, null otherwise
     */
    public async detectConflict(resource1: ResourceInfo, resource2: ResourceInfo): Promise<ConflictInfo | null> {
        const conflicts = await this.detectConflicts(resource1, resource2);
        return conflicts.length > 0 ? conflicts[0] : null;
    }

    /**
     * Detect conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    public async detectConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        try {
            // Skip if either resource doesn't have metadata or buffer
            if (!resource1.metadata || !resource2.metadata || !resource1.buffer || !resource2.buffer) {
                return conflicts;
            }

            // Skip if resources are not script resources
            if (!this.isScriptResource(resource1) || !this.isScriptResource(resource2)) {
                return conflicts;
            }

            // Skip if resources are too large
            if (resource1.buffer.length > this.options.maxContentSize ||
                resource2.buffer.length > this.options.maxContentSize) {
                this.logger.debug(`Skipping large script resources: ${resource1.id} (${resource1.buffer.length} bytes) or ${resource2.id} (${resource2.buffer.length} bytes)`);
                return conflicts;
            }

            // Detect script conflicts based on enabled options
            if (this.options.detectClassConflicts) {
                const classConflicts = await this.detectClassConflicts(resource1, resource2);
                conflicts.push(...classConflicts);
            }

            if (this.options.detectFunctionConflicts) {
                const functionConflicts = await this.detectFunctionConflicts(resource1, resource2);
                conflicts.push(...functionConflicts);
            }

            if (this.options.detectInjectionConflicts) {
                const injectionConflicts = await this.detectInjectionConflicts(resource1, resource2);
                conflicts.push(...injectionConflicts);
            }

            if (this.options.detectEventHandlerConflicts) {
                const eventHandlerConflicts = await this.detectEventHandlerConflicts(resource1, resource2);
                conflicts.push(...eventHandlerConflicts);
            }

            if (this.options.detectCommandConflicts) {
                const commandConflicts = await this.detectCommandConflicts(resource1, resource2);
                conflicts.push(...commandConflicts);
            }

            if (this.options.detectImportConflicts) {
                const importConflicts = await this.detectImportConflicts(resource1, resource2);
                conflicts.push(...importConflicts);
            }
        } catch (error: any) {
            this.logger.error(`Error detecting script conflicts: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Check if a resource is a script resource
     * @param resource Resource to check
     * @returns True if the resource is a script resource
     */
    private isScriptResource(resource: ResourceInfo): boolean {
        // Check if resource type is in the include list (if provided)
        if (this.options.includeTypes.length > 0 && !this.options.includeTypes.includes(resource.key.type)) {
            return false;
        }

        // Check if resource type is in the exclude list
        if (this.options.excludeTypes.includes(resource.key.type)) {
            return false;
        }

        // Check resource type based on metadata
        const resourceType = resource.metadata?.resourceType;
        return resourceType === 'SCRIPT' ||
               resourceType === 'SCRIPT_MODULE' ||
               resourceType === 'PYTHON_SCRIPT' ||
               resourceType === 'TS4SCRIPT';
    }

    /**
     * Detect class definition conflicts between two script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    private async detectClassConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        try {
            // Get script analysis data from database
            const scriptData1 = await this.getScriptAnalysisData(resource1);
            const scriptData2 = await this.getScriptAnalysisData(resource2);

            if (!scriptData1 || !scriptData2) {
                return conflicts;
            }

            // Extract class definitions from both scripts
            const classes1 = this.extractClassDefinitions(scriptData1);
            const classes2 = this.extractClassDefinitions(scriptData2);

            // Find conflicting class definitions
            const conflictingClasses = this.findConflictingDefinitions(classes1, classes2);

            if (conflictingClasses.length > 0) {
                // Create a conflict for each conflicting class
                for (const conflict of conflictingClasses) {
                    conflicts.push({
                        id: `script-class-${resource1.id}-${resource2.id}-${conflict.name}`,
                        type: ConflictType.SCRIPT,
                        severity: this.determineClassConflictSeverity(conflict),
                        description: `Both scripts define the same class: ${conflict.name}`,
                        affectedResources: [resource1.key, resource2.key],
                        timestamp: Date.now(),
                        recommendations: [
                            'Check if both mods are compatible',
                            'Only use one of the conflicting mods',
                            'Check if updated versions are available'
                        ],
                        metadata: {
                            className: conflict.name,
                            resource1Path: scriptData1.path,
                            resource2Path: scriptData2.path,
                            similarity: conflict.similarity
                        },
                        confidence: conflict.similarity
                    });
                }
            }
        } catch (error: any) {
            this.logger.error(`Error detecting class conflicts: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Detect function definition conflicts between two script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    private async detectFunctionConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        try {
            // Get script analysis data from database
            const scriptData1 = await this.getScriptAnalysisData(resource1);
            const scriptData2 = await this.getScriptAnalysisData(resource2);

            if (!scriptData1 || !scriptData2) {
                return conflicts;
            }

            // Extract function definitions from both scripts
            const functions1 = this.extractFunctionDefinitions(scriptData1);
            const functions2 = this.extractFunctionDefinitions(scriptData2);

            // Find conflicting function definitions
            const conflictingFunctions = this.findConflictingDefinitions(functions1, functions2);

            if (conflictingFunctions.length > 0) {
                // Create a conflict for each conflicting function
                for (const conflict of conflictingFunctions) {
                    conflicts.push({
                        id: `script-function-${resource1.id}-${resource2.id}-${conflict.name}`,
                        type: ConflictType.SCRIPT,
                        severity: this.determineFunctionConflictSeverity(conflict),
                        description: `Both scripts define the same function: ${conflict.name}`,
                        affectedResources: [resource1.key, resource2.key],
                        timestamp: Date.now(),
                        recommendations: [
                            'Check if both mods are compatible',
                            'Only use one of the conflicting mods',
                            'Check if updated versions are available'
                        ],
                        metadata: {
                            functionName: conflict.name,
                            resource1Path: scriptData1.path,
                            resource2Path: scriptData2.path,
                            similarity: conflict.similarity
                        },
                        confidence: conflict.similarity
                    });
                }
            }
        } catch (error: any) {
            this.logger.error(`Error detecting function conflicts: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Detect injection conflicts between two script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    private async detectInjectionConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        try {
            // Get script analysis data from database
            const scriptData1 = await this.getScriptAnalysisData(resource1);
            const scriptData2 = await this.getScriptAnalysisData(resource2);

            if (!scriptData1 || !scriptData2) {
                return conflicts;
            }

            // Extract injection definitions from both scripts
            const injections1 = this.extractInjections(scriptData1);
            const injections2 = this.extractInjections(scriptData2);

            // Find conflicting injections
            const conflictingInjections = this.findConflictingDefinitions(injections1, injections2);

            if (conflictingInjections.length > 0) {
                // Create a conflict for each conflicting injection
                for (const conflict of conflictingInjections) {
                    conflicts.push({
                        id: `script-injection-${resource1.id}-${resource2.id}-${conflict.name}`,
                        type: ConflictType.SCRIPT,
                        severity: ConflictSeverity.CRITICAL, // Injections are usually critical
                        description: `Both scripts inject into the same target: ${conflict.name}`,
                        affectedResources: [resource1.key, resource2.key],
                        timestamp: Date.now(),
                        recommendations: [
                            'Script injection conflicts can cause game crashes',
                            'Only use one of the conflicting mods',
                            'Check if updated versions are available',
                            'Contact mod authors about compatibility'
                        ],
                        metadata: {
                            injectionTarget: conflict.name,
                            resource1Path: scriptData1.path,
                            resource2Path: scriptData2.path,
                            similarity: conflict.similarity
                        },
                        confidence: conflict.similarity
                    });
                }
            }
        } catch (error: any) {
            this.logger.error(`Error detecting injection conflicts: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Detect event handler conflicts between two script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    private async detectEventHandlerConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        try {
            // Get script analysis data from database
            const scriptData1 = await this.getScriptAnalysisData(resource1);
            const scriptData2 = await this.getScriptAnalysisData(resource2);

            if (!scriptData1 || !scriptData2) {
                return conflicts;
            }

            // Extract event handler definitions from both scripts
            const eventHandlers1 = this.extractEventHandlers(scriptData1);
            const eventHandlers2 = this.extractEventHandlers(scriptData2);

            // Find conflicting event handlers
            const conflictingEventHandlers = this.findConflictingDefinitions(eventHandlers1, eventHandlers2);

            if (conflictingEventHandlers.length > 0) {
                // Create a conflict for each conflicting event handler
                for (const conflict of conflictingEventHandlers) {
                    conflicts.push({
                        id: `script-event-${resource1.id}-${resource2.id}-${conflict.name}`,
                        type: ConflictType.SCRIPT,
                        severity: ConflictSeverity.HIGH, // Event handlers are usually high severity
                        description: `Both scripts handle the same event: ${conflict.name}`,
                        affectedResources: [resource1.key, resource2.key],
                        timestamp: Date.now(),
                        recommendations: [
                            'Event handler conflicts can cause unpredictable behavior',
                            'Check if both mods are compatible',
                            'Only use one of the conflicting mods',
                            'Check if updated versions are available'
                        ],
                        metadata: {
                            eventName: conflict.name,
                            resource1Path: scriptData1.path,
                            resource2Path: scriptData2.path,
                            similarity: conflict.similarity
                        },
                        confidence: conflict.similarity
                    });
                }
            }
        } catch (error: any) {
            this.logger.error(`Error detecting event handler conflicts: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Detect command conflicts between two script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    private async detectCommandConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        try {
            // Get script analysis data from database
            const scriptData1 = await this.getScriptAnalysisData(resource1);
            const scriptData2 = await this.getScriptAnalysisData(resource2);

            if (!scriptData1 || !scriptData2) {
                return conflicts;
            }

            // Extract command definitions from both scripts
            const commands1 = this.extractCommands(scriptData1);
            const commands2 = this.extractCommands(scriptData2);

            // Find conflicting commands
            const conflictingCommands = this.findConflictingDefinitions(commands1, commands2);

            if (conflictingCommands.length > 0) {
                // Create a conflict for each conflicting command
                for (const conflict of conflictingCommands) {
                    conflicts.push({
                        id: `script-command-${resource1.id}-${resource2.id}-${conflict.name}`,
                        type: ConflictType.SCRIPT,
                        severity: ConflictSeverity.MEDIUM, // Commands are usually medium severity
                        description: `Both scripts define the same command: ${conflict.name}`,
                        affectedResources: [resource1.key, resource2.key],
                        timestamp: Date.now(),
                        recommendations: [
                            'Command conflicts can cause one mod to override the other',
                            'Check if both mods are compatible',
                            'Only use one of the conflicting mods',
                            'Check if updated versions are available'
                        ],
                        metadata: {
                            commandName: conflict.name,
                            resource1Path: scriptData1.path,
                            resource2Path: scriptData2.path,
                            similarity: conflict.similarity
                        },
                        confidence: conflict.similarity
                    });
                }
            }
        } catch (error: any) {
            this.logger.error(`Error detecting command conflicts: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Detect import conflicts between two script resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    private async detectImportConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        try {
            // Get script analysis data from database
            const scriptData1 = await this.getScriptAnalysisData(resource1);
            const scriptData2 = await this.getScriptAnalysisData(resource2);

            if (!scriptData1 || !scriptData2) {
                return conflicts;
            }

            // Extract import statements from both scripts
            const imports1 = this.extractImports(scriptData1);
            const imports2 = this.extractImports(scriptData2);

            // Find conflicting imports (only consider critical imports)
            const criticalImports = [
                'sims4.commands',
                'sims4.resources',
                'sims4.service_manager',
                'sims4.tuning.instances',
                'services',
                'zone',
                'objects.script_object',
                'objects.components',
                'interactions'
            ];

            const conflictingImports = this.findConflictingDefinitions(
                imports1.filter(imp => criticalImports.some(ci => imp.name.includes(ci))),
                imports2.filter(imp => criticalImports.some(ci => imp.name.includes(ci)))
            );

            if (conflictingImports.length > 0) {
                // Create a conflict for each conflicting import
                for (const conflict of conflictingImports) {
                    conflicts.push({
                        id: `script-import-${resource1.id}-${resource2.id}-${conflict.name}`,
                        type: ConflictType.SCRIPT,
                        severity: ConflictSeverity.LOW, // Imports are usually low severity
                        description: `Both scripts import the same critical module: ${conflict.name}`,
                        affectedResources: [resource1.key, resource2.key],
                        timestamp: Date.now(),
                        recommendations: [
                            'Import conflicts alone may not cause issues',
                            'Check for other conflicts between these mods',
                            'Monitor for unexpected behavior when using both mods'
                        ],
                        metadata: {
                            importName: conflict.name,
                            resource1Path: scriptData1.path,
                            resource2Path: scriptData2.path,
                            similarity: conflict.similarity
                        },
                        confidence: conflict.similarity * 0.8 // Lower confidence for import conflicts
                    });
                }
            }
        } catch (error: any) {
            this.logger.error(`Error detecting import conflicts: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Get script analysis data from the database
     * @param resource Resource to get script analysis data for
     * @returns Script analysis data or null if not found
     */
    private async getScriptAnalysisData(resource: ResourceInfo): Promise<any | null> {
        try {
            // Check if we have script analysis data in the database
            const scriptData = await this.databaseService.getScriptAnalysisData(resource.id);

            if (scriptData) {
                return scriptData;
            }

            // If not, try to analyze the script using the TS4ScriptAnalyzer
            if (this.ts4ScriptAnalyzer && resource.buffer) {
                try {
                    // Create a temporary file path for analysis
                    const tempFilePath = path.join(os.tmpdir(), `temp_script_${resource.id}.ts4script`);

                    // Write the buffer to a temporary file
                    await fs.writeFile(tempFilePath, resource.buffer);

                    // Analyze the script
                    const result = await this.ts4ScriptAnalyzer.analyzeTS4Script(tempFilePath);

                    // Clean up the temporary file
                    try {
                        await fs.unlink(tempFilePath);
                    } catch (cleanupError) {
                        this.logger.warn(`Error cleaning up temporary file ${tempFilePath}: ${cleanupError}`);
                    }

                    if (result) {
                        // Check if the resource exists in the database before saving script analysis data
                        const resourceExists = await this.databaseService.getResourceById(resource.id);
                        if (resourceExists) {
                            // Save the analysis data to the database
                            await this.databaseService.saveScriptAnalysisData(resource.id, result);
                            return result;
                        } else {
                            this.logger.warn(`Resource ${resource.id} does not exist in the database, cannot save script analysis data`);
                            return result; // Still return the result even if we couldn't save it
                        }
                    }
                } catch (analysisError) {
                    this.logger.error(`Error analyzing script buffer: ${analysisError.message || analysisError}`);
                }
            }

            // If we couldn't get script data, try to parse the bytecode directly
            if (resource.buffer) {
                try {
                    const result = this.bytecodeParser.parseBytecodeWithFallback(
                        resource.buffer,
                        `resource_${resource.id}`
                    );

                    if (result.codeObject) {
                        // Extract basic information
                        const classes = this.bytecodeParser.extractClasses(result.codeObject);
                        const functions = this.bytecodeParser.extractFunctions(result.codeObject);
                        const imports = this.bytecodeParser.extractImports(result.codeObject);

                        // Create a minimal script data object
                        const minimalScriptData = {
                            id: resource.id,
                            path: `resource_${resource.id}`,
                            modules: [{
                                name: `resource_${resource.id}`,
                                path: `resource_${resource.id}`,
                                content: resource.buffer.toString('base64'),
                                classes: classes.map(c => ({ name: c.name, parentClasses: c.parentClasses || [] })),
                                functions: functions.map(f => ({
                                    name: f.name,
                                    isMethod: f.isMethod || false,
                                    isInjection: f.isInjection || false,
                                    isEventHandler: f.isEventHandler || false,
                                    isCommand: f.isCommand || false
                                })),
                                imports: imports.map(i => ({ name: i.name, fromModule: i.fromModule || '' })),
                                metadata: {
                                    pythonVersion: result.pythonVersion,
                                    hasInjections: functions.some(f => f.isInjection),
                                    hasEventHandlers: functions.some(f => f.isEventHandler),
                                    hasCommands: functions.some(f => f.isCommand),
                                    hasTuningReferences: false
                                }
                            }],
                            totalClasses: classes.length,
                            totalFunctions: functions.length,
                            totalImports: imports.length,
                            hasInjections: functions.some(f => f.isInjection),
                            hasEventHandlers: functions.some(f => f.isEventHandler),
                            hasCommands: functions.some(f => f.isCommand),
                            hasTuningReferences: false
                        };

                        // Check if the resource exists in the database before saving script analysis data
                        const resourceExists = await this.databaseService.getResourceById(resource.id);
                        if (resourceExists) {
                            // Save the minimal script data to the database
                            await this.databaseService.saveScriptAnalysisData(resource.id, minimalScriptData);
                            return minimalScriptData;
                        } else {
                            this.logger.warn(`Resource ${resource.id} does not exist in the database, cannot save minimal script analysis data`);
                            return minimalScriptData; // Still return the data even if we couldn't save it
                        }
                    }
                } catch (error: any) {
                    this.logger.error(`Error parsing bytecode: ${error.message || error}`);
                }
            }

            return null;
        } catch (error: any) {
            this.logger.error(`Error getting script analysis data: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Extract class definitions from script data
     * @param scriptData Script analysis data
     * @returns Array of class definitions
     */
    private extractClassDefinitions(scriptData: any): Array<{ name: string, content: string }> {
        const classes: Array<{ name: string, content: string }> = [];

        try {
            if (scriptData.modules) {
                for (const module of scriptData.modules) {
                    if (module.classes) {
                        for (const cls of module.classes) {
                            classes.push({
                                name: cls.name,
                                content: JSON.stringify(cls) // Use the class definition as content for similarity comparison
                            });
                        }
                    }
                }
            }
        } catch (error: any) {
            this.logger.error(`Error extracting class definitions: ${error.message || error}`);
        }

        return classes;
    }

    /**
     * Extract function definitions from script data
     * @param scriptData Script analysis data
     * @returns Array of function definitions
     */
    private extractFunctionDefinitions(scriptData: any): Array<{ name: string, content: string }> {
        const functions: Array<{ name: string, content: string }> = [];

        try {
            if (scriptData.modules) {
                for (const module of scriptData.modules) {
                    if (module.functions) {
                        for (const func of module.functions) {
                            // Skip injections, event handlers, and commands as they're handled separately
                            if (!func.isInjection && !func.isEventHandler && !func.isCommand) {
                                functions.push({
                                    name: func.name,
                                    content: JSON.stringify(func) // Use the function definition as content for similarity comparison
                                });
                            }
                        }
                    }
                }
            }
        } catch (error: any) {
            this.logger.error(`Error extracting function definitions: ${error.message || error}`);
        }

        return functions;
    }

    /**
     * Extract injections from script data
     * @param scriptData Script analysis data
     * @returns Array of injection definitions
     */
    private extractInjections(scriptData: any): Array<{ name: string, content: string }> {
        const injections: Array<{ name: string, content: string }> = [];

        try {
            if (scriptData.modules) {
                for (const module of scriptData.modules) {
                    if (module.functions) {
                        for (const func of module.functions) {
                            if (func.isInjection) {
                                // For injections, the name should include the target class and method
                                const name = func.injectionTarget ?
                                    `${func.injectionTarget}.${func.name}` :
                                    func.name;

                                injections.push({
                                    name: name,
                                    content: JSON.stringify(func) // Use the injection definition as content for similarity comparison
                                });
                            }
                        }
                    }
                }
            }
        } catch (error: any) {
            this.logger.error(`Error extracting injections: ${error.message || error}`);
        }

        return injections;
    }

    /**
     * Extract event handlers from script data
     * @param scriptData Script analysis data
     * @returns Array of event handler definitions
     */
    private extractEventHandlers(scriptData: any): Array<{ name: string, content: string }> {
        const eventHandlers: Array<{ name: string, content: string }> = [];

        try {
            if (scriptData.modules) {
                for (const module of scriptData.modules) {
                    if (module.functions) {
                        for (const func of module.functions) {
                            if (func.isEventHandler) {
                                // For event handlers, the name should include the event name
                                const name = func.eventName ?
                                    `${func.eventName}` :
                                    func.name;

                                eventHandlers.push({
                                    name: name,
                                    content: JSON.stringify(func) // Use the event handler definition as content for similarity comparison
                                });
                            }
                        }
                    }
                }
            }
        } catch (error: any) {
            this.logger.error(`Error extracting event handlers: ${error.message || error}`);
        }

        return eventHandlers;
    }

    /**
     * Extract commands from script data
     * @param scriptData Script analysis data
     * @returns Array of command definitions
     */
    private extractCommands(scriptData: any): Array<{ name: string, content: string }> {
        const commands: Array<{ name: string, content: string }> = [];

        try {
            if (scriptData.modules) {
                for (const module of scriptData.modules) {
                    if (module.functions) {
                        for (const func of module.functions) {
                            if (func.isCommand) {
                                // For commands, the name should include the command name
                                const name = func.commandName ?
                                    `${func.commandName}` :
                                    func.name;

                                commands.push({
                                    name: name,
                                    content: JSON.stringify(func) // Use the command definition as content for similarity comparison
                                });
                            }
                        }
                    }
                }
            }
        } catch (error: any) {
            this.logger.error(`Error extracting commands: ${error.message || error}`);
        }

        return commands;
    }

    /**
     * Extract imports from script data
     * @param scriptData Script analysis data
     * @returns Array of import definitions
     */
    private extractImports(scriptData: any): Array<{ name: string, content: string }> {
        const imports: Array<{ name: string, content: string }> = [];

        try {
            if (scriptData.modules) {
                for (const module of scriptData.modules) {
                    if (module.imports) {
                        for (const imp of module.imports) {
                            imports.push({
                                name: imp.name,
                                content: JSON.stringify(imp) // Use the import definition as content for similarity comparison
                            });
                        }
                    }
                }
            }
        } catch (error: any) {
            this.logger.error(`Error extracting imports: ${error.message || error}`);
        }

        return imports;
    }

    /**
     * Find conflicting definitions between two sets of definitions
     * @param defs1 First set of definitions
     * @param defs2 Second set of definitions
     * @returns Array of conflicting definitions
     */
    private findConflictingDefinitions(
        defs1: Array<{ name: string, content: string }>,
        defs2: Array<{ name: string, content: string }>
    ): Array<{ name: string, content1: string, content2: string, similarity: number }> {
        const conflicts: Array<{ name: string, content1: string, content2: string, similarity: number }> = [];

        try {
            // Check for exact name matches first
            for (const def1 of defs1) {
                for (const def2 of defs2) {
                    if (def1.name === def2.name) {
                        // Calculate similarity between the contents
                        const similarity = compareStringSimilarity(def1.content, def2.content);

                        // Only consider it a conflict if the similarity is below the threshold
                        // (if they're too similar, they're probably the same definition)
                        if (similarity < this.options.similarityThreshold) {
                            conflicts.push({
                                name: def1.name,
                                content1: def1.content,
                                content2: def2.content,
                                similarity: 1.0 - similarity // Invert similarity for conflict confidence
                            });
                        }
                    }
                }
            }

            // Check for similar names (for fuzzy matching)
            for (const def1 of defs1) {
                for (const def2 of defs2) {
                    if (def1.name !== def2.name) {
                        // Check if the names are similar
                        const nameSimilarity = compareStringSimilarity(def1.name, def2.name);

                        if (nameSimilarity > 0.8) { // High name similarity threshold
                            // Calculate similarity between the contents
                            const contentSimilarity = compareStringSimilarity(def1.content, def2.content);

                            // Only consider it a conflict if the content similarity is below the threshold
                            if (contentSimilarity < this.options.similarityThreshold) {
                                conflicts.push({
                                    name: `${def1.name} / ${def2.name}`,
                                    content1: def1.content,
                                    content2: def2.content,
                                    similarity: (1.0 - contentSimilarity) * nameSimilarity // Adjust confidence based on name similarity
                                });
                            }
                        }
                    }
                }
            }
        } catch (error: any) {
            this.logger.error(`Error finding conflicting definitions: ${error.message || error}`);
        }

        return conflicts;
    }

    /**
     * Determine the severity of a class conflict
     * @param conflict Class conflict
     * @returns Conflict severity
     */
    private determineClassConflictSeverity(conflict: { name: string, similarity: number }): ConflictSeverity {
        // Critical classes that are likely to cause serious issues if conflicting
        const criticalClasses = [
            'GameplayArchitecture',
            'Service',
            'Component',
            'Interaction',
            'SimInfo',
            'GameObject',
            'Zone',
            'World',
            'Lot',
            'Household'
        ];

        // Check if the class name contains any critical class names
        if (criticalClasses.some(cc => conflict.name.includes(cc))) {
            return ConflictSeverity.CRITICAL;
        }

        // High similarity means more different implementations, which is worse
        if (conflict.similarity > 0.8) {
            return ConflictSeverity.HIGH;
        }

        return ConflictSeverity.MEDIUM;
    }

    /**
     * Determine the severity of a function conflict
     * @param conflict Function conflict
     * @returns Conflict severity
     */
    private determineFunctionConflictSeverity(conflict: { name: string, similarity: number }): ConflictSeverity {
        // Critical functions that are likely to cause serious issues if conflicting
        const criticalFunctions = [
            'on_add',
            'on_remove',
            'on_init',
            'on_reset',
            'on_zone_load',
            'on_zone_unload',
            'on_sim_reset',
            'on_sim_spawn',
            'on_sim_despawn',
            'on_build_buy_enter',
            'on_build_buy_exit'
        ];

        // Check if the function name matches any critical function names
        if (criticalFunctions.some(cf => conflict.name === cf)) {
            return ConflictSeverity.CRITICAL;
        }

        // Check if the function name contains any critical function names
        if (criticalFunctions.some(cf => conflict.name.includes(cf))) {
            return ConflictSeverity.HIGH;
        }

        // High similarity means more different implementations, which is worse
        if (conflict.similarity > 0.8) {
            return ConflictSeverity.MEDIUM;
        }

        return ConflictSeverity.LOW;
    }
}
