import { DependencyInfo } from '../../../../../types/database.js';

/**
 * Creates a dependency info object
 * @param type Resource type
 * @param group Resource group
 * @param instance Resource instance
 * @param referenceType Type of reference
 * @returns DependencyInfo object
 */
export function createDependency(type: number, group: number | bigint, instance: bigint, referenceType: string = 'TuningReference'): DependencyInfo {
    return {
        resourceId: 0, // Will be set by the caller
        targetType: type,
        targetGroup: typeof group === 'number' ? BigInt(group) : group,
        targetInstance: instance,
        referenceType,
        timestamp: Date.now()
    };
} 