export interface VersionInfo {
  version: string;
  type: 'release' | 'beta' | 'alpha' | 'dev';
  major: number;
  minor: number;
  patch: number;
  preRelease?: string;
  buildMetadata?: string;
}

export interface VersionComparisonResult {
  isCompatible: boolean;
  isNewer: boolean;
  isOlder: boolean;
  isEqual: boolean;
  difference: 'major' | 'minor' | 'patch' | 'preRelease' | 'buildMetadata' | null;
  conflictType?: 'VERSION_CONFLICT' | 'COMPATIBILITY_CONFLICT';
}

export const VERSION_WEIGHTS = {
  release: 4,
  beta: 3,
  alpha: 2,
  dev: 1
} as const;

export const VERSION_PATTERNS = {
  SEMVER: /^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$/,
  SIMS4: /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/
} as const;

export const MINIMUM_SIMS4_VERSION = '1.0.0.0';
export const MAXIMUM_SIMS4_VERSION = '1.100.0.0';

export const VERSION_MODEL_CONSTANTS = {
  VERSION_PATTERN: /^(\d+)\.(\d+)(?:\.(\d+))?(?:-([a-zA-Z0-9-]+))?(?:\+([a-zA-Z0-9-]+))?$/,
  VERSION_PRIORITY: {
    'alpha': 1,
    'beta': 2,
    'rc': 3,
    'release': 4
  },
  VERSION_CONFLICT_THRESHOLD: 0.8,
  VERSION_COMPATIBILITY: {
    MAJOR_BREAKING: true,
    MINOR_BREAKING: false,
    PATCH_BREAKING: false,
    ALLOW_PRE_RELEASE: false,
    ALLOW_BUILD_METADATA: true
  }
}; 