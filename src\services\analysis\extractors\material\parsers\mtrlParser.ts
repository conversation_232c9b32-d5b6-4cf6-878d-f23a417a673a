/**
 * Parser for MTRL (Material) format
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DependencyInfo } from '../../../../databaseService.js';
import { MaterialHeaderInfo, MaterialParameter, MaterialParameterType } from '../materialTypes.js';
import { handleMaterialExtractionError } from '../error/index.js';
import { extractParameterValue } from './parameterExtractor.js';

const logger = new Logger('MtrlParser');

/**
 * Parses a MTRL (Material) resource buffer
 * @param buffer The resource buffer
 * @param resourceId The resource ID
 * @returns The parsed material header and dependencies
 */
export function parseMtrl(buffer: Buffer, resourceId: number): { header: MaterialHeaderInfo, dependencies: DependencyInfo[] } {
    try {
        const dependencies: DependencyInfo[] = [];

        // Initialize header with default format
        const header: MaterialHeaderInfo = {
            format: 'MTRL',
            version: 0,
            materialCount: 0,
            flags: 0
        };

        if (buffer.length >= 20) {
            header.version = buffer.readUInt32LE(4);
            header.flags = buffer.readUInt32LE(8);
            header.textureCount = buffer.readUInt32LE(12);
            header.parameterCount = buffer.readUInt32LE(16);

            // Extract shader type if available
            let offset = 20;
            if (offset + 4 <= buffer.length) {
                const shaderNameLength = buffer.readUInt32LE(offset);
                offset += 4;

                if (offset + shaderNameLength <= buffer.length) {
                    header.shaderType = buffer.slice(offset, offset + shaderNameLength).toString('utf8');
                    offset += shaderNameLength;
                }
            }

            // Extract texture references
            header.textureReferences = [];
            for (let i = 0; i < header.textureCount && offset + 4 <= buffer.length; i++) {
                const typeLength = buffer.readUInt32LE(offset);
                offset += 4;

                if (offset + typeLength <= buffer.length) {
                    const textureType = buffer.slice(offset, offset + typeLength).toString('utf8');
                    offset += typeLength;

                    if (offset + 4 <= buffer.length) {
                        const pathLength = buffer.readUInt32LE(offset);
                        offset += 4;

                        if (offset + pathLength <= buffer.length) {
                            const texturePath = buffer.slice(offset, offset + pathLength).toString('utf8');
                            offset += pathLength;

                            header.textureReferences.push({
                                type: textureType,
                                path: texturePath
                            });

                            // Look for texture TGI reference
                            if (offset + 16 <= buffer.length) {
                                const textureType = buffer.readUInt32LE(offset);
                                const textureGroup = buffer.readUInt32LE(offset + 4);
                                const textureInstance1 = buffer.readUInt32LE(offset + 8);
                                const textureInstance2 = buffer.readUInt32LE(offset + 12);

                                // Only add as dependency if it looks like a valid TGI
                                if (textureType !== 0) {
                                    // Construct bigint instance from two 32-bit parts
                                    const textureInstance = BigInt(textureInstance1) | (BigInt(textureInstance2) << 32n);

                                    // Add as dependency
                                    dependencies.push({
                                        resourceId: resourceId,
                                        targetType: textureType,
                                        targetGroup: BigInt(textureGroup),
                                        targetInstance: textureInstance,
                                        referenceType: 'Texture',
                                        timestamp: Date.now()
                                    });
                                }

                                offset += 16;
                            }
                        }
                    }
                }
            }

            // Extract parameters
            header.parameters = [];
            for (let i = 0; i < header.parameterCount && offset + 4 <= buffer.length; i++) {
                const nameLength = buffer.readUInt32LE(offset);
                offset += 4;

                if (offset + nameLength <= buffer.length) {
                    const paramName = buffer.slice(offset, offset + nameLength).toString('utf8');
                    offset += nameLength;

                    if (offset + 4 <= buffer.length) {
                        const paramType = buffer.readUInt32LE(offset);
                        offset += 4;

                        // Extract parameter value based on type
                        const { value: paramValue, newOffset } = extractParameterValue(buffer, offset, paramType);
                        offset = newOffset;

                        header.parameters.push({
                            name: paramName,
                            value: paramValue
                        });
                    }
                }
            }
        }

        return { header, dependencies };
    } catch (error) {
        return handleMaterialExtractionError(
            error,
            {
                resourceId,
                operation: 'parseMtrl',
                bufferLength: buffer?.length,
                materialFormat: 'MTRL',
                additionalInfo: { format: 'MTRL' }
            },
            {
                header: {
                    format: 'MTRL_ERROR',
                    version: 0,
                    materialCount: 0,
                    flags: 0
                },
                dependencies: []
            }
        );
    }
}
