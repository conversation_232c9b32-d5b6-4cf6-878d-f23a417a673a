/**
 * Utility functions for SimData version handling
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { VersionCategory, getVersionCategory } from '../types.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('VersionUtils');

/**
 * Checks if a version is a standard SimData version (1-20)
 * @param version SimData version
 * @returns True if the version is a standard version
 */
export function isStandardVersion(version: number): boolean {
    return version >= 1 && version <= 20;
}

/**
 * Checks if a version is a special SimData version (16708, 48111)
 * @param version SimData version
 * @returns True if the version is a special version
 */
export function isSpecialVersion(version: number): boolean {
    return version === 16708 || version === 48111;
}

/**
 * Checks if a version is a mod-specific SimData version
 * @param version SimData version
 * @returns True if the version is a mod-specific version
 */
export function isModVersion(version: number): boolean {
    const modVersions = [12345, 21324, 32768, 42069, 50000, 54321, 60000];
    return modVersions.includes(version);
}

/**
 * Checks if a version is an experimental SimData version
 * @param version SimData version
 * @returns True if the version is an experimental version
 */
export function isExperimentalVersion(version: number): boolean {
    const experimentalVersions = [65280, 65290, 65500, 65535];
    return experimentalVersions.includes(version);
}

/**
 * Gets a description for a SimData version
 * @param version SimData version
 * @returns Description of the version
 */
export function getVersionDescription(version: number): string {
    try {
        const category = getVersionCategory(version);
        
        switch (category) {
            case VersionCategory.STANDARD:
                return `Standard SimData version ${version}`;
            case VersionCategory.SPECIAL:
                if (version === 16708) {
                    return 'Special SimData version 16708 (used in some mods)';
                } else if (version === 48111) {
                    return 'Special SimData version 48111 (used in some mods)';
                } else {
                    return `Special SimData version ${version}`;
                }
            case VersionCategory.MOD:
                return `Mod-specific SimData version ${version}`;
            case VersionCategory.EXPERIMENTAL:
                return `Experimental SimData version ${version} (possibly a negative value)`;
            default:
                return `Unknown SimData version ${version}`;
        }
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(version, 'getVersionDescription'),
            `SimData version ${version}`
        );
    }
}

/**
 * Compares two SimData versions for compatibility
 * @param version1 First SimData version
 * @param version2 Second SimData version
 * @returns True if the versions are likely compatible
 */
export function areVersionsCompatible(version1: number, version2: number): boolean {
    try {
        // Same version is always compatible
        if (version1 === version2) {
            return true;
        }
        
        // Standard versions are generally compatible with each other
        if (isStandardVersion(version1) && isStandardVersion(version2)) {
            // Versions that are close to each other are more likely to be compatible
            return Math.abs(version1 - version2) <= 5;
        }
        
        // Special versions are not compatible with other versions
        if (isSpecialVersion(version1) || isSpecialVersion(version2)) {
            return false;
        }
        
        // Mod versions might be compatible with standard versions
        if ((isStandardVersion(version1) && isModVersion(version2)) ||
            (isStandardVersion(version2) && isModVersion(version1))) {
            return true;
        }
        
        // Experimental versions are not compatible with other versions
        if (isExperimentalVersion(version1) || isExperimentalVersion(version2)) {
            return false;
        }
        
        // Unknown versions are assumed to be incompatible
        return false;
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(undefined, 'areVersionsCompatible', { version1, version2 }),
            false
        );
    }
}
