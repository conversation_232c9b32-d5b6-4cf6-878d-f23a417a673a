{"env": {"browser": true, "es2020": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "prettier/prettier": "error"}, "ignorePatterns": ["dist", "node_modules", "coverage"]}