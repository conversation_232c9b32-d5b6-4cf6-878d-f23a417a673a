const fs = require('fs');
const path = require('path');

const nodeModulesPath = path.resolve(__dirname, '../node_modules');
const originalPath = path.join(nodeModulesPath, 'bufferfromfile');
const disabledPath = path.join(nodeModulesPath, 'bufferfromfile_DISABLED');

console.log('Running post-rebuild rename step...');
try {
  if (fs.existsSync(disabledPath)) {
    fs.renameSync(disabledPath, originalPath);
    console.log(`Renamed ${disabledPath} back to ${originalPath}`);
  } else {
    console.log(`${disabledPath} not found, skipping rename.`);
  }
  console.log('Post-rebuild rename step completed.');
} catch (error) {
  console.error('Error during post-rebuild rename step:', error);
  // Log error but don't necessarily fail the whole process
}