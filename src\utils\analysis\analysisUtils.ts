import { ConflictInfo, ConflictType, ConflictSeverity } from '../../types/conflict/index.js'; // Removed .js
import { ResourceKey } from '../../types/resource/interfaces.js'; // Removed .js
import { getResourceTypeName } from '../resource/helpers.js'; // Removed .js

/**
 * Generates a breakdown of resource counts by type name.
 */
export function getResourceTypeBreakdown(resources: Map<string, ResourceKey>): Record<string, number> {
    const breakdown: Record<string, number> = {};
    for (const key of resources.values()) {
        const typeName = getResourceTypeName(key.type);
        breakdown[typeName] = (breakdown[typeName] || 0) + 1;
    }
    return breakdown;
}

/**
 * Generates a breakdown of conflict counts by conflict type.
 */
export function getConflictTypeBreakdown(conflicts: ConflictInfo[]): Record<string, number> {
    const breakdown: Record<string, number> = {};
    // Initialize with all possible types to ensure they appear even if count is 0
    Object.values(ConflictType).forEach(type => {
        if (typeof type === 'string') { // Enum values can be numbers or strings
             breakdown[type] = 0;
        }
    });
    for (const conflict of conflicts) {
        const typeKey = String(conflict.type); // Use string representation of enum
        breakdown[typeKey] = (breakdown[typeKey] || 0) + 1;
    }
    return breakdown;
}

/**
 * Generates a breakdown of conflict counts by severity.
 */
export function getSeverityBreakdown(conflicts: ConflictInfo[]): Record<string, number> {
    const breakdown: Record<string, number> = {};
     // Initialize with all possible severities
    Object.values(ConflictSeverity).forEach(severity => {
         if (typeof severity === 'string') {
            breakdown[severity] = 0;
         }
    });
    for (const conflict of conflicts) {
        const severityKey = String(conflict.severity); // Use string representation of enum
        breakdown[severityKey] = (breakdown[severityKey] || 0) + 1;
    }
    return breakdown;
}
