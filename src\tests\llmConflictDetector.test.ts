import { LlmConflictDetector } from '../services/conflict/LlmConflictDetector.js';
import { ConflictSeverity, ConflictType } from '../types/conflict/index.js';
import { ResourceInfo } from '../types/resource/interfaces.js';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import BinaryResourceType from '@s4tk/models/lib/enums/binary-resources.js';

describe('LlmConflictDetector', () => {
  let detector: LlmConflictDetector;

  beforeEach(() => {
    // Create a mock MCP client
    const mockMcpClient = {
      callTool: vi.fn().mockResolvedValue({
        hasConflict: true,
        severity: 'HIGH',
        description: 'Mocked LLM conflict description',
        recommendations: ['Recommendation 1', 'Recommendation 2'],
        confidence: 0.85
      })
    };

    // Create the detector with the mock client
    detector = new LlmConflictDetector();

    // Replace the mcpClient property
    (detector as any).mcpClient = mockMcpClient;
  });

  it('should detect conflicts between resources using LLM', async () => {
    // Create two resources
    const resource1: ResourceInfo = {
      id: 1,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 123n
      },
      metadata: {
        name: 'Resource 1',
        resourceType: 'TUNING',
        contentSnippet: 'This is the content of resource 1'
      }
    };

    const resource2: ResourceInfo = {
      id: 2,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 456n
      },
      metadata: {
        name: 'Resource 2',
        resourceType: 'TUNING',
        contentSnippet: 'This is the content of resource 2'
      }
    };

    // Detect conflicts
    const result = await detector.detectConflicts(resource1, resource2);

    // Check that a conflict was detected
    expect(result).toBeDefined();
    expect(result?.type).toBe(ConflictType.TUNING);
    expect(result?.severity).toBe(ConflictSeverity.HIGH);
    expect(result?.description).toBe('Mocked LLM conflict description');
    expect(result?.recommendations).toEqual(['Recommendation 1', 'Recommendation 2']);
    expect(result?.confidence).toBe(0.85);
    expect(result?.affectedResources).toEqual([resource1.key, resource2.key]);
  });

  it('should enhance conflict info using LLM', async () => {
    // Create a conflict
    const conflict = {
      id: 'test-conflict',
      type: ConflictType.TUNING,
      severity: ConflictSeverity.MEDIUM,
      description: 'Original conflict description',
      affectedResources: [
        { type: BinaryResourceType.StringTable, group: 0n, instance: 123n },
        { type: BinaryResourceType.StringTable, group: 0n, instance: 456n }
      ],
      timestamp: Date.now()
    };

    // Create two resources
    const resource1: ResourceInfo = {
      id: 1,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 123n
      },
      metadata: {
        name: 'Resource 1',
        resourceType: 'TUNING',
        contentSnippet: 'This is the content of resource 1'
      }
    };

    const resource2: ResourceInfo = {
      id: 2,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 456n
      },
      metadata: {
        name: 'Resource 2',
        resourceType: 'TUNING',
        contentSnippet: 'This is the content of resource 2'
      }
    };

    // Enhance conflict info
    const result = await detector.enhanceConflictInfo(conflict, resource1, resource2);

    // Check that the conflict was enhanced
    expect(result).toBeDefined();
    expect(result?.description).toBe('Mocked LLM conflict description');
    expect(result?.recommendations).toEqual(['Recommendation 1', 'Recommendation 2']);
    expect(result?.confidence).toBe(0.85);
  });

  it('should handle missing metadata gracefully', async () => {
    // Create two resources with missing metadata
    const resource1: ResourceInfo = {
      id: 1,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 123n
      }
    };

    const resource2: ResourceInfo = {
      id: 2,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 456n
      }
    };

    // Detect conflicts
    const result = await detector.detectConflicts(resource1, resource2);

    // Check that no conflict was detected
    expect(result).toBeNull();
  });

  it('should handle empty content gracefully', async () => {
    // Create a mock MCP client that returns no conflict
    const mockMcpClient = {
      callTool: vi.fn().mockResolvedValue({
        hasConflict: false,
        severity: 'LOW',
        description: 'No conflict detected',
        recommendations: []
      })
    };

    // Replace the mcpClient property
    (detector as any).mcpClient = mockMcpClient;

    // Create two resources with empty content
    const resource1: ResourceInfo = {
      id: 1,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 123n
      },
      metadata: {
        name: 'Resource 1',
        resourceType: 'TUNING',
        contentSnippet: ''
      }
    };

    const resource2: ResourceInfo = {
      id: 2,
      key: {
        type: BinaryResourceType.StringTable,
        group: 0n,
        instance: 456n
      },
      metadata: {
        name: 'Resource 2',
        resourceType: 'TUNING',
        contentSnippet: ''
      }
    };

    // Detect conflicts
    const result = await detector.detectConflicts(resource1, resource2);

    // Check that no conflict was detected
    expect(result).toBeNull();
  });
});
