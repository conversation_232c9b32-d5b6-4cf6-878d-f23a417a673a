/**
 * Enums related to CAS Parts, based on community knowledge.
 * Values might need verification against specific game versions or tools.
 */

// Note: Sims 4 uses bit flags, so values are powers of 2.
// Multiple flags can be combined using bitwise OR (|).

export enum CasAgeFlags {
  NONE = 0,
  BABY = 0x1, // 1
  TODDLER = 0x2, // 2
  CHILD = 0x4, // 4
  TEEN = 0x8, // 8
  YOUNGADULT = 0x10, // 16
  ADULT = 0x20, // 32
  ELDER = 0x40, // 64
  // Common combinations
  TODDLER_TO_ELDER = 0x7E, // T+C+Y+A+E
  CHILD_TO_ELDER = 0x7C,   // C+Y+A+E
  TEEN_TO_ELDER = 0x78,    // T+Y+A+E
  ADULT_ELDER = 0x60,      // A+E
}

export enum CasGenderFlags {
  NONE = 0,
  MALE = 0x1, // Often 0x1 or 0x4000 depending on context
  FEMALE = 0x2, // Often 0x2 or 0x8000 depending on context
  // Note: Game files might use different flag values (e.g., 0x4000/0x8000).
  // Need to verify which flags are present in the specific CASP field.
}

export enum CasSpeciesFlags {
  NONE = 0,
  HUMAN = 0x8, // Value from community docs, needs verification
  DOG = 0x10, // Value from community docs, needs verification
  CAT = 0x20, // Value from community docs, needs verification
  HORSE = 0x40, // Value from community docs, needs verification (EP15+)
}

// Combined Age/Gender/Species flags are often stored together.
// Example: Teen-Elder Female Human might be (0x78 | 0x2 | 0x8)

export enum CasOccultFlags {
  NONE = 0,
  VAMPIRE = 0x1,
  MERMAID = 0x2,
  SPELLCASTER = 0x4,
  WEREWOLF = 0x8,
  ALIEN = 0x10, // Needs verification
  GHOST = 0x20, // Needs verification
  PLANTSIM = 0x40, // Needs verification
  SKELETON = 0x80, // Needs verification
}

export enum CasOutfitTypeFlags {
  NONE = 0,
  EVERYDAY = 0x1,
  FORMAL = 0x2,
  ATHLETIC = 0x4,
  SLEEP = 0x8,
  PARTY = 0x10,
  BATHING = 0x20, // Swimwear
  CAREER = 0x40,
  SITUATION = 0x80,
  SPECIAL = 0x100, // Includes Batuu, etc.
  HOTWEATHER = 0x200,
  COLDWEATHER = 0x400,
}

// BodyType enum is very large and complex.
// For now, we'll just store the raw number.
// Example values (need verification):
// HAIR = 0x02, UPPER_BODY = 0x03, LOWER_BODY = 0x04, FULL_BODY = 0x05,
// SHOES = 0x08, HAT = 0x01, EARRINGS = 0x0B, NECKLACE = 0x0C, etc.
// export enum CasBodyType { ... }