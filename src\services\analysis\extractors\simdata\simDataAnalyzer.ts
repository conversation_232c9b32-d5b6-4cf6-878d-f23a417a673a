import { Logger } from '../../../../utils/logging/logger.js';
import { ParsedSimData, SimDataColumn, SimDataInstance, getDataTypeName } from './simDataTypes.js';
import { ResourceMetadata } from '../../../../types/resource/interfaces.js'; // Import ResourceMetadata

const log = new Logger('SimDataAnalyzer');

/**
 * Analyzes parsed SimData to extract detailed metadata and insights.
 */
export class SimDataAnalyzer {
    private logger: Logger;

    /**
     * Create a new SimDataAnalyzer.
     * @param logger The logger instance.
     */
    constructor(logger: Logger) {
        this.logger = logger;
    }

    /**
     * Analyzes the parsed SimData and extracts detailed metadata.
     * @param simData The parsed SimData object.
     * @returns A partial ResourceMetadata object with analysis results.
     */
    public analyze(simData: ParsedSimData): Partial<ResourceMetadata> {
        const analysisMetadata: Partial<ResourceMetadata> = {};

        // Extract basic information
        const schemaName = simData.schema?.name || 'Unknown';
        const instanceCount = simData.instances.length;

        // Add basic metadata
        analysisMetadata.simDataSchemaName = schemaName;
        analysisMetadata.simDataInstanceCount = instanceCount;

        // Extract schema information
        if (simData.schema) {
            analysisMetadata.simDataSchemaId = simData.schema.schemaId;
            analysisMetadata.simDataSchemaHash = simData.schema.hash.toString(16);

            // Add schema version and flags if available
            if (simData.version !== undefined) { // Use simData.version
                analysisMetadata.simDataSchemaVersion = simData.version;
            }

            if (simData.flags !== undefined) { // Use simData.flags
                analysisMetadata.simDataSchemaFlags = simData.flags;
            }

            // Extract column information
            const columns = simData.schema.columns;
            analysisMetadata.simDataColumnCount = columns.length;

            // Create a summary of column types
            const columnTypes: Record<string, number> = {};
            columns.forEach((column: SimDataColumn) => {
                const typeName = getDataTypeName(column.type); // Need getDataTypeName
                columnTypes[typeName] = (columnTypes[typeName] || 0) + 1;
            });
            analysisMetadata.simDataColumnTypes = JSON.stringify(columnTypes);

            // Add column names for better understanding
            const columnNames = columns.map(col => col.name).join(', ');
            analysisMetadata.simDataColumnNames = columnNames.length > 500 ?
                columnNames.substring(0, 497) + '...' : columnNames;

            // Identify important columns based on common patterns
            const keyColumns = columns.filter(col =>
                col.name.includes('Key') ||
                col.name.includes('ID') ||
                col.name.includes('Id') ||
                col.name.includes('Reference') ||
                col.name.includes('Ref') ||
                col.type === 20 // ResourceKey type
            ).map(col => col.name);

            if (keyColumns.length > 0) {
                analysisMetadata.simDataKeyColumns = keyColumns.join(', ');
            }

            // Categorize columns by their purpose
            const columnCategories: Record<string, string> = {};

            // Define critical gameplay column patterns
            const criticalColumnPatterns = [
                'multiplier', 'chance', 'probability', 'duration', 'cost', 'value',
                'weight', 'priority', 'threshold', 'score', 'buff', 'motive',
                'skill', 'trait', 'stat', 'tuning', 'level', 'gain', 'decay'
            ];

            // Identify critical gameplay columns
            const criticalGameplayColumns: string[] = [];

            columns.forEach(col => {
                // Categorize column
                if (col.type === 20) {
                    columnCategories[col.name] = 'reference';
                } else if (keyColumns.includes(col.name)) {
                    columnCategories[col.name] = 'key';
                } else if (col.name.toLowerCase().includes('name') ||
                           col.name.toLowerCase().includes('description') ||
                           col.name.toLowerCase().includes('text')) {
                    columnCategories[col.name] = 'text';
                } else if (criticalColumnPatterns.some(pattern =>
                    col.name.toLowerCase().includes(pattern))) {
                    columnCategories[col.name] = 'gameplay';
                    criticalGameplayColumns.push(col.name);
                } else {
                    columnCategories[col.name] = 'data';
                }
            });

            // Store column categories
            analysisMetadata.simDataColumnCategories = JSON.stringify(columnCategories);

            // Store critical gameplay columns if any
            if (criticalGameplayColumns.length > 0) {
                 // Store in metadata
                 analysisMetadata.simDataCriticalGameplayColumns = JSON.stringify(criticalGameplayColumns);
            }

            // Check for schema inheritance patterns
            if (schemaName.includes('_')) {
                const parts = schemaName.split('_');
                if (parts.length >= 2) {
                    const potentialParent = parts[0];
                    const schemaInheritance = [{
                        parent: potentialParent,
                        inherited: columns.map(col => col.name)
                    }];
                    analysisMetadata.simDataSchemaInheritance = JSON.stringify(schemaInheritance);
                }
            }
        }

        // Analyze all instances to find value patterns and ranges
        if (simData.instances.length > 0 && simData.schema) {
            // Initialize value ranges for numeric columns
            const valueRanges: Record<string, { min: number; max: number; sum: number; count: number }> = {};

            // Initialize value patterns for string columns
            const valuePatterns: Record<string, Set<string>> = {};

            // Initialize critical gameplay values
            const criticalGameplayValues: Record<string, any[]> = {};

            // Process all instances to collect statistics
            for (const instance of simData.instances) {
                // Process each column in the instance
                for (const column of simData.schema.columns) {
                    const value = instance.values[column.name];

                    // Skip undefined or null values
                    if (value === undefined || value === null) continue;

                    // Process numeric values for ranges
                    if (typeof value === 'number') {
                        if (!valueRanges[column.name]) {
                            valueRanges[column.name] = {
                                min: value,
                                max: value,
                                sum: value,
                                count: 1
                            };
                        } else {
                            const range = valueRanges[column.name];
                            range.min = Math.min(range.min, value);
                            range.max = Math.max(range.max, value);
                            range.sum += value;
                            range.count++;
                        }
                    }

                    // Process string values for patterns
                    if (typeof value === 'string' && value.length > 0) {
                        if (!valuePatterns[column.name]) {
                            valuePatterns[column.name] = new Set<string>();
                        }

                        // Store pattern if it's not too long and we don't have too many
                        if (value.length < 50 && valuePatterns[column.name].size < 10) {
                            valuePatterns[column.name].add(value);
                        }
                    }

                    // Collect critical gameplay values
                    if (analysisMetadata.simDataCriticalGameplayColumns &&
                        JSON.parse(analysisMetadata.simDataCriticalGameplayColumns).includes(column.name)) {
                        if (!criticalGameplayValues[column.name]) {
                            criticalGameplayValues[column.name] = [];
                        }

                        // Add value to the list (limit to 10 values per column)
                        if (criticalGameplayValues[column.name].length < 10) {
                            criticalGameplayValues[column.name].push(value);
                        }
                    }
                }
            }

            // Convert value ranges to final format with averages
            const finalValueRanges: Record<string, { min: number; max: number; avg: number }> = {};
            for (const [column, range] of Object.entries(valueRanges)) {
                finalValueRanges[column] = {
                    min: range.min,
                    max: range.max,
                    avg: range.sum / range.count
                };
            }

            // Convert value patterns to arrays
            const finalValuePatterns: Record<string, string[]> = {};
            for (const [column, patterns] of Object.entries(valuePatterns)) {
                finalValuePatterns[column] = Array.from(patterns);
            }

            // Store the results
            analysisMetadata.simDataValueRanges = JSON.stringify(finalValueRanges);
            analysisMetadata.simDataValuePatterns = JSON.stringify(finalValuePatterns);

            // Store critical gameplay values
            if (Object.keys(criticalGameplayValues).length > 0) {
                analysisMetadata.simDataCriticalGameplayValues = JSON.stringify(criticalGameplayValues);
            }

            // Create instance summary
            const instanceNameCounts: Record<string, number> = {};
             for (const instance of simData.instances) {
                 const name = instance.name || 'Unnamed';
                 instanceNameCounts[name] = (instanceNameCounts[name] || 0) + 1;
             }

            const instanceSummary = {
                count: simData.instances.length,
                nameDistribution: instanceNameCounts,
                valueDistribution: Object.keys(finalValueRanges).length > 0 ? finalValueRanges : undefined,
                commonPatterns: Object.keys(finalValuePatterns).length > 0 ? finalValuePatterns : undefined
            };

            analysisMetadata.simDataInstanceSummary = JSON.stringify(instanceSummary);

            // Calculate complexity score based on schema and instances
            const complexityFactors = [
                simData.schema.columns.length * 2, // More columns = more complex
                simData.instances.length, // More instances = more complex
                Object.keys(criticalGameplayValues).length * 5, // Critical gameplay values increase complexity
                analysisMetadata.simDataSchemaInheritance ? 10 : 0, // Schema inheritance adds complexity
            ];

            // Calculate raw score and normalize to 0-100 range
            const rawComplexityScore = complexityFactors.reduce((sum, factor) => sum + factor, 0);
            const normalizedScore = Math.min(100, Math.max(0, Math.round(rawComplexityScore / 2)));

            analysisMetadata.simDataComplexityScore = normalizedScore;
        }

         // Try to determine the semantic meaning of this SimData
         try {
             const schemaName = simData.schema?.name;
             if (schemaName) {
                 // Common SimData schema patterns
                 if (schemaName.includes('Trait')) {
                     analysisMetadata.simDataSemanticType = 'Trait';
                 } else if (schemaName.includes('Buff')) {
                     analysisMetadata.simDataSemanticType = 'Buff';
                 } else if (schemaName.includes('Object')) {
                     analysisMetadata.simDataSemanticType = 'Object';
                 } else if (schemaName.includes('Sim')) {
                     analysisMetadata.simDataSemanticType = 'Sim';
                 } else if (schemaName.includes('Interaction')) {
                     analysisMetadata.simDataSemanticType = 'Interaction';
                 } else if (schemaName.includes('Recipe')) {
                     analysisMetadata.simDataSemanticType = 'Recipe';
                 } else if (schemaName.includes('Career')) {
                     analysisMetadata.simDataSemanticType = 'Career';
                 } else if (schemaName.includes('Aspiration')) {
                     analysisMetadata.simDataSemanticType = 'Aspiration';
                 } else if (schemaName.includes('Lot')) {
                     analysisMetadata.simDataSemanticType = 'Lot';
                 } else if (schemaName.includes('Skill')) {
                     analysisMetadata.simDataSemanticType = 'Skill';
                 }
             }
         } catch (semanticError) {
             this.logger.warn(`Error determining semantic type for SimData: ${semanticError}`);
         }


        return analysisMetadata;
    }
}

// Using getDataTypeName from simDataTypes.js