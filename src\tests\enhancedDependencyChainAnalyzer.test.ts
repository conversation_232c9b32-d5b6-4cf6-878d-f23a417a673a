/**
 * Tests for EnhancedDependencyChainAnalyzer
 */

import { describe, it, beforeEach, afterEach, expect, vi, Mock } from 'vitest';
import { EnhancedDependencyChainAnalyzer } from '../services/analysis/semantic/enhancedDependencyChainAnalyzer.js';
import { DatabaseService } from '../services/databaseService.js';
import { Logger } from '../utils/logging/logger.js';
import * as ResourceTypes from '../constants/resourceTypes.js';

// Mock dependencies
vi.mock('../services/databaseService.js');
vi.mock('../utils/logging/logger.js');

describe('EnhancedDependencyChainAnalyzer', () => {
  let analyzer: EnhancedDependencyChainAnalyzer;
  let mockDatabaseService: DatabaseService;
  let mockLogger: Logger;

  // Sample test data
  const mockResources = [
    { 
      id: 1, 
      resourceType: ResourceTypes.RESOURCE_TYPE_TUNING, 
      group: 0, 
      instance: '123456789', 
      packageId: 1,
      name: 'TestTuning'
    },
    { 
      id: 2, 
      resourceType: ResourceTypes.RESOURCE_TYPE_SIMDATA, 
      group: 0, 
      instance: '123456789', 
      packageId: 1,
      name: 'TestSimData'
    },
    { 
      id: 3, 
      resourceType: ResourceTypes.RESOURCE_TYPE_SCRIPT, 
      group: 0, 
      instance: '987654321', 
      packageId: 2,
      name: 'TestScript'
    }
  ];

  const mockDependencies = [
    { resourceId: 1, targetType: ResourceTypes.RESOURCE_TYPE_SIMDATA, targetGroup: 0, targetInstance: '123456789', dependencyType: 'reference' },
    { resourceId: 1, targetType: ResourceTypes.RESOURCE_TYPE_SCRIPT, targetGroup: 0, targetInstance: '987654321', dependencyType: 'required' },
    { resourceId: 3, targetType: ResourceTypes.RESOURCE_TYPE_TUNING, targetGroup: 0, targetInstance: '123456789', dependencyType: 'soft_reference' }
  ];

  const mockPackages = [
    { id: 1, name: 'TestPackage1' },
    { id: 2, name: 'TestPackage2' }
  ];

  const mockMetadata = [
    { resourceId: 1, key: 'name', value: 'TestTuning' },
    { resourceId: 1, key: 'resourcePurpose', value: 'tuning' },
    { resourceId: 3, key: 'impactedSystems', value: JSON.stringify(['skills', 'interactions']) }
  ];

  // Setup mocks before each test
  beforeEach(() => {
    // Create mock database service with repositories
    mockDatabaseService = {
      resources: {
        getResourceById: vi.fn((id) => Promise.resolve(
          mockResources.find(r => r.id === id) || null
        )),
        findResourceByTGI: vi.fn((type, group, instance) => Promise.resolve(
          mockResources.find(r => r.resourceType === type && 
                              r.group === group && 
                              r.instance === instance) || null
        )),
        getResourcesByPackageId: vi.fn((packageId) => Promise.resolve(
          mockResources.filter(r => r.packageId === packageId)
        ))
      },
      dependencies: {
        getDependenciesBySourceId: vi.fn((resourceId) => Promise.resolve(
          mockDependencies.filter(d => d.resourceId === resourceId)
        )),
        getDependenciesByTargetTGI: vi.fn((type, group, instance) => Promise.resolve(
          mockDependencies.filter(d => d.targetType === type && 
                                  d.targetGroup === group && 
                                  d.targetInstance === instance)
        )),
        getDependenciesForPackage: vi.fn(() => Promise.resolve(mockDependencies))
      },
      packages: {
        getPackageById: vi.fn((id) => Promise.resolve(
          mockPackages.find(p => p.id === id) || null
        ))
      },
      metadata: {
        getMetadataByResourceId: vi.fn((resourceId) => Promise.resolve(
          mockMetadata.filter(m => m.resourceId === resourceId)
        )),
        getMetadataByKey: vi.fn((key) => Promise.resolve(
          mockMetadata.filter(m => m.key === key)
        )),
        saveMetadata: vi.fn(() => Promise.resolve())
      },
      parsedContent: {
        getContentByResourceId: vi.fn(() => Promise.resolve({
          resourceId: 1,
          semanticType: 'interaction',
          content: '<tuning><trait>TestTrait</trait><interaction>TestInteraction</interaction></tuning>'
        }))
      },
      checkTableExists: vi.fn(() => Promise.resolve(true)),
      executeQuery: vi.fn(() => Promise.resolve())
    } as unknown as DatabaseService;

    // Create mock logger
    mockLogger = {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn()
    } as unknown as Logger;

    // Create analyzer instance
    analyzer = new EnhancedDependencyChainAnalyzer(mockDatabaseService, mockLogger);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should initialize correctly', async () => {
    await analyzer.initialize();
    expect(mockDatabaseService.metadata.getMetadataByKey).toHaveBeenCalledWith('resourcePurpose');
    expect(mockLogger.info).toHaveBeenCalled();
  });

  it('should analyze enhanced dependency chain', async () => {
    const result = await analyzer.analyzeEnhancedDependencyChain(1, 2, 'forward', true, true);
    
    // Check that root node exists and has correct properties
    expect(result.root).toBeDefined();
    expect(result.root.resourceId).toBe(1);
    expect(result.root.resourceName).toBe('TestTuning');
    expect(result.root.packageId).toBe(1);
    expect(result.root.packageName).toBe('TestPackage1');
    
    // Verify that children nodes are properly built
    expect(result.root.children.length).toBe(2); // Should have two dependencies
    
    // Verify cross-package dependencies detected
    expect(result.crossPackageDependencies).toBeDefined();
    expect(result.crossPackageDependencies.length).toBeGreaterThan(0);
    
    // Verify gameplay systems analysis
    expect(result.impactedGameplaySystems).toBeDefined();
    
    // Verify visualization metadata
    expect(result.visualizationMetadata).toBeDefined();
    expect(result.visualizationMetadata.layoutType).toBeDefined();
    expect(result.visualizationMetadata.nodeCategories).toBeDefined();
  });

  it('should analyze package dependencies', async () => {
    const result = await analyzer.analyzePackageDependencies([1, 2]);
    
    // Check results structure
    expect(result.dependencies).toBeDefined();
    expect(result.packages).toBeDefined();
    expect(result.packages.length).toBe(2);
    
    // Verify cross-package dependencies
    expect(result.dependencies.length).toBeGreaterThan(0);
    
    // Check that package info is correct
    expect(result.packages[0].packageId).toBe(1);
    expect(result.packages[0].packageName).toBe('TestPackage1');
  });

  it('should export for visualization', async () => {
    const chain = await analyzer.analyzeEnhancedDependencyChain(1);
    const visualization = analyzer.exportForVisualization(chain);
    
    // Check visualization structure
    expect(visualization.nodes).toBeDefined();
    expect(visualization.links).toBeDefined();
    expect(visualization.metadata).toBeDefined();
    
    // Verify nodes have required visualization properties
    expect(visualization.nodes.length).toBeGreaterThan(0);
    expect(visualization.nodes[0].id).toBe(1);
    expect(visualization.nodes[0].label).toBe('TestTuning');
    expect(visualization.nodes[0].category).toBeDefined();
    expect(visualization.nodes[0].importance).toBeDefined();
    
    // Verify links have correct structure
    expect(visualization.links.length).toBeGreaterThan(0);
    expect(visualization.links[0].source).toBeDefined();
    expect(visualization.links[0].target).toBeDefined();
    expect(visualization.links[0].type).toBeDefined();
    expect(visualization.links[0].strength).toBeDefined();
  });

  it('should properly dispose resources', async () => {
    await analyzer.dispose();
    expect(mockLogger.info).toHaveBeenCalledWith('Enhanced Dependency Chain Analyzer resources disposed successfully');
  });
});