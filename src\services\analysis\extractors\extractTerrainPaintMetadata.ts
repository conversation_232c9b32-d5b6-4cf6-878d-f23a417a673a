/**
 * Extracts metadata from terrain paint resources.
 *
 * Unlike Light and Slot resources, terrain paints don't have a dedicated binary resource type.
 * Instead, they are implemented using a combination of standard resource types:
 * - 0x220557DA: String Table (STBL) resources for names and descriptions
 * - 0x3C2A8647: Object Thumbnails for terrain paint swatches
 * - 0x00B2D882: Image resources (likely DDS textures for the terrain paint)
 * - 0xEBCBB16C and 0x01D0E75D: Additional resources related to terrain paint
 *
 * This extractor is a facade that delegates to the TerrainPaintAnalyzer for actual extraction.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { ResourceMetadata } from '../../../types/resource/interfaces.js';
import { TerrainPaintAnalyzer, TERRAIN_PAINT_RESOURCE_TYPES } from './terrainpaint/terrainPaintAnalyzer.js';

const log = new Logger('TerrainPaintMetadataExtractor');

// Cache for package resources to avoid duplicate extraction
const packageResourcesCache: Map<number, { key: ResourceKey, buffer: Buffer, resourceId: number }[]> = new Map();

/**
 * Extracts metadata from terrain paint resources.
 *
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object with terrain paint information.
 */
export async function extractTerrainPaintMetadata(
    key: ResourceKey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    try {
        log.info(`Extracting metadata from terrain paint resource: ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)}`);

        // Get the package ID for this resource
        const packageId = await getPackageIdForResource(resourceId, databaseService);
        if (!packageId) {
            log.warn(`Could not find package ID for resource ${resourceId}`);
            return {
                contentSnippet: '[Could not find package for terrain paint resource]',
                extractorUsed: 'terrainpaint'
            };
        }

        // Get all resources in the package
        const packageResources = await getPackageResources(packageId, databaseService);
        if (!packageResources || packageResources.length === 0) {
            log.warn(`Could not find resources for package ${packageId}`);
            return {
                contentSnippet: '[Could not find resources for terrain paint package]',
                extractorUsed: 'terrainpaint'
            };
        }

        // Create a terrain paint analyzer
        const analyzer = new TerrainPaintAnalyzer(databaseService);

        // Check if this is a terrain paint mod
        if (!analyzer.isTerrainPaintMod(packageResources)) {
            log.warn(`Package ${packageId} is not a terrain paint mod`);
            return {
                contentSnippet: '[Not a terrain paint mod]',
                extractorUsed: 'terrainpaint'
            };
        }

        // Analyze the resources
        const metadata = await analyzer.analyzeResources(packageResources, packageId);

        // Return the metadata
        return {
            ...metadata,
            extractorUsed: 'terrainpaint'
        };
    } catch (error) {
        log.error(`Error extracting terrain paint metadata: ${error}`);
        return {
            contentSnippet: `[Error extracting terrain paint metadata: ${error}]`,
            extractorUsed: 'terrainpaint',
            extractionError: String(error)
        };
    }
}

/**
 * Gets the package ID for a resource
 * @param resourceId Resource ID
 * @param databaseService Database service
 * @returns Package ID
 */
async function getPackageIdForResource(resourceId: number, databaseService: DatabaseService): Promise<number | undefined> {
    try {
        // Query the database for the package ID
        const result = await databaseService.executeQuery(
            'SELECT packageId FROM Resources WHERE id = ?',
            [resourceId]
        );

        if (result && result.length > 0) {
            return result[0].packageId;
        }

        return undefined;
    } catch (error) {
        log.error(`Error getting package ID for resource ${resourceId}: ${error}`);
        return undefined;
    }
}

/**
 * Gets all resources in a package
 * @param packageId Package ID
 * @param databaseService Database service
 * @returns Array of resources
 */
async function getPackageResources(packageId: number, databaseService: DatabaseService): Promise<{ key: ResourceKey, buffer: Buffer, resourceId: number }[]> {
    try {
        // Check if we already have the resources in the cache
        if (packageResourcesCache.has(packageId)) {
            return packageResourcesCache.get(packageId)!;
        }

        // Query the database for all resources in the package
        // Note: 'group' is a reserved keyword in SQL, so we need to quote it
        // Note: The Resources table doesn't have a 'name' column
        const result = await databaseService.executeQuery(
            'SELECT id, type, "group", instance FROM Resources WHERE packageId = ?',
            [packageId]
        );

        if (!result || result.length === 0) {
            return [];
        }

        // Filter resources to only include those that are relevant for terrain paint analysis
        const relevantResourceTypes = Object.values(TERRAIN_PAINT_RESOURCE_TYPES);

        const resources: { key: ResourceKey, buffer: Buffer, resourceId: number }[] = [];

        for (const row of result) {
            // Skip resources that are not relevant for terrain paint analysis
            if (!relevantResourceTypes.includes(row.type)) {
                continue;
            }

            // Get the resource buffer
            const buffer = await getResourceBuffer(row.id, databaseService);
            if (!buffer) {
                continue;
            }

            // Create a resource key
            const key: ResourceKey = {
                type: row.type,
                group: row.group,
                instance: row.instance,
                name: '' // The Resources table doesn't have a 'name' column
            };

            // Add the resource to the array
            resources.push({
                key,
                buffer,
                resourceId: row.id
            });
        }

        // Cache the resources
        packageResourcesCache.set(packageId, resources);

        return resources;
    } catch (error) {
        log.error(`Error getting resources for package ${packageId}: ${error}`);
        return [];
    }
}

/**
 * Gets the buffer for a resource
 * @param resourceId Resource ID
 * @param databaseService Database service
 * @returns Resource buffer
 */
async function getResourceBuffer(resourceId: number, databaseService: DatabaseService): Promise<Buffer | undefined> {
    try {
        // Query the database for the resource buffer
        const result = await databaseService.executeQuery(
            'SELECT buffer FROM ResourceBuffers WHERE resourceId = ?',
            [resourceId]
        );

        if (result && result.length > 0 && result[0].buffer) {
            return result[0].buffer;
        }

        return undefined;
    } catch (error) {
        log.error(`Error getting buffer for resource ${resourceId}: ${error}`);
        return undefined;
    }
}
