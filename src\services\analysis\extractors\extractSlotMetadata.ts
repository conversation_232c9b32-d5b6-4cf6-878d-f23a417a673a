import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey as AppResourceKey } from '../../../types/resource/interfaces.js';
import { ResourceMetadata } from '../../../types/resource/interfaces.js';

const log = new Logger('SlotMetadataExtractor');

/**
 * Extracts metadata from Slot resources.
 * Slot resources define attachment points on objects where other objects can be placed.
 *
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object with slot information.
 */
export async function extractSlotMetadata(
    key: AppResource<PERSON>ey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};

    try {
        // Verify this is a Slot resource
        if (key.type !== 0xD3044521) { // Updated from 0x00000002 to 0xD3044521 based on research
            log.warn(`Resource ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)} is not a Slot resource`);
            return {
                contentSnippet: `[Not a Slot resource: ${key.type.toString(16)}]`,
                extractorUsed: 'slot'
            };
        }

        log.info(`Extracting metadata from Slot resource: ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)}`);

        // Slot resources have a specific binary format
        // First 4 bytes: Version (uint32)
        const version = buffer.readUInt32LE(0);
        extractedMetadata.slotVersion = version;

        // Next 4 bytes: Flags (uint32)
        const flags = buffer.readUInt32LE(4);
        extractedMetadata.slotFlags = flags;

        // Determine slot properties based on flags
        const isDecorative = (flags & 0x01) !== 0;
        const isUserFacing = (flags & 0x02) !== 0;
        const preventSiblingIntersection = (flags & 0x04) !== 0;

        extractedMetadata.slotIsDecorative = isDecorative;
        extractedMetadata.slotIsUserFacing = isUserFacing;
        extractedMetadata.slotPreventSiblingIntersection = preventSiblingIntersection;

        // Next 4 bytes: Slot count (uint32)
        const slotCount = buffer.readUInt32LE(8);
        extractedMetadata.slotCount = slotCount;

        // Extract slot type information if available
        if (buffer.length >= 16) {
            // Bytes 12-15: Slot type ID (uint32)
            const slotTypeId = buffer.readUInt32LE(12);
            extractedMetadata.slotTypeId = slotTypeId;
        }

        // Extract slot position information if available
        if (slotCount > 0 && buffer.length >= 20 + (slotCount * 16)) {
            const slots = [];
            let offset = 20;

            for (let i = 0; i < slotCount; i++) {
                // Each slot has:
                // - 4 bytes: X position (float)
                // - 4 bytes: Y position (float)
                // - 4 bytes: Z position (float)
                // - 4 bytes: Bone name hash (uint32)
                const x = buffer.readFloatLE(offset);
                const y = buffer.readFloatLE(offset + 4);
                const z = buffer.readFloatLE(offset + 8);
                const boneNameHash = buffer.readUInt32LE(offset + 12);

                slots.push({ x, y, z, boneNameHash });
                offset += 16;
            }

            extractedMetadata.slotPositions = JSON.stringify(slots);
        }

        // Extract search radius if available
        if (buffer.length >= 16 + (slotCount * 16) + 4) {
            const searchRadiusOffset = 16 + (slotCount * 16);
            const searchRadius = buffer.readFloatLE(searchRadiusOffset);
            extractedMetadata.slotSearchRadius = searchRadius;
        }

        // Create a content snippet
        let contentSnippet = `Slot v${version} (${slotCount} slots)`;
        if (isDecorative) {
            contentSnippet += ', Decorative';
        }
        if (isUserFacing) {
            contentSnippet += ', User-Facing';
        }

        extractedMetadata.contentSnippet = contentSnippet;
        extractedMetadata.extractorUsed = 'slot';

        return extractedMetadata;
    } catch (error) {
        log.error(`Error extracting Slot metadata: ${error}`);
        return {
            contentSnippet: `[Error extracting Slot metadata: ${error}]`,
            extractorUsed: 'slot',
            extractionError: String(error)
        };
    }
}
