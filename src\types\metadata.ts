﻿import { BinaryResourceType } from './resource/core.js';

/**
 * Base interface for package metadata
 */
export interface PackageMetadata {
  id: string;
  name: string;
  version: string;
  author: string;
  description?: string;
  dependencies?: string[];
  tags?: string[];
  lastModified?: Date;
  size?: number;
  gameVersion?: string;
  requiredPacks?: string[];
  customMetadata?: Record<string, unknown>;
}

/**
 * Interface for resource metadata
 */
export interface ResourceMetadata {
  hash?: string;
  name?: string;
  typeDescription?: string;
  isScript?: boolean;
  isTuning?: boolean;
  isAsset?: boolean;
  mods?: string[];
  resourceType?: BinaryResourceType;
  warning?: string;
  reason?: string;
  size?: number;
  lastModified?: Date;
  dependencies?: string[];
  customMetadata?: Record<string, unknown>;
}

/**
 * Interface for conflict metadata
 */
export interface ConflictMetadata {
  detectionMethod?: string;
  confidence?: number;
  resolutionStatus?: 'unresolved' | 'resolved' | 'ignored';
  resolutionMethod?: string;
  resolutionDate?: Date;
  notes?: string;
  customMetadata?: Record<string, unknown>;
}

/**
 * Interface for analysis metadata
 */
export interface AnalysisMetadata {
  startTime: Date;
  endTime?: Date;
  duration?: number;
  resourcesProcessed?: number;
  conflictsDetected?: number;
  warnings?: number;
  errors?: number;
  customMetadata?: Record<string, unknown>;
} 
