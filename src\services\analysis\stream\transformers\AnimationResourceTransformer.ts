/**
 * Animation Resource Transformer
 *
 * This module provides a specialized transformer for animation resources.
 * It processes animation resources in a streaming fashion, extracting metadata,
 * keyframes, tracks, and other animation components incrementally.
 */

import { TransformCallback } from 'stream';
import { Logger } from '../../../../utils/logging/logger.js';
import { BaseStreamTransformer, StreamTransformerOptions } from '../baseStreamTransformer.js';

// Create a logger for this module
const logger = new Logger('AnimationResourceTransformer');

/**
 * Animation parsing state
 */
enum AnimationParsingState {
    HEADER = 'header',
    ANIMATION_INFO = 'animation_info',
    TRACKS = 'tracks',
    KEYFRAMES = 'keyframes',
    EVENTS = 'events',
    COMPLETE = 'complete'
}

/**
 * Animation keyframe
 */
interface AnimationKeyframe {
    time: number;
    value: number[] | string;
    interpolation?: string;
}

/**
 * Animation track
 */
interface AnimationTrack {
    name: string;
    targetProperty: string;
    targetObject?: string;
    keyframes: AnimationKeyframe[];
}

/**
 * Animation event
 */
interface AnimationEvent {
    name: string;
    time: number;
    parameters?: Record<string, any>;
}

/**
 * Animation data
 */
interface AnimationData {
    name?: string;
    duration: number;
    frameRate: number;
    trackCount: number;
    eventCount: number;
    tracks: AnimationTrack[];
    events: AnimationEvent[];
    format?: string;
    version?: number;
    flags?: number;
    frameCount?: number;
}

/**
 * Animation transformer options
 */
export interface AnimationTransformerOptions extends StreamTransformerOptions {
    parseTracks?: boolean;
    parseKeyframes?: boolean;
    parseEvents?: boolean;
    maxKeyframes?: number;
}

/**
 * Animation resource transformer
 */
export class AnimationResourceTransformer extends BaseStreamTransformer {
    private state: {
        parsingState: AnimationParsingState;
        buffer: Buffer;
        bufferOffset: number;
        animationData: Partial<AnimationData>;
        trackParseCount: number;
        keyframeParseCount: number;
        eventParseCount: number;
        currentTrackIndex: number;
    };

    private parseTracks: boolean;
    private parseKeyframes: boolean;
    private parseEvents: boolean;
    private maxKeyframes: number;

    /**
     * Create a new animation resource transformer
     * @param options Transformer options
     */
    constructor(options: AnimationTransformerOptions = {}) {
        super('AnimationResourceTransformer', options);

        this.parseTracks = options.parseTracks !== false;
        this.parseKeyframes = options.parseKeyframes !== false;
        this.parseEvents = options.parseEvents !== false;
        this.maxKeyframes = options.maxKeyframes || 10000; // 10k keyframes max by default

        // Initialize state
        this.state = {
            parsingState: AnimationParsingState.HEADER,
            buffer: Buffer.alloc(0),
            bufferOffset: 0,
            animationData: {
                tracks: [],
                events: [],
                trackCount: 0,
                eventCount: 0,
                duration: 0,
                frameRate: 30
            },
            trackParseCount: 0,
            keyframeParseCount: 0,
            eventParseCount: 0,
            currentTrackIndex: 0
        };

        this.logger.debug(`Created animation transformer (parseTracks: ${this.parseTracks}, parseKeyframes: ${this.parseKeyframes}, parseEvents: ${this.parseEvents})`);
    }

    /**
     * Reset transformer state
     */
    public reset(): void {
        super.reset();

        // Reset state
        this.state = {
            parsingState: AnimationParsingState.HEADER,
            buffer: Buffer.alloc(0),
            bufferOffset: 0,
            animationData: {
                tracks: [],
                events: [],
                trackCount: 0,
                eventCount: 0,
                duration: 0,
                frameRate: 30
            },
            trackParseCount: 0,
            keyframeParseCount: 0,
            eventParseCount: 0,
            currentTrackIndex: 0
        };
    }

    /**
     * Get animation data
     */
    public getAnimationData(): Partial<AnimationData> {
        return { ...this.state.animationData };
    }

    /**
     * Process a chunk
     * @param chunk Chunk to process
     * @param encoding Chunk encoding
     * @param callback Callback function
     */
    protected processChunkImpl(
        chunk: Buffer,
        encoding: BufferEncoding,
        callback: (error?: Error | null, data?: any) => void
    ): void {
        try {
            // Append chunk to buffer
            this.appendToBuffer(chunk);

            // Process buffer based on current state
            this.processBuffer();

            // Pass the chunk through
            callback(null, chunk);

            // Emit data if we've completed parsing
            if (this.state.parsingState === AnimationParsingState.COMPLETE) {
                this.emitAnimationData();
            }
        } catch (error: any) {
            this.logger.error(`Error processing chunk: ${error.message}`);
            callback(error);
        }
    }

    /**
     * Process buffer based on current parsing state
     */
    private processBuffer(): void {
        // Process buffer until we run out of data or complete parsing
        while (this.canContinueParsing()) {
            switch (this.state.parsingState) {
                case AnimationParsingState.HEADER:
                    if (!this.parseHeader()) {
                        return; // Not enough data
                    }
                    break;

                case AnimationParsingState.ANIMATION_INFO:
                    if (!this.parseAnimationInfo()) {
                        return; // Not enough data
                    }
                    break;

                case AnimationParsingState.TRACKS:
                    if (!this.parseTracks || this.state.trackParseCount >= (this.state.animationData.trackCount || 0)) {
                        // Skip tracks parsing or already parsed all tracks
                        this.state.parsingState = AnimationParsingState.KEYFRAMES;
                        break;
                    }

                    if (!this.parseTrackData()) {
                        return; // Not enough data
                    }
                    break;

                case AnimationParsingState.KEYFRAMES:
                    if (!this.parseKeyframes) {
                        // Skip keyframes parsing
                        this.state.parsingState = AnimationParsingState.EVENTS;
                        break;
                    }

                    if (!this.parseKeyframeData()) {
                        return; // Not enough data
                    }
                    break;

                case AnimationParsingState.EVENTS:
                    if (!this.parseEvents || this.state.eventParseCount >= (this.state.animationData.eventCount || 0)) {
                        // Skip events parsing or already parsed all events
                        this.state.parsingState = AnimationParsingState.COMPLETE;
                        break;
                    }

                    if (!this.parseEventData()) {
                        return; // Not enough data
                    }
                    break;

                case AnimationParsingState.COMPLETE:
                    // All data parsed
                    return;
            }
        }
    }

    /**
     * Check if we can continue parsing
     */
    private canContinueParsing(): boolean {
        // Check if we have enough data to continue parsing
        return this.state.buffer.length - this.state.bufferOffset >= 4 &&
               this.state.parsingState !== AnimationParsingState.COMPLETE;
    }

    /**
     * Append chunk to buffer
     * @param chunk Chunk to append
     */
    private appendToBuffer(chunk: Buffer): void {
        // Create a new buffer with the combined length
        const newBuffer = Buffer.alloc(this.state.buffer.length - this.state.bufferOffset + chunk.length);

        // Copy existing buffer (excluding processed data)
        this.state.buffer.copy(newBuffer, 0, this.state.bufferOffset);

        // Copy new chunk
        chunk.copy(newBuffer, this.state.buffer.length - this.state.bufferOffset);

        // Update buffer and reset offset
        this.state.buffer = newBuffer;
        this.state.bufferOffset = 0;
    }

    /**
     * Parse animation header
     * @returns True if header was parsed successfully, false if more data is needed
     */
    private parseHeader(): boolean {
        // Need at least 4 bytes for magic check
        if (this.state.buffer.length - this.state.bufferOffset < 4) {
            return false;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Read header magic (4 bytes)
            const magicBytes = buffer.subarray(offset, offset + 4);
            const magic = magicBytes.toString('ascii');
            offset += 4;

            // Check for supported animation formats
            const supportedFormats = ['ANIM', 'CLIP', 'CLAF', 'ASMB', 'ASMP', 'BNAM', 'BNRY', 'SKMG'];

            if (!supportedFormats.includes(magic)) {
                // Check for DBPF header (nested package)
                if (magic === 'DBPF') {
                    this.logger.warn(`Animation resource contains DBPF header - treating as nested package`);
                    // Skip DBPF header and try to find animation data
                    return this.handleDBPFHeader();
                }

                // Check for empty or corrupted header
                if (magic.trim() === '' || magic.includes('\0')) {
                    this.logger.warn(`Animation resource has empty/corrupted header - attempting fallback parsing`);
                    return this.handleCorruptedHeader();
                }

                // Unknown format - try generic parsing
                this.logger.warn(`Unknown animation format '${magic}' - attempting generic parsing`);
                return this.handleUnknownFormat(magic);
            }

            // Need at least 12 bytes for standard header
            if (this.state.buffer.length - this.state.bufferOffset < 12) {
                return false;
            }

            // Read version (4 bytes)
            const version = buffer.readUInt32LE(offset);
            offset += 4;

            // Read flags (4 bytes)
            const flags = buffer.readUInt32LE(offset);
            offset += 4;

            // Store format information
            this.state.animationData.format = magic;
            this.state.animationData.version = version;
            this.state.animationData.flags = flags;

            // Update state
            this.state.parsingState = AnimationParsingState.ANIMATION_INFO;
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed animation header: format=${magic}, version=${version}, flags=${flags}`);

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing animation header: ${error.message}`);
            // Don't throw - try to continue with fallback parsing
            return this.handleParsingError(error);
        }
    }

    /**
     * Handle DBPF header in animation resource
     * @returns True if handled successfully, false if more data is needed
     */
    private handleDBPFHeader(): boolean {
        // DBPF header is 96 bytes, skip it and look for animation data
        const dbpfHeaderSize = 96;

        if (this.state.buffer.length - this.state.bufferOffset < dbpfHeaderSize + 4) {
            return false; // Not enough data
        }

        // Skip DBPF header
        this.state.bufferOffset += dbpfHeaderSize;

        // Try to find animation data after DBPF header
        return this.findAnimationDataInBuffer();
    }

    /**
     * Handle corrupted or empty header
     * @returns True if handled successfully, false if more data is needed
     */
    private handleCorruptedHeader(): boolean {
        // Try to find valid animation data by scanning the buffer
        return this.findAnimationDataInBuffer();
    }

    /**
     * Handle unknown format
     * @param magic The unknown magic string
     * @returns True if handled successfully, false if more data is needed
     */
    private handleUnknownFormat(magic: string): boolean {
        this.logger.warn(`Attempting to parse unknown animation format: ${magic}`);

        // Store the unknown format
        this.state.animationData.format = magic;

        // Try generic parsing
        return this.parseGenericAnimationData();
    }

    /**
     * Handle parsing errors with fallback
     * @param error The parsing error
     * @returns True if fallback succeeded, false otherwise
     */
    private handleParsingError(error: Error): boolean {
        this.logger.warn(`Animation parsing error, attempting fallback: ${error.message}`);

        // Try to find animation data by scanning
        return this.findAnimationDataInBuffer();
    }

    /**
     * Find animation data in buffer by scanning for known signatures
     * @returns True if animation data found, false otherwise
     */
    private findAnimationDataInBuffer(): boolean {
        const buffer = this.state.buffer;
        const supportedFormats = ['ANIM', 'CLIP', 'CLAF', 'ASMB', 'ASMP', 'BNAM', 'BNRY', 'SKMG'];

        // Scan buffer for animation signatures
        for (let i = this.state.bufferOffset; i <= buffer.length - 4; i++) {
            const magic = buffer.subarray(i, i + 4).toString('ascii');

            if (supportedFormats.includes(magic)) {
                this.logger.info(`Found animation data at offset ${i} with format: ${magic}`);
                this.state.bufferOffset = i;
                return this.parseHeader(); // Recursively parse the found header
            }
        }

        // No animation data found, use generic parsing
        this.logger.warn('No valid animation signature found, using generic parsing');
        return this.parseGenericAnimationData();
    }

    /**
     * Parse animation data generically when format is unknown
     * @returns True if parsing succeeded, false if more data is needed
     */
    private parseGenericAnimationData(): boolean {
        // Need at least 16 bytes for generic parsing
        if (this.state.buffer.length - this.state.bufferOffset < 16) {
            return false;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Try to extract basic information
            this.state.animationData.version = buffer.readUInt32LE(offset);
            offset += 4;

            this.state.animationData.frameCount = buffer.readUInt32LE(offset);
            offset += 4;

            this.state.animationData.duration = buffer.readFloatLE(offset);
            offset += 4;

            this.state.animationData.flags = buffer.readUInt32LE(offset);
            offset += 4;

            // Calculate frame rate if possible
            if (this.state.animationData.duration && this.state.animationData.duration > 0) {
                this.state.animationData.frameRate = (this.state.animationData.frameCount || 0) / this.state.animationData.duration;
            }

            // Update state
            this.state.parsingState = AnimationParsingState.COMPLETE; // Skip detailed parsing for unknown formats
            this.state.bufferOffset = offset;

            this.logger.debug(`Generic animation parsing completed`);

            return true;
        } catch (error: any) {
            this.logger.error(`Generic animation parsing failed: ${error.message}`);

            // Mark as complete with minimal data
            this.state.animationData.format = 'UNKNOWN';
            this.state.animationData.version = 0;
            this.state.parsingState = AnimationParsingState.COMPLETE;

            return true; // Don't fail completely
        }
    }

    /**
     * Parse animation info
     * @returns True if animation info was parsed successfully, false if more data is needed
     */
    private parseAnimationInfo(): boolean {
        // Need at least 20 bytes for animation info
        if (this.state.buffer.length - this.state.bufferOffset < 20) {
            return false;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Read name length (4 bytes)
            const nameLength = buffer.readUInt32LE(offset);
            offset += 4;

            // Check if we have enough data for the name
            if (buffer.length - offset < nameLength) {
                return false;
            }

            // Read name
            let name;
            if (nameLength > 0) {
                name = buffer.toString('utf8', offset, offset + nameLength);
                offset += nameLength;
            }

            // Read duration (4 bytes, float)
            const duration = buffer.readFloatLE(offset);
            offset += 4;

            // Read frame rate (4 bytes, float)
            const frameRate = buffer.readFloatLE(offset);
            offset += 4;

            // Read track count (4 bytes)
            const trackCount = buffer.readUInt32LE(offset);
            offset += 4;

            // Read event count (4 bytes)
            const eventCount = buffer.readUInt32LE(offset);
            offset += 4;

            // Update state
            this.state.animationData.name = name;
            this.state.animationData.duration = duration;
            this.state.animationData.frameRate = frameRate;
            this.state.animationData.trackCount = trackCount;
            this.state.animationData.eventCount = eventCount;

            this.state.parsingState = AnimationParsingState.TRACKS;
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed animation info: name=${name}, duration=${duration}, frameRate=${frameRate}, trackCount=${trackCount}, eventCount=${eventCount}`);

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing animation info: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse track data
     * @returns True if track was parsed successfully, false if more data is needed
     */
    private parseTrackData(): boolean {
        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Need at least 12 bytes for track header
            if (buffer.length - offset < 12) {
                return false;
            }

            // Read track name length (4 bytes)
            const nameLength = buffer.readUInt32LE(offset);
            offset += 4;

            // Check if we have enough data for the name
            if (buffer.length - offset < nameLength) {
                return false;
            }

            // Read track name
            let name = '';
            if (nameLength > 0) {
                name = buffer.toString('utf8', offset, offset + nameLength);
                offset += nameLength;
            }

            // Read target property length (4 bytes)
            const targetPropLength = buffer.readUInt32LE(offset);
            offset += 4;

            // Check if we have enough data for the target property
            if (buffer.length - offset < targetPropLength) {
                return false;
            }

            // Read target property
            let targetProperty = '';
            if (targetPropLength > 0) {
                targetProperty = buffer.toString('utf8', offset, offset + targetPropLength);
                offset += targetPropLength;
            }

            // Read target object length (4 bytes)
            const targetObjLength = buffer.readUInt32LE(offset);
            offset += 4;

            // Check if we have enough data for the target object
            if (buffer.length - offset < targetObjLength) {
                return false;
            }

            // Read target object
            let targetObject;
            if (targetObjLength > 0) {
                targetObject = buffer.toString('utf8', offset, offset + targetObjLength);
                offset += targetObjLength;
            }

            // Create track
            const track: AnimationTrack = {
                name: name || `Track${this.state.trackParseCount}`,
                targetProperty,
                targetObject,
                keyframes: []
            };

            // Add to tracks array
            this.state.animationData.tracks?.push(track);
            this.state.trackParseCount++;

            // Update state
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed track ${track.name} targeting ${targetProperty}${targetObject ? ' on ' + targetObject : ''}`);

            // Check if we've parsed all tracks
            if (this.state.trackParseCount >= (this.state.animationData.trackCount || 0)) {
                this.state.parsingState = AnimationParsingState.KEYFRAMES;
            }

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing track: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse keyframe data
     * @returns True if keyframes were parsed successfully, false if more data is needed
     */
    private parseKeyframeData(): boolean {
        // If we've already parsed all tracks, move to events
        if (this.state.trackParseCount === 0 || !this.state.animationData.tracks ||
            this.state.animationData.tracks.length === 0) {
            this.state.parsingState = AnimationParsingState.EVENTS;
            return true;
        }

        // If we're done with all tracks, move to events
        if (this.state.currentTrackIndex >= this.state.animationData.tracks.length) {
            this.state.parsingState = AnimationParsingState.EVENTS;
            return true;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            // Need at least 4 bytes for keyframe count
            if (buffer.length - offset < 4) {
                return false;
            }

            // Read keyframe count for current track (4 bytes)
            const keyframeCount = buffer.readUInt32LE(offset);
            offset += 4;

            // Get current track
            const currentTrack = this.state.animationData.tracks[this.state.currentTrackIndex];

            // Parse keyframes in batches to avoid blocking for too long
            const batchSize = 1000;
            const keyframesToParse = Math.min(batchSize, keyframeCount, this.maxKeyframes - this.state.keyframeParseCount);

            if (keyframesToParse <= 0) {
                // Move to next track
                this.state.currentTrackIndex++;
                this.state.bufferOffset = offset;
                return true;
            }

            // Each keyframe is: time (float), value type (uint32), value length (uint32), value (variable)
            // Minimum 12 bytes per keyframe
            const minBytesNeeded = keyframesToParse * 12;

            if (buffer.length - offset < minBytesNeeded) {
                return false; // Not enough data
            }

            for (let i = 0; i < keyframesToParse; i++) {
                // Read time (float = 4 bytes)
                const time = buffer.readFloatLE(offset);
                offset += 4;

                // Read value type (uint32 = 4 bytes)
                const valueType = buffer.readUInt32LE(offset);
                offset += 4;

                // Read value length (uint32 = 4 bytes)
                const valueLength = buffer.readUInt32LE(offset);
                offset += 4;

                // Check if we have enough data for the value
                if (buffer.length - offset < valueLength) {
                    return false;
                }

                let value: number[] | string;

                // Parse value based on type
                switch (valueType) {
                    case 0: // Float
                        value = buffer.readFloatLE(offset);
                        offset += 4;
                        break;

                    case 1: // Vector2
                        value = [
                            buffer.readFloatLE(offset),
                            buffer.readFloatLE(offset + 4)
                        ];
                        offset += 8;
                        break;

                    case 2: // Vector3
                        value = [
                            buffer.readFloatLE(offset),
                            buffer.readFloatLE(offset + 4),
                            buffer.readFloatLE(offset + 8)
                        ];
                        offset += 12;
                        break;

                    case 3: // Quaternion
                        value = [
                            buffer.readFloatLE(offset),
                            buffer.readFloatLE(offset + 4),
                            buffer.readFloatLE(offset + 8),
                            buffer.readFloatLE(offset + 12)
                        ];
                        offset += 16;
                        break;

                    case 4: // String
                        value = buffer.toString('utf8', offset, offset + valueLength);
                        offset += valueLength;
                        break;

                    default:
                        value = 'unknown';
                        offset += valueLength;
                        break;
                }

                // Read interpolation type (string)
                const interpLength = buffer.readUInt32LE(offset);
                offset += 4;

                let interpolation;
                if (interpLength > 0) {
                    // Check if we have enough data for the interpolation
                    if (buffer.length - offset < interpLength) {
                        return false;
                    }

                    interpolation = buffer.toString('utf8', offset, offset + interpLength);
                    offset += interpLength;
                }

                // Create keyframe
                const keyframe: AnimationKeyframe = {
                    time,
                    value,
                    interpolation
                };

                // Add to current track's keyframes
                currentTrack.keyframes.push(keyframe);
                this.state.keyframeParseCount++;
            }

            // Update state
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed ${keyframesToParse} keyframes for track ${currentTrack.name} (${this.state.keyframeParseCount} total)`);

            // Check if we've parsed all keyframes or reached max
            if (this.state.keyframeParseCount >= this.maxKeyframes) {
                // Move to events if we've hit the max keyframes
                this.state.parsingState = AnimationParsingState.EVENTS;
            } else if (currentTrack.keyframes.length >= keyframeCount) {
                // Move to next track
                this.state.currentTrackIndex++;

                // Check if we've parsed all tracks
                if (this.state.currentTrackIndex >= (this.state.animationData.tracks?.length || 0)) {
                    this.state.parsingState = AnimationParsingState.EVENTS;
                }
            }

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing keyframes: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse event data
     * @returns True if events were parsed successfully, false if more data is needed
     */
    private parseEventData(): boolean {
        // Parse events in batches to avoid blocking for too long
        const batchSize = 100;
        const remainingEvents = (this.state.animationData.eventCount || 0) - this.state.eventParseCount;
        const eventsToParse = Math.min(batchSize, remainingEvents);

        if (eventsToParse <= 0) {
            this.state.parsingState = AnimationParsingState.COMPLETE;
            return true;
        }

        try {
            const buffer = this.state.buffer;
            let offset = this.state.bufferOffset;

            for (let i = 0; i < eventsToParse; i++) {
                // Need at least 12 bytes for event header
                if (buffer.length - offset < 12) {
                    return false;
                }

                // Read event name length (4 bytes)
                const nameLength = buffer.readUInt32LE(offset);
                offset += 4;

                // Check if we have enough data for the name
                if (buffer.length - offset < nameLength) {
                    return false;
                }

                // Read event name
                let name = '';
                if (nameLength > 0) {
                    name = buffer.toString('utf8', offset, offset + nameLength);
                    offset += nameLength;
                }

                // Read event time (4 bytes, float)
                const time = buffer.readFloatLE(offset);
                offset += 4;

                // Read parameter count (4 bytes)
                const paramCount = buffer.readUInt32LE(offset);
                offset += 4;

                // Parse parameters
                const parameters: Record<string, any> = {};

                for (let j = 0; j < paramCount; j++) {
                    // Need at least 12 bytes for parameter header
                    if (buffer.length - offset < 12) {
                        return false;
                    }

                    // Read parameter name length (4 bytes)
                    const paramNameLength = buffer.readUInt32LE(offset);
                    offset += 4;

                    // Check if we have enough data for the parameter name
                    if (buffer.length - offset < paramNameLength) {
                        return false;
                    }

                    // Read parameter name
                    let paramName = '';
                    if (paramNameLength > 0) {
                        paramName = buffer.toString('utf8', offset, offset + paramNameLength);
                        offset += paramNameLength;
                    }

                    // Read parameter type (4 bytes)
                    const paramType = buffer.readUInt32LE(offset);
                    offset += 4;

                    // Read parameter value length (4 bytes)
                    const paramValueLength = buffer.readUInt32LE(offset);
                    offset += 4;

                    // Check if we have enough data for the parameter value
                    if (buffer.length - offset < paramValueLength) {
                        return false;
                    }

                    let paramValue: any;

                    // Parse parameter value based on type
                    switch (paramType) {
                        case 0: // Boolean
                            paramValue = buffer.readUInt8(offset) !== 0;
                            offset += 1;
                            break;

                        case 1: // Integer
                            paramValue = buffer.readInt32LE(offset);
                            offset += 4;
                            break;

                        case 2: // Float
                            paramValue = buffer.readFloatLE(offset);
                            offset += 4;
                            break;

                        case 3: // String
                            paramValue = buffer.toString('utf8', offset, offset + paramValueLength);
                            offset += paramValueLength;
                            break;

                        default:
                            // Skip unknown parameter type
                            offset += paramValueLength;
                            break;
                    }

                    // Store parameter
                    if (paramName) {
                        parameters[paramName] = paramValue;
                    }
                }

                // Create event
                const event: AnimationEvent = {
                    name: name || `Event${this.state.eventParseCount}`,
                    time,
                    parameters
                };

                // Add to events array
                this.state.animationData.events?.push(event);
                this.state.eventParseCount++;
            }

            // Update state
            this.state.bufferOffset = offset;

            this.logger.debug(`Parsed ${eventsToParse} events (${this.state.eventParseCount}/${this.state.animationData.eventCount})`);

            // Check if we've parsed all events
            if (this.state.eventParseCount >= (this.state.animationData.eventCount || 0)) {
                this.state.parsingState = AnimationParsingState.COMPLETE;
            }

            return true;
        } catch (error: any) {
            this.logger.error(`Error parsing events: ${error.message}`);
            throw error;
        }
    }

    /**
     * Implementation-specific flush
     * @param callback Callback function
     */
    protected flushImpl(callback: TransformCallback): void {
        // If we haven't completed parsing, try to finalize
        if (this.state.parsingState !== AnimationParsingState.COMPLETE) {
            this.logger.warn(`Flushing before parsing complete, current state: ${this.state.parsingState}`);
            this.emitAnimationData();
        }

        callback();
    }

    /**
     * Calculate progress (0-1)
     */
    protected calculateProgress(): number {
        switch (this.state.parsingState) {
            case AnimationParsingState.HEADER:
                return 0.05;

            case AnimationParsingState.ANIMATION_INFO:
                return 0.1;

            case AnimationParsingState.TRACKS:
                if (this.state.animationData.trackCount && this.state.animationData.trackCount > 0) {
                    return 0.1 + (0.1 * (this.state.trackParseCount / this.state.animationData.trackCount));
                }
                return 0.15;

            case AnimationParsingState.KEYFRAMES:
                // Estimate progress based on tracks processed
                if (this.state.animationData.tracks && this.state.animationData.tracks.length > 0) {
                    return 0.2 + (0.6 * (this.state.currentTrackIndex / this.state.animationData.tracks.length));
                }
                return 0.5;

            case AnimationParsingState.EVENTS:
                if (this.state.animationData.eventCount && this.state.animationData.eventCount > 0) {
                    return 0.8 + (0.2 * (this.state.eventParseCount / this.state.animationData.eventCount));
                }
                return 0.9;

            case AnimationParsingState.COMPLETE:
                return 1.0;

            default:
                return 0;
        }
    }

    /**
     * Emit animation data
     */
    private emitAnimationData(): void {
        // Emit animation data event with current state
        this.emit('animation_data', this.state.animationData);
    }
}