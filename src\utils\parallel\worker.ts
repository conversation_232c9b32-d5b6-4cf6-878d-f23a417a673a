/**
 * Worker implementation for the WorkerPool
 * 
 * This module is loaded by <PERSON><PERSON><PERSON><PERSON> in worker threads to process tasks.
 * It handles different task types and delegates to appropriate handlers.
 */

import { Task, TaskType } from './workerPool.js';

// Import task handlers
import { handleResourceAnalysis } from './handlers/resourceAnalysisHandler.js';
import { handleConflictDetection } from './handlers/conflictDetectionHandler.js';
import { handleMetadataExtraction } from './handlers/metadataExtractionHandler.js';
import { handleSimDataParsing } from './handlers/simDataParsingHandler.js';
import { handleTuningParsing } from './handlers/tuningParsingHandler.js';
import { handleScriptAnalysis } from './handlers/scriptAnalysisHandler.js';
import { handleGenericTask } from './handlers/genericTaskHandler.js';

/**
 * Main worker function that processes tasks
 * This is the entry point for <PERSON><PERSON><PERSON>a
 * 
 * @param data The task data passed from the main thread
 * @returns The result of the task
 */
export default async function worker({ task }: { task: Task }): Promise<any> {
    try {
        // Process the task based on its type
        switch (task.type) {
            case TaskType.RESOURCE_ANALYSIS:
                return await handleResourceAnalysis(task);
            
            case TaskType.CONFLICT_DETECTION:
                return await handleConflictDetection(task);
            
            case TaskType.METADATA_EXTRACTION:
                return await handleMetadataExtraction(task);
            
            case TaskType.SIMDATA_PARSING:
                return await handleSimDataParsing(task);
            
            case TaskType.TUNING_PARSING:
                return await handleTuningParsing(task);
            
            case TaskType.SCRIPT_ANALYSIS:
                return await handleScriptAnalysis(task);
            
            case TaskType.GENERIC:
                return await handleGenericTask(task);
            
            default:
                throw new Error(`Unknown task type: ${task.type}`);
        }
    } catch (error: any) {
        // Capture and rethrow errors with additional context
        const enhancedError = new Error(`Worker error processing task ${task.id} (${task.type}): ${error.message}`);
        enhancedError.stack = error.stack;
        (enhancedError as any).originalError = error;
        throw enhancedError;
    }
}
