/**
 * Interface for Locality-Sensitive Hashing (LSH) algorithms
 * LSH is a technique that hashes similar input items into the same "buckets" with high probability
 */
export interface LSHInterface {
    /**
     * Generate a hash for the given content
     * @param content Content to hash (string or Buffer)
     * @returns Hash value (format depends on the specific LSH algorithm)
     */
    generateHash(content: string | Buffer): string | number | bigint | number[];

    /**
     * Calculate similarity between two hashes
     * @param hash1 First hash
     * @param hash2 Second hash
     * @returns Similarity score between 0 and 1
     */
    calculateSimilarity(hash1: any, hash2: any): number;

    /**
     * Check if two hashes are similar based on a threshold
     * @param hash1 First hash
     * @param hash2 Second hash
     * @param threshold Similarity threshold (default: 0.7)
     * @returns True if hashes are similar, false otherwise
     */
    areSimilar(hash1: any, hash2: any, threshold?: number): boolean;
}
