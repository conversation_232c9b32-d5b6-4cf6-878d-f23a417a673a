#!/usr/bin/env node

import { findPackageFiles, findTS4ScriptFiles } from './testing/fileScanner.js';
import path from 'path';

async function testScriptDetection() {
    console.log('🔍 Testing Script File Detection...');
    
    const modsPath = 'C:/Users/<USER>/OneDrive/Documents/Electronic Arts/The Sims 4/Mods';
    
    try {
        console.log('📦 Scanning for .package files...');
        const packageFiles = await findPackageFiles(modsPath, { maxFiles: 5 });
        console.log('✅ Found', packageFiles.length, 'package files');
        
        console.log('🐍 Scanning for .ts4script files...');
        const scriptFiles = await findTS4ScriptFiles(modsPath, { maxFiles: 5 });
        console.log('✅ Found', scriptFiles.length, 'script files');
        
        if (scriptFiles.length > 0) {
            console.log('📋 Script files found:');
            scriptFiles.forEach((file, i) => {
                console.log(`  ${i+1}. ${path.basename(file)}`);
            });
        } else {
            console.log('⚠️ No script files found');
        }
        
        console.log('🔄 Combined files:', packageFiles.length + scriptFiles.length);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

testScriptDetection().then(() => process.exit(0)).catch(err => { 
    console.error(err); 
    process.exit(1); 
});
