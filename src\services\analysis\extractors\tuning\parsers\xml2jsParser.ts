import { Logger } from '../../../../../utils/logging/logger.js';
import { ResourceKey as AppResourceKey } from '../../../../../types/resource/interfaces.js';
import { TuningParseResult } from '../types.js';
import { createTuningExtractionContext, handleTuningExtractionError } from '../error/tuningExtractorErrorHandler.js';
import { cleanXmlString } from '../utils/xmlUtils.js';
import xml2js from 'xml2js';

/**
 * Attempts to parse a buffer using xml2js as a fallback
 * @param processedBuffer The preprocessed buffer
 * @param key The resource key
 * @param resourceId The resource ID
 * @param log The logger instance
 * @returns The parsed tuning resource or null if parsing failed
 */
export async function parseWithXml2js(
    processedBuffer: Buffer,
    key: AppResourceKey,
    resourceId: number,
    log: Logger
): Promise<TuningParseResult> {
    let tuningResource: any = null;
    let contentSnippet: string | undefined = undefined;

    log.info(`Attempting fallback XML parsing using xml2js for resource ${key.instance.toString(16)} (Type: 0x${key.type.toString(16)})`);

    try {
        // Convert buffer to string
        const xmlString = processedBuffer.toString('utf-8');
        log.debug(`Original XML string (first 200 chars): "${xmlString.substring(0, 200)}"`);

        // Clean the XML string
        const cleanedXmlString = cleanXmlString(xmlString, log);
        log.debug(`Cleaned XML string (first 200 chars): "${cleanedXmlString.substring(0, 200)}"`);

        // Use more permissive xml2js options
        const xml2jsOptions = {
            explicitArray: false,
            ignoreAttrs: false,
            strict: false, // Less strict parsing
            normalizeTags: false,
            attrNameProcessors: [(name: string) => name], // Preserve attribute names
            tagNameProcessors: [(name: string) => name],  // Preserve tag names
            // Add a validation function that always returns true to accept any XML
            validator: () => true
        };

        // Try to parse with xml2js
        const result = await new Promise<any>((resolve, reject) => {
            xml2js.parseString(cleanedXmlString, xml2jsOptions, (err: any, result: any) => {
                if (err) {
                    log.error(`xml2js parseString error: ${err.message}`);

                    // If parsing fails, try a more aggressive approach
                    log.debug(`Trying more aggressive XML cleaning...`);

                    // Extract just the content between the first '<' and last '>'
                    const firstLt = cleanedXmlString.indexOf('<');
                    const lastGt = cleanedXmlString.lastIndexOf('>');

                    if (firstLt >= 0 && lastGt > firstLt) {
                        const extractedXml = cleanedXmlString.substring(firstLt, lastGt + 1);
                        log.debug(`Extracted XML between first '<' and last '>': "${extractedXml.substring(0, 100)}..."`);

                        // Try to parse again with the extracted XML
                        xml2js.parseString(extractedXml, xml2jsOptions, (err2: any, result2: any) => {
                            if (err2) {
                                log.error(`Second xml2js attempt failed: ${err2.message}`);

                                // Last resort: create a minimal valid XML
                                const minimalXml = `<I n="tuning"><T>0</T></I>`;
                                xml2js.parseString(minimalXml, xml2jsOptions, (err3: any, result3: any) => {
                                    if (err3) {
                                        reject(err3);
                                    } else {
                                        log.warn(`Using minimal valid XML as fallback`);
                                        resolve({
                                            dom: {
                                                root: result3.I
                                            }
                                        });
                                    }
                                });
                            } else {
                                log.info(`Second xml2js attempt succeeded`);
                                const rootElement = result2.I || result2.root || Object.values(result2)[0];
                                if (rootElement) {
                                    resolve({
                                        dom: {
                                            root: rootElement
                                        }
                                    });
                                } else {
                                    reject(new Error("Could not find root element in second parse attempt"));
                                }
                            }
                        });
                    } else {
                        reject(err);
                    }
                } else {
                    // First attempt succeeded
                    log.info(`Successfully parsed XML with xml2js`);

                    // Try to find the root element - could be 'I' or something else
                    const rootElement = result.I || result.root || Object.values(result)[0];

                    if (rootElement) {
                        resolve({
                            dom: {
                                root: rootElement
                            }
                        });
                    } else {
                        reject(new Error("Could not find root element in parsed XML"));
                    }
                }
            });
        });

        tuningResource = result;
        contentSnippet = `[Tuning Parsed with xml2js: Root=${result?.dom?.root?.$ ? 'I' : 'N/A'}]`;
        log.info(`Successfully parsed Tuning XML using xml2js fallback.`);

        return {
            tuningResource,
            parsedWithS4TK: false,
            contentSnippet
        };
    } catch (xml2jsError: any) {
        const context = createTuningExtractionContext(key, resourceId, 'Xml2jsParser', {
            bufferLength: processedBuffer.length,
            parserType: 'xml2js',
            fallbackAttempt: true
        });

        // Handle the error
        return handleTuningExtractionError(xml2jsError, context, log);
    }
}
