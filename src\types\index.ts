/**
 * Type exports
 * Consolidating exports to avoid duplicates and conflicts.
 */

// Core resource types (ResourceKey, ResourceMetadata, ResourceType, etc.)
export * from './resource/core.js'; // Added .js extension

// Package-related types (PackageMetadata, ModCategory, etc.)
// Note: PackageAnalysisResult is NOT exported from here.
export * from './resource/Package.js'; // Added .js extension

// Conflict-related types (ConflictInfo, ResourceConflict, ConflictResult, etc.)
export * from './conflict/index.js'; // Added .js extension

// Analysis result types (PackageAnalysisResult)
export * from './analysis/PackageAnalysisResult.js'; // Added .js extension

// Removed redundant exports from:
// - ./resource/conflicts (covered by ./conflict/index)
// - ./resource/metadata (conflicting ResourceMetadata removed, others not globally needed)
// - ./conflict/ConflictTypes (covered by ./conflict/index)
// - Duplicate export of ./resource/core
