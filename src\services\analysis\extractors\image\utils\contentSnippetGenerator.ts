/**
 * Content snippet generator for image extraction
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ImageHeaderInfo } from '../types.js';

// Create a logger instance
const log = new Logger('ImageContentSnippetGenerator');

/**
 * Generates a content snippet for an image
 * @param header Image header information
 * @returns Content snippet
 */
export function generateImageContentSnippet(header: ImageHeaderInfo): string {
    try {
        // Start with the format
        let snippet = `[Image: Format=${header.format}`;
        
        // Add dimensions if available
        if (header.width !== undefined && header.height !== undefined) {
            snippet += `, ${header.width}x${header.height}`;
        }
        
        // Add compression if available
        if (header.compression) {
            snippet += `, ${header.compression}`;
        }
        
        // Add mipmap count if available
        if (header.mipMapCount && header.mipMapCount > 1) {
            snippet += `, Mipmaps=${header.mipMapCount}`;
        }
        
        // Add alpha information if available
        if (header.hasAlpha !== undefined) {
            snippet += `, Alpha=${header.hasAlpha ? 'Yes' : 'No'}`;
        }
        
        // Add bits per pixel if available
        if (header.bitsPerPixel) {
            snippet += `, ${header.bitsPerPixel} bpp`;
        }
        
        // Close the snippet
        snippet += ']';
        
        return snippet;
    } catch (error: any) {
        log.error(`Error generating content snippet: ${error.message || error}`);
        return '[Image]';
    }
}

/**
 * Generates an error content snippet
 * @param format Image format (if known)
 * @param error Error message
 * @returns Error content snippet
 */
export function generateErrorContentSnippet(format: string = 'Unknown', error: string = 'Parse Error'): string {
    return `[Image: Format=${format}, Error=${error}]`;
}
