/**
 * TS4Script Function Analyzer
 * 
 * This module provides functionality for analyzing functions in Sims 4 scripts.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { BytecodeInstruction, CodeObject } from '../bytecode/types.js';
import { TS4ScriptDecorator, TS4ScriptFunction, TS4ScriptParameter } from '../types.js';

/**
 * TS4Script function analyzer
 */
@singleton()
export class FunctionAnalyzer {
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(private logger: Logger = new Logger('FunctionAnalyzer')) {}

    /**
     * Analyze functions in code object
     * @param codeObject Code object
     * @param moduleName Module name
     * @param instructions Bytecode instructions
     * @returns Functions
     */
    public analyzeFunctions(codeObject: CodeObject, moduleName: string, instructions: BytecodeInstruction[]): TS4ScriptFunction[] {
        try {
            this.logger.debug(`Analyzing functions in module: ${moduleName}`);

            const functions: TS4ScriptFunction[] = [];
            
            // Find function definitions in the bytecode
            const functionDefinitions = this.findFunctionDefinitions(instructions, codeObject);
            
            // Process each function definition
            for (const functionDefinition of functionDefinitions) {
                const functionName = functionDefinition.name;
                
                // Create function object
                const functionObj: TS4ScriptFunction = {
                    id: 0, // Will be set when saved to database
                    name: functionName,
                    fullName: `${moduleName}.${functionName}`,
                    parameters: this.extractFunctionParameters(functionDefinition.codeObject),
                    isMethod: false,
                    isStaticMethod: false,
                    isClassMethod: false,
                    isProperty: false,
                    isCommand: this.isCommand(functionDefinition, instructions),
                    isEventHandler: this.isEventHandler(functionDefinition, instructions),
                    isInjection: this.isInjection(functionDefinition, instructions),
                    decorators: this.extractDecorators(functionDefinition, instructions),
                    startLine: functionDefinition.startLine,
                    endLine: functionDefinition.endLine,
                    complexity: this.calculateFunctionComplexity(functionDefinition.codeObject)
                };
                
                functions.push(functionObj);
            }
            
            return functions;
        } catch (error) {
            this.logger.error(`Error analyzing functions in module ${moduleName}:`, error);
            return [];
        }
    }

    /**
     * Find function definitions in bytecode
     * @param instructions Bytecode instructions
     * @param codeObject Code object
     * @returns Function definitions
     */
    private findFunctionDefinitions(instructions: BytecodeInstruction[], codeObject: CodeObject): any[] {
        const functionDefinitions: any[] = [];
        
        // Look for MAKE_FUNCTION opcode
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            if (instruction.opcodeName === 'MAKE_FUNCTION') {
                // Function definition found
                // Previous instruction should be LOAD_CONST with the function code object
                const loadConstInstruction = instructions[i - 1];
                if (loadConstInstruction && loadConstInstruction.opcodeName === 'LOAD_CONST') {
                    const functionCodeObject = loadConstInstruction.argValue;
                    
                    // Previous instruction should be LOAD_CONST with the function name
                    const loadNameInstruction = instructions[i - 2];
                    if (loadNameInstruction && loadNameInstruction.opcodeName === 'LOAD_CONST') {
                        const functionName = loadNameInstruction.argValue;
                        
                        // Find start and end line
                        const startLine = instruction.lineNo || 0;
                        const endLine = this.findFunctionEndLine(instructions, i, startLine);
                        
                        // Check for decorators
                        const decoratorIndices = this.findDecoratorIndices(instructions, i);
                        
                        functionDefinitions.push({
                            name: functionName,
                            codeObject: functionCodeObject,
                            startLine,
                            endLine,
                            decoratorIndices
                        });
                    }
                }
            }
        }
        
        return functionDefinitions;
    }

    /**
     * Find function end line
     * @param instructions Bytecode instructions
     * @param startIndex Start index
     * @param startLine Start line
     * @returns End line
     */
    private findFunctionEndLine(instructions: BytecodeInstruction[], startIndex: number, startLine: number): number {
        // Find the STORE_NAME instruction that follows the function definition
        for (let i = startIndex; i < instructions.length; i++) {
            if (instructions[i].opcodeName === 'STORE_NAME') {
                // Find the next instruction with a different line number
                for (let j = i + 1; j < instructions.length; j++) {
                    if (instructions[j].lineNo && instructions[j].lineNo > startLine) {
                        return instructions[j].lineNo - 1;
                    }
                }
            }
        }
        
        return startLine + 5; // Arbitrary value if we can't determine the end line
    }

    /**
     * Find decorator indices
     * @param instructions Bytecode instructions
     * @param functionIndex Function index
     * @returns Decorator indices
     */
    private findDecoratorIndices(instructions: BytecodeInstruction[], functionIndex: number): number[] {
        const decoratorIndices: number[] = [];
        
        // Look for CALL_FUNCTION instructions before the function definition
        // that are used to apply decorators
        for (let i = functionIndex + 1; i < instructions.length; i++) {
            if (instructions[i].opcodeName === 'CALL_FUNCTION' && instructions[i].arg === 1) {
                decoratorIndices.push(i);
            } else if (instructions[i].opcodeName === 'STORE_NAME') {
                // Stop when we reach the STORE_NAME instruction
                break;
            }
        }
        
        return decoratorIndices;
    }

    /**
     * Extract function parameters
     * @param codeObject Code object
     * @returns Parameters
     */
    private extractFunctionParameters(codeObject: CodeObject): TS4ScriptParameter[] {
        const parameters: TS4ScriptParameter[] = [];
        
        if (!codeObject || !codeObject.varNames) {
            return parameters;
        }
        
        // Extract parameter names from varNames
        // The first argCount entries in varNames are the parameters
        for (let i = 0; i < codeObject.argCount; i++) {
            if (i < codeObject.varNames.length) {
                const paramName = codeObject.varNames[i];
                
                parameters.push({
                    name: paramName,
                    typeAnnotation: undefined, // Would need to parse type annotations
                    defaultValue: undefined, // Would need to analyze default values
                    isPositionalOnly: false,
                    isKeywordOnly: i >= codeObject.argCount - codeObject.kwOnlyArgCount,
                    isVariadicPositional: paramName.startsWith('*') && !paramName.startsWith('**'),
                    isVariadicKeyword: paramName.startsWith('**')
                });
            }
        }
        
        return parameters;
    }

    /**
     * Extract decorators
     * @param functionDefinition Function definition
     * @param instructions Bytecode instructions
     * @returns Decorators
     */
    private extractDecorators(functionDefinition: any, instructions: BytecodeInstruction[]): TS4ScriptDecorator[] {
        const decorators: TS4ScriptDecorator[] = [];
        
        // Process each decorator index
        for (const decoratorIndex of functionDefinition.decoratorIndices) {
            // Look for LOAD_NAME or LOAD_ATTR instructions before the CALL_FUNCTION
            for (let i = decoratorIndex - 1; i >= 0; i--) {
                if (instructions[i].opcodeName === 'LOAD_NAME' || instructions[i].opcodeName === 'LOAD_ATTR') {
                    const decoratorName = instructions[i].argValue as string;
                    
                    // Create decorator object
                    const decorator: TS4ScriptDecorator = {
                        name: decoratorName,
                        arguments: [], // Would need to extract arguments
                        isCommand: this.isCommandDecorator(decoratorName),
                        isInjection: this.isInjectionDecorator(decoratorName),
                        isEventHandler: this.isEventHandlerDecorator(decoratorName)
                    };
                    
                    decorators.push(decorator);
                    break;
                }
            }
        }
        
        return decorators;
    }

    /**
     * Check if function is a command
     * @param functionDefinition Function definition
     * @param instructions Bytecode instructions
     * @returns Is command
     */
    private isCommand(functionDefinition: any, instructions: BytecodeInstruction[]): boolean {
        // Check if function has a command decorator
        for (const decoratorIndex of functionDefinition.decoratorIndices) {
            // Look for LOAD_NAME or LOAD_ATTR instructions before the CALL_FUNCTION
            for (let i = decoratorIndex - 1; i >= 0; i--) {
                if (instructions[i].opcodeName === 'LOAD_NAME' || instructions[i].opcodeName === 'LOAD_ATTR') {
                    const decoratorName = instructions[i].argValue as string;
                    
                    if (this.isCommandDecorator(decoratorName)) {
                        return true;
                    }
                    
                    break;
                }
            }
        }
        
        return false;
    }

    /**
     * Check if function is an event handler
     * @param functionDefinition Function definition
     * @param instructions Bytecode instructions
     * @returns Is event handler
     */
    private isEventHandler(functionDefinition: any, instructions: BytecodeInstruction[]): boolean {
        // Check if function has an event handler decorator
        for (const decoratorIndex of functionDefinition.decoratorIndices) {
            // Look for LOAD_NAME or LOAD_ATTR instructions before the CALL_FUNCTION
            for (let i = decoratorIndex - 1; i >= 0; i--) {
                if (instructions[i].opcodeName === 'LOAD_NAME' || instructions[i].opcodeName === 'LOAD_ATTR') {
                    const decoratorName = instructions[i].argValue as string;
                    
                    if (this.isEventHandlerDecorator(decoratorName)) {
                        return true;
                    }
                    
                    break;
                }
            }
        }
        
        // Check if function name indicates an event handler
        const eventHandlerPrefixes = ['on_', 'handle_'];
        for (const prefix of eventHandlerPrefixes) {
            if (functionDefinition.name.startsWith(prefix)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if function is an injection
     * @param functionDefinition Function definition
     * @param instructions Bytecode instructions
     * @returns Is injection
     */
    private isInjection(functionDefinition: any, instructions: BytecodeInstruction[]): boolean {
        // Check if function has an injection decorator
        for (const decoratorIndex of functionDefinition.decoratorIndices) {
            // Look for LOAD_NAME or LOAD_ATTR instructions before the CALL_FUNCTION
            for (let i = decoratorIndex - 1; i >= 0; i--) {
                if (instructions[i].opcodeName === 'LOAD_NAME' || instructions[i].opcodeName === 'LOAD_ATTR') {
                    const decoratorName = instructions[i].argValue as string;
                    
                    if (this.isInjectionDecorator(decoratorName)) {
                        return true;
                    }
                    
                    break;
                }
            }
        }
        
        // Check if function name indicates an injection
        const injectionPrefixes = ['inject', 'patch'];
        for (const prefix of injectionPrefixes) {
            if (functionDefinition.name.startsWith(prefix)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if decorator is a command decorator
     * @param decoratorName Decorator name
     * @returns Is command decorator
     */
    private isCommandDecorator(decoratorName: string): boolean {
        return decoratorName === 'Command' || decoratorName.endsWith('.Command');
    }

    /**
     * Check if decorator is an event handler decorator
     * @param decoratorName Decorator name
     * @returns Is event handler decorator
     */
    private isEventHandlerDecorator(decoratorName: string): boolean {
        return decoratorName === 'event_handler' || decoratorName.endsWith('.event_handler');
    }

    /**
     * Check if decorator is an injection decorator
     * @param decoratorName Decorator name
     * @returns Is injection decorator
     */
    private isInjectionDecorator(decoratorName: string): boolean {
        return decoratorName === 'inject' || decoratorName === 'injector' || 
               decoratorName.endsWith('.inject') || decoratorName.endsWith('.injector');
    }

    /**
     * Calculate function complexity
     * @param codeObject Code object
     * @returns Complexity
     */
    private calculateFunctionComplexity(codeObject: CodeObject): number {
        if (!codeObject) {
            return 1;
        }
        
        // Simple complexity calculation based on bytecode size and nested code objects
        let complexity = 1;
        
        // Add complexity for bytecode size
        complexity += Math.floor(codeObject.bytecode.length / 10);
        
        // Add complexity for nested code objects
        complexity += codeObject.nestedCodeObjects.length;
        
        return complexity;
    }
}
