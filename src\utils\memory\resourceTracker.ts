/**
 * Resource Tracker
 *
 * This module provides a centralized system for tracking and managing resources
 * used by the application. It helps ensure proper cleanup and prevent resource leaks.
 *
 * Features:
 * - Resource tracking and management
 * - Automatic resource cleanup
 * - Resource usage statistics
 * - Memory pressure monitoring
 * - Resource leak detection
 */

import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import EnhancedMemoryManager from './enhancedMemoryManager.js';
import { AppError, ErrorCategory, ErrorCode, ErrorSeverity } from '../error/errorTypes.js';
import { EnhancedErrorHandler } from '../error/enhancedErrorHandler.js';

// Create a logger for this module
const logger = new Logger('ResourceTracker');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

// Get error handler instance
const errorHandler = EnhancedErrorHandler.getInstance();

/**
 * Resource types
 */
export enum ResourceType {
    FILE_HANDLE = 'fileHandle',
    BUFFER = 'buffer',
    STREAM = 'stream',
    DATABASE = 'database',
    TIMER = 'timer',
    EVENT_LISTENER = 'eventListener',
    OPERATION = 'operation',
    RESOURCE_INFO = 'resourceInfo',
    BATCH = 'batch',
    DETECTOR = 'detector',
    OTHER = 'other'
}

/**
 * Resource states
 */
export enum ResourceState {
    ACTIVE = 'active',
    IDLE = 'idle',
    CLOSED = 'closed'
}

/**
 * Resource cleanup priorities
 */
export enum CleanupPriority {
    CRITICAL = 'critical',
    HIGH = 'high',
    MEDIUM = 'medium',
    LOW = 'low'
}

/**
 * Resource information
 */
export interface ResourceInfo {
    id: string;
    type: ResourceType;
    owner: string;
    creationTime: number;
    lastAccessTime: number;
    size?: number;
    state: ResourceState;
    priority: CleanupPriority;
    metadata?: Record<string, any>;
}

/**
 * Resource cleanup function
 */
export type CleanupFunction = () => Promise<void> | void;

/**
 * Resource tracker options
 */
export interface ResourceTrackerOptions {
    cleanupInterval?: number;
    resourceTimeout?: number;
    enableLeakDetection?: boolean;
    leakDetectionInterval?: number;
    leakDetectionThreshold?: number;
}

/**
 * Resource tracker statistics
 */
export interface ResourceTrackerStats {
    totalResources: number;
    activeResources: number;
    idleResources: number;
    closedResources: number;
    resourcesByType: Record<ResourceType, number>;
    resourcesByOwner: Record<string, number>;
    resourcesByState: Record<ResourceState, number>;
    resourcesByPriority: Record<CleanupPriority, number>;
    averageResourceAge: number;
    oldestResource: number;
    totalSize: number;
}

/**
 * Resource tracker class
 */
export class ResourceTracker extends EventEmitter {
    private static instance: ResourceTracker;
    private resources: Map<string, ResourceInfo> = new Map();
    private cleanupFunctions: Map<string, CleanupFunction> = new Map();
    private cleanupTimer?: NodeJS.Timeout;
    private leakDetectionTimer?: NodeJS.Timeout;
    private options: ResourceTrackerOptions;

    /**
     * Create a new resource tracker
     * @param options Resource tracker options
     */
    private constructor(options: ResourceTrackerOptions = {}) {
        super();

        this.options = {
            cleanupInterval: 30000, // 30 seconds
            resourceTimeout: 300000, // 5 minutes
            enableLeakDetection: true,
            leakDetectionInterval: 60000, // 1 minute
            leakDetectionThreshold: 1000, // 1000 resources
            ...options
        };

        // Start cleanup timer
        this.startCleanupTimer();

        // Start leak detection timer if enabled
        if (this.options.enableLeakDetection) {
            this.startLeakDetectionTimer();
        }

        logger.info('Resource tracker initialized');
    }

    /**
     * Get the resource tracker instance
     * @param options Resource tracker options
     * @returns Resource tracker instance
     */
    public static getInstance(options?: ResourceTrackerOptions): ResourceTracker {
        if (!ResourceTracker.instance) {
            ResourceTracker.instance = new ResourceTracker(options);
        } else if (options) {
            // Update options if provided
            ResourceTracker.instance.options = {
                ...ResourceTracker.instance.options,
                ...options
            };
        }

        return ResourceTracker.instance;
    }

    /**
     * Start the cleanup timer
     * @private
     */
    private startCleanupTimer(): void {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }

        this.cleanupTimer = setInterval(() => {
            this.cleanupIdleResources();
        }, this.options.cleanupInterval);

        logger.debug(`Started cleanup timer with interval ${this.options.cleanupInterval}ms`);
    }

    /**
     * Start the leak detection timer
     * @private
     */
    private startLeakDetectionTimer(): void {
        if (this.leakDetectionTimer) {
            clearInterval(this.leakDetectionTimer);
        }

        this.leakDetectionTimer = setInterval(() => {
            this.detectResourceLeaks();
        }, this.options.leakDetectionInterval);

        logger.debug(`Started leak detection timer with interval ${this.options.leakDetectionInterval}ms`);
    }

    /**
     * Clean up idle resources
     * @private
     */
    private async cleanupIdleResources(): Promise<void> {
        const now = Date.now();
        const memoryPressure = memoryManager.getMemoryPressure();

        // Get all resources
        const resources = this.getAllResources();

        // Sort resources by priority and idle time
        resources.sort((a, b) => {
            // First sort by state (idle first)
            if (a.state === ResourceState.IDLE && b.state !== ResourceState.IDLE) return -1;
            if (a.state !== ResourceState.IDLE && b.state === ResourceState.IDLE) return 1;

            // Then sort by priority (higher priority first)
            const priorityOrder = {
                [CleanupPriority.CRITICAL]: 0,
                [CleanupPriority.HIGH]: 1,
                [CleanupPriority.MEDIUM]: 2,
                [CleanupPriority.LOW]: 3
            };

            if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                return priorityOrder[a.priority] - priorityOrder[b.priority];
            }

            // Then sort by idle time (oldest first)
            return a.lastAccessTime - b.lastAccessTime;
        });

        // Determine how many resources to clean up based on memory pressure
        let cleanupCount = 0;

        if (memoryPressure > 0.9) {
            // Critical memory pressure - clean up all idle resources
            cleanupCount = resources.filter(r => r.state === ResourceState.IDLE).length;
        } else if (memoryPressure > 0.7) {
            // High memory pressure - clean up most idle resources
            cleanupCount = Math.floor(resources.filter(r => r.state === ResourceState.IDLE).length * 0.75);
        } else if (memoryPressure > 0.5) {
            // Medium memory pressure - clean up some idle resources
            cleanupCount = Math.floor(resources.filter(r => r.state === ResourceState.IDLE).length * 0.5);
        } else {
            // Low memory pressure - clean up only old idle resources
            cleanupCount = resources.filter(r =>
                r.state === ResourceState.IDLE &&
                (now - r.lastAccessTime) > this.options.resourceTimeout!
            ).length;
        }

        // Clean up resources
        let cleanedUp = 0;

        for (const resource of resources) {
            if (cleanedUp >= cleanupCount) {
                break;
            }

            if (resource.state === ResourceState.IDLE) {
                const idleTime = now - resource.lastAccessTime;

                // Clean up if idle for too long or if we need to clean up due to memory pressure
                if (idleTime > this.options.resourceTimeout! || cleanedUp < cleanupCount) {
                    logger.debug(`Cleaning up idle resource ${resource.id} (${resource.type}) owned by ${resource.owner}, idle for ${idleTime}ms`);

                    const released = await this.releaseResource(resource.id);

                    if (released) {
                        cleanedUp++;
                    }
                }
            }
        }

        if (cleanedUp > 0) {
            logger.info(`Cleaned up ${cleanedUp} idle resources`);
        }
    }

    /**
     * Detect resource leaks
     * @private
     */
    private detectResourceLeaks(): void {
        const now = Date.now();
        const resources = this.getAllResources();

        // Check for potential leaks
        const oldResources = resources.filter(r =>
            r.state === ResourceState.ACTIVE &&
            (now - r.lastAccessTime) > this.options.resourceTimeout! * 2
        );

        if (oldResources.length > 0) {
            logger.warn(`Potential resource leak detected: ${oldResources.length} resources have been active for more than ${this.options.resourceTimeout! * 2}ms`);

            // Log details of the oldest resources
            const oldestResources = oldResources
                .sort((a, b) => a.lastAccessTime - b.lastAccessTime)
                .slice(0, 5);

            for (const resource of oldestResources) {
                logger.warn(`Leaked resource: ${resource.id} (${resource.type}) owned by ${resource.owner}, active for ${now - resource.lastAccessTime}ms`);
            }

            // Emit leak detected event
            this.emit('leakDetected', oldResources);
        }

        // Check for excessive resources
        if (resources.length > this.options.leakDetectionThreshold!) {
            logger.warn(`Excessive resources detected: ${resources.length} resources tracked (threshold: ${this.options.leakDetectionThreshold!})`);

            // Emit excessive resources event
            this.emit('excessiveResources', resources.length);
        }
    }

    /**
     * Track a resource
     * @param type Resource type
     * @param owner Resource owner
     * @param cleanup Cleanup function
     * @param options Additional options
     * @returns Resource ID
     */
    public trackResource(
        type: ResourceType,
        owner: string,
        cleanup: CleanupFunction,
        options: {
            id?: string;
            size?: number;
            state?: ResourceState;
            priority?: CleanupPriority;
            metadata?: Record<string, any>;
        } = {}
    ): string {
        // Generate resource ID if not provided
        const id = options.id || `${type}-${owner}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        // Create resource info
        const now = Date.now();
        const resourceInfo: ResourceInfo = {
            id,
            type,
            owner,
            creationTime: now,
            lastAccessTime: now,
            size: options.size,
            state: options.state || ResourceState.ACTIVE,
            priority: options.priority || CleanupPriority.MEDIUM,
            metadata: options.metadata
        };

        // Store resource info and cleanup function
        this.resources.set(id, resourceInfo);
        this.cleanupFunctions.set(id, cleanup);

        logger.debug(`Tracking resource ${id} (${type}) owned by ${owner}`);

        // Emit resource tracked event
        this.emit('resourceTracked', resourceInfo);

        return id;
    }

    /**
     * Update resource state
     * @param id Resource ID
     * @param state New resource state
     * @returns True if the resource was updated, false otherwise
     */
    public updateResourceState(id: string, state: ResourceState): boolean {
        const resource = this.resources.get(id);

        if (!resource) {
            logger.warn(`Cannot update state for unknown resource ${id}`);
            return false;
        }

        // Update state and last access time
        resource.state = state;
        resource.lastAccessTime = Date.now();

        logger.debug(`Updated resource ${id} state to ${state}`);

        // Emit resource updated event
        this.emit('resourceUpdated', resource);

        return true;
    }

    /**
     * Update resource metadata
     * @param id Resource ID
     * @param metadata Resource metadata
     * @returns True if the resource was updated, false otherwise
     */
    public updateResourceMetadata(id: string, metadata: Record<string, any>): boolean {
        const resource = this.resources.get(id);

        if (!resource) {
            logger.warn(`Cannot update metadata for unknown resource ${id}`);
            return false;
        }

        // Update metadata and last access time
        resource.metadata = {
            ...resource.metadata,
            ...metadata
        };
        resource.lastAccessTime = Date.now();

        logger.debug(`Updated resource ${id} metadata`);

        // Emit resource updated event
        this.emit('resourceUpdated', resource);

        return true;
    }

    /**
     * Release a resource
     * @param id Resource ID
     * @returns True if the resource was released, false otherwise
     */
    public async releaseResource(id: string): Promise<boolean> {
        const resource = this.resources.get(id);
        const cleanup = this.cleanupFunctions.get(id);

        if (!resource || !cleanup) {
            logger.warn(`Cannot release unknown resource ${id}`);
            return false;
        }

        try {
            // Call cleanup function
            await cleanup();

            // Remove resource and cleanup function
            this.resources.delete(id);
            this.cleanupFunctions.delete(id);

            logger.debug(`Released resource ${id} (${resource.type}) owned by ${resource.owner}`);

            // Emit resource released event
            this.emit('resourceReleased', resource);

            return true;
        } catch (error: any) {
            // Handle cleanup error
            errorHandler.handleError(
                new AppError(
                    (ErrorCode as any).RESOURCE_CLEANUP_ERROR,
                    `Failed to clean up resource ${id}: ${error.message}`,
                    {
                        category: ErrorCategory.RESOURCE,
                        severity: ErrorSeverity.ERROR,
                        context: {
                            resource: {
                                id,
                                type: resource.type,
                                owner: resource.owner
                            } as any
                        },
                        cause: error
                    }
                )
            );

            return false;
        }
    }

    /**
     * Get resource information
     * @param id Resource ID
     * @returns Resource information or undefined if not found
     */
    public getResourceInfo(id: string): ResourceInfo | undefined {
        return this.resources.get(id);
    }

    /**
     * Get all resources
     * @returns All resources
     */
    public getAllResources(): ResourceInfo[] {
        return Array.from(this.resources.values());
    }

    /**
     * Get resources by type
     * @param type Resource type
     * @returns Resources of the specified type
     */
    public getResourcesByType(type: ResourceType): ResourceInfo[] {
        return this.getAllResources().filter(resource => resource.type === type);
    }

    /**
     * Get resources by owner
     * @param owner Resource owner
     * @returns Resources owned by the specified owner
     */
    public getResourcesByOwner(owner: string): ResourceInfo[] {
        return this.getAllResources().filter(resource => resource.owner === owner);
    }

    /**
     * Get resources by state
     * @param state Resource state
     * @returns Resources in the specified state
     */
    public getResourcesByState(state: ResourceState): ResourceInfo[] {
        return this.getAllResources().filter(resource => resource.state === state);
    }

    /**
     * Release all resources owned by a specific owner
     * @param owner Resource owner
     * @returns Number of resources released
     */
    public async releaseResourcesByOwner(owner: string): Promise<number> {
        const resourcesOwnedByOwner = this.getResourcesByOwner(owner);
        let releasedCount = 0;

        for (const resource of resourcesOwnedByOwner) {
            try {
                const released = await this.releaseResource(resource.id);
                if (released) {
                    releasedCount++;
                }
            } catch (error: any) {
                logger.error(`Error releasing resource ${resource.id} owned by ${owner}: ${error.message || error}`);
            }
        }

        if (releasedCount > 0) {
            logger.debug(`Released ${releasedCount} resources owned by ${owner}`);
        }

        return releasedCount;
    }

    /**
     * Get resource tracker statistics
     * @returns Resource tracker statistics
     */
    public getStats(): ResourceTrackerStats {
        const resources = this.getAllResources();
        const now = Date.now();

        // Calculate statistics
        const totalResources = resources.length;
        const activeResources = resources.filter(r => r.state === ResourceState.ACTIVE).length;
        const idleResources = resources.filter(r => r.state === ResourceState.IDLE).length;
        const closedResources = resources.filter(r => r.state === ResourceState.CLOSED).length;

        const resourcesByType: Record<ResourceType, number> = {
            [ResourceType.FILE_HANDLE]: 0,
            [ResourceType.BUFFER]: 0,
            [ResourceType.STREAM]: 0,
            [ResourceType.DATABASE]: 0,
            [ResourceType.TIMER]: 0,
            [ResourceType.EVENT_LISTENER]: 0,
            [ResourceType.OPERATION]: 0,
            [ResourceType.RESOURCE_INFO]: 0,
            [ResourceType.BATCH]: 0,
            [ResourceType.DETECTOR]: 0,
            [ResourceType.OTHER]: 0
        };

        const resourcesByState: Record<ResourceState, number> = {
            [ResourceState.ACTIVE]: activeResources,
            [ResourceState.IDLE]: idleResources,
            [ResourceState.CLOSED]: closedResources
        };

        const resourcesByPriority: Record<CleanupPriority, number> = {
            [CleanupPriority.CRITICAL]: 0,
            [CleanupPriority.HIGH]: 0,
            [CleanupPriority.MEDIUM]: 0,
            [CleanupPriority.LOW]: 0
        };

        const resourcesByOwner: Record<string, number> = {};
        let totalAge = 0;
        let oldestResource = 0;
        let totalSize = 0;

        // Calculate detailed statistics
        for (const resource of resources) {
            // Count by type
            resourcesByType[resource.type]++;

            // Count by priority
            resourcesByPriority[resource.priority]++;

            // Count by owner
            resourcesByOwner[resource.owner] = (resourcesByOwner[resource.owner] || 0) + 1;

            // Calculate age
            const age = now - resource.creationTime;
            totalAge += age;
            oldestResource = Math.max(oldestResource, age);

            // Calculate size
            if (resource.size !== undefined) {
                totalSize += resource.size;
            }
        }

        return {
            totalResources,
            activeResources,
            idleResources,
            closedResources,
            resourcesByType,
            resourcesByOwner,
            resourcesByState,
            resourcesByPriority,
            averageResourceAge: totalResources > 0 ? totalAge / totalResources : 0,
            oldestResource,
            totalSize
        };
    }
}
