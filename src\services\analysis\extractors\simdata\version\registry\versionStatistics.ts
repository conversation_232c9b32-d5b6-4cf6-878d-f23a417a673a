/**
 * SimData version statistics utilities
 */

import { VersionInfo, VersionStatistics } from '../types.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

/**
 * Calculates statistics for a collection of version information
 * @param versions Array of version information
 * @returns Version statistics
 */
export function calculateVersionStatistics(versions: VersionInfo[]): VersionStatistics {
    try {
        const standardVersions = versions.filter(v => v.isStandard);
        const specialVersions = versions.filter(v => v.isSpecial);
        const unknownVersions = versions.filter(v => !v.isStandard && !v.hasCustomHandler);

        // Find most common version
        let mostCommonVersion = 0;
        let mostCommonCount = 0;

        for (const info of versions) {
            if (info.count > mostCommonCount) {
                mostCommonCount = info.count;
                mostCommonVersion = info.version;
            }
        }

        // Get recently discovered versions (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const recentlyDiscovered = versions
            .filter(v => v.firstSeen >= thirtyDaysAgo)
            .sort((a, b) => b.firstSeen.getTime() - a.firstSeen.getTime());

        return {
            totalVersions: versions.length,
            standardVersions: standardVersions.length,
            specialVersions: specialVersions.length,
            unknownVersions: unknownVersions.length,
            mostCommonVersion,
            mostCommonCount,
            recentlyDiscovered
        };
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(undefined, 'calculateVersionStatistics'),
            {
                totalVersions: 0,
                standardVersions: 0,
                specialVersions: 0,
                unknownVersions: 0,
                mostCommonVersion: 0,
                mostCommonCount: 0,
                recentlyDiscovered: []
            }
        );
    }
}

/**
 * Generates a report of version statistics
 * @param statistics Version statistics
 * @returns Report string
 */
export function generateVersionStatisticsReport(statistics: VersionStatistics): string {
    try {
        let report = 'SimData Version Statistics\n';
        report += '==========================\n\n';
        
        report += `Total Versions: ${statistics.totalVersions}\n`;
        report += `Standard Versions: ${statistics.standardVersions}\n`;
        report += `Special Versions: ${statistics.specialVersions}\n`;
        report += `Unknown Versions: ${statistics.unknownVersions}\n\n`;
        
        if (statistics.mostCommonVersion > 0) {
            report += `Most Common Version: ${statistics.mostCommonVersion} (seen ${statistics.mostCommonCount} times)\n\n`;
        }
        
        if (statistics.recentlyDiscovered.length > 0) {
            report += 'Recently Discovered Versions:\n';
            
            for (const info of statistics.recentlyDiscovered) {
                report += `- Version ${info.version}: First seen ${info.firstSeen.toLocaleDateString()}, `;
                report += `used in ${info.schemaNames.size} schemas from ${info.modNames.size} mods\n`;
            }
        }
        
        return report;
    } catch (error) {
        return handleVersionError(
            error,
            createVersionErrorContext(undefined, 'generateVersionStatisticsReport'),
            'Error generating version statistics report'
        );
    }
}
