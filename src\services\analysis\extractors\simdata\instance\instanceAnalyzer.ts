/**
 * SimData Instance Analyzer
 *
 * This module provides functionality for analyzing SimData instances,
 * extracting semantic meaning, and identifying relationships between instances.
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataInstance, SimDataSchema } from '../simDataTypes.js';
import { InstanceAnalysisResult, InstanceRelationship, InstanceValuePattern } from './instanceInterfaces.js';

const log = {
    info: (message: string) => console.log(`[InstanceAnalyzer] INFO: ${message}`),
    error: (message: string) => console.error(`[InstanceAnalyzer] ERROR: ${message}`),
    warn: (message: string) => console.warn(`[InstanceAnalyzer] WARN: ${message}`),
    debug: (message: string) => console.debug(`[InstanceAnalyzer] DEBUG: ${message}`)
};

/**
 * SimData Instance Analyzer
 * Analyzes SimData instances to extract semantic meaning and relationships
 */
export class InstanceAnalyzer {
    /**
     * Analyze a collection of SimData instances
     * @param instances The SimData instances to analyze
     * @param schema The SimData schema for these instances
     * @returns Instance analysis results
     */
    public analyzeInstances(instances: SimDataInstance[], schema: SimDataSchema): InstanceAnalysisResult {
        try {
            // Skip analysis if no instances
            if (!instances || instances.length === 0) {
                return {
                    count: 0,
                    valuePatterns: {},
                    relationships: [],
                    keyInstances: [],
                    categories: {}
                };
            }

            // Extract value patterns
            const valuePatterns = this.extractValuePatterns(instances, schema);

            // Identify relationships between instances
            const relationships = this.identifyRelationships(instances, schema);

            // Identify key instances
            const keyInstances = this.identifyKeyInstances(instances, schema);

            // Categorize instances
            const categories = this.categorizeInstances(instances, schema);

            return {
                count: instances.length,
                valuePatterns,
                relationships,
                keyInstances,
                categories
            };
        } catch (error) {
            log.error(`Error analyzing instances: ${error}`);
            return {
                count: instances.length,
                valuePatterns: {},
                relationships: [],
                keyInstances: [],
                categories: {}
            };
        }
    }

    /**
     * Extract patterns from instance values
     * @param instances The SimData instances
     * @param schema The SimData schema
     * @returns Value patterns by column
     */
    private extractValuePatterns(instances: SimDataInstance[], schema: SimDataSchema): Record<string, InstanceValuePattern> {
        const patterns: Record<string, InstanceValuePattern> = {};

        // Skip if no instances
        if (!instances || instances.length === 0) {
            return patterns;
        }

        // Get column names from schema
        const columnNames = schema.columns.map(col => col.name);

        // Analyze each column
        for (const columnName of columnNames) {
            // Skip if column doesn't exist in any instance
            if (!instances.some(instance => instance.values && columnName in instance.values)) {
                continue;
            }

            // Collect all values for this column
            const values = instances
                .filter(instance => instance.values && columnName in instance.values)
                .map(instance => instance.values[columnName]);

            // Count occurrences of each value
            const valueCounts: Record<string, number> = {};
            for (const value of values) {
                const valueKey = JSON.stringify(value);
                valueCounts[valueKey] = (valueCounts[valueKey] || 0) + 1;
            }

            // Get unique values
            const uniqueValues = Object.keys(valueCounts).map(key => JSON.parse(key));

            // Get common values (occurring more than once)
            const commonValues = Object.entries(valueCounts)
                .filter(([_, count]) => count > 1)
                .sort((a, b) => b[1] - a[1])
                .map(([key, _]) => JSON.parse(key));

            // Check for null values
            const hasNullValues = values.some(value => value === null || value === undefined);

            // Calculate value range for numeric values
            let valueRange: { min: number; max: number } | undefined;
            if (values.every(value => typeof value === 'number')) {
                const numericValues = values as number[];
                valueRange = {
                    min: Math.min(...numericValues),
                    max: Math.max(...numericValues)
                };
            }

            // Create value pattern
            patterns[columnName] = {
                uniqueValues,
                commonValues,
                valueDistribution: Object.fromEntries(
                    Object.entries(valueCounts).map(([key, count]) => [
                        JSON.parse(key),
                        count / values.length
                    ])
                ),
                hasNullValues,
                valueRange
            };
        }

        return patterns;
    }

    /**
     * Identify relationships between instances
     * @param instances The SimData instances
     * @param schema The SimData schema
     * @returns Instance relationships
     */
    private identifyRelationships(instances: SimDataInstance[], schema: SimDataSchema): InstanceRelationship[] {
        const relationships: InstanceRelationship[] = [];

        // Skip if too few instances
        if (!instances || instances.length < 2) {
            return relationships;
        }

        // Create a map of instance IDs to instances for quick lookup
        const instanceMap = new Map<number, SimDataInstance>();
        for (const instance of instances) {
            instanceMap.set(instance.instanceId, instance);
        }

        // Look for reference columns (columns that might reference other instances)
        const referenceColumns = schema.columns.filter(col =>
            col.type === 7 || // Int32
            col.type === 8 || // UInt32
            col.name.toLowerCase().includes('id') ||
            col.name.toLowerCase().includes('ref') ||
            col.name.toLowerCase().includes('parent') ||
            col.name.toLowerCase().includes('child')
        );

        // Check each instance for references to other instances
        for (const instance of instances) {
            for (const column of referenceColumns) {
                const value = instance.values[column.name];

                // Skip if value is not a number or doesn't exist
                if (typeof value !== 'number' || !value) {
                    continue;
                }

                // Check if this value matches another instance's ID
                const referencedInstance = instanceMap.get(value);
                if (referencedInstance) {
                    relationships.push({
                        sourceInstanceId: instance.instanceId,
                        sourceName: instance.name,
                        targetInstanceId: referencedInstance.instanceId,
                        targetName: referencedInstance.name,
                        relationshipType: this.inferRelationshipType(column.name),
                        confidence: this.calculateRelationshipConfidence(column.name),
                        via: column.name
                    });
                }
            }
        }

        return relationships;
    }

    /**
     * Infer the type of relationship based on column name
     * @param columnName The column name
     * @returns Relationship type
     */
    private inferRelationshipType(columnName: string): string {
        const lowerName = columnName.toLowerCase();

        if (lowerName.includes('parent')) {
            return 'child_of';
        } else if (lowerName.includes('child')) {
            return 'parent_of';
        } else if (lowerName.includes('owner')) {
            return 'owned_by';
        } else if (lowerName.includes('target')) {
            return 'targets';
        } else if (lowerName.includes('source')) {
            return 'source_of';
        } else if (lowerName.includes('ref')) {
            return 'references';
        } else {
            return 'related_to';
        }
    }

    /**
     * Calculate confidence in a relationship based on column name
     * @param columnName The column name
     * @returns Confidence score (0-100)
     */
    private calculateRelationshipConfidence(columnName: string): number {
        const lowerName = columnName.toLowerCase();

        if (lowerName === 'parent_id' || lowerName === 'child_id') {
            return 90;
        } else if (lowerName.includes('parent') || lowerName.includes('child')) {
            return 80;
        } else if (lowerName.includes('ref_id') || lowerName === 'reference_id') {
            return 75;
        } else if (lowerName.includes('ref') || lowerName.includes('reference')) {
            return 70;
        } else if (lowerName === 'id' || lowerName.endsWith('_id')) {
            return 60;
        } else {
            return 50;
        }
    }

    /**
     * Identify key instances in a collection
     * @param instances The SimData instances
     * @param schema The SimData schema
     * @returns Array of key instance IDs
     */
    private identifyKeyInstances(instances: SimDataInstance[], schema: SimDataSchema): number[] {
        const keyInstances: number[] = [];

        // Skip if no instances
        if (!instances || instances.length === 0) {
            return keyInstances;
        }

        // Instances that are referenced by others are likely key instances
        const referenceCounts = new Map<number, number>();

        // Look for reference columns
        const referenceColumns = schema.columns.filter(col =>
            col.type === 7 || // Int32
            col.type === 8 || // UInt32
            col.name.toLowerCase().includes('id') ||
            col.name.toLowerCase().includes('ref')
        );

        // Count references to each instance
        for (const instance of instances) {
            for (const column of referenceColumns) {
                const value = instance.values[column.name];

                // Skip if value is not a number or doesn't exist
                if (typeof value !== 'number' || !value) {
                    continue;
                }

                // Increment reference count
                referenceCounts.set(value, (referenceCounts.get(value) || 0) + 1);
            }
        }

        // Instances with the most references are key instances
        const sortedInstances = Array.from(referenceCounts.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5) // Top 5 most referenced instances
            .map(([id, _]) => id);

        // Add instances with special names
        const specialNameInstances = instances
            .filter(instance =>
                instance.name.toLowerCase().includes('default') ||
                instance.name.toLowerCase().includes('base') ||
                instance.name.toLowerCase().includes('root') ||
                instance.name.toLowerCase().includes('main')
            )
            .map(instance => instance.instanceId);

        // Combine and deduplicate
        return [...new Set([...sortedInstances, ...specialNameInstances])];
    }

    /**
     * Categorize instances based on patterns and values
     * @param instances The SimData instances
     * @param schema The SimData schema
     * @returns Categories with instance IDs
     */
    private categorizeInstances(instances: SimDataInstance[], schema: SimDataSchema): Record<string, number[]> {
        const categories: Record<string, number[]> = {};

        // Skip if no instances
        if (!instances || instances.length === 0) {
            return categories;
        }

        // Look for categorical columns
        const categoricalColumns = schema.columns.filter(col =>
            col.type === 1 || // Boolean
            col.type === 3 || // UInt8 (often used for enums)
            col.type === 5 || // UInt16 (often used for enums)
            col.type === 12 || // String
            col.name.toLowerCase().includes('type') ||
            col.name.toLowerCase().includes('category') ||
            col.name.toLowerCase().includes('state') ||
            col.name.toLowerCase().includes('flag')
        );

        // Categorize by each categorical column
        for (const column of categoricalColumns) {
            // Skip if column doesn't exist in any instance
            if (!instances.some(instance => instance.values && column.name in instance.values)) {
                continue;
            }

            // Group instances by value
            const valueGroups: Record<string, number[]> = {};

            for (const instance of instances) {
                const value = instance.values[column.name];

                // Skip if value doesn't exist
                if (value === undefined || value === null) {
                    continue;
                }

                // Convert value to string for grouping
                const valueKey = JSON.stringify(value);

                // Add instance to group
                if (!valueGroups[valueKey]) {
                    valueGroups[valueKey] = [];
                }
                valueGroups[valueKey].push(instance.instanceId);
            }

            // Add groups to categories
            for (const [valueKey, instanceIds] of Object.entries(valueGroups)) {
                // Skip small groups (likely not meaningful categories)
                if (instanceIds.length < 2) {
                    continue;
                }

                const value = JSON.parse(valueKey);
                const categoryName = `${column.name}=${value}`;
                categories[categoryName] = instanceIds;
            }
        }

        return categories;
    }
}
