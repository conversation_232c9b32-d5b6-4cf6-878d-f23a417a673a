/**
 * Calculate the Levenshtein distance between two strings
 * @param str1 First string
 * @param str2 Second string
 * @returns The Levenshtein distance
 */
export function levenshteinDistance(str1: string, str2: string): number {
    // Create a matrix of size (str1.length + 1) x (str2.length + 1)
    const matrix: number[][] = Array(str1.length + 1).fill(null).map(() => Array(str2.length + 1).fill(null));

    // Fill the first row and column
    for (let i = 0; i <= str1.length; i++) {
        matrix[i][0] = i;
    }
    for (let j = 0; j <= str2.length; j++) {
        matrix[0][j] = j;
    }

    // Fill the rest of the matrix
    for (let i = 1; i <= str1.length; i++) {
        for (let j = 1; j <= str2.length; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1, // deletion
                matrix[i][j - 1] + 1, // insertion
                matrix[i - 1][j - 1] + cost // substitution
            );
        }
    }

    // Return the bottom-right cell
    return matrix[str1.length][str2.length];
}

/**
 * Calculate the similarity between two strings using Levenshtein distance
 * @param str1 First string
 * @param str2 Second string
 * @returns Similarity score between 0.0 and 1.0
 */
export function calculateStringSimilarity(str1: string, str2: string): number {
    // Handle edge cases
    if (str1 === str2) {
        return 1.0;
    }
    if (str1.length === 0 || str2.length === 0) {
        return 0.0;
    }

    // Calculate Levenshtein distance
    const distance = levenshteinDistance(str1, str2);

    // Calculate similarity as 1 - (distance / max length)
    const maxLength = Math.max(str1.length, str2.length);
    return 1.0 - (distance / maxLength);
}

/**
 * Calculate the Jaccard similarity between two strings
 * @param str1 First string
 * @param str2 Second string
 * @param ngramSize Size of n-grams to use (default: 2)
 * @returns Jaccard similarity score between 0.0 and 1.0
 */
export function calculateJaccardSimilarity(str1: string, str2: string, ngramSize: number = 2): number {
    // Handle edge cases
    if (str1 === str2) {
        return 1.0;
    }
    if (str1.length === 0 || str2.length === 0) {
        return 0.0;
    }

    // Generate n-grams for both strings
    const ngrams1 = generateNgrams(str1, ngramSize);
    const ngrams2 = generateNgrams(str2, ngramSize);

    // Calculate intersection size
    const intersection = new Set([...ngrams1].filter(x => ngrams2.has(x)));

    // Calculate union size
    const union = new Set([...ngrams1, ...ngrams2]);

    // Calculate Jaccard similarity
    return intersection.size / union.size;
}

/**
 * Generate n-grams from a string
 * @param str The input string
 * @param n The size of each n-gram
 * @returns Set of n-grams
 */
function generateNgrams(str: string, n: number): Set<string> {
    const ngrams = new Set<string>();
    
    // If string is shorter than n, return the string itself
    if (str.length <= n) {
        ngrams.add(str);
        return ngrams;
    }

    // Generate n-grams
    for (let i = 0; i <= str.length - n; i++) {
        ngrams.add(str.substring(i, i + n));
    }

    return ngrams;
}

/**
 * Calculate the cosine similarity between two strings
 * @param str1 First string
 * @param str2 Second string
 * @returns Cosine similarity score between 0.0 and 1.0
 */
export function calculateCosineSimilarity(str1: string, str2: string): number {
    // Handle edge cases
    if (str1 === str2) {
        return 1.0;
    }
    if (str1.length === 0 || str2.length === 0) {
        return 0.0;
    }

    // Tokenize strings into words
    const words1 = str1.toLowerCase().split(/\W+/).filter(word => word.length > 0);
    const words2 = str2.toLowerCase().split(/\W+/).filter(word => word.length > 0);

    // Create word frequency maps
    const freqMap1 = new Map<string, number>();
    const freqMap2 = new Map<string, number>();

    // Count word frequencies in first string
    for (const word of words1) {
        freqMap1.set(word, (freqMap1.get(word) || 0) + 1);
    }

    // Count word frequencies in second string
    for (const word of words2) {
        freqMap2.set(word, (freqMap2.get(word) || 0) + 1);
    }

    // Get unique words from both strings
    const uniqueWords = new Set([...freqMap1.keys(), ...freqMap2.keys()]);

    // Calculate dot product
    let dotProduct = 0;
    let magnitude1 = 0;
    let magnitude2 = 0;

    for (const word of uniqueWords) {
        const freq1 = freqMap1.get(word) || 0;
        const freq2 = freqMap2.get(word) || 0;

        dotProduct += freq1 * freq2;
        magnitude1 += freq1 * freq1;
        magnitude2 += freq2 * freq2;
    }

    // Calculate magnitudes
    magnitude1 = Math.sqrt(magnitude1);
    magnitude2 = Math.sqrt(magnitude2);

    // Calculate cosine similarity
    if (magnitude1 === 0 || magnitude2 === 0) {
        return 0.0;
    }

    return dotProduct / (magnitude1 * magnitude2);
}

/**
 * Calculate the similarity between two strings using multiple algorithms
 * and return the highest similarity score
 * @param str1 First string
 * @param str2 Second string
 * @returns The highest similarity score between 0.0 and 1.0
 */
export function calculateBestStringSimilarity(str1: string, str2: string): number {
    // Calculate similarity using different algorithms
    const levenshteinSimilarity = calculateStringSimilarity(str1, str2);
    const jaccardSimilarity = calculateJaccardSimilarity(str1, str2);
    const cosineSimilarity = calculateCosineSimilarity(str1, str2);

    // Return the highest similarity score
    return Math.max(levenshteinSimilarity, jaccardSimilarity, cosineSimilarity);
}
