/**
 * Package related type definitions
 */

import { ResourceKey } from '../../types/resource/interfaces.js';

/**
 * Represents a Sims 4 resource
 */
export interface Resource {
  /**
   * Resource key (type, group, instance)
   */
  key: ResourceKey;

  /**
   * Resource data
   */
  value: any;

  /**
   * Resource size in bytes
   */
  size?: number;

  /**
   * Resource path in package
   */
  path?: string;

  /**
   * Source package name or path
   */
  sourcePkg?: string;
}

/**
 * Mod category enumeration
 */
export enum ModCategory {
  Build = 'build',
  Buy = 'buy',
  CAS = 'cas',
  Gameplay = 'gameplay',
  Script = 'script',
  Interface = 'interface',
  Miscellaneous = 'misc',
  Unknown = 'unknown',
}

/**
 * Package type
 */
export interface Package {
  /**
   * Package path
   */
  path: string;

  /**
   * Package name
   */
  name: string;

  /**
   * Package size in bytes
   */
  size: number;

  /**
   * Last modified timestamp
   */
  lastModified: number;

  /**
   * Resources contained in the package
   */
  resources?: Resource[];

  /**
   * Package category
   */
  category?: ModCategory;

  /**
   * Whether the package has been analyzed
   */
  analyzed?: boolean;

  /**
   * Whether the package has conflicts
   */
  hasConflicts?: boolean;

  /**
   * Package metadata
   */
  metadata?: Record<string, any>;
}
