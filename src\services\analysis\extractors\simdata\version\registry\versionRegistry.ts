/**
 * SimData version registry
 * Tracks and monitors SimData versions
 */

import { Logger } from '../../../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../../../databaseService.js';
import { VersionInfo } from '../types.js';
import { createVersionErrorContext, handleVersionError } from '../error/versionHandlerErrorHandler.js';

const logger = new Logger('VersionRegistry');

/**
 * SimData version registry
 * Tracks and monitors SimData versions
 */
export class VersionRegistry {
    private static instance: VersionRegistry;
    private databaseService: DatabaseService;
    private versionMap: Map<number, VersionInfo> = new Map();
    private initialized: boolean = false;

    private constructor(databaseService: DatabaseService) {
        this.databaseService = databaseService;
    }

    /**
     * Get the singleton instance of the registry
     * @param databaseService Database service to use
     * @returns VersionRegistry instance
     */
    public static getInstance(databaseService: DatabaseService): VersionRegistry {
        if (!VersionRegistry.instance) {
            VersionRegistry.instance = new VersionRegistry(databaseService);
        }
        return VersionRegistry.instance;
    }

    /**
     * Initialize the registry from the database
     */
    public async initialize(): Promise<void> {
        if (this.initialized) return;

        try {
            // Create the table if it doesn't exist
            await this.databaseService.run(`
                CREATE TABLE IF NOT EXISTS simdata_versions (
                    version INTEGER PRIMARY KEY,
                    count INTEGER NOT NULL DEFAULT 0,
                    first_seen TEXT NOT NULL,
                    last_seen TEXT NOT NULL,
                    schema_names TEXT NOT NULL,
                    mod_names TEXT NOT NULL,
                    is_standard INTEGER NOT NULL DEFAULT 0,
                    is_special INTEGER NOT NULL DEFAULT 0,
                    has_custom_handler INTEGER NOT NULL DEFAULT 0
                )
            `);

            // Load existing versions from the database
            const rows = await this.databaseService.all('SELECT * FROM simdata_versions');

            for (const row of rows) {
                this.versionMap.set(row.version, {
                    version: row.version,
                    count: row.count,
                    firstSeen: new Date(row.first_seen),
                    lastSeen: new Date(row.last_seen),
                    schemaNames: new Set(JSON.parse(row.schema_names)),
                    modNames: new Set(JSON.parse(row.mod_names)),
                    isStandard: row.is_standard === 1,
                    isSpecial: row.is_special === 1,
                    hasCustomHandler: row.has_custom_handler === 1
                });
            }

            this.initialized = true;
            logger.info(`Initialized SimData version registry with ${this.versionMap.size} versions`);
        } catch (error) {
            handleVersionError(
                error,
                createVersionErrorContext(undefined, 'VersionRegistry.initialize'),
                undefined
            );
        }
    }

    /**
     * Register a version sighting
     * @param version SimData version
     * @param schemaName Schema name
     * @param modName Mod name
     * @param isStandard Whether this is a standard version
     * @param isSpecial Whether this is a special version
     * @param hasCustomHandler Whether this version has a custom handler
     */
    public async registerVersion(
        version: number,
        schemaName: string = 'Unknown',
        modName: string = 'Unknown',
        isStandard: boolean = false,
        isSpecial: boolean = false,
        hasCustomHandler: boolean = false
    ): Promise<void> {
        if (!this.initialized) await this.initialize();

        try {
            const now = new Date();
            let versionInfo = this.versionMap.get(version);

            if (versionInfo) {
                // Update existing version info
                versionInfo.count++;
                versionInfo.lastSeen = now;
                versionInfo.schemaNames.add(schemaName);
                versionInfo.modNames.add(modName);

                // Update database
                await this.databaseService.run(
                    `UPDATE simdata_versions
                    SET count = ?, last_seen = ?, schema_names = ?, mod_names = ?
                    WHERE version = ?`,
                    [
                        versionInfo.count,
                        now.toISOString(),
                        JSON.stringify(Array.from(versionInfo.schemaNames)),
                        JSON.stringify(Array.from(versionInfo.modNames)),
                        version
                    ]
                );
            } else {
                // Create new version info
                versionInfo = {
                    version,
                    count: 1,
                    firstSeen: now,
                    lastSeen: now,
                    schemaNames: new Set([schemaName]),
                    modNames: new Set([modName]),
                    isStandard,
                    isSpecial,
                    hasCustomHandler
                };
                this.versionMap.set(version, versionInfo);

                // Insert into database
                await this.databaseService.run(
                    `INSERT INTO simdata_versions
                    (version, count, first_seen, last_seen, schema_names, mod_names, is_standard, is_special, has_custom_handler)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        version,
                        1,
                        now.toISOString(),
                        now.toISOString(),
                        JSON.stringify([schemaName]),
                        JSON.stringify([modName]),
                        isStandard ? 1 : 0,
                        isSpecial ? 1 : 0,
                        hasCustomHandler ? 1 : 0
                    ]
                );

                // Log discovery of new version
                if (!isStandard && !isSpecial) {
                    logger.info(`Discovered new SimData version: ${version} in schema ${schemaName} from mod ${modName}`);
                }
            }
        } catch (error) {
            handleVersionError(
                error,
                createVersionErrorContext(version, 'VersionRegistry.registerVersion'),
                undefined
            );
        }
    }

    /**
     * Get information about a specific version
     * @param version SimData version
     * @returns Version information or undefined if not found
     */
    public getVersionInfo(version: number): VersionInfo | undefined {
        return this.versionMap.get(version);
    }

    /**
     * Get all registered versions
     * @returns Array of version information
     */
    public getAllVersions(): VersionInfo[] {
        return Array.from(this.versionMap.values());
    }

    /**
     * Get unknown versions (versions without custom handlers)
     * @returns Array of version information for unknown versions
     */
    public getUnknownVersions(): VersionInfo[] {
        return Array.from(this.versionMap.values())
            .filter(info => !info.isStandard && !info.hasCustomHandler)
            .sort((a, b) => b.count - a.count); // Sort by frequency (most common first)
    }
}
