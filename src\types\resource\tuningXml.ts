/**
 * Types for Tuning XML analysis
 */

/**
 * Interface for tuning XML class hierarchy information
 */
export interface TuningClassHierarchy {
    className: string;
    parentClass?: string;
    depth: number;
    inheritedProperties?: string[];
    overriddenProperties?: string[];
}

/**
 * Interface for tuning XML override information
 */
export interface TuningOverrideInfo {
    isOverride: boolean;
    targetClass?: string;
    targetInstance?: string;
    overriddenElements: string[];
    intent: 'fix' | 'enhance' | 'replace' | 'unknown';
    confidence: number;
}

/**
 * Interface for critical parameter information
 */
export interface TuningCriticalParameter {
    name: string;
    path: string;
    value: string;
    impact: 'low' | 'medium' | 'high';
    description: string;
    affectsGameplay: boolean;
    potentialConflictRisk: 'low' | 'medium' | 'high';
}

/**
 * Interface for parameter reference information
 */
export interface TuningParameterReference {
    sourcePath: string;
    targetResource?: string;
    targetPath?: string;
    referenceType: string;
    isRequired: boolean;
    referenceContext?: string; // Added property
    resolvedResourceId?: number; // Added property
    resolutionType?: string; // Added property
}

/**
 * Interface for tuning relationship information
 */
export interface TuningRelationship {
    sourceId: number;
    targetId: number;
    relationshipType: string;
    strength: number;
    description: string;
}

/**
 * Interface for tuning classification information
 */
export interface TuningClassification {
    type: string;
    category: string;
    semanticSystems: string[];
    confidence: number;
    description: string;
    tags: string[];
}

/**
 * Interface for tuning XML analysis result
 */
export interface TuningXmlAnalysis {
    resourceId: number;
    rootElement: string;
    rootAttributes: Record<string, string>;
    tuningName?: string;
    tuningClass?: string;
    tuningInstance?: string;
    classification: TuningClassification;
    classHierarchy?: TuningClassHierarchy;
    overrideInfo: TuningOverrideInfo;
    criticalParameters: TuningCriticalParameter[];
    parameterReferences: TuningParameterReference[];
    relationships: TuningRelationship[];
    importantValues: Record<string, string>;
    complexityScore: number;
    potentialConflictRisk: 'low' | 'medium' | 'high';
    analysisConfidence: number;
    isOfficial: boolean;
    moduleReferences: string[];
    tuningReferences: string[];
    overrideNodes: string[];
    dependencyCount: number;
}

/**
 * Common tuning types in Sims 4
 */
export enum TuningType {
    TRAIT = 'Trait',
    BUFF = 'Buff',
    INTERACTION = 'Interaction',
    OBJECT = 'Object',
    ASPIRATION = 'Aspiration',
    CAREER = 'Career',
    RECIPE = 'Recipe',
    LOT = 'Lot',
    SKILL = 'Skill',
    RELATIONSHIP = 'Relationship',
    SITUATION = 'Situation',
    EVENT = 'Event',
    MOOD = 'Mood',
    COMMODITY = 'Commodity',
    STATISTIC = 'Statistic',
    VENUE = 'Venue',
    ZONE_MODIFIER = 'Zone Modifier',
    AWAY_ACTION = 'Away Action',
    SERVICE = 'Service',
    REWARD = 'Reward',
    TUTORIAL = 'Tutorial',
    UI_DIALOG = 'UI Dialog',
    ANIMATION = 'Animation',
    POSTURE = 'Posture',
    SOCIAL = 'Social',
    BROADCASTER = 'Broadcaster',
    LOOT = 'Loot',
    TEST = 'Test',
    SNIPPET = 'Snippet',
    UNKNOWN = 'Unknown'
}

/**
 * Tuning categories in Sims 4
 */
export enum TuningCategory {
    SIM_ATTRIBUTES = 'Sim Attributes',
    INTERACTIONS = 'Interactions',
    BUILD_BUY = 'Build/Buy',
    PROGRESSION = 'Progression',
    GAMEPLAY_EVENTS = 'Gameplay Events',
    CREATE_A_SIM = 'Create-A-Sim',
    UI = 'UI',
    SYSTEM = 'System',
    OTHER = 'Other',
    UNKNOWN = 'Unknown'
}

/**
 * Mapping of tuning types to categories
 */
export const TUNING_TYPE_TO_CATEGORY: Record<TuningType, TuningCategory> = {
    [TuningType.TRAIT]: TuningCategory.SIM_ATTRIBUTES,
    [TuningType.BUFF]: TuningCategory.SIM_ATTRIBUTES,
    [TuningType.MOOD]: TuningCategory.SIM_ATTRIBUTES,
    [TuningType.COMMODITY]: TuningCategory.SIM_ATTRIBUTES,
    [TuningType.STATISTIC]: TuningCategory.SIM_ATTRIBUTES,
    [TuningType.INTERACTION]: TuningCategory.INTERACTIONS,
    [TuningType.SOCIAL]: TuningCategory.INTERACTIONS,
    [TuningType.POSTURE]: TuningCategory.INTERACTIONS,
    [TuningType.ANIMATION]: TuningCategory.INTERACTIONS,
    [TuningType.OBJECT]: TuningCategory.BUILD_BUY,
    [TuningType.LOT]: TuningCategory.BUILD_BUY,
    [TuningType.VENUE]: TuningCategory.BUILD_BUY,
    [TuningType.ZONE_MODIFIER]: TuningCategory.BUILD_BUY,
    [TuningType.CAREER]: TuningCategory.PROGRESSION,
    [TuningType.SKILL]: TuningCategory.PROGRESSION,
    [TuningType.ASPIRATION]: TuningCategory.PROGRESSION,
    [TuningType.REWARD]: TuningCategory.PROGRESSION,
    [TuningType.SITUATION]: TuningCategory.GAMEPLAY_EVENTS,
    [TuningType.EVENT]: TuningCategory.GAMEPLAY_EVENTS,
    [TuningType.RELATIONSHIP]: TuningCategory.SIM_ATTRIBUTES,
    [TuningType.AWAY_ACTION]: TuningCategory.GAMEPLAY_EVENTS,
    [TuningType.SERVICE]: TuningCategory.SYSTEM,
    [TuningType.TUTORIAL]: TuningCategory.UI,
    [TuningType.UI_DIALOG]: TuningCategory.UI,
    [TuningType.BROADCASTER]: TuningCategory.SYSTEM,
    [TuningType.LOOT]: TuningCategory.SYSTEM,
    [TuningType.TEST]: TuningCategory.SYSTEM,
    [TuningType.SNIPPET]: TuningCategory.OTHER,
    [TuningType.RECIPE]: TuningCategory.INTERACTIONS,
    [TuningType.UNKNOWN]: TuningCategory.UNKNOWN
};

/**
 * Common patterns to identify tuning types
 */
export const TUNING_TYPE_PATTERNS: Record<TuningType, string[]> = {
    [TuningType.TRAIT]: ['trait', 'personality', 'character'],
    [TuningType.BUFF]: ['buff', 'moodlet', 'effect'],
    [TuningType.INTERACTION]: ['interaction', 'mixer', 'super_interaction', 'social_super_interaction'],
    [TuningType.OBJECT]: ['object', 'definition', 'object_definition'],
    [TuningType.ASPIRATION]: ['aspiration', 'goal', 'objective'],
    [TuningType.CAREER]: ['career', 'job', 'profession'],
    [TuningType.RECIPE]: ['recipe', 'crafting', 'cooking'],
    [TuningType.LOT]: ['lot', 'venue', 'building'],
    [TuningType.SKILL]: ['skill', 'ability', 'talent'],
    [TuningType.RELATIONSHIP]: ['relationship', 'relation', 'social'],
    [TuningType.SITUATION]: ['situation', 'scenario', 'event'],
    [TuningType.EVENT]: ['event', 'occurrence', 'happening'],
    [TuningType.MOOD]: ['mood', 'emotion', 'feeling'],
    [TuningType.COMMODITY]: ['commodity', 'resource', 'need'],
    [TuningType.STATISTIC]: ['statistic', 'stat', 'value'],
    [TuningType.VENUE]: ['venue', 'location', 'place'],
    [TuningType.ZONE_MODIFIER]: ['zone_modifier', 'lot_modifier', 'area_effect'],
    [TuningType.AWAY_ACTION]: ['away_action', 'rabbit_hole', 'off_lot'],
    [TuningType.SERVICE]: ['service', 'npc_service', 'utility'],
    [TuningType.REWARD]: ['reward', 'prize', 'achievement'],
    [TuningType.TUTORIAL]: ['tutorial', 'guide', 'help'],
    [TuningType.UI_DIALOG]: ['ui_dialog', 'dialog', 'popup'],
    [TuningType.ANIMATION]: ['animation', 'anim', 'pose'],
    [TuningType.POSTURE]: ['posture', 'stance', 'position'],
    [TuningType.SOCIAL]: ['social', 'socials', 'social_interaction'],
    [TuningType.BROADCASTER]: ['broadcaster', 'broadcast', 'emitter'],
    [TuningType.LOOT]: ['loot', 'reward', 'action'],
    [TuningType.TEST]: ['test', 'condition', 'check'],
    [TuningType.SNIPPET]: ['snippet', 'fragment', 'piece'],
    [TuningType.UNKNOWN]: []
};

/**
 * Critical gameplay parameters that often cause conflicts
 */
export const CRITICAL_GAMEPLAY_PARAMETERS = [
    'multiplier',
    'chance',
    'probability',
    'duration',
    'cost',
    'value',
    'weight',
    'threshold',
    'cooldown',
    'timeout',
    'decay_rate',
    'gain_rate',
    'autonomy_weight',
    'success_chance',
    'fail_chance',
    'critical_threshold',
    'max_value',
    'min_value',
    'initial_value',
    'convergence_value',
    'decay_time',
    'gain_time',
    'interval',
    'frequency',
    'priority',
    'score',
    'mood_type',
    'mood_weight',
    'mood_category',
    'mood_intensity',
    'mood_threshold',
    'mood_effect',
    'skill_gain',
    'skill_threshold',
    'skill_multiplier',
    'relationship_gain',
    'relationship_loss',
    'relationship_threshold',
    'relationship_multiplier',
    'commodity_gain',
    'commodity_loss',
    'commodity_threshold',
    'commodity_multiplier',
    'commodity_convergence',
    'commodity_decay',
    'commodity_gain_rate',
    'commodity_loss_rate',
    'commodity_max',
    'commodity_min',
    'commodity_initial',
    'commodity_interval',
    'commodity_frequency',
    'commodity_priority',
    'commodity_score',
    'commodity_effect',
    'commodity_threshold',
    'commodity_multiplier',
    'commodity_convergence',
    'commodity_decay',
    'commodity_gain_rate',
    'commodity_loss_rate',
    'commodity_max',
    'commodity_min',
    'commodity_initial',
    'commodity_interval',
    'commodity_frequency',
    'commodity_priority',
    'commodity_score',
    'commodity_effect'
];
