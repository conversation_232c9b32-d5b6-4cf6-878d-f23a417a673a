﻿﻿﻿﻿﻿﻿﻿﻿// Corrected import
import { <PERSON><PERSON><PERSON> } from '../resource/interfaces.js';

/**
 * Conflict severity levels - Enhanced with practical categories
 * Based on analysis of existing Sims 4 mod manager approaches
 */
export enum ConflictSeverity {
  NONE = 'NONE',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
  UNKNOWN = 'UNKNOWN',
  // New practical categories for better user experience
  DUPLICATE = 'DUPLICATE',        // Exact same file (CRC32 match)
  RECOLOR = 'RECOLOR',           // Recolor of same item (harmless)
  OVERRIDE = 'OVERRIDE',         // One mod overrides another
  INCOMPATIBLE = 'INCOMPATIBLE', // Mods that break each other
  HARMLESS = 'HARMLESS'          // Overlapping but safe
}

/**
 * Conflict Types
 *
 * This module defines the different types of conflicts that can occur in mods.
 */
export enum ConflictType {
  RESOURCE = 'RESOURCE',
  METADATA = 'METADATA',
  VERSION = 'VERSION',
  DEPENDENCY = 'DEPENDENCY',
  CUSTOM = 'CUSTOM',
  CONTENT = 'CONTENT',
  // Additional conflict types for specific resource categories
  TUNING = 'TUNING',
  SCRIPT = 'SCRIPT',
  CASPART = 'CASPART',
  OBJECT = 'OBJECT',
  ANIMATION = 'ANIMATION',
  SOUND = 'SOUND',
  IMAGE = 'IMAGE',
  // Semantic conflict types
  SEMANTIC_GAMEPLAY_SYSTEM = 'SEMANTIC_GAMEPLAY_SYSTEM',
  SEMANTIC_PURPOSE = 'SEMANTIC_PURPOSE',
  SEMANTIC_RELATIONSHIP = 'SEMANTIC_RELATIONSHIP',
  SEMANTIC_CONTEXT = 'SEMANTIC_CONTEXT'
}

/**
 * Base conflict result interface
 */
export interface ConflictResult {
  hasConflicts: boolean;
  conflicts: ResourceConflict[];
  timestamp: number;
  metadata?: {
    [key: string]: unknown;
  };
}

/**
 * Resource conflict interface
 */
export interface ResourceConflict {
  id: string;
  type: ConflictType;
  severity: ConflictSeverity;
  description: string;
  affectedResources: ResourceKey[];
  timestamp: number;
  recommendations: string[];
  resolution?: string;
  metadata?: {
    [key: string]: unknown;
  };
}

/**
 * Conflict detection options interface
 */
export interface ConflictDetectionOptions {
  detectTypeConflicts?: boolean;
  detectVersionConflicts?: boolean;
  detectDependencyConflicts?: boolean;
  detectResourceOverrides?: boolean;
  detectMetadataConflicts?: boolean;
  skipDetection?: string[];
  customDetection?: {
    [key: string]: (resource: unknown) => ResourceConflict[];
  };
}

/**
 * Conflict detection result interface
 */
export interface ConflictDetectionResult {
  conflicts: ConflictResult[];
  options: ConflictDetectionOptions;
  timestamp: number;
  duration: number;
  totalPackages: number;
  totalConflicts: number;
  severityBreakdown: {
    [key in ConflictSeverity]: number;
  };
  typeBreakdown: {
    [key in ConflictType]: number;
  };
}

/**
 * Unified package conflict interface
 */
export interface UnifiedPackageConflict extends ConflictResult {
  conflictingPackages: string[];
  resourceType: number;
  resourceName: string;
  resourcePath: string;
  versionInfo?: {
    required: string;
    actual: string;
  };
  dependencyInfo?: {
    missing: string[];
    incompatible: string[];
  };
  overrideInfo?: {
    original: string;
    override: string;
  };
  compatibilityInfo?: {
    gameVersion: string;
    packRequirements: string[];
  };
  scriptInfo?: {
    functionName: string;
    lineNumber: number;
    columnNumber: number;
  };
  tuningInfo?: {
    instanceId: string;
    tuningType: string;
  };
  assetInfo?: {
    assetId: string;
    assetType: string;
  };
  dataInfo?: {
    dataId: string;
    dataType: string;
  };
}
