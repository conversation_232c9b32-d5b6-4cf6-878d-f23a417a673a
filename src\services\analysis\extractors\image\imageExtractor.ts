/**
 * Main image extractor module
 */

import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../../services/databaseService.js';
import { DependencyInfo } from '../../../../types/database.js';
import { ImageFormat, ImageHeaderInfo, ImageParseResult } from './types.js';
import { detectImageFormat } from './formats/formatDetector.js';
import { parseDDS } from './formats/ddsParser.js';
import { parsePNG } from './formats/pngParser.js';
import { parseJPEG } from './formats/jpegParser.js';
import { parseRLE2 } from './formats/rle2Parser.js';
import { generateImageContentSnippet, generateErrorContentSnippet } from './utils/contentSnippetGenerator.js';
import { convertToResourceMetadata } from './utils/metadataConverter.js';
import { saveImageMetadata, saveImageDependencies } from './database/databaseUtils.js';
import { createImageErrorContext, handleImageError, withImageExtractionErrorHandling } from './error/imageExtractorErrorHandler.js';

// Create a logger instance
const log = new Logger('ImageExtractor');

/**
 * Parses an image buffer based on its format
 * @param buffer Image buffer
 * @param format Image format
 * @param resourceId Resource ID
 * @param instanceId Instance ID (as hex string)
 * @returns Parsed image result
 */
function parseImageBuffer(
    buffer: Buffer,
    format: ImageFormat,
    resourceId: number,
    instanceId: string
): ImageHeaderInfo {
    // Parse based on format
    switch (format) {
        case ImageFormat.DDS:
            return parseDDS(buffer, resourceId, instanceId);
        case ImageFormat.PNG:
            return parsePNG(buffer, resourceId, instanceId);
        case ImageFormat.JPEG:
            return parseJPEG(buffer, resourceId, instanceId);
        case ImageFormat.RLE2:
            return parseRLE2(buffer, resourceId, instanceId);
        default:
            // Handle unknown formats gracefully without warning spam
            log.debug(`Handling unknown image format: ${format} for resource ${resourceId}`);
            return {
                format: ImageFormat.UNKNOWN,
                width: undefined,
                height: undefined,
                hasAlpha: false
            };
    }
}

/**
 * Extracts metadata from an image resource
 * @param key Resource key
 * @param buffer Resource buffer
 * @param resourceId Resource ID
 * @param databaseService Database service
 * @returns Extracted metadata
 */
export const extractImageMetadata = withImageExtractionErrorHandling(
    async function extractImageMetadataInternal(
        key: AppResourceKey,
        buffer: Buffer,
        resourceId: number,
        databaseService: DatabaseService
    ): Promise<Partial<ResourceMetadata>> {
        // Convert instance to hex string for logging
        const instanceId = key.instance.toString(16);

        try {
            // Check if buffer is too small
            if (buffer.length < 16) {
                log.warn(`Image buffer too small for ${instanceId}`);
                return { contentSnippet: '[Image Buffer Too Small]' };
            }

            // Detect image format
            const format = detectImageFormat(buffer);
            log.debug(`Detected image format: ${format} for resource ${instanceId}`);

            // Parse image buffer
            const header = parseImageBuffer(buffer, format, resourceId, instanceId);

            // Generate content snippet
            const contentSnippet = generateImageContentSnippet(header);

            // Convert to resource metadata
            const extractedMetadata = convertToResourceMetadata(header);

            // Save metadata to database
            await saveImageMetadata(header, resourceId, databaseService);

            // Image resources typically don't have TGI dependencies
            const dependencies: DependencyInfo[] = [];

            // Save dependencies to database (if any)
            if (dependencies.length > 0) {
                await saveImageDependencies(dependencies, resourceId, databaseService);
            }

            // Return extracted metadata
            return {
                contentSnippet,
                ...extractedMetadata
            };
        } catch (error: any) {
            // Create error context
            const context = createImageErrorContext(resourceId, instanceId, 'extractImageMetadata');

            // Handle error
            const result = handleImageError(error, context);

            // Return minimal metadata
            return {
                contentSnippet: result.contentSnippet,
                imageFormat: result.header.format
            };
        }
    }
);
