<template>
  <!-- Use Vuetify Card -->
  <v-card class="file-upload-card mb-4"> <!-- Added margin-bottom -->
    <v-card-text>
      <p class="text-h6 mb-2"> <!-- Vuetify typography class -->
        Select Package Files
      </p>
      <p class="text-body-1 mb-4"> <!-- Vuetify typography class -->
        Click the button below to select one or more Sims 4 package (.package) files for analysis.
      </p>
      <!-- Use Vuetify Button -->
      <v-btn
        color="primary"
        @click="requestAnalysisViaDialog"
        :disabled="isAnalyzing"
      >
        Select Files & Start Analysis
      </v-btn>
       <!-- Display status from store -->
       <p v-if="isAnalyzing" class="text-body-2 mt-3 text-medium-emphasis"> <!-- Vuetify typography classes -->
         Status: {{ analysisStore.status }}
       </p>
       <!-- Display selected file count (optional, could show names if needed) -->
       <p v-if="selectedFileCount > 0 && !isAnalyzing" class="text-body-2 mt-3 text-medium-emphasis"> <!-- Vuetify typography classes -->
         Selected {{ selectedFileCount }} file(s) for last analysis.
       </p>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useAnalysisStore } from '../store/analysis';
// Vuetify components are globally available via the plugin

const analysisStore = useAnalysisStore();
const selectedFiles = ref<string[]>([]);

// Computed properties to reactively get state from the store
const isAnalyzing = computed(() => analysisStore.isAnalyzing);
const selectedFileCount = computed(() => selectedFiles.value.length || analysisStore.currentFiles.length);

// Set up file selection listener
onMounted(() => {
  console.log('FileUploadComponent mounted');

  // Listen for files-selected event from the main process
  document.addEventListener('files-selected', (event: any) => {
    console.log('Files selected event received:', event.detail);
    selectedFiles.value = event.detail || [];

    if (selectedFiles.value.length > 0) {
      // Update the analysis store
      analysisStore.updateProgress(20, `Processing ${selectedFiles.value.length} selected files...`);
    } else {
      // No files selected or dialog canceled
      analysisStore.isAnalyzing = false;
      analysisStore.status = 'File selection canceled.';
    }
  });
});

// Request the main process to open a file dialog and start analysis
const requestAnalysisViaDialog = async () => { // Make the function async
  console.log('Button clicked: Requesting main process to open file dialog for analysis...');

  // Log window.electronAPI just before checking
  console.log('Checking window.electronAPI:', window.electronAPI);

  // Check if the exposed openFileDialog function exists
  if (window.electronAPI?.openFileDialog) {
    // Set status to indicate we're waiting for file selection
    analysisStore.isAnalyzing = true; // Set analyzing state immediately
    analysisStore.updateProgress(10, 'Waiting for file selection...');

    try {
      console.log('Calling electronAPI.openFileDialog()');
      // Call the specific function exposed via contextBridge
      await window.electronAPI.openFileDialog(); // Use await as it returns a Promise now
      console.log('File dialog request sent successfully via electronAPI.openFileDialog()');
      // Note: Analysis state is handled by events triggered from main process after dialog selection
    } catch (error: any) {
      console.error('Error calling electronAPI.openFileDialog():', error);
      analysisStore.setError(`Error triggering file dialog: ${error.message || 'Unknown error'}`);
      analysisStore.isAnalyzing = false; // Reset state on error
    }
  } else {
    console.error('window.electronAPI.openFileDialog is not available'); // Check for openFileDialog
    console.log('Available on window:', Object.keys(window));
    analysisStore.setError('IPC API (electronAPI.openFileDialog) is not available.');
    analysisStore.isAnalyzing = false;
  }
};

</script>

<style scoped>
.file-upload-card {
  /* Keep or adjust custom styles as needed */
  text-align: center;
}
/* Vuetify spacing classes like mb-4, mt-3 used instead of p-mt-* */
</style>
