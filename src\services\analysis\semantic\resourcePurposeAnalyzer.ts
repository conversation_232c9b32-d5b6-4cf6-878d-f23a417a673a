/**
 * Resource Purpose Analyzer
 *
 * Analyzes resources to determine their purpose and categorize them
 *
 * Enhanced with:
 * - Context-aware analysis
 * - Content semantic analysis
 * - Pattern-based analysis
 * - Resource type purpose mappings
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourcePurposeAnalysis, ResourcePurposeType } from './interfaces/resourcePurpose.js';
import { RESOURCE_PURPOSE_CATEGORIES, PURPOSE_TYPE_CATEGORIES } from './constants/resourcePurposes.js';
import { SemanticDatabase } from './database/semanticDatabase.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { GameplaySystemRegistry } from './gameplaySystemRegistry.js';
import { GameplaySystemCategorization } from './interfaces/gameplaySystem.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';
import { injectable, singleton } from '../../di/decorators.js';
import { ContextAwareAnalyzer, ResourceRelationshipType } from './contextAwareAnalyzer.js';
import { ContentSemanticAnalyzer } from './contentSemanticAnalyzer.js';
import { PatternBasedAnalyzer } from './patternBasedAnalyzer.js';
import { getPurposeMappingForResourceType } from './data/resourceTypePurposes.js';
import { SimDataPurposeAnalyzer } from './specialized/simDataPurposeAnalyzer.js';
import { TuningPurposeAnalyzer } from './specialized/tuningPurposeAnalyzer.js';
import { ScriptPurposeAnalyzer } from './specialized/scriptPurposeAnalyzer.js';
import { EnhancedDependencyChainAnalyzer } from './dependencyChainAnalyzer.js';

/**
 * Analyzer for resource purposes
 */
@singleton()
export class ResourcePurposeAnalyzer {
    private logger: Logger;
    private semanticDatabase: SemanticDatabase;
    private gameplaySystemRegistry: GameplaySystemRegistry;
    private contextAwareAnalyzer: ContextAwareAnalyzer;
    private contentSemanticAnalyzer: ContentSemanticAnalyzer;
    private patternBasedAnalyzer: PatternBasedAnalyzer;
    private simDataPurposeAnalyzer: SimDataPurposeAnalyzer;
    private tuningPurposeAnalyzer: TuningPurposeAnalyzer;
    private scriptPurposeAnalyzer: ScriptPurposeAnalyzer;
    private dependencyChainAnalyzer: EnhancedDependencyChainAnalyzer;
    private purposeCategories: Map<string, any> = new Map();
    private initialized: boolean = false;

    /**
     * Constructor
     * @param databaseService The database service
     * @param gameplaySystemRegistry The gameplay system registry
     * @param contextAwareAnalyzer The context-aware analyzer
     * @param contentSemanticAnalyzer The content semantic analyzer
     * @param patternBasedAnalyzer The pattern-based analyzer
     * @param simDataPurposeAnalyzer The SimData purpose analyzer
     * @param tuningPurposeAnalyzer The tuning purpose analyzer
     * @param scriptPurposeAnalyzer The script purpose analyzer
     * @param dependencyChainAnalyzer The dependency chain analyzer
     * @param logger The logger instance
     */
    constructor(
        private databaseService: DatabaseService,
        gameplaySystemRegistry?: GameplaySystemRegistry,
        contextAwareAnalyzer?: ContextAwareAnalyzer,
        contentSemanticAnalyzer?: ContentSemanticAnalyzer,
        patternBasedAnalyzer?: PatternBasedAnalyzer,
        simDataPurposeAnalyzer?: SimDataPurposeAnalyzer,
        tuningPurposeAnalyzer?: TuningPurposeAnalyzer,
        scriptPurposeAnalyzer?: ScriptPurposeAnalyzer,
        dependencyChainAnalyzer?: EnhancedDependencyChainAnalyzer,
        logger?: Logger
    ) {
        this.logger = logger || new Logger('ResourcePurposeAnalyzer');
        this.semanticDatabase = new SemanticDatabase(databaseService, this.logger);
        this.gameplaySystemRegistry = gameplaySystemRegistry || new GameplaySystemRegistry(databaseService, this.logger);
        this.contextAwareAnalyzer = contextAwareAnalyzer || new ContextAwareAnalyzer(databaseService, this.logger);
        this.contentSemanticAnalyzer = contentSemanticAnalyzer || new ContentSemanticAnalyzer(this.logger);
        this.patternBasedAnalyzer = patternBasedAnalyzer || new PatternBasedAnalyzer(this.logger);
        this.simDataPurposeAnalyzer = simDataPurposeAnalyzer || new SimDataPurposeAnalyzer(this.logger);
        this.tuningPurposeAnalyzer = tuningPurposeAnalyzer || new TuningPurposeAnalyzer(this.logger);
        this.scriptPurposeAnalyzer = scriptPurposeAnalyzer || new ScriptPurposeAnalyzer(this.logger);
        this.dependencyChainAnalyzer = dependencyChainAnalyzer || new EnhancedDependencyChainAnalyzer(databaseService, this.logger);
    }

    /**
     * Initialize the analyzer
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Initialize the semantic database
            await this.semanticDatabase.initialize();

            // Initialize the gameplay system registry if it has an initialize method
            if (this.gameplaySystemRegistry && typeof this.gameplaySystemRegistry.initialize === 'function') {
                await this.gameplaySystemRegistry.initialize();
            }

            // Initialize the context-aware analyzer if it has an initialize method
            if (this.contextAwareAnalyzer && typeof this.contextAwareAnalyzer.initialize === 'function') {
                await this.contextAwareAnalyzer.initialize();
            }

            // Initialize the dependency chain analyzer if it has an initialize method
            if (this.dependencyChainAnalyzer && typeof this.dependencyChainAnalyzer.initialize === 'function') {
                await this.dependencyChainAnalyzer.initialize();
            }

            // Load all purpose categories into memory
            for (const category of RESOURCE_PURPOSE_CATEGORIES) {
                this.purposeCategories.set(category.id, category);
            }

            this.initialized = true;
            this.logger.info(`Initialized ResourcePurposeAnalyzer with ${this.purposeCategories.size} purpose categories`);
            this.logger.info('Enhanced ResourcePurposeAnalyzer with context-aware, content semantic, and pattern-based analysis');
            this.logger.info('Enhanced ResourcePurposeAnalyzer with specialized analyzers for SimData, Tuning, and Script resources');
        } catch (error) {
            this.logger.error('Error initializing ResourcePurposeAnalyzer:', error);
            throw error;
        }
    }

    /**
     * Get the dependency chain analyzer
     * @returns The dependency chain analyzer
     */
    public getDependencyChainAnalyzer(): EnhancedDependencyChainAnalyzer | undefined {
        return this.dependencyChainAnalyzer;
    }

    /**
     * Set the dependency chain analyzer
     * @param analyzer The dependency chain analyzer
     */
    public setDependencyChainAnalyzer(analyzer: EnhancedDependencyChainAnalyzer): void {
        this.dependencyChainAnalyzer = analyzer;
    }

    /**
     * Analyze a resource to determine its purpose
     * @param resourceKey The resource key
     * @param resourceId The resource ID in the database
     * @param resourceMetadata The resource metadata
     * @param resourceContent Optional resource content for deeper analysis
     * @returns Resource purpose analysis result
     */
    public async analyzeResourcePurpose(
        resourceKey: ResourceKey,
        resourceId: number,
        resourceMetadata: Record<string, any>,
        resourceContent?: string
    ): Promise<ResourcePurposeAnalysis> {
        await this.initialize();

        try {
            // Check if we have valid inputs
            if (!resourceKey || !resourceId) {
                this.logger.warn(`Invalid inputs for analyzeResourcePurpose: resourceKey=${resourceKey}, resourceId=${resourceId}`);
                return {
                    resourceId: resourceId || 0,
                    primaryPurpose: 'unknown',
                    purposeType: ResourcePurposeType.UNKNOWN,
                    confidence: 0,
                    description: 'Invalid inputs',
                    timestamp: Date.now()
                };
            }

            // Get resource type as hex string
            const resourceType = resourceKey?.type;
            if (resourceType === undefined) {
                this.logger.warn('Resource type is undefined');
                return {
                    resourceId,
                    primaryPurpose: 'unknown',
                    purposeType: ResourcePurposeType.UNKNOWN,
                    confidence: 0,
                    description: 'Resource type is undefined',
                    timestamp: Date.now()
                };
            }
            const resourceTypeHex = `0x${resourceType.toString(16).toUpperCase().padStart(8, '0')}`;

            // Track category matches and their confidence scores
            const categoryMatches: Map<string, number> = new Map();
            let matchExplanations: string[] = [];
            let purposeType = ResourcePurposeType.UNKNOWN;

            // Step 1: Check resource type purpose mapping
            const resourceTypeMapping = getPurposeMappingForResourceType(resourceType);
            if (resourceTypeMapping) {
                categoryMatches.set(resourceTypeMapping.commonPurpose, resourceTypeMapping.baseConfidence);
                matchExplanations.push(`Resource type ${resourceTypeMapping.name} (${resourceTypeHex}) is associated with ${resourceTypeMapping.commonPurpose} purpose`);

                // Set initial purpose type from mapping
                purposeType = resourceTypeMapping.purposeType;
            }

            // Also check if resource type directly matches any category's common resource types
            for (const [id, category] of this.purposeCategories.entries()) {
                if (category.commonResourceTypes.includes(resourceTypeHex)) {
                    const baseScore = 20; // Base score for resource type match
                    categoryMatches.set(id, (categoryMatches.get(id) || 0) + baseScore);
                    matchExplanations.push(`Resource type ${resourceTypeHex} is associated with ${category.name} purpose`);
                }
            }

            // Step 2: Apply pattern-based analysis
            const patternMatch = this.patternBasedAnalyzer.getBestPatternMatch(resourceKey, resourceMetadata, resourceContent);
            if (patternMatch) {
                // Add pattern match to category matches
                const patternPurpose = patternMatch.purposeType;
                const patternConfidence = patternMatch.confidence;

                // Find the category that matches this purpose type
                for (const [purposeTypeKey, categories] of Object.entries(PURPOSE_TYPE_CATEGORIES)) {
                    if (purposeTypeKey === patternPurpose) {
                        // Add confidence to all categories of this purpose type
                        for (const category of categories) {
                            categoryMatches.set(category, (categoryMatches.get(category) || 0) + patternConfidence);
                        }
                        break;
                    }
                }

                // Add pattern match explanation
                matchExplanations.push(`Pattern match: ${patternMatch.patternName} (${patternMatch.confidence}% confidence)`);

                // Add evidence to explanations
                for (const evidence of patternMatch.evidence) {
                    matchExplanations.push(`- ${evidence}`);
                }

                // Update purpose type if pattern has high confidence
                if (patternConfidence >= 70) {
                    purposeType = patternPurpose;
                }
            }

            // Step 3: Check resource name/description for keyword matches
            const resourceName = resourceMetadata.name || '';
            const resourceDescription = resourceMetadata.description || '';
            const contentToCheck = `${resourceName} ${resourceDescription} ${resourceContent || ''}`.toLowerCase();

            for (const [id, category] of this.purposeCategories.entries()) {
                let keywordMatches = 0;
                const matchedKeywords: string[] = [];

                for (const keyword of category.keywords) {
                    if (contentToCheck.includes(keyword.toLowerCase())) {
                        keywordMatches++;
                        matchedKeywords.push(keyword);
                    }
                }

                if (keywordMatches > 0) {
                    // Score based on number of keyword matches and their specificity
                    const keywordScore = keywordMatches * 10;
                    categoryMatches.set(id, (categoryMatches.get(id) || 0) + keywordScore);
                    matchExplanations.push(`Found ${keywordMatches} keyword matches for ${category.name} purpose: ${matchedKeywords.join(', ')}`);
                }
            }

            // Step 4: Apply content semantic analysis if content is available
            if (resourceContent) {
                const contentAnalysis = this.contentSemanticAnalyzer.analyzeContent(resourceKey, resourceContent, resourceMetadata);

                // Add content analysis results to category matches
                if (contentAnalysis.entities.length > 0) {
                    // Map entity types to purpose categories
                    for (const entity of contentAnalysis.entities) {
                        switch (entity.type) {
                            case 'TRAIT':
                                categoryMatches.set('add_trait', (categoryMatches.get('add_trait') || 0) + entity.confidence * 0.3);
                                break;
                            case 'BUFF':
                                categoryMatches.set('add_buff', (categoryMatches.get('add_buff') || 0) + entity.confidence * 0.3);
                                break;
                            case 'INTERACTION':
                                categoryMatches.set('add_interaction', (categoryMatches.get('add_interaction') || 0) + entity.confidence * 0.3);
                                break;
                            case 'CAREER':
                                categoryMatches.set('add_career', (categoryMatches.get('add_career') || 0) + entity.confidence * 0.3);
                                break;
                            case 'SKILL':
                                categoryMatches.set('add_skill', (categoryMatches.get('add_skill') || 0) + entity.confidence * 0.3);
                                break;
                            case 'ASPIRATION':
                                categoryMatches.set('add_aspiration', (categoryMatches.get('add_aspiration') || 0) + entity.confidence * 0.3);
                                break;
                            case 'OBJECT':
                                categoryMatches.set('add_object', (categoryMatches.get('add_object') || 0) + entity.confidence * 0.3);
                                break;
                            case 'SCRIPT_FUNCTION':
                            case 'SCRIPT_CLASS':
                            case 'SCRIPT_MODULE':
                                categoryMatches.set('script_utility', (categoryMatches.get('script_utility') || 0) + entity.confidence * 0.3);
                                break;
                        }
                    }

                    // Add content analysis explanation
                    matchExplanations.push(`Content analysis found ${contentAnalysis.entities.length} entities: ${contentAnalysis.entities.map(e => e.type).join(', ')}`);
                }

                // Add key phrases to explanations
                if (contentAnalysis.keyPhrases.length > 0) {
                    matchExplanations.push(`Key phrases: ${contentAnalysis.keyPhrases.slice(0, 5).join(', ')}${contentAnalysis.keyPhrases.length > 5 ? '...' : ''}`);
                }

                // Add main topics to explanations
                if (contentAnalysis.mainTopics.length > 0) {
                    matchExplanations.push(`Main topics: ${contentAnalysis.mainTopics.join(', ')}`);
                }

                // Add content summary to explanations
                if (contentAnalysis.summary) {
                    matchExplanations.push(`Content summary: ${contentAnalysis.summary}`);
                }
            }

            // Step 5: Apply specialized analyzers based on resource type
            if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_TUNING && resourceContent) {
                // Use the specialized tuning analyzer
                const tuningAnalysis = this.tuningPurposeAnalyzer.analyzeTuningPurpose(
                    resourceKey,
                    resourceContent,
                    resourceMetadata
                );

                if (tuningAnalysis.confidence > 0) {
                    // Update purpose type
                    purposeType = tuningAnalysis.purposeType;

                    // Add tuning purpose to category matches
                    categoryMatches.set(tuningAnalysis.tuningPurpose, (categoryMatches.get(tuningAnalysis.tuningPurpose) || 0) + tuningAnalysis.confidence);

                    // Add explanations
                    matchExplanations.push(`Tuning analysis: ${tuningAnalysis.tuningPurpose} (${tuningAnalysis.confidence}% confidence)`);
                    for (const explanation of tuningAnalysis.explanation) {
                        matchExplanations.push(`- ${explanation}`);
                    }

                    // Add key elements to explanations
                    if (tuningAnalysis.keyElements.length > 0) {
                        matchExplanations.push(`Key elements: ${tuningAnalysis.keyElements.join(', ')}`);
                    }

                    // Add critical attributes to explanations
                    if (tuningAnalysis.criticalAttributes.length > 0) {
                        matchExplanations.push(`Critical attributes: ${tuningAnalysis.criticalAttributes.map(attr => `${attr.element}.${attr.attribute}=${attr.value}`).join(', ')}`);
                    }

                    // Add gameplay system if available
                    if (tuningAnalysis.gameplaySystem) {
                        matchExplanations.push(`Related gameplay system: ${tuningAnalysis.gameplaySystem}`);
                    }
                } else {
                    // Fall back to basic analysis
                    // For tuning XML, check if it's adding or modifying content
                    const isOverride = resourceMetadata.isOverride || false;

                    if (isOverride) {
                        // This is modifying existing content
                        purposeType = ResourcePurposeType.MODIFIES_CONTENT;

                        // Boost scores for modification categories
                        for (const categoryId of PURPOSE_TYPE_CATEGORIES[ResourcePurposeType.MODIFIES_CONTENT]) {
                            categoryMatches.set(categoryId, (categoryMatches.get(categoryId) || 0) + 30);
                        }

                        matchExplanations.push('Resource is an override, likely modifying existing content');
                    } else {
                        // This is likely adding new content
                        purposeType = ResourcePurposeType.ADDS_CONTENT;

                        // Boost scores for addition categories
                        for (const categoryId of PURPOSE_TYPE_CATEGORIES[ResourcePurposeType.ADDS_CONTENT]) {
                            categoryMatches.set(categoryId, (categoryMatches.get(categoryId) || 0) + 30);
                        }

                        matchExplanations.push('Resource is not an override, likely adding new content');
                    }

                    // Check tuning type for more specific categorization
                    const tuningType = resourceMetadata.tuningType || '';

                    if (tuningType) {
                        // Map tuning types to purpose categories
                        const tuningTypeMappings: Record<string, string[]> = {
                            'trait': ['add_trait', 'modify_trait'],
                            'buff': ['add_buff', 'modify_buff'],
                            'interaction': ['add_interaction', 'modify_interaction'],
                            'object': ['add_object', 'modify_object'],
                            'career': ['add_career'],
                            'skill': ['add_skill'],
                            'aspiration': ['add_aspiration']
                        };

                        for (const [type, categories] of Object.entries(tuningTypeMappings)) {
                            if (tuningType.toLowerCase().includes(type)) {
                                for (const categoryId of categories) {
                                    categoryMatches.set(categoryId, (categoryMatches.get(categoryId) || 0) + 40);
                                }
                                matchExplanations.push(`Tuning type "${tuningType}" matches purpose categories: ${categories.join(', ')}`);
                            }
                        }
                    }
                }
            } else if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_SIMDATA) {
                // Use the specialized SimData analyzer
                const simDataBuffer = Buffer.from(resourceContent || '', 'utf8');
                const simDataAnalysis = this.simDataPurposeAnalyzer.analyzeSimDataPurpose(
                    resourceKey,
                    simDataBuffer,
                    resourceMetadata
                );

                if (simDataAnalysis.confidence > 0) {
                    // Update purpose type
                    purposeType = simDataAnalysis.purposeType;

                    // Add SimData purpose to category matches
                    categoryMatches.set(simDataAnalysis.schemaPurpose, (categoryMatches.get(simDataAnalysis.schemaPurpose) || 0) + simDataAnalysis.confidence);

                    // Add explanations
                    matchExplanations.push(`SimData analysis: ${simDataAnalysis.schemaPurpose} (${simDataAnalysis.confidence}% confidence)`);
                    for (const explanation of simDataAnalysis.explanation) {
                        matchExplanations.push(`- ${explanation}`);
                    }

                    // Add key columns to explanations
                    if (simDataAnalysis.keyColumns.length > 0) {
                        matchExplanations.push(`Key columns: ${simDataAnalysis.keyColumns.join(', ')}`);
                    }

                    // Add gameplay system if available
                    if (simDataAnalysis.gameplaySystem) {
                        matchExplanations.push(`Related gameplay system: ${simDataAnalysis.gameplaySystem}`);
                    }
                }
            } else if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_SCRIPT && resourceContent) {
                // Use the specialized script analyzer
                const scriptAnalysis = this.scriptPurposeAnalyzer.analyzeScriptPurpose(
                    resourceKey,
                    resourceContent,
                    resourceMetadata
                );

                if (scriptAnalysis.confidence > 0) {
                    // Update purpose type
                    purposeType = scriptAnalysis.purposeType;

                    // Add script purpose to category matches
                    categoryMatches.set(scriptAnalysis.scriptPurpose, (categoryMatches.get(scriptAnalysis.scriptPurpose) || 0) + scriptAnalysis.confidence);

                    // Add explanations
                    matchExplanations.push(`Script analysis: ${scriptAnalysis.scriptPurpose} (${scriptAnalysis.confidence}% confidence)`);
                    for (const explanation of scriptAnalysis.explanation) {
                        matchExplanations.push(`- ${explanation}`);
                    }

                    // Add key imports to explanations
                    if (scriptAnalysis.keyImports.length > 0) {
                        matchExplanations.push(`Key imports: ${scriptAnalysis.keyImports.slice(0, 5).join(', ')}${scriptAnalysis.keyImports.length > 5 ? '...' : ''}`);
                    }

                    // Add key classes to explanations
                    if (scriptAnalysis.keyClasses.length > 0) {
                        matchExplanations.push(`Key classes: ${scriptAnalysis.keyClasses.slice(0, 5).join(', ')}${scriptAnalysis.keyClasses.length > 5 ? '...' : ''}`);
                    }

                    // Add key functions to explanations
                    if (scriptAnalysis.keyFunctions.length > 0) {
                        matchExplanations.push(`Key functions: ${scriptAnalysis.keyFunctions.slice(0, 5).join(', ')}${scriptAnalysis.keyFunctions.length > 5 ? '...' : ''}`);
                    }

                    // Add gameplay system if available
                    if (scriptAnalysis.gameplaySystem) {
                        matchExplanations.push(`Related gameplay system: ${scriptAnalysis.gameplaySystem}`);
                    }
                } else {
                    // Fall back to basic analysis
                    // For scripts, check script metadata
                    const modType = resourceMetadata.modType || '';
                    const hasInjections = resourceMetadata.hasInjections || false;

                    if (hasInjections) {
                        // Scripts with injections are typically modifying existing content
                        purposeType = ResourcePurposeType.MODIFIES_CONTENT;
                        categoryMatches.set('script_gameplay', (categoryMatches.get('script_gameplay') || 0) + 40);
                        matchExplanations.push('Script contains injections, likely modifying existing gameplay');
                    } else if (modType.toLowerCase().includes('utility')) {
                        // Utility scripts
                        purposeType = ResourcePurposeType.UTILITY;
                        categoryMatches.set('script_utility', (categoryMatches.get('script_utility') || 0) + 40);
                        matchExplanations.push('Script appears to be a utility script');
                    } else if (modType.toLowerCase().includes('ui')) {
                        // UI scripts
                        purposeType = ResourcePurposeType.MODIFIES_CONTENT;
                        categoryMatches.set('script_ui', (categoryMatches.get('script_ui') || 0) + 40);
                        matchExplanations.push('Script appears to modify UI elements');
                    } else {
                        // Default to gameplay scripts
                        purposeType = ResourcePurposeType.ADDS_CONTENT;
                        categoryMatches.set('script_gameplay', (categoryMatches.get('script_gameplay') || 0) + 30);
                        matchExplanations.push('Script appears to add new gameplay functionality');
                    }
                }
            } else if ([ResourceTypes.RESOURCE_TYPE_DDS_IMAGE, ResourceTypes.RESOURCE_TYPE_PNG_IMAGE, ResourceTypes.RESOURCE_TYPE_RLE2_IMAGE, ResourceTypes.RESOURCE_TYPE_MODULAR_PART].includes(resourceKey.type)) {
                // For images and modular parts, check if they're replacing existing content
                purposeType = ResourcePurposeType.REPLACES_CONTENT;
                categoryMatches.set('replace_texture', (categoryMatches.get('replace_texture') || 0) + 40);
                matchExplanations.push('Resource is an image or modular part, likely replacing textures');
            } else if ([ResourceTypes.RESOURCE_TYPE_MODEL, ResourceTypes.RESOURCE_TYPE_MODEL_LOD].includes(resourceKey.type)) {
                // For models and geometry, check if they're replacing existing content
                purposeType = ResourcePurposeType.REPLACES_CONTENT;
                categoryMatches.set('replace_mesh', (categoryMatches.get('replace_mesh') || 0) + 40);
                matchExplanations.push('Resource is a model or geometry, likely replacing meshes');
            } else if ([ResourceTypes.RESOURCE_TYPE_ANIMATION, ResourceTypes.RESOURCE_TYPE_ANIMATION_STATE_MACHINE].includes(resourceKey.type)) {
                // For animations, check if they're replacing existing content
                purposeType = ResourcePurposeType.REPLACES_CONTENT;
                categoryMatches.set('replace_animation', (categoryMatches.get('replace_animation') || 0) + 40);
                matchExplanations.push('Resource is an animation, likely replacing animations');
            }

            // Step 6: Apply context-aware analysis if resourceId is valid
            if (resourceId) {
                try {
                    // Build resource context
                    const resourceContext = await this.contextAwareAnalyzer.buildResourceContext(
                        resourceId,
                        resourceKey,
                        resourceMetadata,
                        resourceContent
                    );

                    // Check for relationships that indicate purpose
                    if (resourceContext.relatedResources.size > 0) {
                        // Check for dependencies
                        if (resourceContext.relatedResources.has(ResourceRelationshipType.DEPENDS_ON)) {
                            matchExplanations.push(`Resource depends on ${resourceContext.relatedResources.get(ResourceRelationshipType.DEPENDS_ON)?.length} other resources`);
                        }

                        // Check for references
                        if (resourceContext.relatedResources.has(ResourceRelationshipType.REFERENCED_BY)) {
                            matchExplanations.push(`Resource is referenced by ${resourceContext.relatedResources.get(ResourceRelationshipType.REFERENCED_BY)?.length} other resources`);
                        }

                        // Check for overrides
                        if (resourceContext.relatedResources.has(ResourceRelationshipType.OVERRIDES)) {
                            const overrideCount = resourceContext.relatedResources.get(ResourceRelationshipType.OVERRIDES)?.length || 0;
                            categoryMatches.set('modify_behavior', (categoryMatches.get('modify_behavior') || 0) + overrideCount * 10);
                            matchExplanations.push(`Resource overrides ${overrideCount} other resources`);
                            purposeType = ResourcePurposeType.MODIFIES_CONTENT;
                        }

                        // Check for extensions
                        if (resourceContext.relatedResources.has(ResourceRelationshipType.EXTENDS)) {
                            const extendCount = resourceContext.relatedResources.get(ResourceRelationshipType.EXTENDS)?.length || 0;
                            categoryMatches.set('extend_functionality', (categoryMatches.get('extend_functionality') || 0) + extendCount * 10);
                            matchExplanations.push(`Resource extends ${extendCount} other resources`);
                            purposeType = ResourcePurposeType.EXTENDS_CONTENT;
                        }
                    }

                    // Check package context
                    if (resourceContext.packageContext.packageId) {
                        // Check resource type distribution
                        const distribution = resourceContext.packageContext.resourceTypeDistribution;

                        // If this is the only resource of its type in the package, it's more likely to be important
                        if (distribution[resourceType] === 1) {
                            // Boost confidence by 10%
                            for (const [category, confidence] of categoryMatches.entries()) {
                                categoryMatches.set(category, confidence * 1.1);
                            }

                            matchExplanations.push('Resource is the only one of its type in the package');
                        }

                        // If this is one of many resources of the same type, it's part of a larger system
                        if (distribution[resourceType] > 5) {
                            matchExplanations.push(`Resource is one of ${distribution[resourceType]} resources of the same type in the package`);
                        }
                    }
                } catch (contextError) {
                    this.logger.error(`Error in context-aware analysis for resource ${resourceId}:`, contextError);
                    // Continue with other analysis methods
                }
            }

            // Convert matches to sorted array
            const sortedMatches = Array.from(categoryMatches.entries())
                .sort((a, b) => b[1] - a[1]);

            // Default purpose analysis
            let purposeAnalysis: ResourcePurposeAnalysis = {
                resourceId,
                primaryPurpose: 'unknown',
                purposeType,
                confidence: 0,
                description: 'Unknown purpose',
                timestamp: Date.now()
            };

            // If we have matches, update the purpose analysis
            if (sortedMatches.length > 0) {
                const [primaryCategoryId, primaryScore] = sortedMatches[0];
                const primaryCategory = this.purposeCategories.get(primaryCategoryId);

                if (primaryCategory) {
                    // Normalize confidence to 0-100 scale
                    const normalizedConfidence = Math.min(100, primaryScore);

                    purposeAnalysis.primaryPurpose = primaryCategoryId;
                    purposeAnalysis.confidence = normalizedConfidence;
                    purposeAnalysis.description = `${primaryCategory.description} (${matchExplanations.join('. ')})`;

                    // Get gameplay system categorization
                    const systemCategorization = await this.gameplaySystemRegistry.categorizeResource(
                        resourceKey,
                        resourceMetadata,
                        resourceContent
                    );

                    if (systemCategorization.primaryConfidence > 30) {
                        purposeAnalysis.gameplaySystem = systemCategorization.primarySystem;
                    }

                    // Add dependency chain analysis if resourceId is valid
                    let dependencyChainAnalysis = null;
                    if (resourceId) {
                        try {
                            // Analyze dependency chain
                            dependencyChainAnalysis = await this.dependencyChainAnalyzer.analyzeDependencyChain(resourceId);
                        } catch (error) {
                            this.logger.error(`Error analyzing dependency chain for resource ${resourceId}:`, error);
                        }
                    }

                    // Add additional details
                    purposeAnalysis.details = {
                        categoryName: primaryCategory.name,
                        matchExplanations,
                        secondaryPurposes: sortedMatches
                            .slice(1, 3) // Take up to 2 secondary purposes
                            .map(([id, score]) => ({
                                id,
                                name: this.purposeCategories.get(id)?.name || id,
                                confidence: Math.min(100, score)
                            })),
                        enhancedAnalysis: {
                            usedPatternAnalysis: !!patternMatch,
                            usedContentAnalysis: resourceContent ? true : false,
                            usedContextAnalysis: resourceId ? true : false,
                            usedSpecializedAnalyzers: (
                                (resourceKey.type === ResourceTypes.RESOURCE_TYPE_TUNING && resourceContent) ||
                                (resourceKey.type === ResourceTypes.RESOURCE_TYPE_SIMDATA) ||
                                (resourceKey.type === ResourceTypes.RESOURCE_TYPE_SCRIPT && resourceContent)
                            ),
                            patternName: patternMatch?.patternName,
                            patternConfidence: patternMatch?.confidence,
                            resourceTypeMapping: resourceTypeMapping ? {
                                name: resourceTypeMapping.name,
                                commonPurpose: resourceTypeMapping.commonPurpose,
                                purposeType: resourceTypeMapping.purposeType
                            } : undefined,
                            dependencyChain: dependencyChainAnalysis
                        }
                    };
                }
            }

            // Save to database
            const saveResult = await this.semanticDatabase.saveResourcePurposeAnalysis(purposeAnalysis);
            if (saveResult === -1) {
                this.logger.warn(`Failed to save purpose analysis for resource ${resourceId}`);
            }

            return purposeAnalysis;
        } catch (error) {
            this.logger.error(`Error analyzing purpose for resource ${resourceId}:`, error);

            // Return default analysis on error
            return {
                resourceId,
                primaryPurpose: 'unknown',
                purposeType: ResourcePurposeType.UNKNOWN,
                confidence: 0,
                description: 'Error analyzing resource purpose',
                timestamp: Date.now()
            };
        }
    }

    /**
     * Get resource purpose analysis for a resource
     * @param resourceId The resource ID
     * @returns The resource purpose analysis or undefined if not found
     */
    public async getResourcePurposeAnalysis(resourceId: number): Promise<ResourcePurposeAnalysis | undefined> {
        await this.initialize();
        return this.semanticDatabase.getResourcePurposeAnalysis(resourceId);
    }

    /**
     * Get all purpose categories
     * @returns Array of all purpose categories
     */
    public async getAllPurposeCategories(): Promise<any[]> {
        await this.initialize();
        return Array.from(this.purposeCategories.values());
    }

    /**
     * Get all purposes (alias for getAllPurposeCategories)
     * @returns Array of all purpose categories
     */
    public async getAllPurposes(): Promise<any[]> {
        return this.getAllPurposeCategories();
    }

    /**
     * Get purpose categories by type
     * @param purposeType The purpose type to filter by
     * @returns Array of purpose categories of the specified type
     */
    public async getPurposeCategoriesByType(purposeType: ResourcePurposeType): Promise<any[]> {
        await this.initialize();

        const categoryIds = PURPOSE_TYPE_CATEGORIES[purposeType] || [];
        return categoryIds.map(id => this.purposeCategories.get(id)).filter(Boolean);
    }

    /**
     * Analyze a resource to determine its gameplay system
     * @param resourceKey The resource key
     * @param resourceId The resource ID in the database
     * @param resourceMetadata The resource metadata
     * @param resourceContent Optional resource content for deeper analysis
     * @param purposeAnalysis Optional purpose analysis result
     * @returns Gameplay system categorization result
     */
    public async analyzeGameplaySystem(
        resourceKey: ResourceKey,
        resourceId: number,
        resourceMetadata: Record<string, any>,
        resourceContent?: string,
        purposeAnalysis?: ResourcePurposeAnalysis
    ): Promise<GameplaySystemCategorization> {
        await this.initialize();

        try {
            // Get purpose analysis if not provided
            if (!purposeAnalysis && resourceId) {
                const existingPurpose = await this.semanticDatabase.getResourcePurposeAnalysis(resourceId);
                if (existingPurpose) {
                    purposeAnalysis = existingPurpose;
                }
            }

            // Use content semantic analysis if available
            let contentAnalysis = null;
            if (resourceContent) {
                contentAnalysis = this.contentSemanticAnalyzer.analyzeContent(
                    resourceKey,
                    resourceContent,
                    resourceMetadata
                );
            }

            // Use SimData semantic analysis for SimData resources
            if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_SIMDATA && resourceContent) {
                try {
                    const SimDataSemanticAnalyzer = (await import('./simDataSemanticAnalyzer.js')).SimDataSemanticAnalyzer;
                    const simDataAnalyzer = new SimDataSemanticAnalyzer();
                    const buffer = Buffer.from(resourceContent);
                    contentAnalysis = simDataAnalyzer.analyzeSimData(resourceKey, buffer, resourceMetadata);
                } catch (error) {
                    this.logger.warn('Failed to use SimDataSemanticAnalyzer:', error);
                }
            }

            // Use the gameplay system registry to categorize the resource
            const systemCategorization = await this.gameplaySystemRegistry.categorizeResource(
                resourceKey,
                resourceMetadata,
                resourceContent
            );

            // Set confidence alias for compatibility
            systemCategorization.confidence = systemCategorization.primaryConfidence;

            // Enhance categorization with purpose analysis
            if (purposeAnalysis) {
                // Check if purpose provides clues about gameplay system
                const purposeSystem = this.getPurposeRelatedSystem(purposeAnalysis.primaryPurpose);
                if (purposeSystem && (systemCategorization.confidence || 0) < 70) {
                    systemCategorization.primarySystem = purposeSystem;
                    systemCategorization.primaryConfidence = Math.max(systemCategorization.primaryConfidence, 70);
                    systemCategorization.confidence = systemCategorization.primaryConfidence;
                    systemCategorization.description = `System determined from purpose analysis: ${purposeAnalysis.primaryPurpose}`;
                }
            }

            // Enhance categorization with content analysis
            if (contentAnalysis) {
                // Check if content analysis provides clues about gameplay system
                for (const topic of contentAnalysis.mainTopics) {
                    const system = this.getTopicRelatedSystem(topic);
                    if (system && (systemCategorization.confidence || 0) < 80) {
                        systemCategorization.primarySystem = system;
                        systemCategorization.primaryConfidence = Math.max(systemCategorization.primaryConfidence, 80);
                        systemCategorization.confidence = systemCategorization.primaryConfidence;
                        systemCategorization.description = `System determined from content analysis: ${topic}`;
                        break;
                    }
                }

                // Check entities for gameplay system clues
                for (const entity of contentAnalysis.entities) {
                    const system = this.getEntityTypeRelatedSystem(entity.type);
                    if (system && (systemCategorization.confidence || 0) < 85) {
                        systemCategorization.primarySystem = system;
                        systemCategorization.primaryConfidence = Math.max(systemCategorization.primaryConfidence, 85);
                        systemCategorization.confidence = systemCategorization.primaryConfidence;
                        systemCategorization.description = `System determined from entity type: ${entity.type}`;
                        break;
                    }
                }
            }

            // Add enhanced analysis details
            systemCategorization.details = {
                ...systemCategorization.details,
                enhancedAnalysis: {
                    usedPurposeAnalysis: !!purposeAnalysis,
                    usedContentAnalysis: !!contentAnalysis,
                    purposeType: purposeAnalysis?.purposeType,
                    contentTopics: contentAnalysis?.mainTopics || []
                }
            };

            return systemCategorization;
        } catch (error) {
            this.logger.error(`Error analyzing gameplay system for resource ${resourceId}:`, error);

            // Return default categorization on error
            return {
                resourceId,
                primarySystem: 'unknown',
                primaryConfidence: 0,
                confidence: 0,
                description: 'Error analyzing gameplay system',
                timestamp: Date.now()
            };
        }
    }

    /**
     * Get gameplay system related to purpose
     * @param purpose Resource purpose
     * @returns Related gameplay system or undefined
     */
    private getPurposeRelatedSystem(purpose: string): string | undefined {
        // Map purposes to gameplay systems
        const purposeSystemMap: Record<string, string> = {
            'add_trait': 'traits',
            'modify_trait': 'traits',
            'add_buff': 'emotions',
            'modify_buff': 'emotions',
            'add_interaction': 'interactions',
            'modify_interaction': 'interactions',
            'add_career': 'careers',
            'modify_career': 'careers',
            'add_skill': 'skills',
            'modify_skill': 'skills',
            'add_aspiration': 'aspirations',
            'modify_aspiration': 'aspirations',
            'add_object': 'objects',
            'modify_object': 'objects',
            'add_cas_item': 'cas',
            'modify_cas_item': 'cas',
            'add_world': 'worlds',
            'modify_world': 'worlds',
            'add_ui': 'ui',
            'modify_ui': 'ui'
        };

        return purposeSystemMap[purpose];
    }

    /**
     * Get gameplay system related to content topic
     * @param topic Content topic
     * @returns Related gameplay system or undefined
     */
    private getTopicRelatedSystem(topic: string): string | undefined {
        // Convert topic to lowercase for case-insensitive matching
        const lowerTopic = topic.toLowerCase();

        // Check for direct matches
        if (lowerTopic.includes('trait')) return 'traits';
        if (lowerTopic.includes('buff')) return 'emotions';
        if (lowerTopic.includes('emotion')) return 'emotions';
        if (lowerTopic.includes('mood')) return 'emotions';
        if (lowerTopic.includes('interaction')) return 'interactions';
        if (lowerTopic.includes('career')) return 'careers';
        if (lowerTopic.includes('job')) return 'careers';
        if (lowerTopic.includes('skill')) return 'skills';
        if (lowerTopic.includes('aspiration')) return 'aspirations';
        if (lowerTopic.includes('object')) return 'objects';
        if (lowerTopic.includes('furniture')) return 'objects';
        if (lowerTopic.includes('cas')) return 'cas';
        if (lowerTopic.includes('clothing')) return 'cas';
        if (lowerTopic.includes('hair')) return 'cas';
        if (lowerTopic.includes('world')) return 'worlds';
        if (lowerTopic.includes('lot')) return 'worlds';
        if (lowerTopic.includes('ui')) return 'ui';
        if (lowerTopic.includes('interface')) return 'ui';
        if (lowerTopic.includes('need')) return 'needs';
        if (lowerTopic.includes('motive')) return 'needs';
        if (lowerTopic.includes('relationship')) return 'relationships';
        if (lowerTopic.includes('pet')) return 'pets';
        if (lowerTopic.includes('animal')) return 'pets';
        if (lowerTopic.includes('university')) return 'university';
        if (lowerTopic.includes('college')) return 'university';
        if (lowerTopic.includes('eco')) return 'eco';
        if (lowerTopic.includes('environment')) return 'eco';
        if (lowerTopic.includes('season')) return 'seasons';
        if (lowerTopic.includes('weather')) return 'seasons';

        return undefined;
    }

    /**
     * Get gameplay system related to entity type
     * @param entityType Entity type
     * @returns Related gameplay system or undefined
     */
    private getEntityTypeRelatedSystem(entityType: string): string | undefined {
        // Map entity types to gameplay systems
        const entitySystemMap: Record<string, string> = {
            'TRAIT': 'traits',
            'BUFF': 'emotions',
            'INTERACTION': 'interactions',
            'CAREER': 'careers',
            'SKILL': 'skills',
            'ASPIRATION': 'aspirations',
            'OBJECT': 'objects',
            'UI_ELEMENT': 'ui',
            'ANIMATION': 'animations',
            'VISUAL_EFFECT': 'vfx',
            'SOUND_EFFECT': 'audio'
        };

        return entitySystemMap[entityType];
    }

    /**
     * Get purpose category by ID
     * @param categoryId The category ID
     * @returns The purpose category or undefined if not found
     */
    public getPurposeCategory(categoryId: string): any {
        return this.purposeCategories.get(categoryId);
    }

    /**
     * Analyze dependency chain for a resource
     * @param resourceId Resource ID
     * @param maxDepth Maximum depth to analyze
     * @returns Dependency chain analysis
     */
    public async analyzeDependencyChain(
        resourceId: number,
        maxDepth: number = 3
    ): Promise<any> {
        await this.initialize();

        try {
            // Analyze forward dependencies (resources this resource depends on)
            const forwardChain = await this.dependencyChainAnalyzer.analyzeDependencyChain(
                resourceId,
                maxDepth,
                'forward'
            );

            // Analyze backward dependencies (resources that depend on this resource)
            const backwardChain = await this.dependencyChainAnalyzer.analyzeDependencyChain(
                resourceId,
                maxDepth,
                'backward'
            );

            // Calculate combined impact score
            const combinedImpactScore = Math.min(100, (forwardChain.impactScore + backwardChain.impactScore) / 2);

            // Return combined analysis
            return {
                forwardChain: {
                    totalNodes: forwardChain.totalNodes,
                    maxDepth: forwardChain.maxDepth,
                    impactScore: forwardChain.impactScore,
                    criticalPathsCount: forwardChain.criticalPaths.length
                },
                backwardChain: {
                    totalNodes: backwardChain.totalNodes,
                    maxDepth: backwardChain.maxDepth,
                    impactScore: backwardChain.impactScore,
                    criticalPathsCount: backwardChain.criticalPaths.length
                },
                combinedImpactScore,
                dependencyType: this.getDependencyType(forwardChain, backwardChain)
            };
        } catch (error) {
            this.logger.error(`Error analyzing dependency chain for resource ${resourceId}:`, error);
            return null;
        }
    }

    /**
     * Get dependency type based on forward and backward chains
     * @param forwardChain Forward dependency chain
     * @param backwardChain Backward dependency chain
     * @returns Dependency type
     */
    private getDependencyType(forwardChain: any, backwardChain: any): string {
        // If resource has many dependencies but few dependents, it's a consumer
        if (forwardChain.totalNodes > 5 && backwardChain.totalNodes < 2) {
            return 'CONSUMER';
        }

        // If resource has few dependencies but many dependents, it's a provider
        if (forwardChain.totalNodes < 2 && backwardChain.totalNodes > 5) {
            return 'PROVIDER';
        }

        // If resource has many dependencies and many dependents, it's a hub
        if (forwardChain.totalNodes > 5 && backwardChain.totalNodes > 5) {
            return 'HUB';
        }

        // If resource has few dependencies and few dependents, it's isolated
        if (forwardChain.totalNodes < 2 && backwardChain.totalNodes < 2) {
            return 'ISOLATED';
        }

        // Default to balanced
        return 'BALANCED';
    }

    /**
     * Dispose of resources used by the analyzer
     */
    public async dispose(): Promise<void> {
        try {
            this.logger.info('Disposing ResourcePurposeAnalyzer resources');

            // Dispose of the context-aware analyzer
            if (this.contextAwareAnalyzer) {
                await this.contextAwareAnalyzer.dispose();
            }

            // Dispose of the dependency chain analyzer
            if (this.dependencyChainAnalyzer) {
                await this.dependencyChainAnalyzer.dispose();
            }

            // Clear purpose categories
            this.purposeCategories.clear();

            this.initialized = false;
            this.logger.info('ResourcePurposeAnalyzer resources disposed successfully');
        } catch (error) {
            this.logger.error('Error disposing ResourcePurposeAnalyzer resources:', error);
            throw error;
        }
    }
}
