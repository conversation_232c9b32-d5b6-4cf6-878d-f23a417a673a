/**
 * Bytecode Parser Base
 * 
 * This module provides a base class for bytecode parsers.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable } from '../../../di/decorators.js';
import { CodeObject, BytecodeHeader, BytecodeInstruction } from './types.js';

/**
 * Base class for bytecode parsers
 */
@injectable()
export class BytecodeParserBase {
    protected logger: Logger;

    /**
     * Constructor
     */
    constructor() {
        this.logger = new Logger('BytecodeParserBase');
    }

    /**
     * Parse bytecode
     * @param buffer Bytecode buffer
     * @param filename Filename
     * @returns Parsed code object
     */
    public parseBytecode(buffer: Buffer, filename: string): CodeObject | null {
        this.logger.debug(`Parsing bytecode for ${filename}`);
        return null;
    }

    /**
     * Parse non-standard bytecode
     * @param buffer Bytecode buffer
     * @param filename Filename
     * @returns Parsed code object
     */
    public parseNonStandardBytecode(buffer: Buffer, filename: string): CodeObject | null {
        this.logger.debug(`Parsing non-standard bytecode for ${filename}`);
        return null;
    }

    /**
     * Check if a code object is valid
     * @param codeObject Code object to check
     * @returns True if the code object is valid
     */
    protected isValidCodeObject(codeObject: CodeObject): boolean {
        // Check for minimum required properties
        if (!codeObject.bytecode || !codeObject.constants || !codeObject.names) {
            return false;
        }

        // Check for reasonable values
        if (codeObject.bytecode.length === 0) {
            return false;
        }

        // Check for reasonable stack size
        if (codeObject.stackSize < 0 || codeObject.stackSize > 1000) {
            return false;
        }

        return true;
    }

    /**
     * Find potential code object markers in a buffer
     * @param buffer Buffer to search
     * @returns Array of positions where code object markers were found
     */
    protected findCodeObjectMarkers(buffer: Buffer): number[] {
        const positions: number[] = [];

        // Common code object markers (type codes for code objects in Python marshalled data)
        const markers = [0x63, 0x43]; // 'c' and 'C' in ASCII

        // Search for markers
        for (let i = 0; i < buffer.length - 8; i++) {
            if (markers.includes(buffer[i])) {
                // Check if followed by reasonable values for a code object
                if (i + 4 < buffer.length) {
                    const argCount = buffer.readUInt32LE(i + 1);
                    if (argCount < 256) { // Reasonable max number of arguments
                        positions.push(i);
                    }
                }
            }
        }

        return positions;
    }

    /**
     * Extract potential Python identifiers from a buffer
     * @param buffer Buffer to extract from
     * @returns Array of potential Python identifiers
     */
    protected extractPythonIdentifiers(buffer: Buffer): string[] {
        const identifiers: string[] = [];
        let currentIdentifier = '';

        // Common Python keywords and built-ins to help identify Python code
        const pythonKeywords = new Set([
            'import', 'from', 'class', 'def', 'return', 'if', 'else', 'elif', 'for', 'while',
            'try', 'except', 'finally', 'with', 'as', 'lambda', 'yield', 'global', 'nonlocal',
            'True', 'False', 'None', 'and', 'or', 'not', 'is', 'in', 'self', 'super',
            'sims4', 'services', 'objects', 'interactions', 'buffs', 'traits', 'tuning'
        ]);

        // Scan buffer for ASCII characters that could be Python identifiers
        for (let i = 0; i < buffer.length; i++) {
            const char = buffer[i];

            // Check if character is a valid Python identifier character
            if ((char >= 65 && char <= 90) || // A-Z
                (char >= 97 && char <= 122) || // a-z
                (char === 95) || // underscore
                (currentIdentifier.length > 0 && char >= 48 && char <= 57)) { // 0-9 (not first char)

                currentIdentifier += String.fromCharCode(char);
            } else if (currentIdentifier.length > 0) {
                // End of identifier
                if (currentIdentifier.length >= 2) { // Ignore single-character identifiers
                    // Prioritize known Python keywords
                    if (pythonKeywords.has(currentIdentifier)) {
                        identifiers.push(currentIdentifier);
                    } else if (/^[A-Za-z_][A-Za-z0-9_]*$/.test(currentIdentifier)) {
                        identifiers.push(currentIdentifier);
                    }
                }
                currentIdentifier = '';
            }
        }

        // Add final identifier if there is one
        if (currentIdentifier.length >= 2) {
            if (pythonKeywords.has(currentIdentifier) || /^[A-Za-z_][A-Za-z0-9_]*$/.test(currentIdentifier)) {
                identifiers.push(currentIdentifier);
            }
        }

        // Return unique identifiers
        return [...new Set(identifiers)];
    }

    /**
     * Get Python version object from version string
     * @param versionString Python version string (e.g., '3.7.0')
     * @returns Python version object or null if not recognized
     */
    protected getPythonVersionObject(versionString: string): { major: number, minor: number, micro: number } | null {
        try {
            // Check if version string is in the format 'x.y.z'
            const match = versionString.match(/^(\d+)\.(\d+)\.(\d+)$/);
            if (match) {
                const major = parseInt(match[1], 10);
                const minor = parseInt(match[2], 10);
                const micro = parseInt(match[3], 10);

                return { major, minor, micro };
            }

            // Check if version string is in the format 'x.y'
            const shortMatch = versionString.match(/^(\d+)\.(\d+)$/);
            if (shortMatch) {
                const major = parseInt(shortMatch[1], 10);
                const minor = parseInt(shortMatch[2], 10);

                return { major, minor, micro: 0 };
            }

            // Check if version string contains 'Python' (e.g., 'Python 3.7')
            const pythonMatch = versionString.match(/Python\s+(\d+)\.(\d+)/i);
            if (pythonMatch) {
                const major = parseInt(pythonMatch[1], 10);
                const minor = parseInt(pythonMatch[2], 10);

                return { major, minor, micro: 0 };
            }

            // Check if version string is 'unknown' or empty
            if (versionString === 'unknown' || !versionString) {
                // Default to Python 3.7 (most common in Sims 4)
                return { major: 3, minor: 7, micro: 0 };
            }

            return null;
        } catch (error) {
            this.logger.error(`Error parsing Python version string: ${versionString}`, error);
            return null;
        }
    }

    /**
     * Parse bytecode with fallback strategies
     * @param buffer Bytecode buffer
     * @param filename Filename
     * @returns Parsed code object and confidence level
     */
    public parseBytecodeWithFallback(buffer: Buffer, filename: string): { codeObject: CodeObject | null, confidence: number } {
        try {
            // Try standard parsing
            const codeObject = this.parseBytecode(buffer, filename);
            if (codeObject) {
                // Determine confidence level based on available information
                let confidence = 0.5;

                if (codeObject.constants.length > 0) confidence += 0.1;
                if (codeObject.names.length > 0) confidence += 0.1;
                if (codeObject.bytecode.length > 0) confidence += 0.2;
                if (codeObject.nestedCodeObjects.length > 0) confidence += 0.1;

                return { codeObject, confidence };
            }

            return { codeObject: null, confidence: 0 };
        } catch (error) {
            this.logger.error(`Error in parseBytecodeWithFallback for ${filename}:`, error);
            return { codeObject: null, confidence: 0 };
        }
    }

    /**
     * Disassemble bytecode
     * @param codeObject Code object
     * @returns Bytecode instructions
     */
    public disassemble(codeObject: CodeObject): BytecodeInstruction[] {
        this.logger.debug(`Disassembling bytecode for ${codeObject.name}`);
        return [];
    }

    /**
     * Extract imports from bytecode
     * @param codeObject Code object
     * @returns Array of import statements
     */
    public extractImports(codeObject: CodeObject): string[] {
        this.logger.debug(`Extracting imports from ${codeObject.name}`);
        return [];
    }

    /**
     * Extract classes from bytecode
     * @param codeObject Code object
     * @returns Array of class names
     */
    public extractClasses(codeObject: CodeObject): string[] {
        this.logger.debug(`Extracting classes from ${codeObject.name}`);
        return [];
    }

    /**
     * Extract functions from bytecode
     * @param codeObject Code object
     * @returns Array of function names
     */
    public extractFunctions(codeObject: CodeObject): string[] {
        this.logger.debug(`Extracting functions from ${codeObject.name}`);
        return [];
    }

    /**
     * Get Python version from bytecode
     * @param buffer Bytecode buffer
     * @returns Python version string
     */
    public getPythonVersion(buffer: Buffer): string {
        this.logger.debug(`Getting Python version from bytecode`);
        return 'Unknown';
    }
}
