import { ResourceKey as AppR<PERSON>ource<PERSON>ey, ResourceMetadata } from '../../../types/resource/interfaces.js';
import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { SimDataExtractionService } from './simdata/simDataExtractionService.js';

/**
 * Class for extracting metadata from SimData resources
 * @deprecated Use SimDataExtractionService directly instead
 */
export class SimDataMetadataExtractor {
    private logger: Logger;
    private extractionService: SimDataExtractionService;

    constructor() {
        this.logger = new Logger('SimDataMetadataExtractor');
        this.extractionService = new SimDataExtractionService(this.logger);
    }

    /**
     * Extracts metadata specifically from SimData resources.
     * @param key The resource key.
     * @param buffer The resource buffer.
     * @param resourceId The internal resource ID.
     * @param databaseService The database service instance.
     * @returns A partial ResourceMetadata object for SimData resources.
     */
    public async extract(
        key: AppResource<PERSON><PERSON>,
        buffer: Buffer,
        resourceId: number,
        databaseService: DatabaseService
    ): Promise<Partial<ResourceMetadata>> {
        // Use the new extraction service
        return this.extractionService.extract(key, buffer, resourceId, databaseService);
    }
}

