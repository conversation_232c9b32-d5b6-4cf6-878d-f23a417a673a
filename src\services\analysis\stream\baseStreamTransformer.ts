/**
 * Base Stream Transformer
 * 
 * This module provides a base class for all stream transformers in the pipeline.
 * It extends Node.js Transform stream and provides common functionality for
 * error handling, progress reporting, and resource management.
 * 
 * Features:
 * - Error handling and recovery
 * - Progress reporting
 * - Resource tracking and cleanup
 * - State management
 * - Statistics collection
 */

import { Transform, TransformCallback, TransformOptions } from 'stream';
import { EventEmitter } from 'events';
import { Logger } from '../../../utils/logging/logger.js';
import EnhancedMemoryManager from '../../../utils/memory/enhancedMemoryManager.js';

// Create a logger for this module
const logger = new Logger('BaseStreamTransformer');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

/**
 * Stream transformer options
 */
export interface StreamTransformerOptions extends TransformOptions {
    name?: string;
    errorHandling?: 'strict' | 'lenient' | 'skip';
    emitProgress?: boolean;
    emitStats?: boolean;
    maxRetries?: number;
}

/**
 * Stream transformer progress event
 */
export interface StreamTransformerProgress {
    transformer: string;
    progress: number;
    bytesProcessed: number;
    chunkCount: number;
    timestamp: number;
}

/**
 * Stream transformer statistics
 */
export interface StreamTransformerStats {
    transformer: string;
    bytesProcessed: number;
    chunkCount: number;
    errorCount: number;
    retryCount: number;
    startTime: number;
    endTime: number;
    duration: number;
    memoryUsage: number;
}

/**
 * Stream transformer interface
 */
export interface IStreamTransformer extends Transform {
    /**
     * Set transformer options
     * @param options Transformer options
     */
    setOptions(options: StreamTransformerOptions): void;
    
    /**
     * Reset transformer state
     */
    reset(): void;
    
    /**
     * Get transformer name
     */
    getName(): string;
    
    /**
     * Get transformer statistics
     */
    getStats(): StreamTransformerStats;
    
    /**
     * Get current progress (0-1)
     */
    getProgress(): number;
    
    /**
     * Initialize transformer
     */
    initialize(): Promise<void>;
    
    /**
     * Clean up resources
     */
    cleanup(): Promise<void>;
    
    /**
     * Check if transformer can recover from error
     */
    canRecover(): boolean;
    
    /**
     * Recover from error
     */
    recoverFromError(): void;
}

/**
 * Base stream transformer abstract class
 */
export abstract class BaseStreamTransformer extends Transform implements IStreamTransformer {
    protected name: string;
    protected options: StreamTransformerOptions;
    protected chunkCount: number = 0;
    protected bytesProcessed: number = 0;
    protected errorCount: number = 0;
    protected retryCount: number = 0;
    protected startTime: number = 0;
    protected endTime: number = 0;
    protected initialized: boolean = false;
    protected canRecoverFromError: boolean = false;
    protected logger: Logger;
    
    /**
     * Create a new base stream transformer
     * @param name Transformer name
     * @param options Transformer options
     */
    constructor(name: string, options: StreamTransformerOptions = {}) {
        // Default transform options
        const defaultOptions: StreamTransformerOptions = {
            objectMode: false, // Work with buffers by default
            highWaterMark: 64 * 1024, // 64KB
            name: name,
            errorHandling: 'strict',
            emitProgress: true,
            emitStats: true,
            maxRetries: 3
        };
        
        // Merge default options with provided options
        super({ ...defaultOptions, ...options });
        
        this.name = name;
        this.options = { ...defaultOptions, ...options };
        this.logger = new Logger(`StreamTransformer:${name}`);
        
        // Set start time
        this.startTime = Date.now();
        
        this.logger.debug(`Created ${name} transformer`);
    }
    
    /**
     * Set transformer options
     * @param options Transformer options
     */
    public setOptions(options: StreamTransformerOptions): void {
        this.options = { ...this.options, ...options };
    }
    
    /**
     * Reset transformer state
     */
    public reset(): void {
        this.chunkCount = 0;
        this.bytesProcessed = 0;
        this.errorCount = 0;
        this.retryCount = 0;
        this.startTime = Date.now();
        this.endTime = 0;
        this.initialized = false;
        this.canRecoverFromError = false;
    }
    
    /**
     * Get transformer name
     */
    public getName(): string {
        return this.name;
    }
    
    /**
     * Get transformer statistics
     */
    public getStats(): StreamTransformerStats {
        return {
            transformer: this.name,
            bytesProcessed: this.bytesProcessed,
            chunkCount: this.chunkCount,
            errorCount: this.errorCount,
            retryCount: this.retryCount,
            startTime: this.startTime,
            endTime: this.endTime || Date.now(),
            duration: (this.endTime || Date.now()) - this.startTime,
            memoryUsage: process.memoryUsage().heapUsed
        };
    }
    
    /**
     * Get current progress (0-1)
     */
    public getProgress(): number {
        return this.calculateProgress();
    }
    
    /**
     * Initialize transformer
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }
        
        try {
            await this.initializeImpl();
            this.initialized = true;
        } catch (error: any) {
            this.logger.error(`Failed to initialize ${this.name} transformer: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * Clean up resources
     */
    public async cleanup(): Promise<void> {
        try {
            await this.cleanupImpl();
            
            // Set end time if not already set
            if (this.endTime === 0) {
                this.endTime = Date.now();
            }
            
            // Emit final stats if enabled
            if (this.options.emitStats) {
                this.emit('stats', this.getStats());
            }
            
            this.logger.debug(`Cleaned up ${this.name} transformer`);
        } catch (error: any) {
            this.logger.error(`Failed to clean up ${this.name} transformer: ${error.message}`);
        }
    }
    
    /**
     * Check if transformer can recover from error
     */
    public canRecover(): boolean {
        return this.canRecoverFromError;
    }
    
    /**
     * Recover from error
     */
    public recoverFromError(): void {
        if (!this.canRecoverFromError) {
            this.logger.warn(`${this.name} transformer cannot recover from error`);
            return;
        }
        
        try {
            this.recoverFromErrorImpl();
            this.logger.info(`${this.name} transformer recovered from error`);
        } catch (error: any) {
            this.logger.error(`Failed to recover ${this.name} transformer from error: ${error.message}`);
            this.canRecoverFromError = false;
        }
    }
    
    /**
     * Transform implementation
     * @param chunk Chunk to transform
     * @param encoding Chunk encoding
     * @param callback Callback function
     */
    _transform(chunk: any, encoding: BufferEncoding, callback: TransformCallback): void {
        try {
            // Initialize if not already initialized
            if (!this.initialized) {
                this.initialize()
                    .then(() => this.processChunk(chunk, encoding, callback))
                    .catch((error) => callback(error));
                return;
            }
            
            this.processChunk(chunk, encoding, callback);
        } catch (error: any) {
            this.handleError(error, chunk, encoding, callback);
        }
    }
    
    /**
     * Flush implementation
     * @param callback Callback function
     */
    _flush(callback: TransformCallback): void {
        try {
            this.logger.debug(`Flushing ${this.name} transformer`);
            
            // Set end time
            this.endTime = Date.now();
            
            // Emit final progress if enabled
            if (this.options.emitProgress) {
                this.emitProgress(1);
            }
            
            // Emit final stats if enabled
            if (this.options.emitStats) {
                this.emit('stats', this.getStats());
            }
            
            // Call implementation-specific flush
            this.flushImpl(callback);
        } catch (error: any) {
            this.logger.error(`Error flushing ${this.name} transformer: ${error.message}`);
            callback(error);
        }
    }
    
    /**
     * Process a chunk
     * @param chunk Chunk to process
     * @param encoding Chunk encoding
     * @param callback Callback function
     */
    private processChunk(chunk: any, encoding: BufferEncoding, callback: TransformCallback): void {
        try {
            // Update counters
            this.chunkCount++;
            this.bytesProcessed += chunk.length || 0;
            
            // Process the chunk
            this.processChunkImpl(chunk, encoding, (error, transformedChunk) => {
                if (error) {
                    this.handleError(error, chunk, encoding, callback);
                    return;
                }
                
                // Emit progress if enabled
                if (this.options.emitProgress) {
                    this.emitProgress(this.calculateProgress());
                }
                
                callback(null, transformedChunk);
            });
        } catch (error: any) {
            this.handleError(error, chunk, encoding, callback);
        }
    }
    
    /**
     * Handle error
     * @param error Error to handle
     * @param chunk Chunk that caused the error
     * @param encoding Chunk encoding
     * @param callback Callback function
     */
    private handleError(error: Error, chunk: any, encoding: BufferEncoding, callback: TransformCallback): void {
        this.errorCount++;
        
        this.logger.error(`Error in ${this.name} transformer: ${error.message}`);
        
        // Check if we should retry
        if (this.retryCount < (this.options.maxRetries || 3)) {
            this.retryCount++;
            this.logger.info(`Retrying ${this.name} transformer (${this.retryCount}/${this.options.maxRetries})`);
            
            // Try to process the chunk again
            setTimeout(() => {
                try {
                    this.processChunk(chunk, encoding, callback);
                } catch (retryError: any) {
                    this.logger.error(`Retry failed for ${this.name} transformer: ${retryError.message}`);
                    callback(retryError);
                }
            }, 100 * Math.pow(2, this.retryCount)); // Exponential backoff
            
            return;
        }
        
        // Check error handling strategy
        switch (this.options.errorHandling) {
            case 'lenient':
                // Try to recover
                if (this.canRecover()) {
                    this.logger.warn(`Recovering ${this.name} transformer from error`);
                    this.recoverFromError();
                    callback(null, null); // Skip this chunk
                } else {
                    this.logger.error(`Cannot recover ${this.name} transformer from error`);
                    callback(error);
                }
                break;
                
            case 'skip':
                // Skip this chunk
                this.logger.warn(`Skipping chunk in ${this.name} transformer due to error`);
                callback(null, null);
                break;
                
            case 'strict':
            default:
                // Propagate the error
                callback(error);
                break;
        }
    }
    
    /**
     * Emit progress event
     * @param progress Progress value (0-1)
     */
    protected emitProgress(progress: number): void {
        this.emit('progress', {
            transformer: this.name,
            progress,
            bytesProcessed: this.bytesProcessed,
            chunkCount: this.chunkCount,
            timestamp: Date.now()
        });
    }
    
    /**
     * Calculate progress (0-1)
     * Override in derived classes for more accurate progress calculation
     */
    protected calculateProgress(): number {
        // Default implementation just returns a simple progress based on time
        if (this.endTime > 0) {
            return 1;
        }
        
        // If we've processed at least one chunk, return a small progress value
        if (this.chunkCount > 0) {
            return 0.5;
        }
        
        return 0;
    }
    
    /**
     * Implementation-specific initialization
     * Override in derived classes
     */
    protected async initializeImpl(): Promise<void> {
        // Default implementation does nothing
    }
    
    /**
     * Implementation-specific cleanup
     * Override in derived classes
     */
    protected async cleanupImpl(): Promise<void> {
        // Default implementation does nothing
    }
    
    /**
     * Implementation-specific error recovery
     * Override in derived classes
     */
    protected recoverFromErrorImpl(): void {
        // Default implementation does nothing
    }
    
    /**
     * Implementation-specific flush
     * Override in derived classes if needed
     * @param callback Callback function
     */
    protected flushImpl(callback: TransformCallback): void {
        // Default implementation just calls the callback
        callback();
    }
    
    /**
     * Implementation-specific chunk processing
     * Must be implemented in derived classes
     * @param chunk Chunk to process
     * @param encoding Chunk encoding
     * @param callback Callback function
     */
    protected abstract processChunkImpl(
        chunk: any, 
        encoding: BufferEncoding, 
        callback: (error?: Error | null, data?: any) => void
    ): void;
}
