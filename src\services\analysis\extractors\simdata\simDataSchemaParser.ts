/**
 * SimData Schema Parser
 * Main entry point for SimData schema analysis
 * This file is a wrapper around the new schema analysis system for backward compatibility
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { ParsedSimData, SimDataSchema, SimDataColumn } from './simDataParser.js';
import { SimDataSchemaParser as NewSchemaParser } from './schema/schemaParser.js';
import { DatabaseService } from '../../../databaseService.js';

const log = new Logger('SimDataSchemaParser');

// Define interfaces directly for backward compatibility

/**
 * Interface for schema inheritance information
 */
export interface SchemaInheritanceInfo {
    parent: string;
    child: string;
    inheritedColumns: string[];
    addedColumns: string[];
}

/**
 * Interface for schema compatibility information
 */
export interface SchemaCompatibilityInfo {
    isCompatible: boolean;
    compatibilityScore: number; // 0-100
    incompatibleColumns: string[];
    missingColumns: string[];
    extraColumns: string[];
    typeMismatches: Array<{ column: string, expectedType: number, actualType: number }>;
    criticalIssues: boolean;
    gameplayImpact: 'none' | 'low' | 'medium' | 'high';
    conflictDescription: string;
    compatibleWithBaseGame?: boolean;
    requiredPacks?: string[];
    potentialConflicts?: Array<{
        schemaName: string;
        reason: string;
        severity: 'low' | 'medium' | 'high';
    }>;
}

/**
 * Interface for schema purpose information
 */
export interface SchemaPurposeInfo {
    schemaName: string;
    purpose: string;
    gameplaySystem: string;
    confidence: number; // 0-100
    keyColumns: string[];
    criticalColumns: string[];
    subcategory?: string;
    description?: string;
    examples?: string[];
    commonModTypes?: string[];
}
export interface ColumnSemanticInfo {
    name: string;
    type: number;
    typeName: string;
    category: string;
    isCritical: boolean;
    description: string;
    purpose?: string;
    possibleValues?: any[];
    defaultValue?: any;
    isRequired?: boolean;
    isKey?: boolean;
    isReference?: boolean;
    isValue?: boolean;
    isState?: boolean;
    gameplayImpact?: string;
    relatedColumns?: string[];
    relatedTuning?: string[];
    valuePatterns?: {
        uniqueValues: any[];
        commonValues: any[];
        valueDistribution: Record<string, number>;
        hasNullValues: boolean;
        valueRange?: { min: number; max: number };
    };
}

/**
 * SimData Schema Parser
 * Responsible for parsing and analyzing SimData schemas
 * This class is maintained for backward compatibility
 * New code should use the schema/schemaParser.js module directly
 */
export class SimDataSchemaParser {
    private logger: Logger;
    private newParser: NewSchemaParser;

    constructor(databaseService?: DatabaseService, logger?: Logger) {
        this.logger = logger || log;
        this.newParser = new NewSchemaParser(databaseService, this.logger);
    }

    /**
     * Parse and analyze a SimData schema
     * @param simData The parsed SimData object
     * @param modId Optional mod ID for schema registration
     * @returns Detailed schema analysis
     */
    public async parseSchema(simData: ParsedSimData, modId?: number) {
        return await this.newParser.parseSchema(simData, modId);
    }

    /**
     * Compare two schemas for compatibility
     * @param schema1 The first schema
     * @param schema2 The second schema
     * @returns Schema compatibility information
     */
    public compareSchemas(schema1: SimDataSchema, schema2: SimDataSchema) {
        return this.newParser.compareSchemas(schema1, schema2);
    }

    /**
     * Find schemas that might conflict with a given schema
     * @param schema The SimData schema
     * @returns Array of potentially conflicting schemas
     */
    public async findPotentialConflicts(schema: SimDataSchema) {
        return await this.newParser.findPotentialConflicts(schema);
    }

    /**
     * Find schemas by pattern
     * @param pattern The pattern to match
     * @returns Array of matching schema names
     */
    public async findSchemas(pattern: string) {
        return await this.newParser.findSchemas(pattern);
    }

    /**
     * Initialize the schema repository
     */
    public async initializeRepository() {
        await this.newParser.initializeRepository();
    }

    /**
     * Gets the name of a SimData data type from its numeric value
     * @param type The numeric data type
     * @returns The data type name
     * @deprecated Use the getDataTypeName function from simDataParser.js instead
     */
    private getDataTypeName(type: number): string {
        switch (type) {
            case 1: return 'Boolean';
            case 2: return 'Char';
            case 3: return 'Int8';
            case 4: return 'UInt8';
            case 5: return 'Int16';
            case 6: return 'UInt16';
            case 7: return 'Int32';
            case 8: return 'UInt32';
            case 9: return 'Int64';
            case 10: return 'UInt64';
            case 11: return 'Float';
            case 12: return 'String';
            case 13: return 'HashedString';
            case 14: return 'Object';
            case 15: return 'Vector';
            case 16: return 'Float2';
            case 17: return 'Float3';
            case 18: return 'Float4';
            case 19: return 'TableSetReference';
            case 20: return 'ResourceKey';
            case 21: return 'LocalizationKey';
            case 22: return 'VariantList';
            default: return `Type${type}`;
        }
    }
}
