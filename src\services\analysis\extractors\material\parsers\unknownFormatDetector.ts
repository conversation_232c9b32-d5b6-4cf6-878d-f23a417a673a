/**
 * Detector for unknown material formats
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { DependencyInfo } from '../../../../databaseService.js';
import { MaterialHeaderInfo } from '../materialTypes.js';
import { MATERIAL_RESOURCE_TYPES, TEXTURE_RESOURCE_TYPES } from '../materialConstants.js';
import { handleMaterialExtractionError } from '../error/index.js';

const logger = new Logger('UnknownFormatDetector');

/**
 * Detects and parses an unknown material format based on resource type
 * @param buffer The resource buffer
 * @param resourceId The resource ID
 * @param resourceType The resource type
 * @returns The parsed material header and dependencies
 */
export function detectUnknownFormat(
    buffer: Buffer, 
    resourceId: number, 
    resourceType: number
): { header: MaterialHeaderInfo, dependencies: DependencyInfo[] } {
    try {
        const dependencies: DependencyInfo[] = [];
        let header: MaterialHeaderInfo = {
            format: 'Unknown',
            version: 0,
            materialCount: 0,
            flags: 0
        };

        // For MATERIAL_DEFINITION_ALT (0xAC16FBEC)
        if (resourceType === MATERIAL_RESOURCE_TYPES.MATERIAL_DEFINITION_ALT) {
            header.format = 'MTDF'; // Material Definition Format

            if (buffer.length >= 12) {
                header.version = buffer.readUInt32LE(0);
                header.flags = buffer.readUInt32LE(4);
                header.materialCount = buffer.readUInt32LE(8);
            }

            // Try to detect shader type
            for (let i = 16; i < buffer.length - 32; i += 4) {
                // Look for string lengths that might indicate shader names
                const strLen = buffer.readUInt32LE(i);
                if (strLen > 0 && strLen < 64 && i + 4 + strLen <= buffer.length) {
                    try {
                        const potentialShaderName = buffer.slice(i + 4, i + 4 + strLen).toString('utf8');
                        // Check if it looks like a shader name
                        if (/^[a-zA-Z0-9_]+Shader$/.test(potentialShaderName) ||
                            potentialShaderName.includes('Material') ||
                            potentialShaderName.includes('Shader')) {
                            header.shaderType = potentialShaderName;
                            break;
                        }
                    } catch (e) {
                        // Not a valid string, continue
                    }
                }
            }

            // Try to count texture references
            let textureRefCount = 0;
            for (let i = 32; i < buffer.length - 16; i += 4) {
                const potentialType = buffer.readUInt32LE(i);
                // Check if it's a texture type
                if (TEXTURE_RESOURCE_TYPES.includes(potentialType)) {
                    textureRefCount++;
                }
            }
            if (textureRefCount > 0) {
                header.textureCount = textureRefCount;
            }
        }
        // For MATERIAL_DEFINITION (0x0499A526)
        else if (resourceType === MATERIAL_RESOURCE_TYPES.MATERIAL_DEFINITION) {
            header.format = 'MTRL'; // Assume MTRL format

            if (buffer.length >= 12) {
                header.version = buffer.readUInt32LE(0);
                header.flags = buffer.readUInt32LE(4);
            }

            // Try to detect parameter count
            if (buffer.length >= 16) {
                const potentialParamCount = buffer.readUInt32LE(12);
                if (potentialParamCount > 0 && potentialParamCount < 100) {
                    header.parameterCount = potentialParamCount;
                }
            }
        }
        // For MATERIAL_VARIANT (0x0333406C)
        else if (resourceType === MATERIAL_RESOURCE_TYPES.MATERIAL_VARIANT) {
            header.format = 'MTVR'; // Material Variant

            if (buffer.length >= 12) {
                header.version = buffer.readUInt32LE(0);
                header.flags = buffer.readUInt32LE(4);
            }
        }
        // For TEXTURE_DEFINITION (0x0288B3A2)
        else if (resourceType === MATERIAL_RESOURCE_TYPES.TEXTURE_DEFINITION) {
            header.format = 'TXDF'; // Texture Definition

            if (buffer.length >= 12) {
                header.version = buffer.readUInt32LE(0);
                header.flags = buffer.readUInt32LE(4);
            }
        }
        // For SHADER_DEFINITION (0x0354796A)
        else if (resourceType === MATERIAL_RESOURCE_TYPES.SHADER_DEFINITION) {
            header.format = 'SHDF'; // Shader Definition

            if (buffer.length >= 12) {
                header.version = buffer.readUInt32LE(0);
                header.flags = buffer.readUInt32LE(4);
            }
        }
        // Generic fallback
        else {
            header.format = 'Unknown';

            if (buffer.length >= 12) {
                header.version = buffer.readUInt32LE(0);
                header.flags = buffer.readUInt32LE(4);
                header.materialCount = buffer.readUInt32LE(8);
            }
        }

        // Scan for potential texture references
        for (let i = 32; i < buffer.length - 16; i += 4) {
            const potentialType = buffer.readUInt32LE(i);

            if (TEXTURE_RESOURCE_TYPES.includes(potentialType)) {
                const potentialGroup = buffer.readUInt32LE(i + 4);
                const potentialInstance1 = buffer.readUInt32LE(i + 8);
                const potentialInstance2 = buffer.readUInt32LE(i + 12);

                // Construct potential bigint instance from two 32-bit parts
                const potentialInstance = BigInt(potentialInstance1) | (BigInt(potentialInstance2) << 32n);

                // Add as potential dependency
                dependencies.push({
                    resourceId: resourceId,
                    targetType: potentialType,
                    targetGroup: BigInt(potentialGroup),
                    targetInstance: potentialInstance,
                    referenceType: 'Texture',
                    timestamp: Date.now()
                });
            }
        }

        return { header, dependencies };
    } catch (error) {
        return handleMaterialExtractionError(
            error,
            {
                resourceId,
                operation: 'detectUnknownFormat',
                bufferLength: buffer?.length,
                additionalInfo: { resourceType }
            },
            {
                header: {
                    format: 'FORMAT_DETECTION_ERROR',
                    version: 0,
                    materialCount: 0,
                    flags: 0
                },
                dependencies: []
            }
        );
    }
}
