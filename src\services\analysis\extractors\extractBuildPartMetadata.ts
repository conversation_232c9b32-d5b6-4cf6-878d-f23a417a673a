import { ResourceKey as AppR<PERSON>ource<PERSON>ey, ResourceMetadata } from '../../../types/resource/interfaces.js';
import { DatabaseService } from '../../databaseService.js';
import { Logger } from '../../../utils/logging/logger.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';

const log = new Logger('BuildPartExtractor');

/**
 * Extracts metadata from Build Part resources.
 * Build Part resources define building components like walls, floors, roofs, etc.
 *
 * @param key The resource key.
 * @param buffer The resource buffer.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns A partial ResourceMetadata object with build part information.
 */
export async function extractBuildPartMetadata(
    key: AppResourceKey,
    buffer: Buffer,
    resourceId: number,
    databaseService: DatabaseService
): Promise<Partial<ResourceMetadata>> {
    const extractedMetadata: Partial<ResourceMetadata> = {};

    try {
        // Verify this is a Build Part resource
        if (key.type !== ResourceTypes.RESOURCE_TYPE_BUILD_PART) {
            log.warn(`Resource ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)} is not a Build Part resource`);
            return {
                contentSnippet: `[Not a Build Part resource: ${key.type.toString(16)}]`,
                extractorUsed: 'buildpart'
            };
        }

        log.info(`Extracting metadata from Build Part resource: ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)}`);

        // Build Part resources have a specific binary format
        // First 4 bytes: Version (uint32)
        const version = buffer.readUInt32LE(0);
        extractedMetadata.buildPartVersion = version;

        // Next 4 bytes: Flags (uint32)
        const flags = buffer.readUInt32LE(4);
        extractedMetadata.buildPartFlags = flags;

        // Determine build part type based on flags
        let buildPartType = 'Unknown';
        if ((flags & 0x01) !== 0) buildPartType = 'Wall';
        else if ((flags & 0x02) !== 0) buildPartType = 'Floor';
        else if ((flags & 0x04) !== 0) buildPartType = 'Roof';
        else if ((flags & 0x08) !== 0) buildPartType = 'Fence';
        else if ((flags & 0x10) !== 0) buildPartType = 'Stair';
        else if ((flags & 0x20) !== 0) buildPartType = 'Pool';
        else if ((flags & 0x40) !== 0) buildPartType = 'Terrain';

        extractedMetadata.buildPartType = buildPartType;

        // Next 4 bytes: Material count (uint32)
        const materialCount = buffer.readUInt32LE(8);
        extractedMetadata.buildPartMaterialCount = materialCount;

        // Extract material references if available
        if (materialCount > 0 && buffer.length >= 12 + (materialCount * 16)) {
            const materials: any[] = [];
            let offset = 12;

            for (let i = 0; i < materialCount; i++) {
                // Each material reference has:
                // - 4 bytes: Type (uint32)
                // - 4 bytes: Group (uint32)
                // - 8 bytes: Instance (uint64)
                const type = buffer.readUInt32LE(offset);
                const group = buffer.readUInt32LE(offset + 4);
                const instanceLow = buffer.readUInt32LE(offset + 8);
                const instanceHigh = buffer.readUInt32LE(offset + 12);
                const instance = BigInt(instanceLow) + (BigInt(instanceHigh) << 32n);

                materials.push({
                    type: `0x${type.toString(16).padStart(8, '0')}`,
                    group: `0x${group.toString(16).padStart(8, '0')}`,
                    instance: `0x${instance.toString(16).padStart(16, '0')}`
                });

                // Save dependency in the database
                try {
                    await databaseService.dependencies.saveDependency({
                        sourceResourceId: resourceId,
                        targetType: type,
                        targetGroup: group,
                        targetInstance: instance,
                        referenceType: 'Material'
                    });
                } catch (error) {
                    log.warn(`Failed to save material dependency: ${error}`);
                }

                offset += 16;
            }

            extractedMetadata.buildPartMaterials = JSON.stringify(materials);
        }

        // Extract catalog data if available (typically after materials)
        if (buffer.length >= 12 + (materialCount * 16) + 8) {
            const offset = 12 + (materialCount * 16);

            // 4 bytes: Catalog price (uint32)
            const price = buffer.readUInt32LE(offset);
            extractedMetadata.buildPartPrice = price;

            // 4 bytes: Catalog flags (uint32)
            const catalogFlags = buffer.readUInt32LE(offset + 4);
            extractedMetadata.buildPartCatalogFlags = catalogFlags;
        }

        // Create a content snippet
        let contentSnippet = `Build Part v${version} (${buildPartType})`;
        if (materialCount > 0) {
            contentSnippet += `, ${materialCount} materials`;
        }
        if (extractedMetadata.buildPartPrice) {
            contentSnippet += `, §${extractedMetadata.buildPartPrice}`;
        }

        extractedMetadata.contentSnippet = contentSnippet;
        extractedMetadata.extractorUsed = 'buildpart';

        return extractedMetadata;
    } catch (error) {
        log.error(`Error extracting Build Part metadata: ${error}`);
        return {
            contentSnippet: `[Error extracting Build Part metadata: ${error}]`,
            extractorUsed: 'buildpart',
            extractionError: String(error)
        };
    }
}
