import { ResourceKey as AppResourceKey } from '../../../../types/resource/interfaces.js';
import { Logger } from '../../../../utils/logging/logger.js';

const logger = new Logger('TuningXmlParser');

/**
 * Processes the buffer to handle potential BOM and parses the XML string.
 */
export async function processBufferAndParseXml(key: AppResource<PERSON>ey, buffer: Buffer): Promise<string | undefined> {
    try {
        // Process buffer to handle potential BOM
        let processedBuffer = buffer;

        // Check for UTF-8 BOM (EF BB BF) and remove if present
        if (processedBuffer.length >= 3 &&
            processedBuffer[0] === 0xEF &&
            processedBuffer[1] === 0xBB &&
            processedBuffer[2] === 0xBF) {
            logger.info(`Detected UTF-8 BOM for resource ${key.instance.toString(16)}. Removing BOM.`);
            processedBuffer = processedBuffer.subarray(3);
        }

        // Convert buffer to string
        const xmlString = processedBuffer.toString('utf-8');
        return xmlString;
    } catch (error: any) {
        logger.error(`Error processing buffer or parsing XML for resource ${key.instance.toString(16)}: ${error.message || error}`);
        return undefined;
    }
}
