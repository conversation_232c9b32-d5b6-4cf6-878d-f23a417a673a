/**
 * Schema compatibility analyzer for SimData schemas
 * Responsible for comparing schemas and determining compatibility
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataSchema } from '../simDataParser.js';
import { SchemaCompatibilityInfo } from './schemaInterfaces.js';

const log = new Logger('SchemaCompatibilityAnalyzer');

/**
 * Schema Compatibility Analyzer
 * Analyzes compatibility between SimData schemas
 */
export class SchemaCompatibilityAnalyzer {
    /**
     * Compare two schemas for compatibility
     * @param schema1 The first schema
     * @param schema2 The second schema
     * @returns Schema compatibility information
     */
    public compareSchemas(schema1: SimDataSchema, schema2: SimDataSchema): SchemaCompatibilityInfo {
        // Check if schemas are identical
        if (schema1.schemaId === schema2.schemaId && schema1.hash === schema2.hash) {
            return {
                isCompatible: true,
                compatibilityScore: 100,
                incompatibleColumns: [],
                missingColumns: [],
                extraColumns: [],
                typeMismatches: []
            };
        }
        
        // Create maps of columns by name for easy lookup
        const columns1 = new Map(schema1.columns.map(col => [col.name, col]));
        const columns2 = new Map(schema2.columns.map(col => [col.name, col]));
        
        // Find missing and extra columns
        const missingColumns: string[] = [];
        const extraColumns: string[] = [];
        const incompatibleColumns: string[] = [];
        const typeMismatches: Array<{ column: string, expectedType: number, actualType: number }> = [];
        
        // Check columns in schema1 that are missing or different in schema2
        for (const [name, col1] of columns1.entries()) {
            const col2 = columns2.get(name);
            
            if (!col2) {
                missingColumns.push(name);
            } else if (col1.type !== col2.type) {
                incompatibleColumns.push(name);
                typeMismatches.push({
                    column: name,
                    expectedType: col1.type,
                    actualType: col2.type
                });
            }
        }
        
        // Check columns in schema2 that are extra compared to schema1
        for (const name of columns2.keys()) {
            if (!columns1.has(name)) {
                extraColumns.push(name);
            }
        }
        
        // Calculate compatibility score
        const totalColumns = columns1.size;
        const matchingColumns = totalColumns - missingColumns.length - incompatibleColumns.length;
        const compatibilityScore = totalColumns > 0 
            ? Math.round((matchingColumns / totalColumns) * 100)
            : 0;
        
        return {
            isCompatible: incompatibleColumns.length === 0 && missingColumns.length === 0,
            compatibilityScore,
            incompatibleColumns,
            missingColumns,
            extraColumns,
            typeMismatches
        };
    }

    /**
     * Analyze potential conflicts between two schemas
     * @param schema1 The first schema
     * @param schema2 The second schema
     * @returns Analysis of potential conflicts
     */
    public analyzeConflicts(schema1: SimDataSchema, schema2: SimDataSchema): {
        hasConflicts: boolean;
        conflictSeverity: 'none' | 'low' | 'medium' | 'high';
        criticalConflicts: string[];
        description: string;
    } {
        const compatibility = this.compareSchemas(schema1, schema2);
        
        // No conflicts if schemas are compatible
        if (compatibility.isCompatible) {
            return {
                hasConflicts: false,
                conflictSeverity: 'none',
                criticalConflicts: [],
                description: 'Schemas are fully compatible'
            };
        }
        
        // Identify critical conflicts (type mismatches)
        const criticalConflicts = compatibility.typeMismatches.map(mismatch => 
            `Column '${mismatch.column}' has type mismatch: expected ${mismatch.expectedType}, got ${mismatch.actualType}`
        );
        
        // Determine conflict severity
        let conflictSeverity: 'none' | 'low' | 'medium' | 'high' = 'none';
        
        if (compatibility.typeMismatches.length > 0) {
            conflictSeverity = 'high'; // Type mismatches are critical
        } else if (compatibility.missingColumns.length > 0) {
            conflictSeverity = 'medium'; // Missing columns are important
        } else if (compatibility.extraColumns.length > 0) {
            conflictSeverity = 'low'; // Extra columns are less critical
        }
        
        // Generate description
        let description = '';
        
        if (compatibility.typeMismatches.length > 0) {
            description += `${compatibility.typeMismatches.length} column(s) have type mismatches. `;
        }
        
        if (compatibility.missingColumns.length > 0) {
            description += `${compatibility.missingColumns.length} column(s) are missing. `;
        }
        
        if (compatibility.extraColumns.length > 0) {
            description += `${compatibility.extraColumns.length} extra column(s) are present. `;
        }
        
        description += `Overall compatibility score: ${compatibility.compatibilityScore}%.`;
        
        return {
            hasConflicts: true,
            conflictSeverity,
            criticalConflicts,
            description
        };
    }
}
