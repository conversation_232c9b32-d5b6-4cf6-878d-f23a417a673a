/**
 * SINGLE SOURCE OF TRUTH for all conflict-related types in the application.
 * This file defines all conflict types, interfaces, and enums used throughout the application.
 * All conflict-related types should be defined here and imported from here.
 *
 * The types are organized into categories:
 * 1. Core conflict types (ConflictType, ConflictSeverity)
 * 2. Conflict information interfaces (ConflictInfo, ConflictResult)
 * 3. Specific conflict type interfaces (ResourceConflict, MetadataConflict, etc.)
 * 4. Configuration interfaces (ConflictDetectionConfig, etc.)
 */

// Import only what's needed
import { BinaryResourceType } from '../resource/core.js';
import { ResourceKey } from '../resource/interfaces.js';
import { ConflictType, ConflictSeverity } from './ConflictTypes.js';

// Unified conflict types
export interface ConflictInfo {
  id: string;
  type: ConflictType;
  severity: ConflictSeverity; // Use ConflictSeverity
  description: string;
  affectedResources: ResourceKey[];
  timestamp: number;
  recommendations: string[];
  resolution?: { // Changed to object type
      action: string;
      description: string;
  };
  metadata?: {
    [key: string]: unknown;
  };
  confidence?: number; // Added for LLM-based conflict detection
}

export interface ConflictResult {
  hasConflicts: boolean;
  conflicts: ConflictInfo[];
  timestamp: number;
  metadata?: {
    [key: string]: unknown;
  };
}

export interface ConflictDetectionResult {
  conflicts: ConflictInfo[];
  recommendations: string[];
}

export interface ResourceConflict {
  type: ConflictType.RESOURCE;
  severity: ConflictSeverity; // Use ConflictSeverity
  description: string;
  affectedResources: ResourceKey[];
  timestamp: number;
  resourceType: BinaryResourceType; // Use only BinaryResourceType
  resourceKey: ResourceKey;
  conflictingResources: ResourceKey[];
  resolution?: {
    action: 'KEEP' | 'REPLACE' | 'MERGE' | 'IGNORE';
    details?: string;
  };
  recommendations?: string[];
}

export interface MetadataConflict {
  type: ConflictType.METADATA;
  severity: ConflictSeverity; // Use ConflictSeverity
  description: string;
  affectedResources: ResourceKey[];
  timestamp: number;
  field: string;
  oldValue: any;
  newValue: any;
  resolution?: {
    action: 'KEEP_OLD' | 'KEEP_NEW' | 'MERGE' | 'IGNORE';
    details?: string;
  };
  recommendations?: string[];
}

export interface VersionConflict {
  type: ConflictType.VERSION;
  severity: ConflictSeverity; // Use ConflictSeverity
  description: string;
  affectedResources: ResourceKey[];
  timestamp: number;
  oldVersion: string;
  newVersion: string;
  resolution?: {
    action: 'KEEP_OLD' | 'KEEP_NEW' | 'IGNORE';
    details?: string;
  };
  recommendations?: string[];
}

export interface DependencyConflict {
  type: ConflictType.DEPENDENCY;
  severity: ConflictSeverity; // Use ConflictSeverity
  description: string;
  affectedResources: ResourceKey[];
  timestamp: number;
  missingDependencies: ResourceKey[];
  resolution?: {
    action: 'IGNORE' | 'DOWNLOAD' | 'REMOVE';
    details?: string;
  };
  recommendations?: string[];
}

export interface CustomConflict {
  type: ConflictType.CUSTOM;
  severity: ConflictSeverity; // Use ConflictSeverity
  description: string;
  affectedResources: ResourceKey[];
  timestamp: number;
  customType: string;
  customData: any;
  resolution?: {
    action: string;
    details?: string;
  };
  recommendations?: string[];
}

export type Conflict = ResourceConflict | MetadataConflict | VersionConflict | DependencyConflict | CustomConflict;

// Package-specific types
export interface PackageConflict {
  packageId: string;
  packageName: string;
  conflicts: Conflict[];
  totalConflicts: number;
  severity: ConflictSeverity; // Use ConflictSeverity
  timestamp: Date;
}

export interface PackageConflictSummary {
  packageId: string;
  packageName: string;
  conflictCount: number;
  severity: ConflictSeverity; // Use ConflictSeverity
  lastUpdated: Date;
}

/**
 * Configuration for conflict detection
 */
export interface ConflictDetectionConfig {
  /**
   * Whether to use LLM-based conflict detection
   */
  useLlm: boolean;

  /**
   * Whether to enhance rule-based conflicts with LLM analysis
   */
  enhanceWithLlm: boolean;

  /**
   * Whether to use rule-based conflict detection
   */
  useRuleBased: boolean;

  /**
   * Maximum number of resources to compare
   */
  maxResourcesToCompare?: number;

  /**
   * Maximum number of conflicts to return
   */
  maxConflictsToReturn?: number;
}

/**
 * Result of a conflict analysis operation
 */
export interface ConflictAnalysisResult {
  /**
   * Whether a conflict was detected
   */
  hasConflict: boolean;

  /**
   * Severity of the conflict
   */
  severity: string;

  /**
   * Description of the conflict
   */
  description: string;

  /**
   * Recommendations for resolving the conflict
   */
  recommendations: string[];

  /**
   * Additional details about the conflict
   */
  details?: string;

  /**
   * Confidence level of the conflict detection (0-1)
   */
  confidence?: number;
}

// Re-export commonly used types for convenience
export * from './ConflictTypes.js'; // Added .js extension
// Export ConflictSeverity directly if needed elsewhere, or rely on import from this index
export { ConflictSeverity };
