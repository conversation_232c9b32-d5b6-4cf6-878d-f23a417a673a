/**
 * PNG image format parser
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { ImageFormat, PNGMetadata } from '../types.js';
import { <PERSON><PERSON>erReader } from '../utils/bufferReader.js';
import { createImageErrorContext, handleImageError } from '../error/imageExtractorErrorHandler.js';

// Create a logger instance
const log = new Logger('PNGParser');

/**
 * PNG chunk types
 */
export enum PNGChunkType {
    IHDR = 0x49484452, // "IHDR" - Image header
    PLTE = 0x504c5445, // "PLTE" - Palette
    IDAT = 0x49444154, // "IDAT" - Image data
    IEND = 0x49454e44, // "IEND" - Image end
    tRNS = 0x74524e53, // "tRNS" - Transparency
    gAMA = 0x67414d41, // "gAMA" - Gamma
    cHRM = 0x6348524d, // "cHRM" - Primary chromaticities
    sRGB = 0x73524742, // "sRGB" - Standard RGB color space
    iCCP = 0x69434350, // "iCCP" - Embedded ICC profile
    tEXt = 0x74455874, // "tEXt" - Textual data
    zTXt = 0x7a545874, // "zTXt" - Compressed textual data
    iTXt = 0x69545874, // "iTXt" - International textual data
    bKGD = 0x624b4744, // "bKGD" - Background color
    pHYs = 0x70485973, // "pHYs" - Physical pixel dimensions
    sBIT = 0x73424954, // "sBIT" - Significant bits
    sPLT = 0x73504c54, // "sPLT" - Suggested palette
    hIST = 0x68495354, // "hIST" - Palette histogram
    tIME = 0x74494d45  // "tIME" - Last modification time
}

/**
 * PNG color types
 */
export enum PNGColorType {
    GRAYSCALE = 0,
    RGB = 2,
    PALETTE = 3,
    GRAYSCALE_ALPHA = 4,
    RGBA = 6
}

/**
 * Parses a PNG image buffer
 * @param buffer PNG image buffer
 * @param resourceId Resource ID for error context
 * @param instanceId Instance ID for error context
 * @returns Parsed PNG metadata
 */
export function parsePNG(buffer: Buffer, resourceId: number, instanceId: string): PNGMetadata {
    try {
        // Create a buffer reader
        const reader = new BufferReader(buffer);
        
        // Validate PNG signature (89 50 4E 47 0D 0A 1A 0A)
        if (buffer.length < 8 ||
            buffer[0] !== 0x89 ||
            buffer[1] !== 0x50 ||
            buffer[2] !== 0x4E ||
            buffer[3] !== 0x47 ||
            buffer[4] !== 0x0D ||
            buffer[5] !== 0x0A ||
            buffer[6] !== 0x1A ||
            buffer[7] !== 0x0A) {
            throw new Error('Invalid PNG signature');
        }
        
        // Skip signature
        reader.skip(8);
        
        // Read IHDR chunk
        const ihdrLength = reader.readUInt32BE('IHDR Length');
        if (ihdrLength !== 13) {
            throw new Error(`Invalid IHDR chunk length: ${ihdrLength}`);
        }
        
        const ihdrType = reader.readUInt32BE('IHDR Type');
        if (ihdrType !== PNGChunkType.IHDR) {
            throw new Error(`Invalid IHDR chunk type: ${ihdrType.toString(16)}`);
        }
        
        // Read image dimensions
        const width = reader.readUInt32BE('Width');
        const height = reader.readUInt32BE('Height');
        
        // Read bit depth
        const bitDepth = reader.readUInt8('Bit Depth');
        
        // Read color type
        const colorType = reader.readUInt8('Color Type');
        
        // Read compression method
        const compressionMethod = reader.readUInt8('Compression Method');
        if (compressionMethod !== 0) {
            log.warn(`Unusual PNG compression method: ${compressionMethod}`);
        }
        
        // Read filter method
        const filterMethod = reader.readUInt8('Filter Method');
        if (filterMethod !== 0) {
            log.warn(`Unusual PNG filter method: ${filterMethod}`);
        }
        
        // Read interlace method
        const interlaceMethod = reader.readUInt8('Interlace Method');
        
        // Skip CRC
        reader.skip(4);
        
        // Determine if the image has an alpha channel
        const hasAlpha = colorType === PNGColorType.GRAYSCALE_ALPHA || colorType === PNGColorType.RGBA;
        
        // Calculate bits per pixel
        let bitsPerPixel = bitDepth;
        
        // Adjust bits per pixel based on color type
        switch (colorType) {
            case PNGColorType.GRAYSCALE:
                // No adjustment needed
                break;
            case PNGColorType.RGB:
                bitsPerPixel *= 3; // 3 channels (RGB)
                break;
            case PNGColorType.PALETTE:
                // No adjustment needed, palette-based
                break;
            case PNGColorType.GRAYSCALE_ALPHA:
                bitsPerPixel *= 2; // 2 channels (grayscale + alpha)
                break;
            case PNGColorType.RGBA:
                bitsPerPixel *= 4; // 4 channels (RGBA)
                break;
            default:
                log.warn(`Unknown PNG color type: ${colorType}`);
                break;
        }
        
        // Create and return metadata
        return {
            format: ImageFormat.PNG,
            width: width,
            height: height,
            hasAlpha: hasAlpha,
            bitsPerPixel: bitsPerPixel
        };
    } catch (error) {
        // Handle error and return minimal metadata
        const context = createImageErrorContext(resourceId, instanceId, 'parsePNG');
        const result = handleImageError(error, context);
        
        return {
            format: ImageFormat.PNG,
            width: undefined,
            height: undefined,
            hasAlpha: undefined,
            bitsPerPixel: undefined
        };
    }
}
