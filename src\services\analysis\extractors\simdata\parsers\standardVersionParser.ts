/**
 * Parser for standard SimData versions (1-20)
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataColumn, SimDataSchema, SimDataInstance } from '../simDataTypes.js';
import { <PERSON><PERSON>erReader } from '../utils/bufferReader.js';
import { 
    SimDataErrorContext, 
    createSimDataErrorContext, 
    handleSimDataError 
} from '../error/simDataParserErrorHandler.js';

const logger = new Logger('StandardVersionParser');

/**
 * Interface for parsed SimData
 */
export interface ParsedSimData {
    schema?: SimDataSchema;
    instances: SimDataInstance[];
    version?: number;
    flags?: number;
}

/**
 * Parses a standard SimData buffer (versions 1-20)
 * @param buffer SimData buffer
 * @param version SimData version
 * @returns Parsed SimData or undefined if parsing fails
 */
export function parseStandardVersion(buffer: Buffer, version: number): ParsedSimData | undefined {
    try {
        logger.info(`Parsing standard SimData version ${version}`);
        
        // Create a buffer reader
        const reader = new BufferReader(buffer, 0, version);
        
        // Read version and flags
        const readVersion = reader.readUInt16LE();
        if (readVersion === undefined || readVersion !== version) {
            logger.error(`Version mismatch: expected ${version}, got ${readVersion}`);
            return undefined;
        }
        
        const flags = reader.readUInt16LE();
        if (flags === undefined) {
            logger.error('Failed to read flags');
            return undefined;
        }
        
        // Read schema name
        const schemaName = reader.readLengthPrefixedString();
        if (schemaName === undefined) {
            logger.error('Failed to read schema name');
            return undefined;
        }
        
        // Read schema ID and hash
        const schemaId = reader.readUInt32LE();
        const schemaHash = reader.readUInt32LE();
        
        if (schemaId === undefined || schemaHash === undefined) {
            logger.error('Failed to read schema ID or hash');
            return undefined;
        }
        
        // Read column count
        const columnCount = reader.readUInt32LE();
        if (columnCount === undefined) {
            logger.error('Failed to read column count');
            return undefined;
        }
        
        // Validate column count
        if (columnCount > 1000) {
            logger.error(`Invalid column count: ${columnCount}`);
            return undefined;
        }
        
        // Read columns
        const columns: SimDataColumn[] = [];
        
        for (let i = 0; i < columnCount; i++) {
            // Read column name
            const columnName = reader.readLengthPrefixedString();
            if (columnName === undefined) {
                logger.error(`Failed to read name for column ${i}`);
                return undefined;
            }
            
            // Read column type and flags
            const columnType = reader.readUInt32LE();
            const columnFlags = reader.readUInt32LE();
            
            if (columnType === undefined || columnFlags === undefined) {
                logger.error(`Failed to read type or flags for column ${columnName}`);
                return undefined;
            }
            
            columns.push({
                name: columnName,
                type: columnType,
                flags: columnFlags
            });
        }
        
        // Create schema
        const schema: SimDataSchema = {
            name: schemaName,
            schemaId: schemaId,
            hash: schemaHash,
            columns: columns,
            version: version,
            flags: flags
        };
        
        // Read instance count
        const instanceCount = reader.readUInt32LE();
        if (instanceCount === undefined) {
            logger.error('Failed to read instance count');
            return {
                schema,
                instances: [],
                version,
                flags
            };
        }
        
        // Validate instance count
        if (instanceCount > 10000) {
            logger.error(`Invalid instance count: ${instanceCount}`);
            return {
                schema,
                instances: [],
                version,
                flags
            };
        }
        
        // Read instances
        const instances: SimDataInstance[] = [];
        
        for (let i = 0; i < instanceCount; i++) {
            try {
                // Read instance name
                const instanceName = reader.readLengthPrefixedString();
                if (instanceName === undefined) {
                    logger.error(`Failed to read name for instance ${i}`);
                    break;
                }
                
                // Read instance ID
                const instanceId = reader.readUInt32LE();
                if (instanceId === undefined) {
                    logger.error(`Failed to read ID for instance ${instanceName}`);
                    break;
                }
                
                // Read values
                const values: Record<string, any> = {};
                let validValues = true;
                
                for (const column of columns) {
                    try {
                        switch (column.type) {
                            case 1: // Boolean
                                const boolValue = reader.readUInt8();
                                if (boolValue === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = boolValue !== 0;
                                break;
                            case 2: // Char
                                const charValue = reader.readUInt8();
                                if (charValue === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = String.fromCharCode(charValue);
                                break;
                            case 3: // Int8
                                const int8Value = reader.readInt8();
                                if (int8Value === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = int8Value;
                                break;
                            case 4: // UInt8
                                const uint8Value = reader.readUInt8();
                                if (uint8Value === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = uint8Value;
                                break;
                            case 5: // Int16
                                const int16Value = reader.readInt16LE();
                                if (int16Value === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = int16Value;
                                break;
                            case 6: // UInt16
                                const uint16Value = reader.readUInt16LE();
                                if (uint16Value === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = uint16Value;
                                break;
                            case 7: // Int32
                            case 8: // Int32 (alternate)
                                const int32Value = reader.readInt32LE();
                                if (int32Value === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = int32Value;
                                break;
                            case 9: // UInt32
                            case 10: // UInt32 (alternate)
                                const uint32Value = reader.readUInt32LE();
                                if (uint32Value === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = uint32Value;
                                break;
                            case 11: // Float
                            case 12: // Float (alternate)
                                const floatValue = reader.readFloatLE();
                                if (floatValue === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = floatValue;
                                break;
                            case 13: // String
                                const stringValue = reader.readLengthPrefixedString();
                                if (stringValue === undefined) {
                                    validValues = false;
                                    break;
                                }
                                values[column.name] = stringValue;
                                break;
                            case 20: // ResourceKey
                                const type = reader.readUInt32LE();
                                const group = reader.readUInt32LE();
                                const instance = reader.readBigUInt64LE();
                                
                                if (type === undefined || group === undefined || instance === undefined) {
                                    validValues = false;
                                    break;
                                }
                                
                                values[column.name] = { type, group, instance };
                                break;
                            default:
                                // Skip unknown types (estimate 4 bytes)
                                logger.warn(`Unknown column type ${column.type} for column ${column.name}`);
                                reader.skip(4);
                                values[column.name] = `[Type${column.type}]`;
                                break;
                        }
                    } catch (valueError) {
                        logger.warn(`Error reading value for column ${column.name}: ${valueError}`);
                        validValues = false;
                        break;
                    }
                }
                
                // Only add instance if all values were read successfully
                if (validValues) {
                    instances.push({
                        name: instanceName,
                        instanceId: instanceId,
                        values: values
                    });
                } else {
                    // If we couldn't read all values, stop reading instances
                    break;
                }
            } catch (instanceError) {
                logger.error(`Error parsing instance ${i}: ${instanceError}`);
                break;
            }
        }
        
        return {
            schema: schema,
            instances: instances,
            version: version,
            flags: flags
        };
    } catch (error) {
        return handleSimDataError(
            error,
            createSimDataErrorContext(version, 0, 'parseStandardVersion'),
            undefined
        );
    }
}
