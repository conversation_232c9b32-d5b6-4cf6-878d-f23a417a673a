/**
 * State Manager
 * 
 * Manages application state throughout workflow execution,
 * providing consistent state tracking across CLI and future GUI interfaces.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { ScenarioDefinition, PlayerPersona, SystemState } from '../core/interfaces.js';

/**
 * Manages workflow execution state
 */
export class StateManager {
    private logger: Logger;
    private currentState: SystemState;
    private stateHistory: SystemState[];
    private workflowContext: any;

    constructor() {
        this.logger = new Logger('StateManager');
        this.currentState = this.createInitialState();
        this.stateHistory = [];
        this.workflowContext = {};
    }

    /**
     * Initialize state management for a workflow
     */
    async initialize(): Promise<void> {
        this.logger.info('Initializing State Manager...');
        
        // Reset state to initial values
        this.currentState = this.createInitialState();
        this.stateHistory = [];
        this.workflowContext = {};
        
        this.logger.info('State Manager initialized successfully');
    }

    /**
     * Initialize workflow-specific state
     */
    async initializeWorkflow(
        scenario: ScenarioDefinition,
        persona: PlayerPersona,
        options: any
    ): Promise<void> {
        this.logger.info(`Initializing workflow state for: ${scenario.metadata.name}`);

        // Store workflow context
        this.workflowContext = {
            scenario,
            persona,
            options,
            startTime: Date.now(),
            actionIndex: 0
        };

        // Update system state with workflow information
        this.currentState = {
            ...this.currentState,
            timestamp: Date.now(),
            userPreferences: {
                persona: persona.name,
                batchSize: persona.preferences.batchSize,
                timeoutTolerance: persona.preferences.timeoutTolerance,
                detailLevel: persona.preferences.detailLevel,
                autoResolveConflicts: persona.preferences.autoResolveConflicts
            }
        };

        // Save initial state to history
        this.saveStateSnapshot();
    }

    /**
     * Update state after action execution
     */
    async updateState(actionResult: any): Promise<void> {
        this.logger.debug(`Updating state after action: ${actionResult.actionId}`);

        // Update workflow context
        this.workflowContext.actionIndex++;
        this.workflowContext.lastActionResult = actionResult;

        // Update system state based on action result
        if (actionResult.stateChanges) {
            this.applyStateChanges(actionResult.stateChanges);
        }

        // Update performance metrics
        this.updatePerformanceMetrics(actionResult);

        // Update system health based on action success
        this.updateSystemHealth(actionResult);

        // Save state snapshot
        this.saveStateSnapshot();
    }

    /**
     * Get current system state
     */
    getCurrentState(): SystemState {
        return { ...this.currentState };
    }

    /**
     * Get workflow context
     */
    getWorkflowContext(): any {
        return { ...this.workflowContext };
    }

    /**
     * Get state history
     */
    getStateHistory(): SystemState[] {
        return [...this.stateHistory];
    }

    /**
     * Cleanup state management resources
     */
    async cleanup(): Promise<void> {
        this.logger.info('Cleaning up State Manager...');
        
        // Clear sensitive data
        this.workflowContext = {};
        
        // Keep only the last few state snapshots for analysis
        if (this.stateHistory.length > 10) {
            this.stateHistory = this.stateHistory.slice(-10);
        }
        
        this.logger.info('State Manager cleanup completed');
    }

    /**
     * Create initial system state
     */
    private createInitialState(): SystemState {
        const memUsage = process.memoryUsage();
        
        return {
            timestamp: Date.now(),
            modCollection: {
                totalMods: 0,
                categorizedMods: 0,
                conflictingMods: 0,
                enabledMods: 0
            },
            performance: {
                memoryUsage: memUsage.heapUsed,
                cpuUsage: 0,
                diskUsage: 0
            },
            userPreferences: {},
            systemHealth: {
                databaseIntegrity: true,
                fileSystemConsistency: true,
                configurationValid: true
            }
        };
    }

    /**
     * Apply state changes from action result
     */
    private applyStateChanges(stateChanges: Record<string, any>): void {
        // Apply mod collection changes
        if (stateChanges.modCollection) {
            this.currentState.modCollection = {
                ...this.currentState.modCollection,
                ...stateChanges.modCollection
            };
        }

        // Apply performance changes
        if (stateChanges.performance) {
            this.currentState.performance = {
                ...this.currentState.performance,
                ...stateChanges.performance
            };
        }

        // Apply user preference changes
        if (stateChanges.userPreferences) {
            this.currentState.userPreferences = {
                ...this.currentState.userPreferences,
                ...stateChanges.userPreferences
            };
        }

        // Apply system health changes
        if (stateChanges.systemHealth) {
            this.currentState.systemHealth = {
                ...this.currentState.systemHealth,
                ...stateChanges.systemHealth
            };
        }
    }

    /**
     * Update performance metrics
     */
    private updatePerformanceMetrics(actionResult: any): void {
        if (actionResult.metrics) {
            // Update memory usage
            if (actionResult.metrics.memoryUsed) {
                this.currentState.performance.memoryUsage += actionResult.metrics.memoryUsed;
            }

            // Update CPU usage (simplified)
            if (actionResult.metrics.cpuTime) {
                this.currentState.performance.cpuUsage = actionResult.metrics.cpuTime;
            }

            // Update disk usage (simplified)
            if (actionResult.metrics.ioOperations) {
                this.currentState.performance.diskUsage += actionResult.metrics.ioOperations;
            }
        }
    }

    /**
     * Update system health based on action results
     */
    private updateSystemHealth(actionResult: any): void {
        // If action failed, check what might be affected
        if (!actionResult.success) {
            // Check for database-related errors
            if (actionResult.errors.some((error: string) => 
                error.toLowerCase().includes('database') || 
                error.toLowerCase().includes('sql'))) {
                this.currentState.systemHealth.databaseIntegrity = false;
            }

            // Check for file system errors
            if (actionResult.errors.some((error: string) => 
                error.toLowerCase().includes('file') || 
                error.toLowerCase().includes('path') ||
                error.toLowerCase().includes('permission'))) {
                this.currentState.systemHealth.fileSystemConsistency = false;
            }

            // Check for configuration errors
            if (actionResult.errors.some((error: string) => 
                error.toLowerCase().includes('config') || 
                error.toLowerCase().includes('setting'))) {
                this.currentState.systemHealth.configurationValid = false;
            }
        }
    }

    /**
     * Save current state to history
     */
    private saveStateSnapshot(): void {
        const snapshot = {
            ...this.currentState,
            timestamp: Date.now()
        };

        this.stateHistory.push(snapshot);

        // Limit history size to prevent memory issues
        if (this.stateHistory.length > 100) {
            this.stateHistory = this.stateHistory.slice(-50);
        }
    }

    /**
     * Validate state consistency
     */
    async validateStateConsistency(): Promise<{
        consistent: boolean;
        issues: string[];
    }> {
        const issues: string[] = [];

        // Check mod collection consistency
        if (this.currentState.modCollection.categorizedMods > this.currentState.modCollection.totalMods) {
            issues.push('Categorized mods count exceeds total mods count');
        }

        if (this.currentState.modCollection.enabledMods > this.currentState.modCollection.totalMods) {
            issues.push('Enabled mods count exceeds total mods count');
        }

        // Check performance metrics
        if (this.currentState.performance.memoryUsage < 0) {
            issues.push('Negative memory usage detected');
        }

        if (this.currentState.performance.cpuUsage < 0) {
            issues.push('Negative CPU usage detected');
        }

        // Check timestamp consistency
        if (this.currentState.timestamp > Date.now() + 1000) {
            issues.push('Future timestamp detected');
        }

        return {
            consistent: issues.length === 0,
            issues
        };
    }

    /**
     * Generate state summary for debugging
     */
    generateStateSummary(): string {
        const summary = [
            '=== SYSTEM STATE SUMMARY ===',
            `Timestamp: ${new Date(this.currentState.timestamp).toISOString()}`,
            '',
            'Mod Collection:',
            `  Total: ${this.currentState.modCollection.totalMods}`,
            `  Categorized: ${this.currentState.modCollection.categorizedMods}`,
            `  Conflicting: ${this.currentState.modCollection.conflictingMods}`,
            `  Enabled: ${this.currentState.modCollection.enabledMods}`,
            '',
            'Performance:',
            `  Memory: ${this.formatBytes(this.currentState.performance.memoryUsage)}`,
            `  CPU: ${this.currentState.performance.cpuUsage}μs`,
            `  Disk: ${this.currentState.performance.diskUsage} ops`,
            '',
            'System Health:',
            `  Database: ${this.currentState.systemHealth.databaseIntegrity ? 'OK' : 'ERROR'}`,
            `  File System: ${this.currentState.systemHealth.fileSystemConsistency ? 'OK' : 'ERROR'}`,
            `  Configuration: ${this.currentState.systemHealth.configurationValid ? 'OK' : 'ERROR'}`,
            '',
            `State History: ${this.stateHistory.length} snapshots`,
            '=== END SUMMARY ==='
        ];

        return summary.join('\n');
    }

    /**
     * Format bytes for human-readable output
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    }
}
