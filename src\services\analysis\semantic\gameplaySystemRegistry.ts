/**
 * Gameplay System Registry
 *
 * Defines and manages Sims 4 gameplay systems for resource categorization
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { GameplaySystem, GameplaySystemCategorization } from './interfaces/gameplaySystem.js';
import { ALL_GAMEPLAY_SYSTEMS } from './constants/gameplaySystems.js';
import { SemanticDatabase } from './database/semanticDatabase.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';
import { injectable, singleton } from '../../di/decorators.js';

/**
 * Registry for Sims 4 gameplay systems
 */
@singleton()
export class GameplaySystemRegistry {
    private logger: Logger;
    private semanticDatabase: SemanticDatabase;
    private systems: Map<string, GameplaySystem> = new Map();
    private initialized: boolean = false;

    /**
     * Constructor
     * @param databaseService The database service
     * @param logger The logger instance
     */
    constructor(private databaseService: DatabaseService, logger?: Logger) {
        this.logger = logger || new Logger('GameplaySystemRegistry');
        this.semanticDatabase = new SemanticDatabase(databaseService, this.logger);
    }

    /**
     * Initialize the registry
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Initialize the semantic database
            await this.semanticDatabase.initialize();

            // Load all gameplay systems into memory
            for (const system of ALL_GAMEPLAY_SYSTEMS) {
                this.systems.set(system.id, system);

                // Save to database
                await this.semanticDatabase.saveGameplaySystem(system);
            }

            this.initialized = true;
            this.logger.info(`Initialized GameplaySystemRegistry with ${this.systems.size} systems`);
        } catch (error) {
            this.logger.error('Error initializing GameplaySystemRegistry:', error);
            throw error;
        }
    }

    /**
     * Get a gameplay system by ID
     * @param id The gameplay system ID
     * @returns The gameplay system or undefined if not found
     */
    public async getSystem(id: string): Promise<GameplaySystem | undefined> {
        await this.initialize();
        return this.systems.get(id);
    }

    /**
     * Get all gameplay systems
     * @returns Array of all gameplay systems
     */
    public async getAllSystems(): Promise<GameplaySystem[]> {
        await this.initialize();
        return Array.from(this.systems.values());
    }

    /**
     * Get gameplay systems by category
     * @param category The category to filter by
     * @returns Array of gameplay systems in the category
     */
    public async getSystemsByCategory(category: string): Promise<GameplaySystem[]> {
        await this.initialize();

        return Array.from(this.systems.values()).filter(system => {
            const metadata = system.metadata || {};
            return metadata.category === category;
        });
    }

    /**
     * Categorize a resource by gameplay system
     * @param resourceKey The resource key
     * @param resourceMetadata The resource metadata
     * @param resourceContent Optional resource content for deeper analysis
     * @returns Gameplay system categorization result
     */
    public async categorizeResource(
        resourceKey: ResourceKey,
        resourceMetadata: Record<string, any>,
        resourceContent?: string
    ): Promise<GameplaySystemCategorization> {
        await this.initialize();

        // Start with default categorization
        const categorization: GameplaySystemCategorization = {
            primarySystem: 'unknown',
            primaryConfidence: 0,
            secondarySystems: [],
            explanation: 'No matching gameplay system found',
            timestamp: Date.now()
        };

        try {
            // Get resource type as hex string
            const resourceType = resourceKey?.type;
            if (resourceType === undefined) {
                this.logger.warn('Resource type is undefined');
                return categorization;
            }
            const resourceTypeHex = `0x${resourceType.toString(16).toUpperCase().padStart(8, '0')}`;

            // Track system matches and their confidence scores
            const systemMatches: Map<string, number> = new Map();
            let matchExplanations: string[] = [];

            // Step 1: Check if resource type directly matches any system's related resource types
            for (const system of this.systems.values()) {
                if (system.relatedResourceTypes.includes(resourceTypeHex)) {
                    const baseScore = 20; // Base score for resource type match
                    systemMatches.set(system.id, (systemMatches.get(system.id) || 0) + baseScore);
                    matchExplanations.push(`Resource type ${resourceTypeHex} is associated with ${system.name} system`);
                }
            }

            // Step 2: Check resource name/description for keyword matches
            const resourceName = resourceMetadata.name || '';
            const resourceDescription = resourceMetadata.description || '';
            const contentToCheck = `${resourceName} ${resourceDescription} ${resourceContent || ''}`.toLowerCase();

            for (const system of this.systems.values()) {
                let keywordMatches = 0;
                const matchedKeywords: string[] = [];

                for (const keyword of system.keywords) {
                    if (contentToCheck.includes(keyword.toLowerCase())) {
                        keywordMatches++;
                        matchedKeywords.push(keyword);
                    }
                }

                if (keywordMatches > 0) {
                    // Score based on number of keyword matches and their specificity
                    const keywordScore = keywordMatches * 10;
                    systemMatches.set(system.id, (systemMatches.get(system.id) || 0) + keywordScore);
                    matchExplanations.push(`Found ${keywordMatches} keyword matches for ${system.name} system: ${matchedKeywords.join(', ')}`);
                }
            }

            // Step 3: Check for specific resource type patterns
            if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_TUNING) {
                // For tuning XML, check the tuning type
                const tuningType = resourceMetadata.tuningType || '';

                if (tuningType) {
                    for (const system of this.systems.values()) {
                        if (system.keywords.some(k => tuningType.toLowerCase().includes(k.toLowerCase()))) {
                            systemMatches.set(system.id, (systemMatches.get(system.id) || 0) + 30);
                            matchExplanations.push(`Tuning type "${tuningType}" matches ${system.name} system`);
                        }
                    }
                }
            } else if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_SCRIPT) {
                // For scripts, check script metadata
                const modType = resourceMetadata.modType || '';
                const modCategory = resourceMetadata.modCategory || '';

                if (modType || modCategory) {
                    for (const system of this.systems.values()) {
                        if (system.keywords.some(k =>
                            modType.toLowerCase().includes(k.toLowerCase()) ||
                            modCategory.toLowerCase().includes(k.toLowerCase())
                        )) {
                            systemMatches.set(system.id, (systemMatches.get(system.id) || 0) + 40);
                            matchExplanations.push(`Script type "${modType || modCategory}" matches ${system.name} system`);
                        }
                    }
                }
            }

            // Convert matches to sorted array
            const sortedMatches = Array.from(systemMatches.entries())
                .sort((a, b) => b[1] - a[1]);

            // If we have matches, update the categorization
            if (sortedMatches.length > 0) {
                const [primarySystemId, primaryScore] = sortedMatches[0];
                const primarySystem = this.systems.get(primarySystemId);

                if (primarySystem) {
                    // Normalize confidence to 0-100 scale
                    const normalizedConfidence = Math.min(100, primaryScore);

                    categorization.primarySystem = primarySystem.id;
                    categorization.primaryConfidence = normalizedConfidence;
                    categorization.explanation = matchExplanations.join('. ');

                    // Add secondary systems
                    categorization.secondarySystems = sortedMatches
                        .slice(1, 4) // Take up to 3 secondary systems
                        .map(([id, score]) => ({
                            system: id,
                            confidence: Math.min(100, score)
                        }));
                }
            }

            return categorization;
        } catch (error) {
            this.logger.error(`Error categorizing resource:`, error);
            return categorization;
        }
    }

    /**
     * Register a custom gameplay system
     * @param system The gameplay system to register
     */
    public async registerSystem(system: GameplaySystem): Promise<void> {
        await this.initialize();

        try {
            // Add to memory cache
            this.systems.set(system.id, system);

            // Save to database
            await this.semanticDatabase.saveGameplaySystem(system);

            this.logger.info(`Registered custom gameplay system: ${system.id}`);
        } catch (error) {
            this.logger.error(`Error registering custom gameplay system ${system.id}:`, error);
            throw error;
        }
    }

    /**
     * Save a gameplay system categorization to the database
     * @param categorization The gameplay system categorization
     * @param resourceId The resource ID
     * @returns The ID of the saved categorization
     */
    public async saveCategorization(
        categorization: GameplaySystemCategorization,
        resourceId: number
    ): Promise<number> {
        await this.initialize();
        return this.semanticDatabase.saveGameplaySystemCategorization(categorization, resourceId);
    }

    /**
     * Get gameplay system categorization for a resource
     * @param resourceId The resource ID
     * @returns The gameplay system categorization or undefined if not found
     */
    public async getCategorization(resourceId: number): Promise<GameplaySystemCategorization | undefined> {
        await this.initialize();
        return this.semanticDatabase.getGameplaySystemCategorization(resourceId);
    }

    /**
     * Dispose of resources used by the registry
     */
    public async dispose(): Promise<void> {
        try {
            this.logger.info('Disposing GameplaySystemRegistry resources');

            // Clear systems map
            this.systems.clear();

            this.initialized = false;
            this.logger.info('GameplaySystemRegistry resources disposed successfully');
        } catch (error) {
            this.logger.error('Error disposing GameplaySystemRegistry resources:', error);
            throw error;
        }
    }
}
