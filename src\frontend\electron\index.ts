﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿// Removed duplicate global declaration - now in src/types/electron.d.ts

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import { useAnalysisStore } from './store/analysis'; // Import the store

// Vuetify imports
import 'vuetify/styles'; // Import Vuetify styles
import { createVuetify } from 'vuetify';
import * as components from 'vuetify/components';
import * as directives from 'vuetify/directives';

// Attempting to remove potentially hidden/stale PrimeVue CSS imports
// import 'primevue/resources/themes/lara-light-indigo/theme.css'; // REMOVE IF EXISTS
// import 'primevue/resources/primevue.min.css'; // REMOVE IF EXISTS
// import 'primeicons/primeicons.css'; // REMOVE IF EXISTS

// Removed MUI imports
// import {
//   MuiButton,
//   MuiContainer,
//   MuiTypography,
// } from './material-ui';

// Import global styles if needed
// import '../styles/main.css';


// Create Vue App using the actual App component
const app = createApp(App);

// Setup Pinia store
const pinia = createPinia();
app.use(pinia);

// Create Vuetify instance
const vuetify = createVuetify({
  components,
  directives,
  // Add any theme customization here if needed
});

// Use Vuetify
app.use(vuetify);

// Removed MUI component registrations
// app.component('MuiButton', MuiButton);
// app.component('MuiContainer', MuiContainer);
// app.component('MuiTypography', MuiTypography);

// Restore mount
app.mount('#app');

console.log('Electron entry point loaded and Vue app mounted.');

// Send a test ping immediately after mount
if (window.electronAPI?.sendPing) {
  console.log('Sending initial test ping from index.ts...');
  window.electronAPI.sendPing('Ping from index.ts');
} else {
  console.error('window.electronAPI.sendPing not available immediately in index.ts');
}

// Listener initialization moved to App.vue's onMounted hook
