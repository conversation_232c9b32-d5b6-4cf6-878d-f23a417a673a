# S4TK Package: @s4tk/models

Based on documentation found in `docs/technical/models/`.

## Overview

This is the central S4TK package containing the primary models (classes), enums, and interfaces for representing and interacting with Sims 4 package files (DBPF) and the various resource types they contain.

## Installation

```sh
npm i @s4tk/models
```

## Key Concepts

*   **Package (`Package` class):** Represents a DBPF file. Holds a collection of resource entries. Provides static methods for reading package data from buffers (`from`, `extractResources`) and potentially from files via plugins (`streamResources`, `indexResources`, `fetchResources`). Also supports merging packages.
*   **Resource (`Resource` interface):** Base interface for all resource types. Defines `encodingType` and common methods like `clone()`, `isXml()`, `toBuffer()`.
*   **Resource Models:** Specific classes implement the `Resource` interface for known types (e.g., `XmlResource`, `SimDataResource`, `StringTableResource`, `ObjectDefinitionResource`, `DdsImageResource`). These models parse the resource's buffer and provide structured access to its content.
*   **RawResource:** A fallback model used for resource types that S4TK doesn't have a specific parser for. It holds the raw buffer without interpretation.
*   **ResourceKey:** Interface `{ type: number; group: bigint; instance: bigint; }` used to uniquely identify resources.
*   **Enums:** Provides various enums for standardized values:
    *   `BinaryResourceType`: Defines numeric IDs for known resource types (CASPart, Tuning, SimData, STBL, DDS, etc.). *Note: The definition in `lib/enums/binary-resources.d.ts` seems more complete than the one previously found in the project's `src/types/resource/core.ts`.*
    *   `EncodingType`: String enum identifying the format of a resource (`XML`, `STBL`, `DDS`, `DATA`, `OBJDEF`, `Unknown`, etc.).
    *   `DataType`: Defines primitive and complex types within SimData resources.
    *   `SimDataGroup`: Defines group IDs often associated with specific SimData tuning types.
    *   `StringTableLocale`: Defines locales and helpers for STBL instance IDs.
    *   `TuningResourceType`: Defines specific types for Tuning XML files based on content.
*   **Plugins:** Supports a plugin system (`registerPlugin`) for extending functionality, notably used by `@s4tk/plugin-bufferfromfile` for efficient file reading (which was found to be incompatible in previous sessions).

## Resource Type Support Summary

Based on the examined files:

*   **XML/Tuning (`XmlResource`):** Parses using `@s4tk/xml-dom`, provides access via DOM object (`.dom`) or string (`.content`). Expects uncompressed buffer.
*   **SimData (`SimDataResource`):** Parses binary SimData or S4S-style XML. Provides structured access via `.instance.props` or a simplified DTO via `.getDto()`. Expects uncompressed buffer for binary parsing.
*   **String Table (`StringTableResource`):** Parses binary STBL. Stores entries in a map-like structure accessible via key hash. Provides helpers for adding entries. Expects uncompressed buffer.
*   **Object Definition (`ObjectDefinitionResource`):** Parses binary OBJDEF. Provides structured access via `.properties` object (includes name key, price, etc.). Expects uncompressed buffer.
*   **DDS/DST Image (`DdsImageResource`):** Wraps a `DdsImage` object (from `@s4tk/images`). Parses binary DDS/DST data. Allows conversion to/from other formats via Jimp. Expects uncompressed buffer.
*   **Raw (`RawResource`):** Fallback for unrecognized types. Holds the raw buffer.
*   **CAS Part (`CasPartResource`):** While listed in the `BinaryResourceType` enum, **no corresponding resource model definition (`CasPartResource.d.ts`) was found** in the examined `docs/technical/models` files. The attempt to import it during previous coding steps also failed at runtime, suggesting it's not exported or fully implemented in the installed version (`^0.6.14`). Therefore, detailed CASP parsing using this model is currently not possible based on this documentation.

## Important Notes

*   The library generally expects **uncompressed** resource buffers for its `from()` methods. Decompression (using `@s4tk/compression` or other means) must happen first if reading directly from a package.
*   The `Package.extractResources()` method (used in our `s4tkReader.ts`) handles decompression internally when reading from a package buffer.
*   Efficient file-based reading methods (`streamResources`, `indexResources`, `fetchResources`) depend on the native plugin `@s4tk/plugin-bufferfromfile`.