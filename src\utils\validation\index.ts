﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿/**
 * Validation utilities index file
 * Exports all validation-related utility classes and functions
 */

export * from './SharedValidationUtils.js'; // Added .js
export * from './packageValidation.js'; // Added .js
export * from './resourceValidation.js'; // Added .js
export * from './typeValidation.js'; // Added .js

// Re-export commonly used types
export type { PackageValidationResult as ValidationResult } from '../../types/resource/Package.js'; // Corrected type name and path, added alias, added .js
