/**
 * Safe File Stream Utilities
 * 
 * This module provides utilities for working with file streams in a memory-efficient
 * and leak-free way. It ensures proper cleanup of resources and event listeners.
 */

import fs from 'fs';
import { createHash } from 'crypto';
import { Logger } from '../logging/logger.js';
import { promisify } from 'util';
import { pipeline, Readable, Writable } from 'stream';
import path from 'path';

// Create a logger for this module
const logger = new Logger('SafeFileStream');

// Promisify pipeline for easier use with async/await
const pipelineAsync = promisify(pipeline);

/**
 * Creates a safe read stream that automatically cleans up event listeners
 * @param filePath Path to the file
 * @param options Read stream options
 * @returns A read stream with automatic cleanup
 */
export function createSafeReadStream(filePath: string, options?: any): fs.ReadStream {
    const stream = fs.createReadStream(filePath, options);
    const fileName = path.basename(filePath);
    
    // Keep track of attached listeners for proper cleanup
    const listeners: { event: string, handler: (...args: any[]) => void }[] = [];
    
    // Create a cleanup function
    const cleanup = () => {
        // Remove all tracked listeners
        for (const { event, handler } of listeners) {
            stream.removeListener(event, handler);
        }
        
        // Close the stream if it's still open
        if (!stream.destroyed) {
            stream.destroy();
        }
    };
    
    // Override the original addListener/on method to track listeners
    const originalAddListener = stream.addListener.bind(stream);
    stream.addListener = stream.on = function(event: string, listener: (...args: any[]) => void): fs.ReadStream {
        listeners.push({ event, handler: listener });
        return originalAddListener(event, listener);
    };
    
    // Add our own listeners for automatic cleanup
    stream.once('end', cleanup);
    stream.once('close', cleanup);
    stream.once('error', (err) => {
        logger.debug(`Error in read stream for ${fileName}: ${err.message}`);
        cleanup();
    });
    
    return stream;
}

/**
 * Creates a safe write stream that automatically cleans up event listeners
 * @param filePath Path to the file
 * @param options Write stream options
 * @returns A write stream with automatic cleanup
 */
export function createSafeWriteStream(filePath: string, options?: any): fs.WriteStream {
    const stream = fs.createWriteStream(filePath, options);
    const fileName = path.basename(filePath);
    
    // Keep track of attached listeners for proper cleanup
    const listeners: { event: string, handler: (...args: any[]) => void }[] = [];
    
    // Create a cleanup function
    const cleanup = () => {
        // Remove all tracked listeners
        for (const { event, handler } of listeners) {
            stream.removeListener(event, handler);
        }
        
        // Close the stream if it's still open
        if (!stream.destroyed) {
            stream.destroy();
        }
    };
    
    // Override the original addListener/on method to track listeners
    const originalAddListener = stream.addListener.bind(stream);
    stream.addListener = stream.on = function(event: string, listener: (...args: any[]) => void): fs.WriteStream {
        listeners.push({ event, handler: listener });
        return originalAddListener(event, listener);
    };
    
    // Add our own listeners for automatic cleanup
    stream.once('finish', cleanup);
    stream.once('close', cleanup);
    stream.once('error', (err) => {
        logger.debug(`Error in write stream for ${fileName}: ${err.message}`);
        cleanup();
    });
    
    return stream;
}

/**
 * Calculate a hash for a file using streaming to avoid loading the entire file into memory
 * 
 * @param filePath Path to the file
 * @param algorithm Hash algorithm to use (default: 'md5')
 * @returns Promise resolving to the hash string
 */
export async function calculateFileHash(filePath: string, algorithm: string = 'md5'): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        const hash = createHash(algorithm);
        const stream = createSafeReadStream(filePath);
        
        // Handle stream events
        stream.on('data', (data) => {
            hash.update(data);
        });
        
        stream.on('end', () => {
            const hashValue = hash.digest('hex');
            resolve(hashValue);
        });
        
        stream.on('error', (err) => {
            reject(err);
        });
    });
}

/**
 * Read a file completely into a buffer with proper stream handling
 * 
 * @param filePath Path to the file
 * @returns Promise resolving to the file buffer
 */
export async function readFileToBuffer(filePath: string): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        const stream = createSafeReadStream(filePath);
        
        // Handle stream events
        stream.on('data', (chunk) => {
            chunks.push(chunk);
        });
        
        stream.on('end', () => {
            const buffer = Buffer.concat(chunks);
            resolve(buffer);
        });
        
        stream.on('error', (err) => {
            reject(err);
        });
    });
}

/**
 * Copy a file using streams with proper cleanup
 * 
 * @param sourcePath Source file path
 * @param destinationPath Destination file path
 * @returns Promise that resolves when the copy is complete
 */
export async function copyFileWithStreams(sourcePath: string, destinationPath: string): Promise<void> {
    const readStream = createSafeReadStream(sourcePath);
    const writeStream = createSafeWriteStream(destinationPath);
    
    try {
        // Use pipeline to handle errors and cleanup properly
        await pipelineAsync(readStream, writeStream);
    } catch (error) {
        throw error;
    }
}

/**
 * Read a file in chunks and process each chunk
 * 
 * @param filePath Path to the file
 * @param chunkProcessor Function to process each chunk
 * @param chunkSize Size of each chunk in bytes (default: 64KB)
 * @returns Promise that resolves when processing is complete
 */
export async function processFileInChunks(
    filePath: string, 
    chunkProcessor: (chunk: Buffer) => void | Promise<void>,
    chunkSize: number = 64 * 1024
): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        const stream = createSafeReadStream(filePath, { highWaterMark: chunkSize });
        
        // Handle stream events
        stream.on('data', async (chunk) => {
            try {
                // Pause the stream while processing the chunk
                stream.pause();
                
                // Process the chunk
                await Promise.resolve(chunkProcessor(chunk));
                
                // Resume the stream if it's still active
                if (stream.readable) {
                    stream.resume();
                }
            } catch (error) {
                reject(error);
            }
        });
        
        stream.on('end', () => {
            resolve();
        });
        
        stream.on('error', (err) => {
            reject(err);
        });
    });
}
