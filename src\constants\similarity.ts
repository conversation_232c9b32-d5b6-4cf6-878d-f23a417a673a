export interface SimilarityResult {
  score: number;
  isSimilar: boolean;
  confidence: number;
  details: {
    exactMatch: boolean;
    partialMatch: boolean;
    fuzzyMatch: boolean;
    matchType: 'exact' | 'partial' | 'fuzzy' | 'none';
    matchedParts: string[];
    unmatchedParts: string[];
  };
}

export const SIMILARITY_THRESHOLDS = {
  EXACT_MATCH: 1.0,
  HIGH_SIMILARITY: 0.9,
  MEDIUM_SIMILARITY: 0.7,
  LOW_SIMILARITY: 0.5
} as const;

export const NAME_PATTERNS = {
  VARIANT: /^(.*?)(?:_v\d+|\d+)?$/, // Matches base name with optional version suffix
  VERSION: /_v(\d+)$/, // Matches version number suffix
  TYPE: /^(.+?)_(?:v\d+|\d+)?$/, // Matches type prefix
  ID: /^(.+?)_(\d+)$/ // Matches ID suffix
} as const;

export const SIMILARITY_WEIGHTS = {
  EXACT_MATCH: 1.0,
  PARTIAL_MATCH: 0.8,
  FUZZY_MATCH: 0.6,
  NO_MATCH: 0.0
} as const;

export const SIMILARITY_CONSTANTS = {
  SIMILARITY_THRESHOLD: 0.8,
  NAME_SIMILARITY_THRESHOLD: 0.7,
  VERSION_SIMILARITY_THRESHOLD: 0.9,
  COMMON_VARIANTS: [
    {
      name: 'version',
      variants: ['v', 'version', 'ver', 'rev']
    },
    {
      name: 'update',
      variants: ['update', 'upd', 'patch', 'fix']
    },
    {
      name: 'color',
      variants: ['color', 'colour', 'clr']
    },
    {
      name: 'style',
      variants: ['style', 'stl', 'variant', 'var']
    }
  ]
};

export const CONFLICT_SIMILARITY_CONSTANTS = {
  SIMILARITY_THRESHOLD: 0.8,
  NAME_SIMILARITY_THRESHOLD: 0.9,
  VERSION_SIMILARITY_THRESHOLD: 0.7,
  COMMON_VARIANTS: [
    {
      base: 'recolor',
      variants: ['recolour', 'color', 'colour']
    },
    {
      base: 'default',
      variants: ['def', 'dflt']
    },
    {
      base: 'replacement',
      variants: ['replace', 'repl']
    }
  ]
};