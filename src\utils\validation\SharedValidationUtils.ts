/**
 * SharedValidationUtils - Core validation utility functions shared across the application
 *
 * This class provides essential validation functions for CAS parts and other resources
 * that are used by both the core application and demo components. It serves as the
 * single source of truth for validation operations to reduce redundancy and ensure consistency.
 */
export class SharedValidationUtils {
  /**
   * Validates if a value is a valid color component (0-255)
   * @param value The value to validate
   * @returns True if the value is a valid color component, false otherwise
   */
  public static isValidColorComponent(value: number): boolean {
    return Number.isInteger(value) && value >= 0 && value <= 255;
  }

  /**
   * Validates if a value is a valid part type
   * @param value The value to validate
   * @returns True if the value is a valid part type, false otherwise
   */
  public static isValidPartType(value: number): boolean {
    // Valid part type values are typically in a specific range
    // This is a simplified version - in a real implementation, you would check against
    // a list of known valid part types from constants
    return Number.isInteger(value) && value >= 0 && value <= 50;
  }

  /**
   * Validates if a value is a valid body part
   * @param value The value to validate
   * @returns True if the value is a valid body part, false otherwise
   */
  public static isValidBodyPart(value: number): boolean {
    // Valid body part values are typically in a specific range
    // This is a simplified version - in a real implementation, you would check against
    // a list of known valid body parts from constants
    return Number.isInteger(value) && value >= 0 && value <= 100;
  }

  /**
   * Validates if a value is a valid age
   * @param value The value to validate
   * @returns True if the value is a valid age, false otherwise
   */
  public static isValidAge(value: number): boolean {
    // Valid age values are typically specific bit flags
    // This is a simplified version - in a real implementation, you would check against
    // a list of known valid age flags from constants
    return Number.isInteger(value) && value >= 0;
  }

  /**
   * Validates if a value is a valid gender
   * @param value The value to validate
   * @returns True if the value is a valid gender, false otherwise
   */
  public static isValidGender(value: number): boolean {
    // Valid gender values are typically specific bit flags
    // This is a simplified version - in a real implementation, you would check against
    // a list of known valid gender flags from constants
    return Number.isInteger(value) && value >= 0;
  }

  /**
   * Validates if a buffer has a minimum length
   * @param buffer The buffer to validate
   * @param minLength The minimum length required
   * @returns True if the buffer has at least the minimum length, false otherwise
   */
  public static hasMinimumLength(buffer: Buffer, minLength: number): boolean {
    return buffer && buffer.length >= minLength;
  }

  /**
   * Validates if a value is within a valid range
   * @param value The value to validate
   * @param min The minimum valid value
   * @param max The maximum valid value
   * @returns True if the value is within the valid range, false otherwise
   */
  public static isInRange(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }

  /**
   * Validates if a value is a valid hex color
   * @param value The value to validate
   * @returns True if the value is a valid hex color, false otherwise
   */
  public static isValidHexColor(value: string): boolean {
    // Check if the value is a valid hex color (with or without #)
    return /^#?([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/.test(value);
  }

  /**
   * Validates if a value is a valid RGB color object
   * @param value The value to validate
   * @returns True if the value is a valid RGB color object, false otherwise
   */
  public static isValidRgbColor(value: unknown): boolean {
    return (
      value !== null &&
      typeof value === 'object' &&
      'r' in value &&
      'g' in value &&
      'b' in value &&
      this.isValidColorComponent((value as Record<string, number>).r) &&
      this.isValidColorComponent((value as Record<string, number>).g) &&
      this.isValidColorComponent((value as Record<string, number>).b)
    );
  }

  /**
   * Validates if a value is a valid HSL color object
   * @param value The value to validate
   * @returns True if the value is a valid HSL color object, false otherwise
   */
  public static isValidHslColor(value: unknown): boolean {
    return (
      value !== null &&
      typeof value === 'object' &&
      'h' in value &&
      's' in value &&
      'l' in value &&
      this.isInRange((value as Record<string, number>).h, 0, 1) &&
      this.isInRange((value as Record<string, number>).s, 0, 1) &&
      this.isInRange((value as Record<string, number>).l, 0, 1)
    );
  }

  /**
   * Validates if a value is a valid TGI reference
   * @param value The value to validate
   * @returns True if the value is a valid TGI reference, false otherwise
   */
  public static isValidTgiReference(value: unknown): boolean {
    return (
      value !== null &&
      typeof value === 'object' &&
      'type' in value &&
      'group' in value &&
      'instance' in value &&
      Number.isInteger((value as Record<string, unknown>).type as number) &&
      Number.isInteger((value as Record<string, unknown>).group as number) &&
      (typeof (value as Record<string, unknown>).instance === 'string' ||
        typeof (value as Record<string, unknown>).instance === 'bigint' ||
        typeof (value as Record<string, unknown>).instance === 'number')
    );
  }

  public static isNumber(value: unknown): value is number {
    return typeof value === 'number' && !isNaN(value);
  }

  public static isString(value: unknown): value is string {
    return typeof value === 'string';
  }
}
