# S4TK Package: @s4tk/hashing

Based on documentation found in `docs/technical/hashing/`.

## Overview

This package provides utility functions for:

1.  Calculating FNV-1 hashes (FNV1a variant) for strings, matching the algorithm used by The Sims 4.
2.  Formatting numeric values (resource IDs, hashes) into standardized hexadecimal string representations.

## Installation

```sh
npm i @s4tk/hashing
```

## API

### Hashing Functions

These functions calculate FNV-1a hashes for input strings.

*   `fnv24(value: string): number`: Returns the 24-bit FNV-1a hash.
*   `fnv32(value: string, highBit?: boolean): number`: Returns the 32-bit FNV-1a hash. If `highBit` is true, the most significant bit is forced to 1.
*   `fnv56(value: string): bigint`: Returns the 56-bit FNV-1a hash.
*   `fnv64(value: string, highBit?: boolean): bigint`: Returns the 64-bit FNV-1a hash. If `highBit` is true, the most significant bit is forced to 1.

### Hash Conversion Functions

*   `fnv32to24(hash: number): number`: Converts a 32-bit hash to 24-bit.
*   `fnv64to56(hash: bigint): bigint`: Converts a 64-bit hash to 56-bit.

### Formatting Functions

These functions format numbers or BigInts into hexadecimal strings, often used for resource keys.

*   `formatStringKey(key: number): string`: Formats a 32-bit number as `0x########`. (Typically for STBL keys).
*   `formatResourceType(value: number): string`: Formats a 32-bit number as `########`.
*   `formatResourceGroup(value: number): string`: Formats a 32-bit number as `########`.
*   `formatResourceInstance(value: bigint): string`: Formats a 64-bit BigInt as `################`.
*   `formatResourceTGI(type: number, group: number, instance: bigint, delimeter: string = ':'): string`: Formats Type, Group, and Instance into a single string (e.g., `TTTTTTTT:GGGGGGGG:IIIIIIIIIIIIIIII`).
*   `formatResourceKey(key: { type: number; group: number; instance: bigint }, delimeter: string = ':'): string`: Formats a key object using `formatResourceTGI`.
*   `formatAsHexString(value: number | bigint, digits: number, usePrefix: boolean = false): string`: General utility to format a number/bigint as hex, padded with leading zeros to `digits` length, optionally adding a `0x` prefix.