// Corrected imports
import { BinaryResourceType } from '../../types/resource/core.js';
import { ResourceKey, ResourceMetadata } from '../../types/resource/interfaces.js'; // Corrected path
import { RESOURCE_METADATA_REGISTRY, getResourceMetadata } from '../../types/resource/ResourceMetadataRegistry.js';

export class ResourceTypeResolver {
  private static instance: ResourceTypeResolver;
  // Removed unused metadataCache

  private constructor() {
    // Removed cache initialization
  }

  public static getInstance(): ResourceTypeResolver {
    if (!ResourceTypeResolver.instance) {
      ResourceTypeResolver.instance = new ResourceTypeResolver();
    }
    return ResourceTypeResolver.instance;
  }

  // Removed redundant resolveType method
  // Removed unused getCachedMetadata method

  public getTypeDescription(type: number): string {
    const metadata = getResourceMetadata(type as any); // Convert to OfficialResourceType
    return metadata?.description || 'Unknown Resource Type';
  }
}
