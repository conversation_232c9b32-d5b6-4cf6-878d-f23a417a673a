.file-upload-container {
  width: 100%;
  max-width: 600px;
  margin: 2rem auto;
  padding: 1rem;
}

.file-drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-drop-zone:hover {
  border-color: #007bff;
  background-color: #f1f3f5;
}

.file-drop-zone.dragover {
  border-color: #28a745;
  background-color: #e9ecef;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.drop-zone-content i {
  font-size: 3rem;
  color: #6c757d;
}

.drop-zone-content p {
  margin: 0;
  color: #495057;
}

.file-types {
  font-size: 0.9rem;
  color: #6c757d;
}

.file-list {
  margin-top: 1rem;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.file-name {
  flex: 1;
  margin-right: 1rem;
  font-weight: 500;
}

.file-size {
  color: #6c757d;
  font-size: 0.9rem;
  margin-right: 1rem;
}

.remove-file {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 0.25rem;
  transition: color 0.2s ease;
}

.remove-file:hover {
  color: #c82333;
}

.file-error {
  color: #dc3545;
  padding: 0.5rem;
  margin-top: 0.5rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  font-size: 0.9rem;
} 