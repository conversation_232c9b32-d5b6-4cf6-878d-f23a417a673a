/**
 * Shared types and interfaces for SimData version handling
 */

import { SimDataSchema, SimDataInstance } from '../simDataTypes.js';

/**
 * Interface for parsed SimData
 */
export interface ParsedSimData {
    schema?: SimDataSchema;
    instances: SimDataInstance[];
    version?: number;
    flags?: number;
}

/**
 * Interface for a version handler function
 */
export type VersionHandlerFunction = (buffer: Buffer) => ParsedSimData | undefined;

/**
 * Interface for version handler registration
 */
export interface VersionHandlerRegistration {
    version: number;
    handler: VersionHandlerFunction;
    isStandard: boolean;
    isSpecial: boolean;
    description?: string;
}

/**
 * Interface for version detection result
 */
export interface VersionDetectionResult {
    version: number;
    confidence: 'high' | 'medium' | 'low';
    formatValid: boolean;
    possibleSchema?: string;
}

/**
 * Interface for version statistics
 */
export interface VersionStatistics {
    totalVersions: number;
    standardVersions: number;
    specialVersions: number;
    unknownVersions: number;
    mostCommonVersion: number;
    mostCommonCount: number;
    recentlyDiscovered: VersionInfo[];
}

/**
 * Interface for version information
 */
export interface VersionInfo {
    version: number;
    count: number;
    firstSeen: Date;
    lastSeen: Date;
    schemaNames: Set<string>;
    modNames: Set<string>;
    isStandard: boolean;
    isSpecial: boolean;
    hasCustomHandler: boolean;
}

/**
 * SimData version categories
 */
export enum VersionCategory {
    STANDARD = 'standard',
    SPECIAL = 'special',
    MOD = 'mod',
    EXPERIMENTAL = 'experimental',
    UNKNOWN = 'unknown'
}

/**
 * Get the category for a SimData version
 * @param version SimData version
 * @returns Version category
 */
export function getVersionCategory(version: number): VersionCategory {
    if (version >= 1 && version <= 20) {
        return VersionCategory.STANDARD;
    } else if (version === 16708 || version === 48111) {
        return VersionCategory.SPECIAL;
    } else if ([12345, 21324, 32768, 42069, 50000, 54321, 60000].includes(version)) {
        return VersionCategory.MOD;
    } else if ([65280, 65290, 65500, 65535].includes(version)) {
        return VersionCategory.EXPERIMENTAL;
    } else {
        return VersionCategory.UNKNOWN;
    }
}
