/**
 * WorkerManager - Core worker coordination system for multi-threaded package analysis
 * 
 * This implements the superior pattern from the comprehensive implementation plan:
 * - Worker pool management for parallel processing
 * - Task distribution and progress reporting
 * - Memory-aware processing with adaptive batch sizing
 * - Integration with existing S4TK and database systems
 * 
 * Target Performance Improvements:
 * - Analysis time: 3+ minutes → <1 minute for 30 mods (70% reduction)
 * - CPU utilization: >80% across all cores
 * - Memory per worker: <200MB
 */

import { WorkerPool, Task, TaskType, TaskPriority, TaskResult } from '../utils/parallel/workerPool.js';
import { Logger } from '../utils/logging/logger.js';
import { DatabaseService } from '../services/databaseService.js';
import EnhancedMemoryManager from '../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker } from '../utils/memory/resourceTracker.js';
import { formatBytes, formatDuration } from '../utils/formatting/formatUtils.js';
import { EventEmitter } from 'events';
import { cpus } from 'os';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Create a logger for this module
const logger = new Logger('WorkerManager');

/**
 * Analysis task data structure
 */
export interface AnalysisTask {
    id: string;
    packagePath: string;
    options: {
        batchSize?: number;
        maxConcurrentResources?: number;
        analyzeDependencies?: boolean;
        includeGameplayAnalysis?: boolean;
        trackPerformance?: boolean;
    };
    priority: TaskPriority;
    createdAt: number;
}

/**
 * Analysis job tracking
 */
export interface AnalysisJob {
    id: string;
    tasks: AnalysisTask[];
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    startTime: number;
    endTime?: number;
    results: any[];
    errors: string[];
    progressCallback?: (progress: JobProgress) => void;
}

/**
 * Job progress information
 */
export interface JobProgress {
    jobId: string;
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    progress: number; // 0-100
    estimatedTimeRemaining: number; // milliseconds
    currentTask?: string;
    memoryUsage: {
        heapUsed: number;
        heapTotal: number;
        pressure: number;
    };
}

/**
 * Worker manager options
 */
export interface WorkerManagerOptions {
    maxWorkers?: number;
    minWorkers?: number;
    workerIdleTimeout?: number;
    taskTimeout?: number;
    memoryThreshold?: number; // Memory pressure threshold (0-1)
    adaptiveBatching?: boolean;
    enableProgressReporting?: boolean;
    databasePath?: string;
}

/**
 * WorkerManager class for coordinating parallel package analysis
 */
export class WorkerManager extends EventEmitter {
    private static instance: WorkerManager;
    private workerPool!: WorkerPool;
    private memoryManager!: EnhancedMemoryManager;
    private resourceTracker!: ResourceTracker;
    private databaseService!: DatabaseService;
    private options: WorkerManagerOptions;
    private activeJobs = new Map<string, AnalysisJob>();
    private isInitialized = false;
    
    // Performance tracking
    private stats = {
        totalJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        totalPackagesProcessed: 0,
        totalProcessingTime: 0,
        averagePackagesPerSecond: 0,
        peakMemoryUsage: 0,
        workerUtilization: new Map<number, number>()
    };

    /**
     * Private constructor to enforce Singleton pattern
     */
    private constructor(options: WorkerManagerOptions = {}) {
        super();
        this.setMaxListeners(100);
        
        // Set default options
        this.options = {
            maxWorkers: Math.max(1, cpus().length - 1), // Leave one CPU for main thread
            minWorkers: 1,
            workerIdleTimeout: 60000, // 1 minute
            taskTimeout: 120000, // 2 minutes for package analysis
            memoryThreshold: 0.8, // 80% memory pressure threshold
            adaptiveBatching: true,
            enableProgressReporting: true,
            ...options
        };

        logger.info(`WorkerManager initialized with ${this.options.maxWorkers} max workers`);
    }

    /**
     * Get the WorkerManager instance (Singleton pattern)
     */
    public static getInstance(options?: WorkerManagerOptions): WorkerManager {
        if (!WorkerManager.instance) {
            WorkerManager.instance = new WorkerManager(options);
        }
        return WorkerManager.instance;
    }

    /**
     * Initialize the worker manager
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            logger.warn('WorkerManager is already initialized');
            return;
        }

        try {
            // Initialize memory manager
            this.memoryManager = EnhancedMemoryManager.getInstance();
            this.resourceTracker = ResourceTracker.getInstance();

            // Initialize database service
            this.databaseService = new DatabaseService(this.options.databasePath);
            await this.databaseService.initialize();

            // Initialize worker pool with package analysis worker
            const workerPath = path.join(process.cwd(), 'src', 'core', 'workers', 'PackageAnalysisWorker.ts');
            this.workerPool = WorkerPool.getInstance({
                maxThreads: this.options.maxWorkers,
                minThreads: this.options.minWorkers,
                idleTimeout: this.options.workerIdleTimeout,
                taskTimeout: this.options.taskTimeout,
                workerPath,
                dynamicThreads: true,
                workStealing: true,
                maxRetries: 2
            });

            await this.workerPool.initialize();

            // Set up event listeners
            this.workerPool.on('taskCompleted', this.handleTaskCompleted.bind(this));
            this.workerPool.on('error', this.handleWorkerError.bind(this));

            this.isInitialized = true;
            logger.info('WorkerManager initialized successfully');

        } catch (error) {
            logger.error('Failed to initialize WorkerManager:', error);
            throw error;
        }
    }

    /**
     * Process multiple packages in parallel
     */
    public async processPackages(
        packagePaths: string[],
        options: {
            batchSize?: number;
            maxConcurrentResources?: number;
            analyzeDependencies?: boolean;
            includeGameplayAnalysis?: boolean;
            trackPerformance?: boolean;
            progressCallback?: (progress: JobProgress) => void;
        } = {}
    ): Promise<any[]> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        const jobId = uuidv4();
        const startTime = Date.now();

        logger.info(`Starting parallel processing job ${jobId} for ${packagePaths.length} packages`);

        // Create analysis tasks
        const tasks: AnalysisTask[] = packagePaths.map((packagePath, index) => ({
            id: `${jobId}-task-${index}`,
            packagePath,
            options: {
                batchSize: this.calculateAdaptiveBatchSize(),
                maxConcurrentResources: options.maxConcurrentResources || 10,
                analyzeDependencies: options.analyzeDependencies || false,
                includeGameplayAnalysis: options.includeGameplayAnalysis || false,
                trackPerformance: options.trackPerformance || true
            },
            priority: TaskPriority.NORMAL,
            createdAt: Date.now()
        }));

        // Create job tracking
        const job: AnalysisJob = {
            id: jobId,
            tasks,
            totalTasks: tasks.length,
            completedTasks: 0,
            failedTasks: 0,
            startTime,
            results: [],
            errors: [],
            progressCallback: options.progressCallback
        };

        this.activeJobs.set(jobId, job);
        this.stats.totalJobs++;

        try {
            // Submit tasks to worker pool with chunking for better load distribution
            const chunkSize = Math.max(1, Math.ceil(tasks.length / this.options.maxWorkers!));
            const taskChunks = this.chunkArray(tasks, chunkSize);
            
            const chunkPromises = taskChunks.map(async (chunk, chunkIndex) => {
                const chunkResults: any[] = [];

                for (const task of chunk) {
                    try {
                        // Monitor memory pressure before submitting task
                        const memoryPressure = this.memoryManager.getMemoryPressure();
                        if (memoryPressure > this.options.memoryThreshold!) {
                            logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%), waiting for cleanup`);
                            await this.waitForMemoryRelief();
                        }

                        // Create worker task
                        const workerTask: Task<AnalysisTask, any> = {
                            id: task.id,
                            type: TaskType.RESOURCE_ANALYSIS,
                            data: task,
                            priority: task.priority,
                            createdAt: task.createdAt
                        };

                        // Submit task to worker pool
                        const result = await this.workerPool.submitTask(workerTask);
                        chunkResults.push(result.result);

                        // Update job progress
                        job.completedTasks++;
                        this.updateJobProgress(job);

                    } catch (error: any) {
                        logger.error(`Task ${task.id} failed:`, error);
                        job.failedTasks++;
                        job.errors.push(`Task ${task.id}: ${error.message}`);
                        this.updateJobProgress(job);
                    }
                }
                
                return chunkResults;
            });

            // Wait for all chunks to complete
            const chunkResults = await Promise.all(chunkPromises);
            const allResults = chunkResults.flat();

            // Finalize job
            job.endTime = Date.now();
            job.results = allResults;

            // Update stats
            this.stats.completedJobs++;
            this.stats.totalPackagesProcessed += packagePaths.length;
            this.stats.totalProcessingTime += (job.endTime - job.startTime);
            this.stats.averagePackagesPerSecond = this.stats.totalPackagesProcessed / (this.stats.totalProcessingTime / 1000);

            // Track peak memory usage
            const currentMemory = process.memoryUsage().heapUsed;
            this.stats.peakMemoryUsage = Math.max(this.stats.peakMemoryUsage, currentMemory);

            logger.info(`Job ${jobId} completed: ${job.completedTasks}/${job.totalTasks} tasks successful, ${job.failedTasks} failed`);
            logger.info(`Processing time: ${formatDuration(job.endTime - job.startTime)}`);
            logger.info(`Average speed: ${(packagePaths.length / ((job.endTime - job.startTime) / 1000)).toFixed(2)} packages/second`);

            // Clean up job tracking
            this.activeJobs.delete(jobId);

            return allResults;

        } catch (error: any) {
            logger.error(`Job ${jobId} failed:`, error);
            job.endTime = Date.now();
            job.errors.push(`Job failed: ${error.message}`);
            this.stats.failedJobs++;
            this.activeJobs.delete(jobId);
            throw error;
        }
    }

    /**
     * Calculate adaptive batch size based on system resources
     */
    private calculateAdaptiveBatchSize(): number {
        if (!this.options.adaptiveBatching) {
            return 500; // Default batch size
        }

        const memoryPressure = this.memoryManager.getMemoryPressure();
        const availableMemory = process.memoryUsage().heapTotal - process.memoryUsage().heapUsed;
        
        // Reduce batch size under memory pressure
        if (memoryPressure > 0.8) {
            return 100; // Small batches under high pressure
        } else if (memoryPressure > 0.6) {
            return 250; // Medium batches under moderate pressure
        } else {
            return 500; // Large batches when memory is available
        }
    }

    /**
     * Wait for memory pressure to decrease
     */
    private async waitForMemoryRelief(): Promise<void> {
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                const memoryPressure = this.memoryManager.getMemoryPressure();
                if (memoryPressure < this.options.memoryThreshold!) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 1000); // Check every second

            // Timeout after 30 seconds
            setTimeout(() => {
                clearInterval(checkInterval);
                logger.warn('Memory relief timeout, proceeding with high memory pressure');
                resolve();
            }, 30000);
        });
    }

    /**
     * Handle task completion
     */
    private handleTaskCompleted(taskResult: TaskResult): void {
        // Update worker utilization stats
        if (taskResult.workerId !== undefined) {
            const currentUtilization = this.stats.workerUtilization.get(taskResult.workerId) || 0;
            this.stats.workerUtilization.set(taskResult.workerId, currentUtilization + 1);
        }

        this.emit('taskCompleted', taskResult);
    }

    /**
     * Handle worker errors
     */
    private handleWorkerError(error: Error): void {
        logger.error('Worker error:', error);
        this.emit('workerError', error);
    }

    /**
     * Update job progress and notify callback
     */
    private updateJobProgress(job: AnalysisJob): void {
        if (!this.options.enableProgressReporting || !job.progressCallback) {
            return;
        }

        const progress = (job.completedTasks / job.totalTasks) * 100;
        const elapsedTime = Date.now() - job.startTime;
        const estimatedTotalTime = job.completedTasks > 0 ? (elapsedTime / job.completedTasks) * job.totalTasks : 0;
        const estimatedTimeRemaining = Math.max(0, estimatedTotalTime - elapsedTime);

        const memoryUsage = process.memoryUsage();
        const progressInfo: JobProgress = {
            jobId: job.id,
            totalTasks: job.totalTasks,
            completedTasks: job.completedTasks,
            failedTasks: job.failedTasks,
            progress,
            estimatedTimeRemaining,
            memoryUsage: {
                heapUsed: memoryUsage.heapUsed,
                heapTotal: memoryUsage.heapTotal,
                pressure: this.memoryManager.getMemoryPressure()
            }
        };

        job.progressCallback(progressInfo);
    }

    /**
     * Chunk array into smaller arrays
     */
    private chunkArray<T>(array: T[], chunkSize: number): T[][] {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * Get performance statistics
     */
    public getStats() {
        return {
            ...this.stats,
            activeJobs: this.activeJobs.size,
            workerPoolStats: this.workerPool.getStats(),
            memoryUsage: {
                current: process.memoryUsage(),
                pressure: this.memoryManager.getMemoryPressure(),
                peak: this.stats.peakMemoryUsage
            }
        };
    }

    /**
     * Get active job progress
     */
    public getJobProgress(jobId: string): JobProgress | null {
        const job = this.activeJobs.get(jobId);
        if (!job) {
            return null;
        }

        const progress = (job.completedTasks / job.totalTasks) * 100;
        const elapsedTime = Date.now() - job.startTime;
        const estimatedTotalTime = job.completedTasks > 0 ? (elapsedTime / job.completedTasks) * job.totalTasks : 0;
        const estimatedTimeRemaining = Math.max(0, estimatedTotalTime - elapsedTime);

        const memoryUsage = process.memoryUsage();
        return {
            jobId: job.id,
            totalTasks: job.totalTasks,
            completedTasks: job.completedTasks,
            failedTasks: job.failedTasks,
            progress,
            estimatedTimeRemaining,
            memoryUsage: {
                heapUsed: memoryUsage.heapUsed,
                heapTotal: memoryUsage.heapTotal,
                pressure: this.memoryManager.getMemoryPressure()
            }
        };
    }

    /**
     * Terminate the worker manager
     */
    public async terminate(): Promise<void> {
        logger.info('Terminating WorkerManager...');

        // Cancel all active jobs
        for (const [jobId, job] of this.activeJobs) {
            job.errors.push('Job cancelled due to worker manager termination');
            this.activeJobs.delete(jobId);
        }

        // Terminate worker pool
        if (this.workerPool) {
            await this.workerPool.terminate();
        }

        // Close database connection
        if (this.databaseService) {
            await this.databaseService.close();
        }

        this.isInitialized = false;
        logger.info('WorkerManager terminated');
    }

    /**
     * Reset statistics
     */
    public resetStats(): void {
        this.stats = {
            totalJobs: 0,
            completedJobs: 0,
            failedJobs: 0,
            totalPackagesProcessed: 0,
            totalProcessingTime: 0,
            averagePackagesPerSecond: 0,
            peakMemoryUsage: 0,
            workerUtilization: new Map<number, number>()
        };
    }
}

export default WorkerManager;