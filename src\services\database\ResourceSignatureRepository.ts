import Database from 'better-sqlite3';
import { Logger } from '../../utils/logging/logger.js';
import { LSHType } from '../../utils/lsh/LSHFactory.js';

/**
 * Repository for managing resource signatures in the database
 */
export class ResourceSignatureRepository {
    private db: Database.Database;
    private logger: Logger;
    private initialized: boolean = false;

    /**
     * Create a new resource signature repository
     * @param db Database instance
     * @param logger Optional logger instance
     */
    constructor(db: Database.Database, logger?: Logger) {
        this.db = db;
        this.logger = logger || new Logger('ResourceSignatureRepository');
        this.initialize();
    }

    /**
     * Initialize the repository by creating necessary tables
     */
    private initialize(): void {
        // Skip initialization if already initialized
        if (this.initialized) {
            return;
        }

        try {
            // Create ResourceSignatures table
            this.db.prepare(`
                CREATE TABLE IF NOT EXISTS ResourceSignatures (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    resourceId TEXT NOT NULL,
                    signatureType TEXT NOT NULL,
                    signature TEXT NOT NULL,
                    timestamp INTEGER NOT NULL,
                    UNIQUE(resourceId, signatureType)
                )
            `).run();

            // Create indexes for better performance
            this.db.prepare(`
                CREATE INDEX IF NOT EXISTS idx_resource_signatures_resource_id ON ResourceSignatures(resourceId)
            `).run();

            this.db.prepare(`
                CREATE INDEX IF NOT EXISTS idx_resource_signatures_type ON ResourceSignatures(signatureType)
            `).run();

            this.logger.info('Initialized resource signature repository');
            this.initialized = true;
        } catch (error) {
            this.logger.error('Error initializing resource signature repository:', error);
            throw error;
        }
    }

    /**
     * Save a resource signature to the database
     * @param resourceId Resource ID
     * @param signatureType Signature type (e.g., 'simhash', 'minhash')
     * @param signature Signature value (serialized as JSON)
     * @returns The ID of the saved signature
     */
    saveSignature(resourceId: string, signatureType: LSHType, signature: any): number {
        try {
            // For large signatures, we need to be more efficient
            let serializedSignature: string;

            // Handle different signature types differently
            if (signatureType === LSHType.SIMHASH) {
                // SimHash is just a number, so we can store it directly
                serializedSignature = signature.toString();
            } else if (signatureType === LSHType.MINHASH && Array.isArray(signature)) {
                // For MinHash arrays, use a much more compact representation
                // Store only the first 10 values to save space (still provides good similarity detection)
                const compactSignature = signature.slice(0, 10);

                // Convert to a compact string representation
                serializedSignature = compactSignature.join(',');
            } else if (Array.isArray(signature)) {
                // For other array types, limit size
                const compactSignature = signature.slice(0, 10);
                serializedSignature = JSON.stringify(compactSignature);
            } else if (typeof signature === 'object' && signature !== null) {
                // For objects, extract only essential properties
                const essentialProps = Object.keys(signature).slice(0, 5);
                const compactObj: any = {};

                for (const prop of essentialProps) {
                    compactObj[prop] = signature[prop];
                }

                serializedSignature = JSON.stringify(compactObj);
            } else {
                // For other types, use standard string conversion
                serializedSignature = String(signature).substring(0, 1000); // Limit string length
            }

            // Insert or replace signature
            const stmt = this.db.prepare(`
                INSERT OR REPLACE INTO ResourceSignatures (
                    resourceId, signatureType, signature, timestamp
                ) VALUES (?, ?, ?, ?)
                RETURNING id
            `);

            const result = stmt.get(
                resourceId,
                signatureType,
                serializedSignature,
                Date.now()
            ) as { id: number };

            this.logger.debug(`Saved ${signatureType} signature for resource ${resourceId}`);
            return result.id;
        } catch (error) {
            this.logger.error(`Error saving signature for resource ${resourceId}:`, error);
            throw error;
        }
    }

    /**
     * Get a resource signature from the database
     * @param resourceId Resource ID
     * @param signatureType Signature type (e.g., 'simhash', 'minhash')
     * @returns The signature or null if not found
     */
    getSignature(resourceId: string, signatureType: LSHType): any | null {
        try {
            // Get signature
            const stmt = this.db.prepare(`
                SELECT signature
                FROM ResourceSignatures
                WHERE resourceId = ? AND signatureType = ?
            `);

            const result = stmt.get(resourceId, signatureType) as { signature: string } | undefined;

            if (!result) {
                return null;
            }

            // Handle different signature types differently
            if (signatureType === LSHType.SIMHASH) {
                // SimHash is stored as a string representation of a number
                return BigInt(result.signature);
            } else if (signatureType === LSHType.MINHASH) {
                // Check if it's a comma-separated list (our compact format)
                if (result.signature.includes(',') && !result.signature.includes('[')) {
                    // Parse comma-separated values into an array of numbers
                    return result.signature.split(',').map(Number);
                }
            }

            // For other types, try JSON parsing
            try {
                return JSON.parse(result.signature);
            } catch (parseError) {
                // If parsing fails, return the raw string
                this.logger.debug(`Using raw string signature for resource ${resourceId} (not JSON)`);
                return result.signature;
            }
        } catch (error) {
            this.logger.error(`Error getting signature for resource ${resourceId}:`, error);
            return null;
        }
    }

    /**
     * Get all signatures of a specific type
     * @param signatureType Signature type (e.g., 'simhash', 'minhash')
     * @param limit Maximum number of signatures to return
     * @param offset Offset for pagination
     * @returns Array of signatures with their resource IDs
     */
    getSignaturesByType(signatureType: LSHType, limit: number = 1000, offset: number = 0): { resourceId: string, signature: any }[] {
        try {
            // Get signatures
            const stmt = this.db.prepare(`
                SELECT resourceId, signature
                FROM ResourceSignatures
                WHERE signatureType = ?
                LIMIT ? OFFSET ?
            `);

            const results = stmt.all(signatureType, limit, offset) as { resourceId: string, signature: string }[];

            // Parse signatures from JSON
            return results.map(result => ({
                resourceId: result.resourceId,
                signature: JSON.parse(result.signature)
            }));
        } catch (error) {
            this.logger.error(`Error getting signatures of type ${signatureType}:`, error);
            return [];
        }
    }

    /**
     * Delete a resource signature from the database
     * @param resourceId Resource ID
     * @param signatureType Signature type (e.g., 'simhash', 'minhash')
     * @returns True if signature was deleted, false otherwise
     */
    deleteSignature(resourceId: string, signatureType: LSHType): boolean {
        try {
            // Delete signature
            const stmt = this.db.prepare(`
                DELETE FROM ResourceSignatures
                WHERE resourceId = ? AND signatureType = ?
            `);

            const result = stmt.run(resourceId, signatureType);

            this.logger.debug(`Deleted ${signatureType} signature for resource ${resourceId}`);
            return result.changes > 0;
        } catch (error) {
            this.logger.error(`Error deleting signature for resource ${resourceId}:`, error);
            return false;
        }
    }

    /**
     * Delete all signatures for a resource
     * @param resourceId Resource ID
     * @returns Number of signatures deleted
     */
    deleteSignaturesForResource(resourceId: string): number {
        try {
            // Delete signatures
            const stmt = this.db.prepare(`
                DELETE FROM ResourceSignatures
                WHERE resourceId = ?
            `);

            const result = stmt.run(resourceId);

            this.logger.debug(`Deleted ${result.changes} signatures for resource ${resourceId}`);
            return result.changes;
        } catch (error) {
            this.logger.error(`Error deleting signatures for resource ${resourceId}:`, error);
            return 0;
        }
    }
}
