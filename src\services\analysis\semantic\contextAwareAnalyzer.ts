/**
 * Context-Aware Analyzer
 *
 * This module provides context-aware analysis of resources by considering
 * relationships between resources in the same package and across packages.
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { ResourcePurposeAnalysis, ResourcePurposeType } from './interfaces/resourcePurpose.js';
import { GameplaySystemCategorization } from './interfaces/gameplaySystem.js';
import { injectable, singleton } from '../../di/decorators.js';

/**
 * Resource relationship type
 */
export enum ResourceRelationshipType {
    DEPENDS_ON = 'DEPENDS_ON',
    REFERENCED_BY = 'REFERENCED_BY',
    OVERRIDES = 'OVERRIDES',
    EXTENDS = 'EXTENDS',
    COMPLEMENTS = 'COMPLEMENTS',
    CONFLICTS_WITH = 'CONFLICTS_WITH'
}

/**
 * Resource relationship
 */
export interface ResourceRelationship {
    /**
     * Source resource ID
     */
    sourceResourceId: number;

    /**
     * Target resource ID
     */
    targetResourceId: number;

    /**
     * Relationship type
     */
    relationshipType: ResourceRelationshipType;

    /**
     * Relationship strength (0-100)
     */
    strength: number;

    /**
     * Description of the relationship
     */
    description: string;

    /**
     * Timestamp when this relationship was detected
     */
    timestamp: number;
}

/**
 * Context information for a resource
 */
export interface ResourceContext {
    /**
     * Resource ID
     */
    resourceId: number;

    /**
     * Resource key
     */
    resourceKey: ResourceKey;

    /**
     * Resource metadata
     */
    metadata: Record<string, any>;

    /**
     * Resource content
     */
    content?: string;

    /**
     * Related resources (by relationship type)
     */
    relatedResources: Map<ResourceRelationshipType, number[]>;

    /**
     * Package context
     */
    packageContext: {
        /**
         * Package ID
         */
        packageId: number;

        /**
         * Package name
         */
        packageName: string;

        /**
         * Total resources in the package
         */
        totalResources: number;

        /**
         * Resource type distribution in the package
         */
        resourceTypeDistribution: Record<number, number>;
    };
}

/**
 * Context-aware analysis result
 */
export interface ContextAwareAnalysisResult {
    /**
     * Resource purpose analysis
     */
    purposeAnalysis: ResourcePurposeAnalysis;

    /**
     * Gameplay system categorization
     */
    systemCategorization: GameplaySystemCategorization;

    /**
     * Resource relationships
     */
    relationships: ResourceRelationship[];

    /**
     * Context-enhanced confidence score (0-100)
     */
    contextConfidence: number;

    /**
     * Context-based insights
     */
    contextInsights: string[];
}

/**
 * Context-aware analyzer for resources
 */
@singleton()
export class ContextAwareAnalyzer {
    private logger: Logger;

    /**
     * Constructor
     * @param databaseService Database service
     * @param logger Logger instance
     */
    constructor(
        private databaseService: DatabaseService,
        logger?: Logger
    ) {
        this.logger = logger || new Logger('ContextAwareAnalyzer');
    }

    /**
     * Initialize the analyzer
     */
    public async initialize(): Promise<void> {
        try {
            // Create necessary database tables
            await this.createTables();
            this.logger.info('Context-aware analyzer initialized');
        } catch (error) {
            this.logger.error('Error initializing context-aware analyzer:', error);
            throw error;
        }
    }

    /**
     * Create database tables
     */
    private async createTables(): Promise<void> {
        try {
            // Create ResourceRelationships table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS ResourceRelationships (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sourceResourceId INTEGER NOT NULL,
                    targetResourceId INTEGER NOT NULL,
                    relationshipType TEXT NOT NULL,
                    strength REAL NOT NULL,
                    description TEXT,
                    timestamp INTEGER NOT NULL,
                    FOREIGN KEY (sourceResourceId) REFERENCES Resources(id),
                    FOREIGN KEY (targetResourceId) REFERENCES Resources(id)
                );
            `);

            // Create indices for performance
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourcerelationships_source ON ResourceRelationships(sourceResourceId);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourcerelationships_target ON ResourceRelationships(targetResourceId);');
            await this.databaseService.executeQuery('CREATE INDEX IF NOT EXISTS idx_resourcerelationships_type ON ResourceRelationships(relationshipType);');

            this.logger.info('Context-aware analyzer tables created');
        } catch (error) {
            this.logger.error('Error creating context-aware analyzer tables:', error);
            throw error;
        }
    }

    /**
     * Build context for a resource
     * @param resourceId Resource ID
     * @param resourceKey Resource key
     * @param metadata Resource metadata
     * @param content Resource content
     * @param packageId Package ID
     * @returns Resource context
     */
    public async buildResourceContext(
        resourceId: number,
        resourceKey: ResourceKey,
        metadata: Record<string, any>,
        content?: string,
        packageId?: number
    ): Promise<ResourceContext> {
        try {
            // Initialize context
            const context: ResourceContext = {
                resourceId,
                resourceKey,
                metadata,
                content,
                relatedResources: new Map(),
                packageContext: {
                    packageId: packageId || 0,
                    packageName: '',
                    totalResources: 0,
                    resourceTypeDistribution: {}
                }
            };

            // Get package information if packageId is provided
            if (packageId) {
                const packageInfo = await this.getPackageInfo(packageId);
                if (packageInfo) {
                    context.packageContext = packageInfo;
                }
            }

            // Get resource relationships
            const relationships = await this.getResourceRelationships(resourceId);

            // Organize relationships by type
            for (const relationship of relationships) {
                const targetId = relationship.targetResourceId;
                const type = relationship.relationshipType;

                if (!context.relatedResources.has(type)) {
                    context.relatedResources.set(type, []);
                }

                context.relatedResources.get(type)?.push(targetId);
            }

            return context;
        } catch (error) {
            this.logger.error(`Error building context for resource ${resourceId}:`, error);
            throw error;
        }
    }

    /**
     * Get package information
     * @param packageId Package ID
     * @returns Package context information
     */
    private async getPackageInfo(packageId: number): Promise<ResourceContext['packageContext'] | null> {
        try {
            // Get package name
            const packageResult = await this.databaseService.executeQuery(`
                SELECT name FROM Packages WHERE id = ?
            `, [packageId]);

            if (!packageResult || packageResult.length === 0) {
                return null;
            }

            const packageName = packageResult[0].name || '';

            // Get resource count
            const countResult = await this.databaseService.executeQuery(`
                SELECT COUNT(*) as count FROM Resources WHERE packageId = ?
            `, [packageId]);

            const totalResources = countResult[0]?.count || 0;

            // Get resource type distribution
            const distributionResult = await this.databaseService.executeQuery(`
                SELECT type, COUNT(*) as count FROM Resources WHERE packageId = ? GROUP BY type
            `, [packageId]);

            const resourceTypeDistribution: Record<number, number> = {};

            for (const row of distributionResult) {
                resourceTypeDistribution[row.type] = row.count;
            }

            return {
                packageId,
                packageName,
                totalResources,
                resourceTypeDistribution
            };
        } catch (error) {
            this.logger.error(`Error getting package info for package ${packageId}:`, error);
            return null;
        }
    }

    /**
     * Get relationships for a resource
     * @param resourceId Resource ID
     * @returns Array of resource relationships
     */
    private async getResourceRelationships(resourceId: number): Promise<ResourceRelationship[]> {
        try {
            // First, check if relationships already exist in the database
            const existingResult = await this.databaseService.executeQuery(`
                SELECT * FROM ResourceRelationships
                WHERE sourceResourceId = ? OR targetResourceId = ?
            `, [resourceId, resourceId]);

            if (existingResult && existingResult.length > 0) {
                return existingResult.map(row => ({
                    sourceResourceId: row.sourceResourceId,
                    targetResourceId: row.targetResourceId,
                    relationshipType: row.relationshipType,
                    strength: row.strength,
                    description: row.description,
                    timestamp: row.timestamp
                }));
            }

            // If no relationships exist, detect them
            const relationships = await this.detectResourceRelationships(resourceId);

            // Save detected relationships to database
            for (const relationship of relationships) {
                await this.databaseService.executeQuery(`
                    INSERT INTO ResourceRelationships
                    (sourceResourceId, targetResourceId, relationshipType, strength, description, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                `, [
                    relationship.sourceResourceId,
                    relationship.targetResourceId,
                    relationship.relationshipType,
                    relationship.strength,
                    relationship.description,
                    relationship.timestamp
                ]);
            }

            return relationships;
        } catch (error) {
            this.logger.error(`Error getting relationships for resource ${resourceId}:`, error);
            return [];
        }
    }

    /**
     * Detect relationships for a resource
     * @param resourceId Resource ID
     * @returns Array of resource relationships
     */
    private async detectResourceRelationships(resourceId: number): Promise<ResourceRelationship[]> {
        try {
            const relationships: ResourceRelationship[] = [];

            // Get resource information
            const resourceResult = await this.databaseService.executeQuery(`
                SELECT * FROM Resources WHERE id = ?
            `, [resourceId]);

            if (!resourceResult || resourceResult.length === 0) {
                return [];
            }

            const resource = resourceResult[0];
            const resourceType = resource.type;
            const resourceGroup = resource.group;
            const resourceInstance = resource.instance;
            const resourcePackageId = resource.packageId;
            const resourceMetadata = resource.metadata ? JSON.parse(resource.metadata) : {};
            const resourceContent = resource.contentSnippet || '';

            // 1. Detect dependencies based on resource type
            await this.detectTypeDependencies(resourceId, resourceType, relationships);

            // 2. Detect references in content
            await this.detectContentReferences(resourceId, resourceContent, relationships);

            // 3. Detect overrides based on instance ID
            await this.detectOverrides(resourceId, resourceType, resourceGroup, resourceInstance, relationships);

            // 4. Detect extensions based on metadata
            await this.detectExtensions(resourceId, resourceMetadata, relationships);

            // 5. Detect package-level relationships
            await this.detectPackageRelationships(resourceId, resourcePackageId, relationships);

            return relationships;
        } catch (error) {
            this.logger.error(`Error detecting relationships for resource ${resourceId}:`, error);
            return [];
        }
    }

    /**
     * Detect dependencies based on resource type
     * @param resourceId Resource ID
     * @param resourceType Resource type
     * @param relationships Relationships array to update
     */
    private async detectTypeDependencies(
        resourceId: number,
        resourceType: number,
        relationships: ResourceRelationship[]
    ): Promise<void> {
        try {
            // Define type dependencies
            const typeDependencies: Record<number, number[]> = {
                // SimData depends on Tuning XML
                0x545AC67A: [0x0166038C],

                // Tuning XML may reference SimData
                0x0166038C: [0x545AC67A]
            };

            // Check if this resource type has dependencies
            const dependentTypes = typeDependencies[resourceType];
            if (!dependentTypes || dependentTypes.length === 0) {
                return;
            }

            // Get resource package ID
            const resourceResult = await this.databaseService.executeQuery(`
                SELECT packageId, instance FROM Resources WHERE id = ?
            `, [resourceId]);

            if (!resourceResult || resourceResult.length === 0) {
                return;
            }

            const packageId = resourceResult[0].packageId;
            const instance = resourceResult[0].instance;

            // Find resources of dependent types in the same package with the same instance
            for (const dependentType of dependentTypes) {
                const dependentResources = await this.databaseService.executeQuery(`
                    SELECT id FROM Resources
                    WHERE packageId = ? AND type = ? AND instance = ?
                `, [packageId, dependentType, instance]);

                for (const dependentResource of dependentResources) {
                    relationships.push({
                        sourceResourceId: resourceId,
                        targetResourceId: dependentResource.id,
                        relationshipType: ResourceRelationshipType.DEPENDS_ON,
                        strength: 90,
                        description: `Resource depends on ${dependentType.toString(16)} with same instance`,
                        timestamp: Date.now()
                    });

                    relationships.push({
                        sourceResourceId: dependentResource.id,
                        targetResourceId: resourceId,
                        relationshipType: ResourceRelationshipType.REFERENCED_BY,
                        strength: 90,
                        description: `Resource is referenced by ${resourceType.toString(16)} with same instance`,
                        timestamp: Date.now()
                    });
                }
            }
        } catch (error) {
            this.logger.error(`Error detecting type dependencies for resource ${resourceId}:`, error);
        }
    }

    /**
     * Detect references in content
     * @param resourceId Resource ID
     * @param content Resource content
     * @param relationships Relationships array to update
     */
    private async detectContentReferences(
        resourceId: number,
        content: string,
        relationships: ResourceRelationship[]
    ): Promise<void> {
        try {
            if (!content) {
                return;
            }

            // Get all resources with metadata to get their names
            const resources = await this.databaseService.executeQuery(`
                SELECT r.id, m.value as name
                FROM Resources r
                LEFT JOIN Metadata m ON r.id = m.resourceId AND m.key = 'name'
                WHERE m.value IS NOT NULL
            `);

            if (!resources || resources.length === 0) {
                return;
            }

            // Check for references to other resources by name
            for (const resource of resources) {
                if (resource.id === resourceId || !resource.name) {
                    continue;
                }

                // Skip very short names to avoid false positives
                if (resource.name.length < 4) {
                    continue;
                }

                // Check if content contains the resource name
                if (content.includes(resource.name)) {
                    relationships.push({
                        sourceResourceId: resourceId,
                        targetResourceId: resource.id,
                        relationshipType: ResourceRelationshipType.DEPENDS_ON,
                        strength: 70,
                        description: `Resource content references ${resource.name}`,
                        timestamp: Date.now()
                    });

                    relationships.push({
                        sourceResourceId: resource.id,
                        targetResourceId: resourceId,
                        relationshipType: ResourceRelationshipType.REFERENCED_BY,
                        strength: 70,
                        description: `Resource is referenced by content`,
                        timestamp: Date.now()
                    });
                }
            }
        } catch (error) {
            this.logger.error(`Error detecting content references for resource ${resourceId}:`, error);
        }
    }

    /**
     * Detect overrides based on instance ID
     * @param resourceId Resource ID
     * @param resourceType Resource type
     * @param resourceGroup Resource group
     * @param resourceInstance Resource instance
     * @param relationships Relationships array to update
     */
    private async detectOverrides(
        resourceId: number,
        resourceType: number,
        resourceGroup: number,
        resourceInstance: string,
        relationships: ResourceRelationship[]
    ): Promise<void> {
        try {
            // Find resources with the same type and instance but different package
            const resourceResult = await this.databaseService.executeQuery(`
                SELECT packageId FROM Resources WHERE id = ?
            `, [resourceId]);

            if (!resourceResult || resourceResult.length === 0) {
                return;
            }

            const packageId = resourceResult[0].packageId;

            // Find resources with the same type and instance but different package
            const overriddenResources = await this.databaseService.executeQuery(`
                SELECT id FROM Resources
                WHERE type = ? AND instance = ? AND packageId != ?
            `, [resourceType, resourceInstance, packageId]);

            for (const overriddenResource of overriddenResources) {
                relationships.push({
                    sourceResourceId: resourceId,
                    targetResourceId: overriddenResource.id,
                    relationshipType: ResourceRelationshipType.OVERRIDES,
                    strength: 95,
                    description: `Resource overrides another resource with same type and instance`,
                    timestamp: Date.now()
                });

                relationships.push({
                    sourceResourceId: overriddenResource.id,
                    targetResourceId: resourceId,
                    relationshipType: ResourceRelationshipType.REFERENCED_BY,
                    strength: 95,
                    description: `Resource is overridden by another resource`,
                    timestamp: Date.now()
                });
            }
        } catch (error) {
            this.logger.error(`Error detecting overrides for resource ${resourceId}:`, error);
        }
    }

    /**
     * Detect extensions based on metadata
     * @param resourceId Resource ID
     * @param metadata Resource metadata
     * @param relationships Relationships array to update
     */
    private async detectExtensions(
        resourceId: number,
        metadata: Record<string, any>,
        relationships: ResourceRelationship[]
    ): Promise<void> {
        try {
            if (!metadata) {
                return;
            }

            // Check for extension indicators in metadata
            if (metadata.extends || metadata.extension || metadata.parentClass) {
                const extendedName = metadata.extends || metadata.extension || metadata.parentClass;

                // Find resources with matching name in metadata
                const extendedResources = await this.databaseService.executeQuery(`
                    SELECT r.id
                    FROM Resources r
                    JOIN Metadata m ON r.id = m.resourceId AND m.key = 'name'
                    WHERE m.value = ?
                `, [extendedName]);

                for (const extendedResource of extendedResources) {
                    relationships.push({
                        sourceResourceId: resourceId,
                        targetResourceId: extendedResource.id,
                        relationshipType: ResourceRelationshipType.EXTENDS,
                        strength: 85,
                        description: `Resource extends ${extendedName}`,
                        timestamp: Date.now()
                    });

                    relationships.push({
                        sourceResourceId: extendedResource.id,
                        targetResourceId: resourceId,
                        relationshipType: ResourceRelationshipType.REFERENCED_BY,
                        strength: 85,
                        description: `Resource is extended by another resource`,
                        timestamp: Date.now()
                    });
                }
            }
        } catch (error) {
            this.logger.error(`Error detecting extensions for resource ${resourceId}:`, error);
        }
    }

    /**
     * Detect package-level relationships
     * @param resourceId Resource ID
     * @param packageId Package ID
     * @param relationships Relationships array to update
     */
    private async detectPackageRelationships(
        resourceId: number,
        packageId: number,
        relationships: ResourceRelationship[]
    ): Promise<void> {
        try {
            // Get package name
            const packageResult = await this.databaseService.executeQuery(`
                SELECT name FROM Packages WHERE id = ?
            `, [packageId]);

            if (!packageResult || packageResult.length === 0) {
                return;
            }

            const packageName = packageResult[0].name;

            // Check for complementary packages
            if (packageName.includes('Addon') || packageName.includes('Extension')) {
                // Extract base package name
                const baseName = packageName
                    .replace(/Addon/i, '')
                    .replace(/Extension/i, '')
                    .trim();

                // Find base package
                const basePackages = await this.databaseService.executeQuery(`
                    SELECT id FROM Packages WHERE name LIKE ?
                `, [`%${baseName}%`]);

                for (const basePackage of basePackages) {
                    if (basePackage.id === packageId) {
                        continue;
                    }

                    // Find resources in base package
                    const baseResources = await this.databaseService.executeQuery(`
                        SELECT id FROM Resources WHERE packageId = ?
                    `, [basePackage.id]);

                    for (const baseResource of baseResources) {
                        relationships.push({
                            sourceResourceId: resourceId,
                            targetResourceId: baseResource.id,
                            relationshipType: ResourceRelationshipType.COMPLEMENTS,
                            strength: 75,
                            description: `Resource complements base package resource`,
                            timestamp: Date.now()
                        });
                    }
                }
            }
        } catch (error) {
            this.logger.error(`Error detecting package relationships for resource ${resourceId}:`, error);
        }
    }

    /**
     * Dispose of resources used by the analyzer
     */
    public async dispose(): Promise<void> {
        try {
            this.logger.info('Disposing ContextAwareAnalyzer resources');
            this.logger.info('ContextAwareAnalyzer resources disposed successfully');
        } catch (error) {
            this.logger.error('Error disposing ContextAwareAnalyzer resources:', error);
            throw error;
        }
    }
}
