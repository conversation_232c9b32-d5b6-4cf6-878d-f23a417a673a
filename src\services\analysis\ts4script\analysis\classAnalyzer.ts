/**
 * TS4Script Class Analyzer
 * 
 * This module provides functionality for analyzing classes in Sims 4 scripts.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { injectable, singleton } from '../../../di/decorators.js';
import { BytecodeInstruction, CodeObject } from '../bytecode/types.js';
import { TS4ScriptClass, TS4ScriptFunction, TS4ScriptProperty } from '../types.js';

/**
 * TS4Script class analyzer
 */
@singleton()
export class ClassAnalyzer {
    /**
     * Constructor
     * @param logger Logger instance
     */
    constructor(private logger: Logger = new Logger('ClassAnalyzer')) {}

    /**
     * Analyze classes in code object
     * @param codeObject Code object
     * @param moduleName Module name
     * @param instructions Bytecode instructions
     * @returns Classes
     */
    public analyzeClasses(codeObject: CodeObject, moduleName: string, instructions: BytecodeInstruction[]): TS4ScriptClass[] {
        try {
            this.logger.debug(`Analyzing classes in module: ${moduleName}`);

            const classes: TS4ScriptClass[] = [];
            
            // Find class definitions in the bytecode
            const classDefinitions = this.findClassDefinitions(instructions, codeObject);
            
            // Process each class definition
            for (const classDefinition of classDefinitions) {
                const className = classDefinition.name;
                const parentClasses = classDefinition.parentClasses;
                
                // Create class object
                const classObj: TS4ScriptClass = {
                    id: 0, // Will be set when saved to database
                    name: className,
                    fullName: `${moduleName}.${className}`,
                    parentClasses,
                    isEAClass: this.isEAClass(parentClasses),
                    methods: [],
                    properties: [],
                    startLine: classDefinition.startLine,
                    endLine: classDefinition.endLine,
                    complexity: this.calculateClassComplexity(classDefinition)
                };
                
                // Find methods for this class
                classObj.methods = this.findClassMethods(codeObject, classObj, classDefinition);
                
                // Find properties for this class
                classObj.properties = this.findClassProperties(codeObject, classObj, classDefinition);
                
                classes.push(classObj);
            }
            
            return classes;
        } catch (error) {
            this.logger.error(`Error analyzing classes in module ${moduleName}:`, error);
            return [];
        }
    }

    /**
     * Find class definitions in bytecode
     * @param instructions Bytecode instructions
     * @param codeObject Code object
     * @returns Class definitions
     */
    private findClassDefinitions(instructions: BytecodeInstruction[], codeObject: CodeObject): any[] {
        const classDefinitions: any[] = [];
        
        // Look for LOAD_BUILD_CLASS opcode
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            if (instruction.opcodeName === 'LOAD_BUILD_CLASS') {
                // Class definition found
                // Next instruction should be LOAD_CONST with the class code object
                const loadConstInstruction = instructions[i + 1];
                if (loadConstInstruction && loadConstInstruction.opcodeName === 'LOAD_CONST') {
                    const classCodeObject = loadConstInstruction.argValue;
                    
                    // Next instruction should be LOAD_CONST with the class name
                    const loadNameInstruction = instructions[i + 2];
                    if (loadNameInstruction && loadNameInstruction.opcodeName === 'LOAD_CONST') {
                        const className = loadNameInstruction.argValue;
                        
                        // Find parent classes
                        const parentClasses: string[] = [];
                        let j = i + 3;
                        
                        // Look for LOAD_NAME or LOAD_ATTR instructions for parent classes
                        while (j < instructions.length && 
                               (instructions[j].opcodeName === 'LOAD_NAME' || 
                                instructions[j].opcodeName === 'LOAD_ATTR' || 
                                instructions[j].opcodeName === 'LOAD_GLOBAL')) {
                            parentClasses.push(instructions[j].argValue as string);
                            j++;
                        }
                        
                        // Find start and end line
                        const startLine = instruction.lineNo || 0;
                        const endLine = this.findClassEndLine(instructions, i, startLine);
                        
                        classDefinitions.push({
                            name: className,
                            parentClasses,
                            codeObject: classCodeObject,
                            startLine,
                            endLine
                        });
                    }
                }
            }
        }
        
        return classDefinitions;
    }

    /**
     * Find class end line
     * @param instructions Bytecode instructions
     * @param startIndex Start index
     * @param startLine Start line
     * @returns End line
     */
    private findClassEndLine(instructions: BytecodeInstruction[], startIndex: number, startLine: number): number {
        // Find the CALL_FUNCTION instruction that follows the class definition
        for (let i = startIndex; i < instructions.length; i++) {
            if (instructions[i].opcodeName === 'CALL_FUNCTION' && instructions[i].arg === 2) {
                // Find the next instruction with a different line number
                for (let j = i + 1; j < instructions.length; j++) {
                    if (instructions[j].lineNo && instructions[j].lineNo > startLine) {
                        return instructions[j].lineNo - 1;
                    }
                }
            }
        }
        
        return startLine + 10; // Arbitrary value if we can't determine the end line
    }

    /**
     * Find class methods
     * @param codeObject Code object
     * @param classObj Class object
     * @param classDefinition Class definition
     * @returns Methods
     */
    private findClassMethods(codeObject: CodeObject, classObj: TS4ScriptClass, classDefinition: any): TS4ScriptFunction[] {
        const methods: TS4ScriptFunction[] = [];
        
        // Check if class code object is available
        if (classDefinition.codeObject && typeof classDefinition.codeObject === 'object' && 'type' in classDefinition.codeObject) {
            const classCodeObject = classDefinition.codeObject as CodeObject;
            
            // Look for function definitions in the class code object
            for (const nestedCodeObject of classCodeObject.nestedCodeObjects) {
                if (nestedCodeObject.type === 'function') {
                    // Create method object
                    const method: TS4ScriptFunction = {
                        id: 0, // Will be set when saved to database
                        name: nestedCodeObject.name,
                        fullName: `${classObj.fullName}.${nestedCodeObject.name}`,
                        parameters: this.extractFunctionParameters(nestedCodeObject),
                        isMethod: true,
                        isStaticMethod: false, // Would need to check for @staticmethod decorator
                        isClassMethod: false, // Would need to check for @classmethod decorator
                        isProperty: false, // Would need to check for @property decorator
                        isCommand: false,
                        isEventHandler: false,
                        isInjection: false,
                        decorators: [],
                        startLine: nestedCodeObject.firstLineNo,
                        endLine: nestedCodeObject.firstLineNo + 10, // Arbitrary value
                        complexity: this.calculateFunctionComplexity(nestedCodeObject)
                    };
                    
                    methods.push(method);
                }
            }
        }
        
        return methods;
    }

    /**
     * Find class properties
     * @param codeObject Code object
     * @param classObj Class object
     * @param classDefinition Class definition
     * @returns Properties
     */
    private findClassProperties(codeObject: CodeObject, classObj: TS4ScriptClass, classDefinition: any): TS4ScriptProperty[] {
        const properties: TS4ScriptProperty[] = [];
        
        // Check if class code object is available
        if (classDefinition.codeObject && typeof classDefinition.codeObject === 'object' && 'type' in classDefinition.codeObject) {
            const classCodeObject = classDefinition.codeObject as CodeObject;
            
            // Look for property assignments in the class code object
            // This is a simplified implementation
            // A more robust implementation would analyze the bytecode more thoroughly
            
            // For now, just look for STORE_NAME instructions in the class code object
            const instructions = classCodeObject.bytecode;
            
            // TODO: Implement property detection
        }
        
        return properties;
    }

    /**
     * Extract function parameters
     * @param codeObject Code object
     * @returns Parameters
     */
    private extractFunctionParameters(codeObject: CodeObject): any[] {
        const parameters: any[] = [];
        
        // Extract parameter names from varNames
        // The first argCount entries in varNames are the parameters
        for (let i = 0; i < codeObject.argCount; i++) {
            if (i < codeObject.varNames.length) {
                const paramName = codeObject.varNames[i];
                
                parameters.push({
                    name: paramName,
                    typeAnnotation: undefined, // Would need to parse type annotations
                    defaultValue: undefined, // Would need to analyze default values
                    isPositionalOnly: false,
                    isKeywordOnly: i >= codeObject.argCount - codeObject.kwOnlyArgCount,
                    isVariadicPositional: paramName.startsWith('*') && !paramName.startsWith('**'),
                    isVariadicKeyword: paramName.startsWith('**')
                });
            }
        }
        
        return parameters;
    }

    /**
     * Calculate class complexity
     * @param classDefinition Class definition
     * @returns Complexity
     */
    private calculateClassComplexity(classDefinition: any): number {
        // Simple complexity calculation based on number of methods and parent classes
        let complexity = 1;
        
        // Add complexity for parent classes
        complexity += classDefinition.parentClasses.length;
        
        // Add complexity for methods
        if (classDefinition.codeObject && typeof classDefinition.codeObject === 'object' && 'type' in classDefinition.codeObject) {
            const classCodeObject = classDefinition.codeObject as CodeObject;
            complexity += classCodeObject.nestedCodeObjects.length;
        }
        
        return complexity;
    }

    /**
     * Calculate function complexity
     * @param codeObject Code object
     * @returns Complexity
     */
    private calculateFunctionComplexity(codeObject: CodeObject): number {
        // Simple complexity calculation based on bytecode size and nested code objects
        let complexity = 1;
        
        // Add complexity for bytecode size
        complexity += Math.floor(codeObject.bytecode.length / 10);
        
        // Add complexity for nested code objects
        complexity += codeObject.nestedCodeObjects.length;
        
        return complexity;
    }

    /**
     * Check if class is an EA class
     * @param parentClasses Parent classes
     * @returns Is EA class
     */
    private isEAClass(parentClasses: string[]): boolean {
        // Check if any parent class is from an EA module
        const eaModulePrefixes = [
            'sims4.',
            'services.',
            'interactions.',
            'objects.',
            'server.',
            'ui.',
            'zone.',
            'world.'
        ];
        
        for (const parentClass of parentClasses) {
            for (const prefix of eaModulePrefixes) {
                if (parentClass.startsWith(prefix)) {
                    return true;
                }
            }
        }
        
        return false;
    }
}
