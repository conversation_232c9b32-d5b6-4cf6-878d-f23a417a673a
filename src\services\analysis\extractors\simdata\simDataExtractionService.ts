/**
 * SimData Extraction Service
 * Provides a unified interface for extracting metadata from SimData resources
 * Consolidates functionality from multiple extraction approaches
 */

import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService, DependencyInfo } from '../../../databaseService.js';
import { SimDataParser } from './simDataParser.js';
import { ParsedSimData, isSimDataResource } from './simDataTypes.js';
import { SimDataAnalyzer } from './simDataAnalyzer.js';
import { SimDataSchemaAnalyzer } from './simDataSchemaAnalyzer.js';
import { SimDataInstanceAnalyzer } from './simDataInstanceAnalyzer.js';
import { extractSimDataDependencies } from './simDataDependencyExtractor.js';
import { safeStringify, toSafeJsonObject } from '../../../../utils/serialization/jsonUtils.js';
import { SimDataSchemaParser } from './schema/schemaParser.js';
import { SimDataRelationshipAnalyzer } from './simDataRelationshipAnalyzer.js';
import { CrossResourceAnalyzer } from './crossResourceAnalyzer.js';
import { ResourceExtractionError } from '../../../../utils/error/index.js';
import {
    createSimDataExtractionContext,
    handleSimDataExtractionError,
    withAsyncSimDataExtractionErrorHandling,
    withSimDataExtractionErrorHandling,
    safeJsonStringify,
    getSafeSimDataContext
} from './common/simDataExtractorErrorHandler.js';

const log = new Logger('SimDataExtractionService');

/**
 * SimData Extraction Service
 * Provides a unified interface for extracting metadata from SimData resources
 */
export class SimDataExtractionService {
    private logger: Logger;
    private parser: SimDataParser;
    private analyzer: SimDataAnalyzer;
    private schemaAnalyzer: SimDataSchemaAnalyzer;
    private instanceAnalyzer: SimDataInstanceAnalyzer;
    private schemaParser: SimDataSchemaParser;
    private relationshipAnalyzer: SimDataRelationshipAnalyzer;
    private crossResourceAnalyzer: CrossResourceAnalyzer;

    constructor(logger?: Logger) {
        this.logger = logger || log;
        this.parser = new SimDataParser(this.logger);
        this.analyzer = new SimDataAnalyzer(this.logger);
        this.schemaAnalyzer = new SimDataSchemaAnalyzer(this.logger);
        this.instanceAnalyzer = new SimDataInstanceAnalyzer(this.logger);
    }

    /**
     * Initialize the service with a database service
     * @param databaseService Database service instance
     */
    public initialize(databaseService: DatabaseService): void {
        this.schemaParser = new SimDataSchemaParser(databaseService);
        this.relationshipAnalyzer = new SimDataRelationshipAnalyzer(databaseService, this.logger);
        this.crossResourceAnalyzer = new CrossResourceAnalyzer(databaseService, this.logger);
    }

    /**
     * Extract metadata from a SimData resource
     * @param key Resource key
     * @param buffer Resource buffer
     * @param resourceId Resource ID
     * @param databaseService Database service instance
     * @returns Extracted metadata
     */
    public async extract(
        key: AppResourceKey,
        buffer: Buffer,
        resourceId: number,
        databaseService: DatabaseService
    ): Promise<Partial<ResourceMetadata>> {
        // Initialize if needed
        if (!this.schemaParser) {
            try {
                this.initialize(databaseService);
                this.logger.debug(`SimData extraction service initialized successfully`);
            } catch (initError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataExtractionService', {
                    operation: 'initialize',
                    error: initError.message || String(initError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Failed to initialize SimData extraction service: ${initError.message || initError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);
            }
        }

        const extractedMetadata: Partial<ResourceMetadata> = {};
        let contentSnippet: string | undefined = undefined;
        let simData: ParsedSimData | null = null;

        try {
            // Log the resource being analyzed
            this.logger.debug(`Analyzing SimData resource: ${key.type.toString(16)}:${key.group.toString()}:${key.instance.toString(16)}`);

            // Basic check that this is a SimData resource
            if (!isSimDataResource(key)) {
                this.logger.debug(`Not a SimData resource: ${key.type.toString(16)}`);
                return {
                    contentSnippet: `[Not a SimData resource: ${key.type.toString(16)}]`,
                    extractorUsed: 'simdata',
                    extractionError: 'Not a SimData resource'
                };
            }

            // Parse the SimData with error handling
            try {
                simData = await this.parser.parse(buffer);

                if (simData) {
                    this.logger.debug(`Successfully parsed SimData: schema=${simData.schema?.name || 'Unknown'}, instances=${simData.instances.length}`);
                } else {
                    this.logger.warn(`SimData parser returned null for resource ${key.type.toString(16)}:${key.group.toString()}:${key.instance.toString(16)}`);
                }
            } catch (parseError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataParser', {
                    operation: 'parse',
                    bufferLength: buffer.length,
                    error: parseError.message || String(parseError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Failed to parse SimData: ${parseError.message || parseError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);
            }

            if (!simData) {
                return {
                    contentSnippet: '[Failed to parse SimData]',
                    extractorUsed: 'simdata',
                    extractionError: 'Failed to parse SimData'
                };
            }

            // Analyze the parsed SimData with error handling
            let analysisResults = {};
            let schemaAnalysisResults = {};
            let instanceAnalysisResults = {};

            // Basic analysis with error handling
            try {
                analysisResults = this.analyzer.analyze(simData);
                this.logger.debug(`Successfully performed basic SimData analysis`);
            } catch (analysisError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataAnalyzer', {
                    operation: 'analyze',
                    simDataContext: getSafeSimDataContext(simData),
                    error: analysisError.message || String(analysisError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Failed to perform basic SimData analysis: ${analysisError.message || analysisError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);

                // Use empty results to continue processing
                analysisResults = {};
            }

            // Schema analysis with error handling
            try {
                schemaAnalysisResults = this.schemaAnalyzer.analyzeSchema(simData);
                this.logger.debug(`Successfully performed SimData schema analysis`);
            } catch (schemaError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataSchemaAnalyzer', {
                    operation: 'analyzeSchema',
                    schemaName: simData.schema?.name || 'Unknown',
                    error: schemaError.message || String(schemaError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Failed to perform SimData schema analysis: ${schemaError.message || schemaError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);

                // Use empty results to continue processing
                schemaAnalysisResults = {};
            }

            // Instance analysis with error handling
            try {
                instanceAnalysisResults = this.instanceAnalyzer.analyzeInstances(simData);
                this.logger.debug(`Successfully performed SimData instance analysis`);
            } catch (instanceError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataInstanceAnalyzer', {
                    operation: 'analyzeInstances',
                    instanceCount: simData.instances.length,
                    error: instanceError.message || String(instanceError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Failed to perform SimData instance analysis: ${instanceError.message || instanceError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);

                // Use empty results to continue processing
                instanceAnalysisResults = {};
            }

            // Merge all analysis results into extracted metadata
            Object.assign(extractedMetadata, analysisResults);
            Object.assign(extractedMetadata, schemaAnalysisResults);
            Object.assign(extractedMetadata, instanceAnalysisResults);

            // Extract dependencies with error handling
            let dependencies: DependencyInfo[] = [];
            try {
                dependencies = extractSimDataDependencies(simData, resourceId);
                this.logger.debug(`Successfully extracted ${dependencies.length} dependencies from SimData`);
            } catch (depError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataDependencyExtractor', {
                    operation: 'extractDependencies',
                    simDataContext: getSafeSimDataContext(simData),
                    error: depError.message || String(depError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Failed to extract SimData dependencies: ${depError.message || depError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);

                // Use empty dependencies to continue processing
                dependencies = [];
            }

            // Save dependencies to database with error handling
            if (dependencies.length > 0) {
                try {
                    await databaseService.dependencies.saveDependencies(dependencies);
                    this.logger.debug(`Saved ${dependencies.length} dependencies for SimData resource ${resourceId}`);

                    // Add dependency count to metadata
                    extractedMetadata.simDataDependencyCount = dependencies.length;

                    // Set analysis confidence based on relationship quality
                    const confidenceScore = extractedMetadata.simDataRelatedTuningId ? 90 :
                                           (dependencies.length > 5 ? 80 :
                                           (dependencies.length > 0 ? 70 : 50));

                    extractedMetadata.simDataAnalysisConfidence = confidenceScore;
                } catch (dbError: any) {
                    const context = createSimDataExtractionContext(key, resourceId, 'SimDataExtractionService', {
                        operation: 'saveDependencies',
                        dependencyCount: dependencies.length,
                        error: dbError.message || String(dbError)
                    });

                    // Create a more specific error
                    const specificError = new ResourceExtractionError(
                        `Failed to save SimData dependencies to database: ${dbError.message || dbError}`,
                        context
                    );

                    // Log the error but continue processing
                    handleSimDataExtractionError(specificError, context, this.logger);

                    // Still set the dependency count in metadata
                    extractedMetadata.simDataDependencyCount = dependencies.length;
                }
            } else {
                // Lower confidence if no dependencies found
                extractedMetadata.simDataAnalysisConfidence = 40;
            }

            // Create a comprehensive content snippet
            const schemaName = simData.schema?.name || 'Unknown';
            const instanceCount = simData.instances.length;

            contentSnippet = `SimData: Schema=${schemaName}, Instances=${instanceCount}`;

            // Add schema information
            if (simData.schema) {
                contentSnippet += `, Columns=${simData.schema.columns.length}`;

                // Add schema version if available
                if (simData.version !== undefined) {
                    contentSnippet += `, Version=${simData.version}`;
                }

                // Add schema family if available
                if (schemaAnalysisResults.simDataSchemaFamily) {
                    contentSnippet += `, Family=${schemaAnalysisResults.simDataSchemaFamily}`;
                }

                // Add schema category if available
                if (schemaAnalysisResults.simDataSchemaCategory) {
                    contentSnippet += `, Category=${schemaAnalysisResults.simDataSchemaCategory}`;
                }
            }

            // Add instance information
            if (simData.instances.length > 0) {
                // Add instance names to snippet (limit to 3)
                const instanceNames = simData.instances.slice(0, 3).map(inst => inst.name || 'Unnamed').join(', ');
                contentSnippet += `, Instances=[${instanceNames}${simData.instances.length > 3 ? '...' : ''}]`;
            }

            // Add dependency information
            if (dependencies.length > 0) {
                contentSnippet += `, Dependencies=${dependencies.length}`;
            }

            // Save parsed content to database with error handling
            try {
                // Create a detailed representation of the SimData with error handling
                let detailedSimData: any = null;
                try {
                    detailedSimData = this.createDetailedSimData(
                        simData,
                        analysisResults,
                        schemaAnalysisResults,
                        instanceAnalysisResults,
                        dependencies,
                        extractedMetadata
                    );
                    this.logger.debug(`Successfully created detailed SimData representation`);
                } catch (detailError: any) {
                    const context = createSimDataExtractionContext(key, resourceId, 'SimDataExtractionService', {
                        operation: 'createDetailedSimData',
                        simDataContext: getSafeSimDataContext(simData),
                        error: detailError.message || String(detailError)
                    });

                    // Create a more specific error
                    const specificError = new ResourceExtractionError(
                        `Failed to create detailed SimData representation: ${detailError.message || detailError}`,
                        context
                    );

                    // Log the error but continue processing
                    handleSimDataExtractionError(specificError, context, this.logger);

                    // Create a minimal representation to continue processing
                    detailedSimData = {
                        schema: {
                            name: simData.schema?.name || 'Unknown',
                            version: simData.version || 0
                        },
                        instances: simData.instances.map(inst => ({
                            name: inst.name || 'Unnamed',
                            instanceId: inst.instanceId || 0
                        })),
                        analysis: {
                            error: `Failed to create detailed representation: ${detailError.message || detailError}`
                        }
                    };
                }

                // Convert to JSON with BigInt handling
                const jsonContent = safeJsonStringify(detailedSimData);

                // Save to database
                await databaseService.parsedContent.saveParsedContent({
                    resourceId: resourceId,
                    contentType: 'simdata',
                    content: jsonContent
                });

                this.logger.debug(`Saved parsed SimData content for resource ${resourceId}`);
            } catch (dbError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataExtractionService', {
                    operation: 'saveParsedContent',
                    contentType: 'simdata',
                    error: dbError.message || String(dbError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Failed to save parsed SimData content to database: ${dbError.message || dbError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);
            }

            // Try to use the enhanced schema parser for additional analysis with error handling
            try {
                if (this.schemaParser) {
                    // Initialize if needed
                    if (!this.schemaParser) {
                        try {
                            this.initialize(databaseService);
                            this.logger.debug(`SimData schema parser initialized successfully`);
                        } catch (initError: any) {
                            const context = createSimDataExtractionContext(key, resourceId, 'SimDataSchemaParser', {
                                operation: 'initialize',
                                error: initError.message || String(initError)
                            });

                            // Create a more specific error
                            const specificError = new ResourceExtractionError(
                                `Failed to initialize SimData schema parser: ${initError.message || initError}`,
                                context
                            );

                            // Log the error but continue processing
                            handleSimDataExtractionError(specificError, context, this.logger);

                            // Skip enhanced schema analysis
                            throw new Error(`Schema parser initialization failed: ${initError.message || initError}`);
                        }
                    }

                    // Parse schema with error handling
                    const schemaAnalysis = await withAsyncSimDataExtractionErrorHandling(
                        this.schemaParser.parseSchema.bind(this.schemaParser),
                        key,
                        resourceId,
                        'SimDataSchemaParser',
                        this.logger
                    )(simData, resourceId);

                    if (schemaAnalysis) {
                        this.logger.debug(`Successfully performed enhanced schema analysis`);

                        // Add enhanced schema information to metadata
                        extractedMetadata.simDataSchemaCategory = schemaAnalysis.category;
                        extractedMetadata.simDataSchemaComplexity = schemaAnalysis.complexity;

                        if (schemaAnalysis.purpose) {
                            extractedMetadata.simDataSchemaPurpose = schemaAnalysis.purpose;
                        }

                        if (schemaAnalysis.gameplaySystem) {
                            extractedMetadata.simDataGameplaySystem = schemaAnalysis.gameplaySystem;
                        }

                        if (schemaAnalysis.inheritance) {
                            extractedMetadata.simDataSchemaParent = schemaAnalysis.inheritance.parent;
                        }
                    }
                }
            } catch (schemaError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataSchemaParser', {
                    operation: 'parseSchema',
                    schemaName: simData.schema?.name || 'Unknown',
                    error: schemaError.message || String(schemaError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Enhanced schema analysis failed: ${schemaError.message || schemaError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);
            }

            // Perform enhanced relationship analysis with error handling
            try {
                // Initialize if needed
                if (!this.relationshipAnalyzer) {
                    try {
                        this.initialize(databaseService);
                        this.logger.debug(`SimData relationship analyzer initialized successfully`);
                    } catch (initError: any) {
                        const context = createSimDataExtractionContext(key, resourceId, 'SimDataRelationshipAnalyzer', {
                            operation: 'initialize',
                            error: initError.message || String(initError)
                        });

                        // Create a more specific error
                        const specificError = new ResourceExtractionError(
                            `Failed to initialize SimData relationship analyzer: ${initError.message || initError}`,
                            context
                        );

                        // Log the error but continue processing
                        handleSimDataExtractionError(specificError, context, this.logger);

                        // Skip relationship analysis
                        throw new Error(`Relationship analyzer initialization failed: ${initError.message || initError}`);
                    }
                }

                // Analyze relationships with error handling
                const relationshipAnalysis = await withAsyncSimDataExtractionErrorHandling(
                    this.relationshipAnalyzer.analyzeRelationships.bind(this.relationshipAnalyzer),
                    key,
                    resourceId,
                    'SimDataRelationshipAnalyzer',
                    this.logger
                )(simData, resourceId, key);

                if (relationshipAnalysis) {
                    this.logger.debug(`Successfully performed relationship analysis`);

                    // Add relationship information to metadata
                    if (relationshipAnalysis.relatedTuningResources.length > 0) {
                        extractedMetadata.simDataRelatedTuningCount = relationshipAnalysis.relatedTuningResources.length;

                        // Store high-confidence tuning relationships
                        const highConfidenceTuning = relationshipAnalysis.relatedTuningResources
                            .filter(rel => rel.confidence >= 80)
                            .map(rel => ({
                                id: rel.id,
                                instance: rel.key.instance.toString(),
                                relationship: rel.relationship,
                                semanticMeaning: rel.semanticMeaning
                            }));

                        if (highConfidenceTuning.length > 0) {
                            extractedMetadata.simDataHighConfidenceTuningRelationships =
                                safeJsonStringify(highConfidenceTuning.slice(0, 10)); // Limit to 10
                        }
                    }

                    if (relationshipAnalysis.relatedSimDataResources.length > 0) {
                        extractedMetadata.simDataRelatedSimDataCount = relationshipAnalysis.relatedSimDataResources.length;
                    }

                    if (relationshipAnalysis.relatedObjectResources.length > 0) {
                        extractedMetadata.simDataRelatedObjectCount = relationshipAnalysis.relatedObjectResources.length;
                    }

                    if (relationshipAnalysis.semanticRelationships.length > 0) {
                        extractedMetadata.simDataSemanticRelationshipCount = relationshipAnalysis.semanticRelationships.length;

                        // Store semantic relationships
                        const semanticRelationships = relationshipAnalysis.semanticRelationships
                            .map(rel => ({
                                sourceField: rel.sourceField,
                                targetType: rel.targetResource.type,
                                targetInstance: rel.targetResource.key.instance.toString(),
                                relationshipType: rel.relationshipType,
                                semanticMeaning: rel.semanticMeaning
                            }));

                        if (semanticRelationships.length > 0) {
                            extractedMetadata.simDataSemanticRelationships =
                                safeJsonStringify(semanticRelationships.slice(0, 10)); // Limit to 10
                        }
                    }

                    // Add gameplay system information
                    if (relationshipAnalysis.gameplaySystems.length > 0) {
                        const primarySystem = relationshipAnalysis.gameplaySystems
                            .sort((a, b) => b.confidence - a.confidence)[0];

                        extractedMetadata.simDataPrimaryGameplaySystem = primarySystem.system;
                        extractedMetadata.simDataGameplaySystemConfidence = primarySystem.confidence;

                        // Store all gameplay systems
                        const gameplaySystems = relationshipAnalysis.gameplaySystems
                            .map(sys => ({
                                system: sys.system,
                                confidence: sys.confidence,
                                relatedResourceCount: sys.relatedResources.length
                            }));

                        extractedMetadata.simDataGameplaySystems = safeJsonStringify(gameplaySystems);
                    }

                    // Save relationship analysis to database with error handling
                    try {
                        // Convert to JSON with BigInt handling
                        const jsonContent = safeJsonStringify(relationshipAnalysis);

                        await databaseService.parsedContent.saveParsedContent({
                            resourceId: resourceId,
                            contentType: 'simdata_relationships',
                            content: jsonContent
                        });

                        this.logger.debug(`Saved relationship analysis for SimData resource ${resourceId}`);
                    } catch (dbError: any) {
                        const context = createSimDataExtractionContext(key, resourceId, 'SimDataRelationshipAnalyzer', {
                            operation: 'saveRelationshipAnalysis',
                            contentType: 'simdata_relationships',
                            error: dbError.message || String(dbError)
                        });

                        // Create a more specific error
                        const specificError = new ResourceExtractionError(
                            `Failed to save relationship analysis to database: ${dbError.message || dbError}`,
                            context
                        );

                        // Log the error but continue processing
                        handleSimDataExtractionError(specificError, context, this.logger);
                    }
                }
            } catch (relationshipError: any) {
                const context = createSimDataExtractionContext(key, resourceId, 'SimDataRelationshipAnalyzer', {
                    operation: 'analyzeRelationships',
                    simDataContext: getSafeSimDataContext(simData),
                    error: relationshipError.message || String(relationshipError)
                });

                // Create a more specific error
                const specificError = new ResourceExtractionError(
                    `Enhanced relationship analysis failed: ${relationshipError.message || relationshipError}`,
                    context
                );

                // Log the error but continue processing
                handleSimDataExtractionError(specificError, context, this.logger);
            }

        } catch (error: any) {
            // Enhanced error handling for the main extraction process
            const context = createSimDataExtractionContext(key, resourceId, 'SimDataExtractionService', {
                operation: 'extractSimDataMetadata',
                error: error.message || String(error),
                stack: error.stack
            });

            // Create a more specific error
            const specificError = new ResourceExtractionError(
                `Error extracting SimData metadata: ${error.message || error}`,
                context
            );

            // Log the error but continue processing
            handleSimDataExtractionError(specificError, context, this.logger);

            // Set a descriptive content snippet for the error
            contentSnippet = `[SimData Parse Error: ${error.message || 'Unknown Error'}]`;
            extractedMetadata.extractionError = error.message || 'Unknown Error';
        }

        // Return the metadata with error handling
        try {
            return {
                contentSnippet,
                extractorUsed: 'simdata',
                ...extractedMetadata
            };
        } catch (returnError: any) {
            // If there's an error in the final return (e.g., circular reference in JSON)
            this.logger.error(`Error creating final metadata object: ${returnError.message || returnError}`);

            // Return a minimal metadata object
            return {
                contentSnippet: contentSnippet || '[SimData Metadata Error]',
                extractorUsed: 'simdata',
                extractionError: returnError.message || 'Error creating metadata object'
            };
        }
    }

    /**
     * Create a detailed representation of SimData for storage
     * @param simData Parsed SimData
     * @param analysisResults Basic analysis results
     * @param schemaAnalysisResults Schema analysis results
     * @param instanceAnalysisResults Instance analysis results
     * @param dependencies Dependencies
     * @param extractedMetadata Additional extracted metadata
     * @returns Detailed SimData representation
     */
    private createDetailedSimData(
        simData: ParsedSimData,
        analysisResults: any,
        schemaAnalysisResults: any,
        instanceAnalysisResults: any,
        dependencies: DependencyInfo[],
        extractedMetadata: Partial<ResourceMetadata> = {}
    ): any {
        return {
            schema: simData.schema ? {
                name: simData.schema.name,
                schemaId: simData.schema.schemaId,
                hash: simData.schema.hash.toString(16),
                version: simData.version,
                flags: simData.flags,
                parent: simData.schema.parent,

                // Enhanced schema information
                family: schemaAnalysisResults.simDataSchemaFamily,
                category: schemaAnalysisResults.simDataSchemaCategory,
                inheritance: schemaAnalysisResults.simDataSchemaInheritance ?
                    JSON.parse(schemaAnalysisResults.simDataSchemaInheritance) : undefined,
                complexity: schemaAnalysisResults.simDataSchemaComplexity,

                // Column information with enhanced analysis
                columns: simData.schema.columns.map(col => ({
                    name: col.name,
                    type: col.type,
                    typeId: col.type,
                    flags: col.flags
                }))
            } : null,

            // Basic instance information
            instances: simData.instances.map(inst => ({
                name: inst.name,
                instanceId: inst.instanceId,
                valueCount: Object.keys(inst.values).length
            })),

            // Analysis data
            analysis: {
                basic: analysisResults,
                schema: {
                    family: schemaAnalysisResults.simDataSchemaFamily,
                    category: schemaAnalysisResults.simDataSchemaCategory
                },
                dependencies: {
                    count: dependencies.length,
                    byType: this.groupDependenciesByType(dependencies)
                },
                relationships: {
                    tuningCount: extractedMetadata.simDataRelatedTuningCount || 0,
                    simDataCount: extractedMetadata.simDataRelatedSimDataCount || 0,
                    objectCount: extractedMetadata.simDataRelatedObjectCount || 0,
                    semanticCount: extractedMetadata.simDataSemanticRelationshipCount || 0,
                    primaryGameplaySystem: extractedMetadata.simDataPrimaryGameplaySystem || 'Unknown',
                    gameplaySystemConfidence: extractedMetadata.simDataGameplaySystemConfidence || 0
                }
            }
        };
    }

    /**
     * Group dependencies by type
     * @param dependencies Array of dependencies
     * @returns Dependencies grouped by type
     */
    private groupDependenciesByType(dependencies: DependencyInfo[]): Record<string, number> {
        const byType: Record<string, number> = {};

        for (const dep of dependencies) {
            const typeHex = dep.targetType.toString(16).padStart(8, '0');
            byType[typeHex] = (byType[typeHex] || 0) + 1;
        }

        return byType;
    }
}
