import { Logger } from '../../../../../utils/logging/logger.js';
import { XmlNode, XmlElementNode } from '@s4tk/xml-dom';
import { NodeContext } from '../types.js';

/**
 * Cleans an XML string to handle common issues
 * @param xmlString The XML string to clean
 * @param log The logger instance
 * @returns The cleaned XML string
 */
export function cleanXmlString(xmlString: string, log: Logger): string {
    let cleanedXmlString = xmlString;

    // Remove any non-XML content before the first '<'
    const firstTagIndex = cleanedXmlString.indexOf('<');
    if (firstTagIndex > 0) {
        cleanedXmlString = cleanedXmlString.substring(firstTagIndex);
        log.debug(`Removed ${firstTagIndex} characters before first '<' tag`);
    }

    // Clean up non-standard content within <T> tags
    cleanedXmlString = cleanedXmlString.replace(/<T>(\d+).*?<\/T>/g, '<T>$1</T>');

    // Fix malformed XML if needed
    cleanedXmlString = cleanedXmlString.replace(/&(?!amp;|lt;|gt;|quot;|apos;)/g, '&amp;');

    // Try to ensure the XML has a proper root element
    if (!cleanedXmlString.trim().startsWith('<?xml') && !cleanedXmlString.trim().startsWith('<I')) {
        cleanedXmlString = `<I>${cleanedXmlString}</I>`;
        log.debug(`Added root <I> element to XML`);
    }

    return cleanedXmlString;
}

/**
 * Gets the path of a node in the XML tree
 * @param node The XML element node
 * @returns The path of the node
 */
export function getNodePath(node: XmlElementNode): string {
    const path: string[] = [];
    let current: XmlNode | null = node;

    while (current instanceof XmlElementNode) {
        path.unshift(current.tag);
        current = current.parent;
    }

    return path.join('/');
}

/**
 * Creates a node context for dependency extraction
 * @param element The XML element node
 * @returns The node context
 */
export function createNodeContext(element: XmlElementNode): NodeContext {
    return {
        path: getNodePath(element),
        parentTag: element.parent instanceof XmlElementNode ? element.parent.tag : '',
        tag: element.tag
    };
}

/**
 * Extracts text content from an XML node
 * @param node The XML node
 * @returns The text content
 */
export function getNodeTextContent(node: any): string | undefined {
    if (!node) return undefined;

    // Handle S4TK XML DOM
    if (node instanceof XmlElementNode) {
        return node.innerValue;
    }

    // Handle xml2js structure
    if (typeof node === 'string') {
        return node;
    }

    if (typeof node === 'object') {
        // Check for common xml2js patterns
        if (node._) {
            return node._;
        }

        if (node.text) {
            return node.text;
        }

        if (node.$?.value) {
            return node.$.value;
        }
    }

    return undefined;
}

/**
 * Extracts an attribute from an XML node
 * @param node The XML node
 * @param attributeName The attribute name
 * @returns The attribute value
 */
export function getNodeAttribute(node: any, attributeName: string): string | undefined {
    if (!node) return undefined;

    // Handle S4TK XML DOM
    if (node instanceof XmlElementNode) {
        return node.attributes?.[attributeName];
    }

    // Handle xml2js structure
    if (typeof node === 'object') {
        // Check for common xml2js patterns
        if (node.$?.[attributeName]) {
            return node.$[attributeName];
        }

        if (node.attributes?.[attributeName]) {
            return node.attributes[attributeName];
        }

        if (node._?.[attributeName]) {
            return node._[attributeName];
        }
    }

    return undefined;
}

/**
 * Gets child elements from an XML node
 * @param node The XML node
 * @param childTag The child tag to look for (optional)
 * @returns The child elements
 */
export function getChildElements(node: any, childTag?: string): any[] {
    if (!node) return [];

    // Handle S4TK XML DOM
    if (node instanceof XmlElementNode) {
        if (childTag) {
            return node.children.filter(child => 
                child instanceof XmlElementNode && child.tag === childTag
            );
        }
        return node.children.filter(child => child instanceof XmlElementNode);
    }

    // Handle xml2js structure
    if (typeof node === 'object') {
        if (childTag) {
            const children = node[childTag];
            if (Array.isArray(children)) {
                return children;
            }
            if (children) {
                return [children];
            }
        } else {
            // Return all child elements
            const children: any[] = [];
            for (const key in node) {
                if (key !== '$' && key !== '_' && key !== 'attributes' && key !== 'text') {
                    const value = node[key];
                    if (Array.isArray(value)) {
                        children.push(...value);
                    } else if (value) {
                        children.push(value);
                    }
                }
            }
            return children;
        }
    }

    return [];
}
