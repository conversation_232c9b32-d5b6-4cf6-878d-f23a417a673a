import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';

// Import resource type constants
import * as ResourceTypes from '../../../constants/resourceTypes.js';

// Import individual metadata extractors from the index file
import {
    extractAnimationMetadata,
    extractAnimationStateMetadata,
    extractCasPartMetadata,
    extractGenericMetadata,
    extractImageMetadata,
    extractModelMetadata,
    extractObjectMetadata,
    extractScriptMetadata,
    extractScriptModuleMetadata,
    extractAudioMetadata,
    extractTuningXmlMetadata,
    SimDataExtractionService, // Import the unified SimData extraction service
    extractFootprintMetadata,
    extractBuildPartMetadata,
    extractSlotMetadata,
    extractLightMetadata,
    extractTerrainPaintMetadata,
    extractTerrainGeometryMetadata,
    extractObjectCatalogMetadata,
    extractModularPartMetadata,
    extractMaterialMetadata,
    extractVfxMetadata,
    extractLotDefinitionMetadata,
    extractBlueprintMetadata,
    extractRoomManifestMetadata,
    extractShellInfoMetadata,
    extractLotObjectListMetadata,
    extractFontMetadata
} from '../extractors/index.js';

/**
 * Extracts metadata from a resource based on its type.
 */
export class ResourceMetadataExtractor {
    private databaseService: DatabaseService;
    private logger: Logger;
    private simDataService: SimDataExtractionService;
    private extractorMap = new Map<number, (key: ResourceKey, buffer: Buffer, resourceId: number, databaseService: DatabaseService, logger?: Logger) => Promise<any>>();

    /**
     * Create a new ResourceMetadataExtractor.
     * @param databaseService The database service instance.
     * @param logger The logger instance.
     */
    constructor(databaseService: DatabaseService, logger: Logger) {
        this.databaseService = databaseService;
        this.logger = logger;
        this.simDataService = new SimDataExtractionService(logger);
        this.simDataService.initialize(databaseService);

        // Initialize the extractor map with resource types and their corresponding extractors
        this.initializeExtractorMap();
    }

    /**
     * Initialize the map of resource types to extractor functions
     */
    private initializeExtractorMap(): void {
        this.extractorMap = new Map();

        // XML and Tuning resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_XML, this.wrapExtractor(extractTuningXmlMetadata, 'tuningxml'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_COMBINED_TUNING, this.wrapExtractor(extractTuningXmlMetadata, 'tuningxml'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_TUNING, this.handleTuningResource.bind(this));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_STRING_TABLE, this.wrapExtractor(extractTuningXmlMetadata, 'stringtable'));

        // Object resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_OBJECT_DEFINITION, this.wrapExtractor(extractObjectMetadata, 'object', true));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_OBJECT_CATALOG, this.wrapExtractor(extractObjectCatalogMetadata, 'objectcatalog'));

        // CAS resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_CASPART, this.wrapExtractor(extractCasPartMetadata, 'caspart'));

        // Script resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SCRIPT, this.wrapExtractor(extractScriptMetadata, 'script'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SCRIPT_MODULE, this.wrapExtractor(extractScriptModuleMetadata, 'scriptmodule'));

        // Animation resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_ANIMATION, this.wrapExtractor(extractAnimationMetadata, 'animation'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_MATERIAL_DEFINITION_ALT, this.wrapExtractor(extractAnimationMetadata, 'animation'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_ANIMATION_STATE_MACHINE, this.wrapExtractor(extractAnimationStateMetadata, 'animationstate'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_ANIMATION_CLIP, this.wrapExtractor(extractAnimationStateMetadata, 'animationstate'));

        // Sound resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SOUND, this.wrapExtractor(extractAudioMetadata, 'audio'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SOUND_EFFECT, this.wrapExtractor(extractAudioMetadata, 'audio'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SOUND_DATA, this.wrapExtractor(extractAudioMetadata, 'audio'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SOUND_BANK, this.wrapExtractor(extractAudioMetadata, 'audio'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_HEADERLESS_SOUND, this.wrapExtractor(extractAudioMetadata, 'audio'));

        // Image resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_DDS_IMAGE, this.wrapExtractor(extractImageMetadata, 'image'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_PNG_IMAGE, this.wrapExtractor(extractImageMetadata, 'image'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_RLE_IMAGE, this.wrapExtractor(extractImageMetadata, 'image'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_RLE2_IMAGE, this.wrapExtractor(extractImageMetadata, 'image'));

        // Model resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_MODEL, this.wrapExtractor(extractModelMetadata, 'model'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_MODEL_LOD, this.wrapExtractor(extractModelMetadata, 'model'));

        // Build resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_FOOTPRINT, this.wrapExtractor(extractFootprintMetadata, 'footprint'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_BUILD_PART, this.wrapExtractor(extractBuildPartMetadata, 'buildpart'));

        // SimData resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SIMDATA, this.handleSimDataResource.bind(this));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_ALTERNATIVE_SIMDATA, this.handleSimDataResource.bind(this));

        // Slot and Light resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SLOT, this.wrapExtractor(extractSlotMetadata, 'slot'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_LIGHT, this.wrapExtractor(extractLightMetadata, 'light'));

        // Terrain resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_TERRAIN_GEOMETRY, this.wrapExtractor(extractTerrainGeometryMetadata, 'terraingeometry'));

        // Modular Part resources (using corrected ID)
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_MODULAR_PART_CORRECTED, this.wrapExtractor(extractModularPartMetadata, 'modularpart'));

        // Material resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_MATERIAL_DEFINITION, this.wrapExtractor(extractMaterialMetadata, 'material'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_MATERIAL_VARIANT, this.wrapExtractor(extractMaterialMetadata, 'material'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_TEXTURE_DEFINITION, this.wrapExtractor(extractMaterialMetadata, 'material'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SHADER_DEFINITION, this.wrapExtractor(extractMaterialMetadata, 'material'));

        // VFX resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_VFX_MODIFIER, this.wrapExtractor(extractVfxMetadata, 'vfx'));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_VFX_STATE, this.wrapExtractor(extractVfxMetadata, 'vfx'));

        // Lot resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_LOT_DEFINITION, this.wrapExtractor(extractLotDefinitionMetadata, 'lotdefinition', false, false));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_LOT_OBJECT_LIST, this.wrapExtractor(extractLotObjectListMetadata, 'lotobjectlist', false, false));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_BLUEPRINT, this.wrapExtractor(extractBlueprintMetadata, 'blueprint', false, false));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_ROOM_MANIFEST, this.wrapExtractor(extractRoomManifestMetadata, 'roommanifest', false, false));
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_SHELL_INFO, this.wrapExtractor(extractShellInfoMetadata, 'shellinfo', false, false));

        // Font resources
        this.extractorMap.set(ResourceTypes.RESOURCE_TYPE_OPEN_TYPE_FONT, this.wrapExtractor(extractFontMetadata, 'font'));
    }

    /**
     * Wraps an extractor function to standardize the return format
     * @param extractorFn The extractor function to wrap
     * @param extractorName The name of the extractor for logging
     * @param needsLogger Whether the extractor needs a logger parameter
     * @param needsDb Whether the extractor needs a database parameter
     * @returns A wrapped extractor function
     */
    private wrapExtractor(
        extractorFn: Function,
        extractorName: string,
        needsLogger: boolean = false,
        needsDb: boolean = true
    ): (key: ResourceKey, buffer: Buffer, resourceId: number, databaseService: DatabaseService, logger?: Logger) => Promise<any> {
        return async (key: ResourceKey, buffer: Buffer, resourceId: number, databaseService: DatabaseService, _logger?: Logger) => {
            try {
                let result;
                if (needsLogger && needsDb) {
                    result = await extractorFn(key, buffer, resourceId, databaseService, this.logger);
                } else if (needsDb) {
                    result = await extractorFn(key, buffer, resourceId, databaseService);
                } else {
                    result = await extractorFn(key, buffer);
                }

                return {
                    ...result,
                    extractorUsed: extractorName
                };
            } catch (error: any) {
                this.logger.error(`Error in ${extractorName} extractor: ${error.message || error}`);
                return {
                    extractorUsed: extractorName,
                    extractionError: error.message || 'Unknown error'
                };
            }
        };
    }

    /**
     * Special handler for Tuning resources that may be string tables
     */
    private async handleTuningResource(
        key: ResourceKey,
        buffer: Buffer,
        resourceId: number,
        databaseService: DatabaseService
    ): Promise<any> {
        try {
            // Get the current resource type from the database
            const resource = databaseService.resources.getResourceById(resourceId);
            if (!resource) {
                this.logger.error(`Resource ${resourceId} not found in database`);
                return { extractorUsed: 'unknown' };
            }

            this.logger.debug(`Resource ${resourceId} has type ${resource.type.toString(16)} and resourceType ${resource.resourceType}`);

            // If it's already identified as a string table, use the string table extractor
            if (resource.resourceType === 'STRING_TABLE') {
                this.logger.debug(`Resource ${resourceId} is already identified as a string table`);
                // String table extraction logic is now integrated below
            }

            // Otherwise, check if it's a string table by looking for the STBL magic number
            this.logger.debug(`Checking if resource ${resourceId} is a string table. Buffer length: ${buffer.length}`);
            if (buffer.length >= 4) {
                const magic = buffer.toString('utf8', 0, 4);
                this.logger.debug(`Resource ${resourceId} magic number: "${magic}"`);

                if (magic === 'STBL') {
                    this.logger.debug(`Resource ${resourceId} is a string table!`);

                    // Update the resource type in the database to STRING_TABLE using repository
                    databaseService.resources.updateResourceType(resourceId, 'STRING_TABLE');

                    // String table extraction logic is now integrated below
                }
            }

            this.logger.debug(`Resource ${resourceId} is a regular tuning XML`);
            return {
                ...await extractTuningXmlMetadata(key, buffer, resourceId, databaseService),
                extractorUsed: 'tuningxml'
            };
        } catch (error: any) {
            this.logger.error(`Error handling tuning resource: ${error.message || error}`);
            return {
                extractorUsed: 'tuning',
                extractionError: error.message || 'Unknown error'
            };
        }
    }

    /**
     * Special handler for SimData resources
     */
    private async handleSimDataResource(
        key: ResourceKey,
        buffer: Buffer,
        resourceId: number,
        databaseService: DatabaseService
    ): Promise<any> {
        try {
            // Use the unified SimData extraction service
            return await this.simDataService.extract(key, buffer, resourceId, databaseService);
        } catch (error: any) {
            this.logger.error(`Error extracting SimData metadata: ${error.message || error}`);
            return {
                extractorUsed: 'simdata',
                extractionError: error.message || 'Unknown error'
            };
        }
    }

    /**
     * Check if a resource might be a terrain paint resource
     */
    private isTerrainPaintResource(resourceType: number): boolean {
        return (
            resourceType === ResourceTypes.RESOURCE_TYPE_TUNING ||
            resourceType === ResourceTypes.RESOURCE_TYPE_OBJECT_THUMBNAIL ||
            resourceType === ResourceTypes.RESOURCE_TYPE_DDS_IMAGE ||
            resourceType === ResourceTypes.RESOURCE_TYPE_TERRAIN_PAINT_RESOURCE_1 ||
            resourceType === ResourceTypes.RESOURCE_TYPE_TERRAIN_PAINT_RESOURCE_2
        );
    }

    /**
     * Extract metadata from a resource based on its type.
     * @param key Resource key.
     * @param buffer Resource buffer.
     * @param resourceId Resource ID in the database.
     * @returns Extracted metadata.
     */
    public async extractMetadata(key: ResourceKey, buffer: Buffer, resourceId: number): Promise<any> {
        try {
            const resourceType = key.type;

            // Check if we have a specific extractor for this resource type
            if (this.extractorMap.has(resourceType)) {
                const extractor = this.extractorMap.get(resourceType)!;
                return await extractor(key, buffer, resourceId, this.databaseService, this.logger);
            }

            // Special case for terrain paint resources which are identified by a combination of types
            if (this.isTerrainPaintResource(resourceType)) {
                return {
                    ...await extractTerrainPaintMetadata(key, buffer, resourceId, this.databaseService),
                    extractorUsed: 'terrainpaint'
                };
            }

            // Generic extractor for all other resource types
            return {
                ...await extractGenericMetadata(key, buffer, resourceId, this.databaseService),
                extractorUsed: 'generic'
            };
        } catch (error: any) {
            this.logger.error(`Error extracting metadata for resource ${key.type.toString(16)}:${key.group.toString(16)}:${key.instance.toString(16)}: ${error.message || error}`);

            // Return minimal metadata in case of error
            return {
                extractorUsed: 'generic',
                extractionError: error.message || 'Unknown error'
            };
        }
    }
}