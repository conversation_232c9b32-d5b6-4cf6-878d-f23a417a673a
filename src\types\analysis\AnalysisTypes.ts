﻿// Corrected import
import { ResourceMetadata } from '../resource/interfaces.js';
// Import the central ConflictInfo type
import { ConflictInfo } from '../conflict/index.js';

export interface AnalysisResult {
  resources: ResourceMetadata[];
  conflicts: ConflictInfo[]; // Use imported ConflictInfo type
  metrics: {
    totalResources: number;
    totalConflicts: number;
    conflictsByType: Record<string, number>;
    conflictsBySeverity: Record<string, number>;
  };
  recommendations: string[];
}

export interface AnalysisOptions {
  checkConflicts?: boolean;
  checkDependencies?: boolean;
  checkVersions?: boolean;
  maxResourceSize?: number;
  ignoreTypes?: string[];
  ignorePaths?: string[];
}
