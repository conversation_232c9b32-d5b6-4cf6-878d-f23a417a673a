/**
 * Base class for conflict detectors
 * 
 * Provides common functionality for all conflict detectors.
 */

import { Logger } from '../../utils/logging/logger.js';
import { ResourceInfo } from '../../types/resource/interfaces.js';
import { ConflictInfo } from '../../types/conflict/index.js';
import { DatabaseService } from '../databaseService.js';

/**
 * Base class for conflict detectors
 */
export abstract class ConflictDetectorBase {
    protected databaseService: DatabaseService;
    protected logger: Logger;

    /**
     * Create a new conflict detector base
     * @param databaseService Database service for retrieving resource information
     * @param logger Logger for logging messages
     */
    constructor(databaseService: DatabaseService, logger?: Logger) {
        this.databaseService = databaseService;
        this.logger = logger || new Logger(this.constructor.name);
    }

    /**
     * Initialize the conflict detector
     */
    public async initialize(): Promise<void> {
        // Base implementation does nothing
        // Subclasses can override this method to perform initialization
    }

    /**
     * Detect conflicts between two resources
     * @param resource1 First resource to compare
     * @param resource2 Second resource to compare
     * @returns Array of detected conflicts
     */
    public abstract detectConflicts(
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): Promise<ConflictInfo[]>;

    /**
     * Dispose of the conflict detector
     */
    public async dispose(): Promise<void> {
        // Base implementation does nothing
        // Subclasses can override this method to perform cleanup
    }
}
