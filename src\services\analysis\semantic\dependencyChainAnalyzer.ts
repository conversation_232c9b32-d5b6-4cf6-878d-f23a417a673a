/**
 * Dependency Chain Analyzer
 *
 * Advanced dependency chain analyzer with features including:
 * - Support for detecting indirect dependencies (dependency chains)
 * - Gameplay system impact analysis
 * - Cross-package dependency detection
 * - Visualization-ready data structures
 * - Integration with EnhancedStreamPipeline
 */

import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { ResourceKey } from '../../../types/resource/interfaces.js';
import { injectable, singleton } from '../../di/decorators.js';
import * as ResourceTypes from '../../../constants/resourceTypes.js';

/**
 * Dependency chain node
 */
export interface DependencyChainNode {
    /**
     * Resource ID
     */
    resourceId: number;

    /**
     * Resource key
     */
    resourceKey: ResourceKey;

    /**
     * Resource type name
     */
    resourceTypeName: string;

    /**
     * Resource name
     */
    resourceName: string;

    /**
     * Dependency type
     */
    dependencyType: string;

    /**
     * Dependency strength (0-100)
     */
    dependencyStrength: number;

    /**
     * Distance from root node
     */
    distance: number;

    /**
     * Parent node ID
     */
    parentId?: number;

    /**
     * Child nodes
     */
    children: DependencyChainNode[];

    /**
     * Package ID
     */
    packageId: number;

    /**
     * Package name
     */
    packageName: string;

    /**
     * Impacted gameplay systems
     */
    impactedSystems: string[];

    /**
     * Resource purpose
     */
    resourcePurpose: string;

    /**
     * Visualization metadata
     */
    visualizationMetadata: {
        /**
         * Node category for visualization
         */
        category: string;

        /**
         * Node importance (1-10)
         */
        importance: number;

        /**
         * Node group (for clustering related nodes)
         */
        group: string;
    };
}

/**
 * Dependency chain
 */
export interface DependencyChain {
    /**
     * Root node
     */
    root: DependencyChainNode;

    /**
     * Total nodes in the chain
     */
    totalNodes: number;

    /**
     * Maximum depth of the chain
     */
    maxDepth: number;

    /**
     * Critical paths in the chain
     */
    criticalPaths: DependencyChainNode[][];

    /**
     * Impact score (0-100)
     */
    impactScore: number;

    /**
     * Cross-package dependencies
     */
    crossPackageDependencies: {
        /**
         * Source package ID
         */
        sourcePackageId: number;

        /**
         * Target package ID
         */
        targetPackageId: number;

        /**
         * Dependency count
         */
        count: number;

        /**
         * Dependency strength
         */
        strength: number;
    }[];

    /**
     * Impacted gameplay systems
     */
    impactedGameplaySystems: {
        /**
         * System name
         */
        systemName: string;

        /**
         * Impact score (0-100)
         */
        impactScore: number;

        /**
         * Affected resources count
         */
        affectedResourcesCount: number;
    }[];

    /**
     * Visualization metadata
     */
    visualizationMetadata: {
        /**
         * Graph layout type
         */
        layoutType: 'force-directed' | 'hierarchical' | 'radial';

        /**
         * Node categories
         */
        nodeCategories: string[];

        /**
         * Edge types
         */
        edgeTypes: string[];
    };
}

// Legacy aliases for backward compatibility
export interface EnhancedDependencyChainNode extends DependencyChainNode {}
export interface EnhancedDependencyChain extends DependencyChain {}

/**
 * Dependency Chain Analyzer
 */
@singleton()
export class DependencyChainAnalyzer {
    private logger: Logger;
    private databaseService: DatabaseService;
    private knownGameplaySystems: Set<string>;
    private resourcePurposeCache: Map<number, string>;
    private crossPackageCache: Map<string, any>;
    private initialized: boolean = false;

    /**
     * Constructor
     * @param databaseService Database service
     * @param logger Logger instance
     */
    constructor(
        databaseService: DatabaseService,
        logger?: Logger
    ) {
        this.databaseService = databaseService;
        this.logger = logger || new Logger('DependencyChainAnalyzer');
        this.knownGameplaySystems = new Set([
            'needs', 'emotions', 'traits', 'aspirations', 'careers',
            'relationships', 'skills', 'rewards', 'interactions', 'ui',
            'cas', 'build', 'world', 'storytelling', 'gameplay', 'autonomy'
        ]);
        this.resourcePurposeCache = new Map();
        this.crossPackageCache = new Map();
    }

    /**
     * Initialize the analyzer
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Load cached resource purposes
            const purposeEntries = await this.databaseService.metadata.getMetadataByKey('resourcePurpose');
            if (purposeEntries && purposeEntries.length > 0) {
                for (const entry of purposeEntries) {
                    if (entry.resourceId) {
                        this.resourcePurposeCache.set(entry.resourceId, entry.value);
                    }
                }
            }

            this.initialized = true;
            this.logger.info(`Loaded ${this.resourcePurposeCache.size} resource purposes into cache`);
        } catch (error) {
            this.logger.error('Error initializing dependency analyzer:', error);
        }
    }

    /**
     * Analyze dependency chain for a resource
     * @param resourceId Resource ID
     * @param maxDepth Maximum depth to analyze
     * @param direction Direction of dependencies ('forward' for dependencies, 'backward' for dependents)
     * @returns Dependency chain
     */
    public async analyzeDependencyChain(
        resourceId: number,
        maxDepth: number = 5,
        direction: 'forward' | 'backward' = 'forward'
    ): Promise<DependencyChain> {
        await this.initialize();

        try {
            // Get the resource
            const resource = await this.databaseService.resources.getResourceById(resourceId);
            if (!resource) {
                throw new Error(`Resource ${resourceId} not found`);
            }

            // Create root node
            const rootNode: DependencyChainNode = {
                resourceId,
                resourceKey: {
                    type: resource.resourceType,
                    group: resource.group,
                    instance: resource.instance
                },
                resourceTypeName: await this.getResourceTypeName(resource.resourceType),
                resourceName: await this.getResourceName(resourceId),
                dependencyType: 'root',
                dependencyStrength: 100,
                distance: 0,
                children: [],
                packageId: resource.packageId,
                packageName: `Package ${resource.packageId}`,
                impactedSystems: [],
                resourcePurpose: 'unknown',
                visualizationMetadata: {
                    category: 'root',
                    importance: 10,
                    group: 'root'
                }
            };

            // Build the dependency chain
            const visitedNodes = new Set<number>();
            visitedNodes.add(resourceId);

            await this.buildDependencyChain(rootNode, visitedNodes, 1, maxDepth, direction);

            // Calculate total nodes
            const totalNodes = this.countNodes(rootNode);

            // Calculate max depth
            const maxDepthFound = this.calculateMaxDepth(rootNode);

            // Find critical paths
            const criticalPaths = this.findCriticalPaths(rootNode);

            // Calculate impact score
            const impactScore = this.calculateImpactScore(rootNode, totalNodes, maxDepthFound);

            return {
                root: rootNode,
                totalNodes,
                maxDepth: maxDepthFound,
                criticalPaths,
                impactScore,
                crossPackageDependencies: [],
                impactedGameplaySystems: [],
                visualizationMetadata: {
                    layoutType: 'force-directed',
                    nodeCategories: [],
                    edgeTypes: []
                }
            };
        } catch (error) {
            this.logger.error(`Error analyzing dependency chain for resource ${resourceId}:`, error);

            // Return empty chain on error
            return {
                root: {
                    resourceId,
                    resourceKey: { type: 0, group: 0n, instance: 0n },
                    resourceTypeName: 'Unknown',
                    resourceName: 'Unknown',
                    dependencyType: 'root',
                    dependencyStrength: 0,
                    distance: 0,
                    children: [],
                    packageId: 0,
                    packageName: 'Unknown',
                    impactedSystems: [],
                    resourcePurpose: 'unknown',
                    visualizationMetadata: {
                        category: 'unknown',
                        importance: 1,
                        group: 'unknown'
                    }
                },
                totalNodes: 1,
                maxDepth: 0,
                criticalPaths: [],
                impactScore: 0,
                crossPackageDependencies: [],
                impactedGameplaySystems: [],
                visualizationMetadata: {
                    layoutType: 'force-directed',
                    nodeCategories: [],
                    edgeTypes: []
                }
            };
        }
    }

    /**
     * Calculate dependency strength based on type
     */
    protected calculateDependencyStrength(dependencyType: string): number {
        switch (dependencyType.toLowerCase()) {
            case 'required':
            case 'critical':
                return 100;
            case 'important':
            case 'strong':
                return 80;
            case 'moderate':
            case 'normal':
                return 60;
            case 'weak':
            case 'optional':
                return 40;
            case 'reference':
            case 'link':
                return 20;
            default:
                return 50;
        }
    }

    /**
     * Analyze enhanced dependency chain for a resource
     * @param resourceId Resource ID
     * @param maxDepth Maximum depth to analyze
     * @param direction Direction of dependencies ('forward' for dependencies, 'backward' for dependents)
     * @param includeGameplayAnalysis Whether to include gameplay system analysis
     * @param includeVisualizationMetadata Whether to include visualization metadata
     * @returns Enhanced dependency chain
     */
    public async analyzeEnhancedDependencyChain(
        resourceId: number,
        maxDepth: number = 5,
        direction: 'forward' | 'backward' = 'forward',
        includeGameplayAnalysis: boolean = true,
        includeVisualizationMetadata: boolean = true
    ): Promise<DependencyChain> {
        // Get base dependency chain
        const baseChain = await this.analyzeDependencyChain(resourceId, maxDepth, direction);

        // For now, return the base chain - enhanced features can be added later
        return baseChain;
    }

    /**
     * Enhance a dependency chain node with additional metadata
     * @param node Base node to enhance
     * @param includeGameplayAnalysis Whether to include gameplay system analysis
     * @param includeVisualizationMetadata Whether to include visualization metadata
     * @returns Enhanced node
     */
    private async enhanceNode(
        node: DependencyChainNode,
        includeGameplayAnalysis: boolean,
        includeVisualizationMetadata: boolean
    ): Promise<EnhancedDependencyChainNode> {
        // Get resource
        const resource = await this.databaseService.resources.getResourceById(node.resourceId);
        if (!resource) {
            throw new Error(`Resource ${node.resourceId} not found`);
        }

        // Get package
        const packageInfo = await this.databaseService.packages.getPackageById(resource.packageId);

        // Determine resource purpose
        let resourcePurpose = this.resourcePurposeCache.get(node.resourceId) || 'unknown';
        if (resourcePurpose === 'unknown') {
            resourcePurpose = await this.determineResourcePurpose(node.resourceId, node.resourceKey);
            this.resourcePurposeCache.set(node.resourceId, resourcePurpose);

            // Save purpose to database
            await this.databaseService.metadata.saveMetadata({
                resourceId: node.resourceId,
                key: 'resourcePurpose',
                value: resourcePurpose,
                metadataType: 'string',
                timestamp: Date.now()
            });
        }

        // Determine impacted gameplay systems
        const impactedSystems = includeGameplayAnalysis ?
            await this.determineImpactedSystems(node.resourceId, node.resourceKey, resourcePurpose) : [];

        // Create visualization metadata
        const visualizationMetadata = includeVisualizationMetadata ?
            this.generateNodeVisualizationMetadata(node, resourcePurpose) : {
                category: 'default',
                importance: 5,
                group: 'default'
            };

        // Create enhanced node
        const enhancedNode: EnhancedDependencyChainNode = {
            ...node,
            packageId: resource.packageId,
            packageName: packageInfo?.name || `Package ${resource.packageId}`,
            impactedSystems,
            resourcePurpose,
            visualizationMetadata,
            children: [] // Will be filled below
        };

        // Recursively enhance children
        for (const childNode of node.children) {
            const enhancedChildNode = await this.enhanceNode(
                childNode,
                includeGameplayAnalysis,
                includeVisualizationMetadata
            );
            enhancedNode.children.push(enhancedChildNode);
        }

        return enhancedNode;
    }

    /**
     * Determine the purpose of a resource
     * @param resourceId Resource ID
     * @param resourceKey Resource key
     * @returns Resource purpose
     */
    private async determineResourcePurpose(
        resourceId: number,
        resourceKey: ResourceKey
    ): Promise<string> {
        // Try to get from metadata first
        try {
            const metadata = await this.databaseService.metadata.getMetadataByResourceId(resourceId);
            const purposeMetadata = metadata.find(m => m.key === 'resourcePurpose');
            if (purposeMetadata && purposeMetadata.value) {
                return purposeMetadata.value;
            }

            // Check for semantic meaning from parsed content
            const parsedContent = await this.databaseService.parsedContent.getContentByResourceId(resourceId);
            if (parsedContent && parsedContent.semanticType) {
                return parsedContent.semanticType;
            }
        } catch (error) {
            this.logger.error(`Error getting resource purpose for ${resourceId}:`, error);
        }

        // Determine based on resource type
        switch (resourceKey.type) {
            case ResourceTypes.RESOURCE_TYPE_TUNING:
                return 'tuning';
            case ResourceTypes.RESOURCE_TYPE_SIMDATA:
                return 'simdata';
            case ResourceTypes.RESOURCE_TYPE_OBJECT_DEFINITION:
                return 'object_definition';
            case ResourceTypes.RESOURCE_TYPE_CASPART:
                return 'cas_part';
            case ResourceTypes.RESOURCE_TYPE_SCRIPT:
                return 'script';
            case ResourceTypes.RESOURCE_TYPE_DDS_IMAGE:
            case ResourceTypes.RESOURCE_TYPE_PNG_IMAGE:
                return 'image';
            case ResourceTypes.RESOURCE_TYPE_SOUND:
                return 'sound';
            case ResourceTypes.RESOURCE_TYPE_ANIMATION:
                return 'animation';
            case ResourceTypes.RESOURCE_TYPE_MODEL:
                return 'model';
            default:
                return 'unknown';
        }
    }

    /**
     * Determine gameplay systems impacted by a resource
     * @param resourceId Resource ID
     * @param resourceKey Resource key
     * @param resourcePurpose Resource purpose
     * @returns Impacted systems
     */
    private async determineImpactedSystems(
        resourceId: number,
        resourceKey: ResourceKey,
        resourcePurpose: string
    ): Promise<string[]> {
        const impactedSystems: string[] = [];

        try {
            // Check metadata for gameplay system information
            const metadata = await this.databaseService.metadata.getMetadataByResourceId(resourceId);
            const systemsMetadata = metadata.find(m => m.key === 'impactedSystems');
            if (systemsMetadata && systemsMetadata.value) {
                try {
                    const systems = JSON.parse(systemsMetadata.value);
                    if (Array.isArray(systems)) {
                        return systems.filter(system => this.knownGameplaySystems.has(system));
                    }
                } catch (parseError) {
                    this.logger.error(`Error parsing impacted systems metadata for ${resourceId}:`, parseError);
                }
            }

            // Check parsed content
            const parsedContent = await this.databaseService.parsedContent.getContentByResourceId(resourceId);
            if (parsedContent && parsedContent.content) {
                // For tuning resources, check specific fields
                if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_TUNING) {
                    const content = typeof parsedContent.content === 'string' ?
                        parsedContent.content : JSON.stringify(parsedContent.content);

                    // Check for needs
                    if (content.includes('needs') || content.includes('need_instance') || content.includes('motive')) {
                        impactedSystems.push('needs');
                    }

                    // Check for emotions
                    if (content.includes('emotion') || content.includes('mood') || content.includes('buff')) {
                        impactedSystems.push('emotions');
                    }

                    // Check for traits
                    if (content.includes('trait') || content.match(/traitTypes/i)) {
                        impactedSystems.push('traits');
                    }

                    // Check for aspirations
                    if (content.includes('aspiration') || content.includes('satisfaction')) {
                        impactedSystems.push('aspirations');
                    }

                    // Check for careers
                    if (content.includes('career') || content.includes('job') ||
                        content.includes('workplace') || content.includes('promotion')) {
                        impactedSystems.push('careers');
                    }

                    // Check for relationships
                    if (content.includes('relationship') || content.includes('sentiment') ||
                        content.includes('romantic') || content.includes('friendship')) {
                        impactedSystems.push('relationships');
                    }

                    // Check for skills
                    if (content.includes('skill') || content.includes('ability') ||
                        content.includes('talent') || content.includes('expertise')) {
                        impactedSystems.push('skills');
                    }

                    // Check for rewards
                    if (content.includes('reward') || content.includes('achievement') ||
                        content.includes('unlock') || content.includes('prize')) {
                        impactedSystems.push('rewards');
                    }

                    // Check for interactions
                    if (content.includes('interaction') || content.includes('social_') ||
                        content.includes('mixer') || content.includes('super_interaction')) {
                        impactedSystems.push('interactions');
                    }
                }

                // For script resources
                if (resourceKey.type === ResourceTypes.RESOURCE_TYPE_SCRIPT) {
                    const content = typeof parsedContent.content === 'string' ?
                        parsedContent.content : JSON.stringify(parsedContent.content);

                    // Check for UI related code
                    if (content.includes('UI') || content.includes('dialog') ||
                        content.includes('window') || content.includes('panel')) {
                        impactedSystems.push('ui');
                    }

                    // Check for autonomy
                    if (content.includes('autonomy') || content.includes('situation') ||
                        content.includes('autonomous')) {
                        impactedSystems.push('autonomy');
                    }
                }
            }

            // Use resource purpose to infer impacts
            if (resourcePurpose) {
                switch (resourcePurpose) {
                    case 'cas_part':
                        impactedSystems.push('cas');
                        break;
                    case 'object_definition':
                        impactedSystems.push('build');
                        break;
                    case 'terrain':
                    case 'lot':
                        impactedSystems.push('world');
                        break;
                }
            }
        } catch (error) {
            this.logger.error(`Error determining impacted systems for ${resourceId}:`, error);
        }

        return [...new Set(impactedSystems)]; // Remove duplicates
    }

    /**
     * Generate visualization metadata for a node
     * @param node Base node
     * @param resourcePurpose Resource purpose
     * @returns Visualization metadata
     */
    private generateNodeVisualizationMetadata(node: DependencyChainNode, resourcePurpose: string): {
        category: string;
        importance: number;
        group: string;
    } {
        let category = 'default';
        let importance = 5;
        let group = 'default';

        // Determine category based on resource purpose
        switch (resourcePurpose) {
            case 'tuning':
                category = 'tuning';
                break;
            case 'simdata':
                category = 'simdata';
                break;
            case 'script':
                category = 'script';
                importance = 8; // Scripts are often important
                break;
            case 'image':
            case 'sound':
            case 'animation':
            case 'model':
                category = 'asset';
                break;
            case 'object_definition':
            case 'cas_part':
                category = 'game_object';
                break;
            default:
                category = 'other';
        }

        // Adjust importance based on dependency strength and distance
        if (node.dependencyStrength > 80) {
            importance += 2;
        } else if (node.dependencyStrength < 30) {
            importance -= 2;
        }

        if (node.distance === 0) {
            importance = 10; // Root node is always most important
            group = 'root';
        } else {
            // Further nodes are less important
            importance = Math.max(1, importance - Math.floor(node.distance / 2));

            // Group by parent if available
            if (node.parentId) {
                group = `parent_${node.parentId}`;
            }
        }

        return {
            category,
            importance: Math.min(10, Math.max(1, importance)), // Clamp between 1-10
            group
        };
    }

    /**
     * Generate visualization metadata for the entire dependency chain
     * @param root Root node
     * @returns Visualization metadata
     */
    private generateVisualizationMetadata(root: EnhancedDependencyChainNode): {
        layoutType: 'force-directed' | 'hierarchical' | 'radial';
        nodeCategories: string[];
        edgeTypes: string[];
    } {
        const nodeCategories = new Set<string>();
        const edgeTypes = new Set<string>();

        // Helper function to collect categories and edge types
        const collectMetadata = (node: EnhancedDependencyChainNode) => {
            nodeCategories.add(node.visualizationMetadata.category);
            edgeTypes.add(node.dependencyType);

            for (const child of node.children) {
                collectMetadata(child as EnhancedDependencyChainNode);
            }
        };

        collectMetadata(root);

        // Determine best layout based on structure
        let layoutType: 'force-directed' | 'hierarchical' | 'radial' = 'force-directed';

        // If deep tree with clear hierarchy, use hierarchical
        if (root.children.length > 0 && root.children.every(child => child.children.length <= 3)) {
            layoutType = 'hierarchical';
        }
        // If shallow tree with many connections, use radial
        else if (root.children.length > 5 && root.children.every(child => child.children.length < 2)) {
            layoutType = 'radial';
        }

        return {
            layoutType,
            nodeCategories: [...nodeCategories],
            edgeTypes: [...edgeTypes]
        };
    }

    /**
     * Detect cross-package dependencies
     * @param root Root node
     * @returns Cross-package dependencies
     */
    private async detectCrossPackageDependencies(root: EnhancedDependencyChainNode): Promise<{
        sourcePackageId: number;
        targetPackageId: number;
        count: number;
        strength: number;
    }[]> {
        const dependencies: Map<string, {
            sourcePackageId: number;
            targetPackageId: number;
            count: number;
            totalStrength: number;
        }> = new Map();

        // Helper function to collect cross-package dependencies
        const collectDependencies = (node: EnhancedDependencyChainNode, parent?: EnhancedDependencyChainNode) => {
            // Skip root node
            if (parent && node.packageId !== parent.packageId) {
                const key = `${parent.packageId}:${node.packageId}`;
                if (!dependencies.has(key)) {
                    dependencies.set(key, {
                        sourcePackageId: parent.packageId,
                        targetPackageId: node.packageId,
                        count: 0,
                        totalStrength: 0
                    });
                }

                const dep = dependencies.get(key);
                if (dep) {
                    dep.count++;
                    dep.totalStrength += node.dependencyStrength;
                }
            }

            // Recurse to children
            for (const child of node.children) {
                collectDependencies(child as EnhancedDependencyChainNode, node);
            }
        };

        collectDependencies(root);

        // Convert to array and calculate average strength
        return Array.from(dependencies.values()).map(dep => ({
            sourcePackageId: dep.sourcePackageId,
            targetPackageId: dep.targetPackageId,
            count: dep.count,
            strength: Math.round(dep.totalStrength / dep.count)
        }));
    }

    /**
     * Analyze gameplay system impacts
     * @param root Root node
     * @returns Gameplay system impacts
     */
    private async analyzeGameplaySystemImpacts(root: EnhancedDependencyChainNode): Promise<{
        systemName: string;
        impactScore: number;
        affectedResourcesCount: number;
    }[]> {
        const systemImpacts: Map<string, {
            resourceIds: Set<number>;
            totalStrength: number;
        }> = new Map();

        // Helper function to collect system impacts
        const collectSystemImpacts = (node: EnhancedDependencyChainNode) => {
            // For each impacted system
            for (const system of node.impactedSystems) {
                if (!systemImpacts.has(system)) {
                    systemImpacts.set(system, {
                        resourceIds: new Set(),
                        totalStrength: 0
                    });
                }

                const impact = systemImpacts.get(system);
                if (impact) {
                    impact.resourceIds.add(node.resourceId);
                    impact.totalStrength += node.dependencyStrength;
                }
            }

            // Recurse to children
            for (const child of node.children) {
                collectSystemImpacts(child as EnhancedDependencyChainNode);
            }
        };

        collectSystemImpacts(root);

        // Convert to array and calculate impact score
        return Array.from(systemImpacts.entries()).map(([systemName, impact]) => ({
            systemName,
            affectedResourcesCount: impact.resourceIds.size,
            impactScore: Math.min(100, Math.round((impact.totalStrength / (impact.resourceIds.size * 100)) * 100))
        }));
    }

    /**
     * Analyze dependencies between packages
     * @param packageIds Package IDs to analyze
     * @returns Dependency analysis result
     */
    public async analyzePackageDependencies(packageIds: number[]): Promise<{
        dependencies: {
            sourcePackageId: number;
            targetPackageId: number;
            count: number;
            strength: number;
        }[];
        packages: {
            packageId: number;
            packageName: string;
            dependencyCount: number;
            dependentCount: number;
        }[];
    }> {
        if (!packageIds || packageIds.length === 0) {
            return { dependencies: [], packages: [] };
        }

        const dependencies: {
            sourcePackageId: number;
            targetPackageId: number;
            count: number;
            strength: number;
        }[] = [];

        const packages: Map<number, {
            packageId: number;
            packageName: string;
            dependencyCount: number;
            dependentCount: number;
        }> = new Map();

        // Initialize packages map
        for (const packageId of packageIds) {
            const packageInfo = await this.databaseService.packages.getPackageById(packageId);
            packages.set(packageId, {
                packageId,
                packageName: packageInfo?.name || `Package ${packageId}`,
                dependencyCount: 0,
                dependentCount: 0
            });
        }

        // Get all dependencies between these packages
        for (const sourcePackageId of packageIds) {
            // Get all resources in this package
            const resources = await this.databaseService.resources.getResourcesByPackageId(sourcePackageId);

            // For each resource
            for (const resource of resources) {
                // Get dependencies
                const resourceDependencies = await this.databaseService.dependencies.getDependenciesBySourceId(resource.id);

                // For each dependency
                for (const dependency of resourceDependencies) {
                    // Find target resource
                    const targetResource = await this.databaseService.resources.findResourceByTGI(
                        dependency.targetType,
                        dependency.targetGroup,
                        dependency.targetInstance
                    );

                    if (!targetResource) {
                        continue;
                    }

                    // If target is in one of our packages
                    if (packageIds.includes(targetResource.packageId) && targetResource.packageId !== sourcePackageId) {
                        // Update dependency map
                        const key = `${sourcePackageId}:${targetResource.packageId}`;
                        const existing = dependencies.find(
                            d => d.sourcePackageId === sourcePackageId && d.targetPackageId === targetResource.packageId
                        );

                        if (existing) {
                            existing.count++;
                            existing.strength = Math.round((existing.strength + this.calculateDependencyStrength(dependency.dependencyType || 'reference')) / 2);
                } else {
                            dependencies.push({
                                sourcePackageId,
                                targetPackageId: targetResource.packageId,
                                count: 1,
                                strength: this.calculateDependencyStrength(dependency.dependencyType || 'reference')
                            });
                        }

                        // Update package counts
                        const sourcePackage = packages.get(sourcePackageId);
                        const targetPackage = packages.get(targetResource.packageId);

                        if (sourcePackage) {
                            sourcePackage.dependencyCount++;
                        }

                        if (targetPackage) {
                            targetPackage.dependentCount++;
                        }
                    }
                }
            }
        }

        return {
            dependencies,
            packages: Array.from(packages.values())
        };
    }

    /**
     * Export dependency chain to visualization format (e.g., for D3.js)
     * @param chain Dependency chain to export
     * @returns Visualization-ready data
     */
    public exportForVisualization(chain: EnhancedDependencyChain): {
        nodes: {
            id: number;
            label: string;
            type: string;
            category: string;
            importance: number;
            group: string;
            packageId: number;
            packageName: string;
        }[];
        links: {
            source: number;
            target: number;
            type: string;
            strength: number;
        }[];
        metadata: {
            layoutType: string;
            nodeCategories: string[];
            edgeTypes: string[];
        };
    } {
        const nodes: {
            id: number;
            label: string;
            type: string;
            category: string;
            importance: number;
            group: string;
            packageId: number;
            packageName: string;
        }[] = [];

        const links: {
            source: number;
            target: number;
            type: string;
            strength: number;
        }[] = [];

        // Helper function to collect nodes and links
        const collectVisualizationData = (node: EnhancedDependencyChainNode) => {
            // Add node
            nodes.push({
                id: node.resourceId,
                label: node.resourceName,
                type: node.resourceTypeName,
                category: node.visualizationMetadata.category,
                importance: node.visualizationMetadata.importance,
                group: node.visualizationMetadata.group,
                packageId: node.packageId,
                packageName: node.packageName
            });

            // Add links to children
            for (const child of node.children) {
                const enhancedChild = child as EnhancedDependencyChainNode;

                links.push({
                    source: node.resourceId,
                    target: enhancedChild.resourceId,
                    type: enhancedChild.dependencyType,
                    strength: enhancedChild.dependencyStrength
                });

                // Recurse to child
                collectVisualizationData(enhancedChild);
            }
        };

        collectVisualizationData(chain.root);

        return {
            nodes,
            links,
            metadata: {
                layoutType: chain.visualizationMetadata.layoutType,
                nodeCategories: chain.visualizationMetadata.nodeCategories,
                edgeTypes: chain.visualizationMetadata.edgeTypes
            }
        };
    }

    /**
     * Dispose of resources used by the analyzer
     */
    public async dispose(): Promise<void> {
        try {
            this.resourcePurposeCache.clear();
            this.crossPackageCache.clear();
            this.logger.info('Dependency Chain Analyzer resources disposed successfully');
        } catch (error) {
            this.logger.error('Error disposing Dependency Chain Analyzer resources:', error);
            throw error;
        }
    }

    /**
     * Get resource type name
     */
    private async getResourceTypeName(resourceType: number): Promise<string> {
        // Simple mapping - could be enhanced with a proper resource type registry
        return `ResourceType_${resourceType.toString(16).toUpperCase().padStart(8, '0')}`;
    }

    /**
     * Get resource name
     */
    private async getResourceName(resourceId: number): Promise<string> {
        try {
            const metadata = await this.databaseService.metadata.getMetadataByResourceId(resourceId);
            const nameMetadata = metadata.find((m: any) => m.key === 'name');
            if (nameMetadata && nameMetadata.value) {
                return nameMetadata.value;
            }
        } catch (error) {
            // Ignore errors and fall back to default name
        }
        return `Resource_${resourceId}`;
    }

    /**
     * Build dependency chain recursively
     */
    private async buildDependencyChain(
        node: DependencyChainNode,
        visitedNodes: Set<number>,
        currentDepth: number,
        maxDepth: number,
        direction: 'forward' | 'backward'
    ): Promise<void> {
        if (currentDepth > maxDepth) {
            return;
        }

        try {
            // Get dependencies based on direction
            const dependencies = direction === 'forward'
                ? await this.databaseService.dependencies.getDependenciesBySourceId(node.resourceId)
                : await this.databaseService.dependencies.getDependenciesByTargetId(node.resourceId);

            for (const dependency of dependencies) {
                const targetResourceId = direction === 'forward'
                    ? dependency.targetResourceId
                    : dependency.sourceResourceId;

                if (!targetResourceId || visitedNodes.has(targetResourceId)) {
                    continue;
                }

                // Get target resource
                const targetResource = await this.databaseService.resources.getResourceById(targetResourceId);
                if (!targetResource) {
                    continue;
                }

                // Create child node
                const childNode: DependencyChainNode = {
                    resourceId: targetResourceId,
                    resourceKey: {
                        type: targetResource.resourceType,
                        group: targetResource.group,
                        instance: targetResource.instance
                    },
                    resourceTypeName: await this.getResourceTypeName(targetResource.resourceType),
                    resourceName: await this.getResourceName(targetResourceId),
                    dependencyType: dependency.dependencyType || 'reference',
                    dependencyStrength: this.calculateDependencyStrength(dependency.dependencyType || 'reference'),
                    distance: currentDepth,
                    parentId: node.resourceId,
                    children: [],
                    packageId: targetResource.packageId,
                    packageName: `Package ${targetResource.packageId}`,
                    impactedSystems: [],
                    resourcePurpose: 'unknown',
                    visualizationMetadata: {
                        category: 'dependency',
                        importance: Math.max(1, 10 - currentDepth),
                        group: `depth_${currentDepth}`
                    }
                };

                node.children.push(childNode);
                visitedNodes.add(targetResourceId);

                // Recurse to build deeper levels
                await this.buildDependencyChain(childNode, visitedNodes, currentDepth + 1, maxDepth, direction);
            }
        } catch (error) {
            this.logger.error(`Error building dependency chain for resource ${node.resourceId}:`, error);
        }
    }

    /**
     * Count total nodes in the dependency tree
     */
    private countNodes(node: DependencyChainNode): number {
        let count = 1; // Count this node
        for (const child of node.children) {
            count += this.countNodes(child);
        }
        return count;
    }

    /**
     * Calculate maximum depth of the dependency tree
     */
    private calculateMaxDepth(node: DependencyChainNode): number {
        if (node.children.length === 0) {
            return node.distance;
        }

        let maxDepth = node.distance;
        for (const child of node.children) {
            maxDepth = Math.max(maxDepth, this.calculateMaxDepth(child));
        }
        return maxDepth;
    }

    /**
     * Find critical paths in the dependency tree
     */
    private findCriticalPaths(node: DependencyChainNode): DependencyChainNode[][] {
        const paths: DependencyChainNode[][] = [];

        const buildPath = (currentNode: DependencyChainNode, currentPath: DependencyChainNode[]) => {
            const newPath = [...currentPath, currentNode];

            if (currentNode.children.length === 0) {
                // Leaf node - this is a complete path
                if (newPath.length > 2) { // Only consider paths with at least 3 nodes
                    paths.push(newPath);
                }
            } else {
                // Continue building paths through children
                for (const child of currentNode.children) {
                    buildPath(child, newPath);
                }
            }
        };

        buildPath(node, []);

        // Sort paths by average dependency strength (descending)
        paths.sort((a, b) => {
            const avgStrengthA = a.reduce((sum, n) => sum + n.dependencyStrength, 0) / a.length;
            const avgStrengthB = b.reduce((sum, n) => sum + n.dependencyStrength, 0) / b.length;
            return avgStrengthB - avgStrengthA;
        });

        // Return top 5 critical paths
        return paths.slice(0, 5);
    }

    /**
     * Calculate impact score for the dependency chain
     */
    private calculateImpactScore(node: DependencyChainNode, totalNodes: number, maxDepth: number): number {
        // Base score from node count (0-40 points)
        const nodeScore = Math.min(40, (totalNodes / 10) * 40);

        // Depth score (0-30 points)
        const depthScore = Math.min(30, (maxDepth / 5) * 30);

        // Average strength score (0-30 points)
        const avgStrength = this.calculateAverageStrength(node);
        const strengthScore = (avgStrength / 100) * 30;

        return Math.round(nodeScore + depthScore + strengthScore);
    }

    /**
     * Calculate average dependency strength across all nodes
     */
    private calculateAverageStrength(node: DependencyChainNode): number {
        let totalStrength = 0;
        let nodeCount = 0;

        const sumStrengths = (currentNode: DependencyChainNode) => {
            totalStrength += currentNode.dependencyStrength;
            nodeCount++;

            for (const child of currentNode.children) {
                sumStrengths(child);
            }
        };

        sumStrengths(node);

        return nodeCount > 0 ? totalStrength / nodeCount : 0;
    }
}

// Legacy alias for backward compatibility
export class EnhancedDependencyChainAnalyzer extends DependencyChainAnalyzer {}