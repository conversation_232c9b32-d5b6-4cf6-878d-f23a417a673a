/**
 * Schema repository for SimData schemas
 * Stores and manages SimData schemas for reference and analysis
 */

import { Logger } from '../../../../../utils/logging/logger.js';
import { SimDataSchema } from '../simDataParser.js';
import { SchemaAnalysisResult, SchemaRepositoryEntry, SchemaRelationshipInfo } from './schemaInterfaces.js';
import { SchemaAnalyzer } from './schemaAnalyzer.js';
import { SchemaCompatibilityAnalyzer } from './schemaCompatibilityAnalyzer.js';
import { DatabaseService } from '../../../../databaseService.js';
import { SchemaDatabase } from './schemaDatabase.js';

const log = new Logger('SchemaRepository');

/**
 * Schema Repository
 * Stores and manages SimData schemas for reference and analysis
 */
export class SchemaRepository {
    private schemaAnalyzer: SchemaAnalyzer;
    private compatibilityAnalyzer: SchemaCompatibilityAnalyzer;
    private databaseService: DatabaseService;
    private schemaDatabase: SchemaDatabase;
    private schemaCache: Map<string, SchemaRepositoryEntry> = new Map();
    private initialized: boolean = false;

    constructor(databaseService: DatabaseService) {
        this.schemaAnalyzer = new SchemaAnalyzer();
        this.compatibilityAnalyzer = new SchemaCompatibilityAnalyzer();
        this.databaseService = databaseService;
        this.schemaDatabase = SchemaDatabase.getInstance(databaseService);
    }

    /**
     * Initialize the repository
     * Loads schemas from the database
     */
    public async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Initialize the schema database
            await this.schemaDatabase.initialize();

            // Load schemas from database (legacy code for backward compatibility)
            try {
                const schemas = await this.databaseService.simDataSchemas.getAllSchemas();

                for (const schema of schemas) {
                    try {
                        const schemaObj = JSON.parse(schema.schemaData);
                        const analysisObj = JSON.parse(schema.analysisData);

                        this.schemaCache.set(schema.schemaName, {
                            schema: schemaObj,
                            analysis: analysisObj,
                            occurrences: schema.occurrences,
                            modIds: schema.modIds ? JSON.parse(schema.modIds) : []
                        });

                        // Register with the new schema database
                        await this.schemaDatabase.registerSchema(schemaObj);
                    } catch (error) {
                        log.error(`Error parsing schema data for ${schema.schemaName}: ${error}`);
                    }
                }

                log.info(`Loaded ${this.schemaCache.size} schemas from legacy database`);
            } catch (error) {
                log.warn(`Error loading from legacy database: ${error}`);
                // Continue with new schema database
            }

            this.initialized = true;
        } catch (error) {
            log.error(`Error initializing schema repository: ${error}`);
            throw error;
        }
    }

    /**
     * Register a schema with the repository
     * @param schema The SimData schema
     * @param modId The mod ID
     * @returns The schema analysis result
     */
    public async registerSchema(schema: SimDataSchema, modId: number): Promise<SchemaAnalysisResult> {
        if (!this.initialized) {
            await this.initialize();
        }

        const schemaName = schema.name;

        // Analyze the schema
        const analysis = this.schemaAnalyzer.analyzeSchema(schema);

        // Register with the new schema database
        await this.schemaDatabase.registerSchema(schema);

        // Check if we already have this schema in the legacy cache
        if (this.schemaCache.has(schemaName)) {
            const entry = this.schemaCache.get(schemaName)!;

            // Update occurrences and mod IDs
            entry.occurrences++;
            if (!entry.modIds.includes(modId)) {
                entry.modIds.push(modId);
            }

            // Update in legacy database
            try {
                await this.databaseService.simDataSchemas.updateSchema(schemaName, {
                    occurrences: entry.occurrences,
                    modIds: JSON.stringify(entry.modIds)
                });
            } catch (error) {
                log.warn(`Error updating legacy database: ${error}`);
                // Continue with new schema database
            }

            return entry.analysis;
        }

        // This is a new schema
        const entry: SchemaRepositoryEntry = {
            schema,
            analysis,
            occurrences: 1,
            modIds: [modId]
        };

        this.schemaCache.set(schemaName, entry);

        // Save to legacy database
        try {
            await this.databaseService.simDataSchemas.saveSchema({
                schemaName,
                schemaId: schema.schemaId,
                schemaHash: schema.hash,
                schemaData: JSON.stringify(schema),
                analysisData: JSON.stringify(analysis),
                occurrences: 1,
                modIds: JSON.stringify([modId])
            });
        } catch (error) {
            log.warn(`Error saving to legacy database: ${error}`);
            // Continue with new schema database
        }

        // If this is a child schema, update relationships
        if (analysis.inheritance) {
            // Register relationship with the new schema database
            const inheritanceInfo = await this.schemaDatabase.analyzeSchemaInheritance(schema);
            if (inheritanceInfo) {
                await this.schemaDatabase.registerSchemaRelationship(
                    inheritanceInfo.child,
                    inheritanceInfo.parent,
                    'inherits_from',
                    90,
                    `Inherits ${inheritanceInfo.inheritedColumns.length} columns and adds ${inheritanceInfo.addedColumns.length} new columns`
                );
            }

            // Update relationships in legacy system
            await this.updateSchemaRelationships(schemaName, analysis.inheritance.parent);
        }

        return analysis;
    }

    /**
     * Get a schema by name
     * @param schemaName The schema name
     * @returns The schema repository entry or undefined if not found
     */
    public async getSchema(schemaName: string): Promise<SchemaRepositoryEntry | undefined> {
        if (!this.initialized) {
            await this.initialize();
        }

        return this.schemaCache.get(schemaName);
    }

    /**
     * Find schemas by pattern
     * @param pattern The pattern to match
     * @returns Array of matching schema names
     */
    public async findSchemas(pattern: string): Promise<string[]> {
        if (!this.initialized) {
            await this.initialize();
        }

        const patternLower = pattern.toLowerCase();
        const matches: string[] = [];

        for (const schemaName of this.schemaCache.keys()) {
            if (schemaName.toLowerCase().includes(patternLower)) {
                matches.push(schemaName);
            }
        }

        return matches;
    }

    /**
     * Update schema relationships
     * @param childSchemaName The child schema name
     * @param parentSchemaName The parent schema name
     */
    private async updateSchemaRelationships(childSchemaName: string, parentSchemaName: string): Promise<void> {
        // Get the child schema
        const childEntry = this.schemaCache.get(childSchemaName);
        if (!childEntry) {
            return;
        }

        // Get the parent schema
        const parentEntry = this.schemaCache.get(parentSchemaName);
        if (!parentEntry) {
            return;
        }

        // Update child schema relationships
        if (!childEntry.relationships) {
            childEntry.relationships = {
                schemaName: childSchemaName,
                relatedSchemas: []
            };
        }

        // Add parent relationship if not already present
        if (!childEntry.relationships.relatedSchemas.some(rel => rel.name === parentSchemaName)) {
            childEntry.relationships.relatedSchemas.push({
                name: parentSchemaName,
                relationship: 'parent',
                confidence: 100
            });
        }

        // Update parent schema relationships
        if (!parentEntry.relationships) {
            parentEntry.relationships = {
                schemaName: parentSchemaName,
                relatedSchemas: []
            };
        }

        // Add child relationship if not already present
        if (!parentEntry.relationships.relatedSchemas.some(rel => rel.name === childSchemaName)) {
            parentEntry.relationships.relatedSchemas.push({
                name: childSchemaName,
                relationship: 'child',
                confidence: 100
            });
        }

        // Save relationships to database
        await this.databaseService.simDataSchemas.updateSchema(childSchemaName, {
            relationshipsData: JSON.stringify(childEntry.relationships)
        });

        await this.databaseService.simDataSchemas.updateSchema(parentSchemaName, {
            relationshipsData: JSON.stringify(parentEntry.relationships)
        });
    }

    /**
     * Find schemas that might conflict with a given schema
     * @param schema The SimData schema
     * @returns Array of potentially conflicting schemas
     */
    public async findPotentialConflicts(schema: SimDataSchema): Promise<Array<{
        schemaName: string;
        compatibility: number;
        conflicts: string[];
    }>> {
        if (!this.initialized) {
            await this.initialize();
        }

        const conflicts: Array<{
            schemaName: string;
            compatibility: number;
            conflicts: string[];
        }> = [];

        // First, check for schemas with the same name
        const existingSchema = await this.schemaDatabase.getSchemaByName(schema.name);
        if (existingSchema && existingSchema.schemaId !== schema.schemaId) {
            // Compare schemas
            const compatibility = await this.schemaDatabase.checkSchemaCompatibility(schema, existingSchema);

            // If there are incompatibilities, add to conflicts
            if (!compatibility.isCompatible) {
                conflicts.push({
                    schemaName: schema.name,
                    compatibility: compatibility.compatibilityScore,
                    conflicts: [
                        ...compatibility.incompatibleColumns.map(col => `Type mismatch: ${col}`),
                        ...compatibility.missingColumns.map(col => `Missing column: ${col}`),
                        ...compatibility.extraColumns.map(col => `Extra column: ${col}`)
                    ]
                });
            }
        }

        // Check for related schemas
        const relatedSchemas = await this.schemaDatabase.getRelatedSchemas(schema.name);
        for (const relatedName of relatedSchemas) {
            const relatedSchema = await this.schemaDatabase.getSchemaByName(relatedName);
            if (relatedSchema) {
                // Compare schemas
                const compatibility = await this.schemaDatabase.checkSchemaCompatibility(schema, relatedSchema);

                // If there are incompatibilities, add to conflicts
                if (!compatibility.isCompatible) {
                    conflicts.push({
                        schemaName: relatedName,
                        compatibility: compatibility.compatibilityScore,
                        conflicts: [
                            ...compatibility.incompatibleColumns.map(col => `Type mismatch: ${col}`),
                            ...compatibility.missingColumns.map(col => `Missing column: ${col}`),
                            ...compatibility.extraColumns.map(col => `Extra column: ${col}`)
                        ]
                    });
                }
            }
        }

        // Legacy check for backward compatibility
        try {
            // Check each schema in the repository
            for (const [name, entry] of this.schemaCache.entries()) {
                // Skip if it's the same schema or already checked
                if (name === schema.name || conflicts.some(c => c.schemaName === name)) {
                    continue;
                }

                // Compare schemas
                const compatibility = this.compatibilityAnalyzer.compareSchemas(schema, entry.schema);

                // If there are incompatibilities, add to conflicts
                if (!compatibility.isCompatible) {
                    conflicts.push({
                        schemaName: name,
                        compatibility: compatibility.compatibilityScore,
                        conflicts: [
                            ...compatibility.incompatibleColumns.map(col => `Type mismatch: ${col}`),
                            ...compatibility.missingColumns.map(col => `Missing column: ${col}`),
                            ...compatibility.extraColumns.map(col => `Extra column: ${col}`)
                        ]
                    });
                }
            }
        } catch (error) {
            log.warn(`Error checking legacy conflicts: ${error}`);
            // Continue with new schema database results
        }

        // Sort by compatibility score (ascending)
        conflicts.sort((a, b) => a.compatibility - b.compatibility);

        return conflicts;
    }
}
