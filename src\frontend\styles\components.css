/* Base styles */
:root {
  --primary-color: #4a90e2;
  --secondary-color: #2c3e50;
  --success-color: #2ecc71;
  --warning-color: #f1c40f;
  --danger-color: #e74c3c;
  --text-color: #333;
  --light-gray: #f5f6fa;
  --border-color: #dcdde1;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Progress Bar */
.progress-container {
  width: 100%;
  margin: 1rem 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--light-gray);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.progress-fill.indeterminate {
  background: linear-gradient(
    90deg,
    var(--primary-color) 0%,
    #6ba4e7 50%,
    var(--primary-color) 100%
  );
  background-size: 200% 100%;
  animation: indeterminate 1.5s infinite linear;
}

.progress-status {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.progress-percentage {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--primary-color);
  font-weight: 500;
}

/* Results Panel */
.results-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
  padding: 1.5rem;
  margin: 1rem 0;
}

.results-section,
.recommendations-section {
  margin-bottom: 2rem;
}

.results-section h2,
.recommendations-section h2 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.result-item {
  background: var(--light-gray);
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid var(--primary-color);
}

.result-item.severity-high {
  border-left-color: var(--danger-color);
}

.result-item.severity-medium {
  border-left-color: var(--warning-color);
}

.result-item.severity-low {
  border-left-color: var(--success-color);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.result-header h3 {
  margin: 0;
  color: var(--secondary-color);
  font-size: 1.1rem;
}

.severity-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.severity-high .severity-badge {
  background-color: var(--danger-color);
  color: white;
}

.severity-medium .severity-badge {
  background-color: var(--warning-color);
  color: var(--secondary-color);
}

.severity-low .severity-badge {
  background-color: var(--success-color);
  color: white;
}

.result-description {
  color: var(--text-color);
  margin: 0.5rem 0;
  line-height: 1.5;
}

.affected-files {
  margin-top: 0.75rem;
}

.affected-files h4 {
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.affected-files ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.affected-files li {
  color: var(--text-color);
  font-size: 0.9rem;
  padding: 0.25rem 0;
}

.recommendation-item {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recommendation-item:hover {
  background: var(--light-gray);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.recommendation-item p {
  margin: 0;
  color: var(--text-color);
  line-height: 1.5;
}

/* Animations */
@keyframes indeterminate {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
} 