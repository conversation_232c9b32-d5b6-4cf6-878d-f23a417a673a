import { Logger } from '../src/utils/logging/logger.js';
import { SimDataVersionHandler } from '../src/services/analysis/extractors/simdata/simDataVersionHandler.js';
import { SimDataParser } from '../src/services/analysis/extractors/simdata/simDataParser.js';
import { detectSimDataVersion } from '../src/services/analysis/extractors/simdata/simDataTypes.js';
import * as fs from 'fs';

const log = new Logger('SimDataVersionTest');

/**
 * Test script for SimData version detection and parsing
 * This script tests the SimData version handler with a package file
 */
async function main() {
    try {
        // Get package file path from command line arguments or use default
        const packagePath = process.argv[2] || "C:\\Users\\<USER>\\OneDrive\\Documents\\Electronic Arts\\The Sims 4\\Mods\\mc_cmd_center.package";

        log.info(`Testing SimData version detection with: ${packagePath}`);

        // Initialize our parser
        const simDataParser = new SimDataParser();

        // Track versions found
        const versionsFound = new Map<number, number>();

        // Use our own direct buffer extraction
        // This is a simplified approach that directly extracts SimData buffers from the package
        // without relying on S4TK's Package class

        // Read the package file
        const packageBuffer = fs.readFileSync(packagePath);
        log.info(`Package file size: ${packageBuffer.length} bytes`);

        // SimData type ID
        const simDataType = 0x545AC67A;

        // Extract SimData resources manually
        // This is a very simplified approach that just looks for SimData version markers
        // in the package buffer

        // Find potential SimData buffers by looking for version markers
        const simDataBuffers: Buffer[] = [];

        // Scan the package buffer for potential SimData resources
        // This is a very basic approach - in a real implementation, we would parse the package header
        // and resource index properly
        for (let i = 0; i < packageBuffer.length - 100; i += 4) {
            // Check for potential SimData version (16708 or other known versions)
            const potentialVersion = packageBuffer.readUInt16LE(i);

            // If we find a potential version marker, extract a buffer
            if (potentialVersion === 16708 ||
                potentialVersion === 48111 ||
                (potentialVersion >= 1 && potentialVersion <= 5)) {

                // Extract a reasonable chunk of data (4KB should be enough for most SimData)
                const endPos = Math.min(i + 4096, packageBuffer.length);
                const buffer = packageBuffer.subarray(i, endPos);

                // Add to our collection
                simDataBuffers.push(buffer);

                // Skip ahead to avoid duplicates
                i += 100;
            }
        }

        log.info(`Found ${simDataBuffers.length} potential SimData buffers`);

        // Process each potential SimData buffer
        for (let i = 0; i < simDataBuffers.length; i++) {
            const buffer = simDataBuffers[i];

            // Detect version using our shared function
            const version = detectSimDataVersion(buffer);

            if (version !== undefined) {
                // Count this version
                versionsFound.set(version, (versionsFound.get(version) || 0) + 1);

                // Try to parse with our parser
                const parsedData = await simDataParser.parse(buffer);

                // Log result
                if (parsedData) {
                    log.info(`Successfully parsed SimData version ${version} - Schema: ${parsedData.schema?.name || 'Unknown'}`);
                } else {
                    log.warn(`Failed to parse SimData version ${version}`);
                }
            }
        }

        // Log summary of versions found
        log.info('SimData versions found:');
        for (const [version, count] of versionsFound.entries()) {
            log.info(`  Version ${version}: ${count} resources`);
        }

        // Check if our handler supports all versions found
        for (const version of versionsFound.keys()) {
            if (SimDataVersionHandler.canHandle(version)) {
                log.info(`Handler exists for version ${version}`);
            } else {
                log.warn(`No handler registered for version ${version}`);
            }
        }

        log.info('Test completed successfully');
    } catch (error) {
        log.error(`Error: ${error}`);
    }
}

main().catch(error => {
    log.error(`Unhandled error: ${error}`);
    process.exit(1);
});
