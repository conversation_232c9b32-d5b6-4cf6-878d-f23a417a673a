/**
 * Intelligent Conflict Detection Tests - Phase 2 Integration
 *
 * This module provides comprehensive testing for the intelligent conflict detection system
 * integrated with the main testing framework, following existing patterns and structure.
 */

import { DatabaseService } from '../../services/databaseService.js';
import {
  IntelligentConflictDetector,
  ResourceConflictInput,
  ConflictDetectionConfig,
  IntelligentConflictResult
} from '../../services/conflict/intelligent/IntelligentConflictDetector.js';
import { ConflictSeverity, GameplayImpact } from '../../services/conflict/intelligent/GameplayImpactAnalyzer.js';
import { OfficialResourceType } from '../../types/resource/OfficialResourceTypes.js';
import { Logger } from '../../utils/logging/logger.js';
import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import { createInMemoryDatabase } from './realDatabaseHelper.js';
import { formatBytes } from '../../utils/formatting/formatUtils.js';

/**
 * Log memory usage information
 */
function logMemoryUsage(memoryManager: EnhancedMemoryManager, print: (message: string) => void, label: string): void {
    const memStats = memoryManager.getMemoryStats();
    print(`${label}:`);
    print(`  Heap used: ${formatBytes(memStats.heapUsed)} / ${formatBytes(memStats.heapTotal)} (${Math.round(memStats.usedPercentage)}%)`);
    print(`  RSS: ${formatBytes(memStats.rss)}`);
    print(`  Memory pressure: ${(memoryManager.getMemoryPressure() * 100).toFixed(1)}%`);
}

/**
 * Options for intelligent conflict detection testing
 */
export interface IntelligentConflictTestOptions {
    /** Maximum number of test scenarios to run */
    maxTestScenarios?: number;
    /** Whether to test trait conflict detection */
    testTraitConflicts?: boolean;
    /** Whether to test buff conflict detection */
    testBuffConflicts?: boolean;
    /** Whether to test severity classification */
    testSeverityClassification?: boolean;
    /** Whether to test false positive reduction */
    testFalsePositiveReduction?: boolean;
    /** Whether to test configuration handling */
    testConfigurationHandling?: boolean;
    /** Whether to test priority sorting */
    testPrioritySorting?: boolean;
    /** Log level for the test */
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    /** Whether to use in-memory database */
    useInMemoryDatabase?: boolean;
    /** Whether to log detailed results */
    logDetailedResults?: boolean;
}

/**
 * Test Results Interface
 */
export interface IntelligentConflictTestResults {
    success: boolean;
    totalTests: number;
    passedTests: number;
    failedTests: number;
    errors: string[];
    testResults: {
        traitConflicts?: any;
        buffConflicts?: any;
        severityClassification?: any;
        falsePositiveReduction?: any;
        configurationHandling?: any;
        prioritySorting?: any;
    };
    performance: {
        duration: number;
        memoryUsage: any;
    };
}

/**
 * Test intelligent conflict detection system with comprehensive validation
 */
export async function testIntelligentConflictDetection(
    options: IntelligentConflictTestOptions = {}
): Promise<IntelligentConflictTestResults> {
    // Initialize memory management
    const memoryManager = EnhancedMemoryManager.getInstance();
    const resourceTracker = ResourceTracker.getInstance();

    // Track this test operation
    const testId = Date.now();
    memoryManager.trackResource(`intelligentConflictTest_${testId}`, 1024); // Track 1KB for the test operation

    // Create logger
    const logger = new Logger('IntelligentConflictTest');
    const print = (message: string) => process.stdout.write(`${message}\n`);

    // Set default options
    const testOptions: Required<IntelligentConflictTestOptions> = {
        maxTestScenarios: options.maxTestScenarios || 50,
        testTraitConflicts: options.testTraitConflicts !== false,
        testBuffConflicts: options.testBuffConflicts !== false,
        testSeverityClassification: options.testSeverityClassification !== false,
        testFalsePositiveReduction: options.testFalsePositiveReduction !== false,
        testConfigurationHandling: options.testConfigurationHandling !== false,
        testPrioritySorting: options.testPrioritySorting !== false,
        logLevel: options.logLevel || 'info',
        useInMemoryDatabase: options.useInMemoryDatabase !== false,
        logDetailedResults: options.logDetailedResults !== false
    };

    const startTime = Date.now();
    const results: IntelligentConflictTestResults = {
        success: false,
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        errors: [],
        testResults: {},
        performance: {
            duration: 0,
            memoryUsage: {}
        }
    };

    print('\n🧠 INTELLIGENT CONFLICT DETECTION TEST SUITE');
    print('=' .repeat(80));

    // Log initial memory state
    logMemoryUsage(memoryManager, print, 'Initial memory state');

    // Create database service
    let databaseService: DatabaseService;
    try {
        if (testOptions.useInMemoryDatabase) {
            print('Creating in-memory database...');
            databaseService = await createInMemoryDatabase();
        } else {
            print('Using persistent database...');
            databaseService = new DatabaseService(':memory:', logger);
            await databaseService.initialize();
        }

        // Track database service for cleanup
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `intelligentConflictTest_${testId}`,
            async () => {
                await databaseService.close();
            },
            {
                id: `database_${testId}`,
                state: ResourceState.ACTIVE,
                metadata: { inMemory: testOptions.useInMemoryDatabase }
            }
        );

        logMemoryUsage(memoryManager, print, 'Memory state after database creation');
    } catch (error: any) {
        print(`Error creating database: ${error.message}`);
        results.errors.push(`Database creation failed: ${error.message}`);
        memoryManager.untrackResource(`intelligentConflictTest_${testId}`, 1024);
        return results;
    }

    try {
        // Create intelligent conflict detector
        const detector = new IntelligentConflictDetector();
        print('✅ IntelligentConflictDetector created successfully');

        // Test 1: Trait conflict detection
        if (testOptions.testTraitConflicts) {
            print('\n🎭 Test 1: Trait Conflict Detection');
            results.totalTests++;

            try {
                const traitResult = await testTraitConflictDetection(detector, print);
                if (traitResult.success) {
                    results.passedTests++;
                    print('   ✅ PASS: Trait conflict detection working correctly');
                } else {
                    results.failedTests++;
                    results.errors.push(`Trait conflict test failed: ${traitResult.error}`);
                    print(`   ❌ FAIL: ${traitResult.error}`);
                }
                results.testResults.traitConflicts = traitResult;
            } catch (error: any) {
                results.failedTests++;
                const errorMsg = `Trait conflict test error: ${error.message}`;
                results.errors.push(errorMsg);
                print(`   ❌ ERROR: ${errorMsg}`);
            }
        }

        // Test 2: Buff conflict detection
        if (testOptions.testBuffConflicts) {
            print('\n💫 Test 2: Buff Conflict Detection');
            results.totalTests++;

            try {
                const buffResult = await testBuffConflictDetection(detector, print);
                if (buffResult.success) {
                    results.passedTests++;
                    print('   ✅ PASS: Buff conflict detection working correctly');
                } else {
                    results.failedTests++;
                    results.errors.push(`Buff conflict test failed: ${buffResult.error}`);
                    print(`   ❌ FAIL: ${buffResult.error}`);
                }
                results.testResults.buffConflicts = buffResult;
            } catch (error: any) {
                results.failedTests++;
                const errorMsg = `Buff conflict test error: ${error.message}`;
                results.errors.push(errorMsg);
                print(`   ❌ ERROR: ${errorMsg}`);
            }
        }

        // Test 3: Severity classification
        if (testOptions.testSeverityClassification) {
            print('\n⚖️ Test 3: Severity Classification');
            results.totalTests++;

            try {
                const severityResult = await testSeverityClassification(detector, print);
                if (severityResult.success) {
                    results.passedTests++;
                    print('   ✅ PASS: Severity classification working correctly');
                } else {
                    results.failedTests++;
                    results.errors.push(`Severity classification test failed: ${severityResult.error}`);
                    print(`   ❌ FAIL: ${severityResult.error}`);
                }
                results.testResults.severityClassification = severityResult;
            } catch (error: any) {
                results.failedTests++;
                const errorMsg = `Severity classification test error: ${error.message}`;
                results.errors.push(errorMsg);
                print(`   ❌ ERROR: ${errorMsg}`);
            }
        }

        // Test 4: False positive reduction
        if (testOptions.testFalsePositiveReduction) {
            print('\n🎯 Test 4: False Positive Reduction');
            results.totalTests++;

            try {
                const falsePositiveResult = await testFalsePositiveReduction(detector, print);
                if (falsePositiveResult.success) {
                    results.passedTests++;
                    print('   ✅ PASS: False positive reduction working correctly');
                } else {
                    results.failedTests++;
                    results.errors.push(`False positive reduction test failed: ${falsePositiveResult.error}`);
                    print(`   ❌ FAIL: ${falsePositiveResult.error}`);
                }
                results.testResults.falsePositiveReduction = falsePositiveResult;
            } catch (error: any) {
                results.failedTests++;
                const errorMsg = `False positive reduction test error: ${error.message}`;
                results.errors.push(errorMsg);
                print(`   ❌ ERROR: ${errorMsg}`);
            }
        }

        // Test 5: Configuration handling
        if (testOptions.testConfigurationHandling) {
            print('\n⚙️ Test 5: Configuration Handling');
            results.totalTests++;

            try {
                const configResult = await testConfigurationHandling(detector, print);
                if (configResult.success) {
                    results.passedTests++;
                    print('   ✅ PASS: Configuration handling working correctly');
                } else {
                    results.failedTests++;
                    results.errors.push(`Configuration handling test failed: ${configResult.error}`);
                    print(`   ❌ FAIL: ${configResult.error}`);
                }
                results.testResults.configurationHandling = configResult;
            } catch (error: any) {
                results.failedTests++;
                const errorMsg = `Configuration handling test error: ${error.message}`;
                results.errors.push(errorMsg);
                print(`   ❌ ERROR: ${errorMsg}`);
            }
        }

        // Test 6: Priority sorting
        if (testOptions.testPrioritySorting) {
            print('\n📊 Test 6: Priority Sorting');
            results.totalTests++;

            try {
                const priorityResult = await testPrioritySorting(detector, print);
                if (priorityResult.success) {
                    results.passedTests++;
                    print('   ✅ PASS: Priority sorting working correctly');
                } else {
                    results.failedTests++;
                    results.errors.push(`Priority sorting test failed: ${priorityResult.error}`);
                    print(`   ❌ FAIL: ${priorityResult.error}`);
                }
                results.testResults.prioritySorting = priorityResult;
            } catch (error: any) {
                results.failedTests++;
                const errorMsg = `Priority sorting test error: ${error.message}`;
                results.errors.push(errorMsg);
                print(`   ❌ ERROR: ${errorMsg}`);
            }
        }

        // Calculate final results
        const endTime = Date.now();
        results.performance.duration = endTime - startTime;
        results.performance.memoryUsage = memoryManager.getMemoryStats();
        results.success = results.failedTests === 0;

        // Print final results
        printTestResults(results, print);

        return results;

    } catch (error: any) {
        const errorMsg = `Intelligent conflict detection test failed: ${error.message}`;
        print(`❌ ERROR: ${errorMsg}`);
        results.errors.push(errorMsg);
        return results;
    } finally {
        // Cleanup
        print('Cleaning up resources...');

        // Use resource tracker to clean up database (avoids double-close)
        await resourceTracker.releaseResourcesByOwner(`intelligentConflictTest_${testId}`);
        memoryManager.untrackResource(`intelligentConflictTest_${testId}`, 1024);

        // Final memory state
        logMemoryUsage(memoryManager, print, 'Final memory state');
    }
}

/**
 * Test trait conflict detection
 */
async function testTraitConflictDetection(detector: IntelligentConflictDetector, print: (message: string) => void): Promise<any> {
    const traitConflicts: ResourceConflictInput[] = [{
        resourceType: OfficialResourceType.TRAIT,
        resourceId: 'trait_Mean',
        packageName: 'MeanTraitMod.package',
        conflictingResources: [{
            resourceType: OfficialResourceType.TRAIT,
            resourceId: 'trait_Mean',
            packageName: 'AlternativeMeanTrait.package',
            metadata: {
                name: 'Good Trait',
                conflictingTraits: ['trait_Mean'],
                personalityType: 'positive'
            }
        }],
        metadata: {
            name: 'Mean Trait',
            conflictingTraits: ['trait_Good'],
            personalityType: 'negative'
        }
    }];

    const traitResults = await detector.detectConflicts(traitConflicts);

    if (traitResults.length > 0) {
        const result = traitResults[0];
        print(`   ✅ Detected trait conflict: ${result.severity} severity`);
        print(`   ✅ Impact: ${result.impact}`);
        print(`   ✅ Confidence: ${(result.confidence * 100).toFixed(1)}%`);
        print(`   ✅ Detector used: ${result.detectorUsed}`);

        if (result.severity === ConflictSeverity.HIGH || result.severity === ConflictSeverity.CRITICAL || result.severity === ConflictSeverity.MEDIUM) {
            return { success: true, result };
        } else {
            return { success: false, error: `Expected meaningful severity for trait conflict, got ${result.severity}` };
        }
    } else {
        return { success: false, error: 'No trait conflicts detected when conflicts were expected' };
    }
}

/**
 * Test buff conflict detection
 */
async function testBuffConflictDetection(detector: IntelligentConflictDetector, print: (message: string) => void): Promise<any> {
    const buffConflicts: ResourceConflictInput[] = [{
        resourceType: OfficialResourceType.BUFF,
        resourceId: 'buff_Happy',
        packageName: 'HappyBuffMod.package',
        conflictingResources: [{
            resourceType: OfficialResourceType.BUFF,
            resourceId: 'buff_Happy',
            packageName: 'AlternativeHappyBuff.package',
            metadata: {
                name: 'Sad Buff',
                moodType: 'Sad',
                moodWeight: 3,
                duration: 7200
            }
        }],
        metadata: {
            name: 'Happy Buff',
            moodType: 'Happy',
            moodWeight: 3,
            duration: 3600
        }
    }];

    const buffResults = await detector.detectConflicts(buffConflicts);

    if (buffResults.length > 0) {
        const result = buffResults[0];
        print(`   ✅ Detected buff conflict: ${result.severity} severity`);
        print(`   ✅ Impact: ${result.impact}`);
        print(`   ✅ Confidence: ${(result.confidence * 100).toFixed(1)}%`);
        print(`   ✅ Detector used: ${result.detectorUsed}`);

        if (result.severity !== ConflictSeverity.HARMLESS) {
            return { success: true, result };
        } else {
            return { success: false, error: `Expected non-harmless severity for buff conflict, got ${result.severity}` };
        }
    } else {
        return { success: false, error: 'No buff conflicts detected when conflicts were expected' };
    }
}

/**
 * Test severity classification
 */
async function testSeverityClassification(detector: IntelligentConflictDetector, print: (message: string) => void): Promise<any> {
    const testCases = [
        {
            name: 'High Impact Conflict',
            input: {
                resourceType: OfficialResourceType.TRAIT,
                resourceId: 'trait_Critical',
                packageName: 'CriticalMod.package',
                conflictingResources: [
                    { resourceType: OfficialResourceType.TRAIT, resourceId: 'trait_Critical', packageName: 'Mod1.package' },
                    { resourceType: OfficialResourceType.TRAIT, resourceId: 'trait_Critical', packageName: 'Mod2.package' }
                ],
                metadata: { name: 'Critical Trait' }
            },
            expectedMinSeverity: ConflictSeverity.MEDIUM
        },
        {
            name: 'Low Impact Conflict',
            input: {
                resourceType: OfficialResourceType.PNG,
                resourceId: 'image_icon',
                packageName: 'ImageMod.package',
                conflictingResources: [
                    { resourceType: OfficialResourceType.PNG, resourceId: 'image_icon', packageName: 'AltImageMod.package' }
                ],
                metadata: { name: 'Icon Image' }
            },
            expectedMaxSeverity: ConflictSeverity.MEDIUM
        }
    ];

    let passed = 0;
    for (const testCase of testCases) {
        const testResults = await detector.detectConflicts([testCase.input]);

        if (testResults.length > 0) {
            const result = testResults[0];
            const severityOrder = [
                ConflictSeverity.HARMLESS,
                ConflictSeverity.LOW,
                ConflictSeverity.MEDIUM,
                ConflictSeverity.HIGH,
                ConflictSeverity.CRITICAL
            ];

            const resultIndex = severityOrder.indexOf(result.severity);

            if (testCase.expectedMinSeverity) {
                const minIndex = severityOrder.indexOf(testCase.expectedMinSeverity);
                if (resultIndex >= minIndex) {
                    print(`   ✅ ${testCase.name}: ${result.severity} (meets minimum ${testCase.expectedMinSeverity})`);
                    passed++;
                } else {
                    print(`   ❌ ${testCase.name}: ${result.severity} (below minimum ${testCase.expectedMinSeverity})`);
                }
            }

            if (testCase.expectedMaxSeverity) {
                const maxIndex = severityOrder.indexOf(testCase.expectedMaxSeverity);
                if (resultIndex <= maxIndex) {
                    print(`   ✅ ${testCase.name}: ${result.severity} (within maximum ${testCase.expectedMaxSeverity})`);
                    passed++;
                } else {
                    print(`   ❌ ${testCase.name}: ${result.severity} (exceeds maximum ${testCase.expectedMaxSeverity})`);
                }
            }
        }
    }

    if (passed >= testCases.length) {
        return { success: true, passed, total: testCases.length };
    } else {
        return { success: false, error: `Only ${passed}/${testCases.length} severity tests passed` };
    }
}

/**
 * Test false positive reduction
 */
async function testFalsePositiveReduction(detector: IntelligentConflictDetector, print: (message: string) => void): Promise<any> {
    // Test language variants (should be harmless)
    const languageVariants: ResourceConflictInput[] = [{
        resourceType: OfficialResourceType.TUNING,
        resourceId: 'string_table_eng_us',
        packageName: 'EnglishStrings.package',
        conflictingResources: [{
            resourceType: OfficialResourceType.TUNING,
            resourceId: 'string_table_fra_fr',
            packageName: 'FrenchStrings.package'
        }],
        metadata: { name: 'English Strings' }
    }];

    const languageResults = await detector.detectConflicts(languageVariants);

    let harmlessDetected = 0;
    if (languageResults.length > 0) {
        harmlessDetected = languageResults.filter(r => r.severity === ConflictSeverity.HARMLESS).length;
    }

    print(`   ✅ Language variants: ${harmlessDetected} harmless conflicts detected`);

    if (harmlessDetected > 0 || languageResults.length === 0) {
        return { success: true, harmlessDetected, totalResults: languageResults.length };
    } else {
        return { success: false, error: 'Language variants incorrectly flagged as serious conflicts' };
    }
}

/**
 * Test configuration handling
 */
async function testConfigurationHandling(detector: IntelligentConflictDetector, print: (message: string) => void): Promise<any> {
    const testConflicts: ResourceConflictInput[] = [{
        resourceType: OfficialResourceType.TRAIT,
        resourceId: 'test_trait',
        packageName: 'TestMod.package',
        conflictingResources: [{
            resourceType: OfficialResourceType.TRAIT,
            resourceId: 'test_trait',
            packageName: 'AltTestMod.package'
        }],
        metadata: { name: 'Test Trait' }
    }];

    // Test with trait detection disabled
    const disabledConfig: ConflictDetectionConfig = {
        enableTraitDetection: false,
        enableBuffDetection: true,
        enableInteractionDetection: true,
        enableObjectDetection: true,
        enableServiceDetection: true,
        minimumSeverity: ConflictSeverity.LOW,
        includeHarmlessConflicts: false,
        confidenceThreshold: 0.6
    };

    const disabledResults = await detector.detectConflicts(testConflicts, disabledConfig);

    if (disabledResults.length === 0) {
        print(`   ✅ Trait detection disabled: No results (correct)`);
        return { success: true, disabledResults: disabledResults.length };
    } else {
        return { success: false, error: 'Trait conflicts detected when trait detection was disabled' };
    }
}

/**
 * Test priority sorting
 */
async function testPrioritySorting(detector: IntelligentConflictDetector, print: (message: string) => void): Promise<any> {
    // Create conflicts with different severities
    const mixedConflicts: ResourceConflictInput[] = [
        {
            resourceType: OfficialResourceType.PNG,
            resourceId: 'low_priority',
            packageName: 'LowPriorityMod.package',
            conflictingResources: [{ resourceType: OfficialResourceType.PNG, resourceId: 'low_priority', packageName: 'Alt.package' }],
            metadata: { name: 'Low Priority' }
        },
        {
            resourceType: OfficialResourceType.TRAIT,
            resourceId: 'high_priority',
            packageName: 'HighPriorityMod.package',
            conflictingResources: [
                { resourceType: OfficialResourceType.TRAIT, resourceId: 'high_priority', packageName: 'Alt1.package' },
                { resourceType: OfficialResourceType.TRAIT, resourceId: 'high_priority', packageName: 'Alt2.package' }
            ],
            metadata: { name: 'High Priority Trait' }
        }
    ];

    const sortedResults = await detector.detectConflicts(mixedConflicts);

    if (sortedResults.length >= 2) {
        const firstResult = sortedResults[0];
        const lastResult = sortedResults[sortedResults.length - 1];

        if (firstResult.priorityLevel >= lastResult.priorityLevel) {
            print(`   ✅ Results sorted by priority: ${firstResult.priorityLevel} → ${lastResult.priorityLevel}`);
            return { success: true, sortedCorrectly: true, resultsCount: sortedResults.length };
        } else {
            return { success: false, error: 'Results not properly sorted by priority' };
        }
    } else {
        print(`   ⚠️ Not enough results to test sorting (${sortedResults.length})`);
        return { success: true, sortedCorrectly: true, resultsCount: sortedResults.length }; // Don't fail if we can't test sorting
    }
}

/**
 * Print test results summary
 */
function printTestResults(results: IntelligentConflictTestResults, print: (message: string) => void): void {
    print('\n' + '=' .repeat(80));
    print('🧠 INTELLIGENT CONFLICT DETECTION TEST RESULTS');
    print('=' .repeat(80));

    print(`Total Tests: ${results.totalTests}`);
    print(`Passed: ${results.passedTests} ✅`);
    print(`Failed: ${results.failedTests} ❌`);

    const successRate = results.totalTests > 0 ? (results.passedTests / results.totalTests * 100).toFixed(1) : '0.0';
    print(`Success Rate: ${successRate}%`);
    print(`Duration: ${results.performance.duration}ms`);

    if (results.errors.length > 0) {
        print('\n❌ ERRORS:');
        results.errors.forEach((error, index) => {
            print(`   ${index + 1}. ${error}`);
        });
    }

    if (results.failedTests === 0) {
        print('\n🎉 ALL TESTS PASSED! Intelligent Conflict Detection Phase 2 Complete!');
        print('✅ Trait conflict detection working correctly');
        print('✅ Buff conflict detection operational');
        print('✅ Severity classification accurate');
        print('✅ False positive reduction effective');
        print('✅ Configuration system functional');
        print('✅ Priority sorting working correctly');
        print('\n🚀 Ready for integration with real-world testing!');
    } else {
        print('\n⚠️ Some tests failed. Please review and fix issues before proceeding.');
    }
}
