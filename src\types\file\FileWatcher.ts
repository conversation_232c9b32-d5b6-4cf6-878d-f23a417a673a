﻿/**
 * Types for the File Watcher Service
 */

import { ModCategory } from '../resource/Package.js';
import { ConflictSeverity } from '../conflict/ConflictTypes.js';
import { Stats } from 'fs';
import { Logger } from '../../utils/logging/logger.js';
import { ConflictDetectionOptions, ConflictResult } from '../conflict/ConflictTypes.js';
import { PackageMetadata } from '../resource/Package.js';

/**
 * Represents a change event for files in the watched directory
 */
export interface FileChangeEvent {
  /** The type of file system event */
  eventType: 'add' | 'change' | 'unlink' | 'addDir' | 'unlinkDir';
  /** The path that was changed */
  path: string;
  /** The stat object (present for add and change events) */
  stats?: { size: number; mtime: Date };
  /** Is the path a directory */
  isDirectory?: boolean;
}

/**
 * Options for configuring the file watcher
 */
export interface FileWatcherOptions {
  directories: string[];
  include?: string[];
  exclude?: string[];
  events?: FileWatcherEvents;
}

/**
 * Callback function for file change events
 */
export type FileChangeCallback = (event: FileChangeEvent) => void | Promise<void>;

/**
 * Interface for the File Watcher Service events
 */
export interface FileWatcherEvents {
  [FileWatcherEventType.READY]?: () => void;
  [FileWatcherEventType.ERROR]?: (error: Error) => void;
  [FileWatcherEventType.FILE_ADDED]?: (path: string, metadata: PackageMetadata) => void;
  [FileWatcherEventType.FILE_CHANGED]?: (path: string, metadata: PackageMetadata) => void;
  [FileWatcherEventType.FILE_DELETED]?: (path: string) => void;
  [FileWatcherEventType.CONFLICT_DETECTED]?: (conflicts: ConflictResult[]) => void;
}

/**
 * Interface for the file watcher's statistics
 */
export interface FileWatcherStats {
  totalFiles: number;
  totalDirectories: number;
  totalConflicts: number;
  addedFiles: number;
  changedFiles: number;
  deletedFiles: number;
  lastUpdate: Date;
}

/**
 * Enum for file watcher event types
 */
export enum FileWatcherEventType {
  READY = 'ready',
  ERROR = 'error',
  FILE_ADDED = 'file_added',
  FILE_CHANGED = 'file_changed',
  FILE_DELETED = 'file_deleted',
  CONFLICT_DETECTED = 'conflict_detected',
}

/**
 * Interface for file watcher events
 */
export interface FileWatcherEvent {
  type: FileWatcherEventType;
  path: string;
  stats?: {
    size: number;
    mtime: Date;
    ctime: Date;
  };
}

/**
 * Configuration for the file watcher
 */
export interface FileWatcherConfig {
  options: FileWatcherOptions;
  stats: FileWatcherStats;
}

