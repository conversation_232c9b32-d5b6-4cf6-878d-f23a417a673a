/**
 * Cross-Resource Analyzer for SimData
 * 
 * This module analyzes relationships between SimData resources and other resource types,
 * particularly Tuning XML resources. It helps identify connections, dependencies, and
 * potential conflicts between related resources.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { ParsedSimData } from './simDataParser.js';
import { ResourceKey } from '../../../../types/resource/interfaces.js';
import { schemaCatalog } from './schema/schemaCatalog.js';

const log = new Logger('CrossResourceAnalyzer');

/**
 * Interface for a resource reference
 */
export interface ResourceReference {
    resourceType: number;
    resourceGroup: bigint;
    resourceInstance: bigint;
    referenceType: string;
    referencingColumn?: string;
    confidence: number; // 0-100
}

/**
 * Interface for a tuning reference
 */
export interface TuningReference {
    tuningId: string;
    tuningType?: string;
    tuningName?: string;
    referenceType: string;
    referencingColumn?: string;
    confidence: number; // 0-100
}

/**
 * Interface for cross-resource analysis result
 */
export interface CrossResourceAnalysisResult {
    resourceId: number;
    resourceKey: ResourceKey;
    resourceReferences: ResourceReference[];
    tuningReferences: TuningReference[];
    relatedSimData: {
        id: number;
        key: ResourceKey;
        relationship: string;
        confidence: number;
    }[];
    relatedTuning: {
        id: number;
        key: ResourceKey;
        relationship: string;
        confidence: number;
    }[];
    potentialConflicts: {
        resourceId: number;
        resourceKey: ResourceKey;
        conflictType: string;
        severity: 'low' | 'medium' | 'high';
        description: string;
    }[];
}

/**
 * Cross-Resource Analyzer
 * Analyzes relationships between SimData resources and other resource types
 */
export class CrossResourceAnalyzer {
    private logger: Logger;
    private databaseService: DatabaseService;

    constructor(databaseService: DatabaseService, logger?: Logger) {
        this.logger = logger || log;
        this.databaseService = databaseService;
    }

    /**
     * Analyze cross-resource relationships for a SimData resource
     * @param simData The parsed SimData
     * @param resourceId The resource ID in the database
     * @param resourceKey The resource key
     * @returns Cross-resource analysis result
     */
    public async analyzeResource(
        simData: ParsedSimData,
        resourceId: number,
        resourceKey: ResourceKey
    ): Promise<CrossResourceAnalysisResult> {
        const result: CrossResourceAnalysisResult = {
            resourceId,
            resourceKey,
            resourceReferences: [],
            tuningReferences: [],
            relatedSimData: [],
            relatedTuning: [],
            potentialConflicts: []
        };

        try {
            // Extract resource references from SimData
            result.resourceReferences = this.extractResourceReferences(simData);

            // Extract tuning references from SimData
            result.tuningReferences = this.extractTuningReferences(simData);

            // Find related SimData resources
            result.relatedSimData = await this.findRelatedSimData(simData, resourceId, resourceKey);

            // Find related Tuning resources
            result.relatedTuning = await this.findRelatedTuning(simData, resourceId, resourceKey);

            // Identify potential conflicts
            result.potentialConflicts = await this.identifyPotentialConflicts(simData, resourceId, resourceKey);

            return result;
        } catch (error) {
            this.logger.error(`Error analyzing cross-resource relationships: ${error}`);
            return result;
        }
    }

    /**
     * Extract resource references from SimData
     * @param simData The parsed SimData
     * @returns Array of resource references
     */
    private extractResourceReferences(simData: ParsedSimData): ResourceReference[] {
        const references: ResourceReference[] = [];

        // Skip if no schema or instances
        if (!simData.schema || !simData.instances || simData.instances.length === 0) {
            return references;
        }

        // Get schema entry from catalog if available
        const schemaEntry = schemaCatalog.matchSchema(simData.schema.name);

        // Check each instance for resource references
        for (const instance of simData.instances) {
            if (!instance.values) continue;

            // Check each column for resource references
            for (const column of simData.schema.columns) {
                const columnName = column.name;
                const value = instance.values[columnName];

                // Skip if no value
                if (value === null || value === undefined) continue;

                // Check if this is a ResourceKey type
                if (column.type === 20) { // ResourceKey
                    if (typeof value === 'object' && value.type !== undefined && value.group !== undefined && value.instance !== undefined) {
                        references.push({
                            resourceType: value.type,
                            resourceGroup: BigInt(value.group),
                            resourceInstance: BigInt(value.instance),
                            referenceType: 'direct',
                            referencingColumn: columnName,
                            confidence: 100
                        });
                    }
                }

                // Check for string values that might contain resource references
                if (typeof value === 'string') {
                    // Check for TGI patterns in strings
                    const tgiPattern = /(\d+):(\d+):(\d+)/g;
                    let match;
                    while ((match = tgiPattern.exec(value)) !== null) {
                        const [_, type, group, instance] = match;
                        references.push({
                            resourceType: parseInt(type),
                            resourceGroup: BigInt(group),
                            resourceInstance: BigInt(instance),
                            referenceType: 'string',
                            referencingColumn: columnName,
                            confidence: 80
                        });
                    }

                    // Check for hex TGI patterns
                    const hexTgiPattern = /0x([0-9a-fA-F]+):0x([0-9a-fA-F]+):0x([0-9a-fA-F]+)/g;
                    while ((match = hexTgiPattern.exec(value)) !== null) {
                        const [_, type, group, instance] = match;
                        references.push({
                            resourceType: parseInt(type, 16),
                            resourceGroup: BigInt(parseInt(group, 16)),
                            resourceInstance: BigInt(parseInt(instance, 16)),
                            referenceType: 'string',
                            referencingColumn: columnName,
                            confidence: 80
                        });
                    }
                }
            }
        }

        return references;
    }

    /**
     * Extract tuning references from SimData
     * @param simData The parsed SimData
     * @returns Array of tuning references
     */
    private extractTuningReferences(simData: ParsedSimData): TuningReference[] {
        const references: TuningReference[] = [];

        // Skip if no schema or instances
        if (!simData.schema || !simData.instances || simData.instances.length === 0) {
            return references;
        }

        // Get schema entry from catalog if available
        const schemaEntry = schemaCatalog.matchSchema(simData.schema.name);

        // Check each instance for tuning references
        for (const instance of simData.instances) {
            if (!instance.values) continue;

            // Check each column for tuning references
            for (const column of simData.schema.columns) {
                const columnName = column.name;
                const value = instance.values[columnName];

                // Skip if no value
                if (value === null || value === undefined) continue;

                // Check for numeric values that might be tuning IDs
                if (typeof value === 'number' || typeof value === 'bigint') {
                    // Convert to string for consistency
                    const tuningId = value.toString();
                    
                    // Only consider values that look like tuning IDs (large numbers)
                    if (tuningId.length >= 8) {
                        references.push({
                            tuningId,
                            referenceType: 'numeric',
                            referencingColumn: columnName,
                            confidence: 70
                        });
                    }
                }

                // Check for string values that might contain tuning IDs
                if (typeof value === 'string') {
                    // Check for decimal tuning IDs
                    const decimalPattern = /(\d{8,})/g;
                    let match;
                    while ((match = decimalPattern.exec(value)) !== null) {
                        references.push({
                            tuningId: match[1],
                            referenceType: 'string',
                            referencingColumn: columnName,
                            confidence: 60
                        });
                    }

                    // Check for hex tuning IDs
                    const hexPattern = /0x([0-9a-fA-F]{8,})/g;
                    while ((match = hexPattern.exec(value)) !== null) {
                        // Convert hex to decimal
                        const decimalId = BigInt(`0x${match[1]}`).toString();
                        references.push({
                            tuningId: decimalId,
                            referenceType: 'hex',
                            referencingColumn: columnName,
                            confidence: 70
                        });
                    }
                }
            }
        }

        return references;
    }

    /**
     * Find related SimData resources
     * @param simData The parsed SimData
     * @param resourceId The resource ID in the database
     * @param resourceKey The resource key
     * @returns Array of related SimData resources
     */
    private async findRelatedSimData(
        simData: ParsedSimData,
        resourceId: number,
        resourceKey: ResourceKey
    ): Promise<{
        id: number;
        key: ResourceKey;
        relationship: string;
        confidence: number;
    }[]> {
        const relatedSimData: {
            id: number;
            key: ResourceKey;
            relationship: string;
            confidence: number;
        }[] = [];

        try {
            // Skip if no schema
            if (!simData.schema) {
                return relatedSimData;
            }

            // Get schema entry from catalog if available
            const schemaEntry = schemaCatalog.matchSchema(simData.schema.name);

            // Find SimData resources with the same schema
            const sameSchemaResources = await this.databaseService.executeQuery(
                `SELECT r.id, r.type, r."group", r.instance 
                 FROM Resources r
                 JOIN Metadata m ON r.id = m.resourceId
                 WHERE m.key = 'simDataSchemaName' AND m.value = ? AND r.id != ?`,
                [simData.schema.name, resourceId]
            );

            // Add same schema resources
            for (const resource of sameSchemaResources) {
                relatedSimData.push({
                    id: resource.id,
                    key: {
                        type: resource.type,
                        group: BigInt(resource.group),
                        instance: BigInt(resource.instance)
                    },
                    relationship: 'same_schema',
                    confidence: 90
                });
            }

            // Find parent/child schema relationships if available
            if (schemaEntry && schemaEntry.parentSchema) {
                // Find resources with parent schema
                const parentSchemaResources = await this.databaseService.executeQuery(
                    `SELECT r.id, r.type, r."group", r.instance 
                     FROM Resources r
                     JOIN Metadata m ON r.id = m.resourceId
                     WHERE m.key = 'simDataSchemaName' AND m.value = ?`,
                    [schemaEntry.parentSchema]
                );

                // Add parent schema resources
                for (const resource of parentSchemaResources) {
                    relatedSimData.push({
                        id: resource.id,
                        key: {
                            type: resource.type,
                            group: BigInt(resource.group),
                            instance: BigInt(resource.instance)
                        },
                        relationship: 'parent_schema',
                        confidence: 80
                    });
                }
            }

            // Find resources that reference this resource
            const referencingResources = await this.databaseService.executeQuery(
                `SELECT r.id, r.type, r."group", r.instance 
                 FROM Resources r
                 JOIN Dependencies d ON r.id = d.sourceResourceId
                 WHERE d.targetType = ? AND d.targetGroup = ? AND d.targetInstance = ?`,
                [resourceKey.type, resourceKey.group.toString(), resourceKey.instance.toString()]
            );

            // Add referencing resources
            for (const resource of referencingResources) {
                // Only add SimData resources
                if (resource.type === 0x545AC67A || resource.type === 0x6017E896) {
                    relatedSimData.push({
                        id: resource.id,
                        key: {
                            type: resource.type,
                            group: BigInt(resource.group),
                            instance: BigInt(resource.instance)
                        },
                        relationship: 'references_this',
                        confidence: 90
                    });
                }
            }

            return relatedSimData;
        } catch (error) {
            this.logger.error(`Error finding related SimData resources: ${error}`);
            return relatedSimData;
        }
    }

    /**
     * Find related Tuning resources
     * @param simData The parsed SimData
     * @param resourceId The resource ID in the database
     * @param resourceKey The resource key
     * @returns Array of related Tuning resources
     */
    private async findRelatedTuning(
        simData: ParsedSimData,
        resourceId: number,
        resourceKey: ResourceKey
    ): Promise<{
        id: number;
        key: ResourceKey;
        relationship: string;
        confidence: number;
    }[]> {
        const relatedTuning: {
            id: number;
            key: ResourceKey;
            relationship: string;
            confidence: number;
        }[] = [];

        try {
            // Skip if no schema
            if (!simData.schema) {
                return relatedTuning;
            }

            // Find Tuning resources that reference this resource
            const referencingTuning = await this.databaseService.executeQuery(
                `SELECT r.id, r.type, r."group", r.instance 
                 FROM Resources r
                 JOIN Dependencies d ON r.id = d.sourceResourceId
                 WHERE d.targetType = ? AND d.targetGroup = ? AND d.targetInstance = ? AND r.resourceType = 'TUNING_XML'`,
                [resourceKey.type, resourceKey.group.toString(), resourceKey.instance.toString()]
            );

            // Add referencing Tuning resources
            for (const resource of referencingTuning) {
                relatedTuning.push({
                    id: resource.id,
                    key: {
                        type: resource.type,
                        group: BigInt(resource.group),
                        instance: BigInt(resource.instance)
                    },
                    relationship: 'references_this',
                    confidence: 90
                });
            }

            // Find Tuning resources referenced by this resource
            for (const reference of this.extractTuningReferences(simData)) {
                // Convert tuning ID to instance
                const tuningInstance = BigInt(reference.tuningId);

                // Find Tuning resource with this instance
                const tuningResources = await this.databaseService.executeQuery(
                    `SELECT r.id, r.type, r."group", r.instance 
                     FROM Resources r
                     WHERE r.instance = ? AND r.resourceType = 'TUNING_XML'`,
                    [tuningInstance.toString()]
                );

                // Add referenced Tuning resources
                for (const resource of tuningResources) {
                    relatedTuning.push({
                        id: resource.id,
                        key: {
                            type: resource.type,
                            group: BigInt(resource.group),
                            instance: BigInt(resource.instance)
                        },
                        relationship: 'referenced_by_this',
                        confidence: reference.confidence
                    });
                }
            }

            // Find Tuning resources with matching instance ID
            const matchingInstanceTuning = await this.databaseService.executeQuery(
                `SELECT r.id, r.type, r."group", r.instance 
                 FROM Resources r
                 WHERE r.instance = ? AND r.resourceType = 'TUNING_XML'`,
                [resourceKey.instance.toString()]
            );

            // Add matching instance Tuning resources
            for (const resource of matchingInstanceTuning) {
                relatedTuning.push({
                    id: resource.id,
                    key: {
                        type: resource.type,
                        group: BigInt(resource.group),
                        instance: BigInt(resource.instance)
                    },
                    relationship: 'matching_instance',
                    confidence: 80
                });
            }

            return relatedTuning;
        } catch (error) {
            this.logger.error(`Error finding related Tuning resources: ${error}`);
            return relatedTuning;
        }
    }

    /**
     * Identify potential conflicts with other resources
     * @param simData The parsed SimData
     * @param resourceId The resource ID in the database
     * @param resourceKey The resource key
     * @returns Array of potential conflicts
     */
    private async identifyPotentialConflicts(
        simData: ParsedSimData,
        resourceId: number,
        resourceKey: ResourceKey
    ): Promise<{
        resourceId: number;
        resourceKey: ResourceKey;
        conflictType: string;
        severity: 'low' | 'medium' | 'high';
        description: string;
    }[]> {
        const conflicts: {
            resourceId: number;
            resourceKey: ResourceKey;
            conflictType: string;
            severity: 'low' | 'medium' | 'high';
            description: string;
        }[] = [];

        try {
            // Skip if no schema
            if (!simData.schema) {
                return conflicts;
            }

            // Find resources with the same instance ID but different type
            const sameInstanceResources = await this.databaseService.executeQuery(
                `SELECT r.id, r.type, r."group", r.instance, r.resourceType
                 FROM Resources r
                 WHERE r.instance = ? AND r.type != ? AND r.id != ?`,
                [resourceKey.instance.toString(), resourceKey.type, resourceId]
            );

            // Add same instance conflicts
            for (const resource of sameInstanceResources) {
                conflicts.push({
                    resourceId: resource.id,
                    resourceKey: {
                        type: resource.type,
                        group: BigInt(resource.group),
                        instance: BigInt(resource.instance)
                    },
                    conflictType: 'same_instance_different_type',
                    severity: 'medium',
                    description: `Resource shares instance ID with ${resource.resourceType} resource`
                });
            }

            // Find resources with the same schema and similar instance values
            const sameSchemaResources = await this.databaseService.executeQuery(
                `SELECT r.id, r.type, r."group", r.instance 
                 FROM Resources r
                 JOIN Metadata m ON r.id = m.resourceId
                 WHERE m.key = 'simDataSchemaName' AND m.value = ? AND r.id != ?`,
                [simData.schema.name, resourceId]
            );

            // Check for instance value conflicts
            for (const resource of sameSchemaResources) {
                // Get resource content
                const content = await this.databaseService.executeQuery(
                    `SELECT content FROM ParsedContent
                     WHERE resourceId = ? AND contentType = 'simdata'`,
                    [resource.id]
                );

                // Skip if no content
                if (!content || content.length === 0) continue;

                try {
                    // Parse content
                    const parsedContent = JSON.parse(content[0].content);
                    
                    // Check for instance name conflicts
                    if (parsedContent.instances) {
                        for (const instance of simData.instances) {
                            for (const otherInstance of parsedContent.instances) {
                                if (instance.name === otherInstance.name && instance.instanceId !== otherInstance.instanceId) {
                                    conflicts.push({
                                        resourceId: resource.id,
                                        resourceKey: {
                                            type: resource.type,
                                            group: BigInt(resource.group),
                                            instance: BigInt(resource.instance)
                                        },
                                        conflictType: 'same_instance_name',
                                        severity: 'high',
                                        description: `Resource has instance with same name "${instance.name}" but different ID`
                                    });
                                }
                            }
                        }
                    }
                } catch (error) {
                    this.logger.error(`Error parsing content for resource ${resource.id}: ${error}`);
                }
            }

            return conflicts;
        } catch (error) {
            this.logger.error(`Error identifying potential conflicts: ${error}`);
            return conflicts;
        }
    }
}
