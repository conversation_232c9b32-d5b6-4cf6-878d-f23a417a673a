﻿import { PackageAnalysisService } from '../../services/analysis/packageAnalysisService.js';
import { Logger } from '../logging/logger.js';
const logger = new Logger('AnalysisUtils');

/**
 * Safely analyzes a package and returns the result or null if analysis fails
 */
export async function analyzePackageSafely(
  service: PackageAnalysisService,
  packagePath: string
): Promise<any | null> {
  try {
    return await service.analyzePackage(packagePath);
  } catch (error) {
    logger.error(`Failed to analyze package at ${packagePath}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return null;
  }
} 
