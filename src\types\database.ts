export interface PackageInfo {
    id?: number; // Optional for new packages
    name: string;
    path: string;
    hash: string;
    size: number;
    lastModified: number; // Timestamp
}

/**
 * Comprehensive ResourceInfo interface used throughout the application
 * This is the primary interface for resource information
 */
export interface ResourceInfo {
    // Database fields
    id?: number; // Optional for new resources
    packageId: number;
    type: number;
    group: bigint; // Use bigint for group ID
    instance: bigint; // Use bigint for instance ID
    hash: string;
    size: number;
    offset: number;
    contentSnippet?: string;
    resourceType: string; // e.g., 'TUNING_XML', 'SIMDATA', 'OBJDEF', etc.

    // Additional fields for analysis and conflict detection
    key?: { type: number; group: bigint; instance: bigint };
    buffer?: Buffer;
    metadata?: ResourceMetadata;

    // Package information for context
    packageName?: string;
    packagePath?: string;
}

export interface MetadataInfo {
    id?: number;
    resourceId: number;
    key: string;
    value: string | number | boolean | bigint | null; // Added bigint
}

export interface DependencyInfo {
    id?: number;
    resourceId: number; // Source resource ID
    sourceResourceId?: number; // Alias for resourceId for backward compatibility
    targetType: number;
    targetGroup: bigint; // Use bigint for targetGroup ID
    targetInstance: bigint;
    referenceType?: string; // Type of reference (e.g., 'Material', 'Skeleton', 'ScriptReference')
    timestamp?: number; // When the dependency was detected
}

export interface ParsedContentInfo {
    id?: number;
    resourceId: number;
    contentType: string; // e.g., 'tuning', 'simdata'
    content: string; // JSON string of parsed data
}

export interface OverrideInfo {
    id?: number;
    packageId: number;
    overridingResourceId: number;
    overriddenTGI: string; // Format: 'Type:Group:Instance'
    overriddenResourceId?: number | null; // ID if the overridden resource is in our DB
}

export interface ResourceMetadata {
    name?: string;
    description?: string;
    resourceType?: string;
    contentType?: string;
    hash?: string;
    isOverride?: boolean;
    tuningType?: string;
    modType?: string;
    modCategory?: string;

    // Content-addressable storage fields
    contentPath?: string; // Path to content in content-addressable storage
    contentSize?: number; // Size of content in content-addressable storage
    signatureHash?: string; // Hash used for similarity detection

    // Path to the file containing the resource
    path?: string;

    // Resource size
    size?: number;

    // Timestamp when the resource was analyzed
    timestamp?: number;

    // Resource type information
    resourceTypeHex?: string;
    resourceDescription?: string;
    isOfficialType?: boolean;

    // Extractor information
    extractorUsed?: string;
    extractionError?: string;

    [key: string]: any; // Allow any additional properties
}