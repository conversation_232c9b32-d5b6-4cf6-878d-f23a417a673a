import { Logger } from '../../../../utils/logging/logger.js';
import { DatabaseService } from '../../../databaseService.js';
import { ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { DependencyInfo } from '../../../../types/database.js'; // Corrected import path for DependencyInfo
import {
    TuningType,
    TuningCategory,
    TUNING_TYPE_PATTERNS,
    TUNING_TYPE_TO_CATEGORY,
    TuningClassHierarchy,
    TuningOverrideInfo,
    TuningCriticalParameter,
    TuningParameterReference,
    TuningClassification,
    CRITICAL_GAMEPLAY_PARAMETERS,
    TuningXmlAnalysis // Import TuningXmlAnalysis
} from '../../../../types/resource/tuningXml.js'; // Corrected import path for tuningXml types

const logger = new Logger('EnhancedTuningAnalysis');

/**
 * Mapping of common attribute names to their likely resource types (hexadecimal).
 * This is used to infer the target resource type when only an instance ID is provided.
 */
const ATTRIBUTE_NAME_TYPE_MAPPING: { [key: string]: number } = {
    trait_type: 0x8D107530, // Trait
    buff_instance: 0x60BEE33C, // Buff
    interaction_instance: 0xE882D22F, // Interaction
    object_definition: 0x60038DBB, // Object
    simdata_instance: 0x220557DA, // SimData
    tuning_instance: 0x62E9FDDA, // Tuning
    // Add more mappings as needed based on common tuning patterns
};

/**
 * Mapping of common parent tag and attribute name combinations to their likely resource types (hexadecimal).
 * This is used to infer the target resource type when only an instance ID is provided,
 * considering the context of the reference within the XML structure.
 */
const PARENT_ATTRIBUTE_TYPE_MAPPING: { [key: string]: number } = {
    'L.trait_type': 0x8D107530, // List of Traits
    'U.buff_instance': 0x60BEE33C, // Buff Update
    'T.interaction_instance': 0xE882D22F, // Test or other element referencing an interaction
    'V.object_definition': 0x60038DBB, // Value referencing an object definition
    'L.instance': 0x62E9FDDA, // List of tuning instances
    'U.instance': 0x62E9FDDA, // Tuning Update referencing an instance
    // Add more mappings as needed based on common tuning patterns and structures
};

/**
 * Track parameter references within tuning XML, including potential references to other resources
 * via instance IDs. These instance IDs would need to be resolved against the database
 * in a later analysis stage to establish cross-file relationships.
 * @param rootElement The root element of the XML
 * @returns Array of parameter references
 */
function trackParameterReferences(
    rootElement: any,
    resourceId: number, // Pass resourceId
    databaseService: DatabaseService // Pass databaseService
): TuningParameterReference[] {
    const parameterReferences: TuningParameterReference[] = [];

    try {
        // Function to recursively search for parameter references
        function findParameterReferences(node: any, path: string, parentNode: any = null) {
            if (!node || typeof node !== 'object') return;

            // Check for reference attributes
            if (node.$) {
                const parentTag = parentNode?.tag || ''; // Get parent tag if parentNode exists

                for (const attrName in node.$) {
                    const attrValue = node.$[attrName];

                    // Check for reference attributes (typically 'r' or 'ref')
                    if ((attrName === 'r' || attrName === 'ref') && attrValue) {
                        parameterReferences.push({
                            sourcePath: path,
                            targetPath: attrValue,
                            referenceType: 'parameter_reference',
                            isRequired: true,
                            referenceContext: `${attrName}="${attrValue}"`
                        });
                    }

                    // Check for instance references (potential cross-file references)
                    else if (attrName === 'i' || attrName === 'instance' || ATTRIBUTE_NAME_TYPE_MAPPING[attrName] || PARENT_ATTRIBUTE_TYPE_MAPPING[`${parentTag}.${attrName}`]) {
                        // Check if the attribute value looks like an instance ID (hexadecimal string)
                        if (typeof attrValue === 'string' &&
                            (attrValue.match(/^0x[0-9a-fA-F]+$/) ||
                             attrValue.match(/^[0-9a-fA-F]{8,16}$/))) {

                            // Convert to numeric value (BigInt) and then to string for storage
                            let instanceId: bigint;
                            try {
                                instanceId = BigInt(attrValue.startsWith('0x') ? attrValue : `0x${attrValue}`);
                            } catch (error) {
                                // Not a valid instance ID, ignore
                                logger.debug(`Could not parse potential instance ID "${attrValue}" at path "${path}.${attrName}"`);
                                continue; // Skip to the next attribute
                            }

                            let resolvedResource = undefined;
                            let resolutionAttempted = false;
                            let resolutionType = 'unresolved';

                            // Attempt to infer target type from parent tag and attribute name
                            const parentAttrKey = `${parentTag}.${attrName}`;
                            if (PARENT_ATTRIBUTE_TYPE_MAPPING[parentAttrKey]) {
                                const targetType = PARENT_ATTRIBUTE_TYPE_MAPPING[parentAttrKey];
                                logger.debug(`Attempting to resolve instance ID ${instanceId.toString()} with inferred type ${targetType.toString(16)} (from parent "${parentTag}" and attribute "${attrName}")`);
                                resolvedResource = databaseService.resources.findResourceByTGI( // Use resources repository
                                    targetType,
                                    0x00000000n, // Common Group - assuming common group for now
                                    instanceId, // Use instanceId BigInt
                                    resourceId // Exclude the source resource
                                );
                                resolutionAttempted = true;
                                resolutionType = `inferred_parent_attr:${parentAttrKey}`;

                                if (resolvedResource) {
                                    logger.debug(`Resolved instance ID ${instanceId.toString()} with inferred type ${targetType.toString(16)} to resource ID ${resolvedResource.id}`);
                                } else {
                                    logger.debug(`Could not resolve instance ID ${instanceId.toString()} with inferred type ${targetType.toString(16)} (from parent "${parentTag}" and attribute "${attrName}").`);
                                }
                            }


                            // If not resolved, attempt to infer target type from attribute name
                            if (!resolvedResource && ATTRIBUTE_NAME_TYPE_MAPPING[attrName]) {
                                const targetType = ATTRIBUTE_NAME_TYPE_MAPPING[attrName];
                                logger.debug(`Attempting to resolve instance ID ${instanceId.toString()} with inferred type ${targetType.toString(16)} (from attribute "${attrName}")`);
                                resolvedResource = databaseService.resources.findResourceByTGI( // Use resources repository
                                    targetType,
                                    0x00000000n, // Common Group - assuming common group for now
                                    instanceId, // Use instanceId BigInt
                                    resourceId // Exclude the source resource
                                );
                                resolutionAttempted = true;
                                resolutionType = `inferred_attr:${attrName}`;

                                if (resolvedResource) {
                                    logger.debug(`Resolved instance ID ${instanceId.toString()} with inferred type ${targetType.toString(16)} to resource ID ${resolvedResource.id}`);
                                } else {
                                    logger.debug(`Could not resolve instance ID ${instanceId.toString()} with inferred type ${targetType.toString(16)} (from attribute "${attrName}").`);
                                }
                            }


                            // If not resolved and no type inferred, attempt with common types
                            if (!resolvedResource && !resolutionAttempted) {
                                logger.debug(`Attempting to resolve instance ID ${instanceId.toString()} with common Tuning type`);
                                resolvedResource = databaseService.resources.findResourceByTGI( // Use resources repository
                                    0x62E9FDDA, // Common Tuning Type
                                    0x00000000n, // Common Group
                                    instanceId, // Use instanceId BigInt
                                    resourceId // Exclude the source resource
                                );

                                if (resolvedResource) {
                                    resolutionType = 'common_tuning';
                                    logger.debug(`Resolved instance ID ${instanceId.toString()} with common Tuning type to resource ID ${resolvedResource.id}`);
                                } else {
                                    logger.debug(`Attempting to resolve instance ID ${instanceId.toString()} with common SimData type`);
                                    resolvedResource = databaseService.resources.findResourceByTGI( // Use resources repository
                                        0x220557DA, // Common SimData Type
                                        0x00000000n, // Common Group
                                        instanceId, // Use instanceId BigInt
                                        resourceId // Exclude the source resource
                                    );

                                    if (resolvedResource) {
                                        resolutionType = 'common_simdata';
                                        logger.debug(`Resolved instance ID ${instanceId.toString()} with common SimData type to resource ID ${resolvedResource.id}`);
                                    } else {
                                        resolutionType = 'unresolved';
                                        logger.debug(`Could not resolve instance ID ${instanceId.toString()} to a resource in the database using common types.`);
                                    }
                                }
                            }


                            parameterReferences.push({
                                sourcePath: path,
                                targetResource: instanceId.toString(), // Store instance ID for later resolution
                                referenceType: 'instance_reference', // Or a more specific type if inferred
                                isRequired: true, // Assuming instance references are required
                                referenceContext: `${attrName}="${attrValue}"`, // Keep the original context
                                resolvedResourceId: resolvedResource?.id, // Add resolved resource ID if resolved
                                resolutionType: resolutionType // Add how the resolution was attempted/successful
                            });

                             // If resolved, save the relationship as a dependency
                            if (resolvedResource) {
                                databaseService.dependencies.saveDependency({ // Use dependencies repository
                                    resourceId: resourceId, // Use resourceId property
                                    targetType: resolvedResource.type,
                                    targetGroup: BigInt(resolvedResource.group), // Ensure bigint
                                    targetInstance: BigInt(resolvedResource.instance), // Ensure bigint
                                    referenceType: 'tuning_parameter_reference', // Specific reference type
                                    // Cannot add referenceContext, resolvedResourceId, resolutionType here due to interface limitation
                                    // referenceContext: `${attrName}="${attrValue}"`, // Keep the original context
                                    // resolvedResourceId: resolvedResource.id, // Store the resolved resource ID
                                    // resolutionType: resolutionType // Store how it was resolved
                                });
                                logger.debug(`Saved dependency for resource ${resourceId} to resolved resource ${resolvedResource.id}`);
                            }
                        }
                    }
                }
            }

            // Recursively check child nodes
            for (const key in node) {
                if (key === '$' || key === '_') continue;

                const child = node[key];
                const childPath = path ? `${path}.${key}` : key;

                if (Array.isArray(child)) {
                    for (let i = 0; i < child.length; i++) {
                        findParameterReferences(child[i], `${childPath}[${i}]`, node); // Pass current node as parent
                    }
                } else if (typeof child === 'object') {
                    findParameterReferences(child, childPath, node); // Pass current node as parent
                }
            }
        }

        // Start the search from the root
        findParameterReferences(rootElement, '');

        return parameterReferences;
    } catch (error) {
        logger.error(`Error tracking parameter references: ${error}`);
        return [];
    }
}

/**
 * Extracts class hierarchy information from the tuning XML.
 * @param rootElement The root element of the XML.
 * @param tuningClass The tuning class name.
 * @returns The class hierarchy information.
 */
function extractClassHierarchy(rootElement: any, tuningClass?: string): TuningClassHierarchy | undefined {
    if (!tuningClass) return undefined;

    const hierarchy: string[] = [tuningClass];
    let currentElement = rootElement;
    let currentClass = tuningClass;
    let depth = 1;

    // Traverse up the hierarchy using the 'parent' attribute
    while (currentElement.$ && currentElement.$.parent) {
        const parentClass = currentElement.$.parent;
        if (hierarchy.includes(parentClass)) {
            // Avoid infinite loops in case of circular references in XML
            logger.warn(`Circular class hierarchy detected for ${tuningClass}. Hierarchy: ${hierarchy.join(' -> ')} -> ${parentClass}`);
            break;
        }
        hierarchy.push(parentClass);
        currentClass = parentClass;
        depth++;

        // In a real scenario, you might need to fetch the parent tuning's XML
        // to continue traversing. For this analysis, we'll stop after the first parent.
        break;
    }

    return {
        className: tuningClass, // Corrected property name
        parentClass: hierarchy.length > 1 ? hierarchy[hierarchy.length - 1] : undefined,
        depth: depth,
        // inheritedProperties and overriddenProperties are not extracted here
    };
}

/**
 * Detects if the tuning XML is an override and extracts relevant information.
 * @param rootElement The root element of the XML.
 * @param rootElementName The name of the root element.
 * @param tuningName The tuning name.
 * @returns The override information.
 */
function detectOverrides(rootElement: any, rootElementName: string, tuningName?: string): TuningOverrideInfo {
    const overriddenElements: string[] = [];
    let isOverride = false;
    let targetClass: string | undefined = undefined;
    let targetInstance: string | undefined = undefined;
    let intent: TuningOverrideInfo['intent'] = 'unknown'; // Corrected type and default

    // Check for common override patterns in attributes
    if (rootElement.$) {
        if (rootElement.$.target_class) {
            isOverride = true;
            targetClass = rootElement.$.target_class;
            intent = 'replace'; // Assuming target_class implies replacement
        }
        if (rootElement.$.target_instance) {
            isOverride = true;
            targetInstance = rootElement.$.target_instance;
            intent = intent === 'unknown' ? 'replace' : intent; // Assuming target_instance implies replacement
        }
        if (rootElement.$.override_tuning) {
            isOverride = true;
            targetInstance = rootElement.$.override_tuning; // Assuming this attribute points to the overridden instance
            intent = intent === 'unknown' ? 'replace' : intent; // Assuming override_tuning implies replacement
        }
    }

    // Check for common override patterns in element names (e.g., <Override>)
    if (rootElement.Override) {
        isOverride = true;
        intent = intent === 'unknown' ? 'enhance' : intent; // Assuming <Override> element implies enhancement
        // You might need to recursively find elements within <Override> to list them
        // For simplicity, we'll just note the presence of the element for now.
        overriddenElements.push('Override element present');
    }

    // Check for naming conventions indicating an override
    if (tuningName && (tuningName.includes('_override') || tuningName.includes('_patch'))) {
        isOverride = true;
        intent = intent === 'unknown' ? 'unknown' : intent; // Naming convention alone doesn't strongly indicate intent
    }

    // Recursively find overridden elements (simplified)
    function findOverriddenElements(node: any, path: string) {
        if (!node || typeof node !== 'object') return;

        // Check for attributes indicating override (e.g., 'replace', 'add', 'remove')
        if (node.$) {
            if (node.$.op && ['replace', 'add', 'remove'].includes(node.$.op)) {
                overriddenElements.push(`${path} (op="${node.$.op}")`);
            }
        }

        // Recursively check child nodes
        for (const key in node) {
            if (key === '$' || key === '_') continue;

            const child = node[key];
            const childPath = path ? `${path}.${key}` : key;

            if (Array.isArray(child)) {
                for (let i = 0; i < child.length; i++) {
                    findOverriddenElements(child[i], `${childPath}[${i}]`);
                }
            } else if (typeof child === 'object') {
                findOverriddenElements(child, childPath);
            }
        }
    }

    // Start finding overridden elements from the root
    findOverriddenElements(rootElement, rootElementName);


    return {
        isOverride,
        targetClass,
        targetInstance,
        overriddenElements,
        intent,
        confidence: isOverride ? 1.0 : 0.0 // Set confidence based on detection
    };
}

/**
 * Identifies critical gameplay parameters within the tuning XML.
 * @param rootElement The root element of the XML.
 * @returns An array of critical parameters found.
 */
function identifyCriticalParameters(rootElement: any): TuningCriticalParameter[] {
    const criticalParameters: TuningCriticalParameter[] = [];

    // Function to recursively search for critical parameters
    function findCriticalParameters(node: any, path: string) {
        if (!node || typeof node !== 'object') return;

        // Check if the current path matches any critical parameter path
        for (const criticalParamName of CRITICAL_GAMEPLAY_PARAMETERS) { // Iterate over names
            if (path.endsWith('.' + criticalParamName) || path === criticalParamName) { // Check if path ends with or is the critical parameter name
                let value: any = undefined;
                let valueContext: string | undefined = undefined;

                // Try to get the value from attributes or text content
                if (node.$ && node.$.value !== undefined) {
                    value = node.$.value;
                    valueContext = `attribute: value="${value}"`;
                } else if (node._ !== undefined) {
                    value = node._;
                    valueContext = `text: "${value}"`;
                } else if (Object.keys(node).length === 1 && Object.keys(node)[0] !== '$' && Object.keys(node)[0] !== '_') {
                     // If it's an element with a single child element, the value might be in the child's attributes or text
                     const childKey = Object.keys(node)[0];
                     const childNode = node[childKey];
                     if (childNode.$ && childNode.$.value !== undefined) {
                         value = childNode.$.value;
                         valueContext = `child attribute: ${childKey}.value="${value}"`;
                     } else if (childNode._ !== undefined) {
                         value = childNode._;
                         valueContext = `child text: ${childKey}="${value}"`;
                     }
                }


                criticalParameters.push({
                    name: criticalParamName, // Use the parameter name from the list
                    path: path,
                    value: value !== undefined ? String(value) : 'N/A', // Ensure value is string
                    // valueContext: valueContext || 'N/A', // Removed valueContext as it's not in the type
                    impact: 'medium', // Default impact - Corrected to a valid type
                    description: `Parameter "${criticalParamName}" found at path "${path}"`, // Default description
                    affectsGameplay: true, // Assuming critical parameters affect gameplay
                    potentialConflictRisk: 'medium' // Default risk - need more context to determine
                });
            }
        }

        // Recursively check child nodes
        for (const key in node) {
            if (key === '$' || key === '_') continue;

            const child = node[key];
            const childPath = path ? `${path}.${key}` : key;

            if (Array.isArray(child)) {
                for (let i = 0; i < child.length; i++) {
                    findCriticalParameters(child[i], `${childPath}[${i}]`);
                }
            } else if (typeof child === 'object') {
                findCriticalParameters(child, childPath);
            }
        }
    }

    // Start the search from the root
    findCriticalParameters(rootElement, Object.keys(rootElement)[0]);

    return criticalParameters;
}

/**
 * Classifies the tuning XML based on its content and structure.
 * @param rootElement The root element of the XML.
 * @param rootElementName The name of the root element.
 * @param tuningName The tuning name.
 * @returns The classification information.
 */
function classifyTuning(rootElement: any, rootElementName: string, tuningName?: string): TuningClassification {
    let type: TuningType = TuningType.UNKNOWN;
    let category: TuningCategory = TuningCategory.UNKNOWN; // Corrected default category
    const semanticSystems: string[] = [];
    let confidence = 0.5; // Default confidence
    let description = 'Unknown tuning resource.';
    const tags: string[] = [];

    // Classify based on root element name
    for (const pattern in TUNING_TYPE_PATTERNS) {
        const regex = new RegExp(TUNING_TYPE_PATTERNS[pattern as TuningType].join('|'), 'i'); // Join patterns with |
        if (regex.test(rootElementName)) {
            type = pattern as TuningType;
            category = TUNING_TYPE_TO_CATEGORY[type] || TuningCategory.UNKNOWN; // Corrected default category
            confidence += 0.2; // Increase confidence based on root element match
            description = `${type} tuning resource.`;
            tags.push(type.toLowerCase());
            tags.push(category.toLowerCase());
            break;
        }
    }

    // Further refine classification based on content (simplified examples)
    if (JSON.stringify(rootElement).includes('buff_instance')) {
        if (type === TuningType.UNKNOWN) type = TuningType.BUFF;
        if (category === TuningCategory.UNKNOWN) category = TuningCategory.SIM_ATTRIBUTES; // Buffs are often linked to Sim Attributes
        semanticSystems.push('Sims');
        semanticSystems.push('Emotions');
        confidence += 0.1;
        description = `Likely a ${type} tuning related to buffs.`;
        tags.push('buff');
    }

    if (JSON.stringify(rootElement).includes('interaction_instance')) {
        if (type === TuningType.UNKNOWN) type = TuningType.INTERACTION;
        if (category === TuningCategory.UNKNOWN) category = TuningCategory.INTERACTIONS;
        semanticSystems.push('Interactions');
        semanticSystems.push('Gameplay');
        confidence += 0.1;
        description = `Likely a ${type} tuning related to interactions.`;
        tags.push('interaction');
    }

    // Add more content-based classification rules here

    // Classify based on naming conventions (if tuningName is available)
    if (tuningName) {
        if (tuningName.includes('trait')) {
            if (type === TuningType.UNKNOWN) type = TuningType.TRAIT;
            if (category === TuningCategory.UNKNOWN) category = TuningCategory.SIM_ATTRIBUTES;
            semanticSystems.push('Sims');
            semanticSystems.push('Personality');
            confidence += 0.1;
            description = `Likely a ${type} tuning related to traits.`;
            tags.push('trait');
        }
        // Add more naming convention rules here
    }

    // Ensure confidence does not exceed 1.0
    confidence = Math.min(confidence, 1.0);

    // Remove duplicate semantic systems and tags
    const uniqueSemanticSystems = Array.from(new Set(semanticSystems));
    const uniqueTags = Array.from(new Set(tags));


    return {
        type,
        category,
        semanticSystems: uniqueSemanticSystems,
        confidence,
        description,
        tags: uniqueTags
    };
}


/**
 * Performs enhanced analysis steps and returns the results.
 * This includes extracting class hierarchy, detecting overrides,
 * identifying critical parameters, tracking parameter references,
 * classifying tuning, calculating complexity, and determining conflict risk.
 *
 * @param rootElement The root element of the XML.
 * @param rootElementName The name of the root element.
 * @param extractedMetadata The basic metadata extracted so far.
 * @param resourceId The internal resource ID.
 * @param databaseService The database service instance.
 * @returns An object containing enhanced metadata and the comprehensive tuning analysis object.
 */
export async function performEnhancedAnalysis(
    rootElement: any,
    rootElementName: string,
    extractedMetadata: Partial<ResourceMetadata>,
    resourceId: number,
    databaseService: DatabaseService,
): Promise<{ metadata: Partial<ResourceMetadata>, tuningAnalysis: TuningXmlAnalysis }> { // Explicitly type tuningAnalysis
    const enhancedMetadata: Partial<ResourceMetadata> = {};

    // Extract all attributes from the root element
    const rootAttributes: Record<string, any> = rootElement.$ || {};

    // 1. Extract class hierarchy information
    const classHierarchy = extractClassHierarchy(rootElement, extractedMetadata.tuningClass);
    if (classHierarchy) {
        enhancedMetadata.tuningParentClass = classHierarchy.parentClass;
        enhancedMetadata.tuningClassHierarchy = JSON.stringify(classHierarchy);
        enhancedMetadata.tuningClassHierarchyDepth = classHierarchy.depth;

        // Save class hierarchy to database
        databaseService.metadata.saveMetadata({ // Use metadata repository
            resourceId: resourceId,
            key: 'tuningClassHierarchy',
            value: JSON.stringify(classHierarchy)
        });
    }

    // 2. Detect overrides
    const overrideInfo = detectOverrides(rootElement, rootElementName, extractedMetadata.tuningName);
    enhancedMetadata.tuningIsOverride = overrideInfo.isOverride;
    if (overrideInfo.targetClass) {
        enhancedMetadata.tuningOverrideTarget = overrideInfo.targetClass;
    } else if (overrideInfo.targetInstance) {
        enhancedMetadata.tuningOverrideTarget = overrideInfo.targetInstance;
    }
    enhancedMetadata.tuningOverrideElements = JSON.stringify(overrideInfo.overriddenElements);
    enhancedMetadata.tuningOverrideIntent = overrideInfo.intent;

    // Save override info to database
    databaseService.metadata.saveMetadata({ // Use metadata repository
        resourceId: resourceId,
        key: 'tuningOverrideInfo',
        value: JSON.stringify(overrideInfo)
    });

    // 3. Identify critical parameters
    const criticalParameters = identifyCriticalParameters(rootElement);
    enhancedMetadata.tuningCriticalParameters = JSON.stringify(criticalParameters);

    // Save critical parameters to database
    databaseService.metadata.saveMetadata({ // Use metadata repository
        resourceId: resourceId,
        key: 'tuningCriticalParameters',
        value: JSON.stringify(criticalParameters)
    });

    // 4. Track parameter references and resolve cross-file references (instance IDs)
    const parameterReferences = trackParameterReferences(rootElement, resourceId, databaseService); // Call trackParameterReferences here

    // parameterReferences are resolved and saved as dependencies within trackParameterReferences

    // 5. Classify tuning
    const classification = classifyTuning(rootElement, rootElementName, extractedMetadata.tuningName);
    enhancedMetadata.tuningType = classification.type;
    enhancedMetadata.tuningCategory = classification.category;
    enhancedMetadata.tuningSemanticSystems = classification.semanticSystems.join(', ');
    enhancedMetadata.tuningClassificationConfidence = classification.confidence;
    enhancedMetadata.tuningDescription = classification.description;
    enhancedMetadata.tuningTags = JSON.stringify(classification.tags);

    // Save classification to database
    databaseService.metadata.saveMetadata({ // Use metadata repository
        resourceId: resourceId,
        key: 'tuningClassification',
        value: JSON.stringify(classification)
    });

    // 6. Calculate complexity score
    let complexityScore = 0;
    if (classHierarchy && classHierarchy.depth > 1) {
        complexityScore += classHierarchy.depth * 5;
    }
    if (overrideInfo.isOverride) {
        complexityScore += 10;
        complexityScore += overrideInfo.overriddenElements.length * 2;
    }
    complexityScore += criticalParameters.length * 3;
    complexityScore += parameterReferences.length;
    complexityScore = Math.min(complexityScore, 100);
    enhancedMetadata.tuningComplexityScore = complexityScore;

    // Save complexity score to database
    databaseService.metadata.saveMetadata({ // Use metadata repository
        resourceId: resourceId,
        key: 'tuningComplexityScore',
        value: complexityScore.toString()
    });

    // 7. Determine conflict risk
    let potentialConflictRisk: TuningXmlAnalysis['potentialConflictRisk'] = 'low'; // Corrected type and default
    if (overrideInfo.isOverride) {
        potentialConflictRisk = 'medium';
        if (overrideInfo.overriddenElements.length > 5 || criticalParameters.length > 3) {
            potentialConflictRisk = 'high';
        }
    } else if (criticalParameters.filter(p => p.potentialConflictRisk === 'high').length > 0) {
        potentialConflictRisk = 'medium';
        if (criticalParameters.filter(p => p.potentialConflictRisk === 'high').length > 2) {
            potentialConflictRisk = 'high';
        }
    }
    enhancedMetadata.tuningPotentialConflictRisk = potentialConflictRisk;

    // Save conflict risk to database
    databaseService.metadata.saveMetadata({ // Use metadata repository
        resourceId: resourceId,
        key: 'tuningPotentialConflictRisk',
        value: potentialConflictRisk
    });

    // Create a comprehensive analysis object
    const tuningAnalysis: TuningXmlAnalysis = { // Explicitly type tuningAnalysis
        resourceId: resourceId,
        rootElement: rootElementName,
        rootAttributes,
        tuningName: extractedMetadata.tuningName,
        tuningClass: extractedMetadata.tuningClass,
        tuningInstance: extractedMetadata.tuningInstance,
        classification,
        classHierarchy,
        overrideInfo,
        criticalParameters,
        parameterReferences,
        relationships: [], // Not implemented yet
        importantValues: {}, // Will be populated from critical parameters
        complexityScore,
        potentialConflictRisk: potentialConflictRisk, // Use the determined risk
        analysisConfidence: classification.confidence,
        isOfficial: extractedMetadata.tuningSemanticIsOfficial || false,
        moduleReferences: [],
        tuningReferences: [],
        overrideNodes: overrideInfo.overriddenElements,
        dependencyCount: parameterReferences.length // Use parameterReferences length for dependency count
    };

    // Convert critical parameters to important values
    criticalParameters.forEach(param => {
        tuningAnalysis.importantValues[param.path] = param.value;
    });

    return { metadata: enhancedMetadata, tuningAnalysis };
}
