import { <PERSON><PERSON><PERSON> } from "../../../types/resource/interfaces.js";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { WorldDefinitionMetadata } from "../../../types/resource/analysis.js"; // Assuming WorldDefinitionMetadata is defined here
import { logger } from "../../../utils/logging/logger.js";

/**
 * Extracts metadata from a WORLD_DEFINITION resource (0xF0633989).
 *
 * @param key The resource key of the WORLD_DEFINITION resource.
 * @param buffer The buffer containing the WORLD_DEFINITION resource data.
 * @returns The extracted WorldDefinitionMetadata.
 */
export function extractWorldDefinitionMetadata(key: <PERSON><PERSON><PERSON>, buffer: Buffer): WorldDefinitionMetadata | null {
  // WORLD_DEFINITION is a 512-byte struct starting with 'BWLD'
  const expectedSize = 512;
  const magicWord = buffer.readUInt32LE(0x00);
  const expectedMagic = 0x444C5742; // 'BWLD' in little-endian

  if (buffer.length < expectedSize || magicWord !== expectedMagic) {
    logger.warn(`WORLD_DEFINITION resource ${key.instance.toString(16)} has incorrect size or magic word.`);
    return null;
  }

  // TODO: Implement binary parsing based on docs/lot-res.md
  // Extract fields like WorldID, flags, and offsets.

  const worldId = buffer.readUInt32LE(0x10); // Offset for WorldID
  const flags = buffer.readUInt32LE(0x14); // Assumed offset for flags

  const offsets: number[] = [];
  const offsetsTableStart = 0x40;
  const numberOfOffsetsToRead = 20; // Assuming a fixed number of offsets based on common patterns

  for (let i = 0; i < numberOfOffsetsToRead; i++) {
    const offset = buffer.readUInt32LE(offsetsTableStart + i * 4);
    offsets.push(offset);
  }

  const metadata: WorldDefinitionMetadata = {
    worldId: worldId,
    flags: flags,
    offsets: offsets,
  };

  logger.info(`Extracted metadata for WORLD_DEFINITION resource ${key.instance.toString(16)}`);
  return metadata;
}