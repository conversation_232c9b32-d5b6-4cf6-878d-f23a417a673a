/**
 * Python Bytecode Code Object Parser
 *
 * This module provides functionality for parsing Python bytecode code objects.
 */

import { Logger } from '../../../../utils/logging/logger.js';
import { CodeObject, PythonVersion } from './types.js';
import { parseConstants, TypeCode } from './constantsParser.js';
import { SafeBufferReader } from './safeBufferReader.js';

// Create a logger for this module
const logger = new Logger('BytecodeCodeObjectParser');

/**
 * Reads a 32-bit signed integer from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The integer value and the new offset
 */
export function readLong(buffer: Buffer, offset: number): [number, number] {
    const value = buffer.readInt32LE(offset);
    return [value, offset + 4];
}

/**
 * Reads a string from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @returns The string value and the new offset
 */
export function readString(buffer: Buffer, offset: number): [string, number] {
    // Read the string length
    const [length, newOffset] = readLong(buffer, offset);

    // Read the string data
    const stringData = buffer.slice(newOffset, newOffset + length);
    const value = stringData.toString('utf-8');

    return [value, newOffset + length];
}

/**
 * Reads a string array from a buffer
 * @param buffer Buffer to read from
 * @param offset Offset to read from
 * @param count Number of strings to read
 * @returns The string array and the new offset
 */
export function readStringArray(buffer: Buffer, offset: number, count: number): [string[], number] {
    const strings: string[] = [];
    let currentOffset = offset;

    for (let i = 0; i < count; i++) {
        const [string, newOffset] = readString(buffer, currentOffset);
        strings.push(string);
        currentOffset = newOffset;
    }

    return [strings, currentOffset];
}

/**
 * Parses a Python code object from a buffer
 * @param buffer Buffer to parse from
 * @param offset Offset to start parsing from
 * @param pythonVersion Python version information
 * @returns The parsed code object and the new offset
 */
export function parseCodeObject(
    buffer: Buffer,
    offset: number,
    pythonVersion: PythonVersion
): [CodeObject | null, number] {
    try {
        // Create a safe buffer reader
        const reader = new SafeBufferReader(buffer, offset);

        // Check if we have a code object
        const typeByte = reader.readUInt8();
        if (typeByte === null) {
            logger.error('Failed to read type code byte');
            return [null, offset];
        }

        const typeCode = String.fromCharCode(typeByte);
        if (typeCode !== TypeCode.CODE) {
            logger.error(`Invalid code object type: ${typeCode}`);
            return [null, reader.getPosition()];
        }

        // Parse code object fields
        let argCount: number = 0;
        let posonlyArgCount: number | undefined;
        let kwonlyArgCount: number = 0;
        let nlocals: number = 0;
        let stackSize: number = 0;
        let flags: number = 0;

        // Read argument counts
        const argCountValue = reader.readInt32LE();
        if (argCountValue === null) {
            logger.error('Failed to read argCount');
            return [null, reader.getPosition()];
        }
        argCount = argCountValue;

        // Python 3.8+ has positional-only argument count
        if (pythonVersion.major === 3 && pythonVersion.minor >= 8) {
            const posonlyArgCountValue = reader.readInt32LE();
            if (posonlyArgCountValue === null) {
                logger.error('Failed to read posonlyArgCount');
                return [null, reader.getPosition()];
            }
            posonlyArgCount = posonlyArgCountValue;
        }

        // Read other counts
        const kwonlyArgCountValue = reader.readInt32LE();
        if (kwonlyArgCountValue === null) {
            logger.error('Failed to read kwonlyArgCount');
            return [null, reader.getPosition()];
        }
        kwonlyArgCount = kwonlyArgCountValue;

        const nlocalsValue = reader.readInt32LE();
        if (nlocalsValue === null) {
            logger.error('Failed to read nlocals');
            return [null, reader.getPosition()];
        }
        nlocals = nlocalsValue;

        const stackSizeValue = reader.readInt32LE();
        if (stackSizeValue === null) {
            logger.error('Failed to read stackSize');
            return [null, reader.getPosition()];
        }
        stackSize = stackSizeValue;

        const flagsValue = reader.readInt32LE();
        if (flagsValue === null) {
            logger.error('Failed to read flags');
            return [null, reader.getPosition()];
        }
        flags = flagsValue;

        // Read code
        const codeLengthValue = reader.readInt32LE();
        if (codeLengthValue === null) {
            logger.error('Failed to read code length');
            return [null, reader.getPosition()];
        }
        const codeLength = codeLengthValue;

        const codeSlice = reader.readSlice(codeLength);
        if (codeSlice === null) {
            logger.error('Failed to read code');
            return [null, reader.getPosition()];
        }
        const code = codeSlice;

        // For the remaining parts, we'll continue using the existing functions
        // but with the current position from our safe reader
        let currentOffset = reader.getPosition();

        // Read constants
        let constantCount: number;
        let constants: any[];
        [constantCount, currentOffset] = readLong(buffer, currentOffset);
        [constants, currentOffset] = parseConstants(buffer, currentOffset, constantCount);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read names
        let nameCount: number;
        let names: string[];
        [nameCount, currentOffset] = readLong(buffer, currentOffset);
        [names, currentOffset] = readStringArray(buffer, currentOffset, nameCount);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read variable names
        let varnameCount: number;
        let varnames: string[];
        [varnameCount, currentOffset] = readLong(buffer, currentOffset);
        [varnames, currentOffset] = readStringArray(buffer, currentOffset, varnameCount);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read free variables
        let freevarCount: number;
        let freevars: string[];
        [freevarCount, currentOffset] = readLong(buffer, currentOffset);
        [freevars, currentOffset] = readStringArray(buffer, currentOffset, freevarCount);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read cell variables
        let cellvarCount: number;
        let cellvars: string[];
        [cellvarCount, currentOffset] = readLong(buffer, currentOffset);
        [cellvars, currentOffset] = readStringArray(buffer, currentOffset, cellvarCount);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read filename
        let filename: string;
        [filename, currentOffset] = readString(buffer, currentOffset);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read name
        let name: string;
        [name, currentOffset] = readString(buffer, currentOffset);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read first line number
        let firstLineNo: number;
        [firstLineNo, currentOffset] = readLong(buffer, currentOffset);

        // Update reader position
        reader.setPosition(currentOffset);

        // Read line number table
        let lnotabLength: number;
        let lnotab: Buffer;
        [lnotabLength, currentOffset] = readLong(buffer, currentOffset);

        // Use safe reader for the final slice
        reader.setPosition(currentOffset);
        const lnotabSlice = reader.readSlice(lnotabLength);
        if (lnotabSlice === null) {
            logger.error('Failed to read lnotab');
            return [null, reader.getPosition()];
        }
        lnotab = lnotabSlice;
        currentOffset = reader.getPosition();

        // Create code object
        const codeObject: CodeObject = {
            argCount,
            posonlyArgCount,
            kwonlyArgCount,
            nlocals,
            stackSize,
            flags,
            code,
            constants,
            names,
            varnames,
            freevars,
            cellvars,
            filename,
            name,
            firstLineNo,
            lnotab
        };

        logger.debug(`Parsed code object: name=${name}, filename=${filename}, argCount=${argCount}`);

        return [codeObject, currentOffset];
    } catch (error: any) {
        logger.error(`Error parsing code object: ${error.message || error}`);
        return [null, offset];
    }
}

/**
 * Extracts string literals from a code object
 * @param codeObject Code object to extract string literals from
 * @returns Array of string literals
 */
export function extractStringLiterals(codeObject: CodeObject): string[] {
    const stringLiterals: string[] = [];

    // Extract string literals from constants
    for (const constant of codeObject.constants) {
        if (typeof constant === 'string') {
            stringLiterals.push(constant);
        } else if (constant instanceof CodeObject) {
            // Recursively extract string literals from nested code objects
            stringLiterals.push(...extractStringLiterals(constant));
        }
    }

    // Extract string literals from names
    stringLiterals.push(...codeObject.names);

    // Extract string literals from variable names
    stringLiterals.push(...codeObject.varnames);

    return stringLiterals;
}

/**
 * Extracts imports from a code object
 * @param codeObject Code object to extract imports from
 * @returns Array of import statements
 */
export function extractImports(codeObject: CodeObject): string[] {
    const imports: string[] = [];

    // Look for import-related names
    const importNames = codeObject.names.filter(name =>
        name === 'import' ||
        name === '__import__' ||
        name === 'importlib' ||
        name === 'from_import'
    );

    if (importNames.length > 0) {
        // Look for potential module names in constants
        for (const constant of codeObject.constants) {
            if (typeof constant === 'string' &&
                constant.includes('.') &&
                !constant.includes(' ') &&
                !constant.includes('(') &&
                !constant.includes('{')) {
                imports.push(constant);
            }
        }
    }

    return imports;
}
