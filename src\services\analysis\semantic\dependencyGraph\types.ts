/**
 * Types for the Dependency Graph Builder
 * 
 * This file contains interfaces and types used by the DependencyGraphBuilder class.
 */

import { ResourceKey, ResourceMetadata } from '../../../../types/resource/interfaces.js';

/**
 * Metadata about a dependency between resources
 */
export interface DependencyMetadata {
  /**
   * Type of reference (e.g., 'Required', 'Optional', 'Fallback')
   */
  referenceType: string;
  
  /**
   * Strength of the dependency (0-100)
   * Higher values indicate stronger dependencies
   */
  strength: number;
  
  /**
   * Optional description of the dependency
   */
  description?: string;
  
  /**
   * Timestamp when the dependency was detected
   */
  timestamp: number;
}

/**
 * A node in the dependency tree
 */
export interface DependencyNode {
  /**
   * Resource ID
   */
  resourceId: string;
  
  /**
   * Optional resource metadata
   */
  metadata?: ResourceMetadata;
  
  /**
   * Dependencies of this resource
   */
  dependencies: DependencyNode[];
}

/**
 * Metrics about the dependency graph
 */
export interface DependencyMetrics {
  /**
   * Total number of resources in the graph
   */
  totalResources: number;
  
  /**
   * Total number of dependencies in the graph
   */
  totalDependencies: number;
  
  /**
   * Average number of dependencies per resource
   */
  averageDependenciesPerResource: number;
  
  /**
   * Maximum number of dependencies for a single resource
   */
  maxDependenciesForResource: number;
  
  /**
   * Resource ID with the most dependencies
   */
  resourceWithMostDependencies: string;
  
  /**
   * Maximum number of dependents for a single resource
   */
  maxDependentsForResource: number;
  
  /**
   * Resource ID with the most dependents
   */
  resourceWithMostDependents: string;
  
  /**
   * Number of circular dependencies in the graph
   */
  circularDependencies: number;
  
  /**
   * Number of strongly connected components in the graph
   */
  stronglyConnectedComponents: number;
}

/**
 * Options for building the dependency graph
 */
export interface DependencyGraphOptions {
  /**
   * Whether to include weak dependencies in the graph
   * Default: false
   */
  includeWeakDependencies?: boolean;
  
  /**
   * Maximum depth of the dependency graph
   * Default: unlimited
   */
  maxDepth?: number;
  
  /**
   * Whether to include resource metadata in the graph
   * Default: false
   */
  includeResourceMetadata?: boolean;
  
  /**
   * Whether to cache results for faster retrieval
   * Default: true
   */
  cacheResults?: boolean;
  
  /**
   * Size of batches when processing large numbers of dependencies
   * Default: 1000
   */
  batchSize?: number;
  
  /**
   * Whether to use parallel processing for building the graph
   * Default: false
   */
  useParallelProcessing?: boolean;
}

/**
 * Visualization options for the dependency graph
 */
export interface GraphVisualizationOptions {
  /**
   * Maximum number of nodes to include in the visualization
   * Default: 100
   */
  maxNodes?: number;
  
  /**
   * Whether to include resource metadata in the visualization
   * Default: true
   */
  includeMetadata?: boolean;
  
  /**
   * Whether to include dependency metadata in the visualization
   * Default: true
   */
  includeDependencyMetadata?: boolean;
  
  /**
   * Layout algorithm to use for the visualization
   * Default: 'force'
   */
  layout?: 'force' | 'tree' | 'radial' | 'circular';
}

/**
 * JSON representation of the dependency graph for visualization
 */
export interface JsonGraph {
  /**
   * Nodes in the graph
   */
  nodes: JsonGraphNode[];
  
  /**
   * Links between nodes
   */
  links: JsonGraphLink[];
}

/**
 * A node in the JSON graph
 */
export interface JsonGraphNode {
  /**
   * Node ID (resource ID)
   */
  id: string;
  
  /**
   * Node name (resource name)
   */
  name: string;
  
  /**
   * Resource type
   */
  type: number;
  
  /**
   * Resource group
   */
  group: string;
  
  /**
   * Resource instance
   */
  instance: string;
  
  /**
   * Additional metadata
   */
  [key: string]: any;
}

/**
 * A link in the JSON graph
 */
export interface JsonGraphLink {
  /**
   * Source node ID
   */
  source: string;
  
  /**
   * Target node ID
   */
  target: string;
  
  /**
   * Link type (reference type)
   */
  type: string;
  
  /**
   * Link strength
   */
  strength: number;
  
  /**
   * Additional metadata
   */
  [key: string]: any;
}
