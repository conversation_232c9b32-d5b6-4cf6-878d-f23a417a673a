# Dependency Graph Module

This module provides classes for building, analyzing, and visualizing dependency graphs between resources in Sims 4 mods.

## Overview

The Dependency Graph module consists of three main classes:

1. **DependencyGraphBuilder**: Builds and manages a graph of dependencies between resources.
2. **DependencyGraphAnalyzer**: Extends the builder with analysis capabilities, such as finding circular dependencies and calculating metrics.
3. **DependencyGraphVisualizer**: Extends the analyzer with visualization capabilities, such as generating DOT and JSON representations.

## Usage

### Building a Dependency Graph

```typescript
import { DependencyGraphBuilder } from './dependencyGraph';

// Create a new instance
const graphBuilder = new DependencyGraphBuilder(databaseService);

// Build a graph for all resources
await graphBuilder.buildGraph({
  includeWeakDependencies: false,
  includeResourceMetadata: true
});

// Build a graph for a specific package
await graphBuilder.buildGraphForPackage(packageId, {
  maxDepth: 3,
  includeResourceMetadata: true
});

// Build a graph for a specific resource
await graphBuilder.buildGraphForResource(resourceId, {
  maxDepth: 2,
  includeResourceMetadata: true
});

// Get dependencies for a resource
const dependencies = graphBuilder.getDependencies(resourceId);

// Get dependents for a resource
const dependents = graphBuilder.getDependents(resourceId);
```

### Analyzing a Dependency Graph

```typescript
import { DependencyGraphAnalyzer } from './dependencyGraph';

// Create a new instance
const graphAnalyzer = new DependencyGraphAnalyzer(databaseService);

// Build the graph
await graphAnalyzer.buildGraph();

// Get the dependency chain for a resource
const chain = graphAnalyzer.getDependencyChain(resourceId);

// Get the dependency tree for a resource
const tree = graphAnalyzer.getDependencyTree(resourceId);

// Find circular dependencies
const circularDependencies = graphAnalyzer.findCircularDependencies();

// Find strongly connected components
const components = graphAnalyzer.findStronglyConnectedComponents();

// Calculate dependency metrics
const metrics = graphAnalyzer.calculateDependencyMetrics();
```

### Visualizing a Dependency Graph

```typescript
import { DependencyGraphVisualizer } from './dependencyGraph';

// Create a new instance
const graphVisualizer = new DependencyGraphVisualizer(databaseService);

// Build the graph
await graphVisualizer.buildGraph();

// Generate a DOT graph
const dotGraph = graphVisualizer.generateDotGraph({
  maxNodes: 50,
  includeMetadata: true,
  includeDependencyMetadata: true
});

// Generate a JSON graph
const jsonGraph = graphVisualizer.generateJsonGraph({
  maxNodes: 50,
  includeMetadata: true,
  includeDependencyMetadata: true
});

// Serialize the graph
const serializedGraph = graphVisualizer.serializeGraph();

// Deserialize a graph
graphVisualizer.deserializeGraph(serializedGraph);

// Export the graph to a file
const exportedGraph = graphVisualizer.exportGraph('dot', {
  maxNodes: 50,
  includeMetadata: true
});
```

## Integration with Conflict Detection

The Dependency Graph module can be integrated with the Conflict Detection system to identify conflicts that might not be obvious from direct TGI comparisons. For example, if two mods modify different resources, but those resources have dependencies on each other, there might be a conflict.

```typescript
import { DependencyGraphAnalyzer } from './dependencyGraph';
import { ConflictDetector } from '../conflict/conflictDetector';

// Create instances
const graphAnalyzer = new DependencyGraphAnalyzer(databaseService);
const conflictDetector = new ConflictDetector(databaseService);

// Build the graph
await graphAnalyzer.buildGraph();

// Find circular dependencies
const circularDependencies = graphAnalyzer.findCircularDependencies();

// Check for conflicts in circular dependencies
for (const cycle of circularDependencies) {
  for (let i = 0; i < cycle.length; i++) {
    for (let j = i + 1; j < cycle.length; j++) {
      const resource1 = await databaseService.resources.getResourceById(parseInt(cycle[i]));
      const resource2 = await databaseService.resources.getResourceById(parseInt(cycle[j]));
      
      if (resource1 && resource2) {
        const conflicts = await conflictDetector.detectConflicts(resource1, resource2);
        
        if (conflicts.length > 0) {
          console.log(`Found conflicts between resources in a circular dependency: ${resource1.id} and ${resource2.id}`);
        }
      }
    }
  }
}
```

## Performance Considerations

- The Dependency Graph module uses efficient data structures (Maps and Sets) and provides options to limit the depth of the graph.
- For large graphs, dependencies are processed in batches to avoid memory pressure.
- The module uses LRU caches for dependency chains and trees, which automatically evict the least recently used items when the cache reaches its capacity.
- The module supports incremental updates to the graph, allowing for efficient updates when resources change.

## Future Improvements

- Add support for filtering the graph based on resource types or other criteria.
- Implement more sophisticated visualization options, such as interactive visualizations.
- Add support for exporting the graph to other formats, such as GraphML or GEXF.
- Implement more advanced analysis algorithms, such as impact analysis and change propagation.
