import { createHash } from 'crypto';
import { Logger } from '../../../utils/logging/logger.js';
import { ResourceRepository } from '../../database/ResourceRepository.js';
import { ContentAddressableStorage } from '../../storage/ContentAddressableStorage.js';
import { DatabaseService } from '../../databaseService.js';
import { resourceTypeRegistry } from '../../../utils/resource/resourceTypeRegistry.js';
import { ResourceInfo, ResourceKey, ResourceMetadata } from '../../../types/resource/interfaces.js';

/**
 * Represents the result of processing a single resource entry.
 */
export interface ProcessedResourceEntry { // Export the interface
    resourceInfo: ResourceInfo;
    resourceId: number;
    resourceBuffer: Buffer; // Include buffer for metadata extraction
}

/**
 * Processes individual resources within a package.
 */
export class ResourceProcessor {
    private resourceRepository: ResourceRepository;
    private contentStorage: ContentAddressableStorage;
    private logger: Logger;

    /**
     * Create a new ResourceProcessor.
     * @param resourceRepository The resource repository instance.
     * @param logger The logger instance.
     * @param contentStorage Optional content-addressable storage instance.
     */
    constructor(
        resourceRepository: ResourceRepository,
        logger: Logger,
        contentStorage?: ContentAddressableStorage
    ) {
        this.resourceRepository = resourceRepository;
        this.logger = logger;

        // Use provided content storage or get from DatabaseService
        if (contentStorage) {
            this.contentStorage = contentStorage;
        } else {
            const databaseService = DatabaseService.getInstance();
            this.contentStorage = databaseService.getContentStorage();
        }
    }

    /**
     * Process a resource entry from a package.
     * Extracts buffer, identifies type, saves to database, and prepares for metadata extraction.
     * @param entry The resource entry from the S4TK package.
     * @param packageId The ID of the package in the database.
     * @param filePath The path to the package file.
     * @returns A Promise resolving to the processed ResourceInfo and resourceId, or null if processing fails.
     */
    public async processEntry(
        entry: any,
        packageId: number,
        filePath: string
    ): Promise<ProcessedResourceEntry | null> {
        try {
            const key = entry.key;
            const resource = entry.value;

            // Create resource key
            const resourceKey: ResourceKey = {
                type: key.type,
                group: key.group,
                instance: key.instance
            };

            // Extract resource buffer with enhanced extraction logic
            let resourceBuffer: Buffer;
            try {
                // Try multiple methods to extract the buffer
                if (typeof resource.extract === 'function') {
                    try {
                        resourceBuffer = resource.extract();
                        if (resourceBuffer && Buffer.isBuffer(resourceBuffer) && resourceBuffer.length > 0) {
                            this.logger.debug(`Extracted buffer using resource.extract(), size: ${resourceBuffer.length} bytes`);
                        } else {
                            this.logger.debug(`resource.extract() returned empty or invalid buffer, trying other methods`);
                            throw new Error('Invalid buffer from extract()');
                        }
                    } catch (extractError) {
                        this.logger.debug(`Error using resource.extract(): ${extractError}, trying other methods`);
                        throw extractError; // Propagate to try other methods
                    }
                } else if (typeof resource.toBuffer === 'function') {
                    try {
                        resourceBuffer = resource.toBuffer();
                        if (resourceBuffer && Buffer.isBuffer(resourceBuffer) && resourceBuffer.length > 0) {
                            this.logger.debug(`Extracted buffer using resource.toBuffer(), size: ${resourceBuffer.length} bytes`);
                        } else {
                            this.logger.debug(`resource.toBuffer() returned empty or invalid buffer, trying other methods`);
                            throw new Error('Invalid buffer from toBuffer()');
                        }
                    } catch (toBufferError) {
                        this.logger.debug(`Error using resource.toBuffer(): ${toBufferError}, trying other methods`);
                        throw toBufferError; // Propagate to try other methods
                    }
                } else if (resource.buffer && Buffer.isBuffer(resource.buffer)) {
                    resourceBuffer = resource.buffer;
                    if (resourceBuffer.length > 0) {
                        this.logger.debug(`Used resource.buffer directly, size: ${resourceBuffer.length} bytes`);
                    } else {
                        this.logger.debug(`resource.buffer is empty, trying other methods`);
                        throw new Error('Empty buffer from resource.buffer');
                    }
                } else if (resource.data && Buffer.isBuffer(resource.data)) {
                    resourceBuffer = resource.data;
                    if (resourceBuffer.length > 0) {
                        this.logger.debug(`Used resource.data directly, size: ${resourceBuffer.length} bytes`);
                    } else {
                        this.logger.debug(`resource.data is empty, trying other methods`);
                        throw new Error('Empty buffer from resource.data');
                    }
                } else if (resource._buffer && Buffer.isBuffer(resource._buffer)) {
                    // Some S4TK versions use _buffer internally
                    resourceBuffer = resource._buffer;
                    if (resourceBuffer.length > 0) {
                        this.logger.debug(`Used resource._buffer directly, size: ${resourceBuffer.length} bytes`);
                    } else {
                        this.logger.debug(`resource._buffer is empty, trying other methods`);
                        throw new Error('Empty buffer from resource._buffer');
                    }
                } else if (resource.getBuffer && typeof resource.getBuffer === 'function') {
                    // Try getBuffer method if available
                    try {
                        resourceBuffer = resource.getBuffer();
                        if (resourceBuffer && Buffer.isBuffer(resourceBuffer) && resourceBuffer.length > 0) {
                            this.logger.debug(`Extracted buffer using resource.getBuffer(), size: ${resourceBuffer.length} bytes`);
                        } else {
                            this.logger.debug(`resource.getBuffer() returned empty or invalid buffer, trying other methods`);
                            throw new Error('Invalid buffer from getBuffer()');
                        }
                    } catch (getBufferError) {
                        this.logger.debug(`Error using resource.getBuffer(): ${getBufferError}, trying other methods`);
                        throw getBufferError; // Propagate to try other methods
                    }
                } else if (resource.raw && Buffer.isBuffer(resource.raw)) {
                    // Some packages might expose raw data
                    resourceBuffer = resource.raw;
                    if (resourceBuffer.length > 0) {
                        this.logger.debug(`Used resource.raw directly, size: ${resourceBuffer.length} bytes`);
                    } else {
                        this.logger.debug(`resource.raw is empty, trying other methods`);
                        throw new Error('Empty buffer from resource.raw');
                    }
                } else {
                    // Last resort: try to access raw data through serialization
                    try {
                        if (typeof resource.serialize === 'function') {
                            resourceBuffer = resource.serialize();
                            if (resourceBuffer && Buffer.isBuffer(resourceBuffer) && resourceBuffer.length > 0) {
                                this.logger.debug(`Extracted buffer using resource.serialize(), size: ${resourceBuffer.length} bytes`);
                            } else {
                                throw new Error('Invalid buffer from serialize()');
                            }
                        } else {
                            throw new Error('No serialization method available');
                        }
                    } catch (serializeError) {
                        this.logger.warn(`Could not extract buffer for resource ${resourceKey.type.toString(16)}:${resourceKey.group.toString(16)}:${resourceKey.instance.toString(16)}: ${serializeError}`);
                        resourceBuffer = Buffer.from([]);
                    }
                }
            } catch (extractError) {
                // If all methods failed, try one more approach with S4TK-specific knowledge
                try {
                    // Some S4TK resources have a compressed property that needs to be decompressed
                    if (resource.compressed === true && typeof resource.decompress === 'function') {
                        const decompressedResource = resource.decompress();
                        if (decompressedResource && typeof decompressedResource.extract === 'function') {
                            resourceBuffer = decompressedResource.extract();
                            this.logger.debug(`Extracted buffer from decompressed resource, size: ${resourceBuffer.length} bytes`);
                        } else {
                            throw new Error('Failed to extract from decompressed resource');
                        }
                    } else {
                        throw new Error('Resource is not compressed or lacks decompress method');
                    }
                } catch (decompressError) {
                    this.logger.error(`All buffer extraction methods failed for resource ${resourceKey.type.toString(16)}:${resourceKey.group.toString(16)}:${resourceKey.instance.toString(16)}: ${extractError}, ${decompressError}`);
                    resourceBuffer = Buffer.from([]);
                }
            }

            // Get resource type info
            const resourceTypeInfo = resourceTypeRegistry.getInfo(resourceKey.type);

            // Identify resource type based on content signatures
            let resourceType = resourceTypeInfo.name;
            let contentSnippet = '[Empty Resource]';

            if (resourceBuffer.length > 0) {
                // Try to identify resource type based on content signatures

                // Check for STBL (String Table) - multiple formats
                if (resourceBuffer.length >= 4 && resourceBuffer.toString('utf8', 0, 4) === 'STBL') {
                    contentSnippet = 'STBL (String Table)';
                    resourceType = 'STRING_TABLE';
                    this.logger.debug(`Found string table with STBL magic number, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for STBL in big-endian format
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32BE(0) === 0x5354424C) { // 'STBL' in hex
                    contentSnippet = 'STBL (String Table, BE)';
                    resourceType = 'STRING_TABLE';
                    this.logger.debug(`Found string table with BE magic number, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for STBL in little-endian format
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x4C425453) { // 'LBTS' in hex (STBL reversed)
                    contentSnippet = 'STBL (String Table, LE)';
                    resourceType = 'STRING_TABLE';
                    this.logger.debug(`Found string table with LE magic number, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for XML documents
                else if (resourceBuffer.length >= 5 && (
                    resourceBuffer.toString('utf8', 0, 5) === '<?xml' ||
                    (resourceBuffer.length >= 9 && resourceBuffer.toString('utf8', 0, 9) === '<SimData>') ||
                    (resourceBuffer.length >= 12 && resourceBuffer.toString('utf8', 0, 12) === '<StringTable>') ||
                    (resourceBuffer.length >= 7 && resourceBuffer.toString('utf8', 0, 7) === '<Strings') ||
                    (resourceBuffer.length >= 5 && resourceBuffer.toString('utf8', 0, 5) === '<Text')
                )) {
                    // First 100 bytes for XML content snippet
                    contentSnippet = resourceBuffer.toString('utf8', 0, Math.min(100, resourceBuffer.length));

                    // Check for XML-based string tables
                    if ((resourceBuffer.length >= 12 && resourceBuffer.toString('utf8', 0, 12) === '<StringTable>') ||
                        resourceBuffer.toString('utf8', 0, Math.min(100, resourceBuffer.length)).includes('<StringTable') ||
                        (resourceBuffer.length >= 7 && resourceBuffer.toString('utf8', 0, 7) === '<Strings') ||
                        (resourceBuffer.length >= 5 && resourceBuffer.toString('utf8', 0, 5) === '<Text')) {

                        // Look for string table patterns in the first 1000 bytes
                        const xmlSample = resourceBuffer.toString('utf8', 0, Math.min(1000, resourceBuffer.length));
                        if (xmlSample.includes('<String key=') ||
                            xmlSample.includes('<String id=') ||
                            xmlSample.includes('<String name=')) {
                            contentSnippet = 'XML String Table';
                            resourceType = 'STRING_TABLE';
                            this.logger.debug(`Found XML string table, instance ${resourceKey.instance.toString(16)}`);
                        }
                    }
                }
                // Check for Python scripts
                else if (resourceBuffer.length >= 6 && (
                    resourceBuffer.toString('utf8', 0, 6) === 'import' ||
                    (resourceBuffer.length >= 4 && resourceBuffer.toString('utf8', 0, 4) === 'from') ||
                    (resourceBuffer.length >= 5 && resourceBuffer.toString('utf8', 0, 5) === 'class') ||
                    (resourceBuffer.length >= 3 && resourceBuffer.toString('utf8', 0, 3) === 'def')
                )) {
                    contentSnippet = 'Python Script';
                    resourceType = 'PYTHON_SCRIPT';
                    this.logger.debug(`Found Python script, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for DDS textures
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x20534444) { // 'DDS ' in little-endian
                    contentSnippet = 'DDS Texture';
                    resourceType = 'DDS_IMAGE';
                    this.logger.debug(`Found DDS texture, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for PNG images
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32BE(0) === 0x89504E47) { // PNG signature
                    contentSnippet = 'PNG Image';
                    resourceType = 'PNG_IMAGE';
                    this.logger.debug(`Found PNG image, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for JPEG images
                else if (resourceBuffer.length >= 2 && resourceBuffer.readUInt16BE(0) === 0xFFD8) { // JPEG signature
                    contentSnippet = 'JPEG Image';
                    resourceType = 'JPEG_IMAGE';
                    this.logger.debug(`Found JPEG image, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for OBJD (Object Definition)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x4F424A44) { // 'OBJD' in little-endian
                    contentSnippet = 'Object Definition';
                    resourceType = 'OBJECT_DEFINITION';
                    this.logger.debug(`Found Object Definition, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for CASP (CAS Part)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x43415350) { // 'CASP' in little-endian
                    contentSnippet = 'CAS Part';
                    resourceType = 'CASPART';
                    this.logger.debug(`Found CAS Part, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for GEOM (Geometry)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x47454F4D) { // 'GEOM' in little-endian
                    contentSnippet = 'Geometry';
                    resourceType = 'GEOMETRY';
                    this.logger.debug(`Found Geometry, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for MODL (Model)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x4D4F444C) { // 'MODL' in little-endian
                    contentSnippet = 'Model';
                    resourceType = 'MODEL';
                    this.logger.debug(`Found Model, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for MTST (Material Set)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x4D545354) { // 'MTST' in little-endian
                    contentSnippet = 'Material Set';
                    resourceType = 'MATERIAL_SET';
                    this.logger.debug(`Found Material Set, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for ANIM (Animation)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x414E494D) { // 'ANIM' in little-endian
                    contentSnippet = 'Animation';
                    resourceType = 'ANIMATION';
                    this.logger.debug(`Found Animation, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for AUEV (Audio Event)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x41554556) { // 'AUEV' in little-endian
                    contentSnippet = 'Audio Event';
                    resourceType = 'AUDIO_EVENT';
                    this.logger.debug(`Found Audio Event, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for JAZZ (Jazz File)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x4A415A5A) { // 'JAZZ' in little-endian
                    contentSnippet = 'Jazz File';
                    resourceType = 'JAZZ_FILE';
                    this.logger.debug(`Found Jazz File, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for LAYO (Layout)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x4C41594F) { // 'LAYO' in little-endian
                    contentSnippet = 'Layout';
                    resourceType = 'LAYOUT';
                    this.logger.debug(`Found Layout, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for SLOT (Slot)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x534C4F54) { // 'SLOT' in little-endian
                    contentSnippet = 'Slot';
                    resourceType = 'SLOT';
                    this.logger.debug(`Found Slot, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for VPXY (Visual Proxy)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x56505859) { // 'VPXY' in little-endian
                    contentSnippet = 'Visual Proxy';
                    resourceType = 'VISUAL_PROXY';
                    this.logger.debug(`Found Visual Proxy, instance ${resourceKey.instance.toString(16)}`);
                }
                // Check for WBNK (Wave Bank)
                else if (resourceBuffer.length >= 4 && resourceBuffer.readUInt32LE(0) === 0x57424E4B) { // 'WBNK' in little-endian
                    contentSnippet = 'Wave Bank';
                    resourceType = 'WAVE_BANK';
                    this.logger.debug(`Found Wave Bank, instance ${resourceKey.instance.toString(16)}`);
                }
                else {
                    // For binary data, show a hex dump of the first few bytes
                    contentSnippet = resourceBuffer.slice(0, 16).toString('hex').replace(/(.{2})/g, '$1 ').trim();
                }
            }

            // Store content in content-addressable storage if buffer is not empty
            let contentPath = null;
            let contentSize = null;
            let signatureHash = null;

            if (resourceBuffer.length > 0) {
                try {
                    // Calculate content hash for signature
                    signatureHash = createHash('sha256').update(resourceBuffer).digest('hex');

                    // Determine if we should store the full content or just a snippet
                    // For large resources, we'll store them in content-addressable storage
                    // For small resources, we'll keep them in the database
                    const isLargeResource = resourceBuffer.length > 10 * 1024; // 10KB threshold

                    if (isLargeResource) {
                        // Store content in content-addressable storage
                        const contentType = this.getContentType(resourceType);
                        const contentMetadata = await this.contentStorage.storeContent(
                            resourceBuffer,
                            contentType
                        );

                        contentPath = contentMetadata.contentHash;
                        contentSize = contentMetadata.size;

                        this.logger.debug(`Stored large content (${contentSize} bytes) in content-addressable storage: ${contentPath}`);
                    } else {
                        // For small resources, we'll just keep the hash and size
                        // The actual content will be stored in the database
                        contentPath = signatureHash;
                        contentSize = resourceBuffer.length;
                        this.logger.debug(`Small resource (${contentSize} bytes) will be stored directly in database`);
                    }
                } catch (storageError: any) {
                    this.logger.error(`Error storing content in content-addressable storage: ${storageError.message || storageError}`);
                    // Continue without content-addressable storage
                    // Set fallback values
                    contentPath = signatureHash;
                    contentSize = resourceBuffer.length;
                }
            }

            // Create resource metadata with content-addressable storage information
            const resourceMetadata: ResourceMetadata = {
                name: resourceTypeInfo.name,
                path: filePath,
                size: resourceBuffer.length,
                hash: createHash('md5').update(resourceBuffer).digest('hex'),
                timestamp: Date.now(),
                resourceType: resourceTypeInfo.name,
                resourceTypeHex: `0x${resourceKey.type.toString(16).padStart(8, '0')}`,
                resourceDescription: resourceTypeInfo.description,
                isOfficialType: resourceTypeInfo.category !== 'UNKNOWN',
                extractorUsed: 'none', // Placeholder, will be updated after metadata extraction
                // Add content-addressable storage information
                contentPath: contentPath,
                contentSize: contentSize,
                signatureHash: signatureHash
            };

            // Save resource to database using ResourceRepository
            const resourceId = this.resourceRepository.saveResource({
                packageId,
                type: resourceKey.type,
                group: resourceKey.group,
                instance: resourceKey.instance,
                hash: createHash('md5').update(resourceBuffer).digest('hex'),
                size: resourceBuffer.length,
                offset: entry.offset || 0,
                contentSnippet: contentSnippet,
                resourceType: resourceType,
                metadata: resourceMetadata
            });

            // Prepare ResourceInfo object
            const resourceInfo: ResourceInfo = {
                key: resourceKey,
                metadata: resourceMetadata
            };

            // Return resource info, resource ID, and buffer
            return {
                resourceInfo,
                resourceId,
                resourceBuffer
            };
        } catch (error: any) {
            const keyStr = entry.key ? `${entry.key.type.toString(16)}:${entry.key.group.toString(16)}:${entry.key.instance.toString(16)}` : 'unknown';
            this.logger.error(`Error processing resource ${keyStr}: ${error.message || error}`);
            return null; // Return null if processing fails
        }
    }

    /**
     * Get the content type for a resource type
     * @param resourceType The resource type
     * @returns The content type
     * @private
     */
    private getContentType(resourceType: string): string {
        // Map resource types to content types
        switch (resourceType) {
            case 'STRING_TABLE':
                return 'application/x-sims4-stringtable';
            case 'TUNING_XML':
            case 'XML':
                return 'application/xml';
            case 'SIMDATA':
                return 'application/x-sims4-simdata';
            case 'PYTHON_SCRIPT':
                return 'text/x-python';
            case 'DDS_IMAGE':
                return 'image/vnd.ms-dds';
            case 'PNG_IMAGE':
                return 'image/png';
            case 'JPEG_IMAGE':
                return 'image/jpeg';
            case 'OBJECT_DEFINITION':
                return 'application/x-sims4-objd';
            case 'CASPART':
                return 'application/x-sims4-caspart';
            case 'GEOMETRY':
                return 'application/x-sims4-geometry';
            case 'MODEL':
                return 'application/x-sims4-model';
            case 'MATERIAL_SET':
                return 'application/x-sims4-material';
            case 'ANIMATION':
                return 'application/x-sims4-animation';
            case 'AUDIO_EVENT':
                return 'application/x-sims4-audio';
            case 'WAVE_BANK':
                return 'application/x-sims4-wavebank';
            case 'JAZZ_FILE':
                return 'application/x-sims4-jazz';
            case 'LAYOUT':
                return 'application/x-sims4-layout';
            case 'SLOT':
                return 'application/x-sims4-slot';
            case 'VISUAL_PROXY':
                return 'application/x-sims4-visualproxy';
            default:
                return 'application/octet-stream';
        }
    }
}