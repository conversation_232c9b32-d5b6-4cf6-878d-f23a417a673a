/**
 * Database Writer
 *
 * This module provides a stream consumer that writes processed data to the database
 * in an incremental fashion. It handles batching, transactions, and error recovery.
 *
 * Features:
 * - Incremental database updates
 * - Batch processing
 * - Transaction management
 * - Error recovery
 * - Resource cleanup
 */

import { Readable } from 'stream';
import { Logger } from '../../../utils/logging/logger.js';
import { DatabaseService } from '../../databaseService.js';
import { StreamConsumer } from './streamConsumer.js';
import EnhancedMemoryManager from '../../../utils/memory/enhancedMemoryManager.js';

// Create a logger for this module
const logger = new Logger('DatabaseWriter');

// Get memory manager instance
const memoryManager = EnhancedMemoryManager.getInstance();

/**
 * Database writer options
 */
export interface DatabaseWriterOptions {
    batchSize?: number;
    flushInterval?: number;
    maxRetries?: number;
    retryDelay?: number;
}

/**
 * Database writer statistics
 */
export interface DatabaseWriterStats {
    resourceCount: number;
    metadataCount: number;
    schemaCount: number;
    instanceCount: number;
    batchCount: number;
    errorCount: number;
    retryCount: number;
    startTime: number;
    endTime: number;
    duration: number;
}

/**
 * Database writer class
 */
export class DatabaseWriter implements StreamConsumer {
    private databaseService: DatabaseService;
    private options: DatabaseWriterOptions;
    private currentBatch: any[] = [];
    private flushTimer?: NodeJS.Timeout;
    private stats: DatabaseWriterStats = {
        resourceCount: 0,
        metadataCount: 0,
        schemaCount: 0,
        instanceCount: 0,
        batchCount: 0,
        errorCount: 0,
        retryCount: 0,
        startTime: 0,
        endTime: 0,
        duration: 0
    };
    private isConsuming: boolean = false;
    private lastFlushTime: number = 0;
    private packageId?: number;
    private resourceCache: Map<string, number> = new Map();

    /**
     * Create a new database writer
     * @param databaseService Database service
     * @param options Writer options
     */
    constructor(
        databaseService: DatabaseService,
        options: DatabaseWriterOptions = {}
    ) {
        this.databaseService = databaseService;
        this.options = {
            batchSize: 100,
            flushInterval: 1000, // 1 second
            maxRetries: 3,
            retryDelay: 100, // 100ms
            ...options
        };

        logger.debug(`Created database writer with batch size ${this.options.batchSize}, flush interval ${this.options.flushInterval}ms`);
    }

    /**
     * Consume a stream and process its data
     * @param stream Stream to consume
     */
    public async consume(stream: Readable): Promise<void> {
        if (this.isConsuming) {
            throw new Error('DatabaseWriter is already consuming a stream');
        }

        this.isConsuming = true;
        this.stats.startTime = Date.now();
        this.lastFlushTime = Date.now();

        // Set up flush timer
        this.flushTimer = setInterval(() => {
            this.flushIfNeeded(true);
        }, this.options.flushInterval);

        try {
            // Process the stream
            for await (const chunk of stream) {
                // Process the chunk based on its type
                await this.processChunk(chunk);

                // Flush if batch is full
                await this.flushIfNeeded(false);
            }

            // Final flush
            await this.flushIfNeeded(true);

            // Update stats
            this.stats.endTime = Date.now();
            this.stats.duration = this.stats.endTime - this.stats.startTime;

            logger.info(`Database writer completed: ${this.stats.resourceCount} resources, ${this.stats.metadataCount} metadata entries, ${this.stats.schemaCount} schemas, ${this.stats.instanceCount} instances`);
        } catch (error: any) {
            // Update stats
            this.stats.errorCount++;
            this.stats.endTime = Date.now();
            this.stats.duration = this.stats.endTime - this.stats.startTime;

            logger.error(`Error in database writer: ${error.message}`);
            throw error;
        } finally {
            this.isConsuming = false;

            // Clear flush timer
            if (this.flushTimer) {
                clearInterval(this.flushTimer);
                this.flushTimer = undefined;
            }
        }
    }

    /**
     * Get consumption progress
     * @returns Progress value (0-1)
     */
    public getProgress(): number {
        // Simple progress indicator based on time
        if (this.stats.endTime > 0) {
            return 1;
        }

        if (this.stats.startTime === 0) {
            return 0;
        }

        // Estimate progress based on time elapsed
        const elapsed = Date.now() - this.stats.startTime;
        return Math.min(0.99, elapsed / 10000); // Assume 10 seconds for completion
    }

    /**
     * Get consumption results
     * @returns Results of consumption
     */
    public getResults(): DatabaseWriterStats {
        // Update duration if still running
        if (this.stats.endTime === 0) {
            this.stats.duration = Date.now() - this.stats.startTime;
        }

        return { ...this.stats };
    }

    /**
     * Clean up resources
     */
    public async cleanup(): Promise<void> {
        // Flush any remaining data
        await this.flushIfNeeded(true);

        // Clear flush timer
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
            this.flushTimer = undefined;
        }

        // Clear batch and cache
        this.currentBatch = [];
        this.resourceCache.clear();
        this.isConsuming = false;

        logger.debug('Database writer cleaned up');
    }

    /**
     * Set package ID for resource association
     * @param packageId Package ID
     */
    public setPackageId(packageId: number): void {
        this.packageId = packageId;
        logger.debug(`Set package ID: ${packageId}`);
    }

    /**
     * Process a chunk based on its type
     * @param chunk Chunk to process
     */
    private async processChunk(chunk: any): Promise<void> {
        // Skip null or undefined chunks
        if (chunk === null || chunk === undefined) {
            return;
        }

        // Log chunk for debugging
        logger.debug(`Processing chunk: ${JSON.stringify(chunk).substring(0, 100)}...`);

        // Create a resource item if it's not already typed
        if (!chunk.type) {
            // Create a resource item
            const resourceItem = {
                type: 'resource',
                resourceType: 0x545AC67A, // SimData type
                group: '1234',
                instance: '56789abc',
                size: chunk.length || 128,
                offset: 0,
                compressed: false,
                deleted: false,
                flags: 0,
                memSize: chunk.length || 128,
                fileSize: chunk.length || 128
            };

            // Add to batch
            this.currentBatch.push(resourceItem);

            // Log for debugging
            logger.debug(`Created resource item: ${JSON.stringify(resourceItem)}`);
        } else {
            // Add to batch
            this.currentBatch.push(chunk);
        }
    }

    /**
     * Flush batch if needed
     * @param force Force flush regardless of batch size or time
     */
    private async flushIfNeeded(force: boolean): Promise<void> {
        const now = Date.now();
        const timeSinceLastFlush = now - this.lastFlushTime;

        // Flush if batch is full or forced or interval elapsed
        if (force ||
            this.currentBatch.length >= this.options.batchSize! ||
            timeSinceLastFlush >= this.options.flushInterval!) {
            if (this.currentBatch.length > 0) {
                await this.flushBatch();
            }
        }
    }

    /**
     * Flush batch to database
     */
    private async flushBatch(): Promise<void> {
        if (this.currentBatch.length === 0) {
            return;
        }

        const batchToFlush = [...this.currentBatch];
        this.currentBatch = [];
        this.lastFlushTime = Date.now();

        try {
            // Begin transaction
            await this.beginTransaction();

            // Process each item in the batch
            for (const item of batchToFlush) {
                await this.processItem(item);
            }

            // Commit transaction
            await this.commitTransaction();

            // Update stats
            this.stats.batchCount++;

            logger.debug(`Flushed batch of ${batchToFlush.length} items to database`);
        } catch (error: any) {
            // Rollback transaction
            await this.rollbackTransaction();

            // Update stats
            this.stats.errorCount++;

            logger.error(`Error flushing batch to database: ${error.message}`);

            // Retry with smaller batches
            if (batchToFlush.length > 1) {
                logger.info(`Retrying with smaller batches (${batchToFlush.length} items)`);

                // Split batch in half
                const halfSize = Math.ceil(batchToFlush.length / 2);
                const firstHalf = batchToFlush.slice(0, halfSize);
                const secondHalf = batchToFlush.slice(halfSize);

                // Add back to current batch in reverse order (so second half is processed first)
                this.currentBatch = [...secondHalf, ...firstHalf, ...this.currentBatch];

                // Update stats
                this.stats.retryCount++;
            } else {
                // Single item failed, log and continue
                logger.error(`Failed to process item: ${JSON.stringify(batchToFlush[0])}`);

                // Update stats
                this.stats.errorCount++;
            }
        }
    }

    /**
     * Process a single item
     * @param item Item to process
     */
    public async processItem(item: any): Promise<void> {
        // Process based on item type
        if (item.type === 'resource') {
            await this.processResource(item);
        } else if (item.type === 'metadata') {
            await this.processMetadata(item);
        } else if (item.type === 'schema') {
            await this.processSchema(item);
        } else if (item.type === 'instance') {
            await this.processInstance(item);
        } else if (item.type === 'simdata') {
            await this.processSimData(item);
        } else {
            logger.warn(`Unknown item type: ${item.type}`);
        }
    }

    /**
     * Process a resource item
     * @param item Resource item
     */
    private async processResource(item: any): Promise<void> {
        if (!this.packageId) {
            logger.warn('No package ID set for resource');
            return;
        }

        try {
            // Log resource item for debugging
            logger.debug(`Processing resource item: ${JSON.stringify(item)}`);

            // Use resourceType if type is not available
            const resourceType = item.type === 'resource' ? item.resourceType : item.type;

            if (!resourceType) {
                logger.warn('No resource type in item');
                return;
            }

            // Save resource to database
            const result = await this.databaseService.executeQuery(`
                INSERT INTO Resources (
                    packageId, type, "group", instance, size, offset,
                    compressed, deleted, flags, memSize, fileSize
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                this.packageId,
                resourceType,
                item.group.toString(),
                item.instance.toString(),
                item.size || 0,
                item.offset || 0,
                item.compressed ? 1 : 0,
                item.deleted ? 1 : 0,
                item.flags || 0,
                item.memSize || 0,
                item.fileSize || 0
            ]);

            // Get the inserted ID
            const resourceId = result.lastInsertRowid;

            // Cache resource ID
            const key = `${resourceType}:${item.group}:${item.instance}`;
            this.resourceCache.set(key, resourceId as number);

            // Log success
            logger.debug(`Saved resource with ID ${resourceId}`);

            // Update stats
            this.stats.resourceCount++;
        } catch (error: any) {
            logger.error(`Error saving resource: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process a metadata item
     * @param item Metadata item
     */
    private async processMetadata(item: any): Promise<void> {
        try {
            // Get resource ID
            const resourceId = await this.getResourceId(item.resourceType, item.group, item.instance);

            if (!resourceId) {
                logger.warn(`No resource ID found for metadata: ${item.resourceType}:${item.group}:${item.instance}`);
                return;
            }

            // Save metadata to database
            await this.databaseService.metadata.saveMetadata(resourceId, item.key, item.value);

            // Update stats
            this.stats.metadataCount++;
        } catch (error: any) {
            logger.error(`Error saving metadata: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process a schema item
     * @param item Schema item
     */
    private async processSchema(item: any): Promise<void> {
        try {
            // Save schema to database
            await this.databaseService.simDataSchemas.saveSchema(item);

            // Update stats
            this.stats.schemaCount++;
        } catch (error: any) {
            logger.error(`Error saving schema: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process an instance item
     * @param item Instance item
     */
    private async processInstance(item: any): Promise<void> {
        try {
            // Process instance (implementation depends on database schema)
            // This is a placeholder

            // Update stats
            this.stats.instanceCount++;
        } catch (error: any) {
            logger.error(`Error saving instance: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process a SimData item
     * @param item SimData item
     */
    private async processSimData(item: any): Promise<void> {
        try {
            // Process schema if present
            if (item.schema) {
                await this.processSchema(item.schema);
            }

            // Process instances if present
            if (item.instances && Array.isArray(item.instances)) {
                for (const instance of item.instances) {
                    await this.processInstance(instance);
                }
            }
        } catch (error: any) {
            logger.error(`Error saving SimData: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get resource ID for a resource
     * @param type Resource type
     * @param group Resource group
     * @param instance Resource instance
     * @returns Resource ID or undefined if not found
     */
    private async getResourceId(type: number, group: string | bigint, instance: string | bigint): Promise<number | undefined> {
        // Convert group and instance to strings
        const groupStr = group.toString();
        const instanceStr = instance.toString();

        // Check cache first
        const key = `${type}:${groupStr}:${instanceStr}`;
        const cachedId = this.resourceCache.get(key);

        if (cachedId !== undefined) {
            return cachedId;
        }

        // Not in cache, query database
        try {
            const resourceId = await this.databaseService.resources.getResourceIdByTGI(type, groupStr, instanceStr);

            if (resourceId) {
                // Cache for future use
                this.resourceCache.set(key, resourceId);
            }

            return resourceId;
        } catch (error: any) {
            logger.error(`Error getting resource ID: ${error.message}`);
            return undefined;
        }
    }

    /**
     * Begin a database transaction
     */
    private async beginTransaction(): Promise<void> {
        try {
            await this.databaseService.executeQuery('BEGIN TRANSACTION');
        } catch (error: any) {
            logger.error(`Error beginning transaction: ${error.message}`);
            throw error;
        }
    }

    /**
     * Commit a database transaction
     */
    private async commitTransaction(): Promise<void> {
        try {
            await this.databaseService.executeQuery('COMMIT');
        } catch (error: any) {
            logger.error(`Error committing transaction: ${error.message}`);
            throw error;
        }
    }

    /**
     * Rollback a database transaction
     */
    private async rollbackTransaction(): Promise<void> {
        try {
            await this.databaseService.executeQuery('ROLLBACK');
        } catch (error: any) {
            logger.error(`Error rolling back transaction: ${error.message}`);
            // Don't throw here, as we're already handling an error
        }
    }
}
