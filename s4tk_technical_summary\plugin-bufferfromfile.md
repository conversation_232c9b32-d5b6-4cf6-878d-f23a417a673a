# S4TK Package: @s4tk/plugin-bufferfromfile

Based on documentation found in `docs/technical/plugin-bufferfromfile/`.

## Overview

This package acts as a plugin wrapper for the native Node.js addon `bufferfromfile`. When installed and registered with `@s4tk/models`, it enables S4TK to read resource data directly from package files on disk without loading the entire file into memory first.

This is intended to provide significant performance improvements for operations like streaming, indexing, or fetching specific resources from large package files.

## Installation

```sh
npm i @s4tk/plugin-bufferfromfile
```

**Note:** This package contains a native addon that requires compilation via `node-gyp`. Compatibility issues with specific Node.js or Electron versions can occur (as experienced previously in this project with Electron 35).

## Usage

The plugin must be registered with `@s4tk/models` once at application startup:

```typescript
// ESM / TS
import { registerPlugin } from "@s4tk/models/plugins";
import BufferFromFilePlugin from "@s4tk/plugin-bufferfromfile";
registerPlugin(BufferFromFilePlugin);

// CJS
const { registerPlugin } = require("@s4tk/models/plugins");
const BufferFromFilePlugin = require("@s4tk/plugin-bufferfromfile");
registerPlugin(BufferFromFilePlugin);
```

Once registered, methods like `Package.streamResources()`, `Package.indexResources()`, and `Package.fetchResources()` in `@s4tk/models` become functional.

## API

The package exports a plugin object:

*   `BufferFromFilePlugin`:
    *   `name: string`: Identifier ("BufferFromFile").
    *   `data: any`: The interface to the underlying native `bufferfromfile` module's functions (used internally by `@s4tk/models`).

## Status

Pre-release and requires native compilation, making it potentially fragile across different environments or Electron versions.