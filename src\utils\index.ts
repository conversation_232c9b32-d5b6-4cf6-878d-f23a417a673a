﻿﻿/**
 * Unified Utilities Module
 * This module exports all utility functions in an organized manner.
 */

// Core utilities
// export * from './cache.js'; // Removed - File does not exist
// export * from './error.js'; // Removed - File does not exist
// Explicitly export from filter, excluding ConflictInfo, use 'export type' for interfaces
export { detectConflicts, analyzeConflicts } from './filter/index.js'; // Export functions normally, assuming index.js
export type { ConflictDetectionOptions } from './filter/index.js'; // Export type separately, assuming index.js
export * from './file/index.js'; // Assuming index.js
export * from './validation/index.js'; // Assuming index.js
// export * from './color.js'; // Removed - File does not exist
// export * from './material/index.js'; // Removed - Directory/File no longer exists
export * from './hash/index.js'; // Assuming index.js
export * from './version/index.js'; // Assuming index.js
export * from './formatting/index.js'; // Re-export from formatting subdirectory
export {
  // From conversionUtils.js
  createS4TKCompatibleResource,
  createExtendedResource,
  createResource,
  createResourceArray,
  createExtendedResourceArray,
  // From helpers.js (excluding conflicts)
  ResourceTypeCategoryMap,
  createResourceKey,
  convertS4TKResourceKey,
  convertToS4TKResourceKey,
  getResourceTypeCategory,
  convertToResourceType,
  convertToString,
  getResourceTypeName,
  generateResourceId,
  createResourceKeyFromS4TKWithName,
  generateResourceIdFromMeta,
  // From resource-utils.js
  getResourceTypeDistribution,
  // From resourceTypeMapping.js
  ResourceTypeMapping,
  getExtractorForResourceType,
  getResourceTypeInfo,
  initializeResourceTypeMapping,
  // From resourceTypeRegistry.js
  resourceTypeRegistry,
} from './resource/index.js'; // Re-export from resource subdirectory
export type {
  // From resourceTypeMapping.js
  ResourceTypeInfo,
  // From resourceTypeRegistry.js
  ResourceTypeRegistryInfo, // Exported as alias
} from './resource/index.js'; // Re-export types from resource subdirectory
export * from './similarity/index.js'; // Re-export from similarity subdirectory

// Logging utilities
export * from './logging/logger.js';
export * from './logging/analysisLogger.js';
export * from './logging/loggerConfig.js';

// Type utilities (Removed re-export from empty ./types)

// Validation utilities
export * from './validation/packageValidation.js';
export * from './validation/resourceValidation.js';
export * from './validation/typeValidation.js';

// Removed deprecated default export pattern
