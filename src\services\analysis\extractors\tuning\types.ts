/**
 * Types and interfaces for tuning extraction
 */

import { ResourceKey as AppResourceKey, ResourceMetadata } from '../../../../types/resource/interfaces.js';
import { DependencyInfo } from '../../../../types/database.js';
import { XmlNode, XmlElementNode } from '@s4tk/xml-dom';

/**
 * Tuning parser result
 */
export interface TuningParseResult {
    /** The parsed tuning resource */
    tuningResource: any | null;
    /** Whether the resource was parsed with S4TK */
    parsedWithS4TK: boolean;
    /** Content snippet for the resource */
    contentSnippet?: string;
    /** Error message if parsing failed */
    error?: string;
}

/**
 * Tuning semantic information
 */
export interface TuningSemanticInfo {
    /** The type of tuning (e.g., "Trait", "Buff", "Interaction") */
    tuningType: string;
    /** The category of tuning (e.g., "Sim Attributes", "Interactions", "Build/Buy") */
    tuningCategory: string;
    /** A description of the tuning */
    tuningDescription: string;
    /** Tags associated with the tuning */
    tuningTags: string[];
}

/**
 * Tuning name extraction result
 */
export interface TuningNameResult {
    /** The extracted tuning name */
    tuningName: string | null;
    /** Whether the name was extracted successfully */
    success: boolean;
    /** The method used to extract the name */
    extractionMethod?: string;
    /** Error message if extraction failed */
    error?: string;
}

/**
 * Tuning dependency extraction result
 */
export interface TuningDependencyResult {
    /** The extracted dependencies */
    dependencies: DependencyInfo[];
    /** Whether dependencies were extracted successfully */
    success: boolean;
    /** The method used to extract dependencies */
    extractionMethod?: string;
    /** Error message if extraction failed */
    error?: string;
}

/**
 * Tuning numeric values extraction result
 */
export interface TuningNumericResult {
    /** The extracted numeric values */
    values: number[];
    /** The extracted numeric ranges */
    ranges: { min: number; max: number }[];
    /** Whether the tuning has negative values */
    hasNegatives: boolean;
    /** Whether the tuning has decimal values */
    hasDecimals: boolean;
}

/**
 * Tuning extraction options
 */
export interface TuningExtractionOptions {
    /** Whether to extract dependencies */
    extractDependencies?: boolean;
    /** Whether to save to the database */
    saveToDB?: boolean;
    /** Whether to generate a content snippet */
    generateContentSnippet?: boolean;
    /** Whether to use aggressive parsing */
    aggressiveParsing?: boolean;
}

/**
 * Tuning extraction context
 */
export interface TuningExtractionContext {
    /** The resource key */
    key: AppResourceKey;
    /** The resource ID */
    resourceId: number;
    /** The operation being performed */
    operation: string;
    /** Additional information */
    additionalInfo?: Record<string, any>;
}

/**
 * Extended ResourceMetadata with tuning-specific fields
 */
export interface TuningResourceMetadata extends Partial<ResourceMetadata> {
    /** The tuning name */
    tuningName?: string;
    /** The tuning type */
    tuningType?: string;
    /** The tuning category */
    tuningCategory?: string;
    /** The tuning description */
    tuningDescription?: string;
    /** Tags associated with the tuning */
    tuningTags?: string;
    /** The number of dependencies */
    tuningDependencyCount?: number;
    /** Numeric values in the tuning */
    tuningNumericValues?: number[];
    /** Numeric ranges in the tuning */
    tuningRanges?: { min: number; max: number }[];
    /** Whether the tuning has negative values */
    hasNegativeValues?: boolean;
    /** Whether the tuning has decimal values */
    hasDecimalValues?: boolean;
}

/**
 * Node context for dependency extraction
 */
export interface NodeContext {
    /** The path of the node in the XML tree */
    path: string;
    /** The tag of the parent node */
    parentTag: string;
    /** The tag of the node */
    tag: string;
}

/**
 * XML parser options
 */
export interface XmlParserOptions {
    /** Whether to use arrays for elements that appear multiple times */
    explicitArray?: boolean;
    /** Whether to ignore attributes */
    ignoreAttrs?: boolean;
    /** Whether to enforce strict XML parsing */
    strict?: boolean;
    /** Whether to normalize tag names */
    normalizeTags?: boolean;
    /** Function to process attribute names */
    attrNameProcessors?: ((name: string) => string)[];
    /** Function to process tag names */
    tagNameProcessors?: ((name: string) => string)[];
    /** Function to validate XML */
    validator?: () => boolean;
}
