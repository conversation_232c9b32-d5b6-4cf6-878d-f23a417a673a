/**
 * Hardware Detector
 * 
 * This module provides hardware detection capabilities for the application.
 * It helps optimize performance based on the user's hardware capabilities.
 * 
 * Features:
 * - CPU detection
 * - Memory detection
 * - Disk speed detection
 * - Hardware categorization
 * - Performance recommendations
 */

import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { EventEmitter } from 'events';
import { Logger } from '../logging/logger.js';
import { AppError, ErrorCategory, ErrorCode, ErrorSeverity } from '../error/errorTypes.js';
import { EnhancedErrorHandler } from '../error/enhancedErrorHandler.js';

// Promisify fs functions
const fsOpen = promisify(fs.open);
const fsClose = promisify(fs.close);
const fsWrite = promisify(fs.write);
const fsRead = promisify(fs.read);
const fsUnlink = promisify(fs.unlink);

// Create a logger for this module
const logger = new Logger('HardwareDetector');

// Get error handler instance
const errorHandler = EnhancedErrorHandler.getInstance();

/**
 * Hardware category
 */
export enum HardwareCategory {
    LOW_END = 'low-end',
    MID_RANGE = 'mid-range',
    HIGH_END = 'high-end'
}

/**
 * CPU information
 */
export interface CpuInfo {
    cores: number;
    logicalCores: number;
    speed: number;
    model: string;
    architecture: string;
}

/**
 * Memory information
 */
export interface MemoryInfo {
    total: number;
    free: number;
    available: number;
    percentAvailable: number;
}

/**
 * Disk information
 */
export interface DiskInfo {
    readSpeed: number;
    writeSpeed: number;
    type: 'unknown' | 'hdd' | 'ssd' | 'nvme';
}

/**
 * Hardware information
 */
export interface HardwareInfo {
    cpu: CpuInfo;
    memory: MemoryInfo;
    disk: DiskInfo;
    category: HardwareCategory;
    timestamp: number;
}

/**
 * Performance recommendations
 */
export interface PerformanceRecommendations {
    chunkSize: number;
    batchSize: number;
    concurrency: number;
    bufferSize: number;
    cacheSize: number;
    timeout: number;
}

/**
 * Hardware detector options
 */
export interface HardwareDetectorOptions {
    diskSpeedTestFile?: string;
    diskSpeedTestSize?: number;
    diskSpeedTestIterations?: number;
    refreshInterval?: number;
    enableAutoRefresh?: boolean;
}

/**
 * Hardware detector class
 */
export class HardwareDetector extends EventEmitter {
    private static instance: HardwareDetector;
    private hardwareInfo?: HardwareInfo;
    private recommendations?: PerformanceRecommendations;
    private refreshTimer?: NodeJS.Timeout;
    private options: HardwareDetectorOptions;
    private detecting: boolean = false;
    
    /**
     * Create a new hardware detector
     * @param options Hardware detector options
     */
    private constructor(options: HardwareDetectorOptions = {}) {
        super();
        
        this.options = {
            diskSpeedTestFile: path.join(os.tmpdir(), 'sims4-mod-manager-disk-speed-test.dat'),
            diskSpeedTestSize: 100 * 1024 * 1024, // 100MB
            diskSpeedTestIterations: 3,
            refreshInterval: 60 * 60 * 1000, // 1 hour
            enableAutoRefresh: true,
            ...options
        };
        
        logger.info('Hardware detector initialized');
    }
    
    /**
     * Get the hardware detector instance
     * @param options Hardware detector options
     * @returns Hardware detector instance
     */
    public static getInstance(options?: HardwareDetectorOptions): HardwareDetector {
        if (!HardwareDetector.instance) {
            HardwareDetector.instance = new HardwareDetector(options);
        } else if (options) {
            // Update options if provided
            HardwareDetector.instance.options = {
                ...HardwareDetector.instance.options,
                ...options
            };
        }
        
        return HardwareDetector.instance;
    }
    
    /**
     * Detect hardware capabilities
     * @returns Hardware information
     */
    public async detectHardware(): Promise<HardwareInfo> {
        if (this.detecting) {
            logger.warn('Hardware detection already in progress, returning cached information');
            return this.hardwareInfo || this.getDefaultHardwareInfo();
        }
        
        this.detecting = true;
        
        try {
            // Detect CPU
            const cpu = this.detectCpu();
            
            // Detect memory
            const memory = this.detectMemory();
            
            // Detect disk
            const disk = await this.detectDisk();
            
            // Determine hardware category
            const category = this.categorizeHardware(cpu, memory, disk);
            
            // Create hardware info
            this.hardwareInfo = {
                cpu,
                memory,
                disk,
                category,
                timestamp: Date.now()
            };
            
            // Generate performance recommendations
            this.recommendations = this.generateRecommendations(this.hardwareInfo);
            
            // Start refresh timer if enabled
            if (this.options.enableAutoRefresh) {
                this.startRefreshTimer();
            }
            
            // Emit hardware detected event
            this.emit('hardwareDetected', this.hardwareInfo);
            
            logger.info(`Hardware detected: ${category} (${cpu.cores} cores, ${this.formatBytes(memory.total)} RAM, ${disk.type} disk)`);
            
            return this.hardwareInfo;
        } catch (error: any) {
            logger.error(`Error detecting hardware: ${error.message}`);
            
            // Use default hardware info if detection fails
            this.hardwareInfo = this.getDefaultHardwareInfo();
            
            // Generate performance recommendations
            this.recommendations = this.generateRecommendations(this.hardwareInfo);
            
            // Emit hardware detection error event
            this.emit('hardwareDetectionError', error);
            
            return this.hardwareInfo;
        } finally {
            this.detecting = false;
        }
    }
    
    /**
     * Get hardware information
     * @returns Hardware information
     */
    public getHardwareInfo(): HardwareInfo {
        return this.hardwareInfo || this.getDefaultHardwareInfo();
    }
    
    /**
     * Get performance recommendations
     * @returns Performance recommendations
     */
    public getRecommendations(): PerformanceRecommendations {
        return this.recommendations || this.generateRecommendations(this.getHardwareInfo());
    }
    
    /**
     * Refresh hardware information
     * @returns Hardware information
     */
    public async refreshHardwareInfo(): Promise<HardwareInfo> {
        return this.detectHardware();
    }
    
    /**
     * Detect CPU capabilities
     * @returns CPU information
     * @private
     */
    private detectCpu(): CpuInfo {
        const cpus = os.cpus();
        
        return {
            cores: os.cpus().length,
            logicalCores: os.cpus().length,
            speed: cpus[0]?.speed || 0,
            model: cpus[0]?.model || 'Unknown',
            architecture: os.arch()
        };
    }
    
    /**
     * Detect memory capabilities
     * @returns Memory information
     * @private
     */
    private detectMemory(): MemoryInfo {
        const total = os.totalmem();
        const free = os.freemem();
        
        return {
            total,
            free,
            available: free,
            percentAvailable: (free / total) * 100
        };
    }
    
    /**
     * Detect disk capabilities
     * @returns Disk information
     * @private
     */
    private async detectDisk(): Promise<DiskInfo> {
        try {
            const testFile = this.options.diskSpeedTestFile!;
            const testSize = this.options.diskSpeedTestSize!;
            const iterations = this.options.diskSpeedTestIterations!;
            
            // Create test buffer
            const buffer = Buffer.alloc(testSize / iterations);
            
            // Test write speed
            const writeStart = Date.now();
            let fd = await fsOpen(testFile, 'w');
            
            for (let i = 0; i < iterations; i++) {
                await fsWrite(fd, buffer, 0, buffer.length, i * buffer.length);
            }
            
            await fsClose(fd);
            const writeEnd = Date.now();
            const writeSpeed = testSize / ((writeEnd - writeStart) / 1000) / (1024 * 1024); // MB/s
            
            // Test read speed
            const readStart = Date.now();
            fd = await fsOpen(testFile, 'r');
            
            for (let i = 0; i < iterations; i++) {
                await fsRead(fd, buffer, 0, buffer.length, i * buffer.length);
            }
            
            await fsClose(fd);
            const readEnd = Date.now();
            const readSpeed = testSize / ((readEnd - readStart) / 1000) / (1024 * 1024); // MB/s
            
            // Clean up test file
            await fsUnlink(testFile);
            
            // Determine disk type based on speed
            let type: 'unknown' | 'hdd' | 'ssd' | 'nvme' = 'unknown';
            
            if (readSpeed < 50) {
                type = 'hdd';
            } else if (readSpeed < 500) {
                type = 'ssd';
            } else {
                type = 'nvme';
            }
            
            return {
                readSpeed,
                writeSpeed,
                type
            };
        } catch (error: any) {
            logger.error(`Error detecting disk capabilities: ${error.message}`);
            
            // Return default disk info
            return {
                readSpeed: 100,
                writeSpeed: 50,
                type: 'ssd'
            };
        }
    }
    
    /**
     * Categorize hardware
     * @param cpu CPU information
     * @param memory Memory information
     * @param disk Disk information
     * @returns Hardware category
     * @private
     */
    private categorizeHardware(cpu: CpuInfo, memory: MemoryInfo, disk: DiskInfo): HardwareCategory {
        // Calculate score based on hardware capabilities
        let score = 0;
        
        // CPU score (0-10)
        const cpuScore = Math.min(10, cpu.cores / 2);
        score += cpuScore;
        
        // Memory score (0-10)
        const memoryScore = Math.min(10, memory.total / (1024 * 1024 * 1024) / 2);
        score += memoryScore;
        
        // Disk score (0-10)
        let diskScore = 0;
        switch (disk.type) {
            case 'hdd':
                diskScore = 2;
                break;
            case 'ssd':
                diskScore = 6;
                break;
            case 'nvme':
                diskScore = 10;
                break;
            default:
                diskScore = 4;
                break;
        }
        score += diskScore;
        
        // Determine category based on score
        if (score < 10) {
            return HardwareCategory.LOW_END;
        } else if (score < 20) {
            return HardwareCategory.MID_RANGE;
        } else {
            return HardwareCategory.HIGH_END;
        }
    }
    
    /**
     * Generate performance recommendations
     * @param hardwareInfo Hardware information
     * @returns Performance recommendations
     * @private
     */
    private generateRecommendations(hardwareInfo: HardwareInfo): PerformanceRecommendations {
        // Generate recommendations based on hardware category
        switch (hardwareInfo.category) {
            case HardwareCategory.LOW_END:
                return {
                    chunkSize: 64 * 1024, // 64KB
                    batchSize: 10,
                    concurrency: 2,
                    bufferSize: 1 * 1024 * 1024, // 1MB
                    cacheSize: 10 * 1024 * 1024, // 10MB
                    timeout: 60000 // 1 minute
                };
                
            case HardwareCategory.MID_RANGE:
                return {
                    chunkSize: 256 * 1024, // 256KB
                    batchSize: 50,
                    concurrency: 4,
                    bufferSize: 4 * 1024 * 1024, // 4MB
                    cacheSize: 50 * 1024 * 1024, // 50MB
                    timeout: 30000 // 30 seconds
                };
                
            case HardwareCategory.HIGH_END:
                return {
                    chunkSize: 1024 * 1024, // 1MB
                    batchSize: 100,
                    concurrency: 8,
                    bufferSize: 16 * 1024 * 1024, // 16MB
                    cacheSize: 100 * 1024 * 1024, // 100MB
                    timeout: 15000 // 15 seconds
                };
                
            default:
                return {
                    chunkSize: 256 * 1024, // 256KB
                    batchSize: 50,
                    concurrency: 4,
                    bufferSize: 4 * 1024 * 1024, // 4MB
                    cacheSize: 50 * 1024 * 1024, // 50MB
                    timeout: 30000 // 30 seconds
                };
        }
    }
    
    /**
     * Get default hardware information
     * @returns Default hardware information
     * @private
     */
    private getDefaultHardwareInfo(): HardwareInfo {
        return {
            cpu: {
                cores: 4,
                logicalCores: 4,
                speed: 2500,
                model: 'Unknown',
                architecture: os.arch()
            },
            memory: {
                total: 8 * 1024 * 1024 * 1024, // 8GB
                free: 4 * 1024 * 1024 * 1024, // 4GB
                available: 4 * 1024 * 1024 * 1024, // 4GB
                percentAvailable: 50
            },
            disk: {
                readSpeed: 100,
                writeSpeed: 50,
                type: 'ssd'
            },
            category: HardwareCategory.MID_RANGE,
            timestamp: Date.now()
        };
    }
    
    /**
     * Start refresh timer
     * @private
     */
    private startRefreshTimer(): void {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            this.refreshHardwareInfo().catch(error => {
                logger.error(`Error refreshing hardware info: ${error.message}`);
            });
        }, this.options.refreshInterval);
        
        logger.debug(`Started hardware refresh timer with interval ${this.options.refreshInterval}ms`);
    }
    
    /**
     * Format bytes to human-readable string
     * @param bytes Number of bytes
     * @returns Formatted string
     * @private
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
