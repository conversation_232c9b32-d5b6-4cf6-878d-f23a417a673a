/**
 * Gameplay Systems for the Sims 4
 * 
 * This file defines the gameplay systems in the Sims 4 game, based on analysis of the game's Python scripts.
 * These systems are used to categorize resources and identify potential conflicts between mods.
 */

import { GameplaySystem } from './types.js';

/**
 * Enum of gameplay system IDs
 */
export enum GameplaySystemId {
  // Core systems
  CORE = 'CORE',
  GAMEPLAY = 'GAMEPLAY',
  LOCALIZATION = 'LOCALIZATION',
  
  // Sim systems
  SIMS = 'SIMS',
  CAS = 'CAS',
  TRAITS = 'TRAITS',
  SKILLS = 'SKILLS',
  CAREERS = 'CAREERS',
  ASPIRATIONS = 'ASPIRATIONS',
  RELATIONSHIPS = 'RELATIONSHIPS',
  NEEDS = 'NEEDS',
  MOODS = 'MOODS',
  BUFFS = 'BUFFS',
  WHIMS = 'WHIMS',
  AUTONOMY = 'AUTONOMY',
  
  // Object systems
  OBJECTS = 'OBJECTS',
  BUILD_BUY = 'BUILD_BUY',
  INTERACTIONS = 'INTERACTIONS',
  STATES = 'STATES',
  COMPONENTS = 'COMPONENTS',
  
  // World systems
  WORLD = 'WORLD',
  VENUES = 'VENUES',
  LOTS = 'LOTS',
  ZONES = 'ZONES',
  ROUTING = 'ROUTING',
  WEATHER = 'WEATHER',
  UTILITIES = 'UTILITIES',
  
  // Visual systems
  VISUALS = 'VISUALS',
  ANIMATIONS = 'ANIMATIONS',
  EFFECTS = 'EFFECTS',
  MATERIALS = 'MATERIALS',
  TEXTURES = 'TEXTURES',
  
  // Audio systems
  AUDIO = 'AUDIO',
  MUSIC = 'MUSIC',
  SOUND_EFFECTS = 'SOUND_EFFECTS',
  
  // Gameplay features
  EVENTS = 'EVENTS',
  HOLIDAYS = 'HOLIDAYS',
  CLUBS = 'CLUBS',
  RETAIL = 'RETAIL',
  RESTAURANTS = 'RESTAURANTS',
  FAME = 'FAME',
  MAGIC = 'MAGIC',
  VAMPIRES = 'VAMPIRES',
  ALIENS = 'ALIENS',
  MERMAIDS = 'MERMAIDS',
  WEREWOLVES = 'WEREWOLVES',
  PETS = 'PETS',
  SEASONS = 'SEASONS',
  ECO_LIFESTYLE = 'ECO_LIFESTYLE',
  UNIVERSITY = 'UNIVERSITY',
  ISLAND_LIVING = 'ISLAND_LIVING',
  STAR_WARS = 'STAR_WARS',
  COTTAGE_LIVING = 'COTTAGE_LIVING',
  SNOWY_ESCAPE = 'SNOWY_ESCAPE',
  DREAM_HOME_DECORATOR = 'DREAM_HOME_DECORATOR',
  PARANORMAL = 'PARANORMAL',
  TINY_LIVING = 'TINY_LIVING',
  REALM_OF_MAGIC = 'REALM_OF_MAGIC',
  STRANGERVILLE = 'STRANGERVILLE',
  GET_FAMOUS = 'GET_FAMOUS',
  JUNGLE_ADVENTURE = 'JUNGLE_ADVENTURE',
  LAUNDRY_DAY = 'LAUNDRY_DAY',
  BOWLING_NIGHT = 'BOWLING_NIGHT',
  BACKYARD_STUFF = 'BACKYARD_STUFF',
  TODDLER_STUFF = 'TODDLER_STUFF',
  FITNESS_STUFF = 'FITNESS_STUFF',
  VINTAGE_GLAMOUR = 'VINTAGE_GLAMOUR',
  KIDS_ROOM = 'KIDS_ROOM',
  ROMANTIC_GARDEN = 'ROMANTIC_GARDEN',
  MOVIE_HANGOUT = 'MOVIE_HANGOUT',
  SPOOKY_STUFF = 'SPOOKY_STUFF',
  COOL_KITCHEN = 'COOL_KITCHEN',
  LUXURY_PARTY = 'LUXURY_PARTY',
  PERFECT_PATIO = 'PERFECT_PATIO',
  SPA_DAY = 'SPA_DAY',
  OUTDOOR_RETREAT = 'OUTDOOR_RETREAT',
  HIGH_SCHOOL = 'HIGH_SCHOOL',
  GROWING_TOGETHER = 'GROWING_TOGETHER',
  HORSE_RANCH = 'HORSE_RANCH',
  FOR_RENT = 'FOR_RENT'
}

/**
 * List of gameplay systems
 */
export const gameplaySystems: GameplaySystem[] = [
  {
    id: GameplaySystemId.CORE,
    name: 'Core Systems',
    description: 'Fundamental systems that the game relies on',
    keywords: ['core', 'system', 'base', 'fundamental'],
    relatedResourceTypes: [],
    relatedSystems: [GameplaySystemId.GAMEPLAY, GameplaySystemId.LOCALIZATION],
    introducedIn: 'Base Game',
    isCore: true
  },
  {
    id: GameplaySystemId.GAMEPLAY,
    name: 'Gameplay',
    description: 'General gameplay systems and mechanics',
    keywords: ['gameplay', 'mechanics', 'systems'],
    relatedResourceTypes: [],
    relatedSystems: [GameplaySystemId.CORE, GameplaySystemId.SIMS, GameplaySystemId.OBJECTS],
    introducedIn: 'Base Game',
    isCore: true
  },
  {
    id: GameplaySystemId.LOCALIZATION,
    name: 'Localization',
    description: 'Text and language systems',
    keywords: ['localization', 'language', 'text', 'translation', 'string'],
    relatedResourceTypes: [],
    relatedSystems: [GameplaySystemId.CORE],
    introducedIn: 'Base Game',
    isCore: true
  },
  {
    id: GameplaySystemId.SIMS,
    name: 'Sims',
    description: 'Systems related to Sim characters',
    keywords: ['sim', 'sims', 'character', 'person', 'people'],
    relatedResourceTypes: [],
    relatedSystems: [GameplaySystemId.GAMEPLAY, GameplaySystemId.CAS, GameplaySystemId.TRAITS, GameplaySystemId.SKILLS],
    introducedIn: 'Base Game',
    isCore: true
  },
  {
    id: GameplaySystemId.CAS,
    name: 'Create-A-Sim',
    description: 'Systems for creating and customizing Sims',
    keywords: ['cas', 'create-a-sim', 'customization', 'appearance'],
    relatedResourceTypes: [],
    relatedSystems: [GameplaySystemId.SIMS],
    introducedIn: 'Base Game',
    isCore: true
  },
  {
    id: GameplaySystemId.TRAITS,
    name: 'Traits',
    description: 'Sim personality traits and attributes',
    keywords: ['trait', 'traits', 'personality', 'attribute'],
    relatedResourceTypes: [],
    relatedSystems: [GameplaySystemId.SIMS, GameplaySystemId.AUTONOMY],
    introducedIn: 'Base Game',
    isCore: true
  }
];

/**
 * Get a gameplay system by ID
 * @param id Gameplay system ID
 * @returns The gameplay system, or undefined if not found
 */
export function getGameplaySystemById(id: GameplaySystemId): GameplaySystem | undefined {
  return gameplaySystems.find(system => system.id === id);
}

/**
 * Get gameplay systems by related resource type
 * @param resourceType Resource type ID
 * @returns Array of gameplay systems related to the specified resource type
 */
export function getGameplaySystemsByResourceType(resourceType: number): GameplaySystem[] {
  return gameplaySystems.filter(system => system.relatedResourceTypes.includes(resourceType));
}

/**
 * Get gameplay systems by keyword
 * @param keyword Keyword to search for
 * @returns Array of gameplay systems with the specified keyword
 */
export function getGameplaySystemsByKeyword(keyword: string): GameplaySystem[] {
  return gameplaySystems.filter(system => system.keywords.includes(keyword.toLowerCase()));
}
