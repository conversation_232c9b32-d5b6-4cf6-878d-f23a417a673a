/**
 * Resource Type Registry - Official Sims 4 Resource Type Management
 * 
 * This registry provides comprehensive mapping and metadata for all official
 * Sims 4 resource types, based on game asset analysis.
 */

import { OfficialResourceType, ResourceCategory, ResourceTypeMetadata } from './OfficialResourceTypes.js';

/**
 * Resource Type ID to Enum mapping
 * Maps numeric resource type IDs to their enum values
 */
export const RESOURCE_TYPE_ID_MAP: Map<number, OfficialResourceType> = new Map([
  // === STATIC RESOURCE TYPES ===
  [4294967295, OfficialResourceType.INVALID],
  [23466547, OfficialResourceType.MODEL],
  [2393838558, OfficialResourceType.RIG],
  [3548561239, OfficialResourceType.FOOTPRINT],
  [3540272417, OfficialResourceType.SLOT],
  [3235601127, OfficialResourceType.OBJECTDEFINITION],
  [832458525, OfficialResourceType.OBJCATALOG],
  [3625704905, OfficialResourceType.OBJDEF],
  [796721156, OfficialResourceType.PNG],
  [796721158, OfficialResourceType.TGA],
  [11720834, OfficialResourceType.DDS],
  [47570707, OfficialResourceType.STATEMACHINE],
  [1797309683, OfficialResourceType.CLIP],
  [3158986820, OfficialResourceType.CLIP_HEADER],
  [666901909, OfficialResourceType.WALKSTYLE],
  [968010314, OfficialResourceType.PROPX],
  [929579223, OfficialResourceType.VP6],
  [479834948, OfficialResourceType.BC_CACHE],
  [3794048034, OfficialResourceType.AC_CACHE],
  [53690476, OfficialResourceType.XML],
  [1659456824, OfficialResourceType.COMBINED_TUNING],
  [53633251, OfficialResourceType.TRACKMASK],
  [39769844, OfficialResourceType.SIMINFO],
  [3015981296, OfficialResourceType.HOUSEHOLD_BINARY],
  [1923050575, OfficialResourceType.HOUSEHOLD_DESCRIPTION],
  [55242443, OfficialResourceType.CASPART],
  [55867754, OfficialResourceType.SKINTONE],
  [3596464121, OfficialResourceType.REGION_DESCRIPTION],
  [2793466443, OfficialResourceType.WORLD_DESCRIPTION],
  [26488364, OfficialResourceType.LOT_DESCRIPTION],
  
  // Build mode resources
  [2690089244, OfficialResourceType.FRIEZE],
  [127102176, OfficialResourceType.BLOCK],
  [1057772186, OfficialResourceType.CEILING_RAILING],
  [68746794, OfficialResourceType.FENCE],
  [2227319321, OfficialResourceType.FLOOR_TRIM],
  [3036111561, OfficialResourceType.FLOOR_PATTERN],
  [2782919923, OfficialResourceType.POOL_TRIM],
  [2448276798, OfficialResourceType.ROOF],
  [2956008719, OfficialResourceType.ROOF_TRIM],
  [4058889606, OfficialResourceType.ROOF_PATTERN],
  [2585840924, OfficialResourceType.STAIRS],
  [471658999, OfficialResourceType.RAILING],
  [2438063804, OfficialResourceType.WALL],
  [3589339425, OfficialResourceType.WALL_PATTERN],
  [2851789917, OfficialResourceType.HALFWALL_TRIM],
  [332336850, OfficialResourceType.DECOTRIM],
  
  // Material and style
  [2673671952, OfficialResourceType.STYLE],
  [2885921078, OfficialResourceType.GENERIC_MTX],
  [2377243942, OfficialResourceType.MTX_BUNDLE],
  
  // Miscellaneous static types
  [1946487583, OfficialResourceType.MAGAZINECOLLECTION],
  [2249506521, OfficialResourceType.GPINI],
  [1415235194, OfficialResourceType.PLAYLIST],
  [713711138, OfficialResourceType.TRAY_METADATA],
  [1220708729, OfficialResourceType.LOCATOR],
  
  // === TUNING RESOURCE TYPES ===
  [62078431, OfficialResourceType.TUNING],
  [2113017500, OfficialResourceType.SNIPPET],
  [2909789983, OfficialResourceType.POSTURE],
  [1772477092, OfficialResourceType.SLOT_TYPE],
  [1058419973, OfficialResourceType.SLOT_TYPE_SET],
  [1359443523, OfficialResourceType.STATIC_COMMODITY],
  [865846717, OfficialResourceType.STATISTIC],
  [151314192, OfficialResourceType.RELATIONSHIP_BIT],
  [776446212, OfficialResourceType.SOCIAL_GROUP],
  [1938713686, OfficialResourceType.TOPIC],
  [1526890910, OfficialResourceType.OBJECT_STATE],
  [1900520272, OfficialResourceType.OBJECT_PART],
  [3055412916, OfficialResourceType.OBJECT],
  [3952605219, OfficialResourceType.RECIPE],
  [3779558936, OfficialResourceType.GAME_RULESET],
  [3128647864, OfficialResourceType.MOOD],
  [1612179606, OfficialResourceType.BUFF],
  [3412057543, OfficialResourceType.TRAIT],
  [683034229, OfficialResourceType.ASPIRATION],
  [3813727192, OfficialResourceType.ASPIRATION_CATEGORY],
  [3223387309, OfficialResourceType.ASPIRATION_TRACK],
  [6899006, OfficialResourceType.OBJECTIVE],
  [3762955427, OfficialResourceType.TUTORIAL],
  [2410930353, OfficialResourceType.TUTORIAL_TIP],
  [3567295165, OfficialResourceType.GUIDANCE_TIP],
  [1939434475, OfficialResourceType.CAREER],
  [745582072, OfficialResourceType.CAREER_LEVEL],
  [**********, OfficialResourceType.CAREER_TRACK],
  [**********, OfficialResourceType.CAREER_EVENT],
  [**********, OfficialResourceType.CAREER_GIG],
  [**********, OfficialResourceType.INTERACTION],
  [********, OfficialResourceType.PIE_MENU_CATEGORY],
  [**********, OfficialResourceType.ACHIEVEMENT],
  [*********, OfficialResourceType.ACHIEVEMENT_CATEGORY],
  [********, OfficialResourceType.ACHIEVEMENT_COLLECTION],
  [**********, OfficialResourceType.REWARD],
  [**********, OfficialResourceType.ACCOUNT_REWARD],
  [**********, OfficialResourceType.TEST_BASED_SCORE],
  [**********, OfficialResourceType.SERVICE_NPC],
  [**********, OfficialResourceType.VENUE],
  [**********, OfficialResourceType.LOT_TUNING],
  [**********, OfficialResourceType.REGION],
  [**********, OfficialResourceType.STREET],
  [**********, OfficialResourceType.WALK_BY],
  [**********, OfficialResourceType.ANIMATION],
  [**********, OfficialResourceType.BALLOON],
  [*********, OfficialResourceType.ACTION],
  [**********, OfficialResourceType.SITUATION],
  [**********, OfficialResourceType.SITUATION_JOB],
  [**********, OfficialResourceType.SITUATION_GOAL],
  [**********, OfficialResourceType.SITUATION_GOAL_SET],
  [**********, OfficialResourceType.STRATEGY],
  [**********, OfficialResourceType.SIM_FILTER],
  [*********, OfficialResourceType.SIM_TEMPLATE],
  [**********, OfficialResourceType.SIM_INFO_FIXUP],
  [**********, OfficialResourceType.SUBROOT],
  [**********, OfficialResourceType.TAG_SET],
  [1220728301, OfficialResourceType.TEMPLATE_CHOOSER],
  [4183335058, OfficialResourceType.ZONE_DIRECTOR],
  [1008568217, OfficialResourceType.ZONE_MODIFIER],
  [1265622724, OfficialResourceType.OPEN_STREET_DIRECTOR],
  [239932923, OfficialResourceType.ROLE_STATE],
  [3736796019, OfficialResourceType.BROADCASTER],
  [2947394632, OfficialResourceType.AWAY_ACTION],
  [2976568058, OfficialResourceType.RABBIT_HOLE],
  
  // === EXPANSION PACK CONTENT ===
  [938421991, OfficialResourceType.ROYALTY],
  [1400130038, OfficialResourceType.DETECTIVE_CLUE],
  [2567109238, OfficialResourceType.NOTEBOOK_ENTRY],
  [3963461902, OfficialResourceType.BUCKS_PERK],
  [794407991, OfficialResourceType.CLUB_SEED],
  [4195351092, OfficialResourceType.CLUB_INTERACTION_GROUP],
  [626258997, OfficialResourceType.DRAMA_NODE],
  [1047870521, OfficialResourceType.NARRATIVE],
  [1613438381, OfficialResourceType.STORY_ARC],
  [1250314810, OfficialResourceType.STORY_CHAPTER],
  [3112702240, OfficialResourceType.ENSEMBLE],
  [1977092083, OfficialResourceType.BUSINESS],
  [3102051436, OfficialResourceType.BUSINESS_RULE],
  [3099531875, OfficialResourceType.USER_INTERFACE_INFO],
  [4114068192, OfficialResourceType.CALL_TO_ACTION],
  [3288062174, OfficialResourceType.SICKNESS],
  [874331941, OfficialResourceType.BREED],
  [213537012, OfficialResourceType.CAS_MENU_ITEM],
  [2472182722, OfficialResourceType.CAS_MENU],
  [52718493, OfficialResourceType.CAS_STORIES_QUESTION],
  [2163289367, OfficialResourceType.CAS_STORIES_ANSWER],
  [2376930633, OfficialResourceType.CAS_STORIES_TRAIT_CHOOSER],
  [1720776484, OfficialResourceType.CAS_PREFERENCE_GROUP],
  [3456433227, OfficialResourceType.CAS_PREFERENCE_CATEGORY],
  [3966303522, OfficialResourceType.CAS_PREFERENCE_ITEM],
  [2922702451, OfficialResourceType.RELATIONSHIP_LOCK],
  [963831539, OfficialResourceType.HOUSEHOLD_MILESTONE],
  [2441338001, OfficialResourceType.CONDITIONAL_LAYER],
  [3381515358, OfficialResourceType.SEASON],
  [238120813, OfficialResourceType.HOLIDAY_DEFINITION],
  [1070408838, OfficialResourceType.HOLIDAY_TRADITION],
  [1476851130, OfficialResourceType.WEATHER_EVENT],
  [1233072753, OfficialResourceType.WEATHER_FORECAST],
  [4264407467, OfficialResourceType.LOT_DECORATION],
  [3726571771, OfficialResourceType.LOT_DECORATION_PRESET],
  [4093714525, OfficialResourceType.HEADLINE],
  [523506649, OfficialResourceType.SPELL],
  [689745854, OfficialResourceType.UNIVERSITY_COURSE_DATA],
  [660124491, OfficialResourceType.UNIVERSITY_MAJOR],
  [3646477745, OfficialResourceType.UNIVERSITY],
  [2559322869, OfficialResourceType.UNIVERSITY_COURSE_SCHEDULE],
  [1430862616, OfficialResourceType.LUNAR_CYCLE],
  [1956251190, OfficialResourceType.WHIM],
  [3737052837, OfficialResourceType.CLAN],
  [2576273579, OfficialResourceType.CLAN_VALUE],
  [3307360148, OfficialResourceType.DEVELOPMENTAL_MILESTONE],
  [2519486516, OfficialResourceType.TUNING_DESCRIPTION],
]);

/**
 * Reverse mapping: Enum to Resource Type ID
 */
export const RESOURCE_TYPE_ENUM_MAP: Map<OfficialResourceType, number> = new Map(
  Array.from(RESOURCE_TYPE_ID_MAP.entries()).map(([id, type]) => [type, id])
);

/**
 * Get resource type enum from numeric ID
 */
export function getResourceTypeFromId(id: number): OfficialResourceType {
  return RESOURCE_TYPE_ID_MAP.get(id) || OfficialResourceType.UNKNOWN;
}

/**
 * Get numeric ID from resource type enum
 */
export function getResourceTypeId(type: OfficialResourceType): number | undefined {
  return RESOURCE_TYPE_ENUM_MAP.get(type);
}

/**
 * Check if a resource type ID is known
 */
export function isKnownResourceType(id: number): boolean {
  return RESOURCE_TYPE_ID_MAP.has(id);
}

/**
 * Get all known resource type IDs
 */
export function getAllResourceTypeIds(): number[] {
  return Array.from(RESOURCE_TYPE_ID_MAP.keys());
}

/**
 * Get all known resource types
 */
export function getAllResourceTypes(): OfficialResourceType[] {
  return Array.from(RESOURCE_TYPE_ID_MAP.values());
}
