/**
 * StreamPipeline Compatibility Layer
 * 
 * This module provides a compatibility layer for the StreamPipeline class.
 * It delegates to the ConsolidatedStreamPipeline while maintaining the same API.
 */

import { Readable } from 'stream';
import { EventEmitter } from 'events';
import { Logger } from '../../../../utils/logging/logger.js';
import { IStreamTransformer } from '../baseStreamTransformer.js';
import { ConsolidatedStreamPipeline, ConsolidatedStreamPipelineOptions } from '../consolidatedStreamPipeline.js';
import { IStreamPipeline, PipelineStats, StreamPipelineOptions } from '../streamPipelineBase.js';

// Create a logger for this module
const logger = new Logger('StreamPipelineCompat');

/**
 * StreamPipeline compatibility implementation
 */
export class StreamPipeline extends EventEmitter implements IStreamPipeline {
    private consolidatedPipeline: ConsolidatedStreamPipeline;
    
    /**
     * Create a new stream pipeline
     * @param options Pipeline options
     */
    constructor(options: StreamPipelineOptions = {}) {
        super();
        
        // Create consolidated pipeline
        this.consolidatedPipeline = new ConsolidatedStreamPipeline(options);
        
        // Forward events from the consolidated pipeline
        this.consolidatedPipeline.on('progress', (progress) => this.emit('progress', progress));
        this.consolidatedPipeline.on('stats', (stats) => this.emit('stats', stats));
        this.consolidatedPipeline.on('error', (error) => this.emit('error', error));
        
        logger.debug('Created StreamPipeline compatibility layer');
    }
    
    /**
     * Create a pipeline for a specific resource type
     * @param resourceType Resource type
     * @param source Source stream
     * @param options Pipeline options
     */
    public async createPipeline(
        resourceType: number, 
        source: Readable, 
        options: StreamPipelineOptions = {}
    ): Promise<Readable> {
        // Convert options to consolidated options
        const consolidatedOptions: ConsolidatedStreamPipelineOptions = {
            ...options,
            // Add any additional options needed by the consolidated pipeline
        };
        
        return this.consolidatedPipeline.createPipeline(resourceType, source, consolidatedOptions);
    }
    
    /**
     * Add a custom transformer to the pipeline
     * @param transformer Transformer to add
     * @param position Position to add the transformer at
     */
    public addTransformer(
        transformer: IStreamTransformer, 
        position?: number
    ): void {
        this.consolidatedPipeline.addTransformer(transformer, position);
    }
    
    /**
     * Get pipeline statistics
     */
    public getStats(): PipelineStats {
        return this.consolidatedPipeline.getStats();
    }
    
    /**
     * Destroy the pipeline and clean up resources
     */
    public async destroy(): Promise<void> {
        return this.consolidatedPipeline.destroy();
    }
}
