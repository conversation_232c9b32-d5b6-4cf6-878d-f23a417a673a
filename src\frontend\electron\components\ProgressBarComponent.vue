<template>
  <div v-if="isAnalyzing || status !== 'Ready'" class="progress-container mt-4"> <!-- Use Vuetify spacing -->
    <!-- Use Vuetify Progress Linear -->
    <v-progress-linear
      v-model="progress"
      color="primary"
      height="20"
      striped
      stream
    >
      <template v-slot:default="{ value }">
        <strong>{{ Math.ceil(value) }}%</strong>
      </template>
    </v-progress-linear>
    <p class="text-body-2 mt-2 text-medium-emphasis">{{ status }}</p> <!-- Vuetify typography -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAnalysisStore } from '../store/analysis';
// Vuetify components are globally available via the plugin

const analysisStore = useAnalysisStore();

// Computed properties to reactively get state from the store
const isAnalyzing = computed(() => analysisStore.isAnalyzing);
const progress = computed(() => analysisStore.progress);
const status = computed(() => analysisStore.status);

</script>

<style scoped>
.progress-container {
  /* Keep or adjust custom styles as needed */
  margin-bottom: 1.5rem;
  text-align: center;
}
/* Vuetify spacing classes like mt-4, mt-2 used */
</style>
